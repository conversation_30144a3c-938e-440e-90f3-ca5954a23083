{"name": "mediinspect-v2-backend", "version": "2.0.0", "description": "医保基金监管平台后端API服务", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit", "db:setup": "node -e \"console.log('数据库设置脚本')\"", "db:verify": "node -e \"console.log('数据库验证脚本')\"", "db:data": "node -e \"console.log('初始化数据脚本')\""}, "dependencies": {"@types/node": "^22.10.2", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "jsonwebtoken": "^9.0.2", "next": "^15.3.4", "oracledb": "^6.7.0", "typescript": "^5.7.2", "uuid": "^11.1.0", "zod": "^3.25.74"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/oracledb": "^6.6.0", "@types/uuid": "^10.0.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["医保", "基金监管", "医疗保险", "监督管理", "API", "后端服务", "Oracle", "TypeScript"], "author": {"name": "医保基金监管平台开发团队", "email": "<EMAIL>"}, "license": "UNLICENSED"}