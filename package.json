{"name": "mediinspect-v2", "version": "2.0.0", "description": "医保基金监管平台v2.0 - 基于Next.js的现代化医疗保险基金监督管理系统", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:test": "node scripts/test-db-connection.js", "db:init": "node scripts/init-database.js", "db:data": "node scripts/init-default-data.js", "db:verify": "node scripts/verify-database.js", "db:setup": "node scripts/setup-database.js", "db:setup-force": "node scripts/setup-database.js --force", "db:help": "node scripts/setup-database.js --help", "migrate-rule-types": "node scripts/migrate-rule-types.js", "rollback-rule-types": "node scripts/rollback-rule-types.js"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.17.0", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.20", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "embla-carousel-react": "^8.6.0", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "monaco-editor": "^0.52.2", "next": "^15.3.4", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "oracledb": "^6.7.0", "postcss": "^8.5.2", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "uuid": "^11.1.0", "web-vitals": "^5.0.3", "zod": "^3.25.74", "zustand": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.53.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/oracledb": "^6.6.0", "@types/uuid": "^10.0.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.53.2", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "ts-jest": "^29.4.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["医保", "基金监管", "医疗保险", "监督管理", "Next.js", "React", "TypeScript", "Oracle", "企业级应用", "shadcn/ui"], "author": {"name": "医保基金监管平台开发团队", "email": "<EMAIL>"}, "license": "UNLICENSED"}