{"name": "mediinspect-v2", "version": "2.0.0", "description": "医保基金监管平台v2.0 - 基于Next.js的现代化医疗保险基金监督管理系统", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:test": "node scripts/test-db-connection.js", "db:init": "node scripts/init-database.js", "db:data": "node scripts/init-default-data.js", "db:verify": "node scripts/verify-database.js", "db:setup": "node scripts/setup-database.js", "db:setup-force": "node scripts/setup-database.js --force", "db:help": "node scripts/setup-database.js --help", "migrate-rule-types": "node scripts/migrate-rule-types.js", "rollback-rule-types": "node scripts/rollback-rule-types.js"}, "dependencies": {"@types/node": "^22.10.2", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "jsonwebtoken": "^9.0.2", "next": "^15.3.4", "oracledb": "^6.7.0", "typescript": "^5.7.2", "uuid": "^11.1.0", "zod": "^3.25.74"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/oracledb": "^6.6.0", "@types/uuid": "^10.0.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.4", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.4.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["医保", "基金监管", "医疗保险", "监督管理", "Next.js API", "TypeScript", "Oracle", "企业级应用", "后端服务"], "author": {"name": "医保基金监管平台开发团队", "email": "<EMAIL>"}, "license": "UNLICENSED"}