# 医保基金监管平台 - 前端架构设计文档

## 📋 目录

- [1. 架构概述](#1-架构概述)
- [2. 技术栈选型](#2-技术栈选型)
- [3. 项目结构设计](#3-项目结构设计)
- [4. 组件设计规范](#4-组件设计规范)
- [5. 状态管理设计](#5-状态管理设计)
- [6. 路由设计](#6-路由设计)
- [7. 样式设计系统](#7-样式设计系统)
- [8. 性能优化策略](#8-性能优化策略)

## 1. 架构概述

### 1.1 设计目标
构建现代化、高性能、易维护的前端应用，为医保基金监管提供优秀的用户体验。

### 1.2 架构特点
- **组件化**: 基于React的组件化开发
- **类型安全**: TypeScript提供完整的类型检查
- **响应式**: 适配多种设备和屏幕尺寸
- **模块化**: 清晰的模块划分和依赖管理
- **可扩展**: 支持功能模块的灵活扩展

### 1.3 核心原则
- **用户体验优先**: 注重交互体验和性能优化
- **开发效率**: 提供高效的开发工具和流程
- **代码质量**: 严格的代码规范和质量控制
- **可维护性**: 清晰的架构设计和文档

## 2. 技术栈选型

### 2.1 核心框架
- **Next.js 15**: 全栈React框架，支持SSR/SSG
- **React 18**: 现代化的UI库，支持并发特性
- **TypeScript**: 静态类型检查，提升代码质量

### 2.2 UI组件库
- **shadcn/ui**: 基于Radix UI的现代化组件库
- **Radix UI**: 无样式的可访问性组件基础
- **Lucide React**: 现代化的图标库
- **Tailwind CSS**: 原子化CSS框架

### 2.3 状态管理
- **Zustand**: 轻量级状态管理库
- **React Query (TanStack Query)**: 服务器状态管理
- **React Hook Form**: 表单状态管理
- **Zod**: 数据验证和类型推断

### 2.4 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks管理
- **Commitlint**: 提交信息规范

### 2.5 动画和图表
- **GSAP**: 高性能动画库
- **Recharts**: React图表库
- **D3.js**: 复杂数据可视化
- **Framer Motion**: React动画库

### 2.6 工具库
- **date-fns**: 日期处理库
- **clsx**: 条件类名工具
- **react-hot-toast**: 通知组件
- **react-hook-form**: 表单处理

## 3. 项目结构设计

### 3.1 目录结构

```
src/
├── app/                    # Next.js App Router
│   ├── (dashboard)/       # 仪表板路由组
│   │   ├── dashboard/     # 仪表板页面
│   │   └── layout.tsx     # 仪表板布局
│   ├── api/               # API路由
│   │   ├── auth/          # 认证API
│   │   ├── users/         # 用户管理API
│   │   ├── medical-cases/ # 医疗病例API
│   │   ├── supervision-rules/ # 监管规则API
│   │   ├── knowledge-base/ # 知识库API
│   │   └── analytics/     # 数据统计API
│   ├── auth/              # 认证相关页面
│   │   ├── login/         # 登录页面
│   │   └── logout/        # 登出页面
│   ├── medical-cases/     # 医疗病例页面
│   │   ├── page.tsx       # 病例列表页
│   │   ├── [id]/          # 病例详情页
│   │   └── new/           # 新增病例页
│   ├── supervision-rules/ # 监管规则页面
│   ├── knowledge-base/    # 知识库页面
│   ├── analytics/         # 数据统计页面
│   ├── settings/          # 系统设置页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── loading.tsx        # 全局加载组件
│   ├── error.tsx          # 全局错误组件
│   ├── not-found.tsx      # 404页面
│   └── page.tsx           # 首页
├── components/            # 组件库
│   ├── ui/               # shadcn/ui基础组件
│   │   ├── button.tsx    # 按钮组件
│   │   ├── input.tsx     # 输入框组件
│   │   ├── table.tsx     # 表格组件
│   │   ├── dialog.tsx    # 对话框组件
│   │   └── ...           # 其他UI组件
│   ├── layout/           # 布局组件
│   │   ├── header.tsx    # 页面头部
│   │   ├── sidebar.tsx   # 侧边栏
│   │   ├── footer.tsx    # 页面底部
│   │   └── navigation.tsx # 导航组件
│   ├── common/           # 通用组件
│   │   ├── loading.tsx   # 加载组件
│   │   ├── error-boundary.tsx # 错误边界
│   │   ├── data-table.tsx # 数据表格
│   │   └── search-box.tsx # 搜索框
│   ├── dashboard/        # 仪表板组件
│   │   ├── stats-card.tsx # 统计卡片
│   │   ├── chart-widget.tsx # 图表组件
│   │   └── recent-activities.tsx # 最近活动
│   ├── medical-cases/    # 医疗病例组件
│   │   ├── case-list.tsx # 病例列表
│   │   ├── case-detail.tsx # 病例详情
│   │   ├── case-form.tsx # 病例表单
│   │   └── case-filters.tsx # 病例筛选
│   ├── supervision-rules/ # 监管规则组件
│   ├── knowledge-base/   # 知识库组件
│   ├── analytics/        # 数据分析组件
│   ├── auth/             # 认证组件
│   │   ├── login-form.tsx # 登录表单
│   │   └── auth-guard.tsx # 认证守卫
│   └── providers/        # Context提供者
│       ├── auth-provider.tsx # 认证提供者
│       ├── theme-provider.tsx # 主题提供者
│       └── query-provider.tsx # 查询提供者
├── lib/                  # 工具库
│   ├── api/              # API客户端
│   │   ├── client.ts     # API客户端配置
│   │   ├── auth.ts       # 认证API
│   │   ├── users.ts      # 用户API
│   │   ├── medical-cases.ts # 病例API
│   │   └── ...           # 其他API
│   ├── auth/             # 认证工具
│   │   ├── config.ts     # 认证配置
│   │   ├── providers.ts  # 认证提供者
│   │   └── utils.ts      # 认证工具函数
│   ├── database/         # 数据库工具
│   │   ├── connection.ts # 数据库连接
│   │   ├── queries.ts    # 查询构建器
│   │   └── migrations.ts # 数据迁移
│   ├── utils/            # 通用工具
│   │   ├── cn.ts         # 类名工具
│   │   ├── format.ts     # 格式化工具
│   │   ├── validation.ts # 验证工具
│   │   └── constants.ts  # 常量定义
│   └── hooks/            # 自定义Hooks
│       ├── use-auth.ts   # 认证Hook
│       ├── use-api.ts    # API Hook
│       └── use-local-storage.ts # 本地存储Hook
├── types/                # TypeScript类型定义
│   ├── auth.ts           # 认证类型
│   ├── user.ts           # 用户类型
│   ├── medical-case.ts   # 病例类型
│   ├── supervision-rule.ts # 规则类型
│   ├── knowledge-base.ts # 知识库类型
│   ├── analytics.ts      # 分析类型
│   └── api.ts            # API类型
├── hooks/                # 自定义Hooks
│   ├── use-debounce.ts   # 防抖Hook
│   ├── use-local-storage.ts # 本地存储Hook
│   ├── use-medical-cases.ts # 病例Hook
│   └── use-pagination.ts # 分页Hook
├── contexts/             # React Context
│   ├── auth-context.tsx  # 认证上下文
│   ├── theme-context.tsx # 主题上下文
│   └── tab-context.tsx   # 标签页上下文
├── config/               # 配置文件
│   ├── site.ts           # 站点配置
│   ├── navigation.ts     # 导航配置
│   ├── api.ts            # API配置
│   └── constants.ts      # 常量配置
└── styles/               # 样式文件
    ├── globals.css       # 全局样式
    ├── components.css    # 组件样式
    └── utilities.css     # 工具样式
```

### 3.2 模块划分原则

#### 3.2.1 按功能模块划分
- **认证模块**: 登录、注册、权限管理
- **仪表板模块**: 数据概览、快速操作
- **病例管理模块**: 病例CRUD、查询、统计
- **规则管理模块**: 规则定义、执行、监控
- **知识库模块**: 文档管理、搜索、分类
- **数据分析模块**: 报表、图表、导出
- **系统管理模块**: 用户、角色、配置

#### 3.2.2 按技术层次划分
- **表现层**: 页面组件、布局组件
- **业务层**: 业务组件、业务逻辑
- **数据层**: API调用、状态管理
- **工具层**: 工具函数、常量定义

## 4. 组件设计规范

### 4.1 组件分类

#### 4.1.1 基础组件 (UI Components)
- **来源**: shadcn/ui提供的原子级组件
- **特点**: 无业务逻辑，高度可复用
- **示例**: Button, Input, Table, Dialog

#### 4.1.2 业务组件 (Business Components)
- **特点**: 包含特定业务逻辑
- **示例**: CaseList, RuleEditor, UserForm

#### 4.1.3 布局组件 (Layout Components)
- **特点**: 负责页面布局和结构
- **示例**: Header, Sidebar, PageLayout

#### 4.1.4 页面组件 (Page Components)
- **特点**: 完整的页面实现
- **示例**: DashboardPage, CaseDetailPage

### 4.2 组件设计原则

#### 4.2.1 单一职责原则
- 每个组件只负责一个功能
- 避免组件过于复杂
- 便于测试和维护

#### 4.2.2 可复用性原则
- 通过props配置组件行为
- 避免硬编码业务逻辑
- 支持主题和样式定制

#### 4.2.3 组合优于继承
- 使用组合模式构建复杂组件
- 通过children和render props传递内容
- 保持组件接口简洁

### 4.3 组件开发规范

#### 4.3.1 TypeScript接口定义
```typescript
// 组件Props接口
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
  className?: string;
  variant?: 'default' | 'compact';
}

// 组件实现
export function UserCard({ 
  user, 
  onEdit, 
  onDelete, 
  className,
  variant = 'default'
}: UserCardProps) {
  // 组件实现
}
```

#### 4.3.2 样式处理
```typescript
import { cn } from '@/lib/utils';

export function Button({ className, variant, ...props }: ButtonProps) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium',
        {
          'bg-primary text-primary-foreground': variant === 'default',
          'bg-secondary text-secondary-foreground': variant === 'secondary',
        },
        className
      )}
      {...props}
    />
  );
}
```

#### 4.3.3 事件处理
```typescript
export function SearchBox({ onSearch, placeholder }: SearchBoxProps) {
  const [value, setValue] = useState('');
  
  const handleSubmit = useCallback((e: FormEvent) => {
    e.preventDefault();
    onSearch?.(value);
  }, [value, onSearch]);
  
  return (
    <form onSubmit={handleSubmit}>
      <input
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder={placeholder}
      />
    </form>
  );
}
```

## 5. 状态管理设计

### 5.1 状态分层架构

```
全局状态 (Zustand)
├── 用户认证状态 (auth)
├── 主题配置状态 (theme)
├── 导航状态 (navigation)
└── 应用配置状态 (config)

组件状态 (useState/useReducer)
├── 表单状态 (form data)
├── UI交互状态 (modal, dropdown)
├── 临时数据状态 (temp data)
└── 组件内部状态 (internal state)

服务器状态 (React Query)
├── API数据缓存 (cached data)
├── 请求状态管理 (loading, error)
├── 数据同步 (sync)
└── 乐观更新 (optimistic updates)
```

### 5.2 Zustand状态管理

#### 5.2.1 认证状态
```typescript
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  permissions: string[];
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  isAuthenticated: false,
  permissions: [],

  login: async (credentials) => {
    const response = await authApi.login(credentials);
    set({
      user: response.user,
      token: response.token,
      isAuthenticated: true,
      permissions: response.permissions,
    });
  },

  logout: () => {
    set({
      user: null,
      token: null,
      isAuthenticated: false,
      permissions: [],
    });
  },

  hasPermission: (permission) => {
    return get().permissions.includes(permission);
  },
}));
```

#### 5.2.2 主题状态
```typescript
interface ThemeState {
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

export const useThemeStore = create<ThemeState>((set) => ({
  theme: 'system',
  setTheme: (theme) => set({ theme }),
}));
```

### 5.3 React Query配置

#### 5.3.1 查询客户端配置
```typescript
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      refetchOnWindowFocus: false,
      retry: (failureCount, error) => {
        if (error.status === 404) return false;
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false,
    },
  },
});
```

#### 5.3.2 自定义Hooks
```typescript
// 医疗病例查询Hook
export function useMedicalCases(params?: CaseQueryParams) {
  return useQuery({
    queryKey: ['medical-cases', params],
    queryFn: () => medicalCaseApi.getList(params),
    enabled: !!params,
  });
}

// 病例详情查询Hook
export function useMedicalCase(id: string) {
  return useQuery({
    queryKey: ['medical-case', id],
    queryFn: () => medicalCaseApi.getById(id),
    enabled: !!id,
  });
}

// 病例创建Mutation
export function useCreateMedicalCase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: medicalCaseApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['medical-cases']);
      toast.success('病例创建成功');
    },
    onError: (error) => {
      toast.error('病例创建失败');
    },
  });
}
```
