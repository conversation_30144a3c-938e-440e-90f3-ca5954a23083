import { executeQuery } from '@/lib/database'
import { executeQueryWithCache, CACHE_CONFIG } from '@/lib/database-optimizer'
// import { getMedicalCaseWithRelations } from '@/lib/medical-case-service'
import { getRuleExecutionResultWithRelations } from '@/lib/rule-execution-service'
import { getKnowledgeVersionHistory, logKnowledgeAccess } from '@/lib/knowledge-version-service'

/**
 * 增强的数据库关联查询服务
 * 提供跨模块的复杂关联查询和业务逻辑支持
 */

export interface CrossModuleQueryParams {
  caseId?: number
  ruleId?: number
  documentId?: number
  userId?: number
  startDate?: string
  endDate?: string
  includeRelations?: boolean
}

export interface CrossModuleQueryResult {
  medicalCases: any[]
  ruleExecutions: any[]
  knowledgeDocuments: any[]
  userActivities: any[]
  statistics: {
    totalCases: number
    totalRuleExecutions: number
    totalDocuments: number
    totalActivities: number
  }
}

/**
 * 跨模块综合查询
 */
export async function performCrossModuleQuery(params: CrossModuleQueryParams): Promise<CrossModuleQueryResult> {
  try {
    const {
      caseId,
      ruleId,
      documentId,
      userId,
      startDate,
      endDate,
      includeRelations = false
    } = params

    // 并行执行多个查询
    const [
      medicalCasesResult,
      ruleExecutionsResult,
      knowledgeDocumentsResult,
      userActivitiesResult
    ] = await Promise.all([
      queryMedicalCasesWithFilters(params),
      queryRuleExecutionsWithFilters(params),
      queryKnowledgeDocumentsWithFilters(params),
      queryUserActivitiesWithFilters(params)
    ])

    // 如果需要包含关联数据，进行深度查询
    let enhancedResults = {
      medicalCases: medicalCasesResult,
      ruleExecutions: ruleExecutionsResult,
      knowledgeDocuments: knowledgeDocumentsResult,
      userActivities: userActivitiesResult
    }

    if (includeRelations) {
      enhancedResults = await enhanceWithRelations(enhancedResults)
    }

    // 计算统计信息
    const statistics = {
      totalCases: medicalCasesResult.length,
      totalRuleExecutions: ruleExecutionsResult.length,
      totalDocuments: knowledgeDocumentsResult.length,
      totalActivities: userActivitiesResult.length
    }

    return {
      ...enhancedResults,
      statistics
    }
  } catch (error) {
    console.error('跨模块查询失败:', error)
    throw new Error('跨模块查询失败')
  }
}

/**
 * 查询医疗案例（带过滤条件）
 */
async function queryMedicalCasesWithFilters(params: CrossModuleQueryParams): Promise<any[]> {
  try {
    const conditions: string[] = ['mc.IS_DELETED = 0']
    const queryParams: any = {}

    if (params.caseId) {
      conditions.push('mc.ID = :caseId')
      queryParams.caseId = params.caseId
    }

    if (params.startDate) {
      conditions.push('mc.ADMISSION_DATE >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = params.startDate
    }

    if (params.endDate) {
      conditions.push('mc.ADMISSION_DATE <= TO_DATE(:endDate, \'YYYY-MM-DD\') + 1')
      queryParams.endDate = params.endDate
    }

    const sql = `
      SELECT 
        mc.ID,
        mc.CASE_NUMBER,
        mc.PATIENT_NAME,
        mc.HOSPITAL_NAME,
        mc.TOTAL_COST,
        mc.MEDICAL_CATEGORY,
        mc.ADMISSION_DATE,
        mc.DISCHARGE_DATE,
        -- 关联规则执行次数
        (SELECT COUNT(*) FROM RULE_EXECUTION_RESULT rer WHERE rer.CASE_ID = mc.ID) as RULE_EXECUTION_COUNT,
        -- 关联违规次数
        (SELECT COUNT(*) FROM RULE_EXECUTION_RESULT rer WHERE rer.CASE_ID = mc.ID AND rer.RESULT_TYPE = 'VIOLATION') as VIOLATION_COUNT
      FROM MEDICAL_CASE mc
      WHERE ${conditions.join(' AND ')}
      ORDER BY mc.ADMISSION_DATE DESC
    `

    const result = await executeQuery(sql, queryParams)
    return result.rows || []
  } catch (error) {
    console.error('查询医疗案例失败:', error)
    return []
  }
}

/**
 * 查询规则执行记录（带过滤条件）
 */
async function queryRuleExecutionsWithFilters(params: CrossModuleQueryParams): Promise<any[]> {
  try {
    const conditions: string[] = ['1=1']
    const queryParams: any = {}

    if (params.ruleId) {
      conditions.push('rer.RULE_ID = :ruleId')
      queryParams.ruleId = params.ruleId
    }

    if (params.caseId) {
      conditions.push('rer.CASE_ID = :caseId')
      queryParams.caseId = params.caseId
    }

    if (params.startDate) {
      conditions.push('rer.EXECUTED_AT >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = params.startDate
    }

    if (params.endDate) {
      conditions.push('rer.EXECUTED_AT <= TO_DATE(:endDate, \'YYYY-MM-DD\') + 1')
      queryParams.endDate = params.endDate
    }

    const sql = `
      SELECT 
        rer.ID,
        rer.RULE_ID,
        rer.CASE_ID,
        rer.RESULT_TYPE,
        rer.SEVERITY,
        rer.VIOLATION_AMOUNT,
        rer.EXECUTED_AT,
        sr.RULE_NAME,
        sr.CATEGORY,
        mc.CASE_NUMBER,
        mc.PATIENT_NAME
      FROM RULE_EXECUTION_RESULT rer
      LEFT JOIN SUPERVISION_RULE sr ON rer.RULE_ID = sr.ID
      LEFT JOIN MEDICAL_CASE mc ON rer.CASE_ID = mc.ID
      WHERE ${conditions.join(' AND ')}
      ORDER BY rer.EXECUTED_AT DESC
    `

    const result = await executeQuery(sql, queryParams)
    return result.rows || []
  } catch (error) {
    console.error('查询规则执行记录失败:', error)
    return []
  }
}

/**
 * 查询知识库文档（带过滤条件）
 */
async function queryKnowledgeDocumentsWithFilters(params: CrossModuleQueryParams): Promise<any[]> {
  try {
    const conditions: string[] = ['kb.IS_DELETED = 0']
    const queryParams: any = {}

    if (params.documentId) {
      conditions.push('kb.ID = :documentId')
      queryParams.documentId = params.documentId
    }

    if (params.startDate) {
      conditions.push('kb.CREATED_AT >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = params.startDate
    }

    if (params.endDate) {
      conditions.push('kb.CREATED_AT <= TO_DATE(:endDate, \'YYYY-MM-DD\') + 1')
      queryParams.endDate = params.endDate
    }

    const sql = `
      SELECT 
        kb.ID,
        kb.TITLE,
        kb.CATEGORY,
        kb.STATUS,
        kb.CREATED_AT,
        kb.UPDATED_AT,
        -- 版本数量
        (SELECT COUNT(*) FROM KNOWLEDGE_VERSION_HISTORY kvh WHERE kvh.DOCUMENT_ID = kb.ID) as VERSION_COUNT,
        -- 访问次数
        (SELECT COUNT(*) FROM KNOWLEDGE_ACCESS_LOG kal WHERE kal.DOCUMENT_ID = kb.ID) as ACCESS_COUNT
      FROM KNOWLEDGE_BASE kb
      WHERE ${conditions.join(' AND ')}
      ORDER BY kb.UPDATED_AT DESC
    `

    const result = await executeQuery(sql, queryParams)
    return result.rows || []
  } catch (error) {
    console.error('查询知识库文档失败:', error)
    return []
  }
}

/**
 * 查询用户活动（带过滤条件）
 */
async function queryUserActivitiesWithFilters(params: CrossModuleQueryParams): Promise<any[]> {
  try {
    const conditions: string[] = ['1=1']
    const queryParams: any = {}

    if (params.userId) {
      conditions.push('u.ID = :userId')
      queryParams.userId = params.userId
    }

    if (params.startDate) {
      conditions.push('u.LAST_LOGIN_TIME >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = params.startDate
    }

    const sql = `
      SELECT 
        u.ID,
        u.USERNAME,
        u.REAL_NAME,
        u.LAST_LOGIN_TIME,
        u.LOGIN_COUNT,
        -- 创建的案例数
        (SELECT COUNT(*) FROM MEDICAL_CASE mc WHERE mc.CREATED_BY = u.ID) as CREATED_CASES_COUNT,
        -- 执行的规则数
        (SELECT COUNT(*) FROM RULE_EXECUTION_LOG rel WHERE rel.CREATED_BY = u.ID) as EXECUTED_RULES_COUNT,
        -- 访问的文档数
        (SELECT COUNT(DISTINCT kal.DOCUMENT_ID) FROM KNOWLEDGE_ACCESS_LOG kal WHERE kal.USER_ID = u.ID) as ACCESSED_DOCUMENTS_COUNT
      FROM USER_MANAGEMENT_USER u
      WHERE ${conditions.join(' AND ')} AND u.IS_DELETED = 0
      ORDER BY u.LAST_LOGIN_TIME DESC
    `

    const result = await executeQuery(sql, queryParams)
    return result.rows || []
  } catch (error) {
    console.error('查询用户活动失败:', error)
    return []
  }
}

/**
 * 增强结果，添加关联数据
 */
async function enhanceWithRelations(results: any): Promise<any> {
  try {
    // 为医疗案例添加完整关联数据
    const enhancedCases = await Promise.all(
      results.medicalCases.slice(0, 10).map(async (caseItem: any) => {
        try {
          // const relations = await getMedicalCaseWithRelations(caseItem.ID)
          const relations = null
          return { ...caseItem, relations }
        } catch (error) {
          console.error(`获取案例 ${caseItem.ID} 关联数据失败:`, error)
          return caseItem
        }
      })
    )

    // 为规则执行结果添加完整关联数据
    const enhancedExecutions = await Promise.all(
      results.ruleExecutions.slice(0, 10).map(async (execution: any) => {
        try {
          const relations = await getRuleExecutionResultWithRelations(execution.ID)
          return { ...execution, relations }
        } catch (error) {
          console.error(`获取执行结果 ${execution.ID} 关联数据失败:`, error)
          return execution
        }
      })
    )

    // 为知识库文档添加版本历史
    const enhancedDocuments = await Promise.all(
      results.knowledgeDocuments.slice(0, 10).map(async (doc: any) => {
        try {
          const versionHistory = await getKnowledgeVersionHistory({
            documentId: doc.ID,
            page: 1,
            pageSize: 5
          })
          return { ...doc, versionHistory: versionHistory.items }
        } catch (error) {
          console.error(`获取文档 ${doc.ID} 版本历史失败:`, error)
          return doc
        }
      })
    )

    return {
      medicalCases: enhancedCases,
      ruleExecutions: enhancedExecutions,
      knowledgeDocuments: enhancedDocuments,
      userActivities: results.userActivities
    }
  } catch (error) {
    console.error('增强关联数据失败:', error)
    return results
  }
}

/**
 * 获取业务关联统计
 */
export async function getBusinessRelationStatistics(): Promise<{
  caseRuleRelations: any[]
  ruleDocumentRelations: any[]
  userActivitySummary: any[]
}> {
  try {
    // 案例-规则关联统计
    const caseRuleStats = await executeQuery(`
      SELECT 
        mc.MEDICAL_CATEGORY,
        sr.CATEGORY as RULE_CATEGORY,
        COUNT(*) as EXECUTION_COUNT,
        AVG(rer.VIOLATION_AMOUNT) as AVG_VIOLATION_AMOUNT
      FROM RULE_EXECUTION_RESULT rer
      JOIN MEDICAL_CASE mc ON rer.CASE_ID = mc.ID
      JOIN SUPERVISION_RULE sr ON rer.RULE_ID = sr.ID
      WHERE rer.RESULT_TYPE = 'VIOLATION'
      GROUP BY mc.MEDICAL_CATEGORY, sr.CATEGORY
      ORDER BY EXECUTION_COUNT DESC
    `)

    // 规则-文档关联统计
    const ruleDocStats = await executeQuery(`
      SELECT 
        sr.CATEGORY as RULE_CATEGORY,
        kb.CATEGORY as DOC_CATEGORY,
        COUNT(DISTINCT kal.USER_ID) as UNIQUE_USERS,
        COUNT(*) as ACCESS_COUNT
      FROM KNOWLEDGE_ACCESS_LOG kal
      JOIN KNOWLEDGE_BASE kb ON kal.DOCUMENT_ID = kb.ID
      JOIN SUPERVISION_RULE sr ON UPPER(kb.CONTENT) LIKE '%' || UPPER(sr.CATEGORY) || '%'
      GROUP BY sr.CATEGORY, kb.CATEGORY
      ORDER BY ACCESS_COUNT DESC
    `)

    // 用户活动汇总
    const userActivityStats = await executeQuery(`
      SELECT 
        u.ID,
        u.USERNAME,
        u.REAL_NAME,
        COUNT(DISTINCT mc.ID) as MANAGED_CASES,
        COUNT(DISTINCT rel.ID) as EXECUTED_RULES,
        COUNT(DISTINCT kal.DOCUMENT_ID) as ACCESSED_DOCUMENTS
      FROM USER_MANAGEMENT_USER u
      LEFT JOIN MEDICAL_CASE mc ON u.ID = mc.CREATED_BY
      LEFT JOIN RULE_EXECUTION_LOG rel ON u.ID = rel.CREATED_BY
      LEFT JOIN KNOWLEDGE_ACCESS_LOG kal ON u.ID = kal.USER_ID
      WHERE u.IS_DELETED = 0
      GROUP BY u.ID, u.USERNAME, u.REAL_NAME
      HAVING COUNT(DISTINCT mc.ID) > 0 OR COUNT(DISTINCT rel.ID) > 0 OR COUNT(DISTINCT kal.DOCUMENT_ID) > 0
      ORDER BY (COUNT(DISTINCT mc.ID) + COUNT(DISTINCT rel.ID) + COUNT(DISTINCT kal.DOCUMENT_ID)) DESC
    `)

    return {
      caseRuleRelations: caseRuleStats.rows || [],
      ruleDocumentRelations: ruleDocStats.rows || [],
      userActivitySummary: userActivityStats.rows || []
    }
  } catch (error) {
    console.error('获取业务关联统计失败:', error)
    throw new Error('获取业务关联统计失败')
  }
}
