/**
 * 统一页面状态管理器
 * 实现P0-006用户体验流程重构目标：
 * - 页面跳转状态保持
 * - 操作流程连贯性
 * - 统一的用户反馈机制
 */

export interface PageState {
  listState?: {
    page: number
    pageSize: number
    filters: any
    sortBy: string
    sortOrder: string
    searchTerm?: string
    selectedItems?: number[]
  }
  formState?: {
    data: any
    isDirty: boolean
    step?: number
  }
  viewState?: {
    activeTab?: string
    expandedSections?: string[]
    scrollPosition?: number
  }
  timestamp: number
}

export interface NavigationContext {
  from?: string
  returnUrl?: string
  preserveState?: boolean
  metadata?: any
}

export interface OperationProgress {
  id: string
  type: 'create' | 'update' | 'delete' | 'batch'
  status: 'pending' | 'running' | 'success' | 'error' | 'cancelled'
  progress: number
  message: string
  total?: number
  completed?: number
  errors?: string[]
  startTime: number
  endTime?: number
}

class PageStateManager {
  private states = new Map<string, PageState>()
  private navigationStack: NavigationContext[] = []
  private operations = new Map<string, OperationProgress>()
  private listeners = new Map<string, Set<(state: PageState) => void>>()
  private operationListeners = new Set<(operation: OperationProgress) => void>()

  /**
   * 保存页面状态
   */
  saveState(pageKey: string, state: Partial<PageState>): void {
    const currentState = this.states.get(pageKey) || { timestamp: Date.now() }
    const newState: PageState = {
      ...currentState,
      ...state,
      timestamp: Date.now()
    }

    this.states.set(pageKey, newState)

    // 持久化到sessionStorage
    try {
      sessionStorage.setItem(`page_state_${pageKey}`, JSON.stringify(newState))
    } catch (error) {
      console.warn('Failed to save state to sessionStorage:', error)
    }

    // 通知监听器
    this.notifyStateListeners(pageKey, newState)
  }

  /**
   * 恢复页面状态
   */
  restoreState(pageKey: string): PageState | null {
    // 先从内存中获取
    let state = this.states.get(pageKey)
    
    if (!state) {
      // 从sessionStorage恢复
      try {
        const stored = sessionStorage.getItem(`page_state_${pageKey}`)
        if (stored) {
          state = JSON.parse(stored)
          if (state) {
            this.states.set(pageKey, state)
          }
        }
      } catch (error) {
        console.warn('Failed to restore state from sessionStorage:', error)
      }
    }

    // 检查状态是否过期（24小时）
    if (state && Date.now() - state.timestamp > 24 * 60 * 60 * 1000) {
      this.clearState(pageKey)
      return null
    }

    return state || null
  }

  /**
   * 清除页面状态
   */
  clearState(pageKey: string): void {
    this.states.delete(pageKey)
    try {
      sessionStorage.removeItem(`page_state_${pageKey}`)
    } catch (error) {
      console.warn('Failed to clear state from sessionStorage:', error)
    }
  }

  /**
   * 导航上下文管理
   */
  pushNavigationContext(context: NavigationContext): void {
    this.navigationStack.push(context)
    
    // 限制栈深度
    if (this.navigationStack.length > 10) {
      this.navigationStack.shift()
    }
  }

  popNavigationContext(): NavigationContext | null {
    return this.navigationStack.pop() || null
  }

  getCurrentNavigationContext(): NavigationContext | null {
    return this.navigationStack[this.navigationStack.length - 1] || null
  }

  /**
   * 智能返回导航
   */
  smartGoBack(defaultUrl: string = '/'): string {
    const context = this.popNavigationContext()
    
    if (context?.returnUrl) {
      return context.returnUrl
    }

    if (context?.from) {
      return context.from
    }

    // 检查浏览器历史
    if (typeof window !== 'undefined' && window.history.length > 1) {
      window.history.back()
      return ''
    }

    return defaultUrl
  }

  /**
   * 操作进度管理
   */
  startOperation(operation: Omit<OperationProgress, 'startTime'>): string {
    const fullOperation: OperationProgress = {
      ...operation,
      startTime: Date.now()
    }

    this.operations.set(operation.id, fullOperation)
    this.notifyOperationListeners(fullOperation)
    
    return operation.id
  }

  updateOperation(id: string, updates: Partial<OperationProgress>): void {
    const operation = this.operations.get(id)
    if (!operation) return

    const updatedOperation = { ...operation, ...updates }
    this.operations.set(id, updatedOperation)
    this.notifyOperationListeners(updatedOperation)
  }

  completeOperation(id: string, success: boolean, message?: string): void {
    const operation = this.operations.get(id)
    if (!operation) return

    const updatedOperation: OperationProgress = {
      ...operation,
      status: success ? 'success' : 'error',
      progress: 100,
      message: message || operation.message,
      endTime: Date.now()
    }

    this.operations.set(id, updatedOperation)
    this.notifyOperationListeners(updatedOperation)

    // 5秒后清理完成的操作
    setTimeout(() => {
      this.operations.delete(id)
    }, 5000)
  }

  getOperation(id: string): OperationProgress | null {
    return this.operations.get(id) || null
  }

  getAllOperations(): OperationProgress[] {
    return Array.from(this.operations.values())
  }

  /**
   * 监听器管理
   */
  onStateChange(pageKey: string, listener: (state: PageState) => void): () => void {
    if (!this.listeners.has(pageKey)) {
      this.listeners.set(pageKey, new Set())
    }
    
    this.listeners.get(pageKey)!.add(listener)

    // 返回取消监听的函数
    return () => {
      this.listeners.get(pageKey)?.delete(listener)
    }
  }

  onOperationChange(listener: (operation: OperationProgress) => void): () => void {
    this.operationListeners.add(listener)

    return () => {
      this.operationListeners.delete(listener)
    }
  }

  private notifyStateListeners(pageKey: string, state: PageState): void {
    const listeners = this.listeners.get(pageKey)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(state)
        } catch (error) {
          console.error('Error in state listener:', error)
        }
      })
    }
  }

  private notifyOperationListeners(operation: OperationProgress): void {
    this.operationListeners.forEach(listener => {
      try {
        listener(operation)
      } catch (error) {
        console.error('Error in operation listener:', error)
      }
    })
  }

  /**
   * 批量操作辅助方法
   */
  async executeBatchOperation<T>(
    items: T[],
    operation: (item: T, index: number) => Promise<void>,
    options: {
      operationId: string
      operationType: OperationProgress['type']
      operationMessage: string
      batchSize?: number
      onProgress?: (completed: number, total: number) => void
    }
  ): Promise<{ success: number; errors: string[] }> {
    const { operationId, operationType, operationMessage, batchSize = 5, onProgress } = options
    
    this.startOperation({
      id: operationId,
      type: operationType,
      status: 'running',
      progress: 0,
      message: operationMessage,
      total: items.length,
      completed: 0,
      errors: []
    })

    const errors: string[] = []
    let completed = 0

    // 分批处理
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      
      await Promise.allSettled(
        batch.map(async (item, batchIndex) => {
          try {
            await operation(item, i + batchIndex)
            completed++
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error)
            errors.push(`项目 ${i + batchIndex + 1}: ${errorMessage}`)
          }
        })
      )

      // 更新进度
      const progress = Math.round((completed / items.length) * 100)
      this.updateOperation(operationId, {
        progress,
        completed,
        errors: [...errors]
      })

      onProgress?.(completed, items.length)
    }

    // 完成操作
    const success = completed > 0
    this.completeOperation(
      operationId,
      success,
      success 
        ? `成功处理 ${completed}/${items.length} 项${errors.length > 0 ? `，${errors.length} 项失败` : ''}`
        : '操作失败'
    )

    return { success: completed, errors }
  }

  /**
   * 清理过期状态
   */
  cleanup(): void {
    const now = Date.now()
    const expireTime = 24 * 60 * 60 * 1000 // 24小时

    for (const [key, state] of this.states.entries()) {
      if (now - state.timestamp > expireTime) {
        this.clearState(key)
      }
    }
  }
}

// 创建全局实例
export const pageStateManager = new PageStateManager()

// 定期清理过期状态
if (typeof window !== 'undefined') {
  setInterval(() => {
    pageStateManager.cleanup()
  }, 60 * 60 * 1000) // 每小时清理一次
}
