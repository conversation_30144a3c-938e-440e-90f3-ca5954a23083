/**
 * 中间件认证工具函数
 * 这些函数可以在服务端中间件中安全使用
 */

/**
 * 简单的JWT格式检查（不验证签名）
 * 仅用于中间件中的基本格式验证
 */
export function isValidJWTFormat(token: string): boolean {
  try {
    if (!token || typeof token !== 'string') {
      return false
    }

    const parts = token.split('.')
    if (parts.length !== 3) {
      return false
    }

    // 尝试解析payload
    const payload = parts[1]
    if (!payload) return false
    const decoded = atob(payload)
    const parsed = JSON.parse(decoded)

    // 检查是否有必要的字段
    if (!parsed.userId || !parsed.username || !parsed.exp) {
      return false
    }

    // 检查是否过期
    const now = Math.floor(Date.now() / 1000)
    if (now >= parsed.exp) {
      return false
    }

    return true
  } catch (error) {
    return false
  }
}

/**
 * 从请求中提取认证token
 */
export function extractTokenFromRequest(request: Request): string | null {
  // 从Authorization头中提取
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // 从Cookie中提取
  const cookieHeader = request.headers.get('cookie')
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=')
      if (key && value) {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, string>)
    
    return cookies['access_token'] || null
  }

  return null
}

/**
 * 检查用户是否已认证（基于token格式）
 */
export function isUserAuthenticated(request: Request): boolean {
  const token = extractTokenFromRequest(request)
  return token ? isValidJWTFormat(token) : false
}
