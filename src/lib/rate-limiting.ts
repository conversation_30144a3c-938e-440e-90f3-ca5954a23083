/**
 * 企业级API限流和防护机制
 */

import { NextRequest } from 'next/server'
import { log, monitoring } from './monitoring'

export interface RateLimitConfig {
  windowMs: number // 时间窗口（毫秒）
  maxRequests: number // 最大请求数
  keyGenerator?: (req: NextRequest) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  message?: string
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  totalHits: number
}

/**
 * 内存存储的限流器（生产环境应使用Redis）
 */
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>()

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    const data = this.store.get(key)
    if (!data) return null

    // 检查是否过期
    if (Date.now() > data.resetTime) {
      this.store.delete(key)
      return null
    }

    return data
  }

  async set(key: string, count: number, resetTime: number): Promise<void> {
    this.store.set(key, { count, resetTime })
  }

  async increment(key: string, windowMs: number): Promise<{ count: number; resetTime: number }> {
    const now = Date.now()
    const existing = await this.get(key)

    if (!existing) {
      const data = { count: 1, resetTime: now + windowMs }
      await this.set(key, data.count, data.resetTime)
      return data
    }

    existing.count++
    await this.set(key, existing.count, existing.resetTime)
    return existing
  }

  // 清理过期数据
  cleanup(): void {
    const now = Date.now()
    for (const [key, data] of this.store.entries()) {
      if (now > data.resetTime) {
        this.store.delete(key)
      }
    }
  }
}

/**
 * 限流器类
 */
class RateLimiter {
  private store = new MemoryStore()
  private cleanupInterval: NodeJS.Timeout

  constructor() {
    // 每分钟清理一次过期数据
    this.cleanupInterval = setInterval(() => {
      this.store.cleanup()
    }, 60000)
  }

  /**
   * 检查请求是否被限流
   */
  async checkLimit(
    request: NextRequest,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const key = this.generateKey(request, config)
    const data = await this.store.increment(key, config.windowMs)

    const allowed = data.count <= config.maxRequests
    const remaining = Math.max(0, config.maxRequests - data.count)

    // 记录限流指标
    monitoring.recordMetric({
      name: 'rate_limit_check',
      value: 1,
      tags: {
        allowed: allowed.toString(),
        endpoint: request.nextUrl.pathname,
        method: request.method
      }
    })

    if (!allowed) {
      log({
        level: 'warn',
        message: 'Rate limit exceeded',
        context: {
          key,
          count: data.count,
          limit: config.maxRequests,
          ip: this.getClientIP(request),
          userAgent: request.headers.get('user-agent'),
          endpoint: request.nextUrl.pathname
        }
      })

      monitoring.recordMetric({
        name: 'rate_limit_exceeded',
        value: 1,
        tags: {
          endpoint: request.nextUrl.pathname,
          method: request.method
        }
      })
    }

    return {
      allowed,
      remaining,
      resetTime: data.resetTime,
      totalHits: data.count
    }
  }

  /**
   * 生成限流键
   */
  private generateKey(request: NextRequest, config: RateLimitConfig): string {
    if (config.keyGenerator) {
      return config.keyGenerator(request)
    }

    // 默认使用 IP + 路径
    const ip = this.getClientIP(request)
    const path = request.nextUrl.pathname
    return `${ip}:${path}`
  }

  /**
   * 获取客户端IP
   */
  private getClientIP(request: NextRequest): string {
    return (
      request.headers.get('x-forwarded-for')?.split(',')[0] ||
      request.headers.get('x-real-ip') ||
      request.headers.get('cf-connecting-ip') ||
      'unknown'
    )
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
  }
}

// 单例实例
const rateLimiter = new RateLimiter()

/**
 * 获取环境相关的限流倍数
 */
function getRateLimitMultiplier(): number {
  const isDev = process.env.NODE_ENV === 'development'
  const devMultiplier = parseInt(process.env['RATE_LIMIT_DEV_MULTIPLIER'] || '5')
  return isDev ? devMultiplier : 1
}

/**
 * 预定义的限流配置
 */
export const RATE_LIMIT_CONFIGS = {
  // 严格限流：登录、注册等敏感操作
  STRICT: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: Math.max(5, 5 * getRateLimitMultiplier()),
    message: '请求过于频繁，请15分钟后再试'
  },

  // 中等限流：一般API操作
  MODERATE: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 100 * getRateLimitMultiplier(),
    message: '请求过于频繁，请稍后再试'
  },

  // 宽松限流：查询操作
  LENIENT: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: Math.max(1000, 1000 * getRateLimitMultiplier()),
    message: '请求过于频繁，请稍后再试'
  },

  // 用户级限流
  PER_USER: {
    windowMs: 60 * 60 * 1000, // 1小时
    maxRequests: 1000,
    keyGenerator: (req: NextRequest) => {
      const userId = req.headers.get('x-user-id') || 'anonymous'
      return `user:${userId}`
    },
    message: '您的请求过于频繁，请稍后再试'
  },

  // IP级限流
  PER_IP: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 100,
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || 
                 req.headers.get('x-real-ip') || 'unknown'
      return `ip:${ip}`
    },
    message: '来自您IP的请求过于频繁，请稍后再试'
  }
} as const

/**
 * DDoS 防护
 */
export class DDoSProtection {
  private suspiciousIPs = new Map<string, { count: number; firstSeen: number }>()
  private blockedIPs = new Set<string>()

  /**
   * 检查是否为DDoS攻击
   */
  checkForDDoS(request: NextRequest): {
    isBlocked: boolean
    isSuspicious: boolean
    reason?: string
  } {
    const ip = this.getClientIP(request)
    const now = Date.now()

    // 检查是否已被阻止
    if (this.blockedIPs.has(ip)) {
      return { isBlocked: true, isSuspicious: true, reason: 'IP已被阻止' }
    }

    // 检查可疑活动
    const suspicious = this.suspiciousIPs.get(ip)
    if (suspicious) {
      // 如果在短时间内有大量请求
      if (now - suspicious.firstSeen < 60000 && suspicious.count > 100) {
        this.blockedIPs.add(ip)
        log({
          level: 'error',
          message: 'DDoS attack detected, IP blocked',
          context: { ip, requestCount: suspicious.count, timeWindow: now - suspicious.firstSeen }
        })

        monitoring.recordMetric({
          name: 'ddos_attack_blocked',
          value: 1,
          tags: { ip, reason: 'high_frequency_requests' }
        })

        return { isBlocked: true, isSuspicious: true, reason: 'DDoS攻击检测' }
      }

      suspicious.count++
    } else {
      this.suspiciousIPs.set(ip, { count: 1, firstSeen: now })
    }

    // 清理旧数据
    this.cleanup()

    return { isBlocked: false, isSuspicious: false }
  }

  private getClientIP(request: NextRequest): string {
    return (
      request.headers.get('x-forwarded-for')?.split(',')[0] ||
      request.headers.get('x-real-ip') ||
      'unknown'
    )
  }

  private cleanup(): void {
    const now = Date.now()
    const oneHourAgo = now - 60 * 60 * 1000

    // 清理1小时前的可疑IP记录
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (data.firstSeen < oneHourAgo) {
        this.suspiciousIPs.delete(ip)
      }
    }

    // 清理24小时前的阻止IP（可根据需要调整）
    // 这里简化处理，实际应该有更复杂的解封机制
  }

  /**
   * 手动解封IP
   */
  unblockIP(ip: string): void {
    this.blockedIPs.delete(ip)
    this.suspiciousIPs.delete(ip)
    log({
      level: 'info',
      message: 'IP manually unblocked',
      context: { ip }
    })
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    blockedIPs: number
    suspiciousIPs: number
    blockedIPsList: string[]
  } {
    return {
      blockedIPs: this.blockedIPs.size,
      suspiciousIPs: this.suspiciousIPs.size,
      blockedIPsList: Array.from(this.blockedIPs)
    }
  }
}

// 单例实例
export const ddosProtection = new DDoSProtection()

// 导出主要功能
export { rateLimiter }
export const checkRateLimit = (request: NextRequest, config: RateLimitConfig) => 
  rateLimiter.checkLimit(request, config)

export default rateLimiter
