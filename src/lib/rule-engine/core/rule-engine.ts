/**
 * 规则引擎核心类
 * 
 * 负责协调规则解析、执行、调度和结果处理
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { 
  RuleExecutionContext, 
  RuleExecutionResult, 
  RuleExecutionRequest,
  RuleEngineConfig,
  RuleEngineEvent,
  RuleEngineListener,
  IRuleParser,
  IRuleExecutor,
  IRuleScheduler,
  IResultProcessor
} from '../types/engine-types'
import { getEngineConfig } from '../config/engine-config'
import { RuleParser } from '../parsers/rule-parser'
import { RuleExecutor } from '../executors/rule-executor'
import { RuleScheduler } from '../scheduler/rule-scheduler'
import { ResultProcessor } from '../processors/result-processor'
import { generateExecutionId } from '../utils/rule-utils'

export class RuleEngine {
  private config: RuleEngineConfig
  private parser: IRuleParser
  private executor: IRuleExecutor
  private scheduler: IRuleScheduler
  private processor: IResultProcessor
  private listeners: RuleEngineListener[] = []
  private runningExecutions = new Map<string, Promise<RuleExecutionResult>>()

  constructor(config?: Partial<RuleEngineConfig>) {
    this.config = { ...getEngineConfig(), ...config }
    this.parser = new RuleParser()
    this.executor = new RuleExecutor(this.config)
    this.scheduler = new RuleScheduler(this)
    this.processor = new ResultProcessor()
  }

  /**
   * 执行单个规则
   */
  async executeRule(
    rule: SupervisionRule, 
    context?: Partial<RuleExecutionContext>
  ): Promise<RuleExecutionResult> {
    const executionId = generateExecutionId()
    const fullContext: RuleExecutionContext = {
      ruleId: rule.id,
      executionId,
      startTime: new Date(),
      timeout: this.config.defaultTimeout,
      ...context
    }

    // 检查并发限制
    if (this.runningExecutions.size >= this.config.maxConcurrentExecutions) {
      throw new Error('已达到最大并发执行数限制')
    }

    // 触发执行开始事件
    this.emitEvent({
      type: 'EXECUTION_STARTED',
      timestamp: new Date(),
      ruleId: rule.id,
      executionId,
      data: { context: fullContext }
    })

    try {
      // 解析规则
      const parsedRule = await this.parser.parse(rule)
      
      // 执行规则
      const executionPromise = this.executor.execute(rule, fullContext)
      this.runningExecutions.set(executionId, executionPromise)
      
      const result = await executionPromise
      
      // 处理结果
      await this.processor.process(result)
      
      // 触发执行完成事件
      this.emitEvent({
        type: 'EXECUTION_COMPLETED',
        timestamp: new Date(),
        ruleId: rule.id,
        executionId,
        data: { result }
      })

      return result

    } catch (error) {
      const errorResult: RuleExecutionResult = {
        executionId,
        ruleId: rule.id,
        status: 'FAILED',
        startTime: fullContext.startTime,
        endTime: new Date(),
        duration: Date.now() - fullContext.startTime.getTime(),
        processedCount: 0,
        matchedCount: 0,
        results: [],
        error: error instanceof Error ? error.message : String(error)
      }

      // 触发执行失败事件
      this.emitEvent({
        type: 'EXECUTION_FAILED',
        timestamp: new Date(),
        ruleId: rule.id,
        executionId,
        data: { error: errorResult.error }
      })

      // 处理错误结果
      await this.processor.process(errorResult)

      throw error
    } finally {
      this.runningExecutions.delete(executionId)
    }
  }

  /**
   * 批量执行规则
   */
  async executeRules(request: RuleExecutionRequest): Promise<string[]> {
    const executionIds: string[] = []

    for (const ruleId of request.ruleIds) {
      try {
        // 获取规则信息（这里需要从数据库获取）
        const rule = await this.getRuleById(ruleId)
        if (!rule) {
          console.warn(`规则 ${ruleId} 不存在，跳过执行`)
          continue
        }

        const context: Partial<RuleExecutionContext> = {
          userId: request.userId,
          parameters: request.parameters
        }

        if (request.async) {
          // 异步执行
          this.executeRule(rule, context).catch(error => {
            console.error(`规则 ${ruleId} 异步执行失败:`, error)
          })
          executionIds.push(generateExecutionId())
        } else {
          // 同步执行
          const result = await this.executeRule(rule, context)
          executionIds.push(result.executionId)
        }
      } catch (error) {
        console.error(`规则 ${ruleId} 执行失败:`, error)
      }
    }

    return executionIds
  }

  /**
   * 调度规则执行
   */
  async scheduleRule(ruleId: number, cronExpression: string): Promise<void> {
    await this.scheduler.schedule({
      ruleId,
      enabled: true,
      cronExpression,
      maxRetries: this.config.retryAttempts,
      retryDelay: this.config.retryDelay,
      timeout: this.config.defaultTimeout
    })
  }

  /**
   * 取消规则调度
   */
  async unscheduleRule(ruleId: number): Promise<void> {
    await this.scheduler.unschedule(ruleId)
  }

  /**
   * 立即执行规则
   */
  async executeNow(request: RuleExecutionRequest): Promise<string> {
    return await this.scheduler.executeNow(request)
  }

  /**
   * 获取运行中的执行
   */
  getRunningExecutions(): string[] {
    return Array.from(this.runningExecutions.keys())
  }

  /**
   * 获取调度规则
   */
  async getScheduledRules() {
    return await this.scheduler.getScheduledRules()
  }

  /**
   * 取消执行
   */
  async cancelExecution(executionId: string): Promise<boolean> {
    const execution = this.runningExecutions.get(executionId)
    if (execution) {
      // 这里可以实现取消逻辑
      this.runningExecutions.delete(executionId)
      return true
    }
    return false
  }

  /**
   * 添加事件监听器
   */
  addEventListener(listener: RuleEngineListener): void {
    this.listeners.push(listener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(listener: RuleEngineListener): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(event: RuleEngineEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('事件监听器执行失败:', error)
      }
    })
  }

  /**
   * 获取规则信息
   */
  private async getRuleById(ruleId: number): Promise<SupervisionRule | null> {
    try {
      // 动态导入避免循环依赖
      const { getSupervisionRuleById } = await import('@/lib/supervision-rule-service')
      return await getSupervisionRuleById(ruleId)
    } catch (error) {
      console.error(`获取规则 ${ruleId} 失败:`, error)
      return null
    }
  }

  /**
   * 关闭规则引擎
   */
  async shutdown(): Promise<void> {
    // 等待所有执行完成
    await Promise.allSettled(Array.from(this.runningExecutions.values()))
    
    // 清理资源
    this.runningExecutions.clear()
    this.listeners.length = 0
  }
}
