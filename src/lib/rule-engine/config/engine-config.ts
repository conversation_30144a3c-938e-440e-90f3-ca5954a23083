/**
 * 规则引擎配置
 */

import { RuleEngineConfig } from '../types/engine-types'

// 默认规则引擎配置
export const DEFAULT_ENGINE_CONFIG: RuleEngineConfig = {
  maxConcurrentExecutions: 5,
  defaultTimeout: 30000, // 30秒
  retryAttempts: 3,
  retryDelay: 1000, // 1秒
  enableLogging: true,
  enableMetrics: true,
  cacheEnabled: true,
  cacheTtl: 300000, // 5分钟
}

// 规则类型配置
export const RULE_TYPE_CONFIG = {
  SQL: {
    timeout: 60000, // SQL规则超时时间更长
    maxRetries: 2,
    cacheEnabled: true,
  },
  DSL: {
    timeout: 30000,
    maxRetries: 3,
    cacheEnabled: true,
  },
  JAVASCRIPT: {
    timeout: 15000, // JavaScript规则超时时间较短
    maxRetries: 1,
    cacheEnabled: false, // JavaScript规则不缓存
  },
}

// 规则分类配置
export const RULE_CATEGORY_CONFIG = {
  COST_CONTROL: {
    priority: 8,
    timeout: 45000,
    enableNotification: true,
  },
  FRAUD_DETECTION: {
    priority: 10, // 欺诈检测优先级最高
    timeout: 60000,
    enableNotification: true,
  },
  COMPLIANCE_CHECK: {
    priority: 6,
    timeout: 30000,
    enableNotification: false,
  },
  QUALITY_ASSURANCE: {
    priority: 4,
    timeout: 30000,
    enableNotification: false,
  },
  STATISTICAL_ANALYSIS: {
    priority: 2,
    timeout: 120000, // 统计分析允许更长时间
    enableNotification: false,
  },
}

// 严重程度配置
export const SEVERITY_LEVEL_CONFIG = {
  LOW: {
    priority: 1,
    notificationLevel: 'INFO',
    autoProcess: true,
  },
  MEDIUM: {
    priority: 5,
    notificationLevel: 'WARNING',
    autoProcess: true,
  },
  HIGH: {
    priority: 8,
    notificationLevel: 'ERROR',
    autoProcess: false, // 高严重程度需要人工处理
  },
  CRITICAL: {
    priority: 10,
    notificationLevel: 'CRITICAL',
    autoProcess: false,
  },
}

// 执行模式配置
export const EXECUTION_MODE_CONFIG = {
  IMMEDIATE: {
    timeout: 30000,
    maxConcurrent: 3,
  },
  SCHEDULED: {
    timeout: 300000, // 定时执行允许更长时间
    maxConcurrent: 10,
  },
  BATCH: {
    timeout: 600000, // 批量执行允许最长时间
    maxConcurrent: 2,
  },
}

// 数据库连接配置
export const DATABASE_CONFIG = {
  connectionTimeout: 10000,
  queryTimeout: 60000,
  maxConnections: 10,
  idleTimeout: 300000,
}

// 缓存配置
export const CACHE_CONFIG = {
  ruleCache: {
    ttl: 300000, // 5分钟
    maxSize: 1000,
  },
  resultCache: {
    ttl: 600000, // 10分钟
    maxSize: 5000,
  },
  metadataCache: {
    ttl: 3600000, // 1小时
    maxSize: 100,
  },
}

// 日志配置
export const LOGGING_CONFIG = {
  level: 'INFO',
  enableConsole: true,
  enableFile: true,
  enableDatabase: true,
  maxFileSize: '10MB',
  maxFiles: 5,
}

// 监控配置
export const MONITORING_CONFIG = {
  enableMetrics: true,
  metricsInterval: 60000, // 1分钟
  enableHealthCheck: true,
  healthCheckInterval: 30000, // 30秒
  enablePerformanceTracking: true,
}

// 通知配置
export const NOTIFICATION_CONFIG = {
  enableEmail: false,
  enableSms: false,
  enableWebhook: true,
  enableInApp: true,
  retryAttempts: 3,
  retryDelay: 5000,
}

// 安全配置
export const SECURITY_CONFIG = {
  enableSandbox: true, // JavaScript规则沙箱
  maxExecutionTime: 300000, // 最大执行时间
  maxMemoryUsage: 100 * 1024 * 1024, // 100MB
  allowedModules: ['lodash', 'moment', 'crypto'], // 允许的模块
  blockedFunctions: ['eval', 'Function', 'setTimeout', 'setInterval'], // 禁用的函数
}

/**
 * 获取规则引擎配置
 */
export function getEngineConfig(): RuleEngineConfig {
  return {
    ...DEFAULT_ENGINE_CONFIG,
    // 可以从环境变量或配置文件覆盖
    maxConcurrentExecutions: parseInt(process.env['RULE_ENGINE_MAX_CONCURRENT'] || '5'),
    defaultTimeout: parseInt(process.env['RULE_ENGINE_TIMEOUT'] || '30000'),
    enableLogging: process.env['RULE_ENGINE_LOGGING'] !== 'false',
    enableMetrics: process.env['RULE_ENGINE_METRICS'] !== 'false',
  }
}

/**
 * 根据规则类型获取配置
 */
export function getRuleTypeConfig(ruleType: string) {
  return RULE_TYPE_CONFIG[ruleType as keyof typeof RULE_TYPE_CONFIG] || RULE_TYPE_CONFIG.SQL
}

/**
 * 根据规则分类获取配置
 */
export function getRuleCategoryConfig(category: string) {
  return RULE_CATEGORY_CONFIG[category as keyof typeof RULE_CATEGORY_CONFIG] || RULE_CATEGORY_CONFIG.COMPLIANCE_CHECK
}

/**
 * 根据严重程度获取配置
 */
export function getSeverityLevelConfig(severity: string) {
  return SEVERITY_LEVEL_CONFIG[severity as keyof typeof SEVERITY_LEVEL_CONFIG] || SEVERITY_LEVEL_CONFIG.MEDIUM
}
