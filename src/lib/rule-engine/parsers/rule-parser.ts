/**
 * 规则解析器
 * 
 * 负责解析不同类型的规则内容，提取参数和依赖关系
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { ParsedRule, RuleParameter, IRuleParser } from '../types/engine-types'
import { validateRuleContent, parseSqlParameters } from '../utils/rule-utils'

export class RuleParser implements IRuleParser {
  
  /**
   * 解析规则
   */
  async parse(rule: SupervisionRule): Promise<ParsedRule> {
    const ruleType = this.determineRuleType(rule)
    
    switch (ruleType) {
      case 'SQL':
        return this.parseSqlRule(rule)
      case 'DSL':
        return this.parseDslRule(rule)
      case 'JAVASCRIPT':
        return this.parseJavaScriptRule(rule)
      default:
        throw new Error(`不支持的规则类型: ${ruleType}`)
    }
  }

  /**
   * 验证规则内容
   */
  async validate(content: string, type: string): Promise<boolean> {
    return validateRuleContent(content, type)
  }

  /**
   * 获取支持的规则类型
   */
  getSupportedTypes(): string[] {
    return ['SQL', 'DSL', 'JAVASCRIPT']
  }

  /**
   * 确定规则类型
   */
  private determineRuleType(rule: SupervisionRule): string {
    // 优先使用SQL内容
    if (rule.ruleSql && rule.ruleSql.trim()) {
      return 'SQL'
    }
    
    // 其次使用DSL内容
    if (rule.ruleDsl && rule.ruleDsl.trim()) {
      return 'DSL'
    }
    
    // 最后尝试解析规则内容
    const content = rule.ruleContent.trim()
    
    // 检查是否为SQL
    if (content.toUpperCase().startsWith('SELECT')) {
      return 'SQL'
    }
    
    // 检查是否为JSON格式的DSL
    try {
      JSON.parse(content)
      return 'DSL'
    } catch {
      // 不是JSON格式
    }
    
    // 检查是否为JavaScript
    if (content.includes('function') || content.includes('=>') || content.includes('return')) {
      return 'JAVASCRIPT'
    }
    
    // 默认为SQL
    return 'SQL'
  }

  /**
   * 解析SQL规则
   */
  private async parseSqlRule(rule: SupervisionRule): Promise<ParsedRule> {
    const content = rule.ruleSql || rule.ruleContent
    const parameters = this.extractSqlParameters(content)
    const dependencies = this.extractSqlDependencies(content)
    
    return {
      ruleId: rule.id,
      type: 'SQL',
      content,
      parameters,
      dependencies,
      metadata: {
        originalType: rule.ruleType,
        category: rule.ruleCategory,
        severity: rule.severityLevel,
        priority: rule.priorityLevel
      }
    }
  }

  /**
   * 解析DSL规则
   */
  private async parseDslRule(rule: SupervisionRule): Promise<ParsedRule> {
    const content = rule.ruleDsl || rule.ruleContent
    
    try {
      const dslObject = JSON.parse(content)
      const parameters = this.extractDslParameters(dslObject)
      const dependencies = this.extractDslDependencies(dslObject)
      
      return {
        ruleId: rule.id,
        type: 'DSL',
        content,
        parameters,
        dependencies,
        metadata: {
          originalType: rule.ruleType,
          category: rule.ruleCategory,
          severity: rule.severityLevel,
          priority: rule.priorityLevel,
          dslObject
        }
      }
    } catch (error) {
      throw new Error(`DSL解析失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 解析JavaScript规则
   */
  private async parseJavaScriptRule(rule: SupervisionRule): Promise<ParsedRule> {
    const content = rule.ruleContent
    const parameters = this.extractJavaScriptParameters(content)
    const dependencies = this.extractJavaScriptDependencies(content)
    
    return {
      ruleId: rule.id,
      type: 'JAVASCRIPT',
      content,
      parameters,
      dependencies,
      metadata: {
        originalType: rule.ruleType,
        category: rule.ruleCategory,
        severity: rule.severityLevel,
        priority: rule.priorityLevel
      }
    }
  }

  /**
   * 提取SQL参数
   */
  private extractSqlParameters(sql: string): RuleParameter[] {
    const paramNames = parseSqlParameters(sql)
    
    return paramNames.map(name => ({
      name,
      type: this.inferParameterType(name),
      required: true,
      description: `SQL参数: ${name}`
    }))
  }

  /**
   * 提取SQL依赖
   */
  private extractSqlDependencies(sql: string): string[] {
    const dependencies: string[] = []
    
    // 提取表名
    const tableRegex = /FROM\s+(\w+)/gi
    let match
    while ((match = tableRegex.exec(sql)) !== null) {
      if (match[1]) {
        dependencies.push(match[1])
      }
    }

    // 提取JOIN表名
    const joinRegex = /JOIN\s+(\w+)/gi
    while ((match = joinRegex.exec(sql)) !== null) {
      if (match[1]) {
        dependencies.push(match[1])
      }
    }
    
    return [...new Set(dependencies)] // 去重
  }

  /**
   * 提取DSL参数
   */
  private extractDslParameters(dslObject: any): RuleParameter[] {
    const parameters: RuleParameter[] = []
    
    if (dslObject.parameters && Array.isArray(dslObject.parameters)) {
      for (const param of dslObject.parameters) {
        parameters.push({
          name: param.name || '',
          type: param.type || 'string',
          required: param.required !== false,
          defaultValue: param.defaultValue,
          description: param.description,
          validation: param.validation
        })
      }
    }
    
    return parameters
  }

  /**
   * 提取DSL依赖
   */
  private extractDslDependencies(dslObject: any): string[] {
    const dependencies: string[] = []
    
    if (dslObject.dependencies && Array.isArray(dslObject.dependencies)) {
      dependencies.push(...dslObject.dependencies)
    }
    
    // 从条件中提取表名
    if (dslObject.conditions) {
      this.extractDependenciesFromConditions(dslObject.conditions, dependencies)
    }
    
    return [...new Set(dependencies)]
  }

  /**
   * 从条件中提取依赖
   */
  private extractDependenciesFromConditions(conditions: any, dependencies: string[]): void {
    if (Array.isArray(conditions)) {
      for (const condition of conditions) {
        this.extractDependenciesFromConditions(condition, dependencies)
      }
    } else if (typeof conditions === 'object' && conditions !== null) {
      if (conditions.table) {
        dependencies.push(conditions.table)
      }
      if (conditions.source) {
        dependencies.push(conditions.source)
      }
      
      // 递归处理嵌套条件
      for (const value of Object.values(conditions)) {
        if (typeof value === 'object') {
          this.extractDependenciesFromConditions(value, dependencies)
        }
      }
    }
  }

  /**
   * 提取JavaScript参数
   */
  private extractJavaScriptParameters(js: string): RuleParameter[] {
    const parameters: RuleParameter[] = []
    
    // 简单的参数提取，查找函数参数
    const functionRegex = /function\s*\w*\s*\(([^)]*)\)/g
    const arrowFunctionRegex = /\(([^)]*)\)\s*=>/g
    
    let match
    while ((match = functionRegex.exec(js)) !== null) {
      if (match[1]) {
        const params = match[1].split(',').map(p => p.trim()).filter(p => p)
        for (const param of params) {
          parameters.push({
            name: param,
            type: 'string',
            required: true,
            description: `JavaScript参数: ${param}`
          })
        }
      }
    }
    
    while ((match = arrowFunctionRegex.exec(js)) !== null) {
      if (match[1]) {
        const params = match[1].split(',').map(p => p.trim()).filter(p => p)
        for (const param of params) {
          parameters.push({
            name: param,
            type: 'string',
            required: true,
            description: `JavaScript参数: ${param}`
          })
        }
      }
    }
    
    return parameters
  }

  /**
   * 提取JavaScript依赖
   */
  private extractJavaScriptDependencies(js: string): string[] {
    const dependencies: string[] = []
    
    // 提取require语句
    const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g
    let match
    while ((match = requireRegex.exec(js)) !== null) {
      if (match[1]) {
        dependencies.push(match[1])
      }
    }

    // 提取import语句
    const importRegex = /import\s+.*\s+from\s+['"`]([^'"`]+)['"`]/g
    while ((match = importRegex.exec(js)) !== null) {
      if (match[1]) {
        dependencies.push(match[1])
      }
    }
    
    return dependencies
  }

  /**
   * 推断参数类型
   */
  private inferParameterType(paramName: string): 'string' | 'number' | 'boolean' | 'date' | 'array' {
    const name = paramName.toLowerCase()
    
    if (name.includes('date') || name.includes('time')) {
      return 'date'
    }
    if (name.includes('count') || name.includes('amount') || name.includes('price') || name.includes('cost')) {
      return 'number'
    }
    if (name.includes('flag') || name.includes('is') || name.includes('has')) {
      return 'boolean'
    }
    if (name.includes('list') || name.includes('array') || name.includes('ids')) {
      return 'array'
    }
    
    return 'string'
  }
}
