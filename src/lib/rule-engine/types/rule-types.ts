// 规则执行上下文
export interface RuleExecutionContext {
  ruleId: string
  data: Record<string, any>
  startTime: number
  timeout?: number
  userId?: string
  metadata?: Record<string, any>
}

// 规则执行结果
export interface RuleExecutionResult {
  success: boolean
  data?: any
  error?: string
  message?: string
  executionTime: number
  metadata?: Record<string, any>
}

// 规则类型
export enum RuleType {
  SQL = 'sql',
  DSL = 'dsl',
  JAVASCRIPT = 'javascript'
}

// 规则状态
export enum RuleStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived'
}

// 规则优先级
export enum RulePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 规则定义
export interface RuleDefinition {
  id: string
  name: string
  description?: string
  type: RuleType
  content: string
  status: RuleStatus
  priority: RulePriority
  category?: string
  tags?: string[]
  conditions?: RuleCondition[]
  actions?: RuleAction[]
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
  createdBy: string
  updatedBy?: string
}

// 规则条件
export interface RuleCondition {
  field: string
  operator: string
  value: any
  type?: string
}

// 规则动作
export interface RuleAction {
  type: string
  config: Record<string, any>
}

// 规则执行历史
export interface RuleExecutionHistory {
  id: string
  ruleId: string
  executionId: string
  status: 'success' | 'failure' | 'timeout'
  result?: any
  error?: string
  executionTime: number
  startTime: Date
  endTime: Date
  context: RuleExecutionContext
}

// 规则引擎配置
export interface RuleEngineConfig {
  maxConcurrentExecutions: number
  defaultTimeout: number
  enableLogging: boolean
  enableMetrics: boolean
  retryAttempts: number
  retryDelay: number
}

// 规则验证结果
export interface RuleValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

// 规则测试用例
export interface RuleTestCase {
  id: string
  name: string
  description?: string
  input: Record<string, any>
  expectedOutput: any
  metadata?: Record<string, any>
}

// 规则性能指标
export interface RulePerformanceMetrics {
  ruleId: string
  executionCount: number
  averageExecutionTime: number
  successRate: number
  errorRate: number
  lastExecutionTime: Date
  totalExecutionTime: number
}
