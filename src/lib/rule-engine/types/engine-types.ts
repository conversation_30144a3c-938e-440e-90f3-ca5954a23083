/**
 * 规则引擎核心类型定义
 */

import { SupervisionRule, ExecutionStatus, ResultType, RiskLevel } from '@/types/supervision-rule'

// 规则执行上下文
export interface RuleExecutionContext {
  ruleId: number
  executionId: string
  userId?: number
  parameters?: Record<string, any>
  startTime: Date
  timeout?: number // 超时时间（毫秒）
}

// 规则执行结果
export interface RuleExecutionResult {
  executionId: string
  ruleId: number
  status: ExecutionStatus
  startTime: Date
  endTime?: Date
  duration?: number
  processedCount: number
  matchedCount: number
  results: RuleMatchResult[]
  error?: string
  metadata?: Record<string, any>
}

// 规则匹配结果
export interface RuleMatchResult {
  id: string
  caseId?: number
  resultType: ResultType
  riskLevel: RiskLevel
  score: number
  description: string
  details: Record<string, any>
  evidence: RuleEvidence[]
}

// 规则证据
export interface RuleEvidence {
  type: 'SQL_RESULT' | 'CALCULATION' | 'COMPARISON' | 'PATTERN_MATCH'
  description: string
  value: any
  expected?: any
  metadata?: Record<string, any>
}

// 规则解析结果
export interface ParsedRule {
  ruleId: number
  type: 'SQL' | 'DSL' | 'JAVASCRIPT'
  content: string
  parameters: RuleParameter[]
  dependencies: string[]
  metadata: Record<string, any>
}

// 规则参数
export interface RuleParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array'
  required: boolean
  defaultValue?: any
  description?: string
  validation?: RuleParameterValidation
}

// 规则参数验证
export interface RuleParameterValidation {
  min?: number
  max?: number
  pattern?: string
  options?: any[]
}

// 规则调度配置
export interface RuleScheduleConfig {
  ruleId: number
  enabled: boolean
  cronExpression?: string
  interval?: number // 间隔时间（毫秒）
  maxRetries: number
  retryDelay: number
  timeout: number
  parameters?: Record<string, any>
}

// 规则执行请求
export interface RuleExecutionRequest {
  ruleIds: number[]
  parameters?: Record<string, any>
  async?: boolean
  priority?: number
  userId?: number
}

// 规则引擎配置
export interface RuleEngineConfig {
  maxConcurrentExecutions: number
  defaultTimeout: number
  retryAttempts: number
  retryDelay: number
  enableLogging: boolean
  enableMetrics: boolean
  cacheEnabled: boolean
  cacheTtl: number
}

// 规则执行统计
export interface RuleExecutionStats {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageExecutionTime: number
  totalProcessedRecords: number
  totalMatchedRecords: number
  lastExecutionTime?: Date
}

// 规则引擎事件
export interface RuleEngineEvent {
  type: 'EXECUTION_STARTED' | 'EXECUTION_COMPLETED' | 'EXECUTION_FAILED' | 'RULE_MATCHED'
  timestamp: Date
  ruleId: number
  executionId: string
  data: any
}

// 规则引擎监听器
export type RuleEngineListener = (event: RuleEngineEvent) => void

// 规则执行器接口
export interface IRuleExecutor {
  execute(rule: SupervisionRule, context: RuleExecutionContext): Promise<RuleExecutionResult>
  validate(rule: SupervisionRule): Promise<boolean>
  getCapabilities(): string[]
}

// 规则解析器接口
export interface IRuleParser {
  parse(rule: SupervisionRule): Promise<ParsedRule>
  validate(content: string, type: string): Promise<boolean>
  getSupportedTypes(): string[]
}

// 规则调度器接口
export interface IRuleScheduler {
  schedule(config: RuleScheduleConfig): Promise<void>
  unschedule(ruleId: number): Promise<void>
  executeNow(request: RuleExecutionRequest): Promise<string>
  getScheduledRules(): Promise<RuleScheduleConfig[]>
}

// 结果处理器接口
export interface IResultProcessor {
  process(result: RuleExecutionResult): Promise<void>
  notify(result: RuleExecutionResult): Promise<void>
  store(result: RuleExecutionResult): Promise<void>
}
