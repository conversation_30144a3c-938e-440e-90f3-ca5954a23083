/**
 * 结果处理器
 * 
 * 负责处理规则执行结果，包括存储、通知和后续处理
 */

import { RuleExecutionResult, IResultProcessor } from '../types/engine-types'
import { executeQuery } from '@/lib/database'

export class ResultProcessor implements IResultProcessor {
  
  /**
   * 处理执行结果
   */
  async process(result: RuleExecutionResult): Promise<void> {
    try {
      // 1. 存储执行记录
      await this.store(result)
      
      // 2. 处理匹配结果
      if (result.results.length > 0) {
        await this.processMatchResults(result)
      }
      
      // 3. 发送通知
      await this.notify(result)
      
      // 4. 更新规则统计
      await this.updateRuleStatistics(result)
      
    } catch (error) {
      console.error('结果处理失败:', error)
      throw error
    }
  }

  /**
   * 存储执行结果
   */
  async store(result: RuleExecutionResult): Promise<void> {
    try {
      // 存储执行记录
      await this.storeExecutionLog(result)
      
      // 存储匹配结果
      if (result.results.length > 0) {
        await this.storeExecutionResults(result)
      }
      
    } catch (error) {
      console.error('存储执行结果失败:', error)
      throw error
    }
  }

  /**
   * 发送通知
   */
  async notify(result: RuleExecutionResult): Promise<void> {
    try {
      // 根据结果状态和严重程度决定是否发送通知
      if (this.shouldNotify(result)) {
        await this.sendNotification(result)
      }
    } catch (error) {
      console.error('发送通知失败:', error)
      // 通知失败不应该影响主流程
    }
  }

  /**
   * 存储执行日志
   */
  private async storeExecutionLog(result: RuleExecutionResult): Promise<void> {
    const sql = `
      INSERT INTO RULE_EXECUTION_LOG (
        ID, RULE_ID, EXECUTION_ID, EXECUTION_STATUS,
        STARTED_AT, ENDED_AT, EXECUTION_DURATION,
        PROCESSED_RECORD_COUNT, MATCHED_RECORD_COUNT,
        ERROR_MESSAGE, EXECUTION_RESULT, CREATED_AT
      ) VALUES (
        RULE_EXECUTION_LOG_SEQ.NEXTVAL, :ruleId, :executionId, :status,
        :startTime, :endTime, :duration,
        :processedCount, :matchedCount,
        :errorMessage, :executionResult, SYSDATE
      )
    `

    const params = {
      ruleId: result.ruleId,
      executionId: result.executionId,
      status: result.status,
      startTime: result.startTime,
      endTime: result.endTime,
      duration: result.duration,
      processedCount: result.processedCount,
      matchedCount: result.matchedCount,
      errorMessage: result.error || null,
      executionResult: JSON.stringify({
        metadata: result.metadata,
        summary: {
          totalResults: result.results.length,
          riskLevels: this.summarizeRiskLevels(result.results)
        }
      })
    }

    await executeQuery(sql, params)
  }

  /**
   * 存储执行结果
   */
  private async storeExecutionResults(result: RuleExecutionResult): Promise<void> {
    for (const matchResult of result.results) {
      const sql = `
        INSERT INTO RULE_EXECUTION_RESULT (
          ID, EXECUTION_LOG_ID, RULE_ID, CASE_ID,
          RESULT_TYPE, RISK_LEVEL, MATCH_SCORE,
          RESULT_DESCRIPTION, RESULT_DETAILS, EVIDENCE_DATA,
          RESULT_STATUS, CREATED_AT
        ) VALUES (
          RULE_EXECUTION_RESULT_SEQ.NEXTVAL,
          (SELECT ID FROM RULE_EXECUTION_LOG WHERE EXECUTION_ID = :executionId),
          :ruleId, :caseId, :resultType, :riskLevel, :matchScore,
          :description, :details, :evidence, 'PENDING', SYSDATE
        )
      `

      const params = {
        executionId: result.executionId,
        ruleId: result.ruleId,
        caseId: matchResult.caseId,
        resultType: matchResult.resultType,
        riskLevel: matchResult.riskLevel,
        matchScore: matchResult.score,
        description: matchResult.description,
        details: JSON.stringify(matchResult.details),
        evidence: JSON.stringify(matchResult.evidence)
      }

      await executeQuery(sql, params)
    }
  }

  /**
   * 处理匹配结果
   */
  private async processMatchResults(result: RuleExecutionResult): Promise<void> {
    // 根据风险等级和结果类型进行不同处理
    for (const matchResult of result.results) {
      switch (matchResult.riskLevel) {
        case 'CRITICAL':
          await this.processCriticalResult(matchResult, result)
          break
        case 'HIGH':
          await this.processHighRiskResult(matchResult, result)
          break
        case 'MEDIUM':
          await this.processMediumRiskResult(matchResult, result)
          break
        case 'LOW':
          await this.processLowRiskResult(matchResult, result)
          break
      }
    }
  }

  /**
   * 处理严重风险结果
   */
  private async processCriticalResult(matchResult: any, executionResult: RuleExecutionResult): Promise<void> {
    // 严重风险需要立即处理
    // 1. 创建紧急工单
    // 2. 发送紧急通知
    // 3. 可能需要自动暂停相关业务
    console.log('处理严重风险结果:', matchResult.id)
  }

  /**
   * 处理高风险结果
   */
  private async processHighRiskResult(matchResult: any, executionResult: RuleExecutionResult): Promise<void> {
    // 高风险需要优先处理
    // 1. 创建高优先级工单
    // 2. 发送通知给相关人员
    console.log('处理高风险结果:', matchResult.id)
  }

  /**
   * 处理中等风险结果
   */
  private async processMediumRiskResult(matchResult: any, executionResult: RuleExecutionResult): Promise<void> {
    // 中等风险正常处理
    // 1. 创建普通工单
    // 2. 加入处理队列
    console.log('处理中等风险结果:', matchResult.id)
  }

  /**
   * 处理低风险结果
   */
  private async processLowRiskResult(matchResult: any, executionResult: RuleExecutionResult): Promise<void> {
    // 低风险可以批量处理或自动处理
    // 1. 记录日志
    // 2. 可能自动标记为已处理
    console.log('处理低风险结果:', matchResult.id)
  }

  /**
   * 更新规则统计
   */
  private async updateRuleStatistics(result: RuleExecutionResult): Promise<void> {
    const sql = `
      UPDATE RULE_SUPERVISION 
      SET 
        EXECUTION_COUNT = EXECUTION_COUNT + 1,
        SUCCESS_COUNT = SUCCESS_COUNT + CASE WHEN :status = 'SUCCESS' THEN 1 ELSE 0 END,
        LAST_EXECUTED_AT = :executedAt,
        UPDATED_AT = SYSDATE
      WHERE ID = :ruleId
    `

    const params = {
      status: result.status,
      executedAt: result.endTime || result.startTime,
      ruleId: result.ruleId
    }

    await executeQuery(sql, params)
  }

  /**
   * 判断是否需要发送通知
   */
  private shouldNotify(result: RuleExecutionResult): boolean {
    // 执行失败总是通知
    if (result.status === 'FAILED') {
      return true
    }

    // 有匹配结果且包含高风险或严重风险时通知
    if (result.results.length > 0) {
      const hasHighRisk = result.results.some(r => 
        r.riskLevel === 'HIGH' || r.riskLevel === 'CRITICAL'
      )
      return hasHighRisk
    }

    return false
  }

  /**
   * 发送通知
   */
  private async sendNotification(result: RuleExecutionResult): Promise<void> {
    const notification = {
      type: result.status === 'FAILED' ? 'EXECUTION_FAILED' : 'HIGH_RISK_DETECTED',
      title: this.generateNotificationTitle(result),
      message: this.generateNotificationMessage(result),
      data: {
        ruleId: result.ruleId,
        executionId: result.executionId,
        matchedCount: result.matchedCount,
        riskLevels: this.summarizeRiskLevels(result.results)
      }
    }

    // 这里可以集成不同的通知渠道
    // 1. 应用内通知
    // 2. 邮件通知
    // 3. 短信通知
    // 4. Webhook通知
    
    console.log('发送通知:', notification)
  }

  /**
   * 生成通知标题
   */
  private generateNotificationTitle(result: RuleExecutionResult): string {
    if (result.status === 'FAILED') {
      return `规则执行失败 - 规则ID: ${result.ruleId}`
    }
    
    return `检测到高风险异常 - 规则ID: ${result.ruleId}`
  }

  /**
   * 生成通知消息
   */
  private generateNotificationMessage(result: RuleExecutionResult): string {
    if (result.status === 'FAILED') {
      return `规则执行失败，错误信息: ${result.error}`
    }
    
    const riskSummary = this.summarizeRiskLevels(result.results)
    return `检测到 ${result.matchedCount} 个异常，风险分布: ${JSON.stringify(riskSummary)}`
  }

  /**
   * 汇总风险等级
   */
  private summarizeRiskLevels(results: any[]): Record<string, number> {
    const summary: Record<string, number> = {}
    
    for (const result of results) {
      const level = result.riskLevel
      summary[level] = (summary[level] || 0) + 1
    }
    
    return summary
  }
}
