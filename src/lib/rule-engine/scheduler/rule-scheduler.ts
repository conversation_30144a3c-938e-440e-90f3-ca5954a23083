/**
 * 规则调度器
 * 
 * 负责管理规则的定时执行、手动执行和批量执行
 */

import { 
  RuleScheduleConfig, 
  RuleExecutionRequest, 
  IRuleScheduler 
} from '../types/engine-types'
import { RuleEngine } from '../core/rule-engine'
import { generateExecutionId } from '../utils/rule-utils'

export class RuleScheduler implements IRuleScheduler {
  private engine: RuleEngine
  private scheduledJobs = new Map<number, NodeJS.Timeout>()
  private isShuttingDown = false

  constructor(engine: RuleEngine) {
    this.engine = engine
  }

  /**
   * 调度规则
   */
  async schedule(config: RuleScheduleConfig): Promise<void> {
    try {
      // 如果已经调度了，先取消
      if (this.scheduledJobs.has(config.ruleId)) {
        await this.unschedule(config.ruleId)
      }

      if (!config.enabled) {
        return
      }

      // 根据配置类型设置调度
      if (config.cronExpression) {
        await this.scheduleCronJob(config)
      } else if (config.interval) {
        await this.scheduleIntervalJob(config)
      }

      console.log(`规则 ${config.ruleId} 调度成功`)
    } catch (error) {
      console.error(`规则 ${config.ruleId} 调度失败:`, error)
      throw error
    }
  }

  /**
   * 取消调度
   */
  async unschedule(ruleId: number): Promise<void> {
    const job = this.scheduledJobs.get(ruleId)
    if (job) {
      clearTimeout(job)
      this.scheduledJobs.delete(ruleId)
      console.log(`规则 ${ruleId} 调度已取消`)
    }
  }

  /**
   * 立即执行
   */
  async executeNow(request: RuleExecutionRequest): Promise<string> {
    const executionId = generateExecutionId()
    
    try {
      // 异步执行规则
      this.executeRulesAsync(request, executionId)
      
      return executionId
    } catch (error) {
      console.error('立即执行失败:', error)
      throw error
    }
  }

  /**
   * 获取已调度的规则
   */
  async getScheduledRules(): Promise<RuleScheduleConfig[]> {
    // 这里应该从数据库获取调度配置
    // 暂时返回空数组
    return []
  }

  /**
   * 设置Cron调度
   */
  private async scheduleCronJob(config: RuleScheduleConfig): Promise<void> {
    // 简化的Cron实现，实际项目中应该使用专业的Cron库
    const interval = this.parseCronExpression(config.cronExpression!)
    
    const job = setInterval(async () => {
      if (this.isShuttingDown) {
        return
      }
      
      await this.executeScheduledRule(config)
    }, interval)

    this.scheduledJobs.set(config.ruleId, job as any)
  }

  /**
   * 设置间隔调度
   */
  private async scheduleIntervalJob(config: RuleScheduleConfig): Promise<void> {
    const job = setInterval(async () => {
      if (this.isShuttingDown) {
        return
      }
      
      await this.executeScheduledRule(config)
    }, config.interval!)

    this.scheduledJobs.set(config.ruleId, job as any)
  }

  /**
   * 执行调度的规则
   */
  private async executeScheduledRule(config: RuleScheduleConfig): Promise<void> {
    let retryCount = 0
    
    while (retryCount <= config.maxRetries) {
      try {
        const request: RuleExecutionRequest = {
          ruleIds: [config.ruleId],
          parameters: config.parameters,
          async: true
        }
        
        await this.executeRulesAsync(request, generateExecutionId())
        break // 成功执行，退出重试循环
        
      } catch (error) {
        retryCount++
        console.error(`规则 ${config.ruleId} 调度执行失败 (重试 ${retryCount}/${config.maxRetries}):`, error)
        
        if (retryCount <= config.maxRetries) {
          // 等待重试延迟
          await this.sleep(config.retryDelay)
        }
      }
    }
  }

  /**
   * 异步执行规则
   */
  private async executeRulesAsync(request: RuleExecutionRequest, executionId: string): Promise<void> {
    try {
      // 这里需要获取规则信息并执行
      // 暂时只记录日志
      console.log(`开始异步执行规则: ${request.ruleIds.join(', ')}, 执行ID: ${executionId}`)
      
      // TODO: 实际执行规则
      // await this.engine.executeRules(request)
      
    } catch (error) {
      console.error('异步执行规则失败:', error)
      throw error
    }
  }

  /**
   * 解析Cron表达式（简化版）
   */
  private parseCronExpression(cronExpression: string): number {
    // 这是一个简化的Cron解析器
    // 实际项目中应该使用专业的Cron库如node-cron
    
    const parts = cronExpression.split(' ')
    if (parts.length !== 5) {
      throw new Error('无效的Cron表达式格式')
    }

    const [minute, hour, dayOfMonth, month, dayOfWeek] = parts

    // 简单处理一些常见情况
    if (cronExpression === '0 * * * *') {
      return 60 * 60 * 1000 // 每小时
    }
    if (cronExpression === '*/5 * * * *') {
      return 5 * 60 * 1000 // 每5分钟
    }
    if (cronExpression === '0 0 * * *') {
      return 24 * 60 * 60 * 1000 // 每天
    }
    if (cronExpression === '0 0 * * 0') {
      return 7 * 24 * 60 * 60 * 1000 // 每周
    }

    // 默认每小时执行
    return 60 * 60 * 1000
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 关闭调度器
   */
  async shutdown(): Promise<void> {
    this.isShuttingDown = true
    
    // 清理所有调度任务
    for (const [ruleId, job] of this.scheduledJobs) {
      clearTimeout(job)
      console.log(`清理规则 ${ruleId} 的调度任务`)
    }
    
    this.scheduledJobs.clear()
    console.log('规则调度器已关闭')
  }

  /**
   * 获取调度状态
   */
  getScheduleStatus(): { ruleId: number; isScheduled: boolean }[] {
    const status: { ruleId: number; isScheduled: boolean }[] = []
    
    for (const ruleId of this.scheduledJobs.keys()) {
      status.push({ ruleId, isScheduled: true })
    }
    
    return status
  }

  /**
   * 暂停规则调度
   */
  async pauseSchedule(ruleId: number): Promise<void> {
    const job = this.scheduledJobs.get(ruleId)
    if (job) {
      clearTimeout(job)
      this.scheduledJobs.delete(ruleId)
      console.log(`规则 ${ruleId} 调度已暂停`)
    }
  }

  /**
   * 恢复规则调度
   */
  async resumeSchedule(config: RuleScheduleConfig): Promise<void> {
    await this.schedule(config)
  }

  /**
   * 获取下次执行时间
   */
  getNextExecutionTime(ruleId: number): Date | null {
    // 这里应该根据Cron表达式计算下次执行时间
    // 暂时返回null
    return null
  }

  /**
   * 批量调度规则
   */
  async scheduleMultiple(configs: RuleScheduleConfig[]): Promise<void> {
    const results = await Promise.allSettled(
      configs.map(config => this.schedule(config))
    )

    const failures = results
      .map((result, index) => ({ result, config: configs[index] }))
      .filter(({ result }) => result.status === 'rejected')

    if (failures.length > 0) {
      console.warn(`${failures.length} 个规则调度失败:`, failures)
    }

    console.log(`成功调度 ${configs.length - failures.length}/${configs.length} 个规则`)
  }

  /**
   * 批量取消调度
   */
  async unscheduleMultiple(ruleIds: number[]): Promise<void> {
    await Promise.all(ruleIds.map(ruleId => this.unschedule(ruleId)))
    console.log(`成功取消 ${ruleIds.length} 个规则的调度`)
  }
}
