/**
 * 规则引擎工具函数
 */

import { v4 as uuidv4 } from 'uuid'
import { SupervisionRule } from '@/types/supervision-rule'
import { RuleMatchResult, RuleEvidence } from '../types/engine-types'
import { formatDuration, formatFileSize, truncateText } from '@/lib/format-utils'

/**
 * 生成执行ID
 */
export function generateExecutionId(): string {
  return `exec_${Date.now()}_${uuidv4().substring(0, 8)}`
}

/**
 * 生成规则编码
 */
export function generateRuleCode(category: string, type: string): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 6)
  return `${category}_${type}_${timestamp}_${random}`.toUpperCase()
}

/**
 * 验证规则内容
 */
export function validateRuleContent(content: string, type: string): boolean {
  if (!content || content.trim().length === 0) {
    return false
  }

  switch (type) {
    case 'SQL':
      return validateSqlContent(content)
    case 'DSL':
      return validateDslContent(content)
    case 'JAVASCRIPT':
      return validateJavaScriptContent(content)
    default:
      return false
  }
}

/**
 * 验证SQL内容
 */
function validateSqlContent(sql: string): boolean {
  // 基础SQL验证
  const trimmed = sql.trim().toUpperCase()
  
  // 必须以SELECT开头
  if (!trimmed.startsWith('SELECT')) {
    return false
  }
  
  // 不允许危险操作
  const dangerousKeywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
  for (const keyword of dangerousKeywords) {
    if (trimmed.includes(keyword)) {
      return false
    }
  }
  
  return true
}

/**
 * 验证DSL内容
 */
function validateDslContent(dsl: string): boolean {
  try {
    // 简单的DSL格式验证
    const parsed = JSON.parse(dsl)
    return typeof parsed === 'object' && parsed !== null
  } catch {
    return false
  }
}

/**
 * 验证JavaScript内容
 */
function validateJavaScriptContent(js: string): boolean {
  // 检查是否包含危险函数
  const dangerousFunctions = ['eval', 'Function', 'setTimeout', 'setInterval', 'require', 'import']
  for (const func of dangerousFunctions) {
    if (js.includes(func)) {
      return false
    }
  }
  
  return true
}

/**
 * 解析SQL参数
 */
export function parseSqlParameters(sql: string): string[] {
  const paramRegex = /:(\w+)/g
  const params: string[] = []
  let match
  
  while ((match = paramRegex.exec(sql)) !== null) {
    if (match[1] && !params.includes(match[1])) {
      params.push(match[1])
    }
  }
  
  return params
}

/**
 * 替换SQL参数
 */
export function replaceSqlParameters(sql: string, parameters: Record<string, any>): string {
  let result = sql
  
  for (const [key, value] of Object.entries(parameters)) {
    const placeholder = `:${key}`
    const replacement = typeof value === 'string' ? `'${value}'` : String(value)
    result = result.replace(new RegExp(placeholder, 'g'), replacement)
  }
  
  return result
}

/**
 * 计算规则匹配分数
 */
export function calculateMatchScore(evidence: RuleEvidence[]): number {
  if (evidence.length === 0) return 0
  
  let totalScore = 0
  let weightSum = 0
  
  for (const item of evidence) {
    const weight = getEvidenceWeight(item.type)
    const score = getEvidenceScore(item)
    
    totalScore += score * weight
    weightSum += weight
  }
  
  return weightSum > 0 ? Math.round((totalScore / weightSum) * 100) / 100 : 0
}

/**
 * 获取证据权重
 */
function getEvidenceWeight(type: string): number {
  const weights = {
    'SQL_RESULT': 1.0,
    'CALCULATION': 0.8,
    'COMPARISON': 0.6,
    'PATTERN_MATCH': 0.4
  }
  return weights[type as keyof typeof weights] || 0.5
}

/**
 * 获取证据分数
 */
function getEvidenceScore(evidence: RuleEvidence): number {
  switch (evidence.type) {
    case 'SQL_RESULT':
      return evidence.value ? 1.0 : 0.0
    case 'CALCULATION':
      return Math.min(Math.abs(evidence.value), 1.0)
    case 'COMPARISON':
      return evidence.value === evidence.expected ? 1.0 : 0.0
    case 'PATTERN_MATCH':
      return evidence.value ? 0.8 : 0.0
    default:
      return 0.5
  }
}

/**
 * 格式化执行时间 - 使用统一的格式化函数
 */
export function formatExecutionTime(milliseconds: number): string {
  return formatDuration(milliseconds, { format: 'short', maxUnits: 2 })
}

/**
 * 格式化数据大小 - 使用统一的格式化函数
 */
export function formatDataSize(bytes: number): string {
  return formatFileSize(bytes, { decimals: 1, binary: true })
}

/**
 * 生成规则摘要 - 使用统一的文本截断函数
 */
export function generateRuleSummary(rule: SupervisionRule): string {
  const parts = [
    `规则: ${rule.ruleName}`,
    `类型: ${rule.ruleType}`,
    `分类: ${rule.ruleCategory}`,
    `严重程度: ${rule.severityLevel}`
  ]

  if (rule.description) {
    parts.push(`描述: ${truncateText(rule.description, { maxLength: 100, wordBoundary: false })}`)
  }

  return parts.join(' | ')
}

/**
 * 类型守卫：确保 boolean 字段的类型安全
 */
function ensureBoolean(value: any): boolean {
  if (typeof value === 'boolean') {
    return value
  }
  if (typeof value === 'number') {
    return value === 1
  }
  if (typeof value === 'string') {
    return value === '1' || value.toLowerCase() === 'true'
  }
  return false
}

/**
 * 检查规则是否可执行
 */
export function isRuleExecutable(rule: any): boolean {
  const isActive = ensureBoolean(rule.isActive)
  const isDeleted = ensureBoolean(rule.isDeleted)

  return isActive &&
         !isDeleted &&
         rule.ruleContent &&
         rule.ruleContent.trim().length > 0 &&
         (!rule.expiryDate || new Date(rule.expiryDate) > new Date())
}

/**
 * 获取规则优先级描述
 */
export function getPriorityDescription(priority: number): string {
  if (priority >= 9) return '极高'
  if (priority >= 7) return '高'
  if (priority >= 5) return '中'
  if (priority >= 3) return '低'
  return '极低'
}

/**
 * 获取严重程度颜色
 */
export function getSeverityColor(severity: string): string {
  const colors = {
    'LOW': '#10b981',      // green
    'MEDIUM': '#f59e0b',   // yellow
    'HIGH': '#ef4444',     // red
    'CRITICAL': 'hsl(var(--destructive))'  // dark red
  }
  return colors[severity as keyof typeof colors] || 'hsl(var(--muted-foreground))'
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  
  return cloned
}
