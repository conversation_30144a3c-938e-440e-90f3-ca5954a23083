/**
 * 规则引擎性能测试工具
 * 用于验证P0-004性能优化目标
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { RuleEngine } from '../core/rule-engine'
import { ParallelRuleEngine } from '../optimized/parallel-rule-engine'
import { HighPerformanceRuleEngine } from '../optimized/high-performance-rule-engine'
import { getEngineConfig } from '../config/engine-config'

export interface PerformanceTestResult {
  engineType: string
  testName: string
  ruleCount: number
  executionTime: number
  successRate: number
  averageRuleTime: number
  concurrentExecutions: number
  errorCount: number
  throughput: number // 规则/秒
  memoryUsage?: number
  cpuUsage?: number
}

export interface PerformanceComparison {
  baseline: PerformanceTestResult
  optimized: PerformanceTestResult
  improvement: {
    executionTimeImprovement: number // 百分比
    throughputImprovement: number
    successRateImprovement: number
    meetsTargets: boolean
  }
}

export class RuleEnginePerformanceTester {
  private baselineEngine: RuleEngine
  private parallelEngine: ParallelRuleEngine
  private highPerformanceEngine: HighPerformanceRuleEngine

  constructor() {
    const config = getEngineConfig()
    
    this.baselineEngine = new RuleEngine(config)
    this.parallelEngine = new ParallelRuleEngine(config)
    this.highPerformanceEngine = new HighPerformanceRuleEngine({
      ...config,
      maxConcurrentExecutions: 10,
      batchSize: 5,
      enableSmartBatching: true,
      enableResultCaching: true,
      enablePrecompilation: true,
      maxRetryAttempts: 3,
      retryBackoffMs: 1000,
      circuitBreakerThreshold: 5,
      enablePerformanceMetrics: true,
      enableDetailedLogging: false
    })
  }

  /**
   * 运行完整的性能测试套件
   */
  async runFullPerformanceTest(testRules: SupervisionRule[]): Promise<{
    baselineVsParallel: PerformanceComparison
    baselineVsHighPerformance: PerformanceComparison
    parallelVsHighPerformance: PerformanceComparison
    summary: {
      bestEngine: string
      maxThroughput: number
      bestSuccessRate: number
      meetsAllTargets: boolean
    }
  }> {
    console.log('🚀 开始规则引擎性能测试...')
    
    // 测试基线引擎
    const baselineResult = await this.testEngine('baseline', this.baselineEngine, testRules)
    
    // 测试并行引擎
    const parallelResult = await this.testEngine('parallel', this.parallelEngine, testRules)
    
    // 测试高性能引擎
    const highPerformanceResult = await this.testEngine('high-performance', this.highPerformanceEngine, testRules)
    
    // 生成比较报告
    const baselineVsParallel = this.compareResults(baselineResult, parallelResult)
    const baselineVsHighPerformance = this.compareResults(baselineResult, highPerformanceResult)
    const parallelVsHighPerformance = this.compareResults(parallelResult, highPerformanceResult)
    
    // 生成总结
    const summary = this.generateSummary([baselineResult, parallelResult, highPerformanceResult])
    
    console.log('✅ 性能测试完成')
    
    return {
      baselineVsParallel,
      baselineVsHighPerformance,
      parallelVsHighPerformance,
      summary
    }
  }

  /**
   * 测试单个引擎性能
   */
  private async testEngine(
    engineType: string, 
    engine: any, 
    testRules: SupervisionRule[]
  ): Promise<PerformanceTestResult> {
    console.log(`📊 测试 ${engineType} 引擎...`)
    
    const startTime = Date.now()
    const startMemory = process.memoryUsage().heapUsed
    
    let successCount = 0
    let errorCount = 0
    let maxConcurrent = 0
    
    try {
      // 根据引擎类型选择执行方法
      if (engineType === 'high-performance') {
        const result = await engine.executeRulesBatch(testRules)
        successCount = result.successCount
        errorCount = result.failureCount
        maxConcurrent = engine.getPerformanceMetrics().concurrentExecutions
      } else if (engineType === 'parallel') {
        const results = await engine.executeRulesParallel(testRules)
        successCount = results.filter((r: any) => r.status === 'SUCCESS').length
        errorCount = results.filter((r: any) => r.status === 'FAILED').length
        maxConcurrent = 5 // 并行引擎的默认并发数
      } else {
        // 基线引擎：顺序执行
        for (const rule of testRules) {
          try {
            await engine.executeRule(rule)
            successCount++
          } catch (error) {
            errorCount++
          }
        }
        maxConcurrent = 1
      }
    } catch (error) {
      console.error(`${engineType} 引擎测试失败:`, error)
      errorCount = testRules.length
    }
    
    const endTime = Date.now()
    const endMemory = process.memoryUsage().heapUsed
    const executionTime = endTime - startTime
    const memoryUsage = endMemory - startMemory
    
    const result: PerformanceTestResult = {
      engineType,
      testName: `${testRules.length}规则批量执行测试`,
      ruleCount: testRules.length,
      executionTime,
      successRate: (successCount / testRules.length) * 100,
      averageRuleTime: executionTime / testRules.length,
      concurrentExecutions: maxConcurrent,
      errorCount,
      throughput: (testRules.length / executionTime) * 1000, // 规则/秒
      memoryUsage
    }
    
    console.log(`✅ ${engineType} 引擎测试完成:`, {
      执行时间: `${executionTime}ms`,
      成功率: `${result.successRate.toFixed(2)}%`,
      吞吐量: `${result.throughput.toFixed(2)} 规则/秒`
    })
    
    return result
  }

  /**
   * 比较两个测试结果
   */
  private compareResults(
    baseline: PerformanceTestResult, 
    optimized: PerformanceTestResult
  ): PerformanceComparison {
    const executionTimeImprovement = ((baseline.executionTime - optimized.executionTime) / baseline.executionTime) * 100
    const throughputImprovement = ((optimized.throughput - baseline.throughput) / baseline.throughput) * 100
    const successRateImprovement = optimized.successRate - baseline.successRate
    
    // 检查是否满足P0-004目标
    const meetsTargets = 
      executionTimeImprovement >= 70 && // 执行效率提升70%
      optimized.concurrentExecutions >= 5 && // 支持5-10个并行执行
      optimized.concurrentExecutions <= 10 &&
      optimized.successRate >= 99 // 错误率 < 1%
    
    return {
      baseline,
      optimized,
      improvement: {
        executionTimeImprovement,
        throughputImprovement,
        successRateImprovement,
        meetsTargets
      }
    }
  }

  /**
   * 生成测试总结
   */
  private generateSummary(results: PerformanceTestResult[]): {
    bestEngine: string
    maxThroughput: number
    bestSuccessRate: number
    meetsAllTargets: boolean
  } {
    const bestThroughput = Math.max(...results.map(r => r.throughput))
    const bestSuccessRate = Math.max(...results.map(r => r.successRate))
    
    const bestEngine = results.find(r => r.throughput === bestThroughput)?.engineType || 'unknown'
    
    // 检查是否所有引擎都满足基本要求
    const meetsAllTargets = results.every(r => 
      r.successRate >= 99 && 
      r.concurrentExecutions >= 1 &&
      r.errorCount === 0
    )
    
    return {
      bestEngine,
      maxThroughput: bestThroughput,
      bestSuccessRate,
      meetsAllTargets
    }
  }

  /**
   * 生成性能测试报告
   */
  generateReport(testResults: any): string {
    const report = `
# 规则引擎性能测试报告

## 测试概述
- 测试时间: ${new Date().toLocaleString()}
- 测试规则数量: ${testResults.baselineVsHighPerformance.baseline.ruleCount}

## 性能对比结果

### 基线引擎 vs 高性能引擎
- 执行时间改进: ${testResults.baselineVsHighPerformance.improvement.executionTimeImprovement.toFixed(2)}%
- 吞吐量改进: ${testResults.baselineVsHighPerformance.improvement.throughputImprovement.toFixed(2)}%
- 成功率改进: ${testResults.baselineVsHighPerformance.improvement.successRateImprovement.toFixed(2)}%
- 是否达到目标: ${testResults.baselineVsHighPerformance.improvement.meetsTargets ? '✅ 是' : '❌ 否'}

### 基线引擎 vs 并行引擎
- 执行时间改进: ${testResults.baselineVsParallel.improvement.executionTimeImprovement.toFixed(2)}%
- 吞吐量改进: ${testResults.baselineVsParallel.improvement.throughputImprovement.toFixed(2)}%
- 成功率改进: ${testResults.baselineVsParallel.improvement.successRateImprovement.toFixed(2)}%

## 详细指标

### 基线引擎
- 执行时间: ${testResults.baselineVsHighPerformance.baseline.executionTime}ms
- 成功率: ${testResults.baselineVsHighPerformance.baseline.successRate.toFixed(2)}%
- 吞吐量: ${testResults.baselineVsHighPerformance.baseline.throughput.toFixed(2)} 规则/秒
- 并发数: ${testResults.baselineVsHighPerformance.baseline.concurrentExecutions}

### 高性能引擎
- 执行时间: ${testResults.baselineVsHighPerformance.optimized.executionTime}ms
- 成功率: ${testResults.baselineVsHighPerformance.optimized.successRate.toFixed(2)}%
- 吞吐量: ${testResults.baselineVsHighPerformance.optimized.throughput.toFixed(2)} 规则/秒
- 并发数: ${testResults.baselineVsHighPerformance.optimized.concurrentExecutions}

## 总结
- 最佳引擎: ${testResults.summary.bestEngine}
- 最大吞吐量: ${testResults.summary.maxThroughput.toFixed(2)} 规则/秒
- 最佳成功率: ${testResults.summary.bestSuccessRate.toFixed(2)}%
- 是否满足所有目标: ${testResults.summary.meetsAllTargets ? '✅ 是' : '❌ 否'}

## P0-004 目标达成情况
- ✅ 规则执行效率提升70%: ${testResults.baselineVsHighPerformance.improvement.executionTimeImprovement >= 70 ? '已达成' : '未达成'}
- ✅ 支持并行执行5-10个规则: ${testResults.baselineVsHighPerformance.optimized.concurrentExecutions >= 5 && testResults.baselineVsHighPerformance.optimized.concurrentExecutions <= 10 ? '已达成' : '未达成'}
- ✅ 规则执行错误率 < 1%: ${testResults.baselineVsHighPerformance.optimized.successRate >= 99 ? '已达成' : '未达成'}
`
    
    return report
  }

  /**
   * 创建测试规则
   */
  createTestRules(count: number): SupervisionRule[] {
    const testRules: SupervisionRule[] = []
    
    for (let i = 1; i <= count; i++) {
      testRules.push({
        id: i,
        ruleCode: `TEST_RULE_${i}`,
        ruleName: `测试规则 ${i}`,
        description: `性能测试规则 ${i}`,
        ruleCategory: 'COST_CONTROL',
        ruleType: 'SQL',
        ruleSql: `
          SELECT
            ID, CASE_NUMBER, PATIENT_NAME, TOTAL_COST
          FROM MEDICAL_CASE
          WHERE TOTAL_COST > ${1000 + i * 100}
            AND CASE_TYPE = 'INPATIENT'
            AND IS_DELETED = 0
          ORDER BY TOTAL_COST DESC
        `,
        ruleContent: '',
        priorityLevel: 5,
        severityLevel: 'MEDIUM',
        isActive: true,
        effectiveDate: new Date().toISOString().split('T')[0] || '',
        versionNumber: '1.0',
        createdFrom: 'SYSTEM',
        isDeleted: false,
        executionCount: 0,
        successCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 1,
        updatedBy: 1
      })
    }
    
    return testRules
  }
}

// 导出便捷函数
export async function runRuleEnginePerformanceTest(ruleCount: number = 10): Promise<string> {
  const tester = new RuleEnginePerformanceTester()
  const testRules = tester.createTestRules(ruleCount)
  const results = await tester.runFullPerformanceTest(testRules)
  return tester.generateReport(results)
}
