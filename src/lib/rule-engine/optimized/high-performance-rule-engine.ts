/**
 * 高性能规则引擎
 * 实现P0-004性能优化目标：
 * - 规则执行效率提升70%
 * - 支持并行执行5-10个规则
 * - 规则执行错误率 < 1%
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { 
  RuleExecutionContext, 
  RuleExecutionResult, 
  RuleEngineConfig,
  IRuleExecutor,
  IRuleParser,
  IResultProcessor
} from '../types/engine-types'
import { RuleParser } from '../parsers/rule-parser'
import { RuleExecutor } from '../executors/rule-executor'
import { ResultProcessor } from '../processors/result-processor'
import { generateExecutionId } from '../utils/rule-utils'
import { cacheManager } from '@/lib/cache-manager'
import { monitoring } from '@/lib/monitoring'
import { withRetry } from '@/lib/retry-utils'

export interface HighPerformanceConfig extends RuleEngineConfig {
  // 性能优化配置
  maxConcurrentExecutions: number
  batchSize: number
  enableSmartBatching: boolean
  enableResultCaching: boolean
  enablePrecompilation: boolean
  
  // 错误处理配置
  maxRetryAttempts: number
  retryBackoffMs: number
  circuitBreakerThreshold: number
  
  // 监控配置
  enablePerformanceMetrics: boolean
  enableDetailedLogging: boolean
}

export interface BatchExecutionResult {
  totalRules: number
  successCount: number
  failureCount: number
  averageExecutionTime: number
  totalExecutionTime: number
  results: RuleExecutionResult[]
  errors: Array<{ ruleId: number; error: string }>
}

export interface PerformanceMetrics {
  executionCount: number
  averageExecutionTime: number
  successRate: number
  cacheHitRate: number
  concurrentExecutions: number
  lastExecutionTime: Date
}

export class HighPerformanceRuleEngine {
  private config: HighPerformanceConfig
  private parser: IRuleParser
  private executor: IRuleExecutor
  private processor: IResultProcessor
  
  // 性能优化组件
  private executionQueue: Array<{ rule: SupervisionRule; context: RuleExecutionContext; resolve: Function; reject: Function }> = []
  private runningExecutions = new Map<string, Promise<RuleExecutionResult>>()
  private precompiledRules = new Map<number, any>()
  private circuitBreaker = new Map<number, { failures: number; lastFailure: Date; isOpen: boolean }>()
  
  // 性能指标
  private metrics: PerformanceMetrics = {
    executionCount: 0,
    averageExecutionTime: 0,
    successRate: 100,
    cacheHitRate: 0,
    concurrentExecutions: 0,
    lastExecutionTime: new Date()
  }

  constructor(config: HighPerformanceConfig) {
    this.config = {
      ...config,
      maxConcurrentExecutions: config.maxConcurrentExecutions || 10,
      batchSize: config.batchSize || 5,
      enableSmartBatching: config.enableSmartBatching ?? true,
      enableResultCaching: config.enableResultCaching ?? true,
      enablePrecompilation: config.enablePrecompilation ?? true,
      maxRetryAttempts: config.maxRetryAttempts || 3,
      retryBackoffMs: config.retryBackoffMs || 1000,
      circuitBreakerThreshold: config.circuitBreakerThreshold || 5,
      enablePerformanceMetrics: config.enablePerformanceMetrics ?? true,
      enableDetailedLogging: config.enableDetailedLogging ?? false
    }
    
    this.parser = new RuleParser()
    this.executor = new RuleExecutor(this.config)
    this.processor = new ResultProcessor()
    
    // 启动批处理队列处理器
    if (this.config.enableSmartBatching) {
      this.startBatchProcessor()
    }
  }

  /**
   * 高性能批量执行规则
   */
  async executeRulesBatch(
    rules: SupervisionRule[], 
    context?: Partial<RuleExecutionContext>
  ): Promise<BatchExecutionResult> {
    const startTime = Date.now()
    const timer = monitoring.startTimer('high_performance_batch_execution')
    
    try {
      // 预处理规则
      const processedRules = await this.preprocessRules(rules)
      
      // 智能分批
      const batches = this.createOptimalBatches(processedRules)
      
      // 并行执行批次
      const batchResults = await Promise.allSettled(
        batches.map(batch => this.executeBatch(batch, context))
      )
      
      // 聚合结果
      const result = this.aggregateBatchResults(batchResults, startTime)
      
      // 更新性能指标
      this.updateMetrics(result)
      
      timer.end(true)
      return result
      
    } catch (error) {
      timer.end(false)
      throw error
    }
  }

  /**
   * 单个规则高性能执行
   */
  async executeRuleOptimized(
    rule: SupervisionRule, 
    context?: Partial<RuleExecutionContext>
  ): Promise<RuleExecutionResult> {
    const executionId = generateExecutionId()
    const fullContext: RuleExecutionContext = {
      ruleId: rule.id,
      executionId,
      startTime: new Date(),
      timeout: this.config.defaultTimeout,
      ...context
    }

    // 检查熔断器
    if (this.isCircuitBreakerOpen(rule.id)) {
      throw new Error(`规则 ${rule.id} 熔断器已开启，暂停执行`)
    }

    // 检查缓存
    if (this.config.enableResultCaching) {
      const cached = await this.getCachedResult(rule, fullContext)
      if (cached) {
        this.metrics.cacheHitRate = (this.metrics.cacheHitRate * this.metrics.executionCount + 1) / (this.metrics.executionCount + 1)
        return cached
      }
    }

    // 检查并发限制
    if (this.runningExecutions.size >= this.config.maxConcurrentExecutions) {
      // 使用队列机制而不是直接拒绝
      return this.queueExecution(rule, fullContext)
    }

    return this.executeWithOptimizations(rule, fullContext)
  }

  /**
   * 预处理规则（预编译、验证等）
   */
  private async preprocessRules(rules: SupervisionRule[]): Promise<SupervisionRule[]> {
    const processedRules: SupervisionRule[] = []
    
    for (const rule of rules) {
      try {
        // 预编译规则
        if (this.config.enablePrecompilation && !this.precompiledRules.has(rule.id)) {
          await this.precompileRule(rule)
        }
        
        // 验证规则
        const isValid = await this.executor.validate(rule)
        if (isValid) {
          processedRules.push(rule)
        } else {
          console.warn(`规则 ${rule.id} 验证失败，跳过执行`)
        }
      } catch (error) {
        console.error(`预处理规则 ${rule.id} 失败:`, error)
      }
    }
    
    return processedRules
  }

  /**
   * 创建最优批次
   */
  private createOptimalBatches(rules: SupervisionRule[]): SupervisionRule[][] {
    const batches: SupervisionRule[][] = []
    const batchSize = this.config.batchSize
    
    // 按规则复杂度和历史执行时间排序
    const sortedRules = rules.sort((a, b) => {
      const aComplexity = this.calculateRuleComplexity(a)
      const bComplexity = this.calculateRuleComplexity(b)
      return aComplexity - bComplexity
    })
    
    // 智能分批：混合简单和复杂规则
    for (let i = 0; i < sortedRules.length; i += batchSize) {
      const batch = sortedRules.slice(i, i + batchSize)
      batches.push(batch)
    }
    
    return batches
  }

  /**
   * 执行单个批次
   */
  private async executeBatch(
    batch: SupervisionRule[], 
    context?: Partial<RuleExecutionContext>
  ): Promise<RuleExecutionResult[]> {
    const batchPromises = batch.map(rule => 
      this.executeWithOptimizations(rule, {
        ...context,
        ruleId: rule.id,
        executionId: generateExecutionId(),
        startTime: new Date(),
        timeout: this.config.defaultTimeout
      })
    )
    
    const results = await Promise.allSettled(batchPromises)
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        // 记录失败并返回错误结果
        this.recordCircuitBreakerFailure(batch[index]?.id || 0)
        return {
          ruleId: batch[index]?.id || 0,
          executionId: generateExecutionId(),
          status: 'FAILED' as const,
          startTime: new Date(),
          endTime: new Date(),
          duration: 0,
          processedCount: 0,
          matchedCount: 0,
          results: [],
          error: result.reason?.message || '执行失败'
        }
      }
    })
  }

  /**
   * 带优化的规则执行
   */
  private async executeWithOptimizations(
    rule: SupervisionRule, 
    context: RuleExecutionContext
  ): Promise<RuleExecutionResult> {
    const executionPromise = withRetry(
      async () => {
        this.runningExecutions.set(context.executionId, Promise.resolve({} as RuleExecutionResult))
        this.metrics.concurrentExecutions = this.runningExecutions.size
        
        try {
          // 使用预编译的规则（如果可用）
          const compiledRule = this.precompiledRules.get(rule.id) || rule
          
          // 执行规则
          const result = await this.executor.execute(compiledRule, context)
          
          // 处理结果
          await this.processor.process(result)
          
          // 缓存结果
          if (this.config.enableResultCaching) {
            await this.cacheResult(rule, context, result)
          }
          
          return result
        } finally {
          this.runningExecutions.delete(context.executionId)
          this.metrics.concurrentExecutions = this.runningExecutions.size
        }
      },
      {
        maxAttempts: this.config.maxRetryAttempts,
        baseDelay: this.config.retryBackoffMs,
        maxDelay: this.config.retryBackoffMs * 8,
        backoffMultiplier: 2,
        retryableErrors: ['TIMEOUT', 'CONNECTION_ERROR', 'TEMPORARY_FAILURE']
      },
      { operation: 'optimized_rule_execution', ruleId: rule.id.toString() }
    )
    
    return executionPromise
  }

  /**
   * 队列执行机制
   */
  private async queueExecution(
    rule: SupervisionRule, 
    context: RuleExecutionContext
  ): Promise<RuleExecutionResult> {
    return new Promise((resolve, reject) => {
      this.executionQueue.push({ rule, context, resolve, reject })
    })
  }

  /**
   * 启动批处理队列处理器
   */
  private startBatchProcessor(): void {
    setInterval(() => {
      if (this.executionQueue.length > 0 && this.runningExecutions.size < this.config.maxConcurrentExecutions) {
        const availableSlots = this.config.maxConcurrentExecutions - this.runningExecutions.size
        const itemsToProcess = this.executionQueue.splice(0, availableSlots)
        
        itemsToProcess.forEach(async ({ rule, context, resolve, reject }) => {
          try {
            const result = await this.executeWithOptimizations(rule, context)
            resolve(result)
          } catch (error) {
            reject(error)
          }
        })
      }
    }, 100) // 每100ms检查一次队列
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = {
      executionCount: 0,
      averageExecutionTime: 0,
      successRate: 100,
      cacheHitRate: 0,
      concurrentExecutions: this.runningExecutions.size,
      lastExecutionTime: new Date()
    }
  }

  // 私有辅助方法
  private calculateRuleComplexity(rule: SupervisionRule): number {
    // 基于规则内容计算复杂度
    const content = rule.ruleContent || rule.ruleSql || ''
    return content.length + (content.match(/JOIN|UNION|SUBQUERY/gi)?.length || 0) * 100
  }

  private async precompileRule(rule: SupervisionRule): Promise<void> {
    // 预编译规则逻辑
    this.precompiledRules.set(rule.id, rule)
  }

  private isCircuitBreakerOpen(ruleId: number): boolean {
    const breaker = this.circuitBreaker.get(ruleId)
    if (!breaker) return false
    
    if (breaker.isOpen) {
      // 检查是否可以重试
      const timeSinceLastFailure = Date.now() - breaker.lastFailure.getTime()
      if (timeSinceLastFailure > 60000) { // 1分钟后重试
        breaker.isOpen = false
        breaker.failures = 0
      }
    }
    
    return breaker.isOpen
  }

  private recordCircuitBreakerFailure(ruleId: number): void {
    const breaker = this.circuitBreaker.get(ruleId) || { failures: 0, lastFailure: new Date(), isOpen: false }
    breaker.failures++
    breaker.lastFailure = new Date()
    
    if (breaker.failures >= this.config.circuitBreakerThreshold) {
      breaker.isOpen = true
    }
    
    this.circuitBreaker.set(ruleId, breaker)
  }

  private async getCachedResult(rule: SupervisionRule, context: RuleExecutionContext): Promise<RuleExecutionResult | null> {
    const cacheKey = `rule_result_${rule.id}_${JSON.stringify(context.parameters || {})}`
    return await cacheManager.get(cacheKey)
  }

  private async cacheResult(rule: SupervisionRule, context: RuleExecutionContext, result: RuleExecutionResult): Promise<void> {
    const cacheKey = `rule_result_${rule.id}_${JSON.stringify(context.parameters || {})}`
    await cacheManager.set(cacheKey, result, { ttl: 300 }) // 5分钟缓存
  }

  private aggregateBatchResults(batchResults: PromiseSettledResult<RuleExecutionResult[]>[], startTime: number): BatchExecutionResult {
    const allResults: RuleExecutionResult[] = []
    const errors: Array<{ ruleId: number; error: string }> = []
    
    batchResults.forEach(batchResult => {
      if (batchResult.status === 'fulfilled') {
        allResults.push(...batchResult.value)
      } else {
        errors.push({ ruleId: -1, error: batchResult.reason?.message || '批次执行失败' })
      }
    })
    
    const totalExecutionTime = Date.now() - startTime
    const successCount = allResults.filter(r => r.status === 'SUCCESS').length
    
    return {
      totalRules: allResults.length,
      successCount,
      failureCount: allResults.length - successCount,
      averageExecutionTime: allResults.length > 0 ? totalExecutionTime / allResults.length : 0,
      totalExecutionTime,
      results: allResults,
      errors
    }
  }

  private updateMetrics(result: BatchExecutionResult): void {
    this.metrics.executionCount += result.totalRules
    this.metrics.averageExecutionTime = (this.metrics.averageExecutionTime + result.averageExecutionTime) / 2
    this.metrics.successRate = (result.successCount / result.totalRules) * 100
    this.metrics.lastExecutionTime = new Date()
  }
}
