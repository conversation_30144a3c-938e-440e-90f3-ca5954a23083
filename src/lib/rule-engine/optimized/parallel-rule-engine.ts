/**
 * 优化的并行规则引擎
 * 实现高性能的并行执行、智能缓存和批量处理
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { 
  RuleExecutionContext, 
  RuleExecutionResult, 
  RuleExecutionRequest,
  RuleEngineConfig,
  IRuleExecutor,
  IRuleParser,
  IResultProcessor
} from '../types/engine-types'
import { RuleParser } from '../parsers/rule-parser'
import { RuleExecutor } from '../executors/rule-executor'
import { ResultProcessor } from '../processors/result-processor'
import { generateExecutionId } from '../utils/rule-utils'
import { cacheManager, CACHE_CONFIGS } from '@/lib/cache-manager'
import { log } from '@/lib/logger'
import { monitoring } from '@/lib/monitoring'
import { withRetry } from '@/lib/retry-utils'

/**
 * 批次执行配置
 */
interface BatchConfig {
  batchSize: number
  maxConcurrency: number
  retryAttempts: number
  retryDelay: number
  timeout: number
}

/**
 * 规则执行统计
 */
interface ExecutionStats {
  totalRules: number
  successCount: number
  failureCount: number
  averageExecutionTime: number
  totalProcessedRecords: number
  totalMatchedRecords: number
}

/**
 * 优化的并行规则引擎
 */
export class ParallelRuleEngine {
  private parser: IRuleParser
  private executor: IRuleExecutor
  private processor: IResultProcessor
  private config: RuleEngineConfig
  private runningExecutions = new Map<string, Promise<RuleExecutionResult>>()
  private executionStats: ExecutionStats = {
    totalRules: 0,
    successCount: 0,
    failureCount: 0,
    averageExecutionTime: 0,
    totalProcessedRecords: 0,
    totalMatchedRecords: 0
  }

  constructor(config: RuleEngineConfig) {
    this.config = {
      ...config,
      maxConcurrentExecutions: Math.max(config.maxConcurrentExecutions, 10), // 提升并发数
      defaultTimeout: config.defaultTimeout || 60000,
      cacheEnabled: true,
      cacheTtl: config.cacheTtl || 300000 // 5分钟缓存
    }
    
    this.parser = new RuleParser()
    this.executor = new RuleExecutor(this.config)
    this.processor = new ResultProcessor()
  }

  /**
   * 并行执行多个规则
   */
  async executeRulesParallel(
    rules: SupervisionRule[], 
    context?: Partial<RuleExecutionContext>,
    batchConfig?: Partial<BatchConfig>
  ): Promise<RuleExecutionResult[]> {
    const timer = monitoring.startTimer('rule_engine_parallel_execution')
    
    try {
      const config: BatchConfig = {
        batchSize: 5,
        maxConcurrency: this.config.maxConcurrentExecutions,
        retryAttempts: 3,
        retryDelay: 1000,
        timeout: this.config.defaultTimeout,
        ...batchConfig
      }

      log.info('Starting parallel rule execution', {
        ruleCount: rules.length,
        batchSize: config.batchSize,
        maxConcurrency: config.maxConcurrency
      })

      // 将规则分批处理
      const batches = this.createExecutionBatches(rules, config.batchSize)
      const results: RuleExecutionResult[] = []

      // 并行执行批次
      for (const batch of batches) {
        const batchResults = await this.executeBatch(batch, context, config)
        results.push(...batchResults)
      }

      // 更新统计信息
      this.updateExecutionStats(results)

      timer.end(true)
      return results

    } catch (error) {
      timer.end(false, (error as Error).message)
      log.error('Parallel rule execution failed', { error: (error as Error).message })
      throw error
    }
  }

  /**
   * 执行单个批次
   */
  private async executeBatch(
    rules: SupervisionRule[],
    context?: Partial<RuleExecutionContext>,
    config?: BatchConfig
  ): Promise<RuleExecutionResult[]> {
    const semaphore = new Semaphore(config?.maxConcurrency || this.config.maxConcurrentExecutions)
    
    const promises = rules.map(async (rule) => {
      await semaphore.acquire()
      
      try {
        return await this.executeRuleWithCache(rule, context, config)
      } finally {
        semaphore.release()
      }
    })

    const results = await Promise.allSettled(promises)
    
    return results
      .filter(result => result.status === 'fulfilled')
      .map(result => (result as PromiseFulfilledResult<RuleExecutionResult>).value)
      .filter(Boolean)
  }

  /**
   * 带缓存的规则执行
   */
  private async executeRuleWithCache(
    rule: SupervisionRule,
    context?: Partial<RuleExecutionContext>,
    config?: BatchConfig
  ): Promise<RuleExecutionResult> {
    // 生成缓存键
    const cacheKey = this.generateCacheKey(rule, context)
    
    // 尝试从缓存获取
    if (this.config.cacheEnabled) {
      const cached = await cacheManager.get<RuleExecutionResult>(cacheKey)
      if (cached) {
        log.debug('Rule execution result retrieved from cache', { ruleId: rule.id, cacheKey })
        return cached
      }
    }

    // 执行规则
    const result = await this.executeRuleWithRetry(rule, context, config)
    
    // 缓存结果（只缓存成功的结果）
    if (this.config.cacheEnabled && result.status === 'SUCCESS') {
      await cacheManager.set(cacheKey, result, {
        ttl: this.config.cacheTtl,
        strategy: 'LRU'
      })
    }

    return result
  }

  /**
   * 带重试的规则执行
   */
  private async executeRuleWithRetry(
    rule: SupervisionRule,
    context?: Partial<RuleExecutionContext>,
    config?: BatchConfig
  ): Promise<RuleExecutionResult> {
    const executionId = generateExecutionId()
    const fullContext: RuleExecutionContext = {
      ruleId: rule.id,
      executionId,
      startTime: new Date(),
      timeout: config?.timeout || this.config.defaultTimeout,
      ...context
    }

    return await withRetry(
      async () => {
        // 检查并发限制
        if (this.runningExecutions.size >= this.config.maxConcurrentExecutions) {
          throw new Error('已达到最大并发执行数限制')
        }

        // 执行规则
        const executionPromise = this.executor.execute(rule, fullContext)
        this.runningExecutions.set(executionId, executionPromise)

        try {
          const result = await executionPromise
          
          // 处理结果
          await this.processor.process(result)
          
          return result
        } finally {
          this.runningExecutions.delete(executionId)
        }
      },
      {
        maxAttempts: config?.retryAttempts || 3,
        baseDelay: config?.retryDelay || 1000,
        maxDelay: 10000,
        backoffMultiplier: 2,
        retryableErrors: ['TIMEOUT', 'CONNECTION_ERROR', 'TEMPORARY_FAILURE']
      },
      { operation: 'rule_execution', ruleId: rule.id.toString() }
    )
  }

  /**
   * 创建执行批次
   */
  private createExecutionBatches(rules: SupervisionRule[], batchSize: number): SupervisionRule[][] {
    const batches: SupervisionRule[][] = []
    
    for (let i = 0; i < rules.length; i += batchSize) {
      batches.push(rules.slice(i, i + batchSize))
    }
    
    return batches
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(rule: SupervisionRule, context?: Partial<RuleExecutionContext>): string {
    const contextHash = context?.parameters ? 
      this.hashObject(context.parameters) : 'no-params'
    
    return `rule_result:${rule.id}:${rule.updatedAt}:${contextHash}`
  }

  /**
   * 对象哈希
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32bit integer
    }
    return hash.toString(36)
  }

  /**
   * 更新执行统计
   */
  private updateExecutionStats(results: RuleExecutionResult[]): void {
    this.executionStats.totalRules += results.length
    
    for (const result of results) {
      if (result.status === 'SUCCESS') {
        this.executionStats.successCount++
      } else {
        this.executionStats.failureCount++
      }
      
      if (result.duration) {
        const currentAvg = this.executionStats.averageExecutionTime
        const count = this.executionStats.totalRules
        this.executionStats.averageExecutionTime = 
          (currentAvg * (count - 1) + result.duration) / count
      }
      
      this.executionStats.totalProcessedRecords += result.processedCount || 0
      this.executionStats.totalMatchedRecords += result.matchedCount || 0
    }

    // 记录性能指标
    monitoring.recordMetric({
      name: 'rule_engine_batch_execution',
      value: results.length,
      tags: {
        success_rate: (this.executionStats.successCount / this.executionStats.totalRules).toString(),
        avg_duration: this.executionStats.averageExecutionTime.toString()
      }
    })
  }

  /**
   * 获取执行统计
   */
  getExecutionStats(): ExecutionStats {
    return { ...this.executionStats }
  }

  /**
   * 清理缓存
   */
  async clearCache(pattern?: string): Promise<void> {
    const cachePattern = pattern || 'rule_result:*'
    await cacheManager.clear(cachePattern)
    
    log.info('Rule engine cache cleared', { pattern: cachePattern })
  }

  /**
   * 获取运行中的执行数量
   */
  getRunningExecutionCount(): number {
    return this.runningExecutions.size
  }
}

/**
 * 信号量实现，用于控制并发数
 */
class Semaphore {
  private permits: number
  private waitQueue: Array<() => void> = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--
      return Promise.resolve()
    }

    return new Promise<void>((resolve) => {
      this.waitQueue.push(resolve)
    })
  }

  release(): void {
    this.permits++
    
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!
      this.permits--
      resolve()
    }
  }
}
