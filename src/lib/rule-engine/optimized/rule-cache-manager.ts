/**
 * 智能规则缓存管理器
 * 提供规则执行结果的多层缓存和智能失效策略
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { RuleExecutionResult, RuleExecutionContext } from '../types/engine-types'
import { cacheManager, CacheConfig } from '@/lib/cache-manager'
import { log } from '@/lib/logger'
import { monitoring } from '@/lib/monitoring'

/**
 * 缓存策略配置
 */
interface CacheStrategy {
  ttl: number
  maxSize: number
  strategy: 'LRU' | 'LFU' | 'FIFO'
  enableCompression: boolean
  enableEncryption: boolean
}

/**
 * 缓存键生成配置
 */
interface CacheKeyConfig {
  includeRuleVersion: boolean
  includeParameters: boolean
  includeTimestamp: boolean
  customSuffix?: string
}

/**
 * 缓存统计信息
 */
interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  hitRate: number
  averageResponseTime: number
  cacheSize: number
}

/**
 * 智能规则缓存管理器
 */
export class RuleCacheManager {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    hitRate: 0,
    averageResponseTime: 0,
    cacheSize: 0
  }

  private readonly strategies: Record<string, CacheStrategy> = {
    // 快速规则：短期缓存
    FAST: {
      ttl: 60000, // 1分钟
      maxSize: 1000,
      strategy: 'LRU',
      enableCompression: false,
      enableEncryption: false
    },
    
    // 标准规则：中期缓存
    STANDARD: {
      ttl: 300000, // 5分钟
      maxSize: 500,
      strategy: 'LRU',
      enableCompression: true,
      enableEncryption: false
    },
    
    // 复杂规则：长期缓存
    COMPLEX: {
      ttl: 900000, // 15分钟
      maxSize: 200,
      strategy: 'LFU',
      enableCompression: true,
      enableEncryption: true
    },
    
    // 静态规则：超长期缓存
    STATIC: {
      ttl: 3600000, // 1小时
      maxSize: 100,
      strategy: 'LFU',
      enableCompression: true,
      enableEncryption: false
    }
  }

  /**
   * 获取缓存的规则执行结果
   */
  async get(
    rule: SupervisionRule,
    context: RuleExecutionContext,
    keyConfig?: Partial<CacheKeyConfig>
  ): Promise<RuleExecutionResult | null> {
    const timer = monitoring.startTimer('rule_cache_get')
    
    try {
      const cacheKey = this.generateCacheKey(rule, context, keyConfig)
      const result = await cacheManager.get<RuleExecutionResult>(cacheKey)
      
      if (result) {
        this.stats.hits++
        this.updateHitRate()
        
        log.debug('Rule cache hit', {
          ruleId: rule.id,
          cacheKey: this.sanitizeCacheKey(cacheKey)
        })
        
        monitoring.recordMetric({
          name: 'rule_cache_hit',
          value: 1,
          tags: { 
            rule_type: this.determineRuleType(rule),
            rule_category: rule.ruleCategory || 'unknown'
          }
        })
        
        timer.end(true)
        return result
      } else {
        this.stats.misses++
        this.updateHitRate()
        
        timer.end(false, 'cache_miss')
        return null
      }
      
    } catch (error) {
      timer.end(false, (error as Error).message)
      log.error('Rule cache get failed', {
        ruleId: rule.id,
        error: (error as Error).message
      })
      return null
    }
  }

  /**
   * 设置规则执行结果缓存
   */
  async set(
    rule: SupervisionRule,
    context: RuleExecutionContext,
    result: RuleExecutionResult,
    keyConfig?: Partial<CacheKeyConfig>
  ): Promise<void> {
    const timer = monitoring.startTimer('rule_cache_set')
    
    try {
      const cacheKey = this.generateCacheKey(rule, context, keyConfig)
      const strategy = this.selectCacheStrategy(rule, result)
      const cacheConfig = this.buildCacheConfig(strategy)
      
      // 处理结果数据
      const processedResult = this.processResultForCache(result, strategy as unknown as CacheStrategy)
      
      await cacheManager.set(cacheKey, processedResult, cacheConfig)
      
      this.stats.sets++
      
      log.debug('Rule result cached', {
        ruleId: rule.id,
        cacheKey: this.sanitizeCacheKey(cacheKey),
        strategy: strategy,
        ttl: cacheConfig.ttl
      })
      
      monitoring.recordMetric({
        name: 'rule_cache_set',
        value: 1,
        tags: { 
          rule_type: this.determineRuleType(rule),
          strategy: strategy
        }
      })
      
      timer.end(true)
      
    } catch (error) {
      timer.end(false, (error as Error).message)
      log.error('Rule cache set failed', {
        ruleId: rule.id,
        error: (error as Error).message
      })
    }
  }

  /**
   * 删除规则缓存
   */
  async delete(
    rule: SupervisionRule,
    context?: RuleExecutionContext,
    keyConfig?: Partial<CacheKeyConfig>
  ): Promise<void> {
    try {
      if (context) {
        // 删除特定上下文的缓存
        const cacheKey = this.generateCacheKey(rule, context, keyConfig)
        await cacheManager.delete(cacheKey)
      } else {
        // 删除规则的所有缓存
        const pattern = `rule_result:${rule.id}:*`
        await cacheManager.clear(pattern)
      }
      
      this.stats.deletes++
      
      log.debug('Rule cache deleted', { ruleId: rule.id })
      
    } catch (error) {
      log.error('Rule cache delete failed', {
        ruleId: rule.id,
        error: (error as Error).message
      })
    }
  }

  /**
   * 批量删除规则缓存
   */
  async deleteBatch(ruleIds: number[]): Promise<void> {
    try {
      const patterns = ruleIds.map(id => `rule_result:${id}:*`)
      
      for (const pattern of patterns) {
        await cacheManager.clear(pattern)
      }
      
      this.stats.deletes += ruleIds.length
      
      log.info('Batch rule cache deleted', { ruleCount: ruleIds.length })
      
    } catch (error) {
      log.error('Batch rule cache delete failed', {
        ruleIds,
        error: (error as Error).message
      })
    }
  }

  /**
   * 智能缓存预热
   */
  async warmup(rules: SupervisionRule[], contexts: RuleExecutionContext[]): Promise<void> {
    log.info('Starting rule cache warmup', {
      ruleCount: rules.length,
      contextCount: contexts.length
    })

    const warmupPromises = []
    
    for (const rule of rules) {
      for (const context of contexts) {
        // 只预热高频使用的规则
        if (this.shouldWarmupRule(rule)) {
          warmupPromises.push(
            this.get(rule, context).catch(error => {
              log.warn('Cache warmup failed for rule', { ruleId: rule.id, error: (error as Error).message })
            })
          )
        }
      }
    }

    await Promise.allSettled(warmupPromises)
    
    log.info('Rule cache warmup completed', { warmupCount: warmupPromises.length })
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    rule: SupervisionRule,
    context: RuleExecutionContext,
    config: Partial<CacheKeyConfig> = {}
  ): string {
    const keyConfig: CacheKeyConfig = {
      includeRuleVersion: true,
      includeParameters: true,
      includeTimestamp: false,
      ...config
    }

    let key = `rule_result:${rule.id}`
    
    if (keyConfig.includeRuleVersion) {
      key += `:${rule.updatedAt || rule.createdAt}`
    }
    
    if (keyConfig.includeParameters && context.parameters) {
      const paramHash = this.hashObject(context.parameters)
      key += `:${paramHash}`
    }
    
    if (keyConfig.includeTimestamp) {
      const timeSlot = Math.floor(Date.now() / 60000) // 1分钟时间槽
      key += `:${timeSlot}`
    }
    
    if (keyConfig.customSuffix) {
      key += `:${keyConfig.customSuffix}`
    }
    
    return key
  }

  /**
   * 选择缓存策略
   */
  private selectCacheStrategy(rule: SupervisionRule, result: RuleExecutionResult): string {
    // 基于规则类型选择策略
    if (rule.ruleType === 'JAVASCRIPT') {
      return 'FAST' // JavaScript规则变化频繁，短期缓存
    }
    
    // 基于执行时间选择策略
    if (result.duration && result.duration > 10000) {
      return 'COMPLEX' // 执行时间长的规则，长期缓存
    }
    
    // 基于规则分类选择策略
    if (rule.ruleCategory === 'STATISTICAL_ANALYSIS') {
      return 'STATIC' // 统计分析规则，超长期缓存
    }
    
    return 'STANDARD' // 默认策略
  }

  /**
   * 构建缓存配置
   */
  private buildCacheConfig(strategyName: string): CacheConfig {
    const strategy = this.strategies[strategyName] || this.strategies['STANDARD']

    if (!strategy) {
      throw new Error(`Unknown cache strategy: ${strategyName}`)
    }

    return {
      ttl: strategy.ttl,
      maxSize: strategy.maxSize,
      strategy: strategy.strategy
    }
  }

  /**
   * 处理结果数据用于缓存
   */
  private processResultForCache(
    result: RuleExecutionResult,
    strategy: CacheStrategy
  ): RuleExecutionResult {
    let processedResult = { ...result }
    
    // 压缩大型结果
    if (strategy.enableCompression && this.shouldCompress(result)) {
      processedResult = this.compressResult(result)
    }
    
    // 加密敏感数据
    if (strategy.enableEncryption && this.containsSensitiveData(result)) {
      processedResult = this.encryptSensitiveFields(result)
    }
    
    return processedResult
  }

  /**
   * 判断是否应该预热规则
   */
  private shouldWarmupRule(rule: SupervisionRule): boolean {
    // 高优先级规则
    if (rule.priorityLevel && rule.priorityLevel >= 8) {
      return true
    }
    
    // 频繁执行的规则
    if (rule.executionCount && rule.executionCount > 100) {
      return true
    }
    
    // 关键业务规则
    const criticalCategories = ['FRAUD_DETECTION', 'COMPLIANCE_CHECK']
    if (criticalCategories.includes(rule.ruleCategory || '')) {
      return true
    }
    
    return false
  }

  /**
   * 对象哈希
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return hash.toString(36)
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
  }

  /**
   * 清理缓存键用于日志
   */
  private sanitizeCacheKey(key: string): string {
    return key.length > 50 ? key.substring(0, 47) + '...' : key
  }

  /**
   * 确定规则类型
   */
  private determineRuleType(rule: SupervisionRule): string {
    if (rule.ruleSql) return 'SQL'
    if (rule.ruleDsl) return 'DSL'
    return 'JAVASCRIPT'
  }

  /**
   * 判断是否需要压缩
   */
  private shouldCompress(result: RuleExecutionResult): boolean {
    const resultSize = JSON.stringify(result).length
    return resultSize > 10000 // 大于10KB的结果进行压缩
  }

  /**
   * 压缩结果（简化实现）
   */
  private compressResult(result: RuleExecutionResult): RuleExecutionResult {
    // 这里可以实现真正的压缩逻辑
    return result
  }

  /**
   * 判断是否包含敏感数据
   */
  private containsSensitiveData(result: RuleExecutionResult): boolean {
    // 检查结果中是否包含敏感信息
    const sensitiveFields = ['patient_id', 'id_card', 'phone', 'address']
    const resultStr = JSON.stringify(result).toLowerCase()
    return sensitiveFields.some(field => resultStr.includes(field))
  }

  /**
   * 加密敏感字段（简化实现）
   */
  private encryptSensitiveFields(result: RuleExecutionResult): RuleExecutionResult {
    // 这里可以实现真正的加密逻辑
    return result
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      hitRate: 0,
      averageResponseTime: 0,
      cacheSize: 0
    }
  }
}
