import { executeQuery } from '@/lib/database'
import { RuleExecutionContext, RuleExecutionResult } from '../types/rule-types'
import { replaceSqlParameters } from '../utils/rule-utils'

export class SqlExecutor {
  async execute(
    sql: string,
    context: RuleExecutionContext
  ): Promise<RuleExecutionResult> {
    try {
      // 替换SQL参数
      const processedSql = replaceSqlParameters(sql, context.data)

      // 执行SQL查询
      const result = await executeQuery(processedSql)

      return {
        success: true,
        data: result,
        message: 'SQL执行成功',
        executionTime: Date.now() - context.startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        message: 'SQL执行失败',
        executionTime: Date.now() - context.startTime
      }
    }
  }

  async validate(sql: string): Promise<boolean> {
    try {
      // 简单的SQL语法验证
      const trimmed = sql.trim().toUpperCase()
      return trimmed.startsWith('SELECT') && !this.containsDangerousKeywords(trimmed)
    } catch {
      return false
    }
  }

  private containsDangerousKeywords(sql: string): boolean {
    const dangerous = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
    return dangerous.some(keyword => sql.includes(keyword))
  }
}
