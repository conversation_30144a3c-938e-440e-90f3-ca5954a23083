/**
 * 规则执行器
 * 
 * 负责执行不同类型的规则并生成结果
 */

import { SupervisionRule } from '@/types/supervision-rule'
import { 
  RuleExecutionContext, 
  RuleExecutionResult, 
  RuleMatchResult,
  RuleEvidence,
  IRuleExecutor,
  RuleEngineConfig
} from '../types/engine-types'
import { executeQuery } from '@/lib/database'
import { replaceSqlParameters, calculateMatchScore } from '../utils/rule-utils'
import { SqlExecutor } from './sql-executor'
import { DslExecutor } from './dsl-executor'
import { JavaScriptExecutor } from './javascript-executor'

export class RuleExecutor implements IRuleExecutor {
  private sqlExecutor: SqlExecutor
  private dslExecutor: DslExecutor
  private jsExecutor: JavaScriptExecutor
  private config: RuleEngineConfig

  constructor(config: RuleEngineConfig) {
    this.config = config
    this.sqlExecutor = new SqlExecutor()
    this.dslExecutor = new DslExecutor()
    this.jsExecutor = new JavaScriptExecutor()
  }

  /**
   * 执行规则
   */
  async execute(rule: SupervisionRule, context: RuleExecutionContext): Promise<RuleExecutionResult> {
    const startTime = new Date()
    
    try {
      // 确定规则类型
      const ruleType = this.determineRuleType(rule)
      
      // 根据类型执行规则
      let results: RuleMatchResult[] = []
      
      switch (ruleType) {
        case 'SQL':
          results = await this.executeSqlRule(rule, context)
          break
        case 'DSL':
          results = await this.executeDslRule(rule, context)
          break
        case 'JAVASCRIPT':
          results = await this.executeJavaScriptRule(rule, context)
          break
        default:
          throw new Error(`不支持的规则类型: ${ruleType}`)
      }

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      return {
        executionId: context.executionId,
        ruleId: rule.id,
        status: 'SUCCESS',
        startTime,
        endTime,
        duration,
        processedCount: this.calculateProcessedCount(results),
        matchedCount: results.length,
        results,
        metadata: {
          ruleType,
          ruleCategory: rule.ruleCategory,
          severityLevel: rule.severityLevel
        }
      }

    } catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      return {
        executionId: context.executionId,
        ruleId: rule.id,
        status: 'FAILED',
        startTime,
        endTime,
        duration,
        processedCount: 0,
        matchedCount: 0,
        results: [],
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          ruleType: this.determineRuleType(rule),
          ruleCategory: rule.ruleCategory,
          severityLevel: rule.severityLevel
        }
      }
    }
  }

  /**
   * 验证规则
   */
  async validate(rule: SupervisionRule): Promise<boolean> {
    try {
      const ruleType = this.determineRuleType(rule)
      
      switch (ruleType) {
        case 'SQL':
          return await this.sqlExecutor.validate(rule.ruleSql || rule.ruleContent)
        case 'DSL':
          return await this.dslExecutor.validate(rule.ruleDsl || rule.ruleContent)
        case 'JAVASCRIPT':
          return await this.jsExecutor.validate(rule.ruleContent)
        default:
          return false
      }
    } catch {
      return false
    }
  }

  /**
   * 获取执行器能力
   */
  getCapabilities(): string[] {
    return ['SQL', 'DSL', 'JAVASCRIPT']
  }

  /**
   * 确定规则类型
   */
  private determineRuleType(rule: SupervisionRule): string {
    if (rule.ruleSql && rule.ruleSql.trim()) {
      return 'SQL'
    }
    if (rule.ruleDsl && rule.ruleDsl.trim()) {
      return 'DSL'
    }
    
    const content = rule.ruleContent.trim()
    if (content.toUpperCase().startsWith('SELECT')) {
      return 'SQL'
    }
    
    try {
      JSON.parse(content)
      return 'DSL'
    } catch {
      // 不是JSON格式
    }
    
    if (content.includes('function') || content.includes('=>')) {
      return 'JAVASCRIPT'
    }
    
    return 'SQL'
  }

  /**
   * 执行SQL规则
   */
  private async executeSqlRule(rule: SupervisionRule, context: RuleExecutionContext): Promise<RuleMatchResult[]> {
    const sql = rule.ruleSql || rule.ruleContent
    const executionContext = {
      ruleId: rule.id.toString(),
      data: context.parameters || {},
      startTime: Date.now()
    }

    const result = await this.sqlExecutor.execute(sql, executionContext)
    return this.convertExecutionResultToMatchResults(result, rule)
  }

  /**
   * 执行DSL规则
   */
  private async executeDslRule(rule: SupervisionRule, context: RuleExecutionContext): Promise<RuleMatchResult[]> {
    const dsl = rule.ruleDsl || rule.ruleContent
    const executionContext = {
      ruleId: rule.id.toString(),
      data: context.parameters || {},
      startTime: Date.now()
    }

    const result = await this.dslExecutor.execute(dsl, executionContext)
    return this.convertExecutionResultToMatchResults(result, rule)
  }

  /**
   * 执行JavaScript规则
   */
  private async executeJavaScriptRule(rule: SupervisionRule, context: RuleExecutionContext): Promise<RuleMatchResult[]> {
    const js = rule.ruleContent
    const executionContext = {
      ruleId: rule.id.toString(),
      data: context.parameters || {},
      startTime: Date.now()
    }

    const result = await this.jsExecutor.execute(js, executionContext)
    return this.convertExecutionResultToMatchResults(result, rule)
  }

  /**
   * 将执行结果转换为匹配结果
   */
  private convertExecutionResultToMatchResults(result: any, rule: SupervisionRule): RuleMatchResult[] {
    if (!result.success || !result.data) {
      return []
    }

    // 如果数据是数组，转换每个项目
    if (Array.isArray(result.data)) {
      return result.data.map((row: any, index: number) => this.createMatchResult(row, rule, index))
    }

    // 如果数据是单个对象，创建单个匹配结果
    return [this.createMatchResult(result.data, rule, 0)]
  }

  /**
   * 创建匹配结果
   */
  private createMatchResult(data: any, rule: SupervisionRule, index: number): RuleMatchResult {
    const evidence: RuleEvidence[] = Object.entries(data).map(([key, value]) => ({
      type: 'SQL_RESULT',
      description: `字段 ${key} 的值`,
      value
    }))

    return {
      id: `${rule.ruleType}_${rule.id}_${index}`,
      caseId: data.CASE_ID || data.ID || `unknown_${index}`,
      resultType: this.determineResultType(data, rule),
      riskLevel: this.determineRiskLevel(data, rule),
      score: calculateMatchScore(evidence),
      description: this.generateDescription(data, rule),
      details: data,
      evidence
    }
  }

  /**
   * 确定结果类型
   */
  private determineResultType(data: any, rule: SupervisionRule): any {
    if (rule.ruleCategory === 'FRAUD_DETECTION') {
      return 'VIOLATION'
    }
    return 'SUSPICIOUS'
  }

  /**
   * 确定风险等级
   */
  private determineRiskLevel(data: any, rule: SupervisionRule): any {
    return rule.severityLevel
  }

  /**
   * 生成描述
   */
  private generateDescription(data: any, rule: SupervisionRule): string {
    return `规则 "${rule.ruleName}" 检测到异常: ${JSON.stringify(data).substring(0, 100)}...`
  }

  /**
   * 计算处理记录数
   */
  private calculateProcessedCount(results: RuleMatchResult[]): number {
    // 这里可以根据实际业务逻辑计算
    // 暂时返回结果数量
    return results.length
  }
}


