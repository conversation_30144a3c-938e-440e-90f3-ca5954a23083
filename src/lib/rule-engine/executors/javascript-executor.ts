import { RuleExecutionContext, RuleExecutionResult } from '../types/rule-types'

export class JavaScriptExecutor {
  async execute(
    code: string,
    context: RuleExecutionContext
  ): Promise<RuleExecutionResult> {
    try {
      // 创建安全的执行环境
      const result = this.executeJavaScript(code, context.data)

      return {
        success: true,
        data: result,
        message: 'JavaScript执行成功',
        executionTime: Date.now() - context.startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        message: 'JavaScript执行失败',
        executionTime: Date.now() - context.startTime
      }
    }
  }

  async validate(js: string): Promise<boolean> {
    try {
      // 检查是否包含危险函数
      const dangerous = ['eval', 'Function', 'setTimeout', 'setInterval']
      return !dangerous.some(func => js.includes(func))
    } catch {
      return false
    }
  }

  private executeJavaScript(code: string, data: any): any {
    // 创建安全的执行上下文
    const context = {
      data,
      Math,
      Date,
      JSON,
      console: {
        log: (...args: any[]) => console.log('[Rule Engine]', ...args)
      }
    }
    
    try {
      // 使用Function构造函数创建安全的执行环境
      const func = new Function(
        'context',
        `
        const { data, Math, Date, JSON, console } = context;
        ${code}
        `
      )
      
      return func(context)
    } catch (error) {
      throw new Error(`JavaScript执行错误: ${error}`)
    }
  }
}
