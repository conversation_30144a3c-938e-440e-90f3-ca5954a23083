import { RuleExecutionContext, RuleExecutionResult } from '../types/rule-types'

export class DslExecutor {
  async execute(
    dsl: string,
    context: RuleExecutionContext
  ): Promise<RuleExecutionResult> {
    try {
      // 解析DSL表达式
      const result = this.parseDsl(dsl, context.data)

      return {
        success: true,
        data: result,
        message: 'DSL执行成功',
        executionTime: Date.now() - context.startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        message: 'DSL执行失败',
        executionTime: Date.now() - context.startTime
      }
    }
  }

  async validate(dsl: string): Promise<boolean> {
    try {
      JSON.parse(dsl)
      return true
    } catch {
      return false
    }
  }

  private parseDsl(dsl: string, data: any): any {
    // 简单的DSL解析器实现
    // 支持基本的条件判断和数学运算
    
    // 替换变量
    let processedDsl = dsl
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`\\$\\{${key}\\}`, 'g')
      processedDsl = processedDsl.replace(regex, String(value))
    }
    
    // 简单的表达式求值
    try {
      // 注意：在生产环境中应该使用更安全的表达式求值器
      return Function(`"use strict"; return (${processedDsl})`)()
    } catch (error) {
      throw new Error(`DSL解析错误: ${error}`)
    }
  }
}
