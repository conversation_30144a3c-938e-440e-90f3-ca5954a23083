/**
 * 企业级缓存管理系统
 */

import { monitoring, log } from './monitoring'

export interface CacheConfig {
  ttl: number // 生存时间（秒）
  maxSize?: number // 最大缓存大小
  strategy?: 'LRU' | 'LFU' | 'FIFO'
  serialize?: boolean // 是否序列化存储
  namespace?: string // 命名空间
}

export interface CacheEntry<T> {
  value: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

export interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  size: number
  hitRate: number
}

/**
 * 多层缓存管理器
 */
class CacheManager {
  private memoryCache = new Map<string, CacheEntry<any>>()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    size: 0,
    hitRate: 0
  }

  /**
   * 获取缓存值
   */
  async get<T>(key: string, config?: CacheConfig): Promise<T | null> {
    const timer = monitoring.startTimer('cache_get', { key })

    try {
      // 1. 检查内存缓存
      const memoryResult = this.getFromMemory<T>(key)
      if (memoryResult !== null) {
        this.stats.hits++
        timer.end(true)
        this.updateHitRate()
        
        monitoring.recordMetric({
          name: 'cache_hit',
          value: 1,
          tags: { layer: 'memory', key: this.sanitizeKey(key) }
        })

        return memoryResult
      }

      // 2. 检查Redis缓存（生产环境）
      if (process.env.NODE_ENV === 'production') {
        const redisResult = await this.getFromRedis<T>(key)
        if (redisResult !== null) {
          // 回填到内存缓存
          if (config) {
            this.setInMemory(key, redisResult, config)
          }
          
          this.stats.hits++
          timer.end(true)
          this.updateHitRate()
          
          monitoring.recordMetric({
            name: 'cache_hit',
            value: 1,
            tags: { layer: 'redis', key: this.sanitizeKey(key) }
          })

          return redisResult
        }
      }

      // 缓存未命中
      this.stats.misses++
      timer.end(true)
      this.updateHitRate()
      
      monitoring.recordMetric({
        name: 'cache_miss',
        value: 1,
        tags: { key: this.sanitizeKey(key) }
      })

      return null
    } catch (error) {
      timer.end(false, (error as Error).message)
      log({
        level: 'error',
        message: 'Cache get operation failed',
        context: { key, error: (error as Error).message }
      })
      return null
    }
  }

  /**
   * 设置缓存值
   */
  async set<T>(key: string, value: T, config: CacheConfig): Promise<void> {
    const timer = monitoring.startTimer('cache_set', { key })

    try {
      // 1. 设置内存缓存
      this.setInMemory(key, value, config)

      // 2. 设置Redis缓存（生产环境）
      if (process.env.NODE_ENV === 'production') {
        await this.setInRedis(key, value, config)
      }

      this.stats.sets++
      timer.end(true)
      
      monitoring.recordMetric({
        name: 'cache_set',
        value: 1,
        tags: { key: this.sanitizeKey(key) }
      })

    } catch (error) {
      timer.end(false, (error as Error).message)
      log({
        level: 'error',
        message: 'Cache set operation failed',
        context: { key, error: (error as Error).message }
      })
      throw error
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    const timer = monitoring.startTimer('cache_delete', { key })

    try {
      // 1. 删除内存缓存
      this.memoryCache.delete(key)

      // 2. 删除Redis缓存（生产环境）
      if (process.env.NODE_ENV === 'production') {
        await this.deleteFromRedis(key)
      }

      this.stats.deletes++
      this.stats.size = this.memoryCache.size
      timer.end(true)
      
      monitoring.recordMetric({
        name: 'cache_delete',
        value: 1,
        tags: { key: this.sanitizeKey(key) }
      })

    } catch (error) {
      timer.end(false, (error as Error).message)
      log({
        level: 'error',
        message: 'Cache delete operation failed',
        context: { key, error: (error as Error).message }
      })
    }
  }

  /**
   * 缓存穿透保护：获取或设置
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    config: CacheConfig
  ): Promise<T> {
    // 先尝试获取缓存
    const cached = await this.get<T>(key, config)
    if (cached !== null) {
      return cached
    }

    // 防止缓存击穿：使用分布式锁
    const lockKey = `lock:${key}`
    const acquired = await this.acquireLock(lockKey, 30000) // 30秒锁

    if (!acquired) {
      // 等待一段时间后重试
      await this.sleep(100)
      const retryResult = await this.get<T>(key, config)
      if (retryResult !== null) {
        return retryResult
      }
    }

    try {
      // 执行数据获取
      const timer = monitoring.startTimer('cache_fetch', { key })
      const value = await fetcher()
      timer.end(true)

      // 设置缓存
      await this.set(key, value, config)
      
      monitoring.recordMetric({
        name: 'cache_fetch_success',
        value: 1,
        tags: { key: this.sanitizeKey(key) }
      })

      return value
    } catch (error) {
      monitoring.recordMetric({
        name: 'cache_fetch_error',
        value: 1,
        tags: { key: this.sanitizeKey(key) }
      })
      throw error
    } finally {
      if (acquired) {
        await this.releaseLock(lockKey)
      }
    }
  }

  /**
   * 批量操作
   */
  async mget<T>(keys: string[]): Promise<Map<string, T | null>> {
    const results = new Map<string, T | null>()
    
    await Promise.all(
      keys.map(async (key) => {
        const value = await this.get<T>(key)
        results.set(key, value)
      })
    )

    return results
  }

  async mset<T>(entries: Map<string, { value: T; config: CacheConfig }>): Promise<void> {
    await Promise.all(
      Array.from(entries.entries()).map(async ([key, { value, config }]) => {
        await this.set(key, value, config)
      })
    )
  }

  /**
   * 清空缓存
   */
  async clear(pattern?: string): Promise<void> {
    if (pattern) {
      // 清空匹配模式的缓存
      const regex = new RegExp(pattern)
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          await this.delete(key)
        }
      }
    } else {
      // 清空所有缓存
      this.memoryCache.clear()
      this.stats.size = 0
    }

    monitoring.recordMetric({
      name: 'cache_clear',
      value: 1,
      tags: { pattern: pattern || 'all' }
    })
  }

  /**
   * 获取统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats, size: this.memoryCache.size }
  }

  /**
   * 内存缓存操作
   */
  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key)
    if (!entry) return null

    const now = Date.now()
    
    // 检查是否过期
    if (now > entry.timestamp + entry.ttl * 1000) {
      this.memoryCache.delete(key)
      return null
    }

    // 更新访问信息
    entry.accessCount++
    entry.lastAccessed = now

    return entry.value
  }

  private setInMemory<T>(key: string, value: T, config: CacheConfig): void {
    const now = Date.now()
    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      ttl: config.ttl,
      accessCount: 1,
      lastAccessed: now
    }

    this.memoryCache.set(key, entry)
    this.stats.size = this.memoryCache.size

    // 检查是否需要清理
    if (config.maxSize && this.memoryCache.size > config.maxSize) {
      this.evictEntries(config)
    }
  }

  /**
   * Redis缓存操作（生产环境）
   */
  private async getFromRedis<T>(key: string): Promise<T | null> {
    // 这里应该实现Redis连接和操作
    // 为了示例，返回null
    return null
  }

  private async setInRedis<T>(key: string, value: T, config: CacheConfig): Promise<void> {
    // 这里应该实现Redis设置操作
  }

  private async deleteFromRedis(key: string): Promise<void> {
    // 这里应该实现Redis删除操作
  }

  /**
   * 分布式锁
   */
  private async acquireLock(key: string, ttl: number): Promise<boolean> {
    // 简化实现，生产环境应使用Redis分布式锁
    return true
  }

  private async releaseLock(key: string): Promise<void> {
    // 释放锁的实现
  }

  /**
   * 缓存淘汰策略
   */
  private evictEntries(config: CacheConfig): void {
    const strategy = config.strategy || 'LRU'
    const maxSize = config.maxSize || 1000

    if (this.memoryCache.size <= maxSize) return

    const entries = Array.from(this.memoryCache.entries())
    let toEvict: string[] = []

    switch (strategy) {
      case 'LRU': // 最近最少使用
        toEvict = entries
          .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
          .slice(0, this.memoryCache.size - maxSize)
          .map(([key]) => key)
        break

      case 'LFU': // 最少使用频率
        toEvict = entries
          .sort(([, a], [, b]) => a.accessCount - b.accessCount)
          .slice(0, this.memoryCache.size - maxSize)
          .map(([key]) => key)
        break

      case 'FIFO': // 先进先出
        toEvict = entries
          .sort(([, a], [, b]) => a.timestamp - b.timestamp)
          .slice(0, this.memoryCache.size - maxSize)
          .map(([key]) => key)
        break
    }

    toEvict.forEach(key => this.memoryCache.delete(key))
    this.stats.size = this.memoryCache.size
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
  }

  private sanitizeKey(key: string): string {
    // 清理敏感信息用于指标记录
    return key.replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, 'UUID')
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 单例实例
export const cacheManager = new CacheManager()

// 预定义的缓存配置
export const CACHE_CONFIGS = {
  // 短期缓存：用户会话、临时数据
  SHORT: { ttl: 300, maxSize: 1000, strategy: 'LRU' as const }, // 5分钟

  // 中期缓存：API响应、计算结果
  MEDIUM: { ttl: 1800, maxSize: 500, strategy: 'LRU' as const }, // 30分钟

  // 长期缓存：配置数据、静态内容
  LONG: { ttl: 3600, maxSize: 200, strategy: 'LFU' as const }, // 1小时

  // 用户数据缓存
  USER_DATA: { ttl: 900, maxSize: 1000, strategy: 'LRU' as const }, // 15分钟

  // 查询结果缓存
  QUERY_RESULT: { ttl: 600, maxSize: 500, strategy: 'LRU' as const }, // 10分钟
} as const

export default cacheManager
