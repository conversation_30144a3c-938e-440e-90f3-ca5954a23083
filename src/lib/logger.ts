/**
 * 简单的日志记录器
 */

export interface LogLevel {
  ERROR: 'error'
  WARN: 'warn'
  INFO: 'info'
  DEBUG: 'debug'
}

export const LOG_LEVELS: LogLevel = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'

  error(message: string, ...args: any[]) {
    console.error(`[ERROR] ${message}`, ...args)
  }

  warn(message: string, ...args: any[]) {
    console.warn(`[WARN] ${message}`, ...args)
  }

  info(message: string, ...args: any[]) {
    if (this.isDevelopment) {
      console.info(`[INFO] ${message}`, ...args)
    }
  }

  debug(message: string, ...args: any[]) {
    if (this.isDevelopment) {
      console.debug(`[DEBUG] ${message}`, ...args)
    }
  }

  log(data: any) {
    if (typeof data === 'object' && data.level) {
      const { level, message, ...rest } = data
      switch (level) {
        case 'ERROR':
          this.error(message, rest)
          break
        case 'WARN':
          this.warn(message, rest)
          break
        case 'INFO':
          this.info(message, rest)
          break
        case 'DEBUG':
          this.debug(message, rest)
          break
        default:
          console.log(message, rest)
      }
    } else {
      console.log(data)
    }
  }
}

export const log = new Logger()
export default log
