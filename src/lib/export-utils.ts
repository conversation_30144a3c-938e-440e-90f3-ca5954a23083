/**
 * 数据导出工具函数
 */

import { formatCurrency, formatPercentage, formatDate } from './format-utils'

/**
 * 导出为CSV格式
 */
export function exportToCSV(data: any[], filename: string) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  // 获取表头
  const headers = Object.keys(data[0])
  
  // 构建CSV内容
  const csvContent = [
    headers.join(','), // 表头
    ...data.map(row => 
      headers.map(header => {
        const value = row[header]
        // 处理包含逗号或引号的值
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    )
  ].join('\n')

  // 创建下载链接
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

/**
 * 导出为JSON格式
 */
export function exportToJSON(data: any, filename: string) {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

/**
 * 导出图表为图片
 */
export function exportChartAsImage(chartRef: React.RefObject<any>, filename: string) {
  if (!chartRef.current) {
    throw new Error('图表引用不存在')
  }

  // 使用html2canvas库导出图片
  import('html2canvas').then(html2canvas => {
    html2canvas.default(chartRef.current).then(canvas => {
      const link = document.createElement('a')
      link.download = `${filename}.png`
      link.href = canvas.toDataURL()
      link.click()
    })
  }).catch(error => {
    console.error('导出图片失败:', error)
    throw new Error('导出图片失败')
  })
}

/**
 * 格式化数据用于导出
 */
export function formatDataForExport(data: any[], type: 'case' | 'cost' | 'rule') {
  switch (type) {
    case 'case':
      return data.map(item => ({
        '案例编号': item.caseNumber || item.id,
        '患者姓名': item.patientName || item.name,
        '医院名称': item.hospitalName || item.hospital,
        '科室': item.department,
        '案例类型': item.caseType,
        '医疗类别': item.medicalCategory,
        '总费用': item.totalCost,
        '创建时间': item.createdAt
      }))
    
    case 'cost':
      return data.map(item => ({
        '类别': item.category,
        '数量': item.count,
        '百分比': formatPercentage(item.percentage, { showSign: true }),
        '金额': formatCurrency(item.value),
        '排名': item.rank
      }))

    case 'rule':
      return data.map(item => ({
        '规则名称': item.name,
        '规则代码': item.code,
        '执行次数': item.count,
        '成功率': formatPercentage(item.percentage, { showSign: true }),
        '排名': item.rank
      }))
    
    default:
      return data
  }
}

/**
 * 生成报表摘要
 */
export function generateReportSummary(data: any, reportType: string) {
  const timestamp = new Date().toLocaleString('zh-CN')
  
  return {
    报表类型: reportType,
    生成时间: timestamp,
    数据条数: Array.isArray(data) ? data.length : Object.keys(data).length,
    生成用户: '系统管理员', // 可以从认证信息中获取
    系统版本: '2.0.0'
  }
}

/**
 * 导出完整报表（包含摘要和数据）
 */
export function exportCompleteReport(data: any, reportType: string, filename: string) {
  const summary = generateReportSummary(data, reportType)
  const completeData = {
    摘要: summary,
    数据: data
  }
  
  exportToJSON(completeData, filename)
}

/**
 * 批量导出多个数据集
 */
export function exportMultipleDatasets(datasets: { name: string; data: any[] }[], filename: string) {
  const workbook: any = {}
  
  datasets.forEach(dataset => {
    workbook[dataset.name] = dataset.data
  })
  
  exportToJSON(workbook, filename)
}

/**
 * 数据验证
 */
export function validateExportData(data: any): boolean {
  if (!data) return false
  if (Array.isArray(data) && data.length === 0) return false
  if (typeof data === 'object' && Object.keys(data).length === 0) return false
  return true
}

/**
 * 获取导出文件名
 */
export function getExportFilename(prefix: string, timeRange?: string): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
  const timeRangeSuffix = timeRange ? `_${timeRange}` : ''
  return `${prefix}${timeRangeSuffix}_${timestamp}`
}
