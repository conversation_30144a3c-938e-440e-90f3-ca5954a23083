import { executeQuery } from '@/lib/database'
import { executePagedQuery, executeQueryWithCache, optimizedExecuteQuery, CACHE_CONFIG } from '@/lib/database-optimizer'
import { getOptimizedMedicalCases, getOptimizedStatistics, OptimizedQueryParams } from '@/lib/database-optimization'
import {
  MedicalCase,
  MedicalDiagnosis,
  MedicalSurgery,
  MedicalCostDetail,
  MedicalSettlement,
  MedicalCaseGroup,
  CaseListParams,
  CaseCreateRequest,
  CaseUpdateRequest,
  CaseStatistics,
  CasePaginationResponse,
  CaseType,
  MedicalCategory,
  Gender
} from '@/types/medical-case'

// 清理Oracle查询结果，移除循环引用
function cleanOracleResult(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(item => cleanOracleResult(item))
  }

  if (obj instanceof Date) {
    return obj
  }

  if (Buffer.isBuffer(obj)) {
    return obj
  }

  // 创建新对象，只复制基本属性
  const cleanObj: any = {}
  for (const [key, value] of Object.entries(obj)) {
    // 跳过Oracle内部属性和可能的循环引用
    if (typeof key === 'string' && (
        key.startsWith('_') ||
        key.toLowerCase().includes('connection') ||
        key.toLowerCase().includes('pool') ||
        key.toLowerCase().includes('client') ||
        key.toLowerCase().includes('socket') ||
        key.toLowerCase().includes('stream') ||
        key.toLowerCase().includes('cursor') ||
        key.toLowerCase().includes('resultset') ||
        key === 'domain' ||
        key === 'constructor' ||
        key === 'prototype'
      )) {
      continue
    }

    try {
      cleanObj[key] = cleanOracleResult(value)
    } catch (error) {
      // 跳过有问题的属性
      continue
    }
  }

  return cleanObj
}

/**
 * 获取医疗案例列表（分页）- 优化版本
 */
export async function getMedicalCases(params: CaseListParams): Promise<CasePaginationResponse> {
  try {
    // 使用优化的查询方法
    const optimizedParams: OptimizedQueryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      sortBy: params.sortBy || 'CREATED_AT',
      sortOrder: params.sortOrder || 'DESC',
      useCache: true,
      cacheTTL: 300000, // 5分钟缓存
      filters: {
        caseType: params.caseType,
        medicalCategory: params.medicalCategory,
        hospitalCode: params.hospitalCode,
        patientName: params.search,
        patientIdCard: params.search?.length === 18 ? params.search : undefined,
        startDate: params.admissionDateStart,
        endDate: params.admissionDateEnd,
        minCost: params.totalCostMin,
        maxCost: params.totalCostMax
      }
    }

    // 过滤掉undefined值
    Object.keys(optimizedParams.filters!).forEach(key => {
      if (optimizedParams.filters![key] === undefined) {
        delete optimizedParams.filters![key]
      }
    })

    const optimizedResult = await getOptimizedMedicalCases(optimizedParams)

    return {
      items: optimizedResult.data.map(cleanOracleResult),
      total: optimizedResult.total,
      page: optimizedResult.page,
      pageSize: optimizedResult.pageSize,
      totalPages: optimizedResult.totalPages
    }
  } catch (error) {
    console.error('获取医疗案例失败:', error)
    throw new Error('获取医疗案例失败')
  }
}

/**
 * 根据ID获取医疗案例详情（完整版本，包含所有关联数据）
 */
export async function getMedicalCaseById(caseId: number): Promise<MedicalCase | null> {
  try {
    const sql = `
      SELECT
        ID,
        CASE_NUMBER,
        PATIENT_NAME,
        PATIENT_ID_CARD,
        PATIENT_PHONE,
        PATIENT_AGE,
        PATIENT_GENDER,
        CASE_TYPE,
        MEDICAL_CATEGORY,
        HOSPITAL_NAME,
        HOSPITAL_CODE,
        DEPARTMENT,
        DOCTOR_NAME,
        ADMISSION_DATE,
        DISCHARGE_DATE,
        TOTAL_COST,
        IS_DELETED,
        CREATED_AT,
        UPDATED_AT,
        CREATED_BY,
        UPDATED_BY
      FROM MEDICAL_CASE
      WHERE ID = :caseId AND IS_DELETED = 0
    `

    const result = await executeQueryWithCache(
      sql,
      { caseId },
      `medical_case_${caseId}`,
      CACHE_CONFIG.CACHE_TTL.MEDICAL_CASE
    )

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    // 确保数据是纯净的JSON对象，避免循环引用
    const row = result.rows[0]
    const medicalCase: MedicalCase = {
      id: row.ID,
      caseNumber: row.CASE_NUMBER,
      patientName: row.PATIENT_NAME || '未知患者',
      patientIdCard: row.PATIENT_ID_CARD || '未提供',
      patientPhone: row.PATIENT_PHONE || '未提供',
      patientAge: row.PATIENT_AGE || 0,
      patientGender: row.PATIENT_GENDER as Gender || 'UNKNOWN',
      caseType: row.CASE_TYPE as CaseType,
      medicalCategory: row.MEDICAL_CATEGORY as MedicalCategory,
      hospitalName: row.HOSPITAL_NAME || '未指定医院',
      hospitalCode: row.HOSPITAL_CODE || '',
      department: row.DEPARTMENT || '未指定科室',
      doctorName: row.DOCTOR_NAME || '未指定',
      admissionDate: row.ADMISSION_DATE ? new Date(row.ADMISSION_DATE).toISOString().split('T')[0] : undefined,
      dischargeDate: row.DISCHARGE_DATE ? new Date(row.DISCHARGE_DATE).toISOString().split('T')[0] : undefined,
      totalCost: row.TOTAL_COST && !isNaN(row.TOTAL_COST) ? row.TOTAL_COST : 0,
      isDeleted: row.IS_DELETED === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }

    // 获取关联数据
    medicalCase.diagnoses = await getMedicalDiagnoses(caseId)
    medicalCase.surgeries = await getMedicalSurgeries(caseId)
    medicalCase.costDetails = await getMedicalCostDetails(caseId)
    medicalCase.settlements = await getMedicalSettlements(caseId)
    medicalCase.caseGroups = await getMedicalCaseGroups(caseId)

    // 清理数据，移除可能的循环引用
    return cleanOracleResult(medicalCase)
  } catch (error) {
    console.error('❌ 获取医疗案例详情失败:', error instanceof Error ? error.message : String(error))
    throw new Error('获取医疗案例详情失败')
  }
}

/**
 * 获取案例的诊断信息
 */
export async function getMedicalDiagnoses(caseId: number): Promise<MedicalDiagnosis[]> {
  try {
    const sql = `
      SELECT 
        ID,
        CASE_ID,
        DIAGNOSIS_TYPE,
        DIAGNOSIS_CODE,
        DIAGNOSIS_NAME,
        DIAGNOSIS_DESC,
        IS_PRIMARY,
        CREATED_AT,
        UPDATED_AT,
        CREATED_BY,
        UPDATED_BY
      FROM MEDICAL_DIAGNOSIS
      WHERE CASE_ID = :caseId
      ORDER BY IS_PRIMARY DESC, CREATED_AT ASC
    `

    const result = await executeQuery(sql, { caseId })

    // 清理数据并映射到标准格式
    const cleanRows = cleanOracleResult(result.rows || [])
    return cleanRows.map((row: any) => ({
      id: row.ID,
      caseId: row.CASE_ID,
      diagnosisType: row.DIAGNOSIS_TYPE,
      diagnosisCode: row.DIAGNOSIS_CODE,
      diagnosisName: row.DIAGNOSIS_NAME,
      diagnosisDesc: row.DIAGNOSIS_DESC,
      isPrimary: row.IS_PRIMARY === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }))
  } catch (error) {
    console.error('❌ 获取诊断信息失败:', error instanceof Error ? error.message : String(error))
    return []
  }
}

/**
 * 获取案例的手术信息
 */
export async function getMedicalSurgeries(caseId: number): Promise<MedicalSurgery[]> {
  try {
    const sql = `
      SELECT
        ID,
        CASE_ID,
        SURGERY_CODE,
        SURGERY_NAME,
        SURGERY_DATE,
        SURGEON_NAME,
        ANESTHESIA_TYPE,
        SURGERY_LEVEL,
        SURGERY_DURATION,
        SURGERY_NOTES,
        CREATED_AT,
        UPDATED_AT,
        CREATED_BY,
        UPDATED_BY
      FROM MEDICAL_SURGERY
      WHERE CASE_ID = :caseId
      ORDER BY SURGERY_DATE ASC
    `

    const result = await executeQuery(sql, { caseId })

    // 清理数据并映射到标准格式
    const cleanRows = cleanOracleResult(result.rows || [])
    return cleanRows.map((row: any) => ({
      id: row.ID,
      caseId: row.CASE_ID,
      surgeryCode: row.SURGERY_CODE,
      surgeryName: row.SURGERY_NAME,
      surgeryDate: row.SURGERY_DATE ? new Date(row.SURGERY_DATE).toISOString().split('T')[0] : '',
      surgeonName: row.SURGEON_NAME,
      anesthesiaType: row.ANESTHESIA_TYPE,
      surgeryLevel: row.SURGERY_LEVEL,
      surgeryDuration: row.SURGERY_DURATION,
      surgeryNotes: row.SURGERY_NOTES,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }))
  } catch (error) {
    console.error('❌ 获取手术信息失败:', error instanceof Error ? error.message : String(error))
    return []
  }
}

/**
 * 获取案例的费用明细
 */
export async function getMedicalCostDetails(caseId: number): Promise<MedicalCostDetail[]> {
  try {
    const sql = `
      SELECT
        ID,
        CASE_ID,
        ITEM_CODE,
        ITEM_NAME,
        INSURANCE_ITEM_CODE,
        INSURANCE_ITEM_NAME,
        ITEM_TYPE,
        UNIT_PRICE,
        QUANTITY,
        TOTAL_AMOUNT,
        COMPLIANT_AMOUNT,
        CHARGED_AT,
        DEPARTMENT,
        DOCTOR_NAME,
        CREATED_AT,
        UPDATED_AT,
        CREATED_BY,
        UPDATED_BY
      FROM MEDICAL_COST_DETAIL
      WHERE CASE_ID = :caseId
      ORDER BY CHARGED_AT DESC
    `

    const result = await executeQuery(sql, { caseId })

    // 清理数据并映射到标准格式
    const cleanRows = cleanOracleResult(result.rows || [])
    return cleanRows.map((row: any) => ({
      id: row.ID,
      caseId: row.CASE_ID,
      itemCode: row.ITEM_CODE,
      itemName: row.ITEM_NAME,
      insuranceItemCode: row.INSURANCE_ITEM_CODE,
      insuranceItemName: row.INSURANCE_ITEM_NAME,
      itemType: row.ITEM_TYPE,
      unitPrice: row.UNIT_PRICE,
      quantity: row.QUANTITY,
      totalAmount: row.TOTAL_AMOUNT,
      compliantAmount: row.COMPLIANT_AMOUNT,
      chargedAt: row.CHARGED_AT,
      department: row.DEPARTMENT,
      doctorName: row.DOCTOR_NAME,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }))
  } catch (error) {
    console.error('❌ 获取费用明细失败:', error instanceof Error ? error.message : String(error))
    return []
  }
}

/**
 * 获取案例的结算信息
 */
export async function getMedicalSettlements(caseId: number): Promise<MedicalSettlement[]> {
  try {
    const sql = `
      SELECT
        ID,
        CASE_ID,
        SETTLEMENT_NUMBER,
        TOTAL_MEDICAL_COST,
        FULL_SELF_PAY_AMOUNT,
        OVER_LIMIT_SELF_PAY_AMOUNT,
        ADVANCE_SELF_PAY_AMOUNT,
        ELIGIBLE_AMOUNT,
        ACTUAL_DEDUCTIBLE,
        BASIC_MEDICAL_PAY_RATIO,
        TOTAL_FUND_PAYMENT,
        POOLING_FUND_PAYMENT,
        POLICY_RANGE_SELF_PAY,
        OUT_OF_POLICY_RANGE_AMOUNT,
        IS_REFUND,
        IS_VALID,
        MEDIUM_TYPE,
        SETTLEMENT_STAFF_CODE,
        SETTLEMENT_STAFF_NAME,
        SETTLED_AT,
        IS_DELETED,
        CREATED_AT,
        UPDATED_AT,
        CREATED_BY,
        UPDATED_BY
      FROM MEDICAL_SETTLEMENT
      WHERE CASE_ID = :caseId AND IS_DELETED = 0
      ORDER BY SETTLED_AT DESC
    `

    const result = await executeQuery(sql, { caseId })

    // 清理数据并映射到标准格式
    const cleanRows = cleanOracleResult(result.rows || [])
    return cleanRows.map((row: any) => ({
      id: row.ID,
      caseId: row.CASE_ID,
      settlementNumber: row.SETTLEMENT_NUMBER,
      totalMedicalCost: row.TOTAL_MEDICAL_COST,
      fullSelfPayAmount: row.FULL_SELF_PAY_AMOUNT,
      overLimitSelfPayAmount: row.OVER_LIMIT_SELF_PAY_AMOUNT,
      advanceSelfPayAmount: row.ADVANCE_SELF_PAY_AMOUNT,
      eligibleAmount: row.ELIGIBLE_AMOUNT,
      actualDeductible: row.ACTUAL_DEDUCTIBLE,
      basicMedicalPayRatio: row.BASIC_MEDICAL_PAY_RATIO,
      totalFundPayment: row.TOTAL_FUND_PAYMENT,
      poolingFundPayment: row.POOLING_FUND_PAYMENT,
      policyRangeSelfPay: row.POLICY_RANGE_SELF_PAY,
      outOfPolicyRangeAmount: row.OUT_OF_POLICY_RANGE_AMOUNT,
      isRefund: row.IS_REFUND === 1,
      isValid: row.IS_VALID === 1,
      mediumType: row.MEDIUM_TYPE,
      settlementStaffCode: row.SETTLEMENT_STAFF_CODE,
      settlementStaffName: row.SETTLEMENT_STAFF_NAME,
      settledAt: row.SETTLED_AT,
      isDeleted: row.IS_DELETED === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }))
  } catch (error) {
    console.error('❌ 获取结算信息失败:', error instanceof Error ? error.message : String(error))
    return []
  }
}

/**
 * 获取案例的分组信息
 */
export async function getMedicalCaseGroups(caseId: number): Promise<MedicalCaseGroup[]> {
  try {
    const sql = `
      SELECT
        ID, CASE_ID, GROUP_CODE, GROUP_NAME, GROUP_WEIGHT, GROUP_RATE,
        MONTHLY_PAYMENT_STANDARD, SETTLEMENT_PAYMENT_STANDARD, GROUP_TYPE,
        IS_VALID, GROUPED_AT, IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
      FROM MEDICAL_CASE_GROUP
      WHERE CASE_ID = :caseId AND IS_DELETED = 0
      ORDER BY GROUPED_AT DESC
    `

    const result = await executeQuery(sql, { caseId })

    // 清理数据并映射到标准格式
    const cleanRows = cleanOracleResult(result.rows || [])
    return cleanRows.map((row: any) => ({
      id: row.ID,
      caseId: row.CASE_ID,
      groupCode: row.GROUP_CODE,
      groupName: row.GROUP_NAME,
      groupWeight: row.GROUP_WEIGHT,
      groupRate: row.GROUP_RATE,
      monthlyPaymentStandard: row.MONTHLY_PAYMENT_STANDARD,
      settlementPaymentStandard: row.SETTLEMENT_PAYMENT_STANDARD,
      groupType: row.GROUP_TYPE,
      isValid: row.IS_VALID === 1,
      groupedAt: row.GROUPED_AT,
      isDeleted: row.IS_DELETED === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }))
  } catch (error) {
    console.error('❌ 获取分组信息失败:', error instanceof Error ? error.message : String(error))
    return []
  }
}

/**
 * 创建医疗案例
 */
export async function createMedicalCase(caseData: CaseCreateRequest, createdBy: number): Promise<number> {
  try {
    // 先获取下一个ID
    const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM MEDICAL_CASE`
    const nextIdResult = await executeQuery(getNextIdSql)
    const caseId = nextIdResult.rows?.[0]?.NEXT_ID || 1

    const sql = `
      INSERT INTO MEDICAL_CASE (
        ID, CASE_NUMBER, PATIENT_NAME, PATIENT_ID_CARD, PATIENT_PHONE, PATIENT_AGE,
        PATIENT_GENDER, CASE_TYPE, MEDICAL_CATEGORY, HOSPITAL_NAME, HOSPITAL_CODE,
        DEPARTMENT, DOCTOR_NAME, ADMISSION_DATE, DISCHARGE_DATE, TOTAL_COST,
        IS_DELETED, CREATED_BY, UPDATED_BY
      ) VALUES (
        :caseId, :caseNumber, :patientName, :patientIdCard, :patientPhone, :patientAge,
        :patientGender, :caseType, :medicalCategory, :hospitalName, :hospitalCode,
        :department, :doctorName,
        CASE WHEN :admissionDate IS NOT NULL THEN TO_DATE(:admissionDate, 'YYYY-MM-DD') ELSE NULL END,
        CASE WHEN :dischargeDate IS NOT NULL THEN TO_DATE(:dischargeDate, 'YYYY-MM-DD') ELSE NULL END,
        :totalCost, 0, :createdBy, :createdBy
      )
    `

    await executeQuery(sql, {
      caseId,
      caseNumber: caseData.caseNumber,
      patientName: caseData.patientName,
      patientIdCard: caseData.patientIdCard,
      patientPhone: caseData.patientPhone,
      patientAge: caseData.patientAge,
      patientGender: caseData.patientGender,
      caseType: caseData.caseType,
      medicalCategory: caseData.medicalCategory,
      hospitalName: caseData.hospitalName,
      hospitalCode: caseData.hospitalCode,
      department: caseData.department,
      doctorName: caseData.doctorName,
      admissionDate: caseData.admissionDate,
      dischargeDate: caseData.dischargeDate,
      totalCost: caseData.totalCost,
      createdBy,
    })

    return caseId
  } catch (error) {
    console.error('❌ 创建医疗案例失败:', error instanceof Error ? error.message : String(error))
    throw new Error('创建医疗案例失败')
  }
}

/**
 * 更新医疗案例
 */
export async function updateMedicalCase(caseId: number, updateData: CaseUpdateRequest, updatedBy: number): Promise<void> {
  try {
    const updateFields: string[] = []
    const params: any = { caseId, updatedBy }

    if (updateData.patientName !== undefined) {
      updateFields.push('PATIENT_NAME = :patientName')
      params.patientName = updateData.patientName
    }

    if (updateData.patientPhone !== undefined) {
      updateFields.push('PATIENT_PHONE = :patientPhone')
      params.patientPhone = updateData.patientPhone
    }

    if (updateData.patientAge !== undefined) {
      updateFields.push('PATIENT_AGE = :patientAge')
      params.patientAge = updateData.patientAge
    }

    if (updateData.patientGender !== undefined) {
      updateFields.push('PATIENT_GENDER = :patientGender')
      params.patientGender = updateData.patientGender
    }

    if (updateData.medicalCategory !== undefined) {
      updateFields.push('MEDICAL_CATEGORY = :medicalCategory')
      params.medicalCategory = updateData.medicalCategory
    }

    if (updateData.hospitalName !== undefined) {
      updateFields.push('HOSPITAL_NAME = :hospitalName')
      params.hospitalName = updateData.hospitalName
    }

    if (updateData.hospitalCode !== undefined) {
      updateFields.push('HOSPITAL_CODE = :hospitalCode')
      params.hospitalCode = updateData.hospitalCode
    }

    if (updateData.department !== undefined) {
      updateFields.push('DEPARTMENT = :department')
      params.department = updateData.department
    }

    if (updateData.doctorName !== undefined) {
      updateFields.push('DOCTOR_NAME = :doctorName')
      params.doctorName = updateData.doctorName
    }

    if (updateData.admissionDate !== undefined) {
      updateFields.push('ADMISSION_DATE = TO_DATE(:admissionDate, \'YYYY-MM-DD\')')
      params.admissionDate = updateData.admissionDate
    }

    if (updateData.dischargeDate !== undefined) {
      updateFields.push('DISCHARGE_DATE = TO_DATE(:dischargeDate, \'YYYY-MM-DD\')')
      params.dischargeDate = updateData.dischargeDate
    }

    if (updateData.totalCost !== undefined) {
      updateFields.push('TOTAL_COST = :totalCost')
      params.totalCost = updateData.totalCost
    }

    if (updateFields.length === 0) {
      throw new Error('没有提供要更新的字段')
    }

    updateFields.push('UPDATED_BY = :updatedBy')
    updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP')

    const sql = `
      UPDATE MEDICAL_CASE
      SET ${updateFields.join(', ')}
      WHERE ID = :caseId AND IS_DELETED = 0
    `

    await executeQuery(sql, params)
  } catch (error) {
    console.error('❌ 更新医疗案例失败:', error instanceof Error ? error.message : String(error))
    throw new Error('更新医疗案例失败')
  }
}

/**
 * 删除医疗案例（软删除）
 */
export async function deleteMedicalCase(caseId: number, deletedBy: number): Promise<void> {
  try {
    const sql = `
      UPDATE MEDICAL_CASE
      SET IS_DELETED = 1, UPDATED_BY = :deletedBy, UPDATED_AT = CURRENT_TIMESTAMP
      WHERE ID = :caseId AND IS_DELETED = 0
    `

    await executeQuery(sql, { caseId, deletedBy })
  } catch (error) {
    console.error('❌ 删除医疗案例失败:', error)
    throw new Error('删除医疗案例失败')
  }
}

/**
 * 获取医疗案例统计信息 - 优化版本
 */
export async function getMedicalCaseStatistics(): Promise<CaseStatistics> {
  try {
    // 使用优化的统计查询
    const basicStats = await getOptimizedStatistics('medical_cases_overview')

    // 按类型统计
    const typeStatsSql = `
      SELECT CASE_TYPE, COUNT(*) as COUNT
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
      GROUP BY CASE_TYPE
    `

    const typeStatsResult = await executeQuery(typeStatsSql)
    const casesByType: Record<CaseType, number> = {
      'INPATIENT': 0,
      'OUTPATIENT': 0
    }

    typeStatsResult.rows?.forEach((row: any) => {
      casesByType[row.CASE_TYPE as CaseType] = row.COUNT
    })

    // 按医疗类别统计
    const categoryStatsSql = `
      SELECT MEDICAL_CATEGORY, COUNT(*) as COUNT
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
      GROUP BY MEDICAL_CATEGORY
    `

    const categoryStatsResult = await executeQuery(categoryStatsSql)
    const casesByCategory: Record<MedicalCategory, number> = {} as any

    categoryStatsResult.rows?.forEach((row: any) => {
      casesByCategory[row.MEDICAL_CATEGORY as MedicalCategory] = row.COUNT
    })

    // 按性别统计
    const genderStatsSql = `
      SELECT PATIENT_GENDER, COUNT(*) as COUNT
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0 AND PATIENT_GENDER IS NOT NULL
      GROUP BY PATIENT_GENDER
    `

    const genderStatsResult = await executeQuery(genderStatsSql)
    const casesByGender: Record<Gender, number> = {
      'MALE': 0,
      'FEMALE': 0,
      'OTHER': 0
    }

    genderStatsResult.rows?.forEach((row: any) => {
      casesByGender[row.PATIENT_GENDER as Gender] = row.COUNT
    })

    // 月度趋势
    const monthlyTrendSql = `
      SELECT
        TO_CHAR(CREATED_AT, 'YYYY-MM') as MONTH,
        COUNT(*) as CASE_COUNT,
        SUM(TOTAL_COST) as TOTAL_COST,
        AVG(TOTAL_COST) as AVG_COST
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
        AND CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -12)
      GROUP BY TO_CHAR(CREATED_AT, 'YYYY-MM')
      ORDER BY MONTH
    `

    const monthlyTrendResult = await executeQuery(monthlyTrendSql)
    const monthlyTrend = (monthlyTrendResult.rows || []).map((row: any) => ({
      month: row.MONTH,
      caseCount: row.CASE_COUNT,
      totalCost: row.TOTAL_COST,
      avgCost: row.AVG_COST,
    }))

    // 医院统计
    const hospitalStatsSql = `
      SELECT
        HOSPITAL_NAME,
        HOSPITAL_CODE,
        COUNT(*) as CASE_COUNT,
        SUM(TOTAL_COST) as TOTAL_COST
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
      GROUP BY HOSPITAL_NAME, HOSPITAL_CODE
      ORDER BY CASE_COUNT DESC
      FETCH FIRST 10 ROWS ONLY
    `

    const hospitalStatsResult = await executeQuery(hospitalStatsSql)
    const hospitalStats = (hospitalStatsResult.rows || []).map((row: any) => ({
      hospitalName: row.HOSPITAL_NAME,
      hospitalCode: row.HOSPITAL_CODE,
      caseCount: row.CASE_COUNT,
      totalCost: row.TOTAL_COST,
    }))

    return {
      totalCases: basicStats.TOTAL_CASES || 0,
      inpatientCases: basicStats.INPATIENT_CASES || 0,
      outpatientCases: basicStats.OUTPATIENT_CASES || 0,
      totalCost: basicStats.TOTAL_COST || 0,
      avgCostPerCase: basicStats.AVG_COST || 0,
      casesByType,
      casesByCategory,
      casesByGender,
      monthlyTrend,
      hospitalStats,
    }
  } catch (error) {
    console.error('❌ 获取医疗案例统计失败:', error)
    throw new Error('获取医疗案例统计失败')
  }
}

/**
 * 批量删除医疗案例（软删除）
 */
export async function batchDeleteMedicalCases(ids: string[], userId: string): Promise<{ success: boolean; data?: { deletedCount: number }; error?: string }> {
  try {
    // 将字符串 ID 转换为数字
    const numericIds = ids.map(id => parseInt(id, 10)).filter(id => !isNaN(id))

    if (numericIds.length === 0) {
      return { success: false, error: '无效的案例ID列表' }
    }

    // 构建 IN 子句的占位符
    const placeholders = numericIds.map((_, index) => `:id${index}`).join(', ')
    const queryParams: any = { userId: parseInt(userId, 10) }

    // 添加 ID 参数
    numericIds.forEach((id, index) => {
      queryParams[`id${index}`] = id
    })

    const deleteQuery = `
      UPDATE MEDICAL_CASE_MEDICAL_CASE
      SET IS_DELETED = 1,
          UPDATED_BY = :userId,
          UPDATED_AT = CURRENT_TIMESTAMP
      WHERE ID IN (${placeholders}) AND IS_DELETED = 0
    `

    const result = await executeQuery(deleteQuery, queryParams)

    return {
      success: true,
      data: { deletedCount: result.rowsAffected || 0 }
    }
  } catch (error) {
    console.error('❌ 批量删除案例失败:', error)
    return { success: false, error: '批量删除案例失败' }
  }
}

/**
 * 批量归档医疗案例
 */
export async function batchArchiveMedicalCases(ids: string[], userId: string): Promise<{ success: boolean; data?: { archivedCount: number }; error?: string }> {
  try {
    // 将字符串 ID 转换为数字
    const numericIds = ids.map(id => parseInt(id, 10)).filter(id => !isNaN(id))

    if (numericIds.length === 0) {
      return { success: false, error: '无效的案例ID列表' }
    }

    // 构建 IN 子句的占位符
    const placeholders = numericIds.map((_, index) => `:id${index}`).join(', ')
    const queryParams: any = { userId: parseInt(userId, 10) }

    // 添加 ID 参数
    numericIds.forEach((id, index) => {
      queryParams[`id${index}`] = id
    })

    const archiveQuery = `
      UPDATE MEDICAL_CASE_MEDICAL_CASE
      SET STATUS = 'ARCHIVED',
          UPDATED_BY = :userId,
          UPDATED_AT = CURRENT_TIMESTAMP
      WHERE ID IN (${placeholders}) AND IS_DELETED = 0 AND STATUS != 'ARCHIVED'
    `

    const result = await executeQuery(archiveQuery, queryParams)

    return {
      success: true,
      data: { archivedCount: result.rowsAffected || 0 }
    }
  } catch (error) {
    console.error('❌ 批量归档案例失败:', error)
    return { success: false, error: '批量归档案例失败' }
  }
}

/**
 * 导出医疗案例
 */
export async function exportMedicalCases(
  ids: string[],
  format: 'excel' | 'csv' | 'pdf'
): Promise<{ success: boolean; data?: { buffer: Buffer; contentType: string; filename: string }; error?: string }> {
  try {
    // 将字符串 ID 转换为数字
    const numericIds = ids.map(id => parseInt(id, 10)).filter(id => !isNaN(id))

    if (numericIds.length === 0) {
      return { success: false, error: '无效的案例ID列表' }
    }

    // 获取案例数据
    const placeholders = numericIds.map((_, index) => `:id${index}`).join(', ')
    const queryParams: any = {}

    // 添加 ID 参数
    numericIds.forEach((id, index) => {
      queryParams[`id${index}`] = id
    })

    const exportQuery = `
      SELECT
        ID,
        CASE_NUMBER,
        PATIENT_NAME,
        PATIENT_ID_CARD,
        PATIENT_PHONE,
        PATIENT_AGE,
        PATIENT_GENDER,
        CASE_TYPE,
        MEDICAL_CATEGORY,
        HOSPITAL_NAME,
        HOSPITAL_CODE,
        DEPARTMENT,
        DOCTOR_NAME,
        ADMISSION_DATE,
        DISCHARGE_DATE,
        TOTAL_COST,
        DESCRIPTION,
        STATUS,
        CREATED_AT
      FROM MEDICAL_CASE
      WHERE ID IN (${placeholders}) AND IS_DELETED = 0
      ORDER BY CREATED_AT DESC
    `

    const result = await executeQuery(exportQuery, queryParams)
    const cases = result.rows || []

    if (cases.length === 0) {
      return { success: false, error: '没有找到要导出的案例' }
    }

    // 根据格式生成文件
    const timestamp = new Date().toISOString().split('T')[0]

    switch (format) {
      case 'csv':
        return generateCSV(cases, timestamp || new Date().toISOString())
      case 'excel':
        return generateExcel(cases, timestamp || new Date().toISOString())
      case 'pdf':
        return generatePDF(cases, timestamp || new Date().toISOString())
      default:
        return { success: false, error: '不支持的导出格式' }
    }
  } catch (error) {
    console.error('❌ 导出案例失败:', error)
    return { success: false, error: '导出案例失败' }
  }
}

// CSV 导出
function generateCSV(cases: any[], timestamp: string): { success: boolean; data?: { buffer: Buffer; contentType: string; filename: string }; error?: string } {
  try {
    const headers = [
      '案例编号', '患者姓名', '身份证号', '联系电话', '年龄', '性别',
      '案例类型', '医疗类别', '医院名称', '医院编码', '科室', '主治医生',
      '入院日期', '出院日期', '总费用', '描述', '状态', '创建时间'
    ]

    const csvContent = [
      headers.join(','),
      ...cases.map(c => [
        c.CASE_NUMBER || '',
        c.PATIENT_NAME || '',
        c.PATIENT_ID_CARD || '',
        c.PATIENT_PHONE || '',
        c.PATIENT_AGE || '',
        c.PATIENT_GENDER === 'MALE' ? '男' : c.PATIENT_GENDER === 'FEMALE' ? '女' : '其他',
        c.CASE_TYPE === 'INPATIENT' ? '住院' : '门诊',
        c.MEDICAL_CATEGORY || '',
        c.HOSPITAL_NAME || '',
        c.HOSPITAL_CODE || '',
        c.DEPARTMENT || '',
        c.DOCTOR_NAME || '',
        c.ADMISSION_DATE ? new Date(c.ADMISSION_DATE).toLocaleDateString() : '',
        c.DISCHARGE_DATE ? new Date(c.DISCHARGE_DATE).toLocaleDateString() : '',
        c.TOTAL_COST || '',
        c.DESCRIPTION || '',
        c.STATUS || '',
        c.CREATED_AT ? new Date(c.CREATED_AT).toLocaleString() : ''
      ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
    ].join('\n')

    const buffer = Buffer.from('\uFEFF' + csvContent, 'utf8') // 添加 BOM 以支持中文

    return {
      success: true,
      data: {
        buffer,
        contentType: 'text/csv; charset=utf-8',
        filename: `medical-cases-${timestamp}.csv`
      }
    }
  } catch (error) {
    console.error('❌ 生成CSV失败:', error)
    return { success: false, error: '生成CSV文件失败' }
  }
}

// Excel 导出（简化版，实际项目中可以使用 xlsx 库）
function generateExcel(cases: any[], timestamp: string): { success: boolean; data?: { buffer: Buffer; contentType: string; filename: string }; error?: string } {
  try {
    // 这里使用简化的 Excel 格式（实际上是 CSV 格式，但浏览器会用 Excel 打开）
    const headers = [
      '案例编号', '患者姓名', '身份证号', '联系电话', '年龄', '性别',
      '案例类型', '医疗类别', '医院名称', '医院编码', '科室', '主治医生',
      '入院日期', '出院日期', '总费用', '描述', '状态', '创建时间'
    ]

    const csvContent = [
      headers.join('\t'),
      ...cases.map(c => [
        c.CASE_NUMBER || '',
        c.PATIENT_NAME || '',
        c.PATIENT_ID_CARD || '',
        c.PATIENT_PHONE || '',
        c.PATIENT_AGE || '',
        c.PATIENT_GENDER === 'MALE' ? '男' : c.PATIENT_GENDER === 'FEMALE' ? '女' : '其他',
        c.CASE_TYPE === 'INPATIENT' ? '住院' : '门诊',
        c.MEDICAL_CATEGORY || '',
        c.HOSPITAL_NAME || '',
        c.HOSPITAL_CODE || '',
        c.DEPARTMENT || '',
        c.DOCTOR_NAME || '',
        c.ADMISSION_DATE ? new Date(c.ADMISSION_DATE).toLocaleDateString() : '',
        c.DISCHARGE_DATE ? new Date(c.DISCHARGE_DATE).toLocaleDateString() : '',
        c.TOTAL_COST || '',
        c.DESCRIPTION || '',
        c.STATUS || '',
        c.CREATED_AT ? new Date(c.CREATED_AT).toLocaleString() : ''
      ].join('\t'))
    ].join('\n')

    const buffer = Buffer.from('\uFEFF' + csvContent, 'utf8')

    return {
      success: true,
      data: {
        buffer,
        contentType: 'application/vnd.ms-excel',
        filename: `medical-cases-${timestamp}.xls`
      }
    }
  } catch (error) {
    console.error('❌ 生成Excel失败:', error)
    return { success: false, error: '生成Excel文件失败' }
  }
}

// PDF 导出（简化版）
function generatePDF(cases: any[], timestamp: string): { success: boolean; data?: { buffer: Buffer; contentType: string; filename: string }; error?: string } {
  try {
    // 简化的 PDF 内容（实际项目中可以使用 pdfkit 或其他 PDF 库）
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>医疗案例导出</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #333; text-align: center; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; font-weight: bold; }
          tr:nth-child(even) { background-color: #f9f9f9; }
        </style>
      </head>
      <body>
        <h1>医疗案例导出报告</h1>
        <p>导出时间: ${new Date().toLocaleString()}</p>
        <p>案例数量: ${cases.length}</p>
        <table>
          <thead>
            <tr>
              <th>案例编号</th>
              <th>患者姓名</th>
              <th>性别</th>
              <th>年龄</th>
              <th>案例类型</th>
              <th>医院名称</th>
              <th>总费用</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            ${cases.map(c => `
              <tr>
                <td>${c.CASE_NUMBER || ''}</td>
                <td>${c.PATIENT_NAME || ''}</td>
                <td>${c.PATIENT_GENDER === 'MALE' ? '男' : c.PATIENT_GENDER === 'FEMALE' ? '女' : '其他'}</td>
                <td>${c.PATIENT_AGE || ''}</td>
                <td>${c.CASE_TYPE === 'INPATIENT' ? '住院' : '门诊'}</td>
                <td>${c.HOSPITAL_NAME || ''}</td>
                <td>${c.TOTAL_COST || ''}</td>
                <td>${c.STATUS || ''}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `

    const buffer = Buffer.from(htmlContent, 'utf8')

    return {
      success: true,
      data: {
        buffer,
        contentType: 'text/html',
        filename: `medical-cases-${timestamp}.html`
      }
    }
  } catch (error) {
    console.error('❌ 生成PDF失败:', error)
    return { success: false, error: '生成PDF文件失败' }
  }
}
