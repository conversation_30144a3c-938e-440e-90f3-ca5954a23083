import { SupervisionRule, RuleStatistics } from '@/types/supervision-rule'

// 模拟监管规则数据
export const mockSupervisionRules: SupervisionRule[] = [
  {
    id: 1,
    ruleCode: 'RULE_001',
    ruleName: '异地住院费用控制规则',
    ruleType: 'SQL',
    ruleCategory: 'COST_CONTROL',
    description: '检测异地住院费用是否超过合理范围，防止过度医疗',
    ruleContent: 'SELECT * FROM medical_cases WHERE case_type = "异地住院" AND total_amount > 50000',
    ruleSql: 'SELECT * FROM medical_cases WHERE case_type = "异地住院" AND total_amount > 50000',
    priorityLevel: 8,
    severityLevel: 'HIGH',
    isActive: true,
    effectiveDate: '2024-01-01',
    expiryDate: '2024-12-31',
    versionNumber: '1.0',
    ruleSource: 'SYSTEM',
    createdFrom: 'MANUAL',
    executionCount: 156,
    successCount: 142,
    lastExecutedAt: '2024-01-15T10:30:00Z',
    isDeleted: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    createdBy: 1,
    updatedBy: 1
  },
  {
    id: 2,
    ruleCode: 'RULE_002',
    ruleName: '欺诈检测规则',
    ruleType: 'DSL',
    ruleCategory: 'FRAUD_DETECTION',
    description: '检测可疑的医疗欺诈行为，包括虚假诊断和过度治疗',
    ruleContent: 'if (diagnosis_count > 10 && treatment_days < 3) { flag_suspicious(); }',
    ruleDsl: 'if (diagnosis_count > 10 && treatment_days < 3) { flag_suspicious(); }',
    priorityLevel: 9,
    severityLevel: 'CRITICAL',
    isActive: true,
    effectiveDate: '2024-01-01',
    expiryDate: '2024-12-31',
    versionNumber: '2.1',
    ruleSource: 'SYSTEM',
    createdFrom: 'TEMPLATE',
    executionCount: 89,
    successCount: 85,
    lastExecutedAt: '2024-01-15T14:20:00Z',
    isDeleted: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T14:20:00Z',
    createdBy: 1,
    updatedBy: 2
  },
  {
    id: 3,
    ruleCode: 'RULE_003',
    ruleName: '合规性检查规则',
    ruleType: 'JAVASCRIPT',
    ruleCategory: 'COMPLIANCE_CHECK',
    description: '检查医疗案例是否符合相关法规和政策要求',
    ruleContent: 'function checkCompliance(caseData) { return caseData.hasValidLicense && caseData.followsProtocol; }',

    priorityLevel: 6,
    severityLevel: 'MEDIUM',
    isActive: true,
    effectiveDate: '2024-01-01',
    expiryDate: '2024-12-31',
    versionNumber: '1.5',
    ruleSource: 'EXTERNAL',
    createdFrom: 'IMPORT',
    executionCount: 234,
    successCount: 228,
    lastExecutedAt: '2024-01-15T16:45:00Z',
    isDeleted: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T16:45:00Z',
    createdBy: 3,
    updatedBy: 3
  },
  {
    id: 4,
    ruleCode: 'RULE_004',
    ruleName: '质量保证规则',
    ruleType: 'SQL',
    ruleCategory: 'QUALITY_ASSURANCE',
    description: '确保医疗服务质量符合标准要求',
    ruleContent: 'SELECT * FROM medical_cases WHERE quality_score < 80',
    ruleSql: 'SELECT * FROM medical_cases WHERE quality_score < 80',
    priorityLevel: 5,
    severityLevel: 'MEDIUM',
    isActive: false,
    effectiveDate: '2024-01-01',
    expiryDate: '2024-12-31',
    versionNumber: '1.0',
    ruleSource: 'SYSTEM',
    createdFrom: 'MANUAL',
    executionCount: 67,
    successCount: 62,
    lastExecutedAt: '2024-01-10T09:15:00Z',
    isDeleted: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T09:15:00Z',
    createdBy: 1,
    updatedBy: 1
  },
  {
    id: 5,
    ruleCode: 'RULE_005',
    ruleName: '药品使用监控规则',
    ruleType: 'DSL',
    ruleCategory: 'COST_CONTROL',
    description: '监控药品使用是否合理，防止滥用和浪费',
    ruleContent: 'if (drug_cost > average_cost * 2) { alert_high_cost(); }',
    ruleDsl: 'if (drug_cost > average_cost * 2) { alert_high_cost(); }',
    priorityLevel: 7,
    severityLevel: 'HIGH',
    isActive: true,
    effectiveDate: '2024-01-01',
    expiryDate: '2024-12-31',
    versionNumber: '1.2',
    ruleSource: 'SYSTEM',
    createdFrom: 'TEMPLATE',
    executionCount: 198,
    successCount: 189,
    lastExecutedAt: '2024-01-15T11:30:00Z',
    isDeleted: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T11:30:00Z',
    createdBy: 2,
    updatedBy: 2
  }
]

// 模拟统计数据
export const mockRuleStatistics: RuleStatistics = {
  totalRules: 5,
  activeRules: 4,
  inactiveRules: 1,
  rulesByType: {
    'SQL': 3,
    'DSL': 1,
    'JAVASCRIPT': 1
  },
  rulesByCategory: {
    'COST_CONTROL': 2,
    'FRAUD_DETECTION': 1,
    'COMPLIANCE_CHECK': 1,
    'QUALITY_ASSURANCE': 1,
    'STATISTICAL_ANALYSIS': 0
  },
  rulesBySeverity: {
    'LOW': 0,
    'MEDIUM': 2,
    'HIGH': 2,
    'CRITICAL': 1
  },
  executionStats: {
    totalExecutions: 744,
    successfulExecutions: 706,
    failedExecutions: 38,
    avgExecutionTime: 1250
  },
  resultStats: {
    totalResults: 1456,
    violationResults: 89,
    suspiciousResults: 156,
    normalResults: 1211,
    avgConfidenceScore: 85.6
  },
  monthlyTrend: [
    {
      month: '2024-01',
      executionCount: 744,
      violationCount: 89,
      avgConfidenceScore: 85.6
    },
    {
      month: '2023-12',
      executionCount: 698,
      violationCount: 76,
      avgConfidenceScore: 83.2
    },
    {
      month: '2023-11',
      executionCount: 612,
      violationCount: 65,
      avgConfidenceScore: 81.8
    }
  ]
}

// 检查是否应该使用模拟数据
export function shouldUseMockData(): boolean {
  // 在开发环境中，如果数据库连接失败，使用模拟数据
  return process.env.NODE_ENV === 'development' && process.env['USE_MOCK_DATA'] === 'true'
}

// 模拟API延迟
export function mockApiDelay(ms: number = 500): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}
