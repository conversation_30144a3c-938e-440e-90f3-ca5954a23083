import oracledb from 'oracledb'

// Oracle数据库连接配置 - 性能优化版本
const dbConfig = {
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  connectString: `${process.env['DB_HOST']}:${process.env['DB_PORT']}/${process.env['DB_SERVICE_NAME']}`,
  poolMin: parseInt(process.env['DB_POOL_MIN'] || '5'),        // 增加最小连接数
  poolMax: parseInt(process.env['DB_POOL_MAX'] || '20'),       // 增加最大连接数
  poolIncrement: parseInt(process.env['DB_POOL_INCREMENT'] || '2'), // 增加连接增量
  poolTimeout: parseInt(process.env['DB_POOL_TIMEOUT'] || '30'),    // 连接超时时间
  stmtCacheSize: parseInt(process.env['DB_STMT_CACHE_SIZE'] || '50'), // 语句缓存大小
  queueMax: parseInt(process.env['DB_QUEUE_MAX'] || '100'),         // 队列最大长度
  queueTimeout: parseInt(process.env['DB_QUEUE_TIMEOUT'] || '10000'), // 队列超时时间
  enableStatistics: true, // 启用连接池统计
}

// 配置Oracle客户端
oracledb.outFormat = oracledb.OUT_FORMAT_OBJECT
oracledb.autoCommit = true

// 连接池
let pool: oracledb.Pool | null = null

/**
 * 初始化数据库连接池
 */
export async function initializePool(): Promise<void> {
  try {
    if (!pool) {
      pool = await oracledb.createPool(dbConfig)
      console.log('✅ 数据库连接池初始化成功')
      // 启动健康检查
      startHealthCheck()
    }
  } catch (error) {
    console.error('❌ 数据库连接池初始化失败:', error)
    throw error
  }
}

/**
 * 获取数据库连接
 */
export async function getConnection(): Promise<oracledb.Connection> {
  try {
    if (!pool) {
      await initializePool()
    }
    return await pool!.getConnection()
  } catch (error) {
    console.error('❌ 获取数据库连接失败:', error)
    throw error
  }
}

// 清理Oracle查询结果，移除循环引用
function cleanOracleResult(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(item => cleanOracleResult(item))
  }

  if (obj instanceof Date) {
    return obj
  }

  if (Buffer.isBuffer(obj)) {
    return obj
  }

  // 创建新对象，只复制基本属性
  const cleanObj: any = {}
  for (const [key, value] of Object.entries(obj)) {
    // 跳过Oracle内部属性和可能的循环引用
    if (typeof key === 'string' && (
        key.startsWith('_') ||
        key.toLowerCase().includes('connection') ||
        key.toLowerCase().includes('pool') ||
        key.toLowerCase().includes('client') ||
        key.toLowerCase().includes('socket') ||
        key.toLowerCase().includes('stream') ||
        key.toLowerCase().includes('cursor') ||
        key.toLowerCase().includes('resultset') ||
        key === 'domain' ||
        key === 'constructor' ||
        key === 'prototype'
      )) {
      continue
    }

    try {
      cleanObj[key] = cleanOracleResult(value)
    } catch (error) {
      // 跳过有问题的属性
      continue
    }
  }

  return cleanObj
}

/**
 * 执行SQL查询
 */
export async function executeQuery<T = any>(
  sql: string,
  binds: any = {},
  options: oracledb.ExecuteOptions = {}
): Promise<{ rows: T[], rowsAffected?: number, metaData?: any }> {
  let connection: oracledb.Connection | null = null

  try {
    connection = await getConnection()
    const result = await connection.execute<T>(sql, binds, options)

    // 使用经过验证的清理函数，移除循环引用
    const cleanRows = result.rows ? cleanOracleResult(result.rows) : []

    return {
      rows: cleanRows,
      rowsAffected: result.rowsAffected,
      metaData: result.metaData ? cleanOracleResult(result.metaData) : undefined
    }
  } catch (error) {
    console.error('❌ SQL查询执行失败:', error)
    console.error('SQL:', sql)
    console.error('Binds:', binds)
    throw error
  } finally {
    if (connection) {
      try {
        await connection.close()
      } catch (error) {
        console.error('⚠️ 关闭数据库连接失败:', error)
      }
    }
  }
}

/**
 * 执行事务
 */
export async function executeTransaction<T = any>(
  operations: (connection: oracledb.Connection) => Promise<T>
): Promise<T> {
  let connection: oracledb.Connection | null = null
  
  try {
    connection = await getConnection()
    
    // 关闭自动提交
    // connection.autoCommit = false // Oracle连接不支持直接设置autoCommit属性
    
    // 执行操作
    const result = await operations(connection)
    
    // 提交事务
    await connection.commit()
    
    return result
  } catch (error) {
    // 回滚事务
    if (connection) {
      try {
        await connection.rollback()
      } catch (rollbackError) {
        console.error('⚠️ 事务回滚失败:', rollbackError)
      }
    }
    
    console.error('❌ 事务执行失败:', error)
    throw error
  } finally {
    if (connection) {
      try {
        // 恢复自动提交
        // connection.autoCommit = true // Oracle连接不支持直接设置autoCommit属性
        await connection.close()
      } catch (error) {
        console.error('⚠️ 关闭数据库连接失败:', error)
      }
    }
  }
}

/**
 * 关闭数据库连接池
 */
export async function closePool(): Promise<void> {
  try {
    if (pool) {
      // 停止健康检查
      stopHealthCheck()
      await pool.close(10) // 等待10秒关闭
      pool = null
      console.log('✅ 数据库连接池已关闭')
    }
  } catch (error) {
    console.error('❌ 关闭数据库连接池失败:', error)
    throw error
  }
}

/**
 * 检查数据库连接状态
 */
export async function checkConnection(): Promise<boolean> {
  try {
    const result = await executeQuery('SELECT SYSDATE FROM DUAL')
    return !!(result.rows && result.rows.length > 0)
  } catch (error) {
    console.error('❌ 数据库连接检查失败:', error)
    return false
  }
}

/**
 * 格式化SQL绑定参数
 */
export function formatBinds(params: Record<string, any>): Record<string, any> {
  const formatted: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(params)) {
    if (value === null || value === undefined) {
      formatted[key] = { val: null, type: oracledb.STRING }
    } else if (typeof value === 'string') {
      formatted[key] = { val: value, type: oracledb.STRING }
    } else if (typeof value === 'number') {
      formatted[key] = { val: value, type: oracledb.NUMBER }
    } else if (value instanceof Date) {
      formatted[key] = { val: value, type: oracledb.DATE }
    } else {
      formatted[key] = value
    }
  }
  
  return formatted
}

/**
 * 构建分页SQL
 */
export function buildPaginationSQL(
  baseSql: string,
  page: number = 1,
  pageSize: number = 10,
  orderBy: string = 'ID'
): string {
  const offset = (page - 1) * pageSize

  // 检查baseSql是否已经包含ORDER BY子句
  const hasOrderBy = /ORDER\s+BY/i.test(baseSql)

  return `
    SELECT * FROM (
      SELECT a.*, ROWNUM rnum FROM (
        ${baseSql}
        ${hasOrderBy ? '' : `ORDER BY ${orderBy}`}
      ) a
      WHERE ROWNUM <= ${offset + pageSize}
    )
    WHERE rnum > ${offset}
  `
}

/**
 * 构建计数SQL
 */
export function buildCountSQL(baseSql: string): string {
  // 移除ORDER BY子句
  const cleanSql = baseSql.replace(/ORDER\s+BY\s+[^)]*$/i, '')
  
  return `SELECT COUNT(*) as TOTAL FROM (${cleanSql})`
}

// 进程退出时关闭连接池
process.on('SIGINT', async () => {
  await closePool()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  await closePool()
  process.exit(0)
})

/**
 * 获取连接池统计信息
 */
export function getPoolStats(): {
  connectionsOpen: number
  connectionsInUse: number
  connectionsAvailable: number
  queueLength: number
  queueTimeout: number
  poolMin: number
  poolMax: number
  poolIncrement: number
} {
  if (!pool) {
    return {
      connectionsOpen: 0,
      connectionsInUse: 0,
      connectionsAvailable: 0,
      queueLength: 0,
      queueTimeout: 0,
      poolMin: 0,
      poolMax: 0,
      poolIncrement: 0
    }
  }

  return {
    connectionsOpen: pool.connectionsOpen,
    connectionsInUse: pool.connectionsInUse,
    connectionsAvailable: pool.connectionsOpen - pool.connectionsInUse,
    queueLength: (pool as any).queueLength || 0,
    queueTimeout: (pool as any).queueTimeout || 0,
    poolMin: pool.poolMin,
    poolMax: pool.poolMax,
    poolIncrement: pool.poolIncrement
  }
}

/**
 * 监控连接池健康状态
 */
export function monitorPoolHealth(): {
  status: 'healthy' | 'warning' | 'critical'
  message: string
  stats: ReturnType<typeof getPoolStats>
} {
  const stats = getPoolStats()

  if (!pool) {
    return {
      status: 'critical',
      message: '连接池未初始化',
      stats
    }
  }

  const utilizationRate = stats.connectionsOpen > 0
    ? (stats.connectionsInUse / stats.connectionsOpen) * 100
    : 0

  const queueUtilization = stats.queueLength > 0
    ? (stats.queueLength / (dbConfig.queueMax || 100)) * 100
    : 0

  if (utilizationRate > 90 || queueUtilization > 80) {
    return {
      status: 'critical',
      message: `连接池使用率过高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,
      stats
    }
  }

  if (utilizationRate > 70 || queueUtilization > 50) {
    return {
      status: 'warning',
      message: `连接池使用率偏高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,
      stats
    }
  }

  return {
    status: 'healthy',
    message: `连接池运行正常: ${utilizationRate.toFixed(1)}% 使用率`,
    stats
  }
}

// 健康检查定时器 - 使用全局变量避免重复创建
declare global {
  var __healthCheckInterval: NodeJS.Timeout | undefined
}

/**
 * 启动连接池健康检查
 */
function startHealthCheck() {
  // 如果已经有定时器在运行，先清除它
  if (global.__healthCheckInterval) {
    clearInterval(global.__healthCheckInterval)
  }

  global.__healthCheckInterval = setInterval(() => {
    const health = monitorPoolHealth()
    if (health.status === 'critical') {
      console.error('🚨 连接池健康检查:', health.message)
    } else if (health.status === 'warning') {
      console.warn('⚠️ 连接池健康检查:', health.message)
    }
  }, 30000) // 每30秒检查一次
}

/**
 * 停止连接池健康检查
 */
function stopHealthCheck() {
  if (global.__healthCheckInterval) {
    clearInterval(global.__healthCheckInterval)
    global.__healthCheckInterval = undefined
  }
}
