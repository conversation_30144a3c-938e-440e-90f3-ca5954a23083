// 注意：此文件为响应格式工具库，不是API路由文件
// 提供统一的响应格式和错误处理工具
// 不需要认证检查，由API路由文件使用

/**
 * 统一API响应格式
 * 实现P1-002 API接口规范化目标：
 * - 统一响应格式
 * - 完整错误处理
 * - 标准化接口规范
 *
 * @param data 响应数据，可以是任意类型
 * @param message 响应消息，默认为"操作成功"
 * @param meta 元数据信息，包含时间戳、版本等
 * @returns 标准化的API响应对象
 *
 * @example 成功响应示例
 * ```typescript
 * // 使用createSuccessResponse
 * const response = createSuccessResponse(
 *   { userId: 123, name: "张三" },
 *   "获取用户信息成功"
 * )
 *
 * // 响应格式
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "获取用户信息成功",
 *   "data": { "userId": 123, "name": "张三" },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * ```
 *
 * @example 错误响应示例
 * ```typescript
 * // 使用createErrorResponse
 * const errorResponse = createErrorResponse(
 *   ErrorCode.VALIDATION_ERROR,
 *   "请求参数验证失败",
 *   { field: "email", message: "邮箱格式不正确" }
 * )
 *
 * // 错误响应格式
 * {
 *   "success": false,
 *   "code": "VALIDATION_ERROR",
 *   "message": "请求参数验证失败",
 *   "details": { "field": "email", "message": "邮箱格式不正确" },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * ```
 *
 * @example 分页响应示例
 * ```typescript
 * // 使用createPaginatedResponse
 * const paginatedResponse = createPaginatedResponse(
 *   [{ id: 1, name: "用户1" }, { id: 2, name: "用户2" }],
 *   { page: 1, pageSize: 20, total: 100, totalPages: 5, hasNext: true, hasPrev: false },
 *   "获取用户列表成功"
 * )
 *
 * // 分页响应格式
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "获取用户列表成功",
 *   "data": [{ "id": 1, "name": "用户1" }, { "id": 2, "name": "用户2" }],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "pagination": {
 *       "page": 1,
 *       "pageSize": 20,
 *       "total": 100,
 *       "totalPages": 5,
 *       "hasNext": true,
 *       "hasPrev": false
 *     }
 *   }
 * }
 * ```
 */

import { NextResponse } from 'next/server'

// 分页元数据
export interface PaginationMeta {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 性能元数据
export interface PerformanceMeta {
  executionTime: number
  queryTime?: number
  cacheHit?: boolean
  requestId: string
}

// 统一API响应格式
export interface ApiResponse<T = any> {
  success: boolean
  code: string
  message: string
  data?: T
  meta?: {
    timestamp: string
    pagination?: PaginationMeta
    performance?: PerformanceMeta
    version?: string
  }
}

// 错误码枚举
export enum ErrorCode {
  // 通用错误
  SUCCESS = 'SUCCESS',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // 认证授权错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  
  // 资源错误
  NOT_FOUND = 'NOT_FOUND',
  RESOURCE_EXISTS = 'RESOURCE_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  
  // 业务错误
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  OPERATION_FAILED = 'OPERATION_FAILED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  
  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  
  // 外部服务错误
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // 限流错误
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  
  // 文件错误
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  
  // 医保业务特定错误
  MEDICAL_CASE_NOT_FOUND = 'MEDICAL_CASE_NOT_FOUND',
  INVALID_MEDICAL_DATA = 'INVALID_MEDICAL_DATA',
  SUPERVISION_RULE_ERROR = 'SUPERVISION_RULE_ERROR',
  KNOWLEDGE_BASE_ERROR = 'KNOWLEDGE_BASE_ERROR'
}

// 错误码对应的HTTP状态码
export const ErrorCodeToHttpStatus: Record<ErrorCode, number> = {
  [ErrorCode.SUCCESS]: 200,
  [ErrorCode.UNKNOWN_ERROR]: 500,
  [ErrorCode.INVALID_REQUEST]: 400,
  [ErrorCode.VALIDATION_ERROR]: 400,
  
  [ErrorCode.UNAUTHORIZED]: 401,
  [ErrorCode.FORBIDDEN]: 403,
  [ErrorCode.TOKEN_EXPIRED]: 401,
  [ErrorCode.INVALID_TOKEN]: 401,
  
  [ErrorCode.NOT_FOUND]: 404,
  [ErrorCode.RESOURCE_EXISTS]: 409,
  [ErrorCode.RESOURCE_CONFLICT]: 409,
  
  [ErrorCode.BUSINESS_ERROR]: 400,
  [ErrorCode.OPERATION_FAILED]: 400,
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: 403,
  
  [ErrorCode.DATABASE_ERROR]: 500,
  [ErrorCode.CONNECTION_ERROR]: 500,
  [ErrorCode.QUERY_ERROR]: 500,
  
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: 502,
  [ErrorCode.NETWORK_ERROR]: 502,
  [ErrorCode.TIMEOUT_ERROR]: 504,
  
  [ErrorCode.RATE_LIMIT_EXCEEDED]: 429,
  [ErrorCode.QUOTA_EXCEEDED]: 429,
  
  [ErrorCode.FILE_NOT_FOUND]: 404,
  [ErrorCode.FILE_TOO_LARGE]: 413,
  [ErrorCode.INVALID_FILE_TYPE]: 400,
  
  [ErrorCode.MEDICAL_CASE_NOT_FOUND]: 404,
  [ErrorCode.INVALID_MEDICAL_DATA]: 400,
  [ErrorCode.SUPERVISION_RULE_ERROR]: 400,
  [ErrorCode.KNOWLEDGE_BASE_ERROR]: 400
}

// 错误码对应的中文消息
export const ErrorCodeToMessage: Record<ErrorCode, string> = {
  [ErrorCode.SUCCESS]: '操作成功',
  [ErrorCode.UNKNOWN_ERROR]: '未知错误',
  [ErrorCode.INVALID_REQUEST]: '请求参数无效',
  [ErrorCode.VALIDATION_ERROR]: '数据验证失败',
  
  [ErrorCode.UNAUTHORIZED]: '未授权访问',
  [ErrorCode.FORBIDDEN]: '访问被禁止',
  [ErrorCode.TOKEN_EXPIRED]: '登录已过期',
  [ErrorCode.INVALID_TOKEN]: '无效的访问令牌',
  
  [ErrorCode.NOT_FOUND]: '资源不存在',
  [ErrorCode.RESOURCE_EXISTS]: '资源已存在',
  [ErrorCode.RESOURCE_CONFLICT]: '资源冲突',
  
  [ErrorCode.BUSINESS_ERROR]: '业务处理失败',
  [ErrorCode.OPERATION_FAILED]: '操作失败',
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: '权限不足',
  
  [ErrorCode.DATABASE_ERROR]: '数据库错误',
  [ErrorCode.CONNECTION_ERROR]: '连接失败',
  [ErrorCode.QUERY_ERROR]: '查询失败',
  
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: '外部服务错误',
  [ErrorCode.NETWORK_ERROR]: '网络错误',
  [ErrorCode.TIMEOUT_ERROR]: '请求超时',
  
  [ErrorCode.RATE_LIMIT_EXCEEDED]: '请求频率超限',
  [ErrorCode.QUOTA_EXCEEDED]: '配额已用完',
  
  [ErrorCode.FILE_NOT_FOUND]: '文件不存在',
  [ErrorCode.FILE_TOO_LARGE]: '文件过大',
  [ErrorCode.INVALID_FILE_TYPE]: '文件类型不支持',
  
  [ErrorCode.MEDICAL_CASE_NOT_FOUND]: '医疗案例不存在',
  [ErrorCode.INVALID_MEDICAL_DATA]: '医疗数据格式错误',
  [ErrorCode.SUPERVISION_RULE_ERROR]: '监管规则处理失败',
  [ErrorCode.KNOWLEDGE_BASE_ERROR]: '知识库操作失败'
}

// API错误类
export class ApiError extends Error {
  public readonly code: ErrorCode
  public readonly httpStatus: number
  public readonly details?: any

  constructor(
    code: ErrorCode,
    message?: string,
    details?: any
  ) {
    super(message || ErrorCodeToMessage[code])
    this.name = 'ApiError'
    this.code = code
    this.httpStatus = ErrorCodeToHttpStatus[code]
    this.details = details
  }
}

// 成功响应构建器
export function createSuccessResponse<T>(
  data?: T,
  message?: string,
  meta?: Partial<ApiResponse['meta']>
): ApiResponse<T> {
  try {
    return {
      success: true,
      code: ErrorCode.SUCCESS,
      message: message || ErrorCodeToMessage[ErrorCode.SUCCESS],
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        ...meta
      }
    }
  } catch (error) {
    console.error('创建成功响应失败:', error)
    // 返回基本的成功响应
    return {
      success: true,
      code: ErrorCode.SUCCESS,
      message: '操作成功',
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    }
  }
}

// 错误响应构建器
export function createErrorResponse(
  code: ErrorCode,
  message?: string,
  details?: any
): ApiResponse {
  try {
    return {
      success: false,
      code,
      message: message || ErrorCodeToMessage[code] || '未知错误',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      },
      ...(details && { details })
    }
  } catch (error) {
    console.error('创建错误响应失败:', error)
    // 返回基本的错误响应
    return {
      success: false,
      code: ErrorCode.UNKNOWN_ERROR,
      message: '系统错误',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    }
  }
}

// 分页响应构建器
export function createPaginatedResponse<T>(
  data: T[],
  pagination: PaginationMeta,
  message?: string,
  performance?: PerformanceMeta
): ApiResponse<T[]> {
  try {
    return {
      success: true,
      code: ErrorCode.SUCCESS,
      message: message || ErrorCodeToMessage[ErrorCode.SUCCESS],
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        pagination,
        ...(performance && { performance })
      }
    }
  } catch (error) {
    console.error('创建分页响应失败:', error)
    // 返回基本的分页响应
    return {
      success: true,
      code: ErrorCode.SUCCESS,
      message: '获取数据成功',
      data: data || [],
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        pagination: pagination || {
          page: 1,
          pageSize: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      }
    }
  }
}

// NextResponse包装器
export function createNextResponse<T>(response: ApiResponse<T>): NextResponse {
  try {
    const httpStatus = response.success
      ? 200
      : ErrorCodeToHttpStatus[response.code as ErrorCode] || 500

    return NextResponse.json(response, { status: httpStatus })
  } catch (error) {
    console.error('创建NextResponse失败:', error)
    // 返回基本的错误响应
    return NextResponse.json({
      success: false,
      code: ErrorCode.UNKNOWN_ERROR,
      message: '系统错误',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    }, { status: 500 })
  }
}

// 成功响应快捷方法
export function successResponse<T>(
  data?: T,
  message?: string,
  meta?: Partial<ApiResponse['meta']>
): NextResponse {
  try {
    return createNextResponse(createSuccessResponse(data, message, meta))
  } catch (error) {
    console.error('创建成功响应失败:', error)
    return createNextResponse(createErrorResponse(ErrorCode.UNKNOWN_ERROR, '创建响应失败'))
  }
}

// 错误响应快捷方法
export function errorResponse(
  code: ErrorCode,
  message?: string,
  details?: any
): NextResponse {
  try {
    return createNextResponse(createErrorResponse(code, message, details))
  } catch (error) {
    console.error('创建错误响应失败:', error)
    return createNextResponse(createErrorResponse(ErrorCode.UNKNOWN_ERROR, '创建错误响应失败'))
  }
}

// 分页响应快捷方法
export function paginatedResponse<T>(
  data: T[],
  pagination: PaginationMeta,
  message?: string,
  performance?: PerformanceMeta
): NextResponse {
  try {
    return createNextResponse(createPaginatedResponse(data, pagination, message, performance))
  } catch (error) {
    console.error('创建分页响应失败:', error)
    return createNextResponse(createErrorResponse(ErrorCode.UNKNOWN_ERROR, '创建分页响应失败'))
  }
}
