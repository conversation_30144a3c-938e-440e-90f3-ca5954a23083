// 注意：此文件为API处理器工具库，不是API路由文件
// 提供统一的API处理器，包含认证、验证、错误处理
// 本身不需要认证检查，为其他API提供认证功能

/**
 * 统一API处理器
 * 实现P1-002 API接口规范化目标：
 * - 统一错误处理
 * - 请求验证
 * - 性能监控
 * - 日志记录
 *
 * @example 基本使用示例
 * ```typescript
 * // 带认证的API处理器
 * export const GET = withAuth(async (request, context) => {
 *   const data = await getUserData(context.user.id)
 *   return data
 * })
 *
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { "userId": 123, "name": "张三" },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "performance": { "executionTime": 150, "requestId": "req_123" }
 *   }
 * }
 * ```
 *
 * @example 带验证的API处理器
 * ```typescript
 * const schema = z.object({
 *   name: z.string(),
 *   email: z.string().email()
 * })
 *
 * export const POST = withAuthAndValidation(schema, async (request, context) => {
 *   const { name, email } = context.validatedData
 *   const user = await createUser({ name, email })
 *   return user
 * })
 *
 * // 错误响应示例
 * {
 *   "success": false,
 *   "code": "VALIDATION_ERROR",
 *   "message": "请求参数验证失败",
 *   "details": [
 *     { "path": ["email"], "message": "Invalid email format" }
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * ```
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { 
  ApiError, 
  ErrorCode, 
  successResponse, 
  errorResponse, 
  createSuccessResponse,
  PerformanceMeta 
} from './response'
import { verifyAccessToken } from '@/lib/jwt'
import { recordAuditLog } from '@/lib/security/audit-middleware'

// API处理器选项
export interface ApiHandlerOptions {
  requireAuth?: boolean
  requiredRoles?: string[]
  validateSchema?: z.ZodSchema
  rateLimit?: {
    maxRequests: number
    windowMs: number
  }
  cache?: {
    ttl: number
    key?: string
  }
  timeout?: number
}

// API处理器函数类型
export type ApiHandlerFunction<T = any> = (
  request: NextRequest,
  context?: any
) => Promise<T>

// 请求上下文
export interface RequestContext {
  user?: {
    id: number
    username: string
    roles: string[]
  }
  requestId: string
  startTime: number
  ip: string
  userAgent: string
}

/**
 * 统一API处理器
 */
export function createApiHandler<T = any>(
  handler: ApiHandlerFunction<T>,
  options: ApiHandlerOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now()
    const requestId = generateRequestId()
    
    // 创建请求上下文
    const context: RequestContext = {
      requestId,
      startTime,
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent') || 'Unknown'
    }

    try {
      // 1. 身份验证
      if (options.requireAuth) {
        context.user = await authenticateRequest(request)
      }

      // 2. 权限检查
      if (options.requiredRoles && context.user) {
        checkUserRoles(context.user.roles, options.requiredRoles)
      }

      // 3. 请求验证
      if (options.validateSchema) {
        await validateRequest(request, options.validateSchema)
      }

      // 4. 速率限制
      if (options.rateLimit) {
        await checkRateLimit(context.ip, options.rateLimit)
      }

      // 5. 执行处理器
      const result = await executeWithTimeout(
        () => handler(request, context),
        options.timeout || 30000
      )

      // 6. 记录审计日志
      if (context.user) {
        await recordAuditLog({
          userId: context.user.id,
          userName: context.user.username,
          action: request.method,
          resource: request.nextUrl.pathname,
          operationResult: 'SUCCESS',
          operationDescription: `API调用成功: ${request.method} ${request.nextUrl.pathname}`,
          ipAddress: context.ip,
          userAgent: context.userAgent,
          timestamp: new Date(),
          executionTime: Date.now() - startTime
        })
      }

      // 7. 构建性能元数据
      const performance: PerformanceMeta = {
        executionTime: Date.now() - startTime,
        requestId: context.requestId
      }

      // 8. 返回成功响应
      return successResponse(result, undefined, { performance })

    } catch (error) {
      // 错误处理
      return handleApiError(error, context, startTime)
    }
  }
}

/**
 * 身份验证
 */
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new ApiError(ErrorCode.UNAUTHORIZED, '缺少访问令牌')
  }

  const token = authHeader.substring(7)
  const payload = verifyAccessToken(token)

  if (!payload) {
    throw new ApiError(ErrorCode.TOKEN_EXPIRED, '访问令牌已过期')
  }

  return {
    id: payload.userId,
    username: payload.username,
    roles: payload.roles || []
  }
}

/**
 * 权限检查
 */
function checkUserRoles(userRoles: string[], requiredRoles: string[]) {
  const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role))
  
  if (!hasRequiredRole) {
    throw new ApiError(
      ErrorCode.INSUFFICIENT_PERMISSIONS,
      `需要以下权限之一: ${requiredRoles.join(', ')}`
    )
  }
}

/**
 * 请求验证
 */
async function validateRequest(request: NextRequest, schema: z.ZodSchema) {
  try {
    let data: any

    if (request.method === 'GET') {
      // 验证查询参数
      const url = new URL(request.url)
      data = Object.fromEntries(url.searchParams.entries())
    } else {
      // 验证请求体
      const contentType = request.headers.get('content-type')
      
      if (contentType?.includes('application/json')) {
        data = await request.json()
      } else if (contentType?.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData()
        data = Object.fromEntries(formData.entries())
      } else {
        throw new ApiError(ErrorCode.INVALID_REQUEST, '不支持的内容类型')
      }
    }

    schema.parse(data)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(
        ErrorCode.VALIDATION_ERROR,
        '请求参数验证失败',
        error.errors
      )
    }
    throw error
  }
}

/**
 * 速率限制检查
 */
async function checkRateLimit(
  ip: string,
  rateLimit: { maxRequests: number; windowMs: number }
) {
  // 这里应该实现实际的速率限制逻辑
  // 可以使用Redis或内存存储
  // 暂时跳过实现
}

/**
 * 超时执行
 */
async function executeWithTimeout<T>(
  fn: () => Promise<T>,
  timeoutMs: number
): Promise<T> {
  return Promise.race([
    fn(),
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new ApiError(ErrorCode.TIMEOUT_ERROR)), timeoutMs)
    )
  ])
}

/**
 * 错误处理
 */
async function handleApiError(
  error: any,
  context: RequestContext,
  startTime: number
): Promise<NextResponse> {
  let apiError: ApiError

  if (error instanceof ApiError) {
    apiError = error
  } else if (error instanceof z.ZodError) {
    apiError = new ApiError(
      ErrorCode.VALIDATION_ERROR,
      '请求参数验证失败',
      error.errors
    )
  } else {
    // 记录未知错误
    console.error('Unhandled API error:', error)
    apiError = new ApiError(ErrorCode.UNKNOWN_ERROR, '服务器内部错误')
  }

  // 记录错误审计日志
  if (context.user) {
    await recordAuditLog({
      userId: context.user.id,
      userName: context.user.username,
      action: 'ERROR',
      resource: 'API',
      operationResult: 'FAILED',
      operationDescription: `API调用失败: ${apiError.message}`,
      ipAddress: context.ip,
      userAgent: context.userAgent,
      timestamp: new Date(),
      executionTime: Date.now() - startTime
    }).catch(console.error)
  }

  return errorResponse(apiError.code, apiError.message, apiError.details)
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 获取客户端IP
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown'
  }
  
  return realIP || 'unknown'
}

/**
 * 便捷的API处理器创建函数
 */
export const withAuth = (handler: ApiHandlerFunction, requiredRoles?: string[]) =>
  createApiHandler(handler, { requireAuth: true, requiredRoles })

export const withValidation = (handler: ApiHandlerFunction, schema: z.ZodSchema) =>
  createApiHandler(handler, { validateSchema: schema })

export const withAuthAndValidation = (
  handler: ApiHandlerFunction,
  schema: z.ZodSchema,
  requiredRoles?: string[]
) =>
  createApiHandler(handler, {
    requireAuth: true,
    requiredRoles,
    validateSchema: schema
  })

export const publicApi = (handler: ApiHandlerFunction) =>
  createApiHandler(handler, { requireAuth: false })
