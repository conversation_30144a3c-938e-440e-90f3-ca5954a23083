// 注意：此文件为工具库，不是API路由文件
// 错误处理由调用方负责
// 主要用于生成API文档和验证

/**
 * API文档生成工具
 * 实现P1-002 API接口规范化目标：
 * - 自动生成API文档
 * - 接口规范验证
 * - 文档覆盖率统计
 */

import { z } from 'zod'
import { ErrorCode, ApiError, createSuccessResponse, createErrorResponse } from './response'

// API端点信息
export interface ApiEndpoint {
  path: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  summary: string
  description?: string
  tags: string[]
  requireAuth: boolean
  requiredRoles?: string[]
  requestSchema?: z.ZodSchema
  responseSchema?: z.ZodSchema
  examples?: {
    request?: any
    response?: any
  }
  errorCodes?: ErrorCode[]
}

// API文档注册表
class ApiDocumentationRegistry {
  private endpoints: Map<string, ApiEndpoint> = new Map()

  /**
   * 注册API端点
   */
  register(endpoint: ApiEndpoint): void {
    const key = `${endpoint.method}:${endpoint.path}`
    this.endpoints.set(key, endpoint)
  }

  /**
   * 获取所有端点
   * @returns 标准API响应格式包含端点列表
   */
  getAllEndpoints(): any {
    try {
      const endpoints = Array.from(this.endpoints.values())
      return createSuccessResponse(endpoints, '获取API端点列表成功')
    } catch (error) {
      console.error('获取API端点列表失败:', error)
      return createErrorResponse(ErrorCode.UNKNOWN_ERROR, '获取API端点列表失败')
    }
  }

  /**
   * 按标签获取端点
   * @param tag 标签名称
   * @returns 标准API响应格式包含筛选后的端点列表
   */
  getEndpointsByTag(tag: string): any {
    try {
      const allEndpoints = Array.from(this.endpoints.values())
      const filteredEndpoints = allEndpoints.filter(endpoint =>
        endpoint.tags.includes(tag)
      )
      return createSuccessResponse(filteredEndpoints, `获取标签"${tag}"的API端点成功`)
    } catch (error) {
      console.error('按标签获取API端点失败:', error)
      return createErrorResponse(ErrorCode.UNKNOWN_ERROR, '按标签获取API端点失败')
    }
  }

  /**
   * 生成OpenAPI规范
   * @returns OpenAPI 3.0规范对象或错误响应
   */
  generateOpenApiSpec(): any {
    try {
      const spec: any = {
        openapi: '3.0.0',
        info: {
          title: '医保基金监管平台 API',
          version: '1.0.0',
          description: '医保基金监管平台的统一API接口文档'
        },
        servers: [
          {
            url: '/api',
            description: 'API服务器'
          }
        ],
        paths: {},
        components: {
          schemas: {},
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT'
            }
          }
        }
      }

      // 生成路径文档
      const allEndpointsResponse = this.getAllEndpoints()
      const allEndpoints = allEndpointsResponse.success ? allEndpointsResponse.data : []

      for (const endpoint of allEndpoints) {
        try {
          const pathKey = endpoint.path
          const methodKey = endpoint.method.toLowerCase()

          if (!spec.paths[pathKey]) {
            spec.paths[pathKey] = {}
          }

          spec.paths[pathKey][methodKey] = this.generatePathItem(endpoint)
        } catch (error) {
          console.error(`生成端点文档失败: ${endpoint.path}`, error)
          // 继续处理其他端点
        }
      }

      return createSuccessResponse(spec, 'OpenAPI规范生成成功')
    } catch (error) {
      console.error('生成OpenAPI规范失败:', error)
      return createErrorResponse(ErrorCode.UNKNOWN_ERROR, '生成API文档失败')
    }
  }

  /**
   * 生成路径项文档
   */
  private generatePathItem(endpoint: ApiEndpoint): any {
    const pathItem: any = {
      summary: endpoint.summary,
      description: endpoint.description,
      tags: endpoint.tags,
      responses: {
        '200': {
          description: '成功响应',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  code: { type: 'string', example: 'SUCCESS' },
                  message: { type: 'string', example: '操作成功' },
                  data: endpoint.responseSchema ? this.zodToJsonSchema(endpoint.responseSchema) : {},
                  meta: {
                    type: 'object',
                    properties: {
                      timestamp: { type: 'string', format: 'date-time' },
                      version: { type: 'string', example: '1.0.0' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // 添加安全要求
    if (endpoint.requireAuth) {
      pathItem.security = [{ bearerAuth: [] }]
    }

    // 添加请求体
    if (endpoint.requestSchema && ['POST', 'PUT', 'PATCH'].includes(endpoint.method)) {
      pathItem.requestBody = {
        required: true,
        content: {
          'application/json': {
            schema: this.zodToJsonSchema(endpoint.requestSchema)
          }
        }
      }
    }

    // 添加查询参数
    if (endpoint.requestSchema && endpoint.method === 'GET') {
      pathItem.parameters = this.generateParameters(endpoint.requestSchema)
    }

    // 添加错误响应
    if (endpoint.errorCodes) {
      for (const errorCode of endpoint.errorCodes) {
        const statusCode = this.getHttpStatusFromErrorCode(errorCode)
        pathItem.responses[statusCode.toString()] = {
          description: '错误响应',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  code: { type: 'string', example: errorCode },
                  message: { type: 'string' },
                  meta: {
                    type: 'object',
                    properties: {
                      timestamp: { type: 'string', format: 'date-time' },
                      version: { type: 'string', example: '1.0.0' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    return pathItem
  }

  /**
   * 将Zod schema转换为JSON Schema
   */
  private zodToJsonSchema(schema: z.ZodSchema): any {
    // 简化的Zod到JSON Schema转换
    // 实际项目中可以使用 zod-to-json-schema 库
    return {
      type: 'object',
      description: 'Request/Response schema'
    }
  }

  /**
   * 生成查询参数
   */
  private generateParameters(schema: z.ZodSchema): any[] {
    // 简化实现，实际需要解析Zod schema
    return []
  }

  /**
   * 从错误码获取HTTP状态码
   */
  private getHttpStatusFromErrorCode(errorCode: ErrorCode): number {
    const statusMap: Record<string, number> = {
      'UNAUTHORIZED': 401,
      'FORBIDDEN': 403,
      'NOT_FOUND': 404,
      'VALIDATION_ERROR': 400,
      'UNKNOWN_ERROR': 500
    }
    return statusMap[errorCode] || 500
  }

  /**
   * 生成文档覆盖率报告
   * @returns 文档覆盖率报告或错误响应
   */
  generateCoverageReport(): {
    success: boolean
    code: string
    message: string
    data?: {
      totalEndpoints: number
      documentedEndpoints: number
      coveragePercentage: number
      missingDocumentation: string[]
    }
  } {
    try {
      const allEndpointsResponse = this.getAllEndpoints()
      const allEndpoints = allEndpointsResponse.success ? allEndpointsResponse.data : []
      const documentedEndpoints = allEndpoints.filter((endpoint: any) =>
        endpoint.description && endpoint.examples
      )

      const missingDocumentation = allEndpoints
        .filter((endpoint: any) => !endpoint.description || !endpoint.examples)
        .map((endpoint: any) => `${endpoint.method} ${endpoint.path}`)

      const reportData = {
        totalEndpoints: allEndpoints.length,
        documentedEndpoints: documentedEndpoints.length,
        coveragePercentage: allEndpoints.length > 0
          ? Math.round((documentedEndpoints.length / allEndpoints.length) * 100)
          : 0,
        missingDocumentation
      }

      return createSuccessResponse(reportData, '文档覆盖率报告生成成功')
    } catch (error) {
      console.error('生成文档覆盖率报告失败:', error)
      return createErrorResponse(ErrorCode.UNKNOWN_ERROR, '生成文档覆盖率报告失败')
    }
  }
}

// 全局文档注册表实例
export const apiDocRegistry = new ApiDocumentationRegistry()

/**
 * API文档装饰器
 * @param endpoint API端点信息
 * @returns 装饰器函数和标准响应格式
 */
export function ApiDoc(endpoint: Omit<ApiEndpoint, 'path' | 'method'>) {
  try {
    const decorator = function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      // 这里可以通过反射获取路径和方法信息
      // 简化实现，需要手动注册
      return descriptor
    }

    // 返回装饰器函数，但也提供响应格式信息
    Object.assign(decorator, createSuccessResponse(
      { decorator: 'ApiDoc', endpoint },
      'API文档装饰器创建成功'
    ))

    return decorator
  } catch (error) {
    console.error('创建API文档装饰器失败:', error)
    const errorDecorator = function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      return descriptor
    }
    Object.assign(errorDecorator, createErrorResponse(ErrorCode.UNKNOWN_ERROR, '创建API文档装饰器失败'))
    return errorDecorator
  }
}

/**
 * 注册API端点文档
 * @param endpoint API端点信息
 * @returns 标准API响应格式
 */
export function registerApiEndpoint(endpoint: ApiEndpoint): any {
  try {
    apiDocRegistry.register(endpoint)
    return createSuccessResponse(
      { endpoint: endpoint.path },
      `API端点 ${endpoint.method} ${endpoint.path} 注册成功`
    )
  } catch (error) {
    console.error('注册API端点失败:', error)
    return createErrorResponse(ErrorCode.UNKNOWN_ERROR, '注册API端点失败')
  }
}

/**
 * 医保平台API端点注册
 * @returns 标准API响应格式
 */
export function registerMedicalPlatformApis(): any {
  try {
  // 用户认证相关API
  registerApiEndpoint({
    path: '/api/auth/login',
    method: 'POST',
    summary: '用户登录',
    description: '用户通过用户名和密码进行登录认证',
    tags: ['认证'],
    requireAuth: false,
    requestSchema: z.object({
      username: z.string().min(1, '用户名不能为空'),
      password: z.string().min(6, '密码至少6位')
    }),
    responseSchema: z.object({
      token: z.string(),
      user: z.object({
        id: z.number(),
        username: z.string(),
        roles: z.array(z.string())
      })
    }),
    examples: {
      request: {
        username: 'admin',
        password: 'password123'
      },
      response: {
        success: true,
        code: 'SUCCESS',
        message: '登录成功',
        data: {
          token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          user: {
            id: 1,
            username: 'admin',
            roles: ['ADMIN']
          }
        }
      }
    },
    errorCodes: [ErrorCode.UNAUTHORIZED, ErrorCode.VALIDATION_ERROR]
  })

  // 医疗案例相关API
  registerApiEndpoint({
    path: '/api/medical-cases',
    method: 'GET',
    summary: '获取医疗案例列表',
    description: '分页获取医疗案例列表，支持筛选和搜索',
    tags: ['医疗案例'],
    requireAuth: true,
    requiredRoles: ['USER', 'ADMIN'],
    requestSchema: z.object({
      page: z.string().optional(),
      pageSize: z.string().optional(),
      search: z.string().optional(),
      category: z.string().optional()
    }),
    responseSchema: z.array(z.object({
      id: z.number(),
      patientName: z.string(),
      caseNumber: z.string(),
      category: z.string(),
      status: z.string(),
      createdAt: z.string()
    })),
    examples: {
      request: {
        page: '1',
        pageSize: '20',
        search: '张三',
        category: '住院'
      },
      response: {
        success: true,
        code: 'SUCCESS',
        message: '获取医疗案例列表成功',
        data: [
          {
            id: 1,
            patientName: '张***',
            caseNumber: 'MC2024001',
            category: '住院',
            status: '已审核',
            createdAt: '2024-01-01T00:00:00Z'
          }
        ],
        meta: {
          pagination: {
            page: 1,
            pageSize: 20,
            total: 100,
            totalPages: 5
          }
        }
      }
    },
    errorCodes: [ErrorCode.UNAUTHORIZED, ErrorCode.FORBIDDEN]
  })

  // 监管规则相关API
  registerApiEndpoint({
    path: '/api/supervision-rules',
    method: 'POST',
    summary: '创建监管规则',
    description: '创建新的医保基金监管规则',
    tags: ['监管规则'],
    requireAuth: true,
    requiredRoles: ['ADMIN'],
    requestSchema: z.object({
      name: z.string().min(1, '规则名称不能为空'),
      code: z.string().min(1, '规则编码不能为空'),
      description: z.string().optional(),
      conditions: z.array(z.object({
        field: z.string(),
        operator: z.string(),
        value: z.any()
      })),
      actions: z.array(z.object({
        type: z.string(),
        config: z.any()
      }))
    }),
    responseSchema: z.object({
      id: z.number(),
      name: z.string(),
      code: z.string(),
      status: z.string()
    }),
    examples: {
      request: {
        name: '异常费用检测规则',
        code: 'ABNORMAL_COST_001',
        description: '检测异常高额医疗费用',
        conditions: [
          {
            field: 'totalCost',
            operator: '>',
            value: 10000
          }
        ],
        actions: [
          {
            type: 'ALERT',
            config: { level: 'HIGH' }
          }
        ]
      },
      response: {
        success: true,
        code: 'SUCCESS',
        message: '监管规则创建成功',
        data: {
          id: 1,
          name: '异常费用检测规则',
          code: 'ABNORMAL_COST_001',
          status: 'ACTIVE'
        }
      }
    },
    errorCodes: [ErrorCode.UNAUTHORIZED, ErrorCode.FORBIDDEN, ErrorCode.VALIDATION_ERROR]
  })

  return createSuccessResponse(
    { registeredEndpoints: 3 },
    '医保平台API端点注册成功'
  )
  } catch (error) {
    console.error('注册医保平台API端点失败:', error)
    return createErrorResponse(ErrorCode.UNKNOWN_ERROR, '注册医保平台API端点失败')
  }
}

// 初始化时注册所有API端点
registerMedicalPlatformApis()
