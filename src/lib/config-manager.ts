/**
 * 企业级配置管理系统
 */

import { z } from 'zod'
import { log } from './monitoring'
import { cache<PERSON>anager, CACHE_CONFIGS } from './cache-manager'

// 配置验证模式
const configSchema = z.object({
  // 应用配置
  app: z.object({
    name: z.string().default('医保基金监管平台'),
    version: z.string().default('2.0.0'),
    environment: z.enum(['development', 'staging', 'production']).default('development'),
    debug: z.boolean().default(false),
    maintenance: z.boolean().default(false)
  }),

  // 数据库配置
  database: z.object({
    host: z.string(),
    port: z.number().min(1).max(65535).default(1521),
    serviceName: z.string(),
    username: z.string(),
    password: z.string(),
    poolMin: z.number().min(0).default(2),
    poolMax: z.number().min(1).default(10),
    poolIncrement: z.number().min(1).default(1),
    connectionTimeout: z.number().min(1000).default(60000),
    idleTimeout: z.number().min(1000).default(300000)
  }),

  // 安全配置
  security: z.object({
    jwtSecret: z.string().min(32),
    jwtExpiresIn: z.string().default('24h'),
    bcryptRounds: z.number().min(10).max(15).default(12),
    sessionTimeout: z.number().min(300).default(3600), // 秒
    maxLoginAttempts: z.number().min(3).max(10).default(5),
    lockoutDuration: z.number().min(300).default(900), // 秒
    passwordMinLength: z.number().min(8).default(8),
    passwordRequireSpecialChars: z.boolean().default(true),
    enableTwoFactor: z.boolean().default(false)
  }),

  // 限流配置
  rateLimit: z.object({
    enabled: z.boolean().default(true),
    windowMs: z.number().min(1000).default(900000), // 15分钟
    maxRequests: z.number().min(1).default(100),
    skipSuccessfulRequests: z.boolean().default(false),
    skipFailedRequests: z.boolean().default(false)
  }),

  // 缓存配置
  cache: z.object({
    enabled: z.boolean().default(true),
    defaultTtl: z.number().min(60).default(3600), // 秒
    maxSize: z.number().min(100).default(1000),
    strategy: z.enum(['LRU', 'LFU', 'FIFO']).default('LRU')
  }),

  // 日志配置
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error', 'fatal']).default('info'),
    enableConsole: z.boolean().default(true),
    enableFile: z.boolean().default(false),
    maxFileSize: z.number().min(1024).default(10485760), // 10MB
    maxFiles: z.number().min(1).default(5),
    enableStructured: z.boolean().default(true)
  }),

  // 监控配置
  monitoring: z.object({
    enabled: z.boolean().default(true),
    metricsInterval: z.number().min(1000).default(60000), // 1分钟
    healthCheckInterval: z.number().min(5000).default(30000), // 30秒
    enablePerformanceTracking: z.boolean().default(true),
    enableErrorTracking: z.boolean().default(true)
  }),

  // 邮件配置
  email: z.object({
    enabled: z.boolean().default(false),
    host: z.string().optional(),
    port: z.number().min(1).max(65535).default(587),
    secure: z.boolean().default(false),
    username: z.string().optional(),
    password: z.string().optional(),
    from: z.string().email().optional()
  }),

  // 文件上传配置
  upload: z.object({
    enabled: z.boolean().default(true),
    maxFileSize: z.number().min(1024).default(10485760), // 10MB
    allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'application/pdf']),
    uploadPath: z.string().default('./uploads'),
    enableVirusScan: z.boolean().default(false)
  }),

  // 业务配置
  business: z.object({
    maxCaseAmount: z.number().min(0).default(1000000), // 最大案例金额
    auditThreshold: z.number().min(0).default(50000), // 审核阈值
    autoApprovalLimit: z.number().min(0).default(10000), // 自动审批限额
    reportRetentionDays: z.number().min(30).default(2555), // 7年
    enableAutoBackup: z.boolean().default(true),
    backupInterval: z.number().min(3600).default(86400) // 24小时
  })
})

export type AppConfig = z.infer<typeof configSchema>

/**
 * 配置管理器类
 */
class ConfigManager {
  private config: AppConfig | null = null
  private configCache = new Map<string, any>()
  private watchers = new Map<string, Array<(value: any) => void>>()

  /**
   * 初始化配置
   */
  async initialize(): Promise<void> {
    try {
      // 从环境变量加载配置
      const envConfig = this.loadFromEnvironment()
      
      // 从配置文件加载（如果存在）
      const fileConfig = await this.loadFromFile()
      
      // 从数据库加载（如果存在）
      const dbConfig = await this.loadFromDatabase()

      // 合并配置（优先级：数据库 > 文件 > 环境变量 > 默认值）
      const mergedConfig = {
        ...envConfig,
        ...fileConfig,
        ...dbConfig
      }

      // 验证配置
      const validationResult = configSchema.safeParse(mergedConfig)
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ')
        throw new Error(`配置验证失败: ${errors}`)
      }

      this.config = validationResult.data

      // 缓存配置
      await this.cacheConfig()

      log({
        level: 'info',
        message: 'Configuration initialized successfully',
        context: {
          environment: this.config.app.environment,
          version: this.config.app.version,
          debug: this.config.app.debug
        }
      })

    } catch (error) {
      log({
        level: 'error',
        message: 'Failed to initialize configuration',
        context: { error: (error as Error).message }
      })
      throw error
    }
  }

  /**
   * 获取配置值
   */
  get<T = any>(path: string, defaultValue?: T): T {
    if (!this.config) {
      throw new Error('配置未初始化，请先调用 initialize()')
    }

    // 检查缓存
    if (this.configCache.has(path)) {
      return this.configCache.get(path)
    }

    // 解析路径
    const keys = path.split('.')
    let value: any = this.config

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        value = defaultValue
        break
      }
    }

    // 缓存结果
    this.configCache.set(path, value)

    return value
  }

  /**
   * 设置配置值（运行时）
   */
  async set(path: string, value: any): Promise<void> {
    if (!this.config) {
      throw new Error('配置未初始化')
    }

    // 更新内存配置
    const keys = path.split('.')
    let current: any = this.config

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!key) continue
      if (!(key in current)) {
        current[key] = {}
      }
      current = current[key]
    }

    const lastKey = keys[keys.length - 1]
    if (lastKey) {
      current[lastKey] = value
    }

    // 更新缓存
    this.configCache.set(path, value)

    // 持久化到数据库
    await this.saveToDatabase(path, value)

    // 通知观察者
    this.notifyWatchers(path, value)

    log({
      level: 'info',
      message: 'Configuration updated',
      context: { path, value: typeof value === 'object' ? '[Object]' : value }
    })
  }

  /**
   * 监听配置变化
   */
  watch(path: string, callback: (value: any) => void): () => void {
    if (!this.watchers.has(path)) {
      this.watchers.set(path, [])
    }
    
    this.watchers.get(path)!.push(callback)

    // 返回取消监听的函数
    return () => {
      const callbacks = this.watchers.get(path)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  /**
   * 获取所有配置
   */
  getAll(): AppConfig {
    if (!this.config) {
      throw new Error('配置未初始化')
    }
    return { ...this.config }
  }

  /**
   * 重新加载配置
   */
  async reload(): Promise<void> {
    this.configCache.clear()
    await this.initialize()
  }

  /**
   * 验证配置
   */
  validate(): { valid: boolean; errors: string[] } {
    if (!this.config) {
      return { valid: false, errors: ['配置未初始化'] }
    }

    const result = configSchema.safeParse(this.config)
    if (result.success) {
      return { valid: true, errors: [] }
    }

    const errors = result.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    )

    return { valid: false, errors }
  }

  private loadFromEnvironment(): Partial<AppConfig> {
    return {
      app: {
        name: process.env['APP_NAME'] || 'MediInspect',
        version: process.env['APP_VERSION'] || '2.0.0',
        environment: process.env['NODE_ENV'] as any || 'development',
        debug: process.env['DEBUG'] === 'true',
        maintenance: process.env['MAINTENANCE_MODE'] === 'true'
      },
      database: {
        host: process.env['DB_HOST']!,
        port: parseInt(process.env['DB_PORT'] || '1521'),
        serviceName: process.env['DB_SERVICE_NAME']!,
        username: process.env['DB_USERNAME']!,
        password: process.env['DB_PASSWORD']!,
        poolMin: parseInt(process.env['DB_POOL_MIN'] || '2'),
        poolMax: parseInt(process.env['DB_POOL_MAX'] || '10'),
        poolIncrement: parseInt(process.env['DB_POOL_INCREMENT'] || '1'),
        connectionTimeout: parseInt(process.env['DB_CONNECTION_TIMEOUT'] || '60000'),
        idleTimeout: parseInt(process.env['DB_IDLE_TIMEOUT'] || '300000')
      },
      security: {
        jwtSecret: process.env['JWT_SECRET']!,
        jwtExpiresIn: process.env['JWT_EXPIRES_IN'] || '24h',
        bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12'),
        sessionTimeout: parseInt(process.env['SESSION_TIMEOUT'] || '3600'),
        maxLoginAttempts: parseInt(process.env['MAX_LOGIN_ATTEMPTS'] || '5'),
        lockoutDuration: parseInt(process.env['LOCKOUT_DURATION'] || '900'),
        passwordMinLength: parseInt(process.env['PASSWORD_MIN_LENGTH'] || '8'),
        passwordRequireSpecialChars: process.env['PASSWORD_REQUIRE_SPECIAL_CHARS'] === 'true',
        enableTwoFactor: process.env['ENABLE_TWO_FACTOR'] === 'true'
      }
    }
  }

  private async loadFromFile(): Promise<Partial<AppConfig>> {
    // 实现从配置文件加载
    return {}
  }

  private async loadFromDatabase(): Promise<Partial<AppConfig>> {
    // 实现从数据库加载
    return {}
  }

  private async cacheConfig(): Promise<void> {
    if (this.config) {
      await cacheManager.set('app_config', this.config, CACHE_CONFIGS.LONG)
    }
  }

  private async saveToDatabase(path: string, value: any): Promise<void> {
    // 实现保存到数据库
  }

  private notifyWatchers(path: string, value: any): void {
    const callbacks = this.watchers.get(path)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(value)
        } catch (error) {
          log({
            level: 'error',
            message: 'Error in config watcher callback',
            context: { path, error: (error as Error).message }
          })
        }
      })
    }
  }
}

// 单例实例
export const configManager = new ConfigManager()

// 便捷方法
export const getConfig = <T = any>(path: string, defaultValue?: T): T => 
  configManager.get(path, defaultValue)

export const setConfig = (path: string, value: any): Promise<void> => 
  configManager.set(path, value)

export const watchConfig = (path: string, callback: (value: any) => void): (() => void) => 
  configManager.watch(path, callback)

export default configManager
