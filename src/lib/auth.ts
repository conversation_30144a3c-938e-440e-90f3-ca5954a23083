import { NextRequest } from 'next/server'
import { verifyAccessToken } from '@/lib/jwt'
import { getUserById } from '@/lib/user-service'

export interface AuthUser {
  id: number
  username: string
  realName: string
  email: string
  roles: Array<{
    id: number
    roleCode: string
    roleName: string
  }>
}

export interface AuthResult {
  success: boolean
  user?: AuthUser
  error?: string
}

/**
 * 验证请求中的认证信息
 */
export async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  try {
    // 从 Authorization header 获取 token
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid authorization header'
      }
    }

    const token = authHeader.substring(7) // 移除 "Bearer " 前缀

    // 验证 token
    const payload = verifyAccessToken(token)
    if (!payload || !payload.userId) {
      return {
        success: false,
        error: 'Invalid token'
      }
    }

    // 获取用户信息
    const user = await getUserById(payload.userId)
    if (!user) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        realName: user.realName,
        email: user.email,
        roles: user.roles
      }
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed'
    }
  }
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(user: AuthUser, roleCode: string): boolean {
  return user.roles.some(role => role.roleCode === roleCode)
}

/**
 * 检查用户是否有任一指定角色
 */
export function hasAnyRole(user: AuthUser, roleCodes: string[]): boolean {
  return user.roles.some(role => roleCodes.includes(role.roleCode))
}

/**
 * 检查用户是否有所有指定角色
 */
export function hasAllRoles(user: AuthUser, roleCodes: string[]): boolean {
  return roleCodes.every(roleCode => hasRole(user, roleCode))
}

/**
 * 权限检查装饰器
 */
export function requireAuth(requiredRoles?: string[]) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (request: NextRequest, ...args: any[]) {
      const authResult = await authenticateRequest(request)
      
      if (!authResult.success) {
        return Response.json(
          { error: authResult.error },
          { status: 401 }
        )
      }

      if (requiredRoles && requiredRoles.length > 0) {
        if (!hasAnyRole(authResult.user!, requiredRoles)) {
          return Response.json(
            { error: 'Insufficient permissions' },
            { status: 403 }
          )
        }
      }

      // 将用户信息添加到请求中
      ;(request as any).user = authResult.user

      return originalMethod.call(this, request, ...args)
    }

    return descriptor
  }
}

/**
 * 从请求中获取用户信息（需要先调用 authenticateRequest）
 */
export function getUserFromRequest(request: NextRequest): AuthUser | null {
  return (request as any).user || null
}

/**
 * 验证Token的别名函数，为了兼容现有代码
 */
export const verifyToken = verifyAccessToken
