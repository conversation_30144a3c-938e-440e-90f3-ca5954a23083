/**
 * 系统配置管理服务
 * 实现动态配置管理，解决硬编码约束问题
 */

import { executeQuery } from '@/lib/database'

export interface SystemConfigCategory {
  id: number
  categoryCode: string
  categoryName: string
  description: string
  sortOrder: number
  isActive: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
  createdBy: number
  updatedBy: number
}

export interface SystemConfigItem {
  id: number
  categoryId: number
  configKey: string
  configValue: string
  configLabel: string
  description: string
  dataType: string
  isRequired: boolean
  sortOrder: number
  isActive: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
  createdBy: number
  updatedBy: number
  // 关联数据
  categoryCode?: string
  categoryName?: string
}

export interface SystemConfigHistory {
  id: number
  configItemId: number
  oldValue: string
  newValue: string
  changeReason: string
  changeType: string
  createdAt: Date
  createdBy: number
  // 关联数据
  configKey?: string
  configLabel?: string
  createdByName?: string
}

export interface ConfigQueryParams {
  page?: number
  pageSize?: number
  categoryCode?: string
  search?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 获取配置分类列表
 */
export async function getConfigCategories(params: ConfigQueryParams = {}) {
  const {
    page = 1,
    pageSize = 20,
    search,
    isActive,
    sortBy = 'SORT_ORDER',
    sortOrder = 'ASC'
  } = params

  const offset = (page - 1) * pageSize
  const conditions: string[] = ['IS_DELETED = 0']
  const queryParams: any = { offset, pageSize }

  // 构建查询条件
  if (isActive !== undefined) {
    conditions.push('IS_ACTIVE = :isActive')
    queryParams.isActive = isActive ? 1 : 0
  }

  if (search) {
    conditions.push('(UPPER(CATEGORY_NAME) LIKE UPPER(:search) OR UPPER(CATEGORY_CODE) LIKE UPPER(:searchCode) OR UPPER(DESCRIPTION) LIKE UPPER(:searchDesc))')
    queryParams.search = `%${search}%`
    queryParams.searchCode = `%${search}%`
    queryParams.searchDesc = `%${search}%`
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM SYSTEM_CONFIG_CATEGORY
    ${whereClause}
  `

  // 为计数查询创建单独的参数（不包含分页参数）
  const countParams: any = {}
  if (isActive !== undefined) {
    countParams.isActive = isActive ? 1 : 0
  }
  if (search) {
    countParams.search = `%${search}%`
    countParams.searchCode = `%${search}%`
    countParams.searchDesc = `%${search}%`
  }

  const countResult = await executeQuery(countSql, countParams)
  const total = countResult.rows[0]?.TOTAL || 0

  // 查询数据
  const dataSql = `
    SELECT 
      ID, CATEGORY_CODE, CATEGORY_NAME, DESCRIPTION, SORT_ORDER,
      IS_ACTIVE, IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
    FROM SYSTEM_CONFIG_CATEGORY
    ${whereClause}
    ORDER BY ${sortBy} ${sortOrder}
    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
  `

  const dataResult = await executeQuery(dataSql, queryParams)

  const items = dataResult.rows.map(row => ({
    id: row.ID,
    categoryCode: row.CATEGORY_CODE,
    categoryName: row.CATEGORY_NAME,
    description: row.DESCRIPTION,
    sortOrder: row.SORT_ORDER,
    isActive: row.IS_ACTIVE === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    createdBy: row.CREATED_BY,
    updatedBy: row.UPDATED_BY
  }))

  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 获取配置项列表
 */
export async function getConfigItems(params: ConfigQueryParams = {}) {
  const {
    page = 1,
    pageSize = 20,
    categoryCode,
    search,
    isActive,
    sortBy = 'SORT_ORDER',
    sortOrder = 'ASC'
  } = params

  const offset = (page - 1) * pageSize
  const conditions: string[] = ['sci.IS_DELETED = 0']
  const queryParams: any = { offset, pageSize }

  // 构建查询条件
  if (categoryCode) {
    conditions.push('scc.CATEGORY_CODE = :categoryCode')
    queryParams.categoryCode = categoryCode
  }

  if (isActive !== undefined) {
    conditions.push('sci.IS_ACTIVE = :isActive')
    queryParams.isActive = isActive ? 1 : 0
  }

  if (search) {
    conditions.push('(UPPER(sci.CONFIG_LABEL) LIKE UPPER(:search) OR UPPER(sci.CONFIG_KEY) LIKE UPPER(:searchKey) OR UPPER(sci.DESCRIPTION) LIKE UPPER(:searchDesc))')
    queryParams.search = `%${search}%`
    queryParams.searchKey = `%${search}%`
    queryParams.searchDesc = `%${search}%`
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM SYSTEM_CONFIG_ITEM sci
    LEFT JOIN SYSTEM_CONFIG_CATEGORY scc ON sci.CATEGORY_ID = scc.ID
    ${whereClause}
  `

  // 为计数查询创建单独的参数（不包含分页参数）
  const countParams: any = {}
  if (categoryCode) {
    countParams.categoryCode = categoryCode
  }
  if (isActive !== undefined) {
    countParams.isActive = isActive ? 1 : 0
  }
  if (search) {
    countParams.search = `%${search}%`
    countParams.searchKey = `%${search}%`
    countParams.searchDesc = `%${search}%`
  }

  const countResult = await executeQuery(countSql, countParams)
  const total = countResult.rows[0]?.TOTAL || 0

  // 查询数据
  const dataSql = `
    SELECT 
      sci.ID, sci.CATEGORY_ID, sci.CONFIG_KEY, sci.CONFIG_VALUE, sci.CONFIG_LABEL,
      sci.DESCRIPTION, sci.DATA_TYPE, sci.IS_REQUIRED, sci.SORT_ORDER,
      sci.IS_ACTIVE, sci.IS_DELETED, sci.CREATED_AT, sci.UPDATED_AT,
      sci.CREATED_BY, sci.UPDATED_BY,
      scc.CATEGORY_CODE, scc.CATEGORY_NAME
    FROM SYSTEM_CONFIG_ITEM sci
    LEFT JOIN SYSTEM_CONFIG_CATEGORY scc ON sci.CATEGORY_ID = scc.ID
    ${whereClause}
    ORDER BY sci.${sortBy} ${sortOrder}
    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
  `

  const dataResult = await executeQuery(dataSql, queryParams)

  const items = dataResult.rows.map(row => ({
    id: row.ID,
    categoryId: row.CATEGORY_ID,
    configKey: row.CONFIG_KEY,
    configValue: row.CONFIG_VALUE,
    configLabel: row.CONFIG_LABEL,
    description: row.DESCRIPTION,
    dataType: row.DATA_TYPE,
    isRequired: row.IS_REQUIRED === 1,
    sortOrder: row.SORT_ORDER,
    isActive: row.IS_ACTIVE === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    createdBy: row.CREATED_BY,
    updatedBy: row.UPDATED_BY,
    categoryCode: row.CATEGORY_CODE,
    categoryName: row.CATEGORY_NAME
  }))

  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 根据分类代码获取配置项（用于动态验证）
 */
export async function getConfigItemsByCategory(categoryCode: string): Promise<SystemConfigItem[]> {
  const sql = `
    SELECT 
      sci.ID, sci.CATEGORY_ID, sci.CONFIG_KEY, sci.CONFIG_VALUE, sci.CONFIG_LABEL,
      sci.DESCRIPTION, sci.DATA_TYPE, sci.IS_REQUIRED, sci.SORT_ORDER,
      sci.IS_ACTIVE, sci.IS_DELETED, sci.CREATED_AT, sci.UPDATED_AT,
      sci.CREATED_BY, sci.UPDATED_BY,
      scc.CATEGORY_CODE, scc.CATEGORY_NAME
    FROM SYSTEM_CONFIG_ITEM sci
    INNER JOIN SYSTEM_CONFIG_CATEGORY scc ON sci.CATEGORY_ID = scc.ID
    WHERE scc.CATEGORY_CODE = :categoryCode 
      AND sci.IS_ACTIVE = 1 
      AND sci.IS_DELETED = 0
      AND scc.IS_ACTIVE = 1 
      AND scc.IS_DELETED = 0
    ORDER BY sci.SORT_ORDER ASC
  `

  const result = await executeQuery(sql, { categoryCode })

  return result.rows.map(row => ({
    id: row.ID,
    categoryId: row.CATEGORY_ID,
    configKey: row.CONFIG_KEY,
    configValue: row.CONFIG_VALUE,
    configLabel: row.CONFIG_LABEL,
    description: row.DESCRIPTION,
    dataType: row.DATA_TYPE,
    isRequired: row.IS_REQUIRED === 1,
    sortOrder: row.SORT_ORDER,
    isActive: row.IS_ACTIVE === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    createdBy: row.CREATED_BY,
    updatedBy: row.UPDATED_BY,
    categoryCode: row.CATEGORY_CODE,
    categoryName: row.CATEGORY_NAME
  }))
}

/**
 * 获取所有有效的配置项（用于缓存）
 */
export async function getAllActiveConfigItems(): Promise<Record<string, string[]>> {
  const sql = `
    SELECT 
      scc.CATEGORY_CODE,
      sci.CONFIG_VALUE
    FROM SYSTEM_CONFIG_ITEM sci
    INNER JOIN SYSTEM_CONFIG_CATEGORY scc ON sci.CATEGORY_ID = scc.ID
    WHERE sci.IS_ACTIVE = 1 
      AND sci.IS_DELETED = 0
      AND scc.IS_ACTIVE = 1 
      AND scc.IS_DELETED = 0
    ORDER BY scc.CATEGORY_CODE, sci.SORT_ORDER
  `

  const result = await executeQuery(sql, {})
  
  const configMap: Record<string, string[]> = {}
  
  result.rows.forEach(row => {
    const categoryCode = row.CATEGORY_CODE
    const configValue = row.CONFIG_VALUE
    
    if (!configMap[categoryCode]) {
      configMap[categoryCode] = []
    }
    
    configMap[categoryCode].push(configValue)
  })

  return configMap
}

/**
 * 创建配置项
 */
export async function createConfigItem(data: {
  categoryId: number
  configKey: string
  configValue: string
  configLabel: string
  description?: string
  dataType?: string
  isRequired?: boolean
  sortOrder?: number
}, userId: number): Promise<number> {
  // 先获取下一个ID
  const getIdSql = 'SELECT SEQ_SYSTEM_CONFIG_ITEM.NEXTVAL as NEXT_ID FROM DUAL'
  const idResult = await executeQuery(getIdSql, {})
  const itemId = idResult.rows[0].NEXT_ID

  const sql = `
    INSERT INTO SYSTEM_CONFIG_ITEM (
      ID, CATEGORY_ID, CONFIG_KEY, CONFIG_VALUE, CONFIG_LABEL,
      DESCRIPTION, DATA_TYPE, IS_REQUIRED, SORT_ORDER,
      IS_ACTIVE, IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY
    ) VALUES (
      :itemId, :categoryId, :configKey, :configValue, :configLabel,
      :description, :dataType, :isRequired, :sortOrder,
      1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, :userId
    )
  `

  await executeQuery(sql, {
    itemId,
    categoryId: data.categoryId,
    configKey: data.configKey,
    configValue: data.configValue,
    configLabel: data.configLabel,
    description: data.description || '',
    dataType: data.dataType || 'STRING',
    isRequired: data.isRequired ? 1 : 0,
    sortOrder: data.sortOrder || 0,
    userId
  })

  // 记录变更历史
  await recordConfigHistory(itemId, null, data.configValue, '创建配置项', 'CREATE', userId)

  return itemId
}

/**
 * 更新配置项
 */
export async function updateConfigItem(
  id: number,
  data: {
    configValue?: string
    configLabel?: string
    description?: string
    dataType?: string
    isRequired?: boolean
    sortOrder?: number
    isActive?: boolean
  },
  userId: number,
  changeReason?: string
): Promise<void> {
  // 获取当前配置项信息
  const currentItem = await getConfigItemById(id)
  if (!currentItem) {
    throw new Error('配置项不存在')
  }

  const updateFields: string[] = []
  const params: any = { id, userId }

  if (data.configValue !== undefined) {
    updateFields.push('CONFIG_VALUE = :configValue')
    params.configValue = data.configValue
  }

  if (data.configLabel !== undefined) {
    updateFields.push('CONFIG_LABEL = :configLabel')
    params.configLabel = data.configLabel
  }

  if (data.description !== undefined) {
    updateFields.push('DESCRIPTION = :description')
    params.description = data.description
  }

  if (data.dataType !== undefined) {
    updateFields.push('DATA_TYPE = :dataType')
    params.dataType = data.dataType
  }

  if (data.isRequired !== undefined) {
    updateFields.push('IS_REQUIRED = :isRequired')
    params.isRequired = data.isRequired ? 1 : 0
  }

  if (data.sortOrder !== undefined) {
    updateFields.push('SORT_ORDER = :sortOrder')
    params.sortOrder = data.sortOrder
  }

  if (data.isActive !== undefined) {
    updateFields.push('IS_ACTIVE = :isActive')
    params.isActive = data.isActive ? 1 : 0
  }

  if (updateFields.length === 0) {
    return
  }

  updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP', 'UPDATED_BY = :userId')

  const sql = `
    UPDATE SYSTEM_CONFIG_ITEM
    SET ${updateFields.join(', ')}
    WHERE ID = :id AND IS_DELETED = 0
  `

  await executeQuery(sql, params)

  // 记录变更历史
  if (data.configValue && data.configValue !== currentItem.configValue) {
    await recordConfigHistory(
      id,
      currentItem.configValue,
      data.configValue,
      changeReason || '更新配置项',
      'UPDATE',
      userId
    )
  }
}

/**
 * 删除配置项（软删除）
 */
export async function deleteConfigItem(id: number, userId: number, changeReason?: string): Promise<void> {
  // 获取当前配置项信息
  const currentItem = await getConfigItemById(id)
  if (!currentItem) {
    throw new Error('配置项不存在')
  }

  const sql = `
    UPDATE SYSTEM_CONFIG_ITEM
    SET IS_DELETED = 1, UPDATED_BY = :userId, UPDATED_AT = CURRENT_TIMESTAMP
    WHERE ID = :id AND IS_DELETED = 0
  `

  await executeQuery(sql, { id, userId })

  // 记录变更历史
  await recordConfigHistory(
    id,
    currentItem.configValue,
    '[DELETED]', // 使用特殊标记而不是NULL
    changeReason || '删除配置项',
    'DELETE',
    userId
  )
}

/**
 * 根据ID获取配置项
 */
export async function getConfigItemById(id: number): Promise<SystemConfigItem | null> {
  const sql = `
    SELECT
      sci.ID, sci.CATEGORY_ID, sci.CONFIG_KEY, sci.CONFIG_VALUE, sci.CONFIG_LABEL,
      sci.DESCRIPTION, sci.DATA_TYPE, sci.IS_REQUIRED, sci.SORT_ORDER,
      sci.IS_ACTIVE, sci.IS_DELETED, sci.CREATED_AT, sci.UPDATED_AT,
      sci.CREATED_BY, sci.UPDATED_BY,
      scc.CATEGORY_CODE, scc.CATEGORY_NAME
    FROM SYSTEM_CONFIG_ITEM sci
    LEFT JOIN SYSTEM_CONFIG_CATEGORY scc ON sci.CATEGORY_ID = scc.ID
    WHERE sci.ID = :id AND sci.IS_DELETED = 0
  `

  const result = await executeQuery(sql, { id })

  if (result.rows.length === 0) {
    return null
  }

  const row = result.rows[0]
  return {
    id: row.ID,
    categoryId: row.CATEGORY_ID,
    configKey: row.CONFIG_KEY,
    configValue: row.CONFIG_VALUE,
    configLabel: row.CONFIG_LABEL,
    description: row.DESCRIPTION,
    dataType: row.DATA_TYPE,
    isRequired: row.IS_REQUIRED === 1,
    sortOrder: row.SORT_ORDER,
    isActive: row.IS_ACTIVE === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    createdBy: row.CREATED_BY,
    updatedBy: row.UPDATED_BY,
    categoryCode: row.CATEGORY_CODE,
    categoryName: row.CATEGORY_NAME
  }
}

/**
 * 记录配置变更历史
 */
async function recordConfigHistory(
  configItemId: number,
  oldValue: string | null,
  newValue: string | null,
  changeReason: string,
  changeType: string,
  userId: number
): Promise<void> {
  // 先获取下一个ID
  const getIdSql = 'SELECT SEQ_SYSTEM_CONFIG_HISTORY.NEXTVAL as NEXT_ID FROM DUAL'
  const idResult = await executeQuery(getIdSql, {})
  const historyId = idResult.rows[0].NEXT_ID

  const sql = `
    INSERT INTO SYSTEM_CONFIG_HISTORY (
      ID, CONFIG_ITEM_ID, OLD_VALUE, NEW_VALUE, CHANGE_REASON,
      CHANGE_TYPE, CREATED_AT, CREATED_BY
    ) VALUES (
      :historyId, :configItemId, :oldValue, :newValue, :changeReason,
      :changeType, CURRENT_TIMESTAMP, :userId
    )
  `

  await executeQuery(sql, {
    historyId,
    configItemId,
    oldValue,
    newValue,
    changeReason,
    changeType,
    userId
  })
}

/**
 * 创建配置分类
 */
export async function createConfigCategory(data: {
  categoryCode: string
  categoryName: string
  description?: string
  sortOrder?: number
}, userId: number): Promise<number> {
  // 先获取下一个ID
  const getIdSql = 'SELECT SEQ_SYSTEM_CONFIG_CATEGORY.NEXTVAL as NEXT_ID FROM DUAL'
  const idResult = await executeQuery(getIdSql, {})
  const categoryId = idResult.rows[0].NEXT_ID

  const sql = `
    INSERT INTO SYSTEM_CONFIG_CATEGORY (
      ID, CATEGORY_CODE, CATEGORY_NAME, DESCRIPTION, SORT_ORDER,
      IS_ACTIVE, IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY
    ) VALUES (
      :categoryId, :categoryCode, :categoryName, :description, :sortOrder,
      1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, :userId
    )
  `

  await executeQuery(sql, {
    categoryId,
    categoryCode: data.categoryCode,
    categoryName: data.categoryName,
    description: data.description || '',
    sortOrder: data.sortOrder || 0,
    userId
  })

  return categoryId
}

/**
 * 根据ID获取配置分类
 */
export async function getConfigCategoryById(id: number): Promise<SystemConfigCategory | null> {
  const sql = `
    SELECT
      ID, CATEGORY_CODE, CATEGORY_NAME, DESCRIPTION, SORT_ORDER,
      IS_ACTIVE, IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
    FROM SYSTEM_CONFIG_CATEGORY
    WHERE ID = :id AND IS_DELETED = 0
  `

  const result = await executeQuery(sql, { id })

  if (result.rows.length === 0) {
    return null
  }

  const row = result.rows[0]
  return {
    id: row.ID,
    categoryCode: row.CATEGORY_CODE,
    categoryName: row.CATEGORY_NAME,
    description: row.DESCRIPTION,
    sortOrder: row.SORT_ORDER,
    isActive: row.IS_ACTIVE === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    createdBy: row.CREATED_BY,
    updatedBy: row.UPDATED_BY
  }
}
