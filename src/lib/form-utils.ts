/**
 * 统一的表单工具库 - 减少重复的表单处理代码
 */

import { z } from 'zod'
import { UseFormReturn, FieldValues } from 'react-hook-form'

/**
 * 常用的验证 schema
 */
export const commonSchemas = {
  // 用户名验证
  username: z.string()
    .min(3, '用户名至少3位')
    .max(20, '用户名最多20位')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),

  // 密码验证
  password: z.string()
    .min(8, '密码至少8位')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字'),

  // 邮箱验证
  email: z.string()
    .email('请输入有效的邮箱地址')
    .max(100, '邮箱地址过长'),

  // 手机号验证
  phoneNumber: z.string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
    .optional()
    .or(z.literal('')),

  // 身份证号验证
  idCard: z.string()
    .regex(/^\d{17}[\dXx]$/, '请输入有效的身份证号')
    .optional()
    .or(z.literal('')),

  // 真实姓名验证
  realName: z.string()
    .min(2, '姓名至少2个字符')
    .max(10, '姓名最多10个字符')
    .regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '姓名格式不正确'),

  // 金额验证
  amount: z.number()
    .min(0, '金额不能为负数')
    .max(999999999.99, '金额过大'),

  // 年龄验证
  age: z.number()
    .int('年龄必须是整数')
    .min(0, '年龄不能为负数')
    .max(150, '年龄不能超过150岁'),

  // 描述文本验证
  description: z.string()
    .max(500, '描述不能超过500字符')
    .optional()
    .or(z.literal('')),

  // 状态枚举验证
  userStatus: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'LOCKED'] as const),
  caseStatus: z.enum(['pending', 'approved', 'rejected', 'under_review'] as const),
}

/**
 * 创建密码确认验证 schema
 */
export function createPasswordConfirmSchema<T extends Record<string, any>>(
  passwordField: keyof T = 'password' as keyof T,
  confirmField: keyof T = 'confirmPassword' as keyof T
) {
  return z.object({
    [passwordField]: commonSchemas.password,
    [confirmField]: z.string(),
  } as unknown as Record<keyof T, z.ZodTypeAny>).refine(
    (data) => data[passwordField] === data[confirmField],
    {
      message: "密码确认不匹配",
      path: [confirmField as string],
    }
  )
}

/**
 * 表单提交处理器包装器
 */
export function withFormSubmission<T>(
  handler: (data: T) => Promise<void>,
  options: {
    loadingState?: [boolean, (loading: boolean) => void]
    onSuccess?: () => void
    onError?: (error: Error) => void
    successMessage?: string
    errorMessage?: string
  } = {}
) {
  return async (data: T) => {
    const [isLoading, setIsLoading] = options.loadingState || [false, () => {}]
    
    try {
      setIsLoading(true)
      await handler(data)
      
      if (options.onSuccess) {
        options.onSuccess()
      }
      
      // 这里可以添加成功提示逻辑
      if (options.successMessage) {
        console.log(options.successMessage)
      }
    } catch (error) {
      console.error('Form submission error:', error)
      
      if (options.onError) {
        options.onError(error as Error)
      }
      
      // 这里可以添加错误提示逻辑
      if (options.errorMessage) {
        console.error(options.errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }
}

/**
 * 表单字段配置类型
 */
export interface FormFieldConfig {
  name: string
  label: string
  type?: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  options?: Array<{ value: string; label: string }>
  description?: string
}

/**
 * 通用表单验证函数
 */
export function validateFormData<T>(
  data: T,
  validators: Record<keyof T, (value: any) => string | null>
): { isValid: boolean; errors: Partial<Record<keyof T, string>> } {
  const errors: Partial<Record<keyof T, string>> = {}
  
  for (const [field, validator] of Object.entries(validators) as Array<[keyof T, (value: any) => string | null]>) {
    const error = validator(data[field])
    if (error) {
      errors[field] = error
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 常用的字段验证器
 */
export const fieldValidators = {
  required: (fieldName: string) => (value: any) => {
    if (value === undefined || value === null || value === '') {
      return `${fieldName}不能为空`
    }
    return null
  },

  minLength: (min: number, fieldName: string) => (value: string) => {
    if (value && value.length < min) {
      return `${fieldName}至少${min}个字符`
    }
    return null
  },

  maxLength: (max: number, fieldName: string) => (value: string) => {
    if (value && value.length > max) {
      return `${fieldName}最多${max}个字符`
    }
    return null
  },

  email: (value: string) => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return '邮箱格式不正确'
    }
    return null
  },

  phoneNumber: (value: string) => {
    if (value && !/^1[3-9]\d{9}$/.test(value)) {
      return '手机号格式不正确'
    }
    return null
  },

  idCard: (value: string) => {
    if (value && !/^\d{17}[\dXx]$/.test(value)) {
      return '身份证号格式不正确'
    }
    return null
  },

  numberRange: (min: number, max: number, fieldName: string) => (value: number) => {
    if (value !== undefined && value !== null) {
      if (value < min || value > max) {
        return `${fieldName}必须在${min}-${max}之间`
      }
    }
    return null
  },

  positiveNumber: (fieldName: string) => (value: number) => {
    if (value !== undefined && value !== null && value < 0) {
      return `${fieldName}不能为负数`
    }
    return null
  }
}

/**
 * 组合多个验证器
 */
export function combineValidators<T>(
  ...validators: Array<(value: T) => string | null>
) {
  return (value: T): string | null => {
    for (const validator of validators) {
      const error = validator(value)
      if (error) {
        return error
      }
    }
    return null
  }
}

/**
 * 表单重置工具
 */
export function resetFormWithDefaults<T extends FieldValues>(
  form: UseFormReturn<T>,
  defaults: Partial<T>
) {
  form.reset(defaults as T)
}

/**
 * 表单数据清理工具
 */
export function cleanFormData<T extends Record<string, any>>(
  data: T,
  options: {
    trimStrings?: boolean
    removeEmptyStrings?: boolean
    removeNullValues?: boolean
  } = {}
): T {
  const {
    trimStrings = true,
    removeEmptyStrings = true,
    removeNullValues = true
  } = options

  const cleaned = { ...data }

  for (const [key, value] of Object.entries(cleaned)) {
    if (typeof value === 'string') {
      if (trimStrings) {
        (cleaned as any)[key] = value.trim()
      }
      if (removeEmptyStrings && cleaned[key] === '') {
        delete cleaned[key]
      }
    } else if (value === null && removeNullValues) {
      delete cleaned[key]
    }
  }

  return cleaned
}

/**
 * 表单错误处理工具
 */
export function handleFormError(
  error: any,
  defaultMessage: string = '操作失败，请稍后重试'
): string {
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error?.message) {
    return error.message
  }
  
  return defaultMessage
}
