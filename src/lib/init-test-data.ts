/**
 * 初始化测试数据
 * 用于在数据库中插入监管规则的测试数据
 */

import { executeQuery } from '@/lib/database'

/**
 * 插入监管规则测试数据
 */
export async function insertSupervisionRuleTestData() {
  try {
    console.log('🔄 开始插入监管规则测试数据...')

    // 检查是否已有数据
    const checkSql = 'SELECT COUNT(*) as COUNT FROM RULE_SUPERVISION WHERE IS_DELETED = 0'
    const checkResult = await executeQuery(checkSql)
    const existingCount = checkResult.rows?.[0]?.COUNT || 0

    if (existingCount > 0) {
      console.log(`ℹ️ 数据库中已有 ${existingCount} 条规则数据，跳过插入`)
      return
    }

    // 插入测试规则数据
    const testRules = [
      {
        ruleName: '住院费用异常检测规则',
        ruleCode: 'INPATIENT_COST_ANOMALY_001',
        ruleType: 'ADVANCED',
        ruleCategory: 'COST_CONTROL',
        severityLevel: 'HIGH',
        description: '检测住院费用异常高的案例，识别可能的过度医疗或欺诈行为',
        ruleLogic: JSON.stringify({
          conditions: [
            { field: 'totalCost', operator: '>', value: 50000 },
            { field: 'stayDays', operator: '<', value: 7 }
          ],
          logic: 'AND'
        }),
        isActive: 1
      },
      {
        ruleName: '重复检查项目识别',
        ruleCode: 'DUPLICATE_EXAM_CHECK_002',
        ruleType: 'BASIC',
        ruleCategory: 'FRAUD_DETECTION',
        severityLevel: 'MEDIUM',
        description: '识别短期内重复进行相同检查项目的情况',
        ruleLogic: JSON.stringify({
          conditions: [
            { field: 'examType', operator: 'duplicate_within_days', value: 30 }
          ]
        }),
        isActive: 1
      },
      {
        ruleName: '药品使用合规性检查',
        ruleCode: 'DRUG_COMPLIANCE_003',
        ruleType: 'ADVANCED',
        ruleCategory: 'COMPLIANCE_CHECK',
        severityLevel: 'HIGH',
        description: '检查药品使用是否符合医保目录和用药规范',
        ruleLogic: JSON.stringify({
          conditions: [
            { field: 'drugCode', operator: 'not_in_catalog', value: 'medical_insurance_catalog' },
            { field: 'dosage', operator: '>', value: 'standard_dosage' }
          ],
          logic: 'OR'
        }),
        isActive: 1
      },
      {
        ruleName: '门诊频次异常监控',
        ruleCode: 'OUTPATIENT_FREQ_004',
        ruleType: 'BASIC',
        ruleCategory: 'STATISTICAL_ANALYSIS',
        severityLevel: 'MEDIUM',
        description: '监控患者门诊就诊频次异常情况',
        ruleLogic: JSON.stringify({
          conditions: [
            { field: 'visitCount', operator: '>', value: 20 },
            { field: 'timeRange', operator: '=', value: 'monthly' }
          ],
          logic: 'AND'
        }),
        isActive: 1
      },
      {
        ruleName: '医疗服务质量评估',
        ruleCode: 'QUALITY_ASSESSMENT_005',
        ruleType: 'CUSTOM',
        ruleCategory: 'QUALITY_ASSURANCE',
        severityLevel: 'LOW',
        description: '评估医疗服务质量指标，识别质量问题',
        ruleLogic: JSON.stringify({
          conditions: [
            { field: 'readmissionRate', operator: '>', value: 0.15 },
            { field: 'complicationRate', operator: '>', value: 0.1 }
          ],
          logic: 'OR'
        }),
        isActive: 0
      }
    ]

    // 批量插入规则
    for (const rule of testRules) {
      const insertSql = `
        INSERT INTO RULE_SUPERVISION (
          RULE_NAME, RULE_CODE, RULE_TYPE, RULE_CATEGORY, SEVERITY_LEVEL,
          DESCRIPTION, RULE_LOGIC, IS_ACTIVE, IS_DELETED, CREATED_AT, UPDATED_AT
        ) VALUES (
          :ruleName, :ruleCode, :ruleType, :ruleCategory, :severityLevel,
          :description, :ruleLogic, :isActive, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
      `

      await executeQuery(insertSql, {
        ruleName: rule.ruleName,
        ruleCode: rule.ruleCode,
        ruleType: rule.ruleType,
        ruleCategory: rule.ruleCategory,
        severityLevel: rule.severityLevel,
        description: rule.description,
        ruleLogic: rule.ruleLogic,
        isActive: rule.isActive
      })
    }

    console.log(`✅ 成功插入 ${testRules.length} 条监管规则测试数据`)

    // 插入执行日志测试数据
    await insertExecutionLogTestData()

    // 插入执行结果测试数据
    await insertExecutionResultTestData()

  } catch (error) {
    console.error('❌ 插入监管规则测试数据失败:', error)
    throw error
  }
}

/**
 * 插入执行日志测试数据
 */
async function insertExecutionLogTestData() {
  try {
    console.log('🔄 插入执行日志测试数据...')

    // 获取规则ID
    const rulesSql = 'SELECT ID FROM RULE_SUPERVISION WHERE IS_DELETED = 0'
    const rulesResult = await executeQuery(rulesSql)
    const ruleIds = rulesResult.rows?.map(row => row.ID) || []

    if (ruleIds.length === 0) {
      console.log('⚠️ 没有找到规则数据，跳过执行日志插入')
      return
    }

    // 生成最近3个月的执行日志
    const executionLogs = []
    const now = new Date()
    
    for (let i = 0; i < 90; i++) { // 90天
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      const dailyExecutions = Math.floor(Math.random() * 20) + 5 // 5-25次执行
      
      for (let j = 0; j < dailyExecutions; j++) {
        const ruleId = ruleIds[Math.floor(Math.random() * ruleIds.length)]
        const status = Math.random() > 0.1 ? 'SUCCESS' : 'FAILED' // 90%成功率
        const duration = Math.floor(Math.random() * 2000) + 100 // 100-2100ms
        
        executionLogs.push({
          ruleId,
          status,
          duration,
          createdAt: date
        })
      }
    }

    // 批量插入执行日志
    for (const log of executionLogs) {
      const insertSql = `
        INSERT INTO RULE_EXECUTION_LOG (
          RULE_ID, EXECUTION_STATUS, EXECUTION_DURATION, CREATED_AT
        ) VALUES (
          :ruleId, :status, :duration, :createdAt
        )
      `

      await executeQuery(insertSql, {
        ruleId: log.ruleId,
        status: log.status,
        duration: log.duration,
        createdAt: log.createdAt
      })
    }

    console.log(`✅ 成功插入 ${executionLogs.length} 条执行日志测试数据`)

  } catch (error) {
    console.error('❌ 插入执行日志测试数据失败:', error)
    throw error
  }
}

/**
 * 插入执行结果测试数据
 */
async function insertExecutionResultTestData() {
  try {
    console.log('🔄 插入执行结果测试数据...')

    // 获取成功的执行日志ID
    const logsSql = `
      SELECT ID FROM RULE_EXECUTION_LOG 
      WHERE EXECUTION_STATUS = 'SUCCESS' 
      AND ROWNUM <= 500
    `
    const logsResult = await executeQuery(logsSql)
    const logIds = logsResult.rows?.map(row => row.ID) || []

    if (logIds.length === 0) {
      console.log('⚠️ 没有找到执行日志数据，跳过执行结果插入')
      return
    }

    // 生成执行结果
    const results = []
    for (const logId of logIds) {
      const resultType = Math.random() > 0.8 ? 'VIOLATION' : 
                        Math.random() > 0.5 ? 'SUSPICIOUS' : 'NORMAL'
      const confidence = Math.random() * 0.4 + 0.6 // 0.6-1.0
      
      results.push({
        logId,
        resultType,
        confidence
      })
    }

    // 批量插入执行结果
    for (const result of results) {
      const insertSql = `
        INSERT INTO RULE_EXECUTION_RESULT (
          EXECUTION_LOG_ID, RESULT_TYPE, CONFIDENCE_SCORE, CREATED_AT
        ) VALUES (
          :logId, :resultType, :confidence, CURRENT_TIMESTAMP
        )
      `

      await executeQuery(insertSql, {
        logId: result.logId,
        resultType: result.resultType,
        confidence: result.confidence
      })
    }

    console.log(`✅ 成功插入 ${results.length} 条执行结果测试数据`)

  } catch (error) {
    console.error('❌ 插入执行结果测试数据失败:', error)
    throw error
  }
}

/**
 * 清理测试数据
 */
export async function clearTestData() {
  try {
    console.log('🔄 清理测试数据...')

    await executeQuery('DELETE FROM RULE_EXECUTION_RESULT')
    await executeQuery('DELETE FROM RULE_EXECUTION_LOG')
    await executeQuery('DELETE FROM RULE_SUPERVISION')

    console.log('✅ 测试数据清理完成')

  } catch (error) {
    console.error('❌ 清理测试数据失败:', error)
    throw error
  }
}
