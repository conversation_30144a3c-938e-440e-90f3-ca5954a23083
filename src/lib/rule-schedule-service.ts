/**
 * 规则调度服务
 * 
 * 负责管理规则调度配置的CRUD操作
 */

import { executeQuery } from '@/lib/database'

// 调度配置接口
export interface RuleScheduleConfig {
  id?: number
  ruleId: number
  scheduleName: string
  cronExpression?: string
  intervalSeconds?: number
  isEnabled: boolean
  maxRetries: number
  retryDelay: number
  timeoutSeconds: number
  executionParams?: Record<string, any>
  lastExecutionTime?: string
  nextExecutionTime?: string
  executionCount: number
  successCount: number
  failureCount: number
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
}

// 调度列表参数
export interface ScheduleListParams {
  page?: number
  pageSize?: number
  search?: string
  ruleId?: number
  isEnabled?: boolean
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

// 分页响应
export interface SchedulePaginationResponse {
  data: RuleScheduleConfig[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

/**
 * 获取调度配置列表
 */
export async function getScheduleConfigs(params: ScheduleListParams): Promise<SchedulePaginationResponse> {
  try {
    const {
      page = 1,
      pageSize = 10,
      search = '',
      ruleId,
      isEnabled,
      sortBy = 'CREATED_AT',
      sortOrder = 'DESC'
    } = params

    // 构建WHERE条件
    const conditions: string[] = ['sc.IS_DELETED = 0']
    const queryParams: any = {}

    if (search) {
      conditions.push(`(
        UPPER(sc.SCHEDULE_NAME) LIKE UPPER(:search) OR 
        UPPER(rs.RULE_NAME) LIKE UPPER(:search) OR 
        UPPER(rs.RULE_CODE) LIKE UPPER(:search)
      )`)
      queryParams.search = `%${search}%`
    }

    if (ruleId) {
      conditions.push('sc.RULE_ID = :ruleId')
      queryParams.ruleId = ruleId
    }

    if (isEnabled !== undefined) {
      conditions.push('sc.IS_ENABLED = :isEnabled')
      queryParams.isEnabled = isEnabled ? 1 : 0
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as TOTAL_COUNT
      FROM RULE_SCHEDULE_CONFIG sc
      LEFT JOIN RULE_SUPERVISION rs ON sc.RULE_ID = rs.ID
      ${whereClause}
    `

    const countResult = await executeQuery(countSql, queryParams)
    const total = countResult.rows?.[0]?.TOTAL_COUNT || 0
    const totalPages = Math.ceil(total / pageSize)

    // 获取数据
    const offset = (page - 1) * pageSize
    const dataSql = `
      SELECT 
        sc.ID, sc.RULE_ID, sc.SCHEDULE_NAME, sc.CRON_EXPRESSION,
        sc.INTERVAL_SECONDS, sc.IS_ENABLED, sc.MAX_RETRIES,
        sc.RETRY_DELAY, sc.TIMEOUT_SECONDS, sc.EXECUTION_PARAMS,
        sc.LAST_EXECUTION_TIME, sc.NEXT_EXECUTION_TIME,
        sc.EXECUTION_COUNT, sc.SUCCESS_COUNT, sc.FAILURE_COUNT,
        sc.CREATED_AT, sc.UPDATED_AT, sc.CREATED_BY, sc.UPDATED_BY,
        rs.RULE_NAME, rs.RULE_CODE, rs.RULE_CATEGORY, rs.SEVERITY_LEVEL
      FROM RULE_SCHEDULE_CONFIG sc
      LEFT JOIN RULE_SUPERVISION rs ON sc.RULE_ID = rs.ID
      ${whereClause}
      ORDER BY sc.${sortBy} ${sortOrder}
      OFFSET ${offset} ROWS FETCH NEXT ${pageSize} ROWS ONLY
    `

    const dataResult = await executeQuery(dataSql, queryParams)

    const data = (dataResult.rows || []).map((row: any) => ({
      id: row.ID,
      ruleId: row.RULE_ID,
      scheduleName: row.SCHEDULE_NAME,
      cronExpression: row.CRON_EXPRESSION,
      intervalSeconds: row.INTERVAL_SECONDS,
      isEnabled: row.IS_ENABLED === 1,
      maxRetries: row.MAX_RETRIES,
      retryDelay: row.RETRY_DELAY,
      timeoutSeconds: row.TIMEOUT_SECONDS,
      executionParams: row.EXECUTION_PARAMS ? JSON.parse(row.EXECUTION_PARAMS) : {},
      lastExecutionTime: row.LAST_EXECUTION_TIME?.toISOString(),
      nextExecutionTime: row.NEXT_EXECUTION_TIME?.toISOString(),
      executionCount: row.EXECUTION_COUNT,
      successCount: row.SUCCESS_COUNT,
      failureCount: row.FAILURE_COUNT,
      createdAt: row.CREATED_AT?.toISOString(),
      updatedAt: row.UPDATED_AT?.toISOString(),
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
      // 关联的规则信息
      ruleName: row.RULE_NAME,
      ruleCode: row.RULE_CODE,
      ruleCategory: row.RULE_CATEGORY,
      severityLevel: row.SEVERITY_LEVEL
    }))

    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages
      }
    }

  } catch (error) {
    console.error('❌ 获取调度配置列表失败:', error)
    throw new Error(`获取调度配置列表失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取单个调度配置
 */
export async function getScheduleConfigById(id: number): Promise<RuleScheduleConfig | null> {
  try {
    const sql = `
      SELECT 
        sc.ID, sc.RULE_ID, sc.SCHEDULE_NAME, sc.CRON_EXPRESSION,
        sc.INTERVAL_SECONDS, sc.IS_ENABLED, sc.MAX_RETRIES,
        sc.RETRY_DELAY, sc.TIMEOUT_SECONDS, sc.EXECUTION_PARAMS,
        sc.LAST_EXECUTION_TIME, sc.NEXT_EXECUTION_TIME,
        sc.EXECUTION_COUNT, sc.SUCCESS_COUNT, sc.FAILURE_COUNT,
        sc.CREATED_AT, sc.UPDATED_AT, sc.CREATED_BY, sc.UPDATED_BY
      FROM RULE_SCHEDULE_CONFIG sc
      WHERE sc.ID = :id AND sc.IS_DELETED = 0
    `

    const result = await executeQuery(sql, { id })
    
    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.ID,
      ruleId: row.RULE_ID,
      scheduleName: row.SCHEDULE_NAME,
      cronExpression: row.CRON_EXPRESSION,
      intervalSeconds: row.INTERVAL_SECONDS,
      isEnabled: row.IS_ENABLED === 1,
      maxRetries: row.MAX_RETRIES,
      retryDelay: row.RETRY_DELAY,
      timeoutSeconds: row.TIMEOUT_SECONDS,
      executionParams: row.EXECUTION_PARAMS ? JSON.parse(row.EXECUTION_PARAMS) : {},
      lastExecutionTime: row.LAST_EXECUTION_TIME?.toISOString(),
      nextExecutionTime: row.NEXT_EXECUTION_TIME?.toISOString(),
      executionCount: row.EXECUTION_COUNT,
      successCount: row.SUCCESS_COUNT,
      failureCount: row.FAILURE_COUNT,
      createdAt: row.CREATED_AT?.toISOString(),
      updatedAt: row.UPDATED_AT?.toISOString(),
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY
    }

  } catch (error) {
    console.error('❌ 获取调度配置详情失败:', error)
    throw new Error(`获取调度配置详情失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 创建调度配置
 */
export async function createScheduleConfig(config: Omit<RuleScheduleConfig, 'id'>, userId: number): Promise<number> {
  try {
    const sql = `
      INSERT INTO RULE_SCHEDULE_CONFIG (
        RULE_ID, SCHEDULE_NAME, CRON_EXPRESSION, INTERVAL_SECONDS,
        IS_ENABLED, MAX_RETRIES, RETRY_DELAY, TIMEOUT_SECONDS,
        EXECUTION_PARAMS, CREATED_BY, UPDATED_BY
      ) VALUES (
        :ruleId, :scheduleName, :cronExpression, :intervalSeconds,
        :isEnabled, :maxRetries, :retryDelay, :timeoutSeconds,
        :executionParams, :userId, :userId
      ) RETURNING ID INTO :id
    `

    const params = {
      ruleId: config.ruleId,
      scheduleName: config.scheduleName,
      cronExpression: config.cronExpression || null,
      intervalSeconds: config.intervalSeconds || null,
      isEnabled: config.isEnabled ? 1 : 0,
      maxRetries: config.maxRetries,
      retryDelay: config.retryDelay,
      timeoutSeconds: config.timeoutSeconds,
      executionParams: config.executionParams ? JSON.stringify(config.executionParams) : null,
      userId,
      id: { dir: 'OUT', type: 'NUMBER' }
    }

    const result = await executeQuery(sql, params)
    return (result as any).outBinds?.id || 0

  } catch (error) {
    console.error('❌ 创建调度配置失败:', error)
    throw new Error(`创建调度配置失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 更新调度配置
 */
export async function updateScheduleConfig(id: number, config: Partial<RuleScheduleConfig>, userId: number): Promise<void> {
  try {
    const updateFields: string[] = []
    const params: any = { id, userId }

    if (config.scheduleName !== undefined) {
      updateFields.push('SCHEDULE_NAME = :scheduleName')
      params.scheduleName = config.scheduleName
    }

    if (config.cronExpression !== undefined) {
      updateFields.push('CRON_EXPRESSION = :cronExpression')
      params.cronExpression = config.cronExpression
    }

    if (config.intervalSeconds !== undefined) {
      updateFields.push('INTERVAL_SECONDS = :intervalSeconds')
      params.intervalSeconds = config.intervalSeconds
    }

    if (config.isEnabled !== undefined) {
      updateFields.push('IS_ENABLED = :isEnabled')
      params.isEnabled = config.isEnabled ? 1 : 0
    }

    if (config.maxRetries !== undefined) {
      updateFields.push('MAX_RETRIES = :maxRetries')
      params.maxRetries = config.maxRetries
    }

    if (config.retryDelay !== undefined) {
      updateFields.push('RETRY_DELAY = :retryDelay')
      params.retryDelay = config.retryDelay
    }

    if (config.timeoutSeconds !== undefined) {
      updateFields.push('TIMEOUT_SECONDS = :timeoutSeconds')
      params.timeoutSeconds = config.timeoutSeconds
    }

    if (config.executionParams !== undefined) {
      updateFields.push('EXECUTION_PARAMS = :executionParams')
      params.executionParams = config.executionParams ? JSON.stringify(config.executionParams) : null
    }

    if (updateFields.length === 0) {
      return
    }

    updateFields.push('UPDATED_BY = :userId', 'UPDATED_AT = SYSDATE')

    const sql = `
      UPDATE RULE_SCHEDULE_CONFIG 
      SET ${updateFields.join(', ')}
      WHERE ID = :id AND IS_DELETED = 0
    `

    await executeQuery(sql, params)

  } catch (error) {
    console.error('❌ 更新调度配置失败:', error)
    throw new Error(`更新调度配置失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 删除调度配置
 */
export async function deleteScheduleConfig(id: number, userId: number): Promise<void> {
  try {
    const sql = `
      UPDATE RULE_SCHEDULE_CONFIG 
      SET IS_DELETED = 1, UPDATED_BY = :userId, UPDATED_AT = SYSDATE
      WHERE ID = :id
    `

    await executeQuery(sql, { id, userId })

  } catch (error) {
    console.error('❌ 删除调度配置失败:', error)
    throw new Error(`删除调度配置失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 更新执行统计
 */
export async function updateExecutionStats(
  id: number, 
  success: boolean, 
  nextExecutionTime?: Date
): Promise<void> {
  try {
    const sql = `
      UPDATE RULE_SCHEDULE_CONFIG 
      SET 
        EXECUTION_COUNT = EXECUTION_COUNT + 1,
        SUCCESS_COUNT = SUCCESS_COUNT + CASE WHEN :success = 1 THEN 1 ELSE 0 END,
        FAILURE_COUNT = FAILURE_COUNT + CASE WHEN :success = 0 THEN 1 ELSE 0 END,
        LAST_EXECUTION_TIME = SYSDATE,
        NEXT_EXECUTION_TIME = :nextExecutionTime,
        UPDATED_AT = SYSDATE
      WHERE ID = :id
    `

    const params = {
      id,
      success: success ? 1 : 0,
      nextExecutionTime: nextExecutionTime || null
    }

    await executeQuery(sql, params)

  } catch (error) {
    console.error('❌ 更新执行统计失败:', error)
    throw new Error(`更新执行统计失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}
