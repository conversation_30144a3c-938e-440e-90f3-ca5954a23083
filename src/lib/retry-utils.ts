/**
 * 智能重试工具库
 * 提供灵活的重试策略和错误处理
 */

import { log } from './logger'
import { monitoring } from './monitoring'

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors?: string[]
  shouldRetry?: (error: Error) => boolean
  onRetry?: (attempt: number, error: Error) => void
}

/**
 * 重试上下文
 */
export interface RetryContext {
  operation: string
  [key: string]: any
}

/**
 * 默认重试配置
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffMultiplier: 2,
  retryableErrors: ['TIMEOUT', 'CONNECTION_ERROR', 'TEMPORARY_FAILURE', 'RATE_LIMIT']
}

/**
 * 带重试的异步函数执行
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  context?: RetryContext
): Promise<T> {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config }
  const timer = monitoring.startTimer('retry_operation', context)
  
  let lastError: Error = new Error('Unknown error')
  let attempt = 0

  while (attempt < finalConfig.maxAttempts) {
    attempt++
    
    try {
      const result = await operation()
      
      if (attempt > 1) {
        log.info('Operation succeeded after retry', {
          ...context,
          attempt,
          totalAttempts: finalConfig.maxAttempts
        })
        
        monitoring.recordMetric({
          name: 'retry_success',
          value: 1,
          tags: {
            operation: context?.operation || 'unknown',
            attempt: attempt.toString()
          }
        })
      }
      
      timer.end(true)
      return result
      
    } catch (error) {
      lastError = error as Error
      
      // 检查是否应该重试
      if (!shouldRetryError(lastError, finalConfig)) {
        log.error('Operation failed with non-retryable error', {
          ...context,
          attempt,
          error: lastError.message
        })
        break
      }
      
      // 如果是最后一次尝试，不再重试
      if (attempt >= finalConfig.maxAttempts) {
        log.error('Operation failed after all retry attempts', {
          ...context,
          totalAttempts: finalConfig.maxAttempts,
          error: lastError.message
        })
        break
      }
      
      // 计算延迟时间
      const delay = calculateDelay(attempt, finalConfig)
      
      log.warn('Operation failed, retrying', {
        ...context,
        attempt,
        totalAttempts: finalConfig.maxAttempts,
        delay,
        error: lastError.message
      })
      
      // 调用重试回调
      if (finalConfig.onRetry) {
        finalConfig.onRetry(attempt, lastError)
      }
      
      // 等待延迟时间
      await sleep(delay)
    }
  }
  
  // 记录最终失败
  monitoring.recordMetric({
    name: 'retry_failure',
    value: 1,
    tags: {
      operation: context?.operation || 'unknown',
      attempts: attempt.toString()
    }
  })
  
  timer.end(false, lastError.message)
  throw lastError
}

/**
 * 判断错误是否应该重试
 */
function shouldRetryError(error: Error, config: RetryConfig): boolean {
  // 如果提供了自定义判断函数，优先使用
  if (config.shouldRetry) {
    return config.shouldRetry(error)
  }
  
  // 检查错误类型是否在可重试列表中
  if (config.retryableErrors) {
    const errorMessage = error.message.toUpperCase()
    return config.retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError.toUpperCase())
    )
  }
  
  // 默认的可重试错误判断
  const retryablePatterns = [
    /timeout/i,
    /connection/i,
    /network/i,
    /temporary/i,
    /rate.?limit/i,
    /too.?many.?requests/i,
    /service.?unavailable/i,
    /internal.?server.?error/i
  ]
  
  return retryablePatterns.some(pattern => pattern.test(error.message))
}

/**
 * 计算延迟时间（指数退避）
 */
function calculateDelay(attempt: number, config: RetryConfig): number {
  const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1)
  const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5) // 添加抖动
  return Math.min(jitteredDelay, config.maxDelay)
}

/**
 * 睡眠函数
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 断路器模式实现
 */
export class CircuitBreaker {
  private failureCount = 0
  private lastFailureTime = 0
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'
  
  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000, // 1分钟
    private successThreshold: number = 3
  ) {}

  async execute<T>(operation: () => Promise<T>, context?: RetryContext): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime < this.recoveryTimeout) {
        throw new Error('Circuit breaker is OPEN')
      } else {
        this.state = 'HALF_OPEN'
        this.failureCount = 0
      }
    }

    try {
      const result = await operation()
      
      if (this.state === 'HALF_OPEN') {
        this.failureCount = 0
        this.state = 'CLOSED'
        
        log.info('Circuit breaker recovered', context)
      }
      
      return result
      
    } catch (error) {
      this.failureCount++
      this.lastFailureTime = Date.now()
      
      if (this.failureCount >= this.failureThreshold) {
        this.state = 'OPEN'
        
        log.error('Circuit breaker opened', {
          ...context,
          failureCount: this.failureCount,
          threshold: this.failureThreshold
        })
      }
      
      throw error
    }
  }

  getState(): string {
    return this.state
  }

  reset(): void {
    this.state = 'CLOSED'
    this.failureCount = 0
    this.lastFailureTime = 0
  }
}

/**
 * 批量重试处理器
 */
export class BatchRetryProcessor<T> {
  private retryQueue: Array<{
    operation: () => Promise<T>
    resolve: (value: T) => void
    reject: (error: Error) => void
    config: RetryConfig
    context?: RetryContext
  }> = []
  
  private processing = false

  /**
   * 添加操作到重试队列
   */
  async add(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    context?: RetryContext
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      this.retryQueue.push({
        operation,
        resolve,
        reject,
        config: { ...DEFAULT_RETRY_CONFIG, ...config },
        context
      })
      
      this.processQueue()
    })
  }

  /**
   * 处理重试队列
   */
  private async processQueue(): Promise<void> {
    if (this.processing || this.retryQueue.length === 0) {
      return
    }

    this.processing = true

    while (this.retryQueue.length > 0) {
      const item = this.retryQueue.shift()!
      
      try {
        const result = await withRetry(item.operation, item.config, item.context)
        item.resolve(result)
      } catch (error) {
        item.reject(error as Error)
      }
    }

    this.processing = false
  }

  /**
   * 获取队列长度
   */
  getQueueLength(): number {
    return this.retryQueue.length
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.retryQueue.forEach(item => {
      item.reject(new Error('Queue cleared'))
    })
    this.retryQueue = []
  }
}

/**
 * 预定义的重试配置
 */
export const RETRY_CONFIGS = {
  // 快速重试：适用于轻量级操作
  FAST: {
    maxAttempts: 3,
    baseDelay: 500,
    maxDelay: 5000,
    backoffMultiplier: 1.5
  },
  
  // 标准重试：适用于一般操作
  STANDARD: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2
  },
  
  // 慢重试：适用于重量级操作
  SLOW: {
    maxAttempts: 5,
    baseDelay: 2000,
    maxDelay: 30000,
    backoffMultiplier: 2
  },
  
  // 数据库重试：适用于数据库操作
  DATABASE: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 15000,
    backoffMultiplier: 2,
    retryableErrors: ['CONNECTION_ERROR', 'TIMEOUT', 'DEADLOCK', 'LOCK_TIMEOUT']
  },
  
  // 网络重试：适用于网络请求
  NETWORK: {
    maxAttempts: 4,
    baseDelay: 1000,
    maxDelay: 20000,
    backoffMultiplier: 2,
    retryableErrors: ['TIMEOUT', 'CONNECTION_ERROR', 'NETWORK_ERROR', 'RATE_LIMIT']
  }
} as const
