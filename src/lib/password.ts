import bcrypt from 'bcryptjs'

// 密码加密配置
const SALT_ROUNDS = 12

/**
 * 加密密码
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    const salt = await bcrypt.genSalt(SALT_ROUNDS)
    const hashedPassword = await bcrypt.hash(password, salt)
    return hashedPassword
  } catch (error) {
    console.error('❌ 密码加密失败:', error)
    throw new Error('密码加密失败')
  }
}

/**
 * 验证密码
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hashedPassword)
  } catch (error) {
    console.error('❌ 密码验证失败:', error)
    return false
  }
}

/**
 * 密码强度验证
 */
export interface PasswordStrength {
  isValid: boolean
  score: number // 0-4
  feedback: string[]
  requirements: {
    minLength: boolean
    hasUppercase: boolean
    hasLowercase: boolean
    hasNumbers: boolean
    hasSpecialChars: boolean
  }
}

/**
 * 检查密码强度
 */
export function checkPasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = []
  const requirements = {
    minLength: password.length >= 8,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumbers: /\d/.test(password),
    hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
  }

  // 检查最小长度
  if (!requirements.minLength) {
    feedback.push('密码长度至少8位')
  }

  // 检查大写字母
  if (!requirements.hasUppercase) {
    feedback.push('至少包含一个大写字母')
  }

  // 检查小写字母
  if (!requirements.hasLowercase) {
    feedback.push('至少包含一个小写字母')
  }

  // 检查数字
  if (!requirements.hasNumbers) {
    feedback.push('至少包含一个数字')
  }

  // 检查特殊字符
  if (!requirements.hasSpecialChars) {
    feedback.push('至少包含一个特殊字符')
  }

  // 计算强度分数
  let score = 0
  if (requirements.minLength) score++
  if (requirements.hasUppercase) score++
  if (requirements.hasLowercase) score++
  if (requirements.hasNumbers) score++
  if (requirements.hasSpecialChars) score++

  // 额外检查
  if (password.length >= 12) score += 0.5
  if (password.length >= 16) score += 0.5

  // 检查常见弱密码模式
  const weakPatterns = [
    /^123456/,
    /^password/i,
    /^admin/i,
    /^qwerty/i,
    /^abc123/i,
    /(.)\1{2,}/, // 连续重复字符
  ]

  const hasWeakPattern = weakPatterns.some(pattern => pattern.test(password))
  if (hasWeakPattern) {
    score = Math.max(0, score - 1)
    feedback.push('避免使用常见的弱密码模式')
  }

  const isValid = Object.values(requirements).every(req => req) && !hasWeakPattern

  return {
    isValid,
    score: Math.min(4, Math.floor(score)),
    feedback,
    requirements,
  }
}

/**
 * 获取密码强度描述
 */
export function getPasswordStrengthText(score: number): string {
  switch (score) {
    case 0:
    case 1:
      return '弱'
    case 2:
      return '一般'
    case 3:
      return '强'
    case 4:
      return '很强'
    default:
      return '未知'
  }
}

/**
 * 获取密码强度颜色
 */
export function getPasswordStrengthColor(score: number): string {
  switch (score) {
    case 0:
    case 1:
      return 'text-red-500'
    case 2:
      return 'text-yellow-500'
    case 3:
      return 'text-blue-500'
    case 4:
      return 'text-green-500'
    default:
      return 'text-gray-500'
  }
}

/**
 * 生成随机密码
 */
export function generateRandomPassword(length: number = 12): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '0123456789'
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'
  
  const allChars = uppercase + lowercase + numbers + specialChars
  
  let password = ''
  
  // 确保至少包含每种类型的字符
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += specialChars[Math.floor(Math.random() * specialChars.length)]
  
  // 填充剩余长度
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // 打乱字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

/**
 * 验证密码确认
 */
export function validatePasswordConfirmation(password: string, confirmPassword: string): boolean {
  return password === confirmPassword
}
