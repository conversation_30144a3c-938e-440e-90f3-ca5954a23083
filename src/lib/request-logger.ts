import { NextRequest } from 'next/server'

export interface RequestLogData {
  requestId: string
  method: string
  pathname: string
  query?: Record<string, string>
  ip?: string
  userAgent?: string
  userId?: number
  username?: string
  timestamp: string
  duration?: number
  status?: number
  error?: string
}

/**
 * 生成唯一的请求ID
 */
export function generateRequestId(): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  return `req_${timestamp}_${random}`
}

/**
 * 从请求中提取基本信息
 */
export function extractRequestInfo(request: NextRequest): Pick<RequestLogData, 'method' | 'pathname' | 'timestamp'> & Partial<Pick<RequestLogData, 'query' | 'ip' | 'userAgent'>> {
  const url = new URL(request.url)
  const query: Record<string, string> = {}
  
  // 提取查询参数
  url.searchParams.forEach((value, key) => {
    query[key] = value
  })

  return {
    method: request.method,
    pathname: url.pathname,
    query: Object.keys(query).length > 0 ? query : undefined,
    ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
    timestamp: new Date().toISOString()
  }
}

/**
 * 记录请求开始
 */
export function logRequestStart(request: NextRequest, requestId?: string): RequestLogData {
  const logData: RequestLogData = {
    requestId: requestId || generateRequestId(),
    ...extractRequestInfo(request)
  }

  // 在开发环境下输出调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log(`🐛 [DEBUG] Request started: ${logData.method} ${logData.pathname}`, {
      requestId: logData.requestId,
      method: logData.method,
      pathname: logData.pathname,
      ip: logData.ip,
      userAgent: logData.userAgent?.substring(0, 100) + (logData.userAgent && logData.userAgent.length > 100 ? '...' : '')
    })
  }

  return logData
}

/**
 * 记录请求完成
 */
export function logRequestEnd(
  logData: RequestLogData, 
  status: number, 
  startTime: number,
  error?: string
): void {
  const duration = Date.now() - startTime
  const finalLogData = {
    ...logData,
    duration,
    status,
    error
  }

  // 根据状态码选择日志级别
  const isError = status >= 400
  const isWarning = status >= 300 && status < 400

  if (process.env.NODE_ENV === 'development') {
    if (isError) {
      console.error(`❌ [ERROR] Request failed: ${finalLogData.method} ${finalLogData.pathname}`, {
        requestId: finalLogData.requestId,
        method: finalLogData.method,
        pathname: finalLogData.pathname,
        duration: finalLogData.duration,
        ip: finalLogData.ip,
        status: finalLogData.status,
        error: finalLogData.error
      })
    } else if (isWarning) {
      console.warn(`⚠️ [WARN] Request completed: ${finalLogData.method} ${finalLogData.pathname}`, {
        requestId: finalLogData.requestId,
        method: finalLogData.method,
        pathname: finalLogData.pathname,
        duration: finalLogData.duration,
        ip: finalLogData.ip,
        status: finalLogData.status
      })
    } else {
      console.log(`ℹ️ [INFO] Request completed: ${finalLogData.method} ${finalLogData.pathname}`, {
        requestId: finalLogData.requestId,
        method: finalLogData.method,
        pathname: finalLogData.pathname,
        duration: finalLogData.duration,
        ip: finalLogData.ip,
        status: finalLogData.status
      })
    }
  }

  // 在生产环境中，可以将日志发送到外部服务
  if (process.env.NODE_ENV === 'production') {
    // TODO: 发送到日志服务（如 CloudWatch, Datadog 等）
    // await sendToLogService(finalLogData)
  }
}

/**
 * 请求日志中间件装饰器
 */
export function withRequestLogging(handler: Function) {
  return async function (request: NextRequest, ...args: any[]) {
    const startTime = Date.now()
    const logData = logRequestStart(request)

    try {
      const response = await handler(request, ...args)
      
      // 如果返回的是 Response 对象，记录状态码
      if (response instanceof Response) {
        logRequestEnd(logData, response.status, startTime)
      } else {
        // 对于其他类型的返回值，假设是成功的
        logRequestEnd(logData, 200, startTime)
      }

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logRequestEnd(logData, 500, startTime, errorMessage)
      throw error
    }
  }
}

/**
 * 记录用户操作日志
 */
export function logUserAction(
  userId: number,
  username: string,
  action: string,
  resource: string,
  details?: Record<string, any>
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    userId,
    username,
    action,
    resource,
    details
  }

  if (process.env.NODE_ENV === 'development') {
    console.log(`👤 [USER] ${username} performed ${action} on ${resource}`, logData)
  }

  // 在生产环境中，可以将用户操作日志发送到审计服务
  if (process.env.NODE_ENV === 'production') {
    // TODO: 发送到审计日志服务
    // await sendToAuditService(logData)
  }
}

/**
 * 默认的请求日志记录器
 */
export const requestLogger = {
  logRequestStart,
  logRequestEnd,
  logUserAction,
  withRequestLogging,
  generateRequestId,
  extractRequestInfo
}
