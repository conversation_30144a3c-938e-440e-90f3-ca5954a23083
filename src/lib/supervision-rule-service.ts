import { executeQuery } from '@/lib/database'
import { 
  SupervisionRule, 
  RuleExecutionLog, 
  RuleTemplate, 
  RuleExecutionResult,
  RuleAuditResult,
  RuleListParams, 
  RuleCreateRequest, 
  RuleUpdateRequest,
  RuleExecutionRequest,
  RuleStatistics,
  RulePaginationResponse,
  RuleType,
  RuleCategory,
  SeverityLevel,
  CreatedFrom,
  ExecutionStatus,
  ResultType,
  RiskLevel
} from '@/types/supervision-rule'



/**
 * 获取监管规则列表（分页）
 */
export async function getSupervisionRules(params: RuleListParams): Promise<RulePaginationResponse> {
  try {
    const {
      page = 1,
      pageSize = 10,
      search = '',
      ruleType,
      ruleCategory,
      severityLevel,
      isActive,
      createdFrom,
      startDate,
      endDate,
      sortBy = 'CREATED_AT',
      sortOrder = 'DESC'
    } = params

    // 字段名映射：前端字段名 -> 数据库字段名
    const fieldMapping: Record<string, string> = {
      'createdAt': 'CREATED_AT',
      'updatedAt': 'UPDATED_AT',
      'ruleName': 'RULE_NAME',
      'ruleCode': 'RULE_CODE',
      'ruleType': 'RULE_TYPE',
      'ruleCategory': 'RULE_CATEGORY',
      'severityLevel': 'SEVERITY_LEVEL',
      'isActive': 'IS_ACTIVE'
    }

    // 转换排序字段名
    const dbSortBy = fieldMapping[sortBy] || 'CREATED_AT'
    console.log(`🔍 字段映射调试: sortBy=${sortBy}, dbSortBy=${dbSortBy}`)

    // 构建WHERE条件
    const conditions: string[] = ['IS_DELETED = 0']
    const queryParams: any = {}

    if (search) {
      conditions.push(`(
        UPPER(RULE_NAME) LIKE UPPER(:search) OR 
        UPPER(RULE_CODE) LIKE UPPER(:search) OR 
        UPPER(DESCRIPTION) LIKE UPPER(:search)
      )`)
      queryParams.search = `%${search}%`
    }

    if (ruleType) {
      conditions.push('RULE_TYPE = :ruleType')
      queryParams.ruleType = ruleType
    }

    if (ruleCategory) {
      conditions.push('RULE_CATEGORY = :ruleCategory')
      queryParams.ruleCategory = ruleCategory
    }

    if (severityLevel) {
      conditions.push('SEVERITY_LEVEL = :severityLevel')
      queryParams.severityLevel = severityLevel
    }

    if (isActive !== undefined) {
      conditions.push('IS_ACTIVE = :isActive')
      queryParams.isActive = isActive ? 1 : 0
    }

    if (createdFrom) {
      conditions.push('CREATED_FROM = :createdFrom')
      queryParams.createdFrom = createdFrom
    }

    if (startDate) {
      conditions.push('CREATED_AT >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = startDate
    }

    if (endDate) {
      conditions.push('CREATED_AT <= TO_DATE(:endDate, \'YYYY-MM-DD\')')
      queryParams.endDate = endDate
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as TOTAL_COUNT
      FROM RULE_SUPERVISION
      ${whereClause}
    `

    const countResult = await executeQuery(countSql, queryParams)
    const totalCount = countResult.rows?.[0]?.TOTAL_COUNT || 0

    // 计算分页
    const offset = (page - 1) * pageSize
    const totalPages = Math.ceil(totalCount / pageSize)

    // 获取数据
    const dataSql = `
      SELECT * FROM (
        SELECT 
          ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,
          RULE_CONTENT, RULE_SQL, RULE_DSL, PRIORITY_LEVEL, SEVERITY_LEVEL,
          IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE, VERSION_NUMBER, PARENT_RULE_ID,
          RULE_SOURCE, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT, LAST_EXECUTED_AT,
          IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY,
          ROW_NUMBER() OVER (ORDER BY ${dbSortBy} ${sortOrder}) as RN
        FROM RULE_SUPERVISION
        ${whereClause}
      ) WHERE RN > :offset AND RN <= :limit
    `

    queryParams.offset = offset
    queryParams.limit = offset + pageSize

    const dataResult = await executeQuery(dataSql, queryParams)

    const rules: SupervisionRule[] = (dataResult.rows || []).map((row: any): SupervisionRule => {
      // 确保所有字段都是基本类型，避免Oracle对象引用
      const cleanRow = {
        id: Number(row.ID) || 0,
        ruleCode: String(row.RULE_CODE || ''),
        ruleName: String(row.RULE_NAME || ''),
        ruleType: String(row.RULE_TYPE || 'BASIC') as RuleType,
        ruleCategory: String(row.RULE_CATEGORY || 'COMPLIANCE_CHECK') as RuleCategory,
        description: String(row.DESCRIPTION || ''),
        ruleContent: String(row.RULE_CONTENT || ''),
        ruleSql: String(row.RULE_SQL || ''),
        ruleDsl: String(row.RULE_DSL || ''),
        priorityLevel: Number(row.PRIORITY_LEVEL || 1),
        severityLevel: String(row.SEVERITY_LEVEL || 'MEDIUM') as SeverityLevel,
        isActive: Boolean(row.IS_ACTIVE === 1),
        effectiveDate: row.EFFECTIVE_DATE ?
          (new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] || '') :
          (new Date().toISOString().split('T')[0] || ''),
        expiryDate: row.EXPIRY_DATE ?
          new Date(row.EXPIRY_DATE).toISOString().split('T')[0] :
          undefined,
        versionNumber: String(row.VERSION_NUMBER || '1.0'),
        parentRuleId: row.PARENT_RULE_ID ? Number(row.PARENT_RULE_ID) : undefined,
        ruleSource: String(row.RULE_SOURCE || ''),
        createdFrom: String(row.CREATED_FROM || 'MANUAL') as CreatedFrom,
        executionCount: Number(row.EXECUTION_COUNT || 0),
        successCount: Number(row.SUCCESS_COUNT || 0),
        lastExecutedAt: row.LAST_EXECUTED_AT ?
          new Date(row.LAST_EXECUTED_AT).toISOString() :
          undefined,
        isDeleted: Boolean(row.IS_DELETED === 1),
        createdAt: row.CREATED_AT ? String(row.CREATED_AT) : new Date().toISOString(),
        updatedAt: row.UPDATED_AT ? String(row.UPDATED_AT) : new Date().toISOString(),
        createdBy: Number(row.CREATED_BY) || undefined,
        updatedBy: Number(row.UPDATED_BY) || undefined,
      }
      return cleanRow
    })

    return {
      items: rules,
      page,
      pageSize,
      total: totalCount,
      totalPages,
    }
  } catch (error) {
    console.error('❌ 获取监管规则列表失败:', error)
    throw new Error('获取监管规则列表失败')
  }
}

/**
 * 根据ID获取监管规则详情
 */
export async function getSupervisionRuleById(ruleId: number): Promise<SupervisionRule | null> {
  try {
    const sql = `
      SELECT 
        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,
        RULE_CONTENT, RULE_SQL, RULE_DSL, PRIORITY_LEVEL, SEVERITY_LEVEL,
        IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE, VERSION_NUMBER, PARENT_RULE_ID,
        RULE_SOURCE, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT, LAST_EXECUTED_AT,
        IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
      FROM RULE_SUPERVISION
      WHERE ID = :ruleId AND IS_DELETED = 0
    `

    const result = await executeQuery(sql, { ruleId })

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    const rule: SupervisionRule = {
      id: row.ID,
      ruleCode: row.RULE_CODE,
      ruleName: row.RULE_NAME,
      ruleType: row.RULE_TYPE as RuleType,
      ruleCategory: row.RULE_CATEGORY as RuleCategory,
      description: row.DESCRIPTION,
      ruleContent: row.RULE_CONTENT,
      ruleSql: row.RULE_SQL,
      ruleDsl: row.RULE_DSL,
      priorityLevel: row.PRIORITY_LEVEL,
      severityLevel: row.SEVERITY_LEVEL as SeverityLevel,
      isActive: row.IS_ACTIVE === 1,
      effectiveDate: (row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]) as string,
      expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,
      versionNumber: row.VERSION_NUMBER,
      parentRuleId: row.PARENT_RULE_ID,
      ruleSource: row.RULE_SOURCE,
      createdFrom: row.CREATED_FROM as CreatedFrom,
      executionCount: row.EXECUTION_COUNT,
      successCount: row.SUCCESS_COUNT,
      lastExecutedAt: row.LAST_EXECUTED_AT ? new Date(row.LAST_EXECUTED_AT).toISOString() : undefined,
      isDeleted: row.IS_DELETED === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }

    // 获取关联数据
    rule.executionLogs = await getRuleExecutionLogs(ruleId)
    rule.executionResults = await getRuleExecutionResults(ruleId)

    // 获取父规则和子规则（避免循环引用）
    if (rule.parentRuleId) {
      const parentRule = await getBasicRuleInfo(rule.parentRuleId)
      rule.parentRule = parentRule || undefined
    }
    rule.childRules = await getChildRules(ruleId)

    return rule
  } catch (error) {
    console.error('❌ 获取监管规则详情失败:', error)
    throw new Error('获取监管规则详情失败')
  }
}

/**
 * 获取规则的执行记录
 */
export async function getRuleExecutionLogs(ruleId: number): Promise<RuleExecutionLog[]> {
  try {
    const sql = `
      SELECT 
        ID, RULE_ID, EXECUTION_ID, EXECUTION_STATUS, STARTED_AT, ENDED_AT,
        EXECUTION_DURATION, PROCESSED_RECORD_COUNT, MATCHED_RECORD_COUNT,
        ERROR_MESSAGE, EXECUTION_PARAMS, EXECUTION_RESULT, EXECUTED_BY, CREATED_AT
      FROM RULE_EXECUTION_LOG
      WHERE RULE_ID = :ruleId
      ORDER BY STARTED_AT DESC
    `

    const result = await executeQuery(sql, { ruleId })

    return (result.rows || []).map((row: any) => ({
      id: row.ID,
      ruleId: row.RULE_ID,
      executionId: row.EXECUTION_ID,
      executionStatus: row.EXECUTION_STATUS as ExecutionStatus,
      startedAt: row.STARTED_AT,
      endedAt: row.ENDED_AT,
      executionDuration: row.EXECUTION_DURATION,
      processedRecordCount: row.PROCESSED_RECORD_COUNT,
      matchedRecordCount: row.MATCHED_RECORD_COUNT,
      errorMessage: row.ERROR_MESSAGE,
      executionParams: row.EXECUTION_PARAMS,
      executionResult: row.EXECUTION_RESULT,
      executedBy: row.EXECUTED_BY,
      createdAt: row.CREATED_AT,
    }))
  } catch (error) {
    console.error('❌ 获取规则执行记录失败:', error)
    return []
  }
}

/**
 * 获取规则的执行结果
 */
export async function getRuleExecutionResults(ruleId: number): Promise<RuleExecutionResult[]> {
  try {
    const sql = `
      SELECT 
        ID, EXECUTION_LOG_ID, RULE_ID, CASE_ID, RESULT_TYPE, RISK_LEVEL,
        VIOLATION_DESCRIPTION, VIOLATION_AMOUNT, EVIDENCE_DATA, RULE_MATCHED_FIELDS,
        CONFIDENCE_SCORE, RESULT_STATUS, IS_AUTO_PROCESSED, IS_FOLLOW_UP_REQUIRED,
        RELATED_CASE_COUNT, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
      FROM RULE_EXECUTION_RESULT
      WHERE RULE_ID = :ruleId
      ORDER BY CREATED_AT DESC
    `

    const result = await executeQuery(sql, { ruleId })

    return (result.rows || []).map((row: any) => ({
      id: row.ID,
      executionLogId: row.EXECUTION_LOG_ID,
      ruleId: row.RULE_ID,
      caseId: row.CASE_ID,
      resultType: row.RESULT_TYPE as ResultType,
      riskLevel: row.RISK_LEVEL as RiskLevel,
      violationDescription: row.VIOLATION_DESCRIPTION,
      violationAmount: row.VIOLATION_AMOUNT,
      evidenceData: row.EVIDENCE_DATA,
      ruleMatchedFields: row.RULE_MATCHED_FIELDS,
      confidenceScore: row.CONFIDENCE_SCORE,
      resultStatus: row.RESULT_STATUS,
      isAutoProcessed: row.IS_AUTO_PROCESSED === 1,
      isFollowUpRequired: row.IS_FOLLOW_UP_REQUIRED === 1,
      relatedCaseCount: row.RELATED_CASE_COUNT,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
    }))
  } catch (error) {
    console.error('❌ 获取规则执行结果失败:', error)
    return []
  }
}

/**
 * 获取基本规则信息（避免循环引用）
 */
async function getBasicRuleInfo(ruleId: number): Promise<SupervisionRule | null> {
  try {
    const sql = `
      SELECT
        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,
        PRIORITY_LEVEL, SEVERITY_LEVEL, IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE,
        VERSION_NUMBER, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT,
        CREATED_AT, UPDATED_AT
      FROM RULE_SUPERVISION
      WHERE ID = :ruleId AND IS_DELETED = 0
    `

    const result = await executeQuery(sql, { ruleId })

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.ID,
      ruleCode: row.RULE_CODE,
      ruleName: row.RULE_NAME,
      ruleType: row.RULE_TYPE as RuleType,
      ruleCategory: row.RULE_CATEGORY as RuleCategory,
      description: row.DESCRIPTION,
      ruleContent: '', // 简化版本
      priorityLevel: row.PRIORITY_LEVEL,
      severityLevel: row.SEVERITY_LEVEL as SeverityLevel,
      isActive: row.IS_ACTIVE === 1,
      effectiveDate: (row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]) as string,
      expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,
      versionNumber: row.VERSION_NUMBER,
      createdFrom: row.CREATED_FROM as CreatedFrom,
      executionCount: row.EXECUTION_COUNT,
      successCount: row.SUCCESS_COUNT,
      isDeleted: false,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
    }
  } catch (error) {
    console.error('❌ 获取基本规则信息失败:', error)
    return null
  }
}

/**
 * 获取子规则
 */
export async function getChildRules(parentRuleId: number): Promise<SupervisionRule[]> {
  try {
    const sql = `
      SELECT 
        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,
        PRIORITY_LEVEL, SEVERITY_LEVEL, IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE,
        VERSION_NUMBER, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT,
        CREATED_AT, UPDATED_AT
      FROM RULE_SUPERVISION
      WHERE PARENT_RULE_ID = :parentRuleId AND IS_DELETED = 0
      ORDER BY PRIORITY_LEVEL DESC, CREATED_AT ASC
    `

    const result = await executeQuery(sql, { parentRuleId })

    return (result.rows || []).map((row: any) => ({
      id: row.ID,
      ruleCode: row.RULE_CODE,
      ruleName: row.RULE_NAME,
      ruleType: row.RULE_TYPE as RuleType,
      ruleCategory: row.RULE_CATEGORY as RuleCategory,
      description: row.DESCRIPTION,
      ruleContent: '', // 简化版本，不包含完整内容
      priorityLevel: row.PRIORITY_LEVEL,
      severityLevel: row.SEVERITY_LEVEL as SeverityLevel,
      isActive: row.IS_ACTIVE === 1,
      effectiveDate: (row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]) as string,
      expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,
      versionNumber: row.VERSION_NUMBER,
      parentRuleId,
      createdFrom: row.CREATED_FROM as CreatedFrom,
      executionCount: row.EXECUTION_COUNT,
      successCount: row.SUCCESS_COUNT,
      isDeleted: false,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
    }))
  } catch (error) {
    console.error('❌ 获取子规则失败:', error)
    return []
  }
}

/**
 * 创建监管规则
 */
export async function createSupervisionRule(ruleData: RuleCreateRequest, createdBy: number): Promise<number> {
  try {
    // 先获取下一个ID
    const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM RULE_SUPERVISION`
    const nextIdResult = await executeQuery(getNextIdSql)
    const ruleId = nextIdResult.rows?.[0]?.NEXT_ID || 1

    const sql = `
      INSERT INTO RULE_SUPERVISION (
        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,
        RULE_CONTENT, RULE_SQL, RULE_DSL, PRIORITY_LEVEL, SEVERITY_LEVEL,
        IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE, VERSION_NUMBER, PARENT_RULE_ID,
        RULE_SOURCE, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT,
        IS_DELETED, CREATED_BY, UPDATED_BY
      ) VALUES (
        :ruleId, :ruleCode, :ruleName, :ruleType, :ruleCategory, :description,
        :ruleContent, :ruleSql, :ruleDsl, :priorityLevel, :severityLevel,
        1, TO_DATE(:effectiveDate, 'YYYY-MM-DD'),
        CASE WHEN :expiryDate IS NOT NULL THEN TO_DATE(:expiryDate, 'YYYY-MM-DD') ELSE NULL END,
        :versionNumber, :parentRuleId, :ruleSource, :createdFrom, 0, 0,
        0, :createdBy, :createdBy
      )
    `

    await executeQuery(sql, {
      ruleId,
      ruleCode: ruleData.ruleCode,
      ruleName: ruleData.ruleName,
      ruleType: ruleData.ruleType,
      ruleCategory: ruleData.ruleCategory,
      description: ruleData.description,
      ruleContent: ruleData.ruleContent,
      ruleSql: ruleData.ruleSql,
      ruleDsl: ruleData.ruleDsl,
      priorityLevel: ruleData.priorityLevel,
      severityLevel: ruleData.severityLevel,
      effectiveDate: ruleData.effectiveDate,
      expiryDate: ruleData.expiryDate,
      versionNumber: ruleData.versionNumber,
      parentRuleId: ruleData.parentRuleId,
      ruleSource: ruleData.ruleSource,
      createdFrom: ruleData.createdFrom,
      createdBy,
    })

    return ruleId
  } catch (error) {
    console.error('❌ 创建监管规则失败:', error)
    throw new Error('创建监管规则失败')
  }
}

/**
 * 更新监管规则
 */
export async function updateSupervisionRule(ruleId: number, updateData: RuleUpdateRequest, updatedBy: number): Promise<void> {
  try {
    const updateFields: string[] = []
    const params: any = { ruleId, updatedBy }

    if (updateData.ruleName !== undefined) {
      updateFields.push('RULE_NAME = :ruleName')
      params.ruleName = updateData.ruleName
    }

    if (updateData.ruleCategory !== undefined) {
      updateFields.push('RULE_CATEGORY = :ruleCategory')
      params.ruleCategory = updateData.ruleCategory
    }

    if (updateData.description !== undefined) {
      updateFields.push('DESCRIPTION = :description')
      params.description = updateData.description
    }

    if (updateData.ruleContent !== undefined) {
      updateFields.push('RULE_CONTENT = :ruleContent')
      params.ruleContent = updateData.ruleContent
    }

    if (updateData.ruleSql !== undefined) {
      updateFields.push('RULE_SQL = :ruleSql')
      params.ruleSql = updateData.ruleSql
    }

    if (updateData.ruleDsl !== undefined) {
      updateFields.push('RULE_DSL = :ruleDsl')
      params.ruleDsl = updateData.ruleDsl
    }

    if (updateData.priorityLevel !== undefined) {
      updateFields.push('PRIORITY_LEVEL = :priorityLevel')
      params.priorityLevel = updateData.priorityLevel
    }

    if (updateData.severityLevel !== undefined) {
      updateFields.push('SEVERITY_LEVEL = :severityLevel')
      params.severityLevel = updateData.severityLevel
    }

    if (updateData.isActive !== undefined) {
      updateFields.push('IS_ACTIVE = :isActive')
      params.isActive = updateData.isActive ? 1 : 0
    }

    if (updateData.effectiveDate !== undefined) {
      updateFields.push('EFFECTIVE_DATE = TO_DATE(:effectiveDate, \'YYYY-MM-DD\')')
      params.effectiveDate = updateData.effectiveDate
    }

    if (updateData.expiryDate !== undefined) {
      updateFields.push('EXPIRY_DATE = CASE WHEN :expiryDate IS NOT NULL THEN TO_DATE(:expiryDate, \'YYYY-MM-DD\') ELSE NULL END')
      params.expiryDate = updateData.expiryDate
    }

    if (updateData.ruleSource !== undefined) {
      updateFields.push('RULE_SOURCE = :ruleSource')
      params.ruleSource = updateData.ruleSource
    }

    if (updateFields.length === 0) {
      throw new Error('没有提供要更新的字段')
    }

    updateFields.push('UPDATED_BY = :updatedBy')
    updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP')

    const sql = `
      UPDATE RULE_SUPERVISION
      SET ${updateFields.join(', ')}
      WHERE ID = :ruleId AND IS_DELETED = 0
    `

    await executeQuery(sql, params)
  } catch (error) {
    console.error('❌ 更新监管规则失败:', error)
    throw new Error('更新监管规则失败')
  }
}

/**
 * 删除监管规则（软删除）
 */
export async function deleteSupervisionRule(ruleId: number, deletedBy: number): Promise<void> {
  try {
    const sql = `
      UPDATE RULE_SUPERVISION
      SET IS_DELETED = 1, UPDATED_BY = :deletedBy, UPDATED_AT = CURRENT_TIMESTAMP
      WHERE ID = :ruleId AND IS_DELETED = 0
    `

    await executeQuery(sql, { ruleId, deletedBy })
  } catch (error) {
    console.error('❌ 删除监管规则失败:', error)
    throw new Error('删除监管规则失败')
  }
}

/**
 * 启用/禁用监管规则
 */
export async function toggleSupervisionRule(ruleId: number, isActive: boolean, updatedBy: number): Promise<void> {
  try {
    const sql = `
      UPDATE RULE_SUPERVISION
      SET IS_ACTIVE = :isActive, UPDATED_BY = :updatedBy, UPDATED_AT = CURRENT_TIMESTAMP
      WHERE ID = :ruleId AND IS_DELETED = 0
    `

    await executeQuery(sql, { ruleId, isActive: isActive ? 1 : 0, updatedBy })
  } catch (error) {
    console.error('❌ 切换规则状态失败:', error)
    throw new Error('切换规则状态失败')
  }
}

/**
 * 执行监管规则
 */
export async function executeSupervisionRules(executionRequest: RuleExecutionRequest, executedBy: number): Promise<string> {
  try {
    const executionId = `EXEC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    for (const ruleId of executionRequest.ruleIds) {
      // 获取规则信息
      const rule = await getSupervisionRuleById(ruleId)
      if (!rule || !rule.isActive) {
        console.warn(`规则 ${ruleId} 不存在或未启用，跳过执行`)
        continue
      }

      // 创建执行记录
      const logId = await createExecutionLog(ruleId, executionId, executedBy, executionRequest.executionParams)

      try {
        // 更新执行记录为运行中
        await updateExecutionLogStatus(logId, 'RUNNING')

        // 执行规则逻辑
        const executionResult = await executeRule(rule, executionRequest)

        // 更新执行记录为成功
        await updateExecutionLogStatus(logId, 'SUCCESS', executionResult)

        // 更新规则执行统计
        await updateRuleExecutionStats(ruleId, true)

      } catch (error) {
        console.error(`规则 ${ruleId} 执行失败:`, error)

        // 更新执行记录为失败
        await updateExecutionLogStatus(logId, 'FAILED', undefined, error instanceof Error ? error.message : String(error))

        // 更新规则执行统计
        await updateRuleExecutionStats(ruleId, false)
      }
    }

    return executionId
  } catch (error) {
    console.error('❌ 执行监管规则失败:', error)
    throw new Error('执行监管规则失败')
  }
}

/**
 * 创建执行记录
 */
async function createExecutionLog(ruleId: number, executionId: string, executedBy: number, executionParams?: Record<string, any>): Promise<number> {
  const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM RULE_EXECUTION_LOG`
  const nextIdResult = await executeQuery(getNextIdSql)
  const logId = nextIdResult.rows?.[0]?.NEXT_ID || 1

  const sql = `
    INSERT INTO RULE_EXECUTION_LOG (
      ID, RULE_ID, EXECUTION_ID, EXECUTION_STATUS, STARTED_AT,
      PROCESSED_RECORD_COUNT, MATCHED_RECORD_COUNT, EXECUTION_PARAMS, EXECUTED_BY
    ) VALUES (
      :logId, :ruleId, :executionId, 'RUNNING', CURRENT_TIMESTAMP,
      0, 0, :executionParams, :executedBy
    )
  `

  await executeQuery(sql, {
    logId,
    ruleId,
    executionId,
    executionParams: executionParams ? JSON.stringify(executionParams) : null,
    executedBy,
  })

  return logId
}

/**
 * 更新执行记录状态
 */
async function updateExecutionLogStatus(logId: number, status: ExecutionStatus, result?: any, errorMessage?: string): Promise<void> {
  const sql = `
    UPDATE RULE_EXECUTION_LOG
    SET EXECUTION_STATUS = :status,
        ENDED_AT = CURRENT_TIMESTAMP,
        EXECUTION_DURATION = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - STARTED_AT)) * 1000,
        EXECUTION_RESULT = :result,
        ERROR_MESSAGE = :errorMessage
    WHERE ID = :logId
  `

  await executeQuery(sql, {
    logId,
    status,
    result: result ? JSON.stringify(result) : null,
    errorMessage,
  })
}

/**
 * 更新规则执行统计
 */
async function updateRuleExecutionStats(ruleId: number, isSuccess: boolean): Promise<void> {
  const sql = `
    UPDATE RULE_SUPERVISION
    SET EXECUTION_COUNT = EXECUTION_COUNT + 1,
        SUCCESS_COUNT = SUCCESS_COUNT + ${isSuccess ? 1 : 0},
        LAST_EXECUTED_AT = CURRENT_TIMESTAMP
    WHERE ID = :ruleId
  `

  await executeQuery(sql, { ruleId })
}

/**
 * 执行单个规则的核心逻辑
 */
async function executeRule(rule: SupervisionRule, executionRequest: RuleExecutionRequest): Promise<any> {
  // 这里是规则执行的核心逻辑
  // 根据规则类型和内容执行不同的检查逻辑

  const results = []

  if (rule.ruleSql) {
    // 执行SQL规则
    try {
      const sqlResult = await executeQuery(rule.ruleSql, executionRequest.executionParams || {})

      // 处理SQL执行结果
      if (sqlResult.rows && sqlResult.rows.length > 0) {
        for (const row of sqlResult.rows) {
          const result = {
            caseId: row.CASE_ID || null,
            resultType: determineResultType(row, rule),
            riskLevel: determineRiskLevel(row, rule),
            violationDescription: generateViolationDescription(row, rule),
            violationAmount: row.VIOLATION_AMOUNT || 0,
            evidenceData: JSON.stringify(row),
            confidenceScore: calculateConfidenceScore(row, rule),
          }
          results.push(result)
        }
      }
    } catch (error) {
      console.error('SQL规则执行失败:', error)
      throw error
    }
  } else if (rule.ruleDsl) {
    // 执行DSL规则
    // 这里可以实现DSL规则引擎
    console.log('DSL规则执行暂未实现')
  } else {
    // 基于规则内容的简单规则执行
    console.log('基于内容的规则执行')
  }

  return {
    processedCount: results.length,
    matchedCount: results.filter(r => r.resultType === 'VIOLATION' || r.resultType === 'SUSPICIOUS').length,
    results,
  }
}

/**
 * 确定结果类型
 */
function determineResultType(row: any, rule: SupervisionRule): ResultType {
  // 根据规则和数据确定结果类型
  if (row.IS_VIOLATION === 1) return 'VIOLATION'
  if (row.IS_SUSPICIOUS === 1) return 'SUSPICIOUS'
  if (row.HAS_ERROR === 1) return 'ERROR'
  return 'NORMAL'
}

/**
 * 确定风险等级
 */
function determineRiskLevel(row: any, rule: SupervisionRule): RiskLevel {
  // 根据规则严重程度和数据确定风险等级
  if (rule.severityLevel === 'CRITICAL') return 'CRITICAL'
  if (rule.severityLevel === 'HIGH') return 'HIGH'
  if (rule.severityLevel === 'MEDIUM') return 'MEDIUM'
  return 'LOW'
}

/**
 * 生成违规描述
 */
function generateViolationDescription(row: any, rule: SupervisionRule): string {
  return `规则 ${rule.ruleName} 检测到异常：${row.VIOLATION_REASON || '未知原因'}`
}

/**
 * 计算置信度分数
 */
function calculateConfidenceScore(row: any, rule: SupervisionRule): number {
  // 根据数据质量和规则匹配度计算置信度
  return row.CONFIDENCE_SCORE || 85
}

/**
 * 获取监管规则统计信息
 */
export async function getSupervisionRuleStatistics(): Promise<RuleStatistics> {
  try {
    // 基础统计
    const basicStatsSql = `
      SELECT
        COUNT(*) as TOTAL_RULES,
        SUM(CASE WHEN IS_ACTIVE = 1 THEN 1 ELSE 0 END) as ACTIVE_RULES,
        SUM(CASE WHEN IS_ACTIVE = 0 THEN 1 ELSE 0 END) as INACTIVE_RULES
      FROM RULE_SUPERVISION
      WHERE IS_DELETED = 0
    `

    const basicStatsResult = await executeQuery(basicStatsSql)
    const basicStats = basicStatsResult.rows?.[0] || {}

    // 按类型统计
    const typeStatsSql = `
      SELECT RULE_TYPE, COUNT(*) as COUNT
      FROM RULE_SUPERVISION
      WHERE IS_DELETED = 0
      GROUP BY RULE_TYPE
    `

    const typeStatsResult = await executeQuery(typeStatsSql)
    const rulesByType: Record<RuleType, number> = {
      'SQL': 0,
      'DSL': 0,
      'JAVASCRIPT': 0
    }

    typeStatsResult.rows?.forEach((row: any) => {
      rulesByType[row.RULE_TYPE as RuleType] = row.COUNT
    })

    // 按分类统计
    const categoryStatsSql = `
      SELECT RULE_CATEGORY, COUNT(*) as COUNT
      FROM RULE_SUPERVISION
      WHERE IS_DELETED = 0
      GROUP BY RULE_CATEGORY
    `

    const categoryStatsResult = await executeQuery(categoryStatsSql)
    const rulesByCategory: Record<RuleCategory, number> = {
      'COST_CONTROL': 0,
      'FRAUD_DETECTION': 0,
      'COMPLIANCE_CHECK': 0,
      'QUALITY_ASSURANCE': 0,
      'STATISTICAL_ANALYSIS': 0
    }

    categoryStatsResult.rows?.forEach((row: any) => {
      rulesByCategory[row.RULE_CATEGORY as RuleCategory] = row.COUNT
    })

    // 按严重程度统计
    const severityStatsSql = `
      SELECT SEVERITY_LEVEL, COUNT(*) as COUNT
      FROM RULE_SUPERVISION
      WHERE IS_DELETED = 0
      GROUP BY SEVERITY_LEVEL
    `

    const severityStatsResult = await executeQuery(severityStatsSql)
    const rulesBySeverity: Record<SeverityLevel, number> = {
      'LOW': 0,
      'MEDIUM': 0,
      'HIGH': 0,
      'CRITICAL': 0
    }

    severityStatsResult.rows?.forEach((row: any) => {
      rulesBySeverity[row.SEVERITY_LEVEL as SeverityLevel] = row.COUNT
    })

    // 执行统计
    const executionStatsSql = `
      SELECT
        COUNT(*) as TOTAL_EXECUTIONS,
        SUM(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) as SUCCESSFUL_EXECUTIONS,
        SUM(CASE WHEN EXECUTION_STATUS = 'FAILED' THEN 1 ELSE 0 END) as FAILED_EXECUTIONS,
        AVG(EXECUTION_DURATION) as AVG_EXECUTION_TIME
      FROM RULE_EXECUTION_LOG
      WHERE CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -3)
    `

    const executionStatsResult = await executeQuery(executionStatsSql)
    const executionStats = executionStatsResult.rows?.[0] || {}

    // 结果统计
    const resultStatsSql = `
      SELECT
        COUNT(*) as TOTAL_RESULTS,
        SUM(CASE WHEN RESULT_TYPE = 'VIOLATION' THEN 1 ELSE 0 END) as VIOLATION_RESULTS,
        SUM(CASE WHEN RESULT_TYPE = 'SUSPICIOUS' THEN 1 ELSE 0 END) as SUSPICIOUS_RESULTS,
        SUM(CASE WHEN RESULT_TYPE = 'NORMAL' THEN 1 ELSE 0 END) as NORMAL_RESULTS,
        AVG(CONFIDENCE_SCORE) as AVG_CONFIDENCE_SCORE
      FROM RULE_EXECUTION_RESULT
      WHERE CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -3)
    `

    const resultStatsResult = await executeQuery(resultStatsSql)
    const resultStats = resultStatsResult.rows?.[0] || {}

    // 月度趋势
    const monthlyTrendSql = `
      SELECT
        TO_CHAR(rel.CREATED_AT, 'YYYY-MM') as MONTH,
        COUNT(DISTINCT rel.ID) as EXECUTION_COUNT,
        COUNT(DISTINCT rer.ID) as VIOLATION_COUNT,
        AVG(rer.CONFIDENCE_SCORE) as AVG_CONFIDENCE_SCORE
      FROM RULE_EXECUTION_LOG rel
      LEFT JOIN RULE_EXECUTION_RESULT rer ON rel.ID = rer.EXECUTION_LOG_ID AND rer.RESULT_TYPE = 'VIOLATION'
      WHERE rel.CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -12)
      GROUP BY TO_CHAR(rel.CREATED_AT, 'YYYY-MM')
      ORDER BY MONTH
    `

    const monthlyTrendResult = await executeQuery(monthlyTrendSql)
    const monthlyTrend = (monthlyTrendResult.rows || []).map((row: any) => ({
      month: row.MONTH,
      executionCount: row.EXECUTION_COUNT,
      violationCount: row.VIOLATION_COUNT,
      avgConfidenceScore: row.AVG_CONFIDENCE_SCORE || 0,
    }))

    return {
      totalRules: basicStats.TOTAL_RULES || 0,
      activeRules: basicStats.ACTIVE_RULES || 0,
      inactiveRules: basicStats.INACTIVE_RULES || 0,
      rulesByType,
      rulesByCategory,
      rulesBySeverity,
      executionStats: {
        totalExecutions: executionStats.TOTAL_EXECUTIONS || 0,
        successfulExecutions: executionStats.SUCCESSFUL_EXECUTIONS || 0,
        failedExecutions: executionStats.FAILED_EXECUTIONS || 0,
        avgExecutionTime: executionStats.AVG_EXECUTION_TIME || 0,
      },
      resultStats: {
        totalResults: resultStats.TOTAL_RESULTS || 0,
        violationResults: resultStats.VIOLATION_RESULTS || 0,
        suspiciousResults: resultStats.SUSPICIOUS_RESULTS || 0,
        normalResults: resultStats.NORMAL_RESULTS || 0,
        avgConfidenceScore: resultStats.AVG_CONFIDENCE_SCORE || 0,
      },
      monthlyTrend,
    }
  } catch (error) {
    console.error('❌ 获取监管规则统计失败:', error)
    throw new Error('获取监管规则统计失败')
  }
}
