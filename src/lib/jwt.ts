import jwt from 'jsonwebtoken'
import { JWTPayload, User, UserRole } from '@/types/auth'

// JWT配置
const JWT_SECRET = process.env['JWT_SECRET'] || 'mediinspect-v2-secret-key'
const JWT_EXPIRES_IN = process.env['JWT_EXPIRES_IN'] || '24h'
const JWT_REFRESH_SECRET = process.env['JWT_REFRESH_SECRET'] || 'mediinspect-v2-refresh-secret'
const JWT_REFRESH_EXPIRES_IN = process.env['JWT_REFRESH_EXPIRES_IN'] || '7d'

/**
 * 生成访问令牌
 */
export function generateAccessToken(user: User): string {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
    userId: user.id,
    username: user.username,
    roles: user.roles.map(role => role.roleCode),
  }

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'mediinspect-v2',
    audience: 'mediinspect-users',
  } as jwt.SignOptions)
}

/**
 * 生成刷新令牌
 */
export function generateRefreshToken(userId: number): string {
  return jwt.sign(
    { userId, type: 'refresh' },
    JWT_REFRESH_SECRET,
    {
      expiresIn: JWT_REFRESH_EXPIRES_IN,
      issuer: 'mediinspect-v2',
      audience: 'mediinspect-users',
    } as jwt.SignOptions
  )
}

/**
 * 验证访问令牌
 */
export function verifyAccessToken(token: string): JWTPayload | null {
  // 检查是否在服务端环境
  if (typeof window !== 'undefined') {
    console.warn('⚠️ JWT验证应该在服务端进行')
    return null
  }

  try {
    if (!JWT_SECRET) {
      console.error('❌ JWT_SECRET未配置')
      return null
    }

    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'mediinspect-v2',
      audience: 'mediinspect-users',
    }) as JWTPayload

    return decoded
  } catch (error) {
    console.error('❌ 访问令牌验证失败:', error)
    return null
  }
}

/**
 * 验证刷新令牌
 */
export function verifyRefreshToken(token: string): { userId: number } | null {
  try {
    const decoded = jwt.verify(token, JWT_REFRESH_SECRET, {
      issuer: 'mediinspect-v2',
      audience: 'mediinspect-users',
    }) as any

    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type')
    }

    return { userId: decoded.userId }
  } catch (error) {
    console.error('❌ 刷新令牌验证失败:', error)
    return null
  }
}

/**
 * 从请求头中提取令牌
 */
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader) return null
  
  const parts = authHeader.split(' ')
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null
  }
  
  return parts[1] || null
}

/**
 * 检查令牌是否即将过期
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
  try {
    const decoded = jwt.decode(token) as JWTPayload
    if (!decoded || !decoded.exp) return true
    
    const now = Math.floor(Date.now() / 1000)
    const threshold = thresholdMinutes * 60
    
    return (decoded.exp - now) < threshold
  } catch (error) {
    return true
  }
}

/**
 * 获取令牌剩余时间（秒）
 */
export function getTokenRemainingTime(token: string): number {
  try {
    const decoded = jwt.decode(token) as JWTPayload
    if (!decoded || !decoded.exp) return 0
    
    const now = Math.floor(Date.now() / 1000)
    return Math.max(0, decoded.exp - now)
  } catch (error) {
    return 0
  }
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(userRoles: UserRole[], requiredRole: UserRole): boolean {
  if (typeof window !== 'undefined') {
    console.warn('⚠️ hasRole函数应该在服务端使用')
  }
  return userRoles.includes(requiredRole)
}

/**
 * 检查用户是否有任一指定角色
 */
export function hasAnyRole(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
  return requiredRoles.some(role => userRoles.includes(role))
}

/**
 * 检查用户是否有所有指定角色
 */
export function hasAllRoles(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
  return requiredRoles.every(role => userRoles.includes(role))
}

/**
 * 角色权限级别映射
 */
const ROLE_LEVELS: Record<UserRole, number> = {
  'ADMIN': 5,
  'SUPERVISOR': 4,
  'AUDITOR': 3,
  'OPERATOR': 2,
  'VIEWER': 1,
}

/**
 * 检查用户是否有足够的权限级别
 */
export function hasPermissionLevel(userRoles: UserRole[], requiredLevel: number): boolean {
  const userLevel = Math.max(...userRoles.map(role => ROLE_LEVELS[role] || 0))
  return userLevel >= requiredLevel
}

/**
 * 获取用户最高权限级别
 */
export function getUserPermissionLevel(userRoles: UserRole[]): number {
  return Math.max(...userRoles.map(role => ROLE_LEVELS[role] || 0))
}

/**
 * 生成令牌对
 */
export function generateTokenPair(user: User): {
  accessToken: string
  refreshToken: string
  expiresIn: number
} {
  const accessToken = generateAccessToken(user)
  const refreshToken = generateRefreshToken(user.id)
  const expiresIn = getTokenRemainingTime(accessToken)

  return {
    accessToken,
    refreshToken,
    expiresIn,
  }
}
