/**
 * 动态验证工具
 * 
 * @description 提供基于系统配置的动态验证功能，替代硬编码枚举验证
 * <AUTHOR> Agent
 */

import { z } from 'zod'
import { getAllActiveConfigItems } from '@/lib/system-config-service'

// 配置缓存
let configCache: Record<string, string[]> | null = null
let cacheTimestamp: number = 0
const CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

/**
 * 获取缓存的配置数据
 */
async function getCachedConfig(): Promise<Record<string, string[]>> {
  const now = Date.now()
  
  if (!configCache || (now - cacheTimestamp) > CACHE_TTL) {
    try {
      configCache = await getAllActiveConfigItems()
      cacheTimestamp = now
      console.log('🔄 动态配置缓存已刷新')
    } catch (error) {
      console.error('❌ 获取动态配置失败:', error)
      // 如果获取失败，使用空配置避免阻塞
      configCache = {}
    }
  }
  
  return configCache
}

/**
 * 清除配置缓存
 */
export function clearDynamicValidationCache(): void {
  configCache = null
  cacheTimestamp = 0
  console.log('🧹 动态验证缓存已清除')
}

/**
 * 创建动态枚举验证器
 * 
 * @param categoryCode 配置分类代码
 * @param errorMessage 自定义错误消息
 * @returns Zod枚举验证器
 */
export async function createDynamicEnum(
  categoryCode: string, 
  errorMessage?: string
): Promise<z.ZodEnum<[string, ...string[]]>> {
  const config = await getCachedConfig()
  const values = config[categoryCode] || []
  
  if (values.length === 0) {
    console.warn(`⚠️  配置分类 ${categoryCode} 没有找到有效值，使用空枚举`)
    // 返回一个永远不匹配的枚举，这样会触发验证错误
    return z.enum(['__INVALID__'] as [string, ...string[]], {
      errorMap: () => ({ 
        message: errorMessage || `配置分类 ${categoryCode} 没有有效的选项` 
      })
    })
  }
  
  // 确保至少有一个值（Zod枚举要求）
  const enumValues = values.length > 0 ? values as [string, ...string[]] : [''] as [string, ...string[]]
  
  return z.enum(enumValues, {
    errorMap: () => ({ 
      message: errorMessage || `请选择有效的${categoryCode}选项: ${values.join(', ')}` 
    })
  })
}

/**
 * 创建可选的动态枚举验证器
 * 
 * @param categoryCode 配置分类代码
 * @param errorMessage 自定义错误消息
 * @returns 可选的Zod枚举验证器
 */
export async function createOptionalDynamicEnum(
  categoryCode: string, 
  errorMessage?: string
): Promise<z.ZodOptional<z.ZodEnum<[string, ...string[]]>>> {
  const enumValidator = await createDynamicEnum(categoryCode, errorMessage)
  return enumValidator.optional()
}

/**
 * 验证单个值是否在配置分类中
 * 
 * @param categoryCode 配置分类代码
 * @param value 要验证的值
 * @returns 是否有效
 */
export async function validateConfigValue(categoryCode: string, value: string): Promise<boolean> {
  const config = await getCachedConfig()
  const values = config[categoryCode] || []
  return values.includes(value)
}

/**
 * 获取配置分类的所有有效值
 * 
 * @param categoryCode 配置分类代码
 * @returns 有效值数组
 */
export async function getConfigValues(categoryCode: string): Promise<string[]> {
  const config = await getCachedConfig()
  return config[categoryCode] || []
}

/**
 * 批量验证多个配置值
 * 
 * @param validations 验证配置数组
 * @returns 验证结果
 */
export async function validateMultipleConfigValues(
  validations: Array<{ categoryCode: string; value: string; fieldName?: string }>
): Promise<{ isValid: boolean; errors: string[] }> {
  const config = await getCachedConfig()
  const errors: string[] = []
  
  for (const validation of validations) {
    const { categoryCode, value, fieldName } = validation
    const values = config[categoryCode] || []
    
    if (!values.includes(value)) {
      const field = fieldName || categoryCode
      errors.push(`${field} 的值 "${value}" 不在有效选项中: ${values.join(', ')}`)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 创建动态验证schema构建器
 * 
 * @description 提供链式API来构建包含动态验证的schema
 */
export class DynamicSchemaBuilder {
  private schema: Record<string, any> = {}
  
  /**
   * 添加动态枚举字段
   */
  async addDynamicEnum(
    fieldName: string, 
    categoryCode: string, 
    options: {
      optional?: boolean
      errorMessage?: string
      transform?: (value: string) => any
    } = {}
  ): Promise<DynamicSchemaBuilder> {
    const enumValidator = options.optional 
      ? await createOptionalDynamicEnum(categoryCode, options.errorMessage)
      : await createDynamicEnum(categoryCode, options.errorMessage)
    
    this.schema[fieldName] = options.transform
      ? enumValidator.transform((value: string | undefined) => options.transform!(value!))
      : enumValidator
    
    return this
  }
  
  /**
   * 添加普通字段
   */
  addField(fieldName: string, validator: z.ZodTypeAny): DynamicSchemaBuilder {
    this.schema[fieldName] = validator
    return this
  }
  
  /**
   * 构建最终的schema
   */
  build(): z.ZodObject<any> {
    return z.object(this.schema)
  }
}

/**
 * 预热配置缓存
 * 
 * @description 在应用启动时调用，预先加载配置数据
 */
export async function warmupDynamicValidation(): Promise<void> {
  try {
    console.log('🔥 预热动态验证缓存...')
    await getCachedConfig()
    console.log('✅ 动态验证缓存预热完成')
  } catch (error) {
    console.error('❌ 动态验证缓存预热失败:', error)
  }
}

/**
 * 获取所有配置分类的统计信息
 */
export async function getConfigStatistics(): Promise<{
  totalCategories: number
  totalItems: number
  categories: Array<{ code: string; itemCount: number }>
}> {
  const config = await getCachedConfig()
  const categories = Object.entries(config).map(([code, items]) => ({
    code,
    itemCount: items.length
  }))
  
  return {
    totalCategories: categories.length,
    totalItems: categories.reduce((sum, cat) => sum + cat.itemCount, 0),
    categories
  }
}
