/**
 * 企业级错误处理和恢复机制
 */

import { monitoring, log } from './monitoring'

export interface ErrorContext {
  userId?: string
  requestId?: string
  operation?: string
  metadata?: Record<string, any>
  timestamp?: number
}

export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors?: string[]
}

export class AppError extends Error {
  public readonly code: string
  public readonly statusCode: number
  public readonly isOperational: boolean
  public readonly context: ErrorContext

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true,
    context: ErrorContext = {}
  ) {
    super(message)
    this.name = this.constructor.name
    this.code = code
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.context = { ...context, timestamp: Date.now() }

    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(message, 'VALIDATION_ERROR', 400, true, context)
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败', context: ErrorContext = {}) {
    super(message, 'AUTHENTICATION_ERROR', 401, true, context)
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足', context: ErrorContext = {}) {
    super(message, 'AUTHORIZATION_ERROR', 403, true, context)
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = '资源', context: ErrorContext = {}) {
    super(`${resource}不存在`, 'NOT_FOUND_ERROR', 404, true, context)
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, context: ErrorContext = {}) {
    super(message, 'DATABASE_ERROR', 500, true, context)
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, context: ErrorContext = {}) {
    super(`外部服务 ${service} 错误: ${message}`, 'EXTERNAL_SERVICE_ERROR', 502, true, context)
  }
}

/**
 * 错误处理器类
 */
class ErrorHandler {
  /**
   * 处理错误
   */
  handleError(error: Error, context: ErrorContext = {}): void {
    const isAppError = error instanceof AppError
    const errorInfo = {
      name: error.name,
      message: error.message,
      code: isAppError ? error.code : 'UNKNOWN_ERROR',
      statusCode: isAppError ? error.statusCode : 500,
      isOperational: isAppError ? error.isOperational : false,
      stack: error.stack,
      context: isAppError ? { ...error.context, ...context } : context
    }

    // 记录错误日志
    log({
      level: this.getLogLevel(errorInfo.statusCode),
      message: `Error: ${errorInfo.message}`,
      context: errorInfo
    })

    // 记录错误指标
    monitoring.recordMetric({
      name: 'error_count',
      value: 1,
      tags: {
        error_code: errorInfo.code,
        error_type: error.name,
        status_code: errorInfo.statusCode.toString()
      }
    })

    // 非操作性错误需要立即通知
    if (!errorInfo.isOperational) {
      this.notifyDevelopmentTeam(errorInfo)
    }

    // 严重错误需要特殊处理
    if (errorInfo.statusCode >= 500) {
      this.handleCriticalError(errorInfo)
    }
  }

  /**
   * 重试机制
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2
    },
    context: ErrorContext = {}
  ): Promise<T> {
    let lastError: Error | null = null
    let attempt = 0

    while (attempt < config.maxAttempts) {
      attempt++
      
      try {
        const timer = monitoring.startTimer('retry_operation', {
          attempt,
          maxAttempts: config.maxAttempts,
          ...context
        })

        const result = await operation()
        timer.end(true)
        
        // 如果不是第一次尝试，记录恢复
        if (attempt > 1) {
          log({
            level: 'info',
            message: `Operation succeeded after ${attempt} attempts`,
            context: { ...context, attempt, maxAttempts: config.maxAttempts }
          })
        }

        return result
      } catch (error) {
        lastError = error as Error
        
        // 检查是否应该重试
        if (!this.shouldRetry(error as Error, config, attempt)) {
          break
        }

        // 计算延迟时间
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
          config.maxDelay
        )

        log({
          level: 'warn',
          message: `Operation failed, retrying in ${delay}ms (attempt ${attempt}/${config.maxAttempts})`,
          context: {
            error: (error as Error).message,
            delay,
            attempt,
            maxAttempts: config.maxAttempts,
            ...context
          }
        })

        await this.sleep(delay)
      }
    }

    // 所有重试都失败了
    log({
      level: 'error',
      message: `Operation failed after ${config.maxAttempts} attempts`,
      context: {
        error: lastError?.message || 'Unknown error',
        attempts: config.maxAttempts,
        ...context
      }
    })

    throw lastError || new Error('Operation failed with unknown error')
  }

  /**
   * 断路器模式
   */
  createCircuitBreaker<T>(
    operation: () => Promise<T>,
    options: {
      failureThreshold: number
      resetTimeout: number
      monitoringPeriod: number
    } = {
      failureThreshold: 5,
      resetTimeout: 60000,
      monitoringPeriod: 10000
    }
  ) {
    let failures = 0
    let lastFailureTime = 0
    let state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'

    return async (...args: any[]): Promise<T> => {
      const now = Date.now()

      // 检查是否应该重置
      if (state === 'OPEN' && now - lastFailureTime > options.resetTimeout) {
        state = 'HALF_OPEN'
        failures = 0
      }

      // 断路器打开状态
      if (state === 'OPEN') {
        throw new AppError(
          '服务暂时不可用，请稍后重试',
          'CIRCUIT_BREAKER_OPEN',
          503,
          true,
          { metadata: { circuitBreakerState: state, failures } }
        )
      }

      try {
        const result = await operation()
        
        // 成功时重置计数器
        if (state === 'HALF_OPEN') {
          state = 'CLOSED'
          failures = 0
        }

        return result
      } catch (error) {
        failures++
        lastFailureTime = now

        // 检查是否应该打开断路器
        if (failures >= options.failureThreshold) {
          state = 'OPEN'
          log({
            level: 'error',
            message: 'Circuit breaker opened due to repeated failures',
            context: {
              failures,
              threshold: options.failureThreshold,
              error: (error as Error).message
            }
          })
        }

        throw error
      }
    }
  }

  /**
   * 优雅降级
   */
  async withFallback<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
    context: ErrorContext = {}
  ): Promise<T> {
    try {
      return await primaryOperation()
    } catch (error) {
      log({
        level: 'warn',
        message: 'Primary operation failed, using fallback',
        context: {
          error: (error as Error).message,
          ...context
        }
      })

      try {
        return await fallbackOperation()
      } catch (fallbackError) {
        log({
          level: 'error',
          message: 'Both primary and fallback operations failed',
          context: {
            primaryError: (error as Error).message,
            fallbackError: (fallbackError as Error).message,
            ...context
          }
        })
        throw error // 抛出原始错误
      }
    }
  }

  private getLogLevel(statusCode: number): 'debug' | 'info' | 'warn' | 'error' | 'fatal' {
    if (statusCode >= 500) return 'error'
    if (statusCode >= 400) return 'warn'
    return 'info'
  }

  private shouldRetry(error: Error, config: RetryConfig, attempt: number): boolean {
    if (attempt >= config.maxAttempts) return false

    // 检查是否是可重试的错误
    if (config.retryableErrors) {
      const errorCode = error instanceof AppError ? error.code : error.name
      return config.retryableErrors.includes(errorCode)
    }

    // 默认重试策略：网络错误和5xx错误
    if (error instanceof AppError) {
      return error.statusCode >= 500 || error.code === 'NETWORK_ERROR'
    }

    return true
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private notifyDevelopmentTeam(errorInfo: any): void {
    // 实现通知逻辑：Slack、邮件、PagerDuty等
    console.error('🚨 CRITICAL ERROR - Development team notified:', errorInfo)
  }

  private handleCriticalError(errorInfo: any): void {
    // 实现关键错误处理：自动恢复、服务降级等
    console.error('💀 CRITICAL ERROR:', errorInfo)
  }
}

// 单例实例
export const errorHandler = new ErrorHandler()

// 便捷方法
export const handleError = (error: Error, context?: ErrorContext) => errorHandler.handleError(error, context)
export const withRetry = <T>(operation: () => Promise<T>, config?: RetryConfig, context?: ErrorContext) => 
  errorHandler.withRetry(operation, config, context)
export const withFallback = <T>(primary: () => Promise<T>, fallback: () => Promise<T>, context?: ErrorContext) =>
  errorHandler.withFallback(primary, fallback, context)

export default errorHandler
