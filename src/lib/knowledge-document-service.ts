/**
 * 知识库文档服务
 * 实现P0-008数据库设计与API实现一致性修复目标：
 * - 补充缺失的知识库文档管理API
 * - 实现版本控制和访问日志功能
 * - 确保数据库表与API接口完全匹配
 */

import { executeQuery } from '@/lib/database'

export interface KnowledgeDocument {
  id: number
  categoryId: number
  documentCode: string
  title: string
  content: string
  documentType: string
  status: string
  keywords: string
  authorId: number
  authorName?: string
  categoryName?: string
  viewCount: number
  downloadCount: number
  isPublic: boolean
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

export interface KnowledgeVersionHistory {
  id: number
  documentId: number
  versionNumber: number
  content: string
  changeDescription: string
  createdBy: number
  createdByName?: string
  createdAt: Date
}

export interface KnowledgeAccessLog {
  id: number
  documentId: number
  userId: number
  userName?: string
  accessType: string
  ipAddress: string
  accessTime: Date
}

export interface DocumentQueryParams {
  page?: number
  pageSize?: number
  categoryId?: number
  documentType?: string
  documentStatus?: string
  search?: string
  authorId?: number
  isPublic?: boolean
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

export interface DocumentCreateRequest {
  categoryId: number
  documentCode: string
  title: string
  content: string
  documentType: string
  status?: string
  keywords?: string
  isPublic?: boolean
}

export interface DocumentUpdateRequest {
  categoryId?: number
  title?: string
  content?: string
  documentType?: string
  status?: string
  keywords?: string
  isPublic?: boolean
  changeDescription?: string
}

/**
 * 获取知识库文档列表
 */
export async function getKnowledgeDocuments(params: DocumentQueryParams = {}) {
  const {
    page = 1,
    pageSize = 20,
    categoryId,
    documentType,
    documentStatus,
    search,
    authorId,
    isPublic,
    sortBy = 'CREATED_AT',
    sortOrder = 'DESC'
  } = params

  const offset = (page - 1) * pageSize
  const conditions: string[] = ['kd.IS_DELETED = 0']
  const queryParams: any = { offset, pageSize }

  // 构建查询条件
  if (categoryId) {
    conditions.push('kd.CATEGORY_ID = :categoryId')
    queryParams.categoryId = categoryId
  }

  if (documentType) {
    conditions.push('kd.DOCUMENT_TYPE = :documentType')
    queryParams.documentType = documentType
  }

  if (documentStatus) {
    conditions.push('kd.DOCUMENT_STATUS = :documentStatus')
    queryParams.documentStatus = documentStatus
  }

  if (authorId) {
    conditions.push('kd.AUTHOR_ID = :authorId')
    queryParams.authorId = authorId
  }

  if (isPublic !== undefined) {
    conditions.push('kd.IS_PUBLIC = :isPublic')
    queryParams.isPublic = isPublic ? 1 : 0
  }

  if (search) {
    conditions.push('(UPPER(kd.TITLE) LIKE UPPER(:search) OR UPPER(kd.CONTENT) LIKE UPPER(:searchContent) OR UPPER(kd.KEYWORDS) LIKE UPPER(:searchKeywords))')
    queryParams.search = `%${search}%`
    queryParams.searchContent = `%${search}%`
    queryParams.searchKeywords = `%${search}%`
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM KNOWLEDGE_DOCUMENT kd
    ${whereClause}
  `

  // 为计数查询创建单独的参数（不包含分页参数）
  const countParams: any = {}
  if (search) {
    countParams.search = `%${search}%`
    countParams.searchCode = `%${search}%`
    countParams.searchTitle = `%${search}%`
    countParams.searchContent = `%${search}%`
  }
  if (categoryId) {
    countParams.categoryId = categoryId
  }
  if (documentType) {
    countParams.documentType = documentType
  }
  if (documentStatus) {
    countParams.documentStatus = documentStatus
  }
  if (authorId) {
    countParams.authorId = authorId
  }
  if (isPublic !== undefined) {
    countParams.isPublic = isPublic ? 1 : 0
  }

  const countResult = await executeQuery(countSql, countParams)
  const total = countResult.rows[0]?.TOTAL || 0

  // 查询数据
  const dataSql = `
    SELECT
      kd.ID, kd.CATEGORY_ID, kd.DOCUMENT_CODE, kd.TITLE,
      kd.CONTENT, kd.DOCUMENT_TYPE, kd.STATUS,
      kd.KEYWORDS, kd.CREATED_BY as AUTHOR_ID, kd.VIEW_COUNT, kd.DOWNLOAD_COUNT,
      kd.IS_PUBLIC, kd.IS_DELETED, kd.CREATED_AT, kd.UPDATED_AT,
      kd.PUBLISHED_AT,
      kc.CATEGORY_NAME,
      u.REAL_NAME as AUTHOR_NAME
    FROM KNOWLEDGE_DOCUMENT kd
    LEFT JOIN KNOWLEDGE_CATEGORY kc ON kd.CATEGORY_ID = kc.ID
    LEFT JOIN USER_INFO u ON kd.CREATED_BY = u.ID
    ${whereClause}
    ORDER BY kd.${sortBy} ${sortOrder}
    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
  `

  const dataResult = await executeQuery(dataSql, queryParams)

  const items = dataResult.rows.map(row => ({
    id: row.ID,
    categoryId: row.CATEGORY_ID,
    documentCode: row.DOCUMENT_CODE,
    title: row.TITLE,
    content: row.CONTENT,
    documentType: row.DOCUMENT_TYPE,
    status: row.STATUS,
    keywords: row.KEYWORDS,
    authorId: row.AUTHOR_ID,
    authorName: row.AUTHOR_NAME,
    categoryName: row.CATEGORY_NAME,
    viewCount: row.VIEW_COUNT || 0,
    downloadCount: row.DOWNLOAD_COUNT || 0,
    isPublic: row.IS_PUBLIC === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    publishedAt: row.PUBLISHED_AT
  }))

  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 根据ID获取知识库文档详情
 */
export async function getKnowledgeDocumentById(id: number): Promise<KnowledgeDocument | null> {
  const sql = `
    SELECT
      kd.ID, kd.CATEGORY_ID, kd.DOCUMENT_CODE, kd.TITLE,
      kd.CONTENT, kd.DOCUMENT_TYPE, kd.STATUS,
      kd.KEYWORDS, kd.CREATED_BY as AUTHOR_ID, kd.VIEW_COUNT, kd.DOWNLOAD_COUNT,
      kd.IS_PUBLIC, kd.IS_DELETED, kd.CREATED_AT, kd.UPDATED_AT,
      kd.PUBLISHED_AT,
      kc.CATEGORY_NAME,
      u.REAL_NAME as AUTHOR_NAME
    FROM KNOWLEDGE_DOCUMENT kd
    LEFT JOIN KNOWLEDGE_CATEGORY kc ON kd.CATEGORY_ID = kc.ID
    LEFT JOIN USER_INFO u ON kd.CREATED_BY = u.ID
    WHERE kd.ID = :id AND kd.IS_DELETED = 0
  `

  const result = await executeQuery(sql, { id })

  if (result.rows.length === 0) {
    return null
  }

  const row = result.rows[0]
  return {
    id: row.ID,
    categoryId: row.CATEGORY_ID,
    documentCode: row.DOCUMENT_CODE,
    title: row.TITLE,
    content: row.CONTENT,
    documentType: row.DOCUMENT_TYPE,
    status: row.STATUS,
    keywords: row.KEYWORDS,
    authorId: row.AUTHOR_ID,
    authorName: row.AUTHOR_NAME,
    categoryName: row.CATEGORY_NAME,
    viewCount: row.VIEW_COUNT || 0,
    downloadCount: row.DOWNLOAD_COUNT || 0,
    isPublic: row.IS_PUBLIC === 1,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT,
    publishedAt: row.PUBLISHED_AT
  }
}

/**
 * 创建知识库文档
 */
export async function createKnowledgeDocument(
  data: DocumentCreateRequest,
  authorId: number
): Promise<number> {
  // 先获取下一个ID
  const getIdSql = 'SELECT SEQ_KNOWLEDGE_DOCUMENT.NEXTVAL as NEXT_ID FROM DUAL'
  const idResult = await executeQuery(getIdSql, {})
  const documentId = idResult.rows[0].NEXT_ID

  const sql = `
    INSERT INTO KNOWLEDGE_DOCUMENT (
      ID, CATEGORY_ID, DOCUMENT_CODE, TITLE, CONTENT,
      DOCUMENT_TYPE, STATUS, KEYWORDS, CREATED_BY, VIEW_COUNT,
      DOWNLOAD_COUNT, IS_PUBLIC, IS_DELETED, CREATED_AT, UPDATED_AT
    ) VALUES (
      :documentId, :categoryId, :documentCode, :title, :content,
      :documentType, :status, :keywords, :authorId, 0,
      0, :isPublic, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
  `

  const params = {
    documentId,
    categoryId: data.categoryId,
    documentCode: data.documentCode,
    title: data.title,
    content: data.content,
    documentType: data.documentType,
    status: data.status || 'DRAFT',
    keywords: data.keywords || '',
    authorId: authorId,
    isPublic: data.isPublic ? 1 : 0
  }

  await executeQuery(sql, params)

  // 创建初始版本记录
  await createVersionHistory(documentId, '1.0', data.title, data.content, '初始版本', authorId)

  return documentId
}

/**
 * 更新知识库文档
 */
export async function updateKnowledgeDocument(
  id: number,
  data: DocumentUpdateRequest,
  userId: number
): Promise<void> {
  // 获取当前文档信息
  const currentDoc = await getKnowledgeDocumentById(id)
  if (!currentDoc) {
    throw new Error('文档不存在')
  }

  const updateFields: string[] = []
  const params: any = { id }

  if (data.categoryId !== undefined) {
    updateFields.push('CATEGORY_ID = :categoryId')
    params.categoryId = data.categoryId
  }

  if (data.title !== undefined) {
    updateFields.push('TITLE = :title')
    params.title = data.title
  }

  if (data.content !== undefined) {
    updateFields.push('CONTENT = :content')
    params.content = data.content
  }

  if (data.documentType !== undefined) {
    updateFields.push('DOCUMENT_TYPE = :documentType')
    params.documentType = data.documentType
  }

  if (data.status !== undefined) {
    updateFields.push('STATUS = :status')
    params.status = data.status
  }

  if (data.keywords !== undefined) {
    updateFields.push('KEYWORDS = :keywords')
    params.keywords = data.keywords
  }

  if (data.isPublic !== undefined) {
    updateFields.push('IS_PUBLIC = :isPublic')
    params.isPublic = data.isPublic ? 1 : 0
  }

  if (updateFields.length === 0) {
    return
  }

  updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP')

  const sql = `
    UPDATE KNOWLEDGE_DOCUMENT 
    SET ${updateFields.join(', ')}
    WHERE ID = :id AND IS_DELETED = 0
  `

  await executeQuery(sql, params)

  // 如果内容有变化，创建版本记录
  if (data.content && data.content !== currentDoc.content) {
    // 获取下一个版本号（基于现有版本数量）
    const versionSql = `
      SELECT COUNT(*) + 1 as NEXT_VERSION
      FROM KNOWLEDGE_VERSION_HISTORY
      WHERE DOCUMENT_ID = :id
    `
    const versionResult = await executeQuery(versionSql, { id })
    const nextVersionNum = versionResult.rows[0]?.NEXT_VERSION || 1
    const nextVersion = `${nextVersionNum}.0`

    await createVersionHistory(
      id,
      nextVersion,
      data.title || currentDoc.title,
      data.content,
      data.changeDescription || '文档更新',
      userId
    )
  }
}

/**
 * 创建版本历史记录
 */
export async function createVersionHistory(
  documentId: number,
  versionNumber: string,
  title: string,
  content: string,
  changeDescription: string,
  userId: number
): Promise<void> {
  // 先获取下一个ID
  const getIdSql = 'SELECT SEQ_KNOWLEDGE_VERSION_HISTORY.NEXTVAL as NEXT_ID FROM DUAL'
  const idResult = await executeQuery(getIdSql, {})
  const historyId = idResult.rows[0].NEXT_ID

  const sql = `
    INSERT INTO KNOWLEDGE_VERSION_HISTORY (
      ID, DOCUMENT_ID, VERSION_NUMBER, TITLE, CONTENT, CHANGE_DESCRIPTION,
      CREATED_BY, CREATED_AT, UPDATED_AT
    ) VALUES (
      :historyId, :documentId, :versionNumber, :title, :content, :changeDescription,
      :userId, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
  `

  await executeQuery(sql, {
    historyId,
    documentId,
    versionNumber,
    title,
    content,
    changeDescription,
    userId
  })
}

/**
 * 记录访问日志
 */
export async function logDocumentAccess(
  documentId: number,
  userId: number,
  accessType: string,
  ipAddress: string
): Promise<void> {
  // 先获取下一个ID
  const getIdSql = 'SELECT SEQ_KNOWLEDGE_ACCESS_LOG.NEXTVAL as NEXT_ID FROM DUAL'
  const idResult = await executeQuery(getIdSql, {})
  const logId = idResult.rows[0].NEXT_ID

  const sql = `
    INSERT INTO KNOWLEDGE_ACCESS_LOG (
      ID, DOCUMENT_ID, USER_ID, ACCESS_TYPE, IP_ADDRESS,
      ACCESSED_AT, CREATED_AT
    ) VALUES (
      :logId, :documentId, :userId, :accessType, :ipAddress,
      CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
  `

  await executeQuery(sql, {
    logId,
    documentId,
    userId,
    accessType,
    ipAddress
  })

  // 更新文档的访问时间和计数
  const updateSql = `
    UPDATE KNOWLEDGE_DOCUMENT
    SET
      VIEW_COUNT = VIEW_COUNT + 1,
      UPDATED_AT = CURRENT_TIMESTAMP
    WHERE ID = :documentId
  `

  await executeQuery(updateSql, { documentId })
}

/**
 * 删除知识库文档（软删除）
 */
export async function deleteKnowledgeDocument(id: number, deletedBy: number): Promise<void> {
  try {
    const sql = `
      UPDATE KNOWLEDGE_DOCUMENT
      SET IS_DELETED = 1, UPDATED_BY = :deletedBy, UPDATED_AT = CURRENT_TIMESTAMP
      WHERE ID = :id AND IS_DELETED = 0
    `

    await executeQuery(sql, { id, deletedBy })
  } catch (error) {
    console.error('❌ 删除知识库文档失败:', error)
    throw new Error('删除知识库文档失败')
  }
}
