/**
 * 企业级数据验证和清理系统
 */

import { z } from 'zod'
import { log } from './monitoring'

/**
 * 中国身份证号验证
 */
export const chineseIdCardSchema = z.string().refine((value) => {
  if (!/^\d{17}[\dXx]$/.test(value)) return false
  
  // 校验码验证
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
  
  let sum = 0
  for (let i = 0; i < 17; i++) {
    const digit = value[i]
    const weight = weights[i]
    if (!digit || weight === undefined) return false
    sum += parseInt(digit) * weight
  }

  const checkCode = checkCodes[sum % 11]
  const lastChar = value[17]
  if (!lastChar) return false
  return lastChar.toUpperCase() === checkCode
}, '身份证号格式不正确')

/**
 * 中国手机号验证
 */
export const chinesePhoneSchema = z.string().refine((value) => {
  return /^1[3-9]\d{9}$/.test(value)
}, '手机号格式不正确')

/**
 * 医保卡号验证
 */
export const medicalCardSchema = z.string().refine((value) => {
  // 医保卡号通常为18位数字
  return /^\d{18}$/.test(value)
}, '医保卡号格式不正确')

/**
 * 医疗机构编码验证
 */
export const medicalInstitutionCodeSchema = z.string().refine((value) => {
  // 医疗机构编码格式验证
  return /^[A-Z0-9]{10,20}$/.test(value)
}, '医疗机构编码格式不正确')

/**
 * 药品编码验证
 */
export const drugCodeSchema = z.string().refine((value) => {
  // 药品编码格式验证
  return /^[A-Z0-9]{8,15}$/.test(value)
}, '药品编码格式不正确')

/**
 * 金额验证（精确到分）
 */
export const amountSchema = z.number().refine((value) => {
  // 检查是否为有效金额（最多2位小数）
  return Number.isFinite(value) && value >= 0 && Math.round(value * 100) === value * 100
}, '金额格式不正确')

/**
 * 用户数据验证模式
 */
export const userValidationSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  
  realName: z.string()
    .min(2, '真实姓名至少2个字符')
    .max(10, '真实姓名最多10个字符')
    .regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '真实姓名只能包含中文、英文和空格'),
  
  email: z.string().email('邮箱格式不正确'),
  
  phone: chinesePhoneSchema,
  
  idCard: chineseIdCardSchema.optional(),
  
  password: z.string()
    .min(8, '密码至少8个字符')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           '密码必须包含大小写字母、数字和特殊字符'),
  
  department: z.string().min(1, '部门不能为空').max(50, '部门名称过长'),
  
  position: z.string().min(1, '职位不能为空').max(30, '职位名称过长')
})

/**
 * 医疗案例数据验证模式
 */
export const medicalCaseValidationSchema = z.object({
  patientName: z.string()
    .min(2, '患者姓名至少2个字符')
    .max(10, '患者姓名最多10个字符')
    .regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/, '患者姓名格式不正确'),
  
  patientIdCard: chineseIdCardSchema,
  
  medicalCardNumber: medicalCardSchema,
  
  institutionCode: medicalInstitutionCodeSchema,
  
  institutionName: z.string().min(1, '医疗机构名称不能为空').max(100, '机构名称过长'),
  
  visitDate: z.string().datetime('就诊日期格式不正确'),
  
  diagnosis: z.string().min(1, '诊断信息不能为空').max(500, '诊断信息过长'),
  
  totalAmount: amountSchema,
  
  insuranceAmount: amountSchema,
  
  personalAmount: amountSchema,
  
  status: z.enum(['pending', 'approved', 'rejected', 'under_review'], {
    errorMap: () => ({ message: '案例状态不正确' })
  })
})

/**
 * 数据清理工具
 */
export class DataSanitizer {
  /**
   * 清理HTML标签
   */
  static sanitizeHtml(input: string): string {
    return input.replace(/<[^>]*>/g, '')
  }

  /**
   * 清理SQL注入风险字符
   */
  static sanitizeSql(input: string): string {
    const dangerous = ['--', ';', '/*', '*/', 'xp_', 'sp_', 'exec', 'execute', 'drop', 'delete', 'insert', 'update']
    let sanitized = input
    
    dangerous.forEach(pattern => {
      const regex = new RegExp(pattern, 'gi')
      sanitized = sanitized.replace(regex, '')
    })
    
    return sanitized.trim()
  }

  /**
   * 清理XSS风险字符
   */
  static sanitizeXss(input: string): string {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi
    ]
    
    let sanitized = input
    xssPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '')
    })
    
    return sanitized
  }

  /**
   * 清理敏感信息
   */
  static maskSensitiveData(input: string, type: 'phone' | 'idCard' | 'email' | 'bankCard'): string {
    switch (type) {
      case 'phone':
        return input.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      
      case 'idCard':
        return input.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
      
      case 'email':
        return input.replace(/(.{2}).*(@.*)/, '$1***$2')
      
      case 'bankCard':
        return input.replace(/(\d{4})\d+(\d{4})/, '$1****$2')
      
      default:
        return input
    }
  }

  /**
   * 标准化文本
   */
  static normalizeText(input: string): string {
    return input
      .trim()
      .replace(/\s+/g, ' ') // 多个空格替换为单个空格
      .replace(/[\r\n\t]/g, ' ') // 换行符和制表符替换为空格
  }

  /**
   * 清理数字输入
   */
  static sanitizeNumber(input: string): number | null {
    const cleaned = input.replace(/[^\d.-]/g, '')
    const number = parseFloat(cleaned)
    return isNaN(number) ? null : number
  }

  /**
   * 清理金额输入
   */
  static sanitizeAmount(input: string): number | null {
    const number = this.sanitizeNumber(input)
    if (number === null || number < 0) return null
    
    // 保留两位小数
    return Math.round(number * 100) / 100
  }
}

/**
 * 验证器类
 */
export class Validator {
  /**
   * 验证数据并返回清理后的结果
   */
  static async validate<T>(
    schema: z.ZodSchema<T>,
    data: unknown,
    context?: { operation?: string; userId?: string }
  ): Promise<{ success: true; data: T } | { success: false; errors: string[] }> {
    try {
      const validatedData = schema.parse(data)
      
      log({
        level: 'debug',
        message: 'Data validation successful',
        context: {
          operation: context?.operation,
          userId: context?.userId,
          dataKeys: Object.keys(data as object || {})
        }
      })
      
      return { success: true, data: validatedData }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        
        log({
          level: 'warn',
          message: 'Data validation failed',
          context: {
            operation: context?.operation,
            userId: context?.userId,
            errors,
            invalidData: this.sanitizeLogData(data)
          }
        })
        
        return { success: false, errors }
      }
      
      log({
        level: 'error',
        message: 'Unexpected validation error',
        context: {
          operation: context?.operation,
          userId: context?.userId,
          error: (error as Error).message
        }
      })
      
      return { success: false, errors: ['验证过程中发生未知错误'] }
    }
  }

  /**
   * 批量验证
   */
  static async validateBatch<T>(
    schema: z.ZodSchema<T>,
    dataArray: unknown[],
    context?: { operation?: string; userId?: string }
  ): Promise<{
    success: boolean
    validData: T[]
    errors: Array<{ index: number; errors: string[] }>
  }> {
    const validData: T[] = []
    const errors: Array<{ index: number; errors: string[] }> = []

    for (let i = 0; i < dataArray.length; i++) {
      const result = await this.validate(schema, dataArray[i], {
        ...context,
        operation: `${context?.operation}_batch_${i}`
      })

      if (result.success) {
        validData.push(result.data)
      } else {
        errors.push({ index: i, errors: result.errors })
      }
    }

    return {
      success: errors.length === 0,
      validData,
      errors
    }
  }

  /**
   * 清理日志数据中的敏感信息
   */
  private static sanitizeLogData(data: unknown): any {
    if (typeof data !== 'object' || data === null) {
      return data
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'idCard', 'phone']
    const sanitized: any = {}

    for (const [key, value] of Object.entries(data)) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '***REDACTED***'
      } else {
        sanitized[key] = value
      }
    }

    return sanitized
  }
}

/**
 * 常用验证规则
 */
export const commonValidations = {
  // 中文姓名
  chineseName: z.string().regex(/^[\u4e00-\u9fa5]{2,10}$/, '请输入2-10个中文字符'),
  
  // 英文姓名
  englishName: z.string().regex(/^[a-zA-Z\s]{2,50}$/, '请输入2-50个英文字符'),
  
  // 用户名
  username: z.string().regex(/^[a-zA-Z0-9_]{3,20}$/, '用户名只能包含字母、数字和下划线，3-20个字符'),
  
  // 强密码
  strongPassword: z.string().regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    '密码必须包含大小写字母、数字和特殊字符，至少8位'
  ),
  
  // 正整数
  positiveInteger: z.number().int().positive('必须为正整数'),
  
  // 非负数
  nonNegativeNumber: z.number().min(0, '不能为负数'),
  
  // 日期字符串
  dateString: z.string().datetime('日期格式不正确'),
  
  // URL
  url: z.string().url('URL格式不正确'),
  
  // 颜色代码
  colorCode: z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色代码格式不正确')
}

export default Validator
