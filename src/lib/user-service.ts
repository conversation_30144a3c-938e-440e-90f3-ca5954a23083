import { executeQuery, executeTransaction, buildPaginationSQL } from '@/lib/database'
import { hashPassword, verifyPassword } from '@/lib/password'
import oracledb from 'oracledb'
import {
  User,
  Role,
  UserStatus,
  UserRole,
  LoginRequest,
  RegisterRequest,
  UserListParams,
  UserListResponse,
  UserUpdateRequest,
  Permission,
  RoleCreateRequest,
  RoleUpdateRequest
} from '@/types/auth'

/**
 * 根据用户ID查找用户
 */
export async function getUserById(userId: number): Promise<User | null> {
  try {
    const sql = `
      SELECT
        u.ID,
        u.USERNAME,
        u.PASSWORD_HASH,
        u.REAL_NAME,
        u.EMAIL,
        u.DEPARTMENT,
        u.POSITION,
        u.STATUS,
        u.CREATED_AT,
        u.UPDATED_AT,
        u.LAST_LOGIN_AT
      FROM USER_INFO u
      WHERE u.ID = :userId
        AND u.IS_DELETED = 0
    `

    const result = await executeQuery(sql, { userId })

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const userRow = result.rows[0] as any

    // 获取用户角色
    const roles = await getUserRoles(userId)

    return {
      id: userRow.ID,
      username: userRow.USERNAME,
      realName: userRow.REAL_NAME,
      email: userRow.EMAIL,
      department: userRow.DEPARTMENT,
      position: userRow.POSITION,
      status: userRow.STATUS as UserStatus,
      createdAt: userRow.CREATED_AT,
      updatedAt: userRow.UPDATED_AT,
      lastLoginAt: userRow.LAST_LOGIN_AT,
      roles
    }
  } catch (error) {
    console.error('获取用户失败:', error)
    throw new Error('获取用户失败')
  }
}

/**
 * 根据用户名查找用户
 */
export async function findUserByUsername(username: string): Promise<User | null> {
  try {
    const sql = `
      SELECT 
        u.ID,
        u.USERNAME,
        u.PASSWORD_HASH,
        u.REAL_NAME,
        u.EMAIL,
        u.DEPARTMENT,
        u.POSITION,
        u.STATUS,
        u.CREATED_AT,
        u.UPDATED_AT,
        u.LAST_LOGIN_AT
      FROM USER_INFO u
      WHERE u.USERNAME = :username 
        AND u.IS_DELETED = 0
    `
    
    const result = await executeQuery(sql, { username })
    
    if (!result.rows || result.rows.length === 0) {
      return null
    }
    
    const userRow = result.rows[0] as any
    
    // 获取用户角色
    const roles = await getUserRoles(userRow.ID)
    
    return {
      id: userRow.ID,
      username: userRow.USERNAME,
      realName: userRow.REAL_NAME,
      email: userRow.EMAIL,
      department: userRow.DEPARTMENT,
      position: userRow.POSITION,
      status: userRow.STATUS as UserStatus,
      createdAt: userRow.CREATED_AT,
      updatedAt: userRow.UPDATED_AT,
      lastLoginAt: userRow.LAST_LOGIN_AT,
      roles,
    }
  } catch (error) {
    console.error('❌ 查找用户失败:', error)
    throw new Error('查找用户失败')
  }
}

/**
 * 根据用户ID查找用户
 */
export async function findUserById(userId: number): Promise<User | null> {
  try {
    const sql = `
      SELECT 
        u.ID,
        u.USERNAME,
        u.REAL_NAME,
        u.EMAIL,
        u.DEPARTMENT,
        u.POSITION,
        u.STATUS,
        u.CREATED_AT,
        u.UPDATED_AT,
        u.LAST_LOGIN_AT
      FROM USER_INFO u
      WHERE u.ID = :userId 
        AND u.IS_DELETED = 0
    `
    
    const result = await executeQuery(sql, { userId })
    
    if (!result.rows || result.rows.length === 0) {
      return null
    }
    
    const userRow = result.rows[0] as any
    
    // 获取用户角色
    const roles = await getUserRoles(userRow.ID)
    
    return {
      id: userRow.ID,
      username: userRow.USERNAME,
      realName: userRow.REAL_NAME,
      email: userRow.EMAIL,
      department: userRow.DEPARTMENT,
      position: userRow.POSITION,
      status: userRow.STATUS as UserStatus,
      createdAt: userRow.CREATED_AT,
      updatedAt: userRow.UPDATED_AT,
      lastLoginAt: userRow.LAST_LOGIN_AT,
      roles,
    }
  } catch (error) {
    console.error('❌ 查找用户失败:', error)
    throw new Error('查找用户失败')
  }
}

/**
 * 获取用户角色
 */
export async function getUserRoles(userId: number): Promise<Role[]> {
  try {
    const sql = `
      SELECT 
        r.ID,
        r.ROLE_CODE,
        r.ROLE_NAME,
        r.DESCRIPTION,
        r.IS_ACTIVE
      FROM USER_ROLE_INFO r
      INNER JOIN USER_ROLE_MAPPING m ON r.ID = m.ROLE_ID
      WHERE m.USER_ID = :userId 
        AND m.IS_DELETED = 0 
        AND r.IS_DELETED = 0
        AND r.IS_ACTIVE = 1
    `
    
    const result = await executeQuery(sql, { userId })
    
    if (!result.rows) {
      return []
    }
    
    const roles: Role[] = []
    for (const row of result.rows as any[]) {
      const permissions = await getRolePermissions(row.ID)
      roles.push({
        id: row.ID,
        roleCode: row.ROLE_CODE as UserRole,
        roleName: row.ROLE_NAME,
        description: row.DESCRIPTION,
        isActive: row.IS_ACTIVE === 1,
        permissions,
      })
    }
    
    return roles
  } catch (error) {
    console.error('❌ 获取用户角色失败:', error)
    return []
  }
}

/**
 * 获取角色权限
 */
export async function getRolePermissions(roleId: number): Promise<Permission[]> {
  try {
    const sql = `
      SELECT 
        p.ID,
        p.PERMISSION_CODE,
        p.PERMISSION_NAME,
        p.DESCRIPTION,
        p.MODULE,
        p.ACTION,
        p.IS_ACTIVE,
        p.CREATED_AT,
        p.UPDATED_AT
      FROM USER_PERMISSION_INFO p
      INNER JOIN USER_ROLE_PERMISSION_MAPPING m ON p.ID = m.PERMISSION_ID
      WHERE m.ROLE_ID = :roleId 
        AND m.IS_DELETED = 0 
        AND p.IS_DELETED = 0
        AND p.IS_ACTIVE = 1
      ORDER BY p.MODULE, p.ACTION
    `

    const result = await executeQuery(sql, { roleId })

    if (!result.rows) {
      return []
    }

    return result.rows.map((row: any) => ({
      id: row.ID,
      code: row.PERMISSION_CODE,
      name: row.PERMISSION_NAME,
      description: row.DESCRIPTION,
      module: row.MODULE,
      action: row.ACTION,
      isActive: row.IS_ACTIVE === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
    }))
  } catch (error) {
    console.error('❌ 获取角色权限失败:', error)
    return []
  }
}

/**
 * 获取所有权限列表
 */
export async function getAllPermissions(): Promise<Permission[]> {
  try {
    const sql = `
      SELECT 
        ID,
        PERMISSION_CODE,
        PERMISSION_NAME,
        DESCRIPTION,
        MODULE,
        ACTION,
        IS_ACTIVE,
        CREATED_AT,
        UPDATED_AT
      FROM USER_PERMISSION_INFO
      WHERE IS_DELETED = 0
      ORDER BY MODULE, ACTION
    `

    const result = await executeQuery(sql)

    if (!result.rows) {
      return []
    }

    return result.rows.map((row: any) => ({
      id: row.ID,
      code: row.PERMISSION_CODE,
      name: row.PERMISSION_NAME,
      description: row.DESCRIPTION,
      module: row.MODULE,
      action: row.ACTION,
      isActive: row.IS_ACTIVE === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
    }))
  } catch (error) {
    console.error('❌ 获取权限列表失败:', error)
    throw new Error('获取权限列表失败')
  }
}

/**
 * 根据ID获取权限信息
 */
export async function getPermissionById(permissionId: number): Promise<Permission | null> {
  try {
    const sql = `
      SELECT
        ID,
        PERMISSION_CODE,
        PERMISSION_NAME,
        DESCRIPTION,
        MODULE,
        ACTION,
        IS_ACTIVE,
        CREATED_AT,
        UPDATED_AT
      FROM USER_PERMISSION_INFO
      WHERE ID = :permissionId AND IS_DELETED = 0
    `

    const result = await executeQuery(sql, { permissionId })

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.ID,
      code: row.PERMISSION_CODE,
      name: row.PERMISSION_NAME,
      description: row.DESCRIPTION,
      module: row.MODULE,
      action: row.ACTION,
      isActive: row.IS_ACTIVE === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT,
    }
  } catch (error) {
    console.error('❌ 获取权限信息失败:', error)
    throw new Error('获取权限信息失败')
  }
}

/**
 * 更新权限信息
 */
export async function updatePermission(
  permissionId: number,
  updateData: { name?: string; description?: string; isActive?: boolean }
): Promise<void> {
  try {
    const updateFields: string[] = []
    const params: any = { permissionId }

    if (updateData.name !== undefined) {
      updateFields.push('PERMISSION_NAME = :name')
      params.name = updateData.name
    }

    if (updateData.description !== undefined) {
      updateFields.push('DESCRIPTION = :description')
      params.description = updateData.description
    }

    if (updateData.isActive !== undefined) {
      updateFields.push('IS_ACTIVE = :isActive')
      params.isActive = updateData.isActive ? 1 : 0
    }

    if (updateFields.length === 0) {
      throw new Error('没有提供要更新的字段')
    }

    updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP')

    const sql = `
      UPDATE USER_PERMISSION_INFO
      SET ${updateFields.join(', ')}
      WHERE ID = :permissionId AND IS_DELETED = 0
    `

    await executeQuery(sql, params)
  } catch (error) {
    console.error('❌ 更新权限失败:', error)
    throw new Error('更新权限失败')
  }
}

/**
 * 分配角色权限
 */
export async function assignRolePermissions(roleId: number, permissionIds: number[]): Promise<void> {
  try {
    // 先删除现有的角色权限关联（物理删除）
    const deleteSql = `
      DELETE FROM USER_ROLE_PERMISSION_MAPPING
      WHERE ROLE_ID = :roleId
    `
    await executeQuery(deleteSql, { roleId })

    // 如果有新的权限ID，则插入新的关联
    if (permissionIds.length > 0) {
      // 获取下一个ID
      const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM USER_ROLE_PERMISSION_MAPPING`
      const nextIdResult = await executeQuery(getNextIdSql)
      let nextId = nextIdResult.rows?.[0]?.NEXT_ID || 1

      const insertSql = `
        INSERT INTO USER_ROLE_PERMISSION_MAPPING (ID, ROLE_ID, PERMISSION_ID, IS_DELETED, CREATED_AT, UPDATED_AT)
        VALUES (:id, :roleId, :permissionId, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `

      for (const permissionId of permissionIds) {
        await executeQuery(insertSql, { id: nextId++, roleId, permissionId })
      }
    }
  } catch (error) {
    console.error('❌ 分配角色权限失败:', error)
    throw new Error('分配角色权限失败')
  }
}

/**
 * 获取所有角色列表
 */
export async function getAllRoles(): Promise<Role[]> {
  try {
    const sql = `
      SELECT 
        ID,
        ROLE_CODE,
        ROLE_NAME,
        DESCRIPTION,
        IS_ACTIVE,
        CREATED_AT,
        UPDATED_AT
      FROM USER_ROLE_INFO
      WHERE IS_DELETED = 0
      ORDER BY ROLE_CODE
    `

    const result = await executeQuery(sql)

    if (!result.rows) {
      return []
    }

    const roles: Role[] = []
    for (const row of result.rows as any[]) {
      const permissions = await getRolePermissions(row.ID)
      roles.push({
        id: row.ID,
        roleCode: row.ROLE_CODE as UserRole,
        roleName: row.ROLE_NAME,
        description: row.DESCRIPTION,
        isActive: row.IS_ACTIVE === 1,
        permissions,
      })
    }

    return roles
  } catch (error) {
    console.error('❌ 获取角色列表失败:', error)
    throw new Error('获取角色列表失败')
  }
}

/**
 * 验证用户登录
 */
export async function authenticateUser(credentials: LoginRequest): Promise<User | null> {
  try {
    const user = await findUserByUsername(credentials.username)
    
    if (!user) {
      return null
    }
    
    // 检查用户状态
    if (user.status !== 'ACTIVE') {
      throw new Error('用户账户已被禁用')
    }
    
    // 获取密码哈希
    const passwordSql = `
      SELECT PASSWORD_HASH 
      FROM USER_INFO 
      WHERE USERNAME = :username AND IS_DELETED = 0
    `
    
    const passwordResult = await executeQuery(passwordSql, { username: credentials.username })
    
    if (!passwordResult.rows || passwordResult.rows.length === 0) {
      return null
    }
    
    const passwordHash = (passwordResult.rows[0] as any).PASSWORD_HASH
    
    // 验证密码
    const isPasswordValid = await verifyPassword(credentials.password, passwordHash)
    
    if (!isPasswordValid) {
      return null
    }
    
    // 更新最后登录时间
    await updateLastLoginTime(user.id)
    
    return user
  } catch (error) {
    console.error('❌ 用户认证失败:', error)
    throw error
  }
}

/**
 * 更新最后登录时间
 */
export async function updateLastLoginTime(userId: number): Promise<void> {
  try {
    const sql = `
      UPDATE USER_INFO
      SET LAST_LOGIN_AT = SYSDATE,
          UPDATED_AT = SYSDATE
      WHERE ID = :userId
    `

    await executeQuery(sql, { userId })
  } catch (error) {
    console.error('❌ 更新登录时间失败:', error)
    // 不抛出错误，因为这不是关键操作
  }
}



/**
 * 创建用户
 */
export async function createUser(userData: RegisterRequest): Promise<User> {
  try {
    return await executeTransaction(async (connection) => {
      // 检查用户名是否已存在
      const existingUserSql = `
        SELECT COUNT(*) as COUNT
        FROM USER_INFO
        WHERE USERNAME = :username AND IS_DELETED = 0
      `
      const existingResult = await connection.execute(existingUserSql, { username: userData.username })
      const existingCount = (existingResult.rows![0] as any).COUNT

      if (existingCount > 0) {
        throw new Error('用户名已存在')
      }

      // 哈希密码
      const passwordHash = await hashPassword(userData.password)

      // 插入用户
      const insertUserSql = `
        INSERT INTO USER_INFO (
          USERNAME, PASSWORD_HASH, REAL_NAME, EMAIL, DEPARTMENT, POSITION, STATUS, IS_DELETED, CREATED_BY
        ) VALUES (
          :username, :passwordHash, :realName, :email, :department, :position, 'ACTIVE', 0, 1
        ) RETURNING ID INTO :userId
      `

      const userResult = await connection.execute(insertUserSql, {
        username: userData.username,
        passwordHash,
        realName: userData.realName,
        email: userData.email,
        department: userData.department,
        position: userData.position,
        userId: { type: oracledb.NUMBER, dir: oracledb.BIND_OUT }
      })

      const userId = (userResult.outBinds as any).userId[0]

      // 分配角色
      if (userData.roleIds && userData.roleIds.length > 0) {
        for (const roleId of userData.roleIds) {
          const assignRoleSql = `
            INSERT INTO USER_ROLE_MAPPING (
              USER_ID, ROLE_ID, IS_DELETED, CREATED_BY
            ) VALUES (
              :userId, :roleId, 0, 1
            )
          `

          await connection.execute(assignRoleSql, { userId, roleId })
        }
      }

      // 返回创建的用户
      const newUser = await findUserById(userId)
      if (!newUser) {
        throw new Error('创建用户后查询失败')
      }

      return newUser
    })
  } catch (error) {
    console.error('❌ 创建用户失败:', error)
    throw error
  }
}

/**
 * 获取用户列表
 */
export async function getUserList(params: UserListParams): Promise<UserListResponse> {
  try {
    const { page = 1, pageSize = 10, search, status, department } = params

    // 构建查询条件
    const conditions: string[] = ['u.IS_DELETED = 0']
    const binds: any = {}

    if (search) {
      conditions.push('(UPPER(u.USERNAME) LIKE UPPER(:search) OR UPPER(u.REAL_NAME) LIKE UPPER(:search) OR UPPER(u.EMAIL) LIKE UPPER(:search))')
      binds.search = `%${search}%`
    }

    if (status) {
      conditions.push('u.STATUS = :status')
      binds.status = status
    }

    if (department) {
      conditions.push('UPPER(u.DEPARTMENT) LIKE UPPER(:department)')
      binds.department = `%${department}%`
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as TOTAL
      FROM USER_INFO u
      ${whereClause}
    `

    const countResult = await executeQuery(countSql, binds)
    const totalCount = (countResult.rows![0] as any).TOTAL

    // 查询数据
    const dataSql = buildPaginationSQL(`
      SELECT
        u.ID,
        u.USERNAME,
        u.REAL_NAME,
        u.EMAIL,
        u.DEPARTMENT,
        u.POSITION,
        u.STATUS,
        u.CREATED_AT,
        u.UPDATED_AT,
        u.LAST_LOGIN_AT
      FROM USER_INFO u
      ${whereClause}
      ORDER BY u.CREATED_AT DESC
    `, page, pageSize)

    const dataResult = await executeQuery(dataSql, binds)

    const users: User[] = []
    if (dataResult.rows) {
      for (const row of dataResult.rows as any[]) {
        const roles = await getUserRoles(row.ID)
        users.push({
          id: row.ID,
          username: row.USERNAME,
          realName: row.REAL_NAME,
          email: row.EMAIL,
          department: row.DEPARTMENT,
          position: row.POSITION,
          status: row.STATUS as UserStatus,
          createdAt: row.CREATED_AT,
          updatedAt: row.UPDATED_AT,
          lastLoginAt: row.LAST_LOGIN_AT,
          roles,
        })
      }
    }

    const totalPages = Math.ceil(totalCount / pageSize)

    return {
      users,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error)
    throw new Error('获取用户列表失败')
  }
}

/**
 * 更新用户信息
 */
export async function updateUser(userId: number, userData: UserUpdateRequest): Promise<User> {
  try {
    return await executeTransaction(async (connection) => {
      const updateFields: string[] = []
      const binds: any = { userId }

      if (userData.realName !== undefined) {
        updateFields.push('REAL_NAME = :realName')
        binds.realName = userData.realName
      }

      if (userData.email !== undefined) {
        updateFields.push('EMAIL = :email')
        binds.email = userData.email
      }

      if (userData.department !== undefined) {
        updateFields.push('DEPARTMENT = :department')
        binds.department = userData.department
      }

      if (userData.position !== undefined) {
        updateFields.push('POSITION = :position')
        binds.position = userData.position
      }

      if (userData.status !== undefined) {
        updateFields.push('STATUS = :status')
        binds.status = userData.status
      }

      if (updateFields.length > 0) {
        updateFields.push('UPDATED_AT = SYSDATE')

        const updateSql = `
          UPDATE USER_INFO
          SET ${updateFields.join(', ')}
          WHERE ID = :userId AND IS_DELETED = 0
        `

        await connection.execute(updateSql, binds)
      }

      // 更新角色
      if (userData.roleIds !== undefined) {
        // 删除现有角色关联
        const deleteRolesSql = `
          UPDATE USER_ROLE_MAPPING
          SET IS_DELETED = 1, UPDATED_AT = SYSDATE
          WHERE USER_ID = :userId AND IS_DELETED = 0
        `

        await connection.execute(deleteRolesSql, { userId })

        // 添加新的角色关联
        for (const roleId of userData.roleIds) {
          const assignRoleSql = `
            INSERT INTO USER_ROLE_MAPPING (
              USER_ID, ROLE_ID, IS_DELETED, CREATED_BY
            ) VALUES (
              :userId, :roleId, 0, 1
            )
          `

          await connection.execute(assignRoleSql, { userId, roleId })
        }
      }

      // 返回更新后的用户
      const updatedUser = await findUserById(userId)
      if (!updatedUser) {
        throw new Error('更新后查询用户失败')
      }

      return updatedUser
    })
  } catch (error) {
    console.error('❌ 更新用户失败:', error)
    throw error
  }
}

/**
 * 删除用户
 */
export async function deleteUser(userId: number): Promise<void> {
  try {
    await executeTransaction(async (connection) => {
      // 软删除用户
      const deleteUserSql = `
        UPDATE USER_INFO
        SET IS_DELETED = 1, UPDATED_AT = SYSDATE
        WHERE ID = :userId AND IS_DELETED = 0
      `

      await connection.execute(deleteUserSql, { userId })

      // 软删除用户角色关联
      const deleteRolesSql = `
        UPDATE USER_ROLE_MAPPING
        SET IS_DELETED = 1, UPDATED_AT = SYSDATE
        WHERE USER_ID = :userId AND IS_DELETED = 0
      `

      await connection.execute(deleteRolesSql, { userId })
    })
  } catch (error) {
    console.error('❌ 删除用户失败:', error)
    throw error
  }
}

/**
 * 批量删除用户
 */
export async function batchDeleteUsers(userIds: number[]): Promise<void> {
  try {
    await executeTransaction(async (connection) => {
      for (const userId of userIds) {
        // 软删除用户
        const deleteUserSql = `
          UPDATE USER_INFO
          SET IS_DELETED = 1, UPDATED_AT = SYSDATE
          WHERE ID = :userId AND IS_DELETED = 0
        `

        await connection.execute(deleteUserSql, { userId })

        // 软删除用户角色关联
        const deleteRolesSql = `
          UPDATE USER_ROLE_MAPPING
          SET IS_DELETED = 1, UPDATED_AT = SYSDATE
          WHERE USER_ID = :userId AND IS_DELETED = 0
        `

        await connection.execute(deleteRolesSql, { userId })
      }
    })
  } catch (error) {
    console.error('❌ 批量删除用户失败:', error)
    throw error
  }
}

/**
 * 分配用户角色
 */
export async function assignUserRoles(userId: number, roleIds: number[]): Promise<void> {
  try {
    await executeTransaction(async (connection) => {
      // 删除现有角色关联
      const deleteRolesSql = `
        UPDATE USER_ROLE_MAPPING
        SET IS_DELETED = 1, UPDATED_AT = SYSDATE
        WHERE USER_ID = :userId AND IS_DELETED = 0
      `

      await connection.execute(deleteRolesSql, { userId })

      // 添加新的角色关联
      for (const roleId of roleIds) {
        const assignRoleSql = `
          INSERT INTO USER_ROLE_MAPPING (
            USER_ID, ROLE_ID, IS_DELETED, CREATED_BY
          ) VALUES (
            :userId, :roleId, 0, 1
          )
        `

        await connection.execute(assignRoleSql, { userId, roleId })
      }
    })
  } catch (error) {
    console.error('❌ 分配用户角色失败:', error)
    throw error
  }
}

/**
 * 创建角色
 */
export async function createRole(roleData: RoleCreateRequest): Promise<Role> {
  try {
    return await executeTransaction(async (connection) => {
      // 检查角色代码是否已存在
      const existingRoleSql = `
        SELECT COUNT(*) as COUNT
        FROM USER_ROLE_INFO
        WHERE ROLE_CODE = :roleCode AND IS_DELETED = 0
      `
      const existingResult = await connection.execute(existingRoleSql, { roleCode: roleData.roleCode })
      const existingCount = (existingResult.rows![0] as any).COUNT

      if (existingCount > 0) {
        throw new Error('角色代码已存在')
      }

      // 插入角色
      const insertRoleSql = `
        INSERT INTO USER_ROLE_INFO (
          ROLE_CODE, ROLE_NAME, DESCRIPTION, IS_ACTIVE, IS_DELETED, CREATED_BY
        ) VALUES (
          :roleCode, :roleName, :description, 1, 0, 1
        ) RETURNING ID INTO :roleId
      `

      const roleResult = await connection.execute(insertRoleSql, {
        roleCode: roleData.roleCode,
        roleName: roleData.roleName,
        description: roleData.description || null,
        roleId: { type: oracledb.NUMBER, dir: oracledb.BIND_OUT }
      })

      const roleId = (roleResult.outBinds as any).roleId[0]

      // 分配权限
      if (roleData.permissionIds && roleData.permissionIds.length > 0) {
        for (const permissionId of roleData.permissionIds) {
          // 生成一个简单的ID（使用时间戳的后6位 + 随机数）
          const mappingId = (Date.now() % 1000000) * 1000 + Math.floor(Math.random() * 1000)

          const assignPermissionSql = `
            INSERT INTO USER_ROLE_PERMISSION_MAPPING (
              ID, ROLE_ID, PERMISSION_ID, IS_DELETED, CREATED_BY
            ) VALUES (
              :mappingId, :roleId, :permissionId, 0, 1
            )
          `

          await connection.execute(assignPermissionSql, { mappingId, roleId, permissionId })
        }
      }

      // 返回创建的角色
      const newRole = await getRoleById(roleId)
      if (!newRole) {
        throw new Error('创建角色后查询失败')
      }

      return newRole
    })
  } catch (error) {
    console.error('❌ 创建角色失败:', error)
    throw error
  }
}

/**
 * 根据ID获取角色
 */
export async function getRoleById(roleId: number): Promise<Role | null> {
  try {
    const sql = `
      SELECT
        ID,
        ROLE_CODE,
        ROLE_NAME,
        DESCRIPTION,
        IS_ACTIVE,
        CREATED_AT,
        UPDATED_AT
      FROM USER_ROLE_INFO
      WHERE ID = :roleId AND IS_DELETED = 0
    `

    const result = await executeQuery(sql, { roleId })

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0] as any
    const permissions = await getRolePermissions(row.ID)

    return {
      id: row.ID,
      roleCode: row.ROLE_CODE as UserRole,
      roleName: row.ROLE_NAME,
      description: row.DESCRIPTION,
      isActive: row.IS_ACTIVE === 1,
      permissions,
    }
  } catch (error) {
    console.error('❌ 获取角色失败:', error)
    return null
  }
}

/**
 * 更新角色
 */
export async function updateRole(roleId: number, roleData: RoleUpdateRequest): Promise<Role> {
  try {
    return await executeTransaction(async (connection) => {
      const updateFields: string[] = []
      const binds: any = { roleId }

      if (roleData.roleName !== undefined) {
        updateFields.push('ROLE_NAME = :roleName')
        binds.roleName = roleData.roleName
      }

      if (roleData.description !== undefined) {
        updateFields.push('DESCRIPTION = :description')
        binds.description = roleData.description
      }

      if (roleData.isActive !== undefined) {
        updateFields.push('IS_ACTIVE = :isActive')
        binds.isActive = roleData.isActive ? 1 : 0
      }

      if (updateFields.length > 0) {
        updateFields.push('UPDATED_AT = SYSDATE')

        const updateSql = `
          UPDATE USER_ROLE_INFO
          SET ${updateFields.join(', ')}
          WHERE ID = :roleId AND IS_DELETED = 0
        `

        await connection.execute(updateSql, binds)
      }

      // 更新权限
      if (roleData.permissionIds !== undefined) {
        // 删除现有权限关联
        const deletePermissionsSql = `
          UPDATE USER_ROLE_PERMISSION_MAPPING
          SET IS_DELETED = 1, UPDATED_AT = SYSDATE
          WHERE ROLE_ID = :roleId AND IS_DELETED = 0
        `

        await connection.execute(deletePermissionsSql, { roleId })

        // 添加新的权限关联
        for (const permissionId of roleData.permissionIds) {
          // 生成一个简单的ID（使用时间戳的后6位 + 随机数）
          const mappingId = (Date.now() % 1000000) * 1000 + Math.floor(Math.random() * 1000)

          const assignPermissionSql = `
            INSERT INTO USER_ROLE_PERMISSION_MAPPING (
              ID, ROLE_ID, PERMISSION_ID, IS_DELETED, CREATED_BY
            ) VALUES (
              :mappingId, :roleId, :permissionId, 0, 1
            )
          `

          await connection.execute(assignPermissionSql, { mappingId, roleId, permissionId })
        }
      }

      // 返回更新后的角色
      const updatedRole = await getRoleById(roleId)
      if (!updatedRole) {
        throw new Error('更新后查询角色失败')
      }

      return updatedRole
    })
  } catch (error) {
    console.error('❌ 更新角色失败:', error)
    throw error
  }
}

/**
 * 删除角色
 */
export async function deleteRole(roleId: number): Promise<void> {
  try {
    await executeTransaction(async (connection) => {
      // 检查是否有用户使用此角色
      const userCountSql = `
        SELECT COUNT(*) as COUNT
        FROM USER_ROLE_MAPPING
        WHERE ROLE_ID = :roleId AND IS_DELETED = 0
      `
      const userCountResult = await connection.execute(userCountSql, { roleId })
      const userCount = (userCountResult.rows![0] as any).COUNT

      if (userCount > 0) {
        throw new Error('该角色正在被用户使用，无法删除')
      }

      // 软删除角色
      const deleteRoleSql = `
        UPDATE USER_ROLE_INFO
        SET IS_DELETED = 1, UPDATED_AT = SYSDATE
        WHERE ID = :roleId AND IS_DELETED = 0
      `

      await connection.execute(deleteRoleSql, { roleId })

      // 软删除角色权限关联
      const deletePermissionsSql = `
        UPDATE USER_ROLE_PERMISSION_MAPPING
        SET IS_DELETED = 1, UPDATED_AT = SYSDATE
        WHERE ROLE_ID = :roleId AND IS_DELETED = 0
      `

      await connection.execute(deletePermissionsSql, { roleId })
    })
  } catch (error) {
    console.error('❌ 删除角色失败:', error)
    throw error
  }
}
