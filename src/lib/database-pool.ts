/**
 * 企业级数据库连接池管理系统
 */

import oracledb from 'oracledb'
import { monitoring, log } from './monitoring'
import { configManager } from './config-manager'
import { withRetry, DatabaseError } from './error-handling'

export interface PoolConfig {
  user: string
  password: string
  connectString: string
  poolMin: number
  poolMax: number
  poolIncrement: number
  poolTimeout: number
  poolPingInterval: number
  stmtCacheSize: number
  enableStatistics: boolean
}

export interface ConnectionStats {
  connectionsOpen: number
  connectionsInUse: number
  poolMin: number
  poolMax: number
  poolIncrement: number
  totalConnectionRequests: number
  totalRequestsEnqueued: number
  totalRequestsDequeued: number
  totalFailedRequests: number
  totalRequestTimeouts: number
  timeOfReset: Date
  timeCreated: Date
}

export interface QueryMetrics {
  query: string
  duration: number
  success: boolean
  rowsAffected?: number
  error?: string
  timestamp: number
}

/**
 * 数据库连接池管理器
 */
class DatabasePoolManager {
  private pool: oracledb.Pool | null = null
  private isInitialized = false
  private queryMetrics: QueryMetrics[] = []
  private connectionCount = 0
  private healthCheckInterval: NodeJS.Timeout | null = null

  /**
   * 初始化连接池
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      log({
        level: 'warn',
        message: 'Database pool already initialized'
      })
      return
    }

    try {
      const config = await this.getPoolConfig()
      
      log({
        level: 'info',
        message: 'Initializing database connection pool',
        context: {
          poolMin: config.poolMin,
          poolMax: config.poolMax,
          poolIncrement: config.poolIncrement
        }
      })

      // 创建连接池
      this.pool = await oracledb.createPool(config)

      // 启动健康检查
      this.startHealthCheck()

      this.isInitialized = true

      const stats = await this.getPoolStatistics()
      log({
        level: 'info',
        message: 'Database connection pool initialized successfully',
        context: stats || undefined
      })

      // 记录初始化指标
      monitoring.recordMetric({
        name: 'db_pool_initialized',
        value: 1,
        tags: {
          pool_min: config.poolMin.toString(),
          pool_max: config.poolMax.toString()
        }
      })

    } catch (error) {
      log({
        level: 'error',
        message: 'Failed to initialize database pool',
        context: { error: (error as Error).message }
      })

      monitoring.recordMetric({
        name: 'db_pool_init_error',
        value: 1
      })

      throw new DatabaseError('数据库连接池初始化失败', {
        operation: 'pool_initialization',
        metadata: { error: (error as Error).message }
      })
    }
  }

  /**
   * 获取数据库连接
   */
  async getConnection(): Promise<oracledb.Connection> {
    if (!this.pool) {
      throw new DatabaseError('数据库连接池未初始化')
    }

    const timer = monitoring.startTimer('db_connection_acquire')

    try {
      const connection = await withRetry(
        () => this.pool!.getConnection(),
        {
          maxAttempts: 3,
          baseDelay: 100,
          maxDelay: 1000,
          backoffMultiplier: 2,
          retryableErrors: ['ORA-12516', 'ORA-12519'] // 连接池相关错误
        },
        { operation: 'get_connection' }
      )

      this.connectionCount++
      timer.end(true)

      // 记录连接获取指标
      monitoring.recordMetric({
        name: 'db_connection_acquired',
        value: 1
      })

      log({
        level: 'debug',
        message: 'Database connection acquired',
        context: {
          connectionId: this.connectionCount,
          poolStats: await this.getPoolStatistics()
        }
      })

      return connection

    } catch (error) {
      timer.end(false, (error as Error).message)
      
      monitoring.recordMetric({
        name: 'db_connection_acquire_error',
        value: 1,
        tags: {
          error_type: (error as Error).name
        }
      })

      throw new DatabaseError('获取数据库连接失败', {
        operation: 'get_connection',
        metadata: { error: (error as Error).message }
      })
    }
  }

  /**
   * 执行查询
   */
  async executeQuery<T = any>(
    sql: string,
    binds: any[] = [],
    options: oracledb.ExecuteOptions = {}
  ): Promise<oracledb.Result<T>> {
    const timer = monitoring.startTimer('db_query_execution', {
      sql: this.sanitizeSqlForLogging(sql)
    })

    let connection: oracledb.Connection | null = null
    const startTime = Date.now()

    try {
      connection = await this.getConnection()
      
      const result = await connection.execute<T>(sql, binds, {
        autoCommit: true,
        ...options
      })

      const duration = Date.now() - startTime
      timer.end(true)

      // 记录查询指标
      this.recordQueryMetrics({
        query: this.sanitizeSqlForLogging(sql),
        duration,
        success: true,
        rowsAffected: result.rowsAffected,
        timestamp: startTime
      })

      monitoring.recordMetric({
        name: 'db_query_success',
        value: 1,
        tags: {
          operation: this.getOperationType(sql)
        }
      })

      log({
        level: 'debug',
        message: 'Database query executed successfully',
        context: {
          sql: this.sanitizeSqlForLogging(sql),
          duration,
          rowsAffected: result.rowsAffected
        }
      })

      return result

    } catch (error) {
      const duration = Date.now() - startTime
      timer.end(false, (error as Error).message)

      // 记录错误指标
      this.recordQueryMetrics({
        query: this.sanitizeSqlForLogging(sql),
        duration,
        success: false,
        error: (error as Error).message,
        timestamp: startTime
      })

      monitoring.recordMetric({
        name: 'db_query_error',
        value: 1,
        tags: {
          operation: this.getOperationType(sql),
          error_type: (error as Error).name
        }
      })

      log({
        level: 'error',
        message: 'Database query failed',
        context: {
          sql: this.sanitizeSqlForLogging(sql),
          duration,
          error: (error as Error).message
        }
      })

      throw new DatabaseError('数据库查询失败', {
        operation: 'execute_query',
        metadata: {
          sql: this.sanitizeSqlForLogging(sql),
          error: (error as Error).message
        }
      })

    } finally {
      if (connection) {
        try {
          await connection.close()
          
          monitoring.recordMetric({
            name: 'db_connection_released',
            value: 1
          })

        } catch (closeError) {
          log({
            level: 'error',
            message: 'Failed to close database connection',
            context: { error: (closeError as Error).message }
          })
        }
      }
    }
  }

  /**
   * 执行事务
   */
  async executeTransaction<T>(
    operations: Array<{
      sql: string
      binds?: any[]
      options?: oracledb.ExecuteOptions
    }>
  ): Promise<T[]> {
    const timer = monitoring.startTimer('db_transaction_execution')
    let connection: oracledb.Connection | null = null

    try {
      connection = await this.getConnection()
      
      // 开始事务
      const results: T[] = []

      for (const operation of operations) {
        const result = await connection.execute<T>(
          operation.sql,
          operation.binds || [],
          {
            autoCommit: false,
            ...operation.options
          }
        )
        results.push(result as T)
      }

      // 提交事务
      await connection.commit()
      timer.end(true)

      monitoring.recordMetric({
        name: 'db_transaction_success',
        value: 1,
        tags: {
          operations_count: operations.length.toString()
        }
      })

      log({
        level: 'info',
        message: 'Database transaction completed successfully',
        context: {
          operationsCount: operations.length
        }
      })

      return results

    } catch (error) {
      timer.end(false, (error as Error).message)

      // 回滚事务
      if (connection) {
        try {
          await connection.rollback()
          log({
            level: 'info',
            message: 'Transaction rolled back due to error'
          })
        } catch (rollbackError) {
          log({
            level: 'error',
            message: 'Failed to rollback transaction',
            context: { error: (rollbackError as Error).message }
          })
        }
      }

      monitoring.recordMetric({
        name: 'db_transaction_error',
        value: 1
      })

      throw new DatabaseError('数据库事务执行失败', {
        operation: 'execute_transaction',
        metadata: { error: (error as Error).message }
      })

    } finally {
      if (connection) {
        try {
          await connection.close()
        } catch (closeError) {
          log({
            level: 'error',
            message: 'Failed to close transaction connection',
            context: { error: (closeError as Error).message }
          })
        }
      }
    }
  }

  /**
   * 获取连接池统计信息
   */
  async getPoolStatistics(): Promise<ConnectionStats | null> {
    if (!this.pool) return null

    try {
      const stats = this.pool.getStatistics()
      return stats as unknown as ConnectionStats
    } catch (error) {
      log({
        level: 'error',
        message: 'Failed to get pool statistics',
        context: { error: (error as Error).message }
      })
      return null
    }
  }

  /**
   * 获取查询性能指标
   */
  getQueryMetrics(timeRange: number = 3600000): QueryMetrics[] {
    const cutoff = Date.now() - timeRange
    return this.queryMetrics.filter(metric => metric.timestamp > cutoff)
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    details: any
  }> {
    if (!this.pool) {
      return {
        status: 'unhealthy',
        details: { error: 'Pool not initialized' }
      }
    }

    try {
      const connection = await this.pool.getConnection()
      const result = await connection.execute('SELECT 1 FROM DUAL')
      await connection.close()

      const stats = await this.getPoolStatistics()
      const utilizationRate = stats ? (stats.connectionsInUse / stats.poolMax) * 100 : 0

      return {
        status: utilizationRate > 90 ? 'degraded' : 'healthy',
        details: {
          poolStats: stats,
          utilizationRate: utilizationRate.toFixed(2) + '%',
          testQuery: 'SUCCESS'
        }
      }

    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: (error as Error).message }
      }
    }
  }

  /**
   * 关闭连接池
   */
  async close(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    if (this.pool) {
      try {
        await this.pool.close(10) // 10秒超时
        this.pool = null
        this.isInitialized = false

        log({
          level: 'info',
          message: 'Database connection pool closed successfully'
        })

      } catch (error) {
        log({
          level: 'error',
          message: 'Failed to close database pool',
          context: { error: (error as Error).message }
        })
        throw error
      }
    }
  }

  private async getPoolConfig(): Promise<PoolConfig> {
    return {
      user: configManager.get('database.username'),
      password: configManager.get('database.password'),
      connectString: `${configManager.get('database.host')}:${configManager.get('database.port')}/${configManager.get('database.serviceName')}`,
      poolMin: configManager.get('database.poolMin', 2),
      poolMax: configManager.get('database.poolMax', 10),
      poolIncrement: configManager.get('database.poolIncrement', 1),
      poolTimeout: configManager.get('database.connectionTimeout', 60),
      poolPingInterval: 60, // 60秒
      stmtCacheSize: 30,
      enableStatistics: true
    }
  }

  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.healthCheck()
        
        monitoring.recordMetric({
          name: 'db_pool_health_check',
          value: 1,
          tags: {
            status: health.status
          }
        })

        if (health.status === 'unhealthy') {
          log({
            level: 'error',
            message: 'Database pool health check failed',
            context: health.details
          })
        }

      } catch (error) {
        log({
          level: 'error',
          message: 'Health check error',
          context: { error: (error as Error).message }
        })
      }
    }, 30000) // 30秒检查一次
  }

  private recordQueryMetrics(metrics: QueryMetrics): void {
    this.queryMetrics.push(metrics)
    
    // 保持最近1000条记录
    if (this.queryMetrics.length > 1000) {
      this.queryMetrics = this.queryMetrics.slice(-1000)
    }
  }

  private sanitizeSqlForLogging(sql: string): string {
    // 移除敏感信息，只保留SQL结构用于日志记录
    return sql
      .replace(/\s+/g, ' ')
      .replace(/'/g, '?')
      .substring(0, 200) + (sql.length > 200 ? '...' : '')
  }

  private getOperationType(sql: string): string {
    const trimmed = sql.trim().toUpperCase()
    if (trimmed.startsWith('SELECT')) return 'SELECT'
    if (trimmed.startsWith('INSERT')) return 'INSERT'
    if (trimmed.startsWith('UPDATE')) return 'UPDATE'
    if (trimmed.startsWith('DELETE')) return 'DELETE'
    if (trimmed.startsWith('CREATE')) return 'CREATE'
    if (trimmed.startsWith('ALTER')) return 'ALTER'
    if (trimmed.startsWith('DROP')) return 'DROP'
    return 'OTHER'
  }
}

// 单例实例
export const databasePool = new DatabasePoolManager()

// 便捷方法
export const executeQuery = <T = any>(
  sql: string,
  binds?: any[],
  options?: oracledb.ExecuteOptions
): Promise<oracledb.Result<T>> => databasePool.executeQuery<T>(sql, binds, options)

export const executeTransaction = <T>(
  operations: Array<{
    sql: string
    binds?: any[]
    options?: oracledb.ExecuteOptions
  }>
): Promise<T[]> => databasePool.executeTransaction<T>(operations)

export default databasePool
