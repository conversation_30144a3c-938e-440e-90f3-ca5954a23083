/**
 * 医疗诊断服务
 * 实现P0-008数据库设计与API实现一致性修复目标：
 * - 补充缺失的医疗诊断管理API
 * - 确保MEDICAL_DIAGNOSIS表与API接口完全匹配
 */

import { executeQuery } from '@/lib/database'

export interface MedicalDiagnosis {
  id: number
  caseId: number
  diagnosisCode: string
  diagnosisName: string
  diagnosisType: string
  diagnosisCategory: string
  icdCode: string
  icdName: string
  isPrimary: boolean
  diagnosisDate: Date
  diagnosisDoctor: string
  diagnosisDescription: string
  treatmentPlan: string
  isDeleted: boolean
  createdAt: Date
  updatedAt: Date
}

export interface DiagnosisCreateRequest {
  caseId: number
  diagnosisCode: string
  diagnosisName: string
  diagnosisType: string
  diagnosisCategory: string
  icdCode: string
  icdName: string
  isPrimary?: boolean
  diagnosisDate: Date
  diagnosisDoctor: string
  diagnosisDescription?: string
  treatmentPlan?: string
}

export interface DiagnosisUpdateRequest {
  diagnosisCode?: string
  diagnosisName?: string
  diagnosisType?: string
  diagnosisCategory?: string
  icdCode?: string
  icdName?: string
  isPrimary?: boolean
  diagnosisDate?: Date
  diagnosisDoctor?: string
  diagnosisDescription?: string
  treatmentPlan?: string
}

/**
 * 获取医疗案例的诊断信息
 */
export async function getMedicalDiagnoses(caseId: number): Promise<MedicalDiagnosis[]> {
  const sql = `
    SELECT 
      ID, CASE_ID, DIAGNOSIS_CODE, DIAGNOSIS_NAME, DIAGNOSIS_TYPE,
      DIAGNOSIS_CATEGORY, ICD_CODE, ICD_NAME, IS_PRIMARY, DIAGNOSIS_DATE,
      DIAGNOSIS_DOCTOR, DIAGNOSIS_DESCRIPTION, TREATMENT_PLAN,
      IS_DELETED, CREATED_AT, UPDATED_AT
    FROM MEDICAL_DIAGNOSIS
    WHERE CASE_ID = :caseId AND IS_DELETED = 0
    ORDER BY IS_PRIMARY DESC, DIAGNOSIS_DATE DESC
  `

  const result = await executeQuery(sql, { caseId })

  return result.rows.map(row => ({
    id: row.ID,
    caseId: row.CASE_ID,
    diagnosisCode: row.DIAGNOSIS_CODE,
    diagnosisName: row.DIAGNOSIS_NAME,
    diagnosisType: row.DIAGNOSIS_TYPE,
    diagnosisCategory: row.DIAGNOSIS_CATEGORY,
    icdCode: row.ICD_CODE,
    icdName: row.ICD_NAME,
    isPrimary: row.IS_PRIMARY === 1,
    diagnosisDate: row.DIAGNOSIS_DATE,
    diagnosisDoctor: row.DIAGNOSIS_DOCTOR,
    diagnosisDescription: row.DIAGNOSIS_DESCRIPTION,
    treatmentPlan: row.TREATMENT_PLAN,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT
  }))
}

/**
 * 根据ID获取诊断详情
 */
export async function getMedicalDiagnosisById(id: number): Promise<MedicalDiagnosis | null> {
  const sql = `
    SELECT 
      ID, CASE_ID, DIAGNOSIS_CODE, DIAGNOSIS_NAME, DIAGNOSIS_TYPE,
      DIAGNOSIS_CATEGORY, ICD_CODE, ICD_NAME, IS_PRIMARY, DIAGNOSIS_DATE,
      DIAGNOSIS_DOCTOR, DIAGNOSIS_DESCRIPTION, TREATMENT_PLAN,
      IS_DELETED, CREATED_AT, UPDATED_AT
    FROM MEDICAL_DIAGNOSIS
    WHERE ID = :id AND IS_DELETED = 0
  `

  const result = await executeQuery(sql, { id })

  if (result.rows.length === 0) {
    return null
  }

  const row = result.rows[0]
  return {
    id: row.ID,
    caseId: row.CASE_ID,
    diagnosisCode: row.DIAGNOSIS_CODE,
    diagnosisName: row.DIAGNOSIS_NAME,
    diagnosisType: row.DIAGNOSIS_TYPE,
    diagnosisCategory: row.DIAGNOSIS_CATEGORY,
    icdCode: row.ICD_CODE,
    icdName: row.ICD_NAME,
    isPrimary: row.IS_PRIMARY === 1,
    diagnosisDate: row.DIAGNOSIS_DATE,
    diagnosisDoctor: row.DIAGNOSIS_DOCTOR,
    diagnosisDescription: row.DIAGNOSIS_DESCRIPTION,
    treatmentPlan: row.TREATMENT_PLAN,
    isDeleted: row.IS_DELETED === 1,
    createdAt: row.CREATED_AT,
    updatedAt: row.UPDATED_AT
  }
}

/**
 * 创建医疗诊断
 */
export async function createMedicalDiagnosis(data: DiagnosisCreateRequest): Promise<number> {
  const sql = `
    INSERT INTO MEDICAL_DIAGNOSIS (
      ID, CASE_ID, DIAGNOSIS_CODE, DIAGNOSIS_NAME, DIAGNOSIS_TYPE,
      DIAGNOSIS_CATEGORY, ICD_CODE, ICD_NAME, IS_PRIMARY, DIAGNOSIS_DATE,
      DIAGNOSIS_DOCTOR, DIAGNOSIS_DESCRIPTION, TREATMENT_PLAN,
      IS_DELETED, CREATED_AT, UPDATED_AT
    ) VALUES (
      SEQ_MEDICAL_DIAGNOSIS.NEXTVAL,
      :caseId, :diagnosisCode, :diagnosisName, :diagnosisType,
      :diagnosisCategory, :icdCode, :icdName, :isPrimary, :diagnosisDate,
      :diagnosisDoctor, :diagnosisDescription, :treatmentPlan,
      0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING ID INTO :id
  `

  const params = {
    caseId: data.caseId,
    diagnosisCode: data.diagnosisCode,
    diagnosisName: data.diagnosisName,
    diagnosisType: data.diagnosisType,
    diagnosisCategory: data.diagnosisCategory,
    icdCode: data.icdCode,
    icdName: data.icdName,
    isPrimary: data.isPrimary ? 1 : 0,
    diagnosisDate: data.diagnosisDate,
    diagnosisDoctor: data.diagnosisDoctor,
    diagnosisDescription: data.diagnosisDescription || '',
    treatmentPlan: data.treatmentPlan || '',
    id: { dir: 'OUT', type: 'NUMBER' }
  }

  const result = await executeQuery(sql, params)
  return (result as any).outBinds?.id || 0
}

/**
 * 更新医疗诊断
 */
export async function updateMedicalDiagnosis(
  id: number,
  data: DiagnosisUpdateRequest
): Promise<void> {
  const updateFields: string[] = []
  const params: any = { id }

  if (data.diagnosisCode !== undefined) {
    updateFields.push('DIAGNOSIS_CODE = :diagnosisCode')
    params.diagnosisCode = data.diagnosisCode
  }

  if (data.diagnosisName !== undefined) {
    updateFields.push('DIAGNOSIS_NAME = :diagnosisName')
    params.diagnosisName = data.diagnosisName
  }

  if (data.diagnosisType !== undefined) {
    updateFields.push('DIAGNOSIS_TYPE = :diagnosisType')
    params.diagnosisType = data.diagnosisType
  }

  if (data.diagnosisCategory !== undefined) {
    updateFields.push('DIAGNOSIS_CATEGORY = :diagnosisCategory')
    params.diagnosisCategory = data.diagnosisCategory
  }

  if (data.icdCode !== undefined) {
    updateFields.push('ICD_CODE = :icdCode')
    params.icdCode = data.icdCode
  }

  if (data.icdName !== undefined) {
    updateFields.push('ICD_NAME = :icdName')
    params.icdName = data.icdName
  }

  if (data.isPrimary !== undefined) {
    updateFields.push('IS_PRIMARY = :isPrimary')
    params.isPrimary = data.isPrimary ? 1 : 0
  }

  if (data.diagnosisDate !== undefined) {
    updateFields.push('DIAGNOSIS_DATE = :diagnosisDate')
    params.diagnosisDate = data.diagnosisDate
  }

  if (data.diagnosisDoctor !== undefined) {
    updateFields.push('DIAGNOSIS_DOCTOR = :diagnosisDoctor')
    params.diagnosisDoctor = data.diagnosisDoctor
  }

  if (data.diagnosisDescription !== undefined) {
    updateFields.push('DIAGNOSIS_DESCRIPTION = :diagnosisDescription')
    params.diagnosisDescription = data.diagnosisDescription
  }

  if (data.treatmentPlan !== undefined) {
    updateFields.push('TREATMENT_PLAN = :treatmentPlan')
    params.treatmentPlan = data.treatmentPlan
  }

  if (updateFields.length === 0) {
    return
  }

  updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP')

  const sql = `
    UPDATE MEDICAL_DIAGNOSIS 
    SET ${updateFields.join(', ')}
    WHERE ID = :id AND IS_DELETED = 0
  `

  await executeQuery(sql, params)
}

/**
 * 删除医疗诊断（软删除）
 */
export async function deleteMedicalDiagnosis(id: number): Promise<void> {
  const sql = `
    UPDATE MEDICAL_DIAGNOSIS 
    SET IS_DELETED = 1, UPDATED_AT = CURRENT_TIMESTAMP
    WHERE ID = :id
  `

  await executeQuery(sql, { id })
}

/**
 * 获取诊断统计信息
 */
export async function getDiagnosisStatistics(caseId?: number) {
  let whereClause = 'WHERE md.IS_DELETED = 0'
  const params: any = {}

  if (caseId) {
    whereClause += ' AND md.CASE_ID = :caseId'
    params.caseId = caseId
  }

  const sql = `
    SELECT 
      COUNT(*) as TOTAL_DIAGNOSES,
      COUNT(CASE WHEN md.IS_PRIMARY = 1 THEN 1 END) as PRIMARY_DIAGNOSES,
      COUNT(DISTINCT md.DIAGNOSIS_CATEGORY) as UNIQUE_CATEGORIES,
      COUNT(DISTINCT md.DIAGNOSIS_DOCTOR) as UNIQUE_DOCTORS
    FROM MEDICAL_DIAGNOSIS md
    ${whereClause}
  `

  const result = await executeQuery(sql, params)
  const row = result.rows[0]

  return {
    totalDiagnoses: row.TOTAL_DIAGNOSES || 0,
    primaryDiagnoses: row.PRIMARY_DIAGNOSES || 0,
    uniqueCategories: row.UNIQUE_CATEGORIES || 0,
    uniqueDoctors: row.UNIQUE_DOCTORS || 0
  }
}
