/**
 * 数据库查询优化工具
 */

import { executeQuery } from './database'

// 查询缓存
const queryCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  MAX_CACHE_SIZE: 100,
  CACHE_TTL: {
    USER_INFO: 10 * 60 * 1000,      // 用户信息缓存10分钟
    MEDICAL_CASE: 2 * 60 * 1000,    // 医疗案例缓存2分钟
    ANALYTICS: 1 * 60 * 1000,       // 分析数据缓存1分钟
    RULES: 15 * 60 * 1000,          // 规则信息缓存15分钟
    SETTINGS: 30 * 60 * 1000,       // 设置信息缓存30分钟
  }
}

/**
 * 带缓存的查询执行
 */
export async function executeQueryWithCache(
  sql: string, 
  params: any = {}, 
  cacheKey?: string,
  ttl?: number
): Promise<any> {
  // 生成缓存键
  const key = cacheKey || generateCacheKey(sql, params)
  
  // 检查缓存
  const cached = getFromCache(key)
  if (cached) {
    console.log(`🎯 缓存命中: ${key}`)
    return cached
  }
  
  // 执行查询
  console.log(`🔍 执行查询: ${key}`)
  const startTime = Date.now()
  const result = await executeQuery(sql, params)
  const duration = Date.now() - startTime
  
  // 记录慢查询
  if (duration > 1000) {
    console.warn(`🐌 慢查询检测 (${duration}ms):`, sql.substring(0, 100) + '...')
  }
  
  // 存入缓存
  const cacheTTL = ttl || CACHE_CONFIG.DEFAULT_TTL
  setToCache(key, result, cacheTTL)
  
  return result
}

/**
 * 生成缓存键
 */
function generateCacheKey(sql: string, params: any): string {
  const sqlHash = btoa(sql).substring(0, 20)
  const paramsHash = btoa(JSON.stringify(params)).substring(0, 10)
  return `query_${sqlHash}_${paramsHash}`
}

/**
 * 从缓存获取数据
 */
function getFromCache(key: string): any | null {
  const cached = queryCache.get(key)
  if (!cached) return null
  
  // 检查是否过期
  if (Date.now() > cached.timestamp + cached.ttl) {
    queryCache.delete(key)
    return null
  }
  
  return cached.data
}

/**
 * 存入缓存
 */
function setToCache(key: string, data: any, ttl: number): void {
  // 检查缓存大小限制
  if (queryCache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
    // 删除最旧的缓存项
    const oldestKey = queryCache.keys().next().value
    if (oldestKey) {
      queryCache.delete(oldestKey)
    }
  }
  
  queryCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  })
}

/**
 * 清除缓存
 */
export function clearCache(pattern?: string): void {
  if (pattern) {
    // 清除匹配模式的缓存
    for (const key of queryCache.keys()) {
      if (key.includes(pattern)) {
        queryCache.delete(key)
      }
    }
  } else {
    // 清除所有缓存
    queryCache.clear()
  }
  console.log(`🧹 缓存已清除${pattern ? ` (模式: ${pattern})` : ''}`)
}

/**
 * 获取缓存统计信息
 */
export function getCacheStats(): {
  size: number
  maxSize: number
  hitRate: number
  keys: string[]
} {
  return {
    size: queryCache.size,
    maxSize: CACHE_CONFIG.MAX_CACHE_SIZE,
    hitRate: 0, // TODO: 实现命中率统计
    keys: Array.from(queryCache.keys())
  }
}

/**
 * 优化的分页查询
 */
export async function executePagedQuery(
  sql: string,
  params: any = {},
  page: number = 1,
  limit: number = 20,
  cacheKey?: string
): Promise<{
  data: any[]
  total: number
  page: number
  limit: number
  totalPages: number
}> {
  const offset = (page - 1) * limit
  
  // 构建分页SQL
  const pagedSql = `
    SELECT * FROM (
      SELECT a.*, ROWNUM rnum FROM (
        ${sql}
      ) a WHERE ROWNUM <= :maxRow
    ) WHERE rnum > :minRow
  `
  
  // 构建计数SQL
  const countSql = `SELECT COUNT(*) as TOTAL_COUNT FROM (${sql})`
  
  const pagedParams = {
    ...params,
    maxRow: offset + limit,
    minRow: offset
  }
  
  // 并行执行数据查询和计数查询
  const [dataResult, countResult] = await Promise.all([
    executeQueryWithCache(pagedSql, pagedParams, cacheKey ? `${cacheKey}_page_${page}_${limit}` : undefined),
    executeQueryWithCache(countSql, params, cacheKey ? `${cacheKey}_count` : undefined)
  ])
  
  const total = countResult.rows?.[0]?.TOTAL_COUNT || 0
  const totalPages = Math.ceil(total / limit)
  
  return {
    data: dataResult.rows || [],
    total,
    page,
    limit,
    totalPages
  }
}

/**
 * 批量查询优化
 */
export async function executeBatchQuery(queries: Array<{
  sql: string
  params?: any
  cacheKey?: string
  ttl?: number
}>): Promise<any[]> {
  console.log(`🔄 执行批量查询: ${queries.length} 个查询`)
  
  const promises = queries.map(query => 
    executeQueryWithCache(query.sql, query.params, query.cacheKey, query.ttl)
  )
  
  return Promise.all(promises)
}

/**
 * 数据库连接池状态监控
 */
export async function getConnectionPoolStats(): Promise<{
  connectionsOpen: number
  connectionsInUse: number
  connectionsAvailable: number
  queueLength: number
}> {
  // TODO: 实现连接池状态监控
  return {
    connectionsOpen: 0,
    connectionsInUse: 0,
    connectionsAvailable: 0,
    queueLength: 0
  }
}

/**
 * 查询性能分析
 */
export class QueryProfiler {
  private static queries: Array<{
    sql: string
    params: any
    duration: number
    timestamp: number
  }> = []
  
  static record(sql: string, params: any, duration: number): void {
    this.queries.push({
      sql,
      params,
      duration,
      timestamp: Date.now()
    })
    
    // 保持最近1000条记录
    if (this.queries.length > 1000) {
      this.queries.shift()
    }
  }
  
  static getSlowQueries(threshold: number = 1000): Array<{
    sql: string
    params: any
    duration: number
    timestamp: number
  }> {
    return this.queries.filter(q => q.duration > threshold)
  }
  
  static getAverageQueryTime(): number {
    if (this.queries.length === 0) return 0
    const total = this.queries.reduce((sum, q) => sum + q.duration, 0)
    return total / this.queries.length
  }
  
  static getQueryStats(): {
    totalQueries: number
    averageTime: number
    slowQueries: number
    fastestQuery: number
    slowestQuery: number
  } {
    if (this.queries.length === 0) {
      return {
        totalQueries: 0,
        averageTime: 0,
        slowQueries: 0,
        fastestQuery: 0,
        slowestQuery: 0
      }
    }
    
    const durations = this.queries.map(q => q.duration)
    
    return {
      totalQueries: this.queries.length,
      averageTime: this.getAverageQueryTime(),
      slowQueries: this.getSlowQueries().length,
      fastestQuery: Math.min(...durations),
      slowestQuery: Math.max(...durations)
    }
  }
  
  static clear(): void {
    this.queries = []
  }
}

/**
 * 优化的executeQuery包装器
 */
export async function optimizedExecuteQuery(
  sql: string,
  params: any = {},
  options: {
    cache?: boolean
    cacheKey?: string
    ttl?: number
    profile?: boolean
  } = {}
): Promise<any> {
  const startTime = Date.now()
  
  try {
    let result: any
    
    if (options.cache) {
      result = await executeQueryWithCache(sql, params, options.cacheKey, options.ttl)
    } else {
      result = await executeQuery(sql, params)
    }
    
    const duration = Date.now() - startTime
    
    // 记录性能数据
    if (options.profile) {
      QueryProfiler.record(sql, params, duration)
    }
    
    return result
    
  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ 查询执行失败 (${duration}ms):`, error)
    throw error
  }
}
