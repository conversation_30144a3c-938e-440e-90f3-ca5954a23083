/**
 * 统一的格式化工具库 - 避免重复代码
 */

/**
 * 格式化货币
 */
export function formatCurrency(
  amount: number | undefined | null,
  options: {
    currency?: string
    locale?: string
    minimumFractionDigits?: number
    maximumFractionDigits?: number
    showSymbol?: boolean
  } = {}
): string {
  if (amount === undefined || amount === null || isNaN(amount)) return '-'
  
  const {
    currency = 'CNY',
    locale = 'zh-CN',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showSymbol = true
  } = options

  if (showSymbol) {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(amount)
  } else {
    return amount.toLocaleString(locale, {
      minimumFractionDigits,
      maximumFractionDigits,
    })
  }
}

/**
 * 格式化大数字（万、千万等）
 */
export function formatLargeNumber(
  num: number | undefined | null,
  options: {
    locale?: string
    useChineseUnits?: boolean
  } = {}
): string {
  if (num === undefined || num === null || isNaN(num)) return '-'
  
  const { locale = 'zh-CN', useChineseUnits = true } = options

  if (useChineseUnits) {
    if (num >= 100000000) {
      return (num / 100000000).toFixed(1) + '亿'
    } else if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    }
    return num.toLocaleString(locale)
  } else {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(
  bytes: number | undefined | null,
  options: {
    decimals?: number
    binary?: boolean
  } = {}
): string {
  if (bytes === undefined || bytes === null || bytes === 0) return '0 B'
  
  const { decimals = 1, binary = true } = options
  const k = binary ? 1024 : 1000
  const sizes = binary 
    ? ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
    : ['B', 'kB', 'MB', 'GB', 'TB', 'PB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const size = bytes / Math.pow(k, i)
  
  return `${size.toFixed(decimals)} ${sizes[i]}`
}

/**
 * 格式化日期
 */
export function formatDate(
  date: string | Date | undefined | null,
  options: {
    locale?: string
    format?: 'short' | 'medium' | 'long' | 'full'
    includeTime?: boolean
    timeZone?: string
  } = {}
): string {
  if (!date) return '-'
  
  const {
    locale = 'zh-CN',
    format = 'medium',
    includeTime = false,
    timeZone
  } = options

  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) return '-'

  const formatOptions: Intl.DateTimeFormatOptions = {
    timeZone,
    ...(format === 'short' && {
      year: '2-digit',
      month: 'numeric',
      day: 'numeric'
    }),
    ...(format === 'medium' && {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }),
    ...(format === 'long' && {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    ...(format === 'full' && {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    ...(includeTime && {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return new Intl.DateTimeFormat(locale, formatOptions).format(dateObj)
}

/**
 * 格式化时间间隔
 */
export function formatDuration(
  milliseconds: number | undefined | null,
  options: {
    format?: 'short' | 'long'
    maxUnits?: number
  } = {}
): string {
  if (milliseconds === undefined || milliseconds === null) return '-'
  
  const { format = 'short', maxUnits = 2 } = options
  
  const units = [
    { name: '天', shortName: 'd', value: 24 * 60 * 60 * 1000 },
    { name: '小时', shortName: 'h', value: 60 * 60 * 1000 },
    { name: '分钟', shortName: 'm', value: 60 * 1000 },
    { name: '秒', shortName: 's', value: 1000 },
    { name: '毫秒', shortName: 'ms', value: 1 }
  ]

  const parts: string[] = []
  let remaining = Math.abs(milliseconds)

  for (const unit of units) {
    if (remaining >= unit.value && parts.length < maxUnits) {
      const count = Math.floor(remaining / unit.value)
      remaining %= unit.value
      
      const unitName = format === 'short' ? unit.shortName : unit.name
      parts.push(`${count}${unitName}`)
    }
  }

  if (parts.length === 0) {
    const unitName = format === 'short' ? 'ms' : '毫秒'
    return `0${unitName}`
  }

  return parts.join(' ')
}

/**
 * 格式化百分比
 */
export function formatPercentage(
  value: number | undefined | null,
  options: {
    decimals?: number
    locale?: string
    showSign?: boolean
  } = {}
): string {
  if (value === undefined || value === null) return '-'
  
  const { decimals = 1, locale = 'zh-CN', showSign = true } = options
  
  const formatted = new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100)

  return showSign ? formatted : formatted.replace('%', '')
}

/**
 * 格式化电话号码
 */
export function formatPhoneNumber(
  phone: string | undefined | null,
  options: {
    format?: 'standard' | 'international' | 'compact'
    countryCode?: string
  } = {}
): string {
  if (!phone) return '-'
  
  const { format = 'standard', countryCode = '+86' } = options
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.length !== 11) return phone // 返回原始值如果格式不正确
  
  switch (format) {
    case 'international':
      return `${countryCode} ${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7)}`
    case 'compact':
      return cleaned
    case 'standard':
    default:
      return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 7)}-${cleaned.slice(7)}`
  }
}

/**
 * 格式化身份证号
 */
export function formatIdCard(
  idCard: string | undefined | null,
  options: {
    mask?: boolean
    maskChar?: string
  } = {}
): string {
  if (!idCard) return '-'
  
  const { mask = false, maskChar = '*' } = options
  
  if (!mask) return idCard
  
  // 显示前4位和后4位，中间用*替代
  if (idCard.length >= 8) {
    const start = idCard.slice(0, 4)
    const end = idCard.slice(-4)
    const middle = maskChar.repeat(idCard.length - 8)
    return `${start}${middle}${end}`
  }
  
  return idCard
}

/**
 * 格式化文本截断
 */
export function truncateText(
  text: string | undefined | null,
  options: {
    maxLength?: number
    suffix?: string
    wordBoundary?: boolean
  } = {}
): string {
  if (!text) return '-'
  
  const { maxLength = 100, suffix = '...', wordBoundary = true } = options
  
  if (text.length <= maxLength) return text
  
  let truncated = text.slice(0, maxLength)
  
  if (wordBoundary) {
    const lastSpace = truncated.lastIndexOf(' ')
    if (lastSpace > 0) {
      truncated = truncated.slice(0, lastSpace)
    }
  }
  
  return truncated + suffix
}

/**
 * 格式化枚举值为可读文本
 */
export function formatEnumValue(
  value: string | undefined | null,
  enumMap: Record<string, string>
): string {
  if (!value) return '-'
  return enumMap[value] || value
}
