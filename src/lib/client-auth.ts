/**
 * 客户端认证工具函数
 * 这些函数可以在客户端安全使用，不涉及JWT密钥验证
 */

/**
 * 解析JWT token的payload（不验证签名）
 * 仅用于客户端读取token信息，不做安全验证
 */
export function parseJWTPayload(token: string): any | null {
  try {
    if (!token || typeof token !== 'string') {
      return null
    }

    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }

    const payload = parts[1]
    if (!payload) {
      return null
    }
    const decoded = atob(payload)
    return JSON.parse(decoded)
  } catch (error) {
    console.error('❌ 解析JWT payload失败:', error)
    return null
  }
}

/**
 * 检查token是否即将过期（客户端安全检查）
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
  try {
    const payload = parseJWTPayload(token)
    if (!payload || !payload.exp) {
      return true
    }

    const now = Math.floor(Date.now() / 1000)
    const threshold = thresholdMinutes * 60
    
    return (payload.exp - now) < threshold
  } catch (error) {
    console.error('❌ 检查token过期时间失败:', error)
    return true
  }
}

/**
 * 获取token剩余时间（秒）
 */
export function getTokenRemainingTime(token: string): number {
  try {
    const payload = parseJWTPayload(token)
    if (!payload || !payload.exp) {
      return 0
    }

    const now = Math.floor(Date.now() / 1000)
    return Math.max(0, payload.exp - now)
  } catch (error) {
    console.error('❌ 获取token剩余时间失败:', error)
    return 0
  }
}

/**
 * 检查token是否有效（基本格式检查）
 */
export function isTokenValid(token: string): boolean {
  try {
    if (!token || typeof token !== 'string') {
      return false
    }

    const parts = token.split('.')
    if (parts.length !== 3) {
      return false
    }

    const payload = parseJWTPayload(token)
    if (!payload) {
      return false
    }

    // 检查是否过期
    if (payload.exp) {
      const now = Math.floor(Date.now() / 1000)
      if (now >= payload.exp) {
        return false
      }
    }

    return true
  } catch (error) {
    console.error('❌ 检查token有效性失败:', error)
    return false
  }
}

/**
 * 从token中提取用户信息
 */
export function getUserInfoFromToken(token: string): {
  userId?: number
  username?: string
  roles?: string[]
} | null {
  try {
    const payload = parseJWTPayload(token)
    if (!payload) {
      return null
    }

    return {
      userId: payload.userId,
      username: payload.username,
      roles: payload.roles || []
    }
  } catch (error) {
    console.error('❌ 从token提取用户信息失败:', error)
    return null
  }
}

/**
 * 格式化token过期时间
 */
export function formatTokenExpiry(token: string): string {
  try {
    const payload = parseJWTPayload(token)
    if (!payload || !payload.exp) {
      return '未知'
    }

    const expiryDate = new Date(payload.exp * 1000)
    return expiryDate.toLocaleString('zh-CN')
  } catch (error) {
    console.error('❌ 格式化token过期时间失败:', error)
    return '未知'
  }
}

/**
 * 清理认证相关的本地存储和Cookie
 */
export function clearAuthStorage(): void {
  try {
    // 清理localStorage
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_info')
    localStorage.removeItem('refresh_token')

    // 清理Cookie
    document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
    document.cookie = 'refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  } catch (error) {
    console.error('❌ 清理认证存储失败:', error)
  }
}

/**
 * 保存认证信息到本地存储和Cookie
 */
export function saveAuthToStorage(token: string, user: any, refreshToken?: string): void {
  try {
    // 保存到localStorage
    localStorage.setItem('access_token', token)
    localStorage.setItem('user_info', JSON.stringify(user))
    if (refreshToken) {
      localStorage.setItem('refresh_token', refreshToken)
    }

    // 保存到Cookie（用于中间件访问）
    // 设置HttpOnly=false以便JavaScript可以访问，但在生产环境中应该考虑安全性
    const tokenExpiry = getTokenRemainingTime(token)
    const expiryDate = new Date(Date.now() + tokenExpiry * 1000)

    document.cookie = `access_token=${token}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Strict`

    if (refreshToken) {
      // 刷新token通常有更长的过期时间
      const refreshExpiryDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天
      document.cookie = `refresh_token=${refreshToken}; expires=${refreshExpiryDate.toUTCString()}; path=/; SameSite=Strict`
    }
  } catch (error) {
    console.error('❌ 保存认证信息失败:', error)
  }
}

/**
 * 从本地存储加载认证信息
 */
export function loadAuthFromStorage(): {
  token: string | null
  user: any | null
  refreshToken: string | null
} {
  try {
    const token = localStorage.getItem('access_token')
    const userStr = localStorage.getItem('user_info')
    const refreshToken = localStorage.getItem('refresh_token')

    let user = null
    if (userStr) {
      try {
        user = JSON.parse(userStr)
      } catch (parseError) {
        console.error('❌ 解析用户信息失败:', parseError)
      }
    }

    return {
      token,
      user,
      refreshToken
    }
  } catch (error) {
    console.error('❌ 加载认证信息失败:', error)
    return {
      token: null,
      user: null,
      refreshToken: null
    }
  }
}
