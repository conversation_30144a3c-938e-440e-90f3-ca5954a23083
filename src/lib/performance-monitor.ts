/**
 * 数据库性能监控工具
 * 用于验证P0-003数据库查询性能优化的效果
 */

import { executeQuery } from '@/lib/database'

export interface PerformanceMetric {
  queryType: string
  executionTime: number
  recordCount: number
  cacheHit: boolean
  timestamp: Date
  status: 'success' | 'error'
  errorMessage?: string
}

export interface PerformanceBenchmark {
  medicalCaseQuery: PerformanceMetric
  ruleExecutionLogQuery: PerformanceMetric
  userQuery: PerformanceMetric
  overallScore: number
  recommendations: string[]
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private readonly PERFORMANCE_TARGETS = {
    medicalCaseQuery: 500, // 500ms
    ruleExecutionLogQuery: 300, // 300ms
    userQuery: 200, // 200ms
  }

  /**
   * 执行性能基准测试
   */
  async runBenchmark(): Promise<PerformanceBenchmark> {
    console.log('🚀 开始数据库性能基准测试...')

    const medicalCaseQuery = await this.testMedicalCaseQuery()
    const ruleExecutionLogQuery = await this.testRuleExecutionLogQuery()
    const userQuery = await this.testUserQuery()

    const overallScore = this.calculateOverallScore([
      medicalCaseQuery,
      ruleExecutionLogQuery,
      userQuery
    ])

    const recommendations = this.generateRecommendations([
      medicalCaseQuery,
      ruleExecutionLogQuery,
      userQuery
    ])

    const benchmark: PerformanceBenchmark = {
      medicalCaseQuery,
      ruleExecutionLogQuery,
      userQuery,
      overallScore,
      recommendations
    }

    console.log('✅ 性能基准测试完成')
    this.logBenchmarkResults(benchmark)

    return benchmark
  }

  /**
   * 测试医疗案例查询性能
   */
  private async testMedicalCaseQuery(): Promise<PerformanceMetric> {
    const startTime = Date.now()
    
    try {
      const sql = `
        SELECT COUNT(*) as TOTAL
        FROM MEDICAL_CASE mc
        WHERE mc.IS_DELETED = 0 
          AND mc.CASE_TYPE = 'INPATIENT'
          AND mc.MEDICAL_CATEGORY = '异地住院'
          AND mc.CREATED_AT >= SYSDATE - 30
      `

      const result = await executeQuery(sql)
      const executionTime = Date.now() - startTime
      const recordCount = result.rows?.[0]?.TOTAL || 0

      const metric: PerformanceMetric = {
        queryType: 'medical_case_query',
        executionTime,
        recordCount,
        cacheHit: false,
        timestamp: new Date(),
        status: 'success'
      }

      this.metrics.push(metric)
      return metric

    } catch (error) {
      const executionTime = Date.now() - startTime
      const metric: PerformanceMetric = {
        queryType: 'medical_case_query',
        executionTime,
        recordCount: 0,
        cacheHit: false,
        timestamp: new Date(),
        status: 'error',
        errorMessage: error instanceof Error ? error.message : String(error)
      }

      this.metrics.push(metric)
      return metric
    }
  }

  /**
   * 测试规则执行日志查询性能
   */
  private async testRuleExecutionLogQuery(): Promise<PerformanceMetric> {
    const startTime = Date.now()
    
    try {
      const sql = `
        SELECT COUNT(*) as TOTAL
        FROM RULE_EXECUTION_LOG rel
        LEFT JOIN RULE_SUPERVISION rs ON rel.RULE_ID = rs.ID
        WHERE rel.EXECUTION_STATUS = 'SUCCESS'
          AND rel.STARTED_AT >= SYSDATE - 7
      `

      const result = await executeQuery(sql)
      const executionTime = Date.now() - startTime
      const recordCount = result.rows?.[0]?.TOTAL || 0

      const metric: PerformanceMetric = {
        queryType: 'rule_execution_log_query',
        executionTime,
        recordCount,
        cacheHit: false,
        timestamp: new Date(),
        status: 'success'
      }

      this.metrics.push(metric)
      return metric

    } catch (error) {
      const executionTime = Date.now() - startTime
      const metric: PerformanceMetric = {
        queryType: 'rule_execution_log_query',
        executionTime,
        recordCount: 0,
        cacheHit: false,
        timestamp: new Date(),
        status: 'error',
        errorMessage: error instanceof Error ? error.message : String(error)
      }

      this.metrics.push(metric)
      return metric
    }
  }

  /**
   * 测试用户查询性能
   */
  private async testUserQuery(): Promise<PerformanceMetric> {
    const startTime = Date.now()
    
    try {
      const sql = `
        SELECT COUNT(*) as TOTAL
        FROM USER_INFO u
        WHERE u.IS_DELETED = 0 
          AND u.STATUS = 'ACTIVE'
          AND (UPPER(u.USERNAME) LIKE '%ADMIN%' OR UPPER(u.REAL_NAME) LIKE '%ADMIN%')
      `

      const result = await executeQuery(sql)
      const executionTime = Date.now() - startTime
      const recordCount = result.rows?.[0]?.TOTAL || 0

      const metric: PerformanceMetric = {
        queryType: 'user_query',
        executionTime,
        recordCount,
        cacheHit: false,
        timestamp: new Date(),
        status: 'success'
      }

      this.metrics.push(metric)
      return metric

    } catch (error) {
      const executionTime = Date.now() - startTime
      const metric: PerformanceMetric = {
        queryType: 'user_query',
        executionTime,
        recordCount: 0,
        cacheHit: false,
        timestamp: new Date(),
        status: 'error',
        errorMessage: error instanceof Error ? error.message : String(error)
      }

      this.metrics.push(metric)
      return metric
    }
  }

  /**
   * 计算总体性能评分
   */
  private calculateOverallScore(metrics: PerformanceMetric[]): number {
    let totalScore = 0
    let validMetrics = 0

    for (const metric of metrics) {
      if (metric.status === 'success') {
        const target = this.PERFORMANCE_TARGETS[metric.queryType as keyof typeof this.PERFORMANCE_TARGETS]
        if (target) {
          // 计算性能评分：执行时间越接近目标，分数越高
          const score = Math.max(0, Math.min(100, (target / metric.executionTime) * 100))
          totalScore += score
          validMetrics++
        }
      }
    }

    return validMetrics > 0 ? Math.round(totalScore / validMetrics) : 0
  }

  /**
   * 生成性能优化建议
   */
  private generateRecommendations(metrics: PerformanceMetric[]): string[] {
    const recommendations: string[] = []

    for (const metric of metrics) {
      const target = this.PERFORMANCE_TARGETS[metric.queryType as keyof typeof this.PERFORMANCE_TARGETS]
      
      if (metric.status === 'error') {
        recommendations.push(`❌ ${metric.queryType}: 查询执行失败 - ${metric.errorMessage}`)
      } else if (target && metric.executionTime > target) {
        const slowness = ((metric.executionTime - target) / target * 100).toFixed(1)
        recommendations.push(`⚠️ ${metric.queryType}: 执行时间${metric.executionTime}ms，超出目标${slowness}%`)
        
        // 具体优化建议
        switch (metric.queryType) {
          case 'medical_case_query':
            recommendations.push('  💡 建议：检查MEDICAL_CASE表的复合索引是否已创建')
            break
          case 'rule_execution_log_query':
            recommendations.push('  💡 建议：检查RULE_EXECUTION_LOG表的索引优化')
            break
          case 'user_query':
            recommendations.push('  💡 建议：检查USER_INFO表的搜索索引')
            break
        }
      } else {
        recommendations.push(`✅ ${metric.queryType}: 性能良好 (${metric.executionTime}ms)`)
      }
    }

    return recommendations
  }

  /**
   * 记录基准测试结果
   */
  private logBenchmarkResults(benchmark: PerformanceBenchmark): void {
    console.log('\n📊 数据库性能基准测试结果:')
    console.log('=' .repeat(50))
    console.log(`总体评分: ${benchmark.overallScore}/100`)
    console.log('\n详细指标:')
    
    const metrics = [
      benchmark.medicalCaseQuery,
      benchmark.ruleExecutionLogQuery,
      benchmark.userQuery
    ]

    for (const metric of metrics) {
      const target = this.PERFORMANCE_TARGETS[metric.queryType as keyof typeof this.PERFORMANCE_TARGETS]
      const status = metric.status === 'success' && target && metric.executionTime <= target ? '✅' : '⚠️'
      console.log(`${status} ${metric.queryType}: ${metric.executionTime}ms (目标: ${target}ms)`)
    }

    console.log('\n优化建议:')
    benchmark.recommendations.forEach(rec => console.log(rec))
    console.log('=' .repeat(50))
  }

  /**
   * 获取历史性能指标
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  /**
   * 清除历史指标
   */
  clearMetrics(): void {
    this.metrics = []
  }
}

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor()

// 导出便捷函数
export async function runPerformanceBenchmark(): Promise<PerformanceBenchmark> {
  return performanceMonitor.runBenchmark()
}
