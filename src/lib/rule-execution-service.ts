/**
 * 规则执行记录服务
 * 
 * 负责管理规则执行记录和结果的查询、统计分析
 */

import { executeQuery } from '@/lib/database'
import { executeQueryWithCache, CACHE_CONFIG } from '@/lib/database-optimizer'

// 执行记录接口
export interface RuleExecutionLog {
  id: number
  ruleId: number
  executionId: string
  executionStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'TIMEOUT'
  startedAt: string
  endedAt?: string
  executionDuration?: number
  processedRecordCount: number
  matchedRecordCount: number
  errorMessage?: string
  executionResult?: any
  createdAt: string
  // 关联的规则信息
  ruleName?: string
  ruleCode?: string
  ruleCategory?: string
  severityLevel?: string
}

// 执行结果接口
export interface RuleExecutionResult {
  id: number
  executionLogId: number
  ruleId: number
  caseId?: number
  resultType: 'VIOLATION' | 'SUSPICIOUS' | 'NORMAL' | 'WARNING'
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  matchScore: number
  resultDescription: string
  resultDetails: any
  evidenceData: any
  resultStatus: 'PENDING' | 'CONFIRMED' | 'REJECTED' | 'IGNORED'
  createdAt: string
}

// 执行统计接口
export interface ExecutionStatistics {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageExecutionTime: number
  totalProcessedRecords: number
  totalMatchedRecords: number
  riskLevelDistribution: Record<string, number>
  categoryDistribution: Record<string, number>
  recentExecutions: RuleExecutionLog[]
}

// 查询参数接口
export interface ExecutionListParams {
  page?: number
  pageSize?: number
  search?: string
  ruleId?: number
  executionStatus?: string
  riskLevel?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

// 分页响应接口
export interface ExecutionPaginationResponse {
  data: RuleExecutionLog[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

/**
 * 获取执行记录列表
 */
export async function getExecutionLogs(params: ExecutionListParams): Promise<ExecutionPaginationResponse> {
  try {
    const {
      page = 1,
      pageSize = 10,
      search = '',
      ruleId,
      executionStatus,
      startDate,
      endDate,
      sortBy = 'STARTED_AT',
      sortOrder = 'DESC'
    } = params

    // 构建WHERE条件
    const conditions: string[] = []
    const queryParams: any = {}

    if (search) {
      conditions.push(`(
        UPPER(el.EXECUTION_ID) LIKE UPPER(:search) OR 
        UPPER(rs.RULE_NAME) LIKE UPPER(:search) OR 
        UPPER(rs.RULE_CODE) LIKE UPPER(:search)
      )`)
      queryParams.search = `%${search}%`
    }

    if (ruleId) {
      conditions.push('el.RULE_ID = :ruleId')
      queryParams.ruleId = ruleId
    }

    if (executionStatus) {
      conditions.push('el.EXECUTION_STATUS = :executionStatus')
      queryParams.executionStatus = executionStatus
    }

    if (startDate) {
      conditions.push('el.STARTED_AT >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = startDate
    }

    if (endDate) {
      conditions.push('el.STARTED_AT <= TO_DATE(:endDate, \'YYYY-MM-DD\') + 1')
      queryParams.endDate = endDate
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as TOTAL_COUNT
      FROM RULE_EXECUTION_LOG el
      LEFT JOIN RULE_SUPERVISION rs ON el.RULE_ID = rs.ID
      ${whereClause}
    `

    const countResult = await executeQuery(countSql, queryParams)
    const total = countResult.rows?.[0]?.TOTAL_COUNT || 0
    const totalPages = Math.ceil(total / pageSize)

    // 获取数据
    const offset = (page - 1) * pageSize
    const dataSql = `
      SELECT 
        el.ID, el.RULE_ID, el.EXECUTION_ID, el.EXECUTION_STATUS,
        el.STARTED_AT, el.ENDED_AT, el.EXECUTION_DURATION,
        el.PROCESSED_RECORD_COUNT, el.MATCHED_RECORD_COUNT,
        el.ERROR_MESSAGE, el.EXECUTION_RESULT, el.CREATED_AT,
        rs.RULE_NAME, rs.RULE_CODE, rs.RULE_CATEGORY, rs.SEVERITY_LEVEL
      FROM RULE_EXECUTION_LOG el
      LEFT JOIN RULE_SUPERVISION rs ON el.RULE_ID = rs.ID
      ${whereClause}
      ORDER BY el.${sortBy} ${sortOrder}
      OFFSET ${offset} ROWS FETCH NEXT ${pageSize} ROWS ONLY
    `

    const dataResult = await executeQuery(dataSql, queryParams)

    const data = (dataResult.rows || []).map((row: any) => ({
      id: row.ID,
      ruleId: row.RULE_ID,
      executionId: row.EXECUTION_ID,
      executionStatus: row.EXECUTION_STATUS,
      startedAt: row.STARTED_AT?.toISOString(),
      endedAt: row.ENDED_AT?.toISOString(),
      executionDuration: row.EXECUTION_DURATION,
      processedRecordCount: row.PROCESSED_RECORD_COUNT,
      matchedRecordCount: row.MATCHED_RECORD_COUNT,
      errorMessage: row.ERROR_MESSAGE,
      executionResult: row.EXECUTION_RESULT ? JSON.parse(row.EXECUTION_RESULT) : null,
      createdAt: row.CREATED_AT?.toISOString(),
      ruleName: row.RULE_NAME,
      ruleCode: row.RULE_CODE,
      ruleCategory: row.RULE_CATEGORY,
      severityLevel: row.SEVERITY_LEVEL
    }))

    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages
      }
    }

  } catch (error) {
    console.error('❌ 获取执行记录列表失败:', error)
    throw new Error(`获取执行记录列表失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取执行记录详情
 */
export async function getExecutionLogById(id: number): Promise<RuleExecutionLog | null> {
  try {
    const sql = `
      SELECT 
        el.ID, el.RULE_ID, el.EXECUTION_ID, el.EXECUTION_STATUS,
        el.STARTED_AT, el.ENDED_AT, el.EXECUTION_DURATION,
        el.PROCESSED_RECORD_COUNT, el.MATCHED_RECORD_COUNT,
        el.ERROR_MESSAGE, el.EXECUTION_RESULT, el.CREATED_AT,
        rs.RULE_NAME, rs.RULE_CODE, rs.RULE_CATEGORY, rs.SEVERITY_LEVEL
      FROM RULE_EXECUTION_LOG el
      LEFT JOIN RULE_SUPERVISION rs ON el.RULE_ID = rs.ID
      WHERE el.ID = :id
    `

    const result = await executeQuery(sql, { id })
    
    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.ID,
      ruleId: row.RULE_ID,
      executionId: row.EXECUTION_ID,
      executionStatus: row.EXECUTION_STATUS,
      startedAt: row.STARTED_AT?.toISOString(),
      endedAt: row.ENDED_AT?.toISOString(),
      executionDuration: row.EXECUTION_DURATION,
      processedRecordCount: row.PROCESSED_RECORD_COUNT,
      matchedRecordCount: row.MATCHED_RECORD_COUNT,
      errorMessage: row.ERROR_MESSAGE,
      executionResult: row.EXECUTION_RESULT ? JSON.parse(row.EXECUTION_RESULT) : null,
      createdAt: row.CREATED_AT?.toISOString(),
      ruleName: row.RULE_NAME,
      ruleCode: row.RULE_CODE,
      ruleCategory: row.RULE_CATEGORY,
      severityLevel: row.SEVERITY_LEVEL
    }

  } catch (error) {
    console.error('❌ 获取执行记录详情失败:', error)
    throw new Error(`获取执行记录详情失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取执行结果列表
 */
export async function getExecutionResults(executionLogId: number): Promise<RuleExecutionResult[]> {
  try {
    const sql = `
      SELECT 
        ID, EXECUTION_LOG_ID, RULE_ID, CASE_ID,
        RESULT_TYPE, RISK_LEVEL, MATCH_SCORE,
        RESULT_DESCRIPTION, RESULT_DETAILS, EVIDENCE_DATA,
        RESULT_STATUS, CREATED_AT
      FROM RULE_EXECUTION_RESULT
      WHERE EXECUTION_LOG_ID = :executionLogId
      ORDER BY MATCH_SCORE DESC, CREATED_AT DESC
    `

    const result = await executeQuery(sql, { executionLogId })

    return (result.rows || []).map((row: any) => ({
      id: row.ID,
      executionLogId: row.EXECUTION_LOG_ID,
      ruleId: row.RULE_ID,
      caseId: row.CASE_ID,
      resultType: row.RESULT_TYPE,
      riskLevel: row.RISK_LEVEL,
      matchScore: row.MATCH_SCORE,
      resultDescription: row.RESULT_DESCRIPTION,
      resultDetails: row.RESULT_DETAILS ? JSON.parse(row.RESULT_DETAILS) : null,
      evidenceData: row.EVIDENCE_DATA ? JSON.parse(row.EVIDENCE_DATA) : null,
      resultStatus: row.RESULT_STATUS,
      createdAt: row.CREATED_AT?.toISOString()
    }))

  } catch (error) {
    console.error('❌ 获取执行结果列表失败:', error)
    throw new Error(`获取执行结果列表失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取执行统计信息
 */
export async function getExecutionStatistics(ruleId?: number, days: number = 30): Promise<ExecutionStatistics> {
  try {
    const conditions = ['el.STARTED_AT >= SYSDATE - :days']
    const params: any = { days }

    if (ruleId) {
      conditions.push('el.RULE_ID = :ruleId')
      params.ruleId = ruleId
    }

    const whereClause = `WHERE ${conditions.join(' AND ')}`

    // 基础统计
    const basicStatsSql = `
      SELECT 
        COUNT(*) as TOTAL_EXECUTIONS,
        SUM(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) as SUCCESSFUL_EXECUTIONS,
        SUM(CASE WHEN EXECUTION_STATUS = 'FAILED' THEN 1 ELSE 0 END) as FAILED_EXECUTIONS,
        AVG(EXECUTION_DURATION) as AVG_EXECUTION_TIME,
        SUM(PROCESSED_RECORD_COUNT) as TOTAL_PROCESSED_RECORDS,
        SUM(MATCHED_RECORD_COUNT) as TOTAL_MATCHED_RECORDS
      FROM RULE_EXECUTION_LOG el
      ${whereClause}
    `

    const basicStats = await executeQuery(basicStatsSql, params)
    const stats = (basicStats.rows && basicStats.rows[0]) || {}

    // 风险等级分布
    const riskDistributionSql = `
      SELECT 
        er.RISK_LEVEL,
        COUNT(*) as COUNT
      FROM RULE_EXECUTION_LOG el
      JOIN RULE_EXECUTION_RESULT er ON el.ID = er.EXECUTION_LOG_ID
      ${whereClause}
      GROUP BY er.RISK_LEVEL
    `

    const riskDistribution = await executeQuery(riskDistributionSql, params)
    const riskLevelDistribution: Record<string, number> = {};
    (riskDistribution.rows || []).forEach((row: any) => {
      riskLevelDistribution[row.RISK_LEVEL] = row.COUNT
    })

    // 分类分布
    const categoryDistributionSql = `
      SELECT 
        rs.RULE_CATEGORY,
        COUNT(*) as COUNT
      FROM RULE_EXECUTION_LOG el
      JOIN RULE_SUPERVISION rs ON el.RULE_ID = rs.ID
      ${whereClause}
      GROUP BY rs.RULE_CATEGORY
    `

    const categoryDistribution = await executeQuery(categoryDistributionSql, params)
    const categoryDist: Record<string, number> = {};
    (categoryDistribution.rows || []).forEach((row: any) => {
      categoryDist[row.RULE_CATEGORY] = row.COUNT
    })

    // 最近执行记录
    const recentExecutionsSql = `
      SELECT 
        el.ID, el.RULE_ID, el.EXECUTION_ID, el.EXECUTION_STATUS,
        el.STARTED_AT, el.ENDED_AT, el.EXECUTION_DURATION,
        el.PROCESSED_RECORD_COUNT, el.MATCHED_RECORD_COUNT,
        rs.RULE_NAME, rs.RULE_CODE
      FROM RULE_EXECUTION_LOG el
      LEFT JOIN RULE_SUPERVISION rs ON el.RULE_ID = rs.ID
      ${whereClause}
      ORDER BY el.STARTED_AT DESC
      FETCH FIRST 10 ROWS ONLY
    `

    const recentExecutionsResult = await executeQuery(recentExecutionsSql, params)
    const recentExecutions = (recentExecutionsResult.rows || []).map((row: any) => ({
      id: row.ID,
      ruleId: row.RULE_ID,
      executionId: row.EXECUTION_ID,
      executionStatus: row.EXECUTION_STATUS,
      startedAt: row.STARTED_AT?.toISOString(),
      endedAt: row.ENDED_AT?.toISOString(),
      executionDuration: row.EXECUTION_DURATION,
      processedRecordCount: row.PROCESSED_RECORD_COUNT,
      matchedRecordCount: row.MATCHED_RECORD_COUNT,
      ruleName: row.RULE_NAME,
      ruleCode: row.RULE_CODE,
      createdAt: row.STARTED_AT?.toISOString() || new Date().toISOString()
    }))

    return {
      totalExecutions: stats.TOTAL_EXECUTIONS || 0,
      successfulExecutions: stats.SUCCESSFUL_EXECUTIONS || 0,
      failedExecutions: stats.FAILED_EXECUTIONS || 0,
      averageExecutionTime: stats.AVG_EXECUTION_TIME || 0,
      totalProcessedRecords: stats.TOTAL_PROCESSED_RECORDS || 0,
      totalMatchedRecords: stats.TOTAL_MATCHED_RECORDS || 0,
      riskLevelDistribution,
      categoryDistribution: categoryDist,
      recentExecutions
    }

  } catch (error) {
    console.error('❌ 获取执行统计信息失败:', error)
    throw new Error(`获取执行统计信息失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取规则执行结果的完整关联数据
 */
export async function getRuleExecutionResultWithRelations(resultId: number): Promise<{
  result: RuleExecutionResult | null
  executionLog: RuleExecutionLog | null
  auditResults: any[]
  relatedCases: any[]
}> {
  try {
    // 并行查询所有关联数据
    const [
      resultData,
      executionLogData,
      auditResultsData,
      relatedCasesData
    ] = await Promise.all([
      getExecutionResults(resultId),
      getExecutionLogByResultId(resultId),
      getAuditResultsByExecutionResultId(resultId),
      getRelatedCasesByResultId(resultId)
    ])

    return {
      result: resultData[0] || null,
      executionLog: executionLogData,
      auditResults: auditResultsData,
      relatedCases: relatedCasesData
    }
  } catch (error) {
    console.error('获取规则执行结果完整关联数据失败:', error)
    throw new Error('获取规则执行结果完整关联数据失败')
  }
}

/**
 * 根据结果ID获取执行日志
 */
async function getExecutionLogByResultId(resultId: number): Promise<RuleExecutionLog | null> {
  try {
    const sql = `
      SELECT
        rel.*,
        sr.RULE_NAME,
        sr.RULE_CODE,
        sr.CATEGORY as RULE_CATEGORY,
        sr.SEVERITY_LEVEL
      FROM RULE_EXECUTION_LOG rel
      LEFT JOIN SUPERVISION_RULE sr ON rel.RULE_ID = sr.ID
      WHERE rel.ID = (
        SELECT EXECUTION_LOG_ID
        FROM RULE_EXECUTION_RESULT
        WHERE ID = :resultId
      )
    `

    const result = await executeQueryWithCache(
      sql,
      { resultId },
      `execution_log_by_result_${resultId}`,
      CACHE_CONFIG.CACHE_TTL.RULES
    )

    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.ID,
      ruleId: row.RULE_ID,
      executionId: row.EXECUTION_ID,
      executionStatus: row.EXECUTION_STATUS,
      startedAt: row.STARTED_AT,
      endedAt: row.ENDED_AT,
      executionDuration: row.EXECUTION_DURATION,
      processedRecordCount: row.PROCESSED_RECORD_COUNT,
      matchedRecordCount: row.MATCHED_RECORD_COUNT,
      errorMessage: row.ERROR_MESSAGE,
      executionResult: row.EXECUTION_RESULT,
      createdAt: row.CREATED_AT,
      ruleName: row.RULE_NAME,
      ruleCode: row.RULE_CODE,
      ruleCategory: row.RULE_CATEGORY,
      severityLevel: row.SEVERITY_LEVEL
    }
  } catch (error) {
    console.error('获取执行日志失败:', error)
    return null
  }
}

/**
 * 获取审计结果
 */
async function getAuditResultsByExecutionResultId(resultId: number): Promise<any[]> {
  try {
    const sql = `
      SELECT
        rar.*,
        u.USERNAME as AUDITOR_NAME,
        u.REAL_NAME as AUDITOR_REAL_NAME
      FROM RULE_AUDIT_RESULT rar
      LEFT JOIN USER_MANAGEMENT_USER u ON rar.AUDITOR_ID = u.ID
      WHERE rar.RESULT_ID = :resultId
      ORDER BY rar.AUDITED_AT DESC
    `

    const result = await executeQuery(sql, { resultId })

    return result.rows?.map((row: any) => ({
      id: row.ID,
      resultId: row.RESULT_ID,
      auditStatus: row.AUDIT_STATUS,
      auditOpinion: row.AUDIT_OPINION,
      auditReason: row.AUDIT_REASON,
      auditorId: row.AUDITOR_ID,
      auditedAt: row.AUDITED_AT,
      createdAt: row.CREATED_AT,
      auditor: {
        id: row.AUDITOR_ID,
        username: row.AUDITOR_NAME,
        realName: row.AUDITOR_REAL_NAME
      }
    })) || []
  } catch (error) {
    console.error('获取审计结果失败:', error)
    return []
  }
}

/**
 * 获取相关案例
 */
async function getRelatedCasesByResultId(resultId: number): Promise<any[]> {
  try {
    const sql = `
      SELECT DISTINCT
        mc.ID,
        mc.CASE_NUMBER,
        mc.PATIENT_NAME,
        mc.HOSPITAL_NAME,
        mc.TOTAL_COST,
        mc.MEDICAL_CATEGORY,
        mc.ADMISSION_DATE,
        mc.DISCHARGE_DATE
      FROM MEDICAL_CASE mc
      INNER JOIN RULE_EXECUTION_RESULT rer ON mc.ID = rer.CASE_ID
      WHERE rer.ID = :resultId
         OR rer.RULE_ID = (SELECT RULE_ID FROM RULE_EXECUTION_RESULT WHERE ID = :resultId)
      ORDER BY mc.ADMISSION_DATE DESC
    `

    const result = await executeQuery(sql, { resultId })

    return result.rows?.map((row: any) => ({
      id: row.ID,
      caseNumber: row.CASE_NUMBER,
      patientName: row.PATIENT_NAME,
      hospitalName: row.HOSPITAL_NAME,
      totalCost: row.TOTAL_COST,
      medicalCategory: row.MEDICAL_CATEGORY,
      admissionDate: row.ADMISSION_DATE,
      dischargeDate: row.DISCHARGE_DATE
    })) || []
  } catch (error) {
    console.error('获取相关案例失败:', error)
    return []
  }
}
