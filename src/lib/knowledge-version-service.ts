import { executeQuery } from '@/lib/database'
import { executeQueryWithCache, CACHE_CONFIG } from '@/lib/database-optimizer'

/**
 * 知识库版本管理服务
 * 实现完整的知识库文档版本控制和访问日志功能
 */

export interface KnowledgeVersionHistory {
  id: number
  documentId: number
  versionNumber: number
  content: string
  changeDescription: string
  createdBy: number
  createdAt: string
  // 关联数据
  document?: {
    id: number
    title: string
    category: string
    status: string
  }
  creator?: {
    id: number
    username: string
    realName: string
  }
}

export interface KnowledgeAccessLog {
  id: number
  documentId: number
  userId: number
  accessType: 'VIEW' | 'DOWNLOAD' | 'EDIT' | 'DELETE'
  ipAddress: string
  accessTime: string
  createdAt: string
  // 关联数据
  document?: {
    id: number
    title: string
    category: string
  }
  user?: {
    id: number
    username: string
    realName: string
  }
}

export interface KnowledgeVersionStatistics {
  totalVersions: number
  totalDocuments: number
  totalAccesses: number
  recentVersions: KnowledgeVersionHistory[]
  popularDocuments: any[]
  accessTrends: any[]
}

/**
 * 获取知识库文档版本历史
 */
export async function getKnowledgeVersionHistory(params: {
  documentId?: number
  page?: number
  pageSize?: number
  startDate?: string
  endDate?: string
  createdBy?: number
}): Promise<{
  items: KnowledgeVersionHistory[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}> {
  try {
    const {
      documentId,
      page = 1,
      pageSize = 20,
      startDate,
      endDate,
      createdBy
    } = params

    const conditions: string[] = ['1=1']
    const queryParams: any = {}

    if (documentId) {
      conditions.push('kvh.DOCUMENT_ID = :documentId')
      queryParams.documentId = documentId
    }

    if (startDate) {
      conditions.push('kvh.CREATED_AT >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = startDate
    }

    if (endDate) {
      conditions.push('kvh.CREATED_AT <= TO_DATE(:endDate, \'YYYY-MM-DD\') + 1')
      queryParams.endDate = endDate
    }

    if (createdBy) {
      conditions.push('kvh.CREATED_BY = :createdBy')
      queryParams.createdBy = createdBy
    }

    const sql = `
      SELECT 
        kvh.ID,
        kvh.DOCUMENT_ID,
        kvh.VERSION_NUMBER,
        kvh.CONTENT,
        kvh.CHANGE_DESCRIPTION,
        kvh.CREATED_BY,
        kvh.CREATED_AT,
        -- 关联文档信息
        kb.TITLE as DOCUMENT_TITLE,
        kb.CATEGORY as DOCUMENT_CATEGORY,
        kb.STATUS as DOCUMENT_STATUS,
        -- 关联用户信息
        u.USERNAME,
        u.REAL_NAME
      FROM KNOWLEDGE_VERSION_HISTORY kvh
      LEFT JOIN KNOWLEDGE_BASE kb ON kvh.DOCUMENT_ID = kb.ID
      LEFT JOIN USER_MANAGEMENT_USER u ON kvh.CREATED_BY = u.ID
      WHERE ${conditions.join(' AND ')}
      ORDER BY kvh.CREATED_AT DESC, kvh.VERSION_NUMBER DESC
    `

    const result = await executeQuery(sql, queryParams)

    const items = result.rows?.map((row: any) => ({
      id: row.ID,
      documentId: row.DOCUMENT_ID,
      versionNumber: row.VERSION_NUMBER,
      content: row.CONTENT,
      changeDescription: row.CHANGE_DESCRIPTION,
      createdBy: row.CREATED_BY,
      createdAt: row.CREATED_AT,
      document: {
        id: row.DOCUMENT_ID,
        title: row.DOCUMENT_TITLE,
        category: row.DOCUMENT_CATEGORY,
        status: row.DOCUMENT_STATUS
      },
      creator: {
        id: row.CREATED_BY,
        username: row.USERNAME,
        realName: row.REAL_NAME
      }
    })) || []

    return {
      items,
      total: items.length,
      page,
      pageSize,
      totalPages: Math.ceil(items.length / pageSize)
    }
  } catch (error) {
    console.error('获取知识库版本历史失败:', error)
    throw new Error('获取知识库版本历史失败')
  }
}

/**
 * 创建新的版本记录
 */
export async function createKnowledgeVersion(data: {
  documentId: number
  content: string
  changeDescription: string
  createdBy: number
}): Promise<number> {
  try {
    // 获取下一个版本号
    const versionSql = `
      SELECT COALESCE(MAX(VERSION_NUMBER), 0) + 1 as NEXT_VERSION
      FROM KNOWLEDGE_VERSION_HISTORY
      WHERE DOCUMENT_ID = :documentId
    `

    const versionResult = await executeQuery(versionSql, { documentId: data.documentId })
    const nextVersion = versionResult.rows?.[0]?.NEXT_VERSION || 1

    // 插入版本记录
    const insertSql = `
      INSERT INTO KNOWLEDGE_VERSION_HISTORY (
        ID, DOCUMENT_ID, VERSION_NUMBER, CONTENT, CHANGE_DESCRIPTION,
        CREATED_BY, CREATED_AT
      ) VALUES (
        SEQ_KNOWLEDGE_VERSION_HISTORY.NEXTVAL,
        :documentId, :versionNumber, :content, :changeDescription,
        :userId, CURRENT_TIMESTAMP
      )
      RETURNING ID INTO :id
    `

    const insertParams = {
      documentId: data.documentId,
      versionNumber: nextVersion,
      content: data.content,
      changeDescription: data.changeDescription,
      userId: data.createdBy,
      id: { dir: 3003, type: 2001 } // Oracle OUT parameter
    }

    const result = await executeQuery(insertSql, insertParams)
    return (result as any).outBinds?.id || 0
  } catch (error) {
    console.error('创建知识库版本失败:', error)
    throw new Error('创建知识库版本失败')
  }
}

/**
 * 记录知识库访问日志
 */
export async function logKnowledgeAccess(data: {
  documentId: number
  userId: number
  accessType: 'VIEW' | 'DOWNLOAD' | 'EDIT' | 'DELETE'
  ipAddress: string
}): Promise<void> {
  try {
    const sql = `
      INSERT INTO KNOWLEDGE_ACCESS_LOG (
        ID, DOCUMENT_ID, USER_ID, ACCESS_TYPE, IP_ADDRESS,
        ACCESS_TIME, CREATED_AT
      ) VALUES (
        SEQ_KNOWLEDGE_ACCESS_LOG.NEXTVAL,
        :documentId, :userId, :accessType, :ipAddress,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      )
    `

    await executeQuery(sql, data)
  } catch (error) {
    console.error('记录知识库访问日志失败:', error)
    // 访问日志失败不应该影响主要功能，只记录错误
  }
}

/**
 * 获取知识库访问日志
 */
export async function getKnowledgeAccessLogs(params: {
  documentId?: number
  userId?: number
  accessType?: string
  page?: number
  pageSize?: number
  startDate?: string
  endDate?: string
}): Promise<{
  items: KnowledgeAccessLog[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}> {
  try {
    const {
      documentId,
      userId,
      accessType,
      page = 1,
      pageSize = 20,
      startDate,
      endDate
    } = params

    const conditions: string[] = ['1=1']
    const queryParams: any = {}

    if (documentId) {
      conditions.push('kal.DOCUMENT_ID = :documentId')
      queryParams.documentId = documentId
    }

    if (userId) {
      conditions.push('kal.USER_ID = :userId')
      queryParams.userId = userId
    }

    if (accessType) {
      conditions.push('kal.ACCESS_TYPE = :accessType')
      queryParams.accessType = accessType
    }

    if (startDate) {
      conditions.push('kal.ACCESS_TIME >= TO_DATE(:startDate, \'YYYY-MM-DD\')')
      queryParams.startDate = startDate
    }

    if (endDate) {
      conditions.push('kal.ACCESS_TIME <= TO_DATE(:endDate, \'YYYY-MM-DD\') + 1')
      queryParams.endDate = endDate
    }

    const sql = `
      SELECT 
        kal.ID,
        kal.DOCUMENT_ID,
        kal.USER_ID,
        kal.ACCESS_TYPE,
        kal.IP_ADDRESS,
        kal.ACCESS_TIME,
        kal.CREATED_AT,
        -- 关联文档信息
        kb.TITLE as DOCUMENT_TITLE,
        kb.CATEGORY as DOCUMENT_CATEGORY,
        -- 关联用户信息
        u.USERNAME,
        u.REAL_NAME
      FROM KNOWLEDGE_ACCESS_LOG kal
      LEFT JOIN KNOWLEDGE_BASE kb ON kal.DOCUMENT_ID = kb.ID
      LEFT JOIN USER_MANAGEMENT_USER u ON kal.USER_ID = u.ID
      WHERE ${conditions.join(' AND ')}
      ORDER BY kal.ACCESS_TIME DESC
    `

    const result = await executeQuery(sql, queryParams)

    const items = result.rows?.map((row: any) => ({
      id: row.ID,
      documentId: row.DOCUMENT_ID,
      userId: row.USER_ID,
      accessType: row.ACCESS_TYPE,
      ipAddress: row.IP_ADDRESS,
      accessTime: row.ACCESS_TIME,
      createdAt: row.CREATED_AT,
      document: {
        id: row.DOCUMENT_ID,
        title: row.DOCUMENT_TITLE,
        category: row.DOCUMENT_CATEGORY
      },
      user: {
        id: row.USER_ID,
        username: row.USERNAME,
        realName: row.REAL_NAME
      }
    })) || []

    return {
      items,
      total: items.length,
      page,
      pageSize,
      totalPages: Math.ceil(items.length / pageSize)
    }
  } catch (error) {
    console.error('获取知识库访问日志失败:', error)
    throw new Error('获取知识库访问日志失败')
  }
}
