import { toast as sonnerToast } from 'sonner'

/**
 * 统一的toast工具函数 - 使用Sonner
 */

// 成功提示
export const toast = {
  success: (message: string, description?: string) => {
    sonnerToast.success(message, {
      description,
      duration: 4000,
    })
  },

  error: (message: string, description?: string) => {
    sonnerToast.error(message, {
      description,
      duration: 5000,
    })
  },

  warning: (message: string, description?: string) => {
    sonnerToast.warning(message, {
      description,
      duration: 4000,
    })
  },

  info: (message: string, description?: string) => {
    sonnerToast.info(message, {
      description,
      duration: 4000,
    })
  },

  // 默认提示
  message: (message: string, description?: string) => {
    sonnerToast(message, {
      description,
      duration: 4000,
    })
  },

  // 加载提示
  loading: (message: string, description?: string) => {
    return sonnerToast.loading(message, {
      description,
    })
  },

  // 自定义提示
  custom: (message: string, options?: {
    description?: string
    duration?: number
    action?: {
      label: string
      onClick: () => void
    }
    cancel?: {
      label: string
      onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void
    }
    onDismiss?: () => void
    onAutoClose?: () => void
  }) => {
    return sonnerToast(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      action: options?.action,
      cancel: options?.cancel,
      onDismiss: options?.onDismiss,
      onAutoClose: options?.onAutoClose,
    })
  },

  // Promise提示
  promise: <T>(
    promise: Promise<T>,
    options: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
      description?: string
      duration?: number
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading: options.loading,
      success: options.success,
      error: options.error,
      description: options.description,
      duration: options.duration,
    })
  },

  // 关闭所有提示
  dismiss: (toastId?: string | number) => {
    if (toastId) {
      sonnerToast.dismiss(toastId)
    } else {
      sonnerToast.dismiss()
    }
  },
}

/**
 * 兼容旧版useToast的hook
 */
export function useToast() {
  return {
    toast: (options: {
      title?: string
      description?: string
      variant?: 'default' | 'destructive'
      duration?: number
      action?: {
        label: string
        onClick: () => void
      }
    }) => {
      const { title = '', description, variant = 'default', duration, action } = options

      if (variant === 'destructive') {
        return toast.error(title, description)
      } else {
        return toast.message(title, description)
      }
    },
    dismiss: toast.dismiss,
  }
}

/**
 * 常用的业务提示函数
 */
export const businessToast = {
  // 登录相关
  loginSuccess: () => toast.success('登录成功', '欢迎回到医保基金监管平台'),
  loginError: (message?: string) => toast.error('登录失败', message || '用户名或密码错误'),
  loginRequired: () => toast.warning('请先登录', '您需要登录后才能访问此功能'),
  
  // 权限相关
  permissionDenied: () => toast.error('权限不足', '您没有权限执行此操作'),
  
  // 数据操作
  saveSuccess: (type: string = '数据') => toast.success('保存成功', `${type}已成功保存`),
  saveError: (type: string = '数据', message?: string) => toast.error('保存失败', message || `${type}保存失败，请稍后重试`),
  
  deleteSuccess: (type: string = '数据') => toast.success('删除成功', `${type}已成功删除`),
  deleteError: (type: string = '数据', message?: string) => toast.error('删除失败', message || `${type}删除失败，请稍后重试`),
  
  updateSuccess: (type: string = '数据') => toast.success('更新成功', `${type}已成功更新`),
  updateError: (type: string = '数据', message?: string) => toast.error('更新失败', message || `${type}更新失败，请稍后重试`),
  
  // 网络相关
  networkError: () => toast.error('网络错误', '请检查网络连接后重试'),
  serverError: () => toast.error('服务器错误', '服务器暂时无法响应，请稍后重试'),
  
  // 文件操作
  uploadSuccess: () => toast.success('上传成功', '文件已成功上传'),
  uploadError: (message?: string) => toast.error('上传失败', message || '文件上传失败，请检查文件格式和大小'),
  
  downloadSuccess: () => toast.success('下载成功', '文件已开始下载'),
  downloadError: () => toast.error('下载失败', '文件下载失败，请稍后重试'),
  
  // 导出相关
  exportSuccess: (type: string = '数据') => toast.success('导出成功', `${type}已成功导出`),
  exportError: (type: string = '数据') => toast.error('导出失败', `${type}导出失败，请稍后重试`),
  
  // 执行操作
  executeSuccess: (action: string = '操作') => toast.success('执行成功', `${action}已成功执行`),
  executeError: (action: string = '操作', message?: string) => toast.error('执行失败', message || `${action}执行失败，请稍后重试`),
  
  // 验证相关
  validationError: (message: string) => toast.warning('输入错误', message),
  
  // 复制相关
  copySuccess: () => toast.success('复制成功', '内容已复制到剪贴板'),
  copyError: () => toast.error('复制失败', '无法复制到剪贴板'),
}

/**
 * 异步操作提示
 */
export const asyncToast = {
  // 通用异步操作
  async: <T>(
    promise: Promise<T>,
    messages: {
      loading: string
      success: string
      error: string
    }
  ) => {
    return toast.promise(promise, {
      loading: messages.loading,
      success: messages.success,
      error: messages.error,
    })
  },

  // 保存操作
  save: <T>(promise: Promise<T>, type: string = '数据') => {
    return toast.promise(promise, {
      loading: `正在保存${type}...`,
      success: `${type}保存成功`,
      error: `${type}保存失败`,
    })
  },

  // 删除操作
  delete: <T>(promise: Promise<T>, type: string = '数据') => {
    return toast.promise(promise, {
      loading: `正在删除${type}...`,
      success: `${type}删除成功`,
      error: `${type}删除失败`,
    })
  },

  // 加载操作
  load: <T>(promise: Promise<T>, type: string = '数据') => {
    return toast.promise(promise, {
      loading: `正在加载${type}...`,
      success: `${type}加载完成`,
      error: `${type}加载失败`,
    })
  },

  // 执行操作
  execute: <T>(promise: Promise<T>, action: string = '操作') => {
    return toast.promise(promise, {
      loading: `正在执行${action}...`,
      success: `${action}执行成功`,
      error: `${action}执行失败`,
    })
  },
}

// 导出默认toast函数
export default toast
