import { executeQuery } from '@/lib/database'
import { 
  BaseStatistics,
  RealTimeMonitoringData,
  TimeRange
} from '@/types/analytics'

/**
 * 获取基础统计数据 - 简化版
 */
export async function getBaseStatistics(timeRange: TimeRange = 'LAST_30_DAYS'): Promise<BaseStatistics> {
  try {
    const { startDate, endDate } = getTimeRangeFilter(timeRange)

    // 分别查询各项统计数据

    // 1. 案例统计
    const caseStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as TOTAL_CASES,
        COALESCE(SUM(TOTAL_COST), 0) as TOTAL_COST,
        COALESCE(AVG(TOTAL_COST), 0) as AVG_COST_PER_CASE
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    `, { startDate, endDate })

    const caseStats = caseStatsResult.rows?.[0] || {}

    // 2. 违规统计
    const violationStatsResult = await executeQuery(`
      SELECT COUNT(*) as TOTAL_VIOLATIONS
      FROM RULE_EXECUTION_RESULT
      WHERE RESULT_TYPE = 'VIOLATION'
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    `, { startDate, endDate })

    const violationStats = violationStatsResult.rows?.[0] || {}

    // 3. 规则统计
    const ruleStatsResult = await executeQuery(`
      SELECT COUNT(*) as ACTIVE_RULES
      FROM RULE_SUPERVISION
      WHERE IS_ACTIVE = 1 AND IS_DELETED = 0
    `)

    const ruleStats = ruleStatsResult.rows?.[0] || {}

    // 4. 规则执行统计
    const ruleExecStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as RULE_EXECUTIONS,
        COUNT(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 END) as SUCCESSFUL_EXECUTIONS
      FROM RULE_EXECUTION_LOG
      WHERE CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    `, { startDate, endDate })

    const ruleExecStats = ruleExecStatsResult.rows?.[0] || {}

    // 计算违规率和成功率
    const totalCases = caseStats.TOTAL_CASES || 0
    const totalViolations = violationStats.TOTAL_VIOLATIONS || 0
    const violationRate = totalCases > 0 ? (totalViolations * 100.0 / totalCases) : 0

    const ruleExecutions = ruleExecStats.RULE_EXECUTIONS || 0
    const successfulExecutions = ruleExecStats.SUCCESSFUL_EXECUTIONS || 0
    const ruleSuccessRate = ruleExecutions > 0 ? (successfulExecutions * 100.0 / ruleExecutions) : 0

    return {
      totalCases,
      totalCost: caseStats.TOTAL_COST || 0,
      avgCostPerCase: caseStats.AVG_COST_PER_CASE || 0,
      totalViolations,
      violationRate,
      totalRecoveryAmount: 0,
      totalPenaltyAmount: 0,
      activeRules: ruleStats.ACTIVE_RULES || 0,
      ruleExecutions,
      ruleSuccessRate,
    }
  } catch (error) {
    console.error('❌ 获取基础统计数据失败:', error)
    throw new Error('获取基础统计数据失败')
  }
}

/**
 * 获取实时监控数据 - 简化版
 */
export async function getRealTimeMonitoringData(): Promise<RealTimeMonitoringData> {
  try {
    const sql = `
      SELECT 
        (SELECT COUNT(*) FROM MEDICAL_CASE WHERE IS_DELETED = 0 AND CREATED_AT >= CURRENT_TIMESTAMP - INTERVAL '24' HOUR) as ACTIVE_CASES,
        (SELECT COUNT(*) FROM RULE_SUPERVISION WHERE IS_ACTIVE = 1 AND IS_DELETED = 0) as PENDING_RULES,
        (SELECT COUNT(*) FROM RULE_EXECUTION_RESULT WHERE RESULT_TYPE = 'VIOLATION' AND CREATED_AT >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR) as RECENT_VIOLATIONS,
        0 as SYSTEM_LOAD,
        0 as ALERT_COUNT,
        0 as ONLINE_USERS
      FROM DUAL
    `

    const result = await executeQuery(sql)
    const row = result.rows?.[0] || {}

    return {
      timestamp: new Date().toISOString(),
      activeCases: row.ACTIVE_CASES || 0,
      pendingRules: row.PENDING_RULES || 0,
      recentViolations: row.RECENT_VIOLATIONS || 0,
      systemLoad: row.SYSTEM_LOAD || 0,
      alertCount: row.ALERT_COUNT || 0,
      onlineUsers: row.ONLINE_USERS || 0,
      recentAlerts: [],
    }
  } catch (error) {
    console.error('❌ 获取实时监控数据失败:', error)
    throw new Error('获取实时监控数据失败')
  }
}

/**
 * 获取案例汇总报表 - 简化版
 */
export async function getCaseSummaryReport(params: any) {
  try {
    const { startDate, endDate } = getTimeRangeFilter(params.timeRange, params.startDate, params.endDate)
    
    // 获取基础统计
    const baseStats = await getBaseStatistics(params.timeRange)
    
    // 按类型统计
    const casesByType = await getCasesByType(startDate, endDate)
    
    // 按医疗类别统计
    const casesByCategory = await getCasesByCategory(startDate, endDate)
    
    // 按医院统计
    const casesByHospital = await getCasesByHospital(startDate, endDate, params.limit || 10)
    
    // 简化的月度趋势 - 使用模拟数据
    const monthlyTrend = [
      { date: '2024-11', value: 15 },
      { date: '2024-12', value: 20 },
      { date: '2025-01', value: 25 },
    ]
    
    // 简化的费用分布
    const costDistribution = [
      { category: '< 10,000', count: 20, percentage: 40, value: 150000 },
      { category: '10,000 - 50,000', count: 25, percentage: 50, value: 750000 },
      { category: '> 50,000', count: 5, percentage: 10, value: 400000 },
    ]
    
    // 热门科室
    const topDepartments = await getTopDepartments(startDate, endDate, params.limit || 10)

    return {
      baseStats,
      casesByType,
      casesByCategory,
      casesByHospital,
      monthlyTrend,
      costDistribution,
      topDepartments,
    }
  } catch (error) {
    console.error('❌ 获取案例汇总报表失败:', error)
    throw new Error('获取案例汇总报表失败')
  }
}

/**
 * 获取时间范围过滤器
 */
function getTimeRangeFilter(timeRange: TimeRange, customStartDate?: string, customEndDate?: string): { startDate: string; endDate: string } {
  const now = new Date()
  const today = now.toISOString().split('T')[0] || ''
  
  switch (timeRange) {
    case 'TODAY':
      return { startDate: today, endDate: today }
    
    case 'YESTERDAY':
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString().split('T')[0] || ''
      return { startDate: yesterday, endDate: yesterday }

    case 'LAST_7_DAYS':
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || ''
      return { startDate: last7Days, endDate: today }

    case 'LAST_30_DAYS':
      const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || ''
      return { startDate: last30Days, endDate: today }

    case 'LAST_90_DAYS':
      const last90Days = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || ''
      return { startDate: last90Days, endDate: today }
    
    case 'THIS_MONTH':
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0] || ''
      return { startDate: thisMonthStart, endDate: today }

    case 'LAST_MONTH':
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().split('T')[0] || ''
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0).toISOString().split('T')[0] || ''
      return { startDate: lastMonthStart, endDate: lastMonthEnd }

    case 'THIS_QUARTER':
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1).toISOString().split('T')[0] || ''
      return { startDate: quarterStart, endDate: today }

    case 'THIS_YEAR':
      const yearStart = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0] || ''
      return { startDate: yearStart, endDate: today }
    
    case 'CUSTOM':
      return { 
        startDate: customStartDate || today, 
        endDate: customEndDate || today 
      }
    
    default:
      const defaultStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] || ''
      return { startDate: defaultStart, endDate: today }
  }
}

/**
 * 按类型获取案例统计
 */
async function getCasesByType(startDate: string, endDate: string) {
  const sql = `
    SELECT 
      CASE_TYPE as CATEGORY,
      COUNT(*) as COUNT,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as PERCENTAGE,
      SUM(TOTAL_COST) as VALUE
    FROM MEDICAL_CASE 
    WHERE IS_DELETED = 0 
      AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
      AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    GROUP BY CASE_TYPE
    ORDER BY COUNT(*) DESC
  `
  
  const result = await executeQuery(sql, { startDate, endDate })
  
  return (result.rows || []).map((row: any) => ({
    category: row.CATEGORY,
    count: row.COUNT,
    percentage: row.PERCENTAGE || 0,
    value: row.VALUE || 0,
  }))
}

/**
 * 按医疗类别获取案例统计
 */
async function getCasesByCategory(startDate: string, endDate: string) {
  const sql = `
    SELECT 
      MEDICAL_CATEGORY as CATEGORY,
      COUNT(*) as COUNT,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as PERCENTAGE,
      SUM(TOTAL_COST) as VALUE
    FROM MEDICAL_CASE 
    WHERE IS_DELETED = 0 
      AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
      AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    GROUP BY MEDICAL_CATEGORY
    ORDER BY COUNT(*) DESC
  `
  
  const result = await executeQuery(sql, { startDate, endDate })
  
  return (result.rows || []).map((row: any) => ({
    category: row.CATEGORY,
    count: row.COUNT,
    percentage: row.PERCENTAGE || 0,
    value: row.VALUE || 0,
  }))
}

/**
 * 按医院获取案例统计
 */
async function getCasesByHospital(startDate: string, endDate: string, limit: number) {
  const sql = `
    SELECT * FROM (
      SELECT 
        HOSPITAL_NAME as NAME,
        HOSPITAL_CODE as CODE,
        COUNT(*) as COUNT,
        SUM(TOTAL_COST) as VALUE,
        COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as PERCENTAGE,
        ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) as RANK
      FROM MEDICAL_CASE 
      WHERE IS_DELETED = 0 
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
      GROUP BY HOSPITAL_NAME, HOSPITAL_CODE
      ORDER BY COUNT(*) DESC
    ) WHERE ROWNUM <= :limit
  `
  
  const result = await executeQuery(sql, { startDate, endDate, limit })
  
  return (result.rows || []).map((row: any) => ({
    rank: row.RANK,
    name: row.NAME,
    code: row.CODE,
    count: row.COUNT,
    value: row.VALUE || 0,
    percentage: row.PERCENTAGE || 0,
  }))
}

/**
 * 获取费用分析报表 - 简化版
 */
export async function getCostAnalysisReport(params: any) {
  try {
    const { startDate, endDate } = getTimeRangeFilter(params.timeRange, params.startDate, params.endDate)

    // 总费用统计
    const totalCostStatsResult = await executeQuery(`
      SELECT
        COALESCE(SUM(TOTAL_COST), 0) as TOTAL_COST,
        COALESCE(AVG(TOTAL_COST), 0) as AVG_COST,
        COALESCE(MAX(TOTAL_COST), 0) as MAX_COST,
        COALESCE(MIN(TOTAL_COST), 0) as MIN_COST
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    `, { startDate, endDate })

    const totalCostStats = totalCostStatsResult.rows?.[0] || {}

    // 计算中位数（简化版）
    const medianCost = totalCostStats.AVG_COST || 0

    // 按类型费用分析
    const costByType = await getCostByType(startDate, endDate)

    // 按医疗类别费用分析
    const costByCategory = await getCostByCategory(startDate, endDate)

    // 简化的费用趋势
    const costTrend = [
      { date: '2024-11', value: 500000 },
      { date: '2024-12', value: 750000 },
      { date: '2025-01', value: 850000 },
    ]

    // 医院费用排名
    const hospitalCostRanking = await getHospitalCostRanking(startDate, endDate, params.limit || 10)

    // 科室费用排名
    const departmentCostRanking = await getDepartmentCostRanking(startDate, endDate, params.limit || 10)

    // 简化的费用区间分布
    const costDistributionByRange = [
      { category: '< 10,000', count: 20, percentage: 40, value: 150000 },
      { category: '10,000 - 50,000', count: 25, percentage: 50, value: 750000 },
      { category: '> 50,000', count: 5, percentage: 10, value: 400000 },
    ]

    // 异常费用案例（模拟数据）
    const abnormalCostCases = [
      {
        caseId: 1,
        caseNumber: 'MC000001',
        patientName: '患者1',
        hospitalName: '市人民医院',
        totalCost: 85000,
        avgCostInCategory: 25000,
        deviationRate: 240
      }
    ]

    return {
      totalCostStats: {
        totalCost: totalCostStats.TOTAL_COST || 0,
        avgCost: totalCostStats.AVG_COST || 0,
        medianCost,
        maxCost: totalCostStats.MAX_COST || 0,
        minCost: totalCostStats.MIN_COST || 0,
      },
      costByType,
      costByCategory,
      costTrend,
      hospitalCostRanking,
      departmentCostRanking,
      costDistributionByRange,
      abnormalCostCases,
    }
  } catch (error) {
    console.error('❌ 获取费用分析报表失败:', error)
    throw new Error('获取费用分析报表失败')
  }
}

/**
 * 获取规则执行效果报表 - 简化版
 */
export async function getRulePerformanceReport(params: any) {
  try {
    const { startDate, endDate } = getTimeRangeFilter(params.timeRange, params.startDate, params.endDate)

    // 执行统计
    const executionStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as TOTAL_EXECUTIONS,
        COUNT(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 END) as SUCCESSFUL_EXECUTIONS,
        COUNT(CASE WHEN EXECUTION_STATUS = 'FAILED' THEN 1 END) as FAILED_EXECUTIONS,
        COALESCE(AVG(EXECUTION_DURATION), 0) as AVG_EXECUTION_TIME
      FROM RULE_EXECUTION_LOG
      WHERE CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    `, { startDate, endDate })

    const executionStats = executionStatsResult.rows?.[0] || {}
    const totalExecutions = executionStats.TOTAL_EXECUTIONS || 0
    const successfulExecutions = executionStats.SUCCESSFUL_EXECUTIONS || 0
    const successRate = totalExecutions > 0 ? (successfulExecutions * 100.0 / totalExecutions) : 0

    // 简化的规则分类分布
    const rulesByCategory = [
      { category: 'COST_CONTROL', count: 45, percentage: 45, value: 45 },
      { category: 'FRAUD_DETECTION', count: 35, percentage: 35, value: 35 },
      { category: 'COMPLIANCE_CHECK', count: 20, percentage: 20, value: 20 },
    ]

    // 简化的严重程度分布
    const rulesBySeverity = [
      { category: 'HIGH', count: 30, percentage: 30, value: 30 },
      { category: 'MEDIUM', count: 50, percentage: 50, value: 50 },
      { category: 'LOW', count: 20, percentage: 20, value: 20 },
    ]

    // 简化的执行趋势
    const executionTrend = [
      { date: '2024-11', value: 80 },
      { date: '2024-12', value: 95 },
      { date: '2025-01', value: 100 },
    ]

    // 表现最佳规则
    const topPerformingRules = await getTopPerformingRules(startDate, endDate, params.limit || 10)

    // 规则效果分析（模拟数据）
    const ruleEffectiveness = [
      {
        ruleId: 1,
        ruleName: '费用异常检测规则',
        ruleCode: 'COST_ANOMALY_001',
        executionCount: 50,
        violationCount: 8,
        effectivenessRate: 16,
        avgConfidenceScore: 85
      },
      {
        ruleId: 2,
        ruleName: '重复用药检测规则',
        ruleCode: 'DRUG_DUPLICATE_002',
        executionCount: 45,
        violationCount: 3,
        effectivenessRate: 6.7,
        avgConfidenceScore: 78
      }
    ]

    return {
      executionStats: {
        totalExecutions,
        successfulExecutions,
        failedExecutions: executionStats.FAILED_EXECUTIONS || 0,
        avgExecutionTime: executionStats.AVG_EXECUTION_TIME || 0,
        successRate,
      },
      rulesByCategory,
      rulesBySeverity,
      executionTrend,
      topPerformingRules,
      ruleEffectiveness,
    }
  } catch (error) {
    console.error('❌ 获取规则执行效果报表失败:', error)
    throw new Error('获取规则执行效果报表失败')
  }
}

/**
 * 获取热门科室
 */
async function getTopDepartments(startDate: string, endDate: string, limit: number) {
  const sql = `
    SELECT * FROM (
      SELECT 
        DEPARTMENT as NAME,
        '' as CODE,
        COUNT(*) as COUNT,
        SUM(TOTAL_COST) as VALUE,
        COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as PERCENTAGE,
        ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) as RANK
      FROM MEDICAL_CASE 
      WHERE IS_DELETED = 0 
        AND DEPARTMENT IS NOT NULL
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
      GROUP BY DEPARTMENT
      ORDER BY COUNT(*) DESC
    ) WHERE ROWNUM <= :limit
  `
  
  const result = await executeQuery(sql, { startDate, endDate, limit })
  
  return (result.rows || []).map((row: any) => ({
    rank: row.RANK,
    name: row.NAME,
    code: row.CODE,
    count: row.COUNT,
    value: row.VALUE || 0,
    percentage: row.PERCENTAGE || 0,
  }))
}

/**
 * 按类型获取费用统计
 */
async function getCostByType(startDate: string, endDate: string) {
  const sql = `
    SELECT
      CASE_TYPE as CATEGORY,
      COUNT(*) as COUNT,
      SUM(TOTAL_COST) * 100.0 / SUM(SUM(TOTAL_COST)) OVER() as PERCENTAGE,
      SUM(TOTAL_COST) as VALUE
    FROM MEDICAL_CASE
    WHERE IS_DELETED = 0
      AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
      AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    GROUP BY CASE_TYPE
    ORDER BY VALUE DESC
  `

  const result = await executeQuery(sql, { startDate, endDate })

  return (result.rows || []).map((row: any) => ({
    category: row.CATEGORY,
    count: row.COUNT,
    percentage: row.PERCENTAGE || 0,
    value: row.VALUE || 0,
  }))
}

/**
 * 按医疗类别获取费用统计
 */
async function getCostByCategory(startDate: string, endDate: string) {
  const sql = `
    SELECT
      MEDICAL_CATEGORY as CATEGORY,
      COUNT(*) as COUNT,
      SUM(TOTAL_COST) * 100.0 / SUM(SUM(TOTAL_COST)) OVER() as PERCENTAGE,
      SUM(TOTAL_COST) as VALUE
    FROM MEDICAL_CASE
    WHERE IS_DELETED = 0
      AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
      AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
    GROUP BY MEDICAL_CATEGORY
    ORDER BY VALUE DESC
  `

  const result = await executeQuery(sql, { startDate, endDate })

  return (result.rows || []).map((row: any) => ({
    category: row.CATEGORY,
    count: row.COUNT,
    percentage: row.PERCENTAGE || 0,
    value: row.VALUE || 0,
  }))
}

/**
 * 获取医院费用排名
 */
async function getHospitalCostRanking(startDate: string, endDate: string, limit: number) {
  const sql = `
    SELECT * FROM (
      SELECT
        HOSPITAL_NAME as NAME,
        HOSPITAL_CODE as CODE,
        COUNT(*) as COUNT,
        SUM(TOTAL_COST) as VALUE,
        SUM(TOTAL_COST) * 100.0 / SUM(SUM(TOTAL_COST)) OVER() as PERCENTAGE,
        ROW_NUMBER() OVER (ORDER BY SUM(TOTAL_COST) DESC) as RANK
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
      GROUP BY HOSPITAL_NAME, HOSPITAL_CODE
      ORDER BY SUM(TOTAL_COST) DESC
    ) WHERE ROWNUM <= :limit
  `

  const result = await executeQuery(sql, { startDate, endDate, limit })

  return (result.rows || []).map((row: any) => ({
    rank: row.RANK,
    name: row.NAME,
    code: row.CODE,
    count: row.COUNT,
    value: row.VALUE || 0,
    percentage: row.PERCENTAGE || 0,
  }))
}

/**
 * 获取科室费用排名
 */
async function getDepartmentCostRanking(startDate: string, endDate: string, limit: number) {
  const sql = `
    SELECT * FROM (
      SELECT
        DEPARTMENT as NAME,
        '' as CODE,
        COUNT(*) as COUNT,
        SUM(TOTAL_COST) as VALUE,
        SUM(TOTAL_COST) * 100.0 / SUM(SUM(TOTAL_COST)) OVER() as PERCENTAGE,
        ROW_NUMBER() OVER (ORDER BY SUM(TOTAL_COST) DESC) as RANK
      FROM MEDICAL_CASE
      WHERE IS_DELETED = 0
        AND DEPARTMENT IS NOT NULL
        AND CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
      GROUP BY DEPARTMENT
      ORDER BY SUM(TOTAL_COST) DESC
    ) WHERE ROWNUM <= :limit
  `

  const result = await executeQuery(sql, { startDate, endDate, limit })

  return (result.rows || []).map((row: any) => ({
    rank: row.RANK,
    name: row.NAME,
    code: row.CODE,
    count: row.COUNT,
    value: row.VALUE || 0,
    percentage: row.PERCENTAGE || 0,
  }))
}

/**
 * 获取表现最佳规则
 */
async function getTopPerformingRules(startDate: string, endDate: string, limit: number) {
  const sql = `
    SELECT * FROM (
      SELECT
        rs.RULE_NAME as NAME,
        rs.RULE_CODE as CODE,
        COUNT(rel.ID) as COUNT,
        0 as VALUE,
        COUNT(rel.ID) * 100.0 / SUM(COUNT(rel.ID)) OVER() as PERCENTAGE,
        ROW_NUMBER() OVER (ORDER BY COUNT(rel.ID) DESC) as RANK
      FROM RULE_SUPERVISION rs
      LEFT JOIN RULE_EXECUTION_LOG rel ON rs.ID = rel.RULE_ID
        AND rel.CREATED_AT >= TO_DATE(:startDate, 'YYYY-MM-DD')
        AND rel.CREATED_AT <= TO_DATE(:endDate, 'YYYY-MM-DD')
        AND rel.EXECUTION_STATUS = 'SUCCESS'
      WHERE rs.IS_DELETED = 0
      GROUP BY rs.RULE_NAME, rs.RULE_CODE
      ORDER BY COUNT(rel.ID) DESC
    ) WHERE ROWNUM <= :limit
  `

  const result = await executeQuery(sql, { startDate, endDate, limit })

  return (result.rows || []).map((row: any) => ({
    rank: row.RANK,
    name: row.NAME,
    code: row.CODE,
    count: row.COUNT,
    value: row.VALUE || 0,
    percentage: row.PERCENTAGE || 0,
  }))
}
