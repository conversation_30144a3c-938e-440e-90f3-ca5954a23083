/**
 * 企业级API响应标准化系统
 */

import { NextResponse } from 'next/server'
import { z } from 'zod'
import { log, monitoring } from './monitoring'

// 标准API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  code: string
  message: string
  data?: T
  errors?: string[]
  meta?: {
    timestamp: string
    requestId?: string
    version: string
    pagination?: PaginationMeta
    performance?: PerformanceMeta
  }
}

export interface PaginationMeta {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PerformanceMeta {
  duration: number
  cacheHit?: boolean
  dbQueries?: number
}

// 错误代码枚举
export enum ErrorCodes {
  // 通用错误
  SUCCESS = 'SUCCESS',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // 认证和授权错误
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  
  // 资源错误
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  
  // 业务逻辑错误
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  
  // 系统错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  
  // 医保业务特定错误
  INVALID_MEDICAL_CARD = 'INVALID_MEDICAL_CARD',
  INVALID_ID_CARD = 'INVALID_ID_CARD',
  CASE_AMOUNT_EXCEEDED = 'CASE_AMOUNT_EXCEEDED',
  AUDIT_REQUIRED = 'AUDIT_REQUIRED',
  SETTLEMENT_FAILED = 'SETTLEMENT_FAILED'
}

// 错误消息映射
const ERROR_MESSAGES: Record<ErrorCodes, string> = {
  [ErrorCodes.SUCCESS]: '操作成功',
  [ErrorCodes.UNKNOWN_ERROR]: '未知错误',
  [ErrorCodes.VALIDATION_ERROR]: '数据验证失败',
  
  [ErrorCodes.AUTHENTICATION_REQUIRED]: '需要身份认证',
  [ErrorCodes.AUTHENTICATION_FAILED]: '身份认证失败',
  [ErrorCodes.AUTHORIZATION_FAILED]: '权限验证失败',
  [ErrorCodes.TOKEN_EXPIRED]: '令牌已过期',
  [ErrorCodes.TOKEN_INVALID]: '令牌无效',
  
  [ErrorCodes.RESOURCE_NOT_FOUND]: '资源不存在',
  [ErrorCodes.RESOURCE_ALREADY_EXISTS]: '资源已存在',
  [ErrorCodes.RESOURCE_CONFLICT]: '资源冲突',
  
  [ErrorCodes.BUSINESS_RULE_VIOLATION]: '违反业务规则',
  [ErrorCodes.INSUFFICIENT_PERMISSIONS]: '权限不足',
  [ErrorCodes.OPERATION_NOT_ALLOWED]: '操作不被允许',
  
  [ErrorCodes.DATABASE_ERROR]: '数据库错误',
  [ErrorCodes.EXTERNAL_SERVICE_ERROR]: '外部服务错误',
  [ErrorCodes.RATE_LIMIT_EXCEEDED]: '请求频率超限',
  [ErrorCodes.SERVICE_UNAVAILABLE]: '服务不可用',
  
  [ErrorCodes.INVALID_MEDICAL_CARD]: '医保卡号无效',
  [ErrorCodes.INVALID_ID_CARD]: '身份证号无效',
  [ErrorCodes.CASE_AMOUNT_EXCEEDED]: '案例金额超出限制',
  [ErrorCodes.AUDIT_REQUIRED]: '需要审核',
  [ErrorCodes.SETTLEMENT_FAILED]: '结算失败'
}

// HTTP状态码映射
const STATUS_CODE_MAP: Record<ErrorCodes, number> = {
  [ErrorCodes.SUCCESS]: 200,
  [ErrorCodes.UNKNOWN_ERROR]: 500,
  [ErrorCodes.VALIDATION_ERROR]: 400,
  
  [ErrorCodes.AUTHENTICATION_REQUIRED]: 401,
  [ErrorCodes.AUTHENTICATION_FAILED]: 401,
  [ErrorCodes.AUTHORIZATION_FAILED]: 403,
  [ErrorCodes.TOKEN_EXPIRED]: 401,
  [ErrorCodes.TOKEN_INVALID]: 401,
  
  [ErrorCodes.RESOURCE_NOT_FOUND]: 404,
  [ErrorCodes.RESOURCE_ALREADY_EXISTS]: 409,
  [ErrorCodes.RESOURCE_CONFLICT]: 409,
  
  [ErrorCodes.BUSINESS_RULE_VIOLATION]: 422,
  [ErrorCodes.INSUFFICIENT_PERMISSIONS]: 403,
  [ErrorCodes.OPERATION_NOT_ALLOWED]: 405,
  
  [ErrorCodes.DATABASE_ERROR]: 500,
  [ErrorCodes.EXTERNAL_SERVICE_ERROR]: 502,
  [ErrorCodes.RATE_LIMIT_EXCEEDED]: 429,
  [ErrorCodes.SERVICE_UNAVAILABLE]: 503,
  
  [ErrorCodes.INVALID_MEDICAL_CARD]: 400,
  [ErrorCodes.INVALID_ID_CARD]: 400,
  [ErrorCodes.CASE_AMOUNT_EXCEEDED]: 422,
  [ErrorCodes.AUDIT_REQUIRED]: 422,
  [ErrorCodes.SETTLEMENT_FAILED]: 422
}

/**
 * API响应构建器
 */
export class ApiResponseBuilder {
  private startTime: number
  private requestId?: string
  private cacheHit?: boolean
  private dbQueries?: number

  constructor(requestId?: string) {
    this.startTime = Date.now()
    this.requestId = requestId
  }

  /**
   * 设置性能指标
   */
  setPerformanceMetrics(cacheHit?: boolean, dbQueries?: number): this {
    this.cacheHit = cacheHit
    this.dbQueries = dbQueries
    return this
  }

  /**
   * 成功响应
   */
  success<T>(data?: T, message?: string): NextResponse<ApiResponse<T>> {
    const response: ApiResponse<T> = {
      success: true,
      code: ErrorCodes.SUCCESS,
      message: message || ERROR_MESSAGES[ErrorCodes.SUCCESS],
      data,
      meta: this.buildMeta()
    }

    this.logResponse('success', response)
    return NextResponse.json(response, { status: 200 })
  }

  /**
   * 分页成功响应
   */
  successWithPagination<T>(
    data: T[],
    pagination: PaginationMeta,
    message?: string
  ): NextResponse<ApiResponse<T[]>> {
    const response: ApiResponse<T[]> = {
      success: true,
      code: ErrorCodes.SUCCESS,
      message: message || ERROR_MESSAGES[ErrorCodes.SUCCESS],
      data,
      meta: {
        ...this.buildMeta(),
        pagination
      }
    }

    this.logResponse('success_paginated', response)
    return NextResponse.json(response, { status: 200 })
  }

  /**
   * 错误响应
   */
  error(
    code: ErrorCodes,
    customMessage?: string,
    errors?: string[]
  ): NextResponse<ApiResponse> {
    const statusCode = STATUS_CODE_MAP[code] || 500
    const message = customMessage || ERROR_MESSAGES[code] || ERROR_MESSAGES[ErrorCodes.UNKNOWN_ERROR]

    const response: ApiResponse = {
      success: false,
      code,
      message,
      errors,
      meta: this.buildMeta()
    }

    this.logResponse('error', response, statusCode)
    
    // 记录错误指标
    monitoring.recordMetric({
      name: 'api_error',
      value: 1,
      tags: {
        error_code: code,
        status_code: statusCode.toString()
      }
    })

    return NextResponse.json(response, { status: statusCode })
  }

  /**
   * 验证错误响应
   */
  validationError(errors: string[]): NextResponse<ApiResponse> {
    return this.error(ErrorCodes.VALIDATION_ERROR, undefined, errors)
  }

  /**
   * 认证错误响应
   */
  authenticationError(message?: string): NextResponse<ApiResponse> {
    return this.error(ErrorCodes.AUTHENTICATION_FAILED, message)
  }

  /**
   * 授权错误响应
   */
  authorizationError(message?: string): NextResponse<ApiResponse> {
    return this.error(ErrorCodes.AUTHORIZATION_FAILED, message)
  }

  /**
   * 资源未找到错误响应
   */
  notFoundError(resource?: string): NextResponse<ApiResponse> {
    const message = resource ? `${resource}不存在` : undefined
    return this.error(ErrorCodes.RESOURCE_NOT_FOUND, message)
  }

  /**
   * 业务规则违反错误响应
   */
  businessRuleError(message: string): NextResponse<ApiResponse> {
    return this.error(ErrorCodes.BUSINESS_RULE_VIOLATION, message)
  }

  /**
   * 服务器内部错误响应
   */
  internalError(message?: string): NextResponse<ApiResponse> {
    return this.error(ErrorCodes.UNKNOWN_ERROR, message)
  }

  private buildMeta() {
    const duration = Date.now() - this.startTime
    
    const meta: NonNullable<ApiResponse['meta']> = {
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      performance: {
        duration,
        cacheHit: this.cacheHit,
        dbQueries: this.dbQueries
      }
    }

    if (this.requestId) {
      meta.requestId = this.requestId
    }

    return meta
  }

  private logResponse(type: string, response: ApiResponse, statusCode?: number) {
    const level = response.success ? 'info' : 'warn'
    
    log({
      level,
      message: `API response: ${type}`,
      context: {
        requestId: this.requestId,
        success: response.success,
        code: response.code,
        statusCode: statusCode || 200,
        duration: response.meta?.performance?.duration,
        cacheHit: response.meta?.performance?.cacheHit,
        hasData: !!response.data,
        errorCount: response.errors?.length || 0
      }
    })

    // 记录性能指标
    if (response.meta?.performance?.duration) {
      monitoring.recordPerformance({
        operation: 'api_response',
        duration: response.meta.performance.duration,
        success: response.success,
        metadata: {
          code: response.code,
          statusCode: statusCode || 200,
          cacheHit: response.meta.performance.cacheHit
        }
      })
    }
  }
}

/**
 * 分页工具
 */
export class PaginationHelper {
  static calculatePagination(
    page: number,
    pageSize: number,
    total: number
  ): PaginationMeta {
    const totalPages = Math.ceil(total / pageSize)
    
    return {
      page,
      pageSize,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }

  static validatePaginationParams(page?: string, pageSize?: string) {
    const pageNum = parseInt(page || '1')
    const pageSizeNum = parseInt(pageSize || '20')

    if (isNaN(pageNum) || pageNum < 1) {
      throw new Error('页码必须是大于0的整数')
    }

    if (isNaN(pageSizeNum) || pageSizeNum < 1 || pageSizeNum > 100) {
      throw new Error('每页大小必须是1-100之间的整数')
    }

    return { page: pageNum, pageSize: pageSizeNum }
  }

  static getOffset(page: number, pageSize: number): number {
    return (page - 1) * pageSize
  }
}

// 便捷方法
export const createApiResponse = (requestId?: string) => new ApiResponseBuilder(requestId)

// 响应验证模式
export const apiResponseSchema = z.object({
  success: z.boolean(),
  code: z.string(),
  message: z.string(),
  data: z.any().optional(),
  errors: z.array(z.string()).optional(),
  meta: z.object({
    timestamp: z.string(),
    requestId: z.string().optional(),
    version: z.string(),
    pagination: z.object({
      page: z.number(),
      pageSize: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean()
    }).optional(),
    performance: z.object({
      duration: z.number(),
      cacheHit: z.boolean().optional(),
      dbQueries: z.number().optional()
    }).optional()
  }).optional()
})

export { ERROR_MESSAGES, STATUS_CODE_MAP }
export default ApiResponseBuilder
