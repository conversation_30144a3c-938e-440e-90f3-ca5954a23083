/**
 * 规则模板服务
 * 
 * 负责规则模板的CRUD操作、分类管理和使用统计
 */

import { executeQuery } from '@/lib/database'
import {
  RuleTemplate,
  RuleTemplateCategory,
  TemplateListParams,
  TemplatePaginationResponse,
  TemplateCreateRequest,
  TemplateUpdateRequest,
  TemplateStatistics,
  RuleTemplateUsage,
  CreateRuleFromTemplateRequest
} from '@/types/rule-template'

// 导出类型以供其他模块使用
export type {
  RuleTemplate,
  RuleTemplateCategory,
  TemplateListParams,
  TemplatePaginationResponse,
  TemplateCreateRequest,
  TemplateUpdateRequest,
  TemplateStatistics,
  RuleTemplateUsage,
  CreateRuleFromTemplateRequest
}

/**
 * 获取模板分类列表
 */
export async function getTemplateCategories(): Promise<RuleTemplateCategory[]> {
  try {
    const sql = `
      SELECT 
        ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESC,
        PARENT_CATEGORY_ID, CATEGORY_LEVEL, SORT_ORDER,
        ICON_NAME, COLOR_SCHEME, IS_ACTIVE,
        CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY
      FROM RULE_TEMPLATE_CATEGORY
      WHERE IS_DELETED = 0
      ORDER BY CATEGORY_LEVEL, SORT_ORDER, CATEGORY_NAME
    `

    const result = await executeQuery(sql)
    
    return (result.rows || []).map((row: any) => ({
      id: row.ID,
      categoryCode: row.CATEGORY_CODE,
      categoryName: row.CATEGORY_NAME,
      categoryDesc: row.CATEGORY_DESC,
      parentCategoryId: row.PARENT_CATEGORY_ID,
      categoryLevel: row.CATEGORY_LEVEL,
      sortOrder: row.SORT_ORDER,
      iconName: row.ICON_NAME,
      colorScheme: row.COLOR_SCHEME,
      isActive: row.IS_ACTIVE === 1,
      isDeleted: false,
      createdAt: row.CREATED_AT?.toISOString() || '',
      updatedAt: row.UPDATED_AT?.toISOString() || '',
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY
    }))

  } catch (error) {
    console.error('❌ 获取模板分类失败:', error)
    throw new Error(`获取模板分类失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取模板列表
 */
export async function getTemplates(params: TemplateListParams): Promise<TemplatePaginationResponse> {
  try {
    const {
      page = 1,
      pageSize = 12,
      search = '',
      categoryId,
      templateType,
      ruleCategory,
      severityLevel,
      isOfficial,
      isFeatured,
      tags,
      sortBy = 'usageCount',
      sortOrder = 'DESC'
    } = params

    // 构建WHERE条件
    const conditions: string[] = ['t.IS_DELETED = 0', 't.IS_ACTIVE = 1']
    const queryParams: any = {}

    if (search) {
      conditions.push(`(
        UPPER(t.TEMPLATE_NAME) LIKE UPPER(:search) OR 
        UPPER(t.TEMPLATE_CODE) LIKE UPPER(:search) OR 
        UPPER(t.TEMPLATE_DESC) LIKE UPPER(:search) OR
        UPPER(t.TAGS) LIKE UPPER(:search)
      )`)
      queryParams.search = `%${search}%`
    }

    if (categoryId) {
      conditions.push('t.CATEGORY_ID = :categoryId')
      queryParams.categoryId = categoryId
    }

    if (templateType) {
      conditions.push('t.TEMPLATE_TYPE = :templateType')
      queryParams.templateType = templateType
    }

    if (ruleCategory) {
      conditions.push('t.RULE_CATEGORY = :ruleCategory')
      queryParams.ruleCategory = ruleCategory
    }

    if (severityLevel) {
      conditions.push('t.SEVERITY_LEVEL = :severityLevel')
      queryParams.severityLevel = severityLevel
    }

    if (isOfficial !== undefined) {
      conditions.push('t.IS_OFFICIAL = :isOfficial')
      queryParams.isOfficial = isOfficial ? 1 : 0
    }

    if (isFeatured !== undefined) {
      conditions.push('t.IS_FEATURED = :isFeatured')
      queryParams.isFeatured = isFeatured ? 1 : 0
    }

    if (tags && tags.length > 0) {
      const tagConditions = tags.map((_, index) => `UPPER(t.TAGS) LIKE UPPER(:tag${index})`)
      conditions.push(`(${tagConditions.join(' OR ')})`)
      tags.forEach((tag, index) => {
        queryParams[`tag${index}`] = `%${tag}%`
      })
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as TOTAL_COUNT
      FROM RULE_TEMPLATE t
      LEFT JOIN RULE_TEMPLATE_CATEGORY c ON t.CATEGORY_ID = c.ID
      ${whereClause}
    `

    const countResult = await executeQuery(countSql, queryParams)
    const total = countResult.rows?.[0]?.TOTAL_COUNT || 0
    const totalPages = Math.ceil(total / pageSize)

    // 获取数据
    const offset = (page - 1) * pageSize
    const dataSql = `
      SELECT 
        t.ID, t.TEMPLATE_CODE, t.TEMPLATE_NAME, t.TEMPLATE_DESC,
        t.CATEGORY_ID, t.TEMPLATE_TYPE, t.RULE_CATEGORY,
        t.SEVERITY_LEVEL, t.PRIORITY_LEVEL, t.TEMPLATE_CONTENT,
        t.TEMPLATE_SQL, t.TEMPLATE_DSL, t.PARAMETER_SCHEMA,
        t.USAGE_COUNT, t.SUCCESS_RATE, t.LAST_USED_AT,
        t.TAGS, t.PREVIEW_DATA, t.IS_OFFICIAL, t.IS_FEATURED,
        t.VERSION_NUMBER, t.CREATED_AT, t.UPDATED_AT,
        t.CREATED_BY, t.UPDATED_BY,
        c.CATEGORY_NAME, c.CATEGORY_CODE, c.ICON_NAME, c.COLOR_SCHEME,
        COALESCE(AVG(r.RATING_SCORE), 0) as AVERAGE_RATING,
        COUNT(r.ID) as RATING_COUNT
      FROM RULE_TEMPLATE t
      LEFT JOIN RULE_TEMPLATE_CATEGORY c ON t.CATEGORY_ID = c.ID
      LEFT JOIN RULE_TEMPLATE_RATING r ON t.ID = r.TEMPLATE_ID
      ${whereClause}
      GROUP BY t.ID, t.TEMPLATE_CODE, t.TEMPLATE_NAME, t.TEMPLATE_DESC,
               t.CATEGORY_ID, t.TEMPLATE_TYPE, t.RULE_CATEGORY,
               t.SEVERITY_LEVEL, t.PRIORITY_LEVEL, t.TEMPLATE_CONTENT,
               t.TEMPLATE_SQL, t.TEMPLATE_DSL, t.PARAMETER_SCHEMA,
               t.USAGE_COUNT, t.SUCCESS_RATE, t.LAST_USED_AT,
               t.TAGS, t.PREVIEW_DATA, t.IS_OFFICIAL, t.IS_FEATURED,
               t.VERSION_NUMBER, t.CREATED_AT, t.UPDATED_AT,
               t.CREATED_BY, t.UPDATED_BY,
               c.CATEGORY_NAME, c.CATEGORY_CODE, c.ICON_NAME, c.COLOR_SCHEME
      ORDER BY t.${sortBy} ${sortOrder}, t.CREATED_AT DESC
      OFFSET ${offset} ROWS FETCH NEXT ${pageSize} ROWS ONLY
    `

    const dataResult = await executeQuery(dataSql, queryParams)

    const data = (dataResult.rows || []).map((row: any) => ({
      id: row.ID,
      templateCode: row.TEMPLATE_CODE,
      templateName: row.TEMPLATE_NAME,
      templateDesc: row.TEMPLATE_DESC,
      categoryId: row.CATEGORY_ID,
      templateType: row.TEMPLATE_TYPE,
      ruleCategory: row.RULE_CATEGORY,
      severityLevel: row.SEVERITY_LEVEL,
      priorityLevel: row.PRIORITY_LEVEL,
      templateContent: row.TEMPLATE_CONTENT,
      templateSql: row.TEMPLATE_SQL,
      templateDsl: row.TEMPLATE_DSL,
      parameterSchema: row.PARAMETER_SCHEMA ? JSON.parse(row.PARAMETER_SCHEMA) : undefined,
      usageCount: row.USAGE_COUNT,
      successRate: row.SUCCESS_RATE,
      lastUsedAt: row.LAST_USED_AT?.toISOString(),
      tags: row.TAGS ? row.TAGS.split(',').map((tag: string) => tag.trim()) : [],
      previewData: row.PREVIEW_DATA ? JSON.parse(row.PREVIEW_DATA) : undefined,
      isOfficial: row.IS_OFFICIAL === 1,
      isFeatured: row.IS_FEATURED === 1,
      isActive: true,
      isDeleted: false,
      versionNumber: row.VERSION_NUMBER,
      createdAt: row.CREATED_AT?.toISOString() || '',
      updatedAt: row.UPDATED_AT?.toISOString() || '',
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
      category: row.CATEGORY_NAME ? {
        id: row.CATEGORY_ID,
        categoryCode: row.CATEGORY_CODE,
        categoryName: row.CATEGORY_NAME,
        categoryDesc: row.CATEGORY_DESC || '',
        parentCategoryId: row.PARENT_CATEGORY_ID,
        categoryLevel: row.CATEGORY_LEVEL || 1,
        sortOrder: row.SORT_ORDER || 0,
        iconName: row.ICON_NAME,
        colorScheme: row.COLOR_SCHEME,
        isActive: true,
        isDeleted: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } : undefined,
      averageRating: parseFloat(row.AVERAGE_RATING) || 0,
      ratingCount: row.RATING_COUNT || 0
    }))

    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages
      }
    }

  } catch (error) {
    console.error('❌ 获取模板列表失败:', error)
    throw new Error(`获取模板列表失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 获取单个模板详情
 */
export async function getTemplateById(id: number): Promise<RuleTemplate | null> {
  try {
    const sql = `
      SELECT 
        t.ID, t.TEMPLATE_CODE, t.TEMPLATE_NAME, t.TEMPLATE_DESC,
        t.CATEGORY_ID, t.TEMPLATE_TYPE, t.RULE_CATEGORY,
        t.SEVERITY_LEVEL, t.PRIORITY_LEVEL, t.TEMPLATE_CONTENT,
        t.TEMPLATE_SQL, t.TEMPLATE_DSL, t.PARAMETER_SCHEMA,
        t.USAGE_COUNT, t.SUCCESS_RATE, t.LAST_USED_AT,
        t.TAGS, t.PREVIEW_DATA, t.IS_OFFICIAL, t.IS_FEATURED,
        t.IS_ACTIVE, t.VERSION_NUMBER, t.CREATED_AT, t.UPDATED_AT,
        t.CREATED_BY, t.UPDATED_BY,
        c.CATEGORY_NAME, c.CATEGORY_CODE, c.ICON_NAME, c.COLOR_SCHEME,
        COALESCE(AVG(r.RATING_SCORE), 0) as AVERAGE_RATING,
        COUNT(r.ID) as RATING_COUNT
      FROM RULE_TEMPLATE t
      LEFT JOIN RULE_TEMPLATE_CATEGORY c ON t.CATEGORY_ID = c.ID
      LEFT JOIN RULE_TEMPLATE_RATING r ON t.ID = r.TEMPLATE_ID
      WHERE t.ID = :id AND t.IS_DELETED = 0
      GROUP BY t.ID, t.TEMPLATE_CODE, t.TEMPLATE_NAME, t.TEMPLATE_DESC,
               t.CATEGORY_ID, t.TEMPLATE_TYPE, t.RULE_CATEGORY,
               t.SEVERITY_LEVEL, t.PRIORITY_LEVEL, t.TEMPLATE_CONTENT,
               t.TEMPLATE_SQL, t.TEMPLATE_DSL, t.PARAMETER_SCHEMA,
               t.USAGE_COUNT, t.SUCCESS_RATE, t.LAST_USED_AT,
               t.TAGS, t.PREVIEW_DATA, t.IS_OFFICIAL, t.IS_FEATURED,
               t.IS_ACTIVE, t.VERSION_NUMBER, t.CREATED_AT, t.UPDATED_AT,
               t.CREATED_BY, t.UPDATED_BY,
               c.CATEGORY_NAME, c.CATEGORY_CODE, c.ICON_NAME, c.COLOR_SCHEME
    `

    const result = await executeQuery(sql, { id })
    
    if (!result.rows || result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      id: row.ID,
      templateCode: row.TEMPLATE_CODE,
      templateName: row.TEMPLATE_NAME,
      templateDesc: row.TEMPLATE_DESC,
      categoryId: row.CATEGORY_ID,
      templateType: row.TEMPLATE_TYPE,
      ruleCategory: row.RULE_CATEGORY,
      severityLevel: row.SEVERITY_LEVEL,
      priorityLevel: row.PRIORITY_LEVEL,
      templateContent: row.TEMPLATE_CONTENT,
      templateSql: row.TEMPLATE_SQL,
      templateDsl: row.TEMPLATE_DSL,
      parameterSchema: row.PARAMETER_SCHEMA ? JSON.parse(row.PARAMETER_SCHEMA) : undefined,
      usageCount: row.USAGE_COUNT,
      successRate: row.SUCCESS_RATE,
      lastUsedAt: row.LAST_USED_AT?.toISOString(),
      tags: row.TAGS ? row.TAGS.split(',').map((tag: string) => tag.trim()) : [],
      previewData: row.PREVIEW_DATA ? JSON.parse(row.PREVIEW_DATA) : undefined,
      isOfficial: row.IS_OFFICIAL === 1,
      isFeatured: row.IS_FEATURED === 1,
      isActive: row.IS_ACTIVE === 1,
      isDeleted: false,
      versionNumber: row.VERSION_NUMBER,
      createdAt: row.CREATED_AT?.toISOString() || '',
      updatedAt: row.UPDATED_AT?.toISOString() || '',
      createdBy: row.CREATED_BY,
      updatedBy: row.UPDATED_BY,
      category: row.CATEGORY_NAME ? {
        id: row.CATEGORY_ID,
        categoryCode: row.CATEGORY_CODE,
        categoryName: row.CATEGORY_NAME,
        categoryDesc: row.CATEGORY_DESC || '',
        parentCategoryId: row.PARENT_CATEGORY_ID,
        categoryLevel: row.CATEGORY_LEVEL || 1,
        sortOrder: row.SORT_ORDER || 0,
        iconName: row.ICON_NAME,
        colorScheme: row.COLOR_SCHEME,
        isActive: true,
        isDeleted: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } : undefined,
      averageRating: parseFloat(row.AVERAGE_RATING) || 0,
      ratingCount: row.RATING_COUNT || 0
    }

  } catch (error) {
    console.error('❌ 获取模板详情失败:', error)
    throw new Error(`获取模板详情失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 创建模板
 */
export async function createTemplate(template: TemplateCreateRequest, userId: number): Promise<number> {
  try {
    const sql = `
      INSERT INTO RULE_TEMPLATE (
        TEMPLATE_CODE, TEMPLATE_NAME, TEMPLATE_DESC, CATEGORY_ID,
        TEMPLATE_TYPE, RULE_CATEGORY, SEVERITY_LEVEL, PRIORITY_LEVEL,
        TEMPLATE_CONTENT, TEMPLATE_SQL, TEMPLATE_DSL, PARAMETER_SCHEMA,
        TAGS, PREVIEW_DATA, IS_OFFICIAL, IS_FEATURED,
        VERSION_NUMBER, CREATED_BY, UPDATED_BY
      ) VALUES (
        :templateCode, :templateName, :templateDesc, :categoryId,
        :templateType, :ruleCategory, :severityLevel, :priorityLevel,
        :templateContent, :templateSql, :templateDsl, :parameterSchema,
        :tags, :previewData, :isOfficial, :isFeatured,
        :versionNumber, :userId, :userId
      ) RETURNING ID INTO :id
    `

    const params = {
      templateCode: template.templateCode,
      templateName: template.templateName,
      templateDesc: template.templateDesc || null,
      categoryId: template.categoryId,
      templateType: template.templateType,
      ruleCategory: template.ruleCategory,
      severityLevel: template.severityLevel,
      priorityLevel: template.priorityLevel,
      templateContent: template.templateContent,
      templateSql: template.templateSql || null,
      templateDsl: template.templateDsl || null,
      parameterSchema: template.parameterSchema ? JSON.stringify(template.parameterSchema) : null,
      tags: template.tags ? template.tags.join(',') : null,
      previewData: template.previewData ? JSON.stringify(template.previewData) : null,
      isOfficial: template.isOfficial ? 1 : 0,
      isFeatured: template.isFeatured ? 1 : 0,
      versionNumber: template.versionNumber || '1.0.0',
      userId,
      id: { dir: 'OUT', type: 'NUMBER' }
    }

    const result = await executeQuery(sql, params)
    return (result as any).outBinds?.id || 0

  } catch (error) {
    console.error('❌ 创建模板失败:', error)
    throw new Error(`创建模板失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 更新模板
 */
export async function updateTemplate(template: TemplateUpdateRequest, userId: number): Promise<void> {
  try {
    const updateFields: string[] = []
    const params: any = { id: template.id, userId }

    if (template.templateName !== undefined) {
      updateFields.push('TEMPLATE_NAME = :templateName')
      params.templateName = template.templateName
    }

    if (template.templateDesc !== undefined) {
      updateFields.push('TEMPLATE_DESC = :templateDesc')
      params.templateDesc = template.templateDesc
    }

    if (template.categoryId !== undefined) {
      updateFields.push('CATEGORY_ID = :categoryId')
      params.categoryId = template.categoryId
    }

    if (template.templateType !== undefined) {
      updateFields.push('TEMPLATE_TYPE = :templateType')
      params.templateType = template.templateType
    }

    if (template.ruleCategory !== undefined) {
      updateFields.push('RULE_CATEGORY = :ruleCategory')
      params.ruleCategory = template.ruleCategory
    }

    if (template.severityLevel !== undefined) {
      updateFields.push('SEVERITY_LEVEL = :severityLevel')
      params.severityLevel = template.severityLevel
    }

    if (template.priorityLevel !== undefined) {
      updateFields.push('PRIORITY_LEVEL = :priorityLevel')
      params.priorityLevel = template.priorityLevel
    }

    if (template.templateContent !== undefined) {
      updateFields.push('TEMPLATE_CONTENT = :templateContent')
      params.templateContent = template.templateContent
    }

    if (template.templateSql !== undefined) {
      updateFields.push('TEMPLATE_SQL = :templateSql')
      params.templateSql = template.templateSql
    }

    if (template.templateDsl !== undefined) {
      updateFields.push('TEMPLATE_DSL = :templateDsl')
      params.templateDsl = template.templateDsl
    }

    if (template.parameterSchema !== undefined) {
      updateFields.push('PARAMETER_SCHEMA = :parameterSchema')
      params.parameterSchema = template.parameterSchema ? JSON.stringify(template.parameterSchema) : null
    }

    if (template.tags !== undefined) {
      updateFields.push('TAGS = :tags')
      params.tags = template.tags ? template.tags.join(',') : null
    }

    if (template.previewData !== undefined) {
      updateFields.push('PREVIEW_DATA = :previewData')
      params.previewData = template.previewData ? JSON.stringify(template.previewData) : null
    }

    if (template.isOfficial !== undefined) {
      updateFields.push('IS_OFFICIAL = :isOfficial')
      params.isOfficial = template.isOfficial ? 1 : 0
    }

    if (template.isFeatured !== undefined) {
      updateFields.push('IS_FEATURED = :isFeatured')
      params.isFeatured = template.isFeatured ? 1 : 0
    }

    if (template.versionNumber !== undefined) {
      updateFields.push('VERSION_NUMBER = :versionNumber')
      params.versionNumber = template.versionNumber
    }

    if (updateFields.length === 0) {
      return
    }

    updateFields.push('UPDATED_BY = :userId', 'UPDATED_AT = SYSDATE')

    const sql = `
      UPDATE RULE_TEMPLATE
      SET ${updateFields.join(', ')}
      WHERE ID = :id AND IS_DELETED = 0
    `

    await executeQuery(sql, params)

  } catch (error) {
    console.error('❌ 更新模板失败:', error)
    throw new Error(`更新模板失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

/**
 * 删除模板
 */
export async function deleteTemplate(id: number, userId: number): Promise<void> {
  try {
    const sql = `
      UPDATE RULE_TEMPLATE
      SET IS_DELETED = 1, UPDATED_BY = :userId, UPDATED_AT = SYSDATE
      WHERE ID = :id
    `

    await executeQuery(sql, { id, userId })

  } catch (error) {
    console.error('❌ 删除模板失败:', error)
    throw new Error(`删除模板失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}
