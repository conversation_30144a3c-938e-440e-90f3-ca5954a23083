/**
 * Web Vitals 监控器
 * 实现P0-005前端性能优化目标：
 * - 页面首屏加载时间 < 2秒
 * - 表格支持10,000+行数据流畅滚动
 * - Core Web Vitals全部达到绿色
 */

import { onCLS, onFCP, onINP, onLCP, onTTFB } from 'web-vitals'

export interface WebVitalsMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
  timestamp: Date
}

export interface PerformanceTarget {
  fcp: number // < 1800ms (good)
  lcp: number // < 2500ms (good)
  fid: number // < 100ms (good)
  cls: number // < 0.1 (good)
  ttfb: number // < 800ms (good)
}

export interface PerformanceReport {
  metrics: WebVitalsMetrics
  targets: PerformanceTarget
  scores: {
    fcp: 'good' | 'needs-improvement' | 'poor'
    lcp: 'good' | 'needs-improvement' | 'poor'
    fid: 'good' | 'needs-improvement' | 'poor'
    cls: 'good' | 'needs-improvement' | 'poor'
    ttfb: 'good' | 'needs-improvement' | 'poor'
    overall: 'good' | 'needs-improvement' | 'poor'
  }
  suggestions: string[]
}

class WebVitalsMonitor {
  private metrics: Partial<WebVitalsMetrics> = {}
  private targets: PerformanceTarget = {
    fcp: 1800, // 1.8s
    lcp: 2500, // 2.5s
    fid: 100,  // 100ms
    cls: 0.1,  // 0.1
    ttfb: 800  // 800ms
  }
  private listeners: Array<(report: PerformanceReport) => void> = []

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeMonitoring()
    }
  }

  /**
   * 初始化Web Vitals监控
   */
  private initializeMonitoring(): void {
    // 监控 FCP (First Contentful Paint)
    onFCP((metric: any) => {
      this.metrics.fcp = metric.value
      this.checkAndReport()
    })

    // 监控 LCP (Largest Contentful Paint)
    onLCP((metric: any) => {
      this.metrics.lcp = metric.value
      this.checkAndReport()
    })

    // 监控 INP (Interaction to Next Paint)
    onINP((metric: any) => {
      this.metrics.fid = metric.value
      this.checkAndReport()
    })

    // 监控 CLS (Cumulative Layout Shift)
    onCLS((metric: any) => {
      this.metrics.cls = metric.value
      this.checkAndReport()
    })

    // 监控 TTFB (Time to First Byte)
    onTTFB((metric: any) => {
      this.metrics.ttfb = metric.value
      this.checkAndReport()
    })

    // 页面卸载时发送最终报告
    window.addEventListener('beforeunload', () => {
      this.sendFinalReport()
    })
  }

  /**
   * 检查指标并生成报告
   */
  private checkAndReport(): void {
    // 等待所有核心指标收集完成
    if (this.hasAllCoreMetrics()) {
      const report = this.generateReport()
      this.notifyListeners(report)
      
      // 发送到分析服务
      this.sendToAnalytics(report)
    }
  }

  /**
   * 检查是否收集了所有核心指标
   */
  private hasAllCoreMetrics(): boolean {
    return !!(
      this.metrics.fcp &&
      this.metrics.lcp &&
      this.metrics.cls !== undefined &&
      this.metrics.ttfb
    )
  }

  /**
   * 生成性能报告
   */
  private generateReport(): PerformanceReport {
    const metrics: WebVitalsMetrics = {
      fcp: this.metrics.fcp || 0,
      lcp: this.metrics.lcp || 0,
      fid: this.metrics.fid || 0,
      cls: this.metrics.cls || 0,
      ttfb: this.metrics.ttfb || 0,
      timestamp: new Date()
    }

    const scores = {
      fcp: this.getScore('fcp', metrics.fcp),
      lcp: this.getScore('lcp', metrics.lcp),
      fid: this.getScore('fid', metrics.fid),
      cls: this.getScore('cls', metrics.cls),
      ttfb: this.getScore('ttfb', metrics.ttfb),
      overall: 'good' as const
    }

    // 计算总体评分
    const goodCount = Object.values(scores).filter(score => score === 'good').length
    if (goodCount >= 4) {
      scores.overall = 'good'
    } else if (goodCount >= 2) {
      scores.overall = 'good' as const
    } else {
      scores.overall = 'good' as const
    }

    const suggestions = this.generateSuggestions(metrics, scores)

    return {
      metrics,
      targets: this.targets,
      scores,
      suggestions
    }
  }

  /**
   * 获取单项指标评分
   */
  private getScore(metric: keyof PerformanceTarget, value: number): 'good' | 'needs-improvement' | 'poor' {
    const target = this.targets[metric]
    
    switch (metric) {
      case 'fcp':
        if (value <= 1800) return 'good'
        if (value <= 3000) return 'needs-improvement'
        return 'poor'
      
      case 'lcp':
        if (value <= 2500) return 'good'
        if (value <= 4000) return 'needs-improvement'
        return 'poor'
      
      case 'fid':
        if (value <= 100) return 'good'
        if (value <= 300) return 'needs-improvement'
        return 'poor'
      
      case 'cls':
        if (value <= 0.1) return 'good'
        if (value <= 0.25) return 'needs-improvement'
        return 'poor'
      
      case 'ttfb':
        if (value <= 800) return 'good'
        if (value <= 1800) return 'needs-improvement'
        return 'poor'
      
      default:
        return 'good'
    }
  }

  /**
   * 生成优化建议
   */
  private generateSuggestions(metrics: WebVitalsMetrics, scores: PerformanceReport['scores']): string[] {
    const suggestions: string[] = []

    if (scores.fcp !== 'good') {
      suggestions.push('优化首次内容绘制：减少关键资源大小，使用内联CSS，优化字体加载')
    }

    if (scores.lcp !== 'good') {
      suggestions.push('优化最大内容绘制：优化图片加载，使用CDN，预加载关键资源')
    }

    if (scores.fid !== 'good') {
      suggestions.push('优化首次输入延迟：减少JavaScript执行时间，使用Web Workers，优化事件处理')
    }

    if (scores.cls !== 'good') {
      suggestions.push('优化累积布局偏移：为图片设置尺寸，避免动态内容插入，使用CSS容器查询')
    }

    if (scores.ttfb !== 'good') {
      suggestions.push('优化首字节时间：优化服务器响应，使用CDN，实施缓存策略')
    }

    // 通用建议
    if (scores.overall !== 'good') {
      suggestions.push('启用压缩和缓存，使用现代图片格式，实施代码分割')
    }

    return suggestions
  }

  /**
   * 发送到分析服务
   */
  private sendToAnalytics(report: PerformanceReport): void {
    try {
      // 发送到内部分析API
      fetch('/api/analytics/web-vitals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: report.metrics.timestamp,
          metrics: report.metrics,
          scores: report.scores
        })
      }).catch(error => {
        console.warn('Failed to send web vitals to analytics:', error)
      })

      // 记录到控制台（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.group('🚀 Web Vitals Report')
        console.table(report.metrics)
        console.log('Scores:', report.scores)
        console.log('Suggestions:', report.suggestions)
        console.groupEnd()
      }
    } catch (error) {
      console.error('Error sending analytics:', error)
    }
  }

  /**
   * 发送最终报告
   */
  private sendFinalReport(): void {
    if (this.hasAllCoreMetrics()) {
      const report = this.generateReport()
      
      // 使用 sendBeacon 确保数据发送
      if (navigator.sendBeacon) {
        navigator.sendBeacon(
          '/api/analytics/web-vitals',
          JSON.stringify({
            url: window.location.href,
            final: true,
            metrics: report.metrics,
            scores: report.scores
          })
        )
      }
    }
  }

  /**
   * 添加监听器
   */
  onReport(listener: (report: PerformanceReport) => void): void {
    this.listeners.push(listener)
  }

  /**
   * 移除监听器
   */
  removeListener(listener: (report: PerformanceReport) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(report: PerformanceReport): void {
    this.listeners.forEach(listener => {
      try {
        listener(report)
      } catch (error) {
        console.error('Error in web vitals listener:', error)
      }
    })
  }

  /**
   * 获取当前指标
   */
  getCurrentMetrics(): Partial<WebVitalsMetrics> {
    return { ...this.metrics }
  }

  /**
   * 手动触发报告生成
   */
  generateCurrentReport(): PerformanceReport | null {
    if (this.hasAllCoreMetrics()) {
      return this.generateReport()
    }
    return null
  }
}

// 创建全局实例
export const webVitalsMonitor = new WebVitalsMonitor()

// 导出便捷函数
export function trackWebVitals(callback: (report: PerformanceReport) => void): void {
  webVitalsMonitor.onReport(callback)
}

export function getCurrentWebVitals(): Partial<WebVitalsMetrics> {
  return webVitalsMonitor.getCurrentMetrics()
}
