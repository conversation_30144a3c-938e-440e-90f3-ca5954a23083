/**
 * 前端性能优化器
 * 提供全面的前端性能优化功能
 */

import { monitoring } from '@/lib/monitoring'
import { log } from '@/lib/logger'

/**
 * 性能优化配置
 */
interface PerformanceConfig {
  enableVirtualScrolling: boolean
  enableLazyLoading: boolean
  enableImageOptimization: boolean
  enableCodeSplitting: boolean
  enableServiceWorker: boolean
  enablePreloading: boolean
  chunkSizeLimit: number
  imageQuality: number
  cacheStrategy: 'aggressive' | 'conservative' | 'balanced'
}

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
  tti: number // Time to Interactive
  bundleSize: number
  cacheHitRate: number
  memoryUsage: number
}

/**
 * 前端性能优化器类
 */
export class FrontendOptimizer {
  private config: PerformanceConfig
  private metrics: PerformanceMetrics = {
    fcp: 0,
    lcp: 0,
    fid: 0,
    cls: 0,
    ttfb: 0,
    tti: 0,
    bundleSize: 0,
    cacheHitRate: 0,
    memoryUsage: 0
  }

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enableVirtualScrolling: true,
      enableLazyLoading: true,
      enableImageOptimization: true,
      enableCodeSplitting: true,
      enableServiceWorker: true,
      enablePreloading: true,
      chunkSizeLimit: 244 * 1024, // 244KB
      imageQuality: 85,
      cacheStrategy: 'balanced',
      ...config
    }
  }

  /**
   * 初始化性能优化
   */
  async initialize(): Promise<void> {
    try {
      // 注册性能监控
      this.registerPerformanceObserver()
      
      // 启用Service Worker
      if (this.config.enableServiceWorker) {
        await this.registerServiceWorker()
      }
      
      // 预加载关键资源
      if (this.config.enablePreloading) {
        this.preloadCriticalResources()
      }
      
      // 优化图片加载
      if (this.config.enableImageOptimization) {
        this.optimizeImageLoading()
      }
      
      // 监控内存使用
      this.monitorMemoryUsage()
      
      log.info('Frontend optimizer initialized', { config: this.config })
      
    } catch (error) {
      log.error('Frontend optimizer initialization failed', { error: (error as Error).message })
    }
  }

  /**
   * 注册性能观察器
   */
  private registerPerformanceObserver(): void {
    if (typeof window === 'undefined') return

    // Web Vitals 监控
    if ('PerformanceObserver' in window) {
      // LCP 监控
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        this.metrics.lcp = lastEntry.startTime
        this.reportMetric('lcp', lastEntry.startTime)
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // FID 监控
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          this.metrics.fid = entry.processingStart - entry.startTime
          this.reportMetric('fid', this.metrics.fid)
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // CLS 监控
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        this.metrics.cls = clsValue
        this.reportMetric('cls', clsValue)
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    }

    // FCP 监控
    if ('performance' in window && 'getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint')
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime
        this.reportMetric('fcp', fcpEntry.startTime)
      }
    }
  }

  /**
   * 注册Service Worker
   */
  private async registerServiceWorker(): Promise<void> {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) return

    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // 新版本可用，提示用户刷新
              this.notifyUpdate()
            }
          })
        }
      })
      
      log.info('Service Worker registered successfully')
      
    } catch (error) {
      log.error('Service Worker registration failed', { error: (error as Error).message })
    }
  }

  /**
   * 预加载关键资源
   */
  private preloadCriticalResources(): void {
    if (typeof window === 'undefined') return

    const criticalResources = [
      // 关键CSS
      { href: '/styles/critical.css', as: 'style' },
      // 关键字体
      { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' },
      // 关键API数据
      { href: '/api/user/profile', as: 'fetch' },
      { href: '/api/medical-cases/stats', as: 'fetch' }
    ]

    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource.href
      link.as = resource.as
      if (resource.type) link.type = resource.type
      if (resource.crossorigin) link.crossOrigin = resource.crossorigin
      document.head.appendChild(link)
    })
  }

  /**
   * 优化图片加载
   */
  private optimizeImageLoading(): void {
    if (typeof window === 'undefined') return

    // 使用Intersection Observer实现图片懒加载
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset['src']
          if (src) {
            img.src = src
            img.removeAttribute('data-src')
            imageObserver.unobserve(img)
          }
        }
      })
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    })

    // 观察所有懒加载图片
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img)
    })
  }

  /**
   * 监控内存使用
   */
  private monitorMemoryUsage(): void {
    if (typeof window === 'undefined') return

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
        
        // 内存使用过高警告
        if (this.metrics.memoryUsage > 100) {
          log.warn('High memory usage detected', { memoryUsage: this.metrics.memoryUsage })
        }
        
        this.reportMetric('memory_usage', this.metrics.memoryUsage)
      }
    }

    // 每30秒检查一次内存使用
    setInterval(checkMemory, 30000)
    checkMemory() // 立即检查一次
  }

  /**
   * 报告性能指标
   */
  private reportMetric(name: string, value: number): void {
    monitoring.recordMetric({
      name: `frontend_${name}`,
      value,
      tags: {
        page: window.location.pathname,
        user_agent: navigator.userAgent.split(' ')[0] || 'unknown'
      }
    })
  }

  /**
   * 通知更新可用
   */
  private notifyUpdate(): void {
    // 这里可以显示一个通知，提示用户刷新页面
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('应用更新可用', {
        body: '点击刷新页面以获取最新版本',
        icon: '/icon-192x192.png'
      })
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取性能评分
   */
  getPerformanceScore(): number {
    const weights = {
      fcp: 0.15,
      lcp: 0.25,
      fid: 0.25,
      cls: 0.25,
      ttfb: 0.10
    }

    // 计算各项指标的评分（0-100）
    const fcpScore = this.calculateScore(this.metrics.fcp, [1800, 3000])
    const lcpScore = this.calculateScore(this.metrics.lcp, [2500, 4000])
    const fidScore = this.calculateScore(this.metrics.fid, [100, 300])
    const clsScore = this.calculateScore(this.metrics.cls, [0.1, 0.25], true)
    const ttfbScore = this.calculateScore(this.metrics.ttfb, [800, 1800])

    const totalScore = 
      fcpScore * weights.fcp +
      lcpScore * weights.lcp +
      fidScore * weights.fid +
      clsScore * weights.cls +
      ttfbScore * weights.ttfb

    return Math.round(totalScore)
  }

  /**
   * 计算单项指标评分
   */
  private calculateScore(value: number, thresholds: [number, number], reverse = false): number {
    const [good, poor] = thresholds
    
    if (reverse) {
      // 对于CLS等指标，值越小越好
      if (value <= good) return 100
      if (value >= poor) return 0
      return Math.round(100 - ((value - good) / (poor - good)) * 100)
    } else {
      // 对于时间类指标，值越小越好
      if (value <= good) return 100
      if (value >= poor) return 0
      return Math.round(100 - ((value - good) / (poor - good)) * 100)
    }
  }

  /**
   * 优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []
    
    if (this.metrics.fcp > 3000) {
      suggestions.push('优化首次内容绘制时间：减少关键资源大小，使用内联CSS')
    }
    
    if (this.metrics.lcp > 4000) {
      suggestions.push('优化最大内容绘制时间：优化图片加载，使用CDN')
    }
    
    if (this.metrics.fid > 300) {
      suggestions.push('优化首次输入延迟：减少JavaScript执行时间，使用Web Workers')
    }
    
    if (this.metrics.cls > 0.25) {
      suggestions.push('优化累积布局偏移：为图片和广告设置尺寸，避免动态内容插入')
    }
    
    if (this.metrics.memoryUsage > 100) {
      suggestions.push('优化内存使用：清理未使用的事件监听器，避免内存泄漏')
    }
    
    return suggestions
  }
}

// 创建全局实例
export const frontendOptimizer = new FrontendOptimizer()

// 自动初始化（仅在浏览器环境）
if (typeof window !== 'undefined') {
  frontendOptimizer.initialize()
}
