'use client'

import React, { Suspense, lazy, ComponentType, ReactNode } from 'react'
import { Loader2 } from 'lucide-react'
import { log } from '@/lib/logger'
import { monitoring } from '@/lib/monitoring'

/**
 * 懒加载配置接口
 */
interface LazyLoadConfig {
  fallback?: ReactNode
  errorBoundary?: ComponentType<{ error: Error; retry: () => void }>
  preload?: boolean
  timeout?: number
  retryCount?: number
}

/**
 * 懒加载组件的默认加载指示器
 */
const DefaultFallback = () => (
  <div className="flex items-center justify-center p-8">
    <div className="flex flex-col items-center space-y-2">
      <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      <p className="text-sm text-gray-500">加载中...</p>
    </div>
  </div>
)

/**
 * 默认错误边界组件
 */
const DefaultErrorBoundary: ComponentType<{ error: Error; retry: () => void }> = ({ error, retry }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center">
      <div className="text-red-500 mb-2">
        <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
      <p className="text-sm text-gray-500 mb-4">{error.message}</p>
      <button
        onClick={retry}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        重试
      </button>
    </div>
  </div>
)

/**
 * 错误边界类组件
 */
class LazyErrorBoundary extends React.Component<
  {
    children: ReactNode
    fallback: ComponentType<{ error: Error; retry: () => void }>
    onError?: (error: Error) => void
  },
  { hasError: boolean; error: Error | null; retryCount: number }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false, error: null, retryCount: 0 }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    log.error('Lazy component loading failed', { error: error.message, errorInfo })

    monitoring.recordMetric({
      name: 'lazy_component_error',
      value: 1,
      tags: { error_type: error.name }
    })

    this.props.onError?.(error)
  }

  retry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1
    }))
  }

  override render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback
      return <FallbackComponent error={this.state.error} retry={this.retry} />
    }

    return this.props.children
  }
}

/**
 * 创建懒加载组件的高阶函数
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
): ComponentType<React.ComponentProps<T>> {
  const {
    fallback = <DefaultFallback />,
    errorBoundary = DefaultErrorBoundary,
    preload = false,
    timeout = 10000,
    retryCount = 3
  } = config

  // 创建懒加载组件
  const LazyComponent = lazy(() => {
    const timer = monitoring.startTimer('lazy_component_load')
    
    return Promise.race([
      importFn().then(module => {
        timer.end(true)
        monitoring.recordMetric({
          name: 'lazy_component_success',
          value: 1
        })
        return module
      }),
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          timer.end(false, 'timeout')
          reject(new Error(`组件加载超时 (${timeout}ms)`))
        }, timeout)
      })
    ]).catch(error => {
      timer.end(false, error.message)
      monitoring.recordMetric({
        name: 'lazy_component_timeout',
        value: 1
      })
      throw error
    })
  })

  // 预加载功能
  if (preload && typeof window !== 'undefined') {
    // 在空闲时间预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        importFn().catch(() => {
          // 预加载失败不影响正常使用
        })
      })
    } else {
      // 降级到setTimeout
      setTimeout(() => {
        importFn().catch(() => {})
      }, 100)
    }
  }

  // 返回包装后的组件
  const WrappedComponent: ComponentType<React.ComponentProps<T>> = (props) => (
    <LazyErrorBoundary fallback={errorBoundary}>
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    </LazyErrorBoundary>
  )

  // 添加预加载方法
  ;(WrappedComponent as any).preload = () => importFn()

  return WrappedComponent
}

/**
 * 路由级别的懒加载组件
 */
export const LazyPageWrapper = ({ children }: { children: ReactNode }) => (
  <Suspense fallback={<PageLoadingFallback />}>
    {children}
  </Suspense>
)

/**
 * 页面级加载指示器
 */
const PageLoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="relative">
        <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mx-auto"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 bg-blue-500 rounded-full animate-pulse"></div>
        </div>
      </div>
      <h2 className="mt-4 text-lg font-medium text-gray-900">加载页面中</h2>
      <p className="mt-2 text-sm text-gray-500">请稍候...</p>
    </div>
  </div>
)

/**
 * 预定义的懒加载组件
 */

// 医疗案例详情页
// export const LazyMedicalCaseDetail = createLazyComponent(
//   () => import('@/components/medical-cases/case-detail'),
//   {
//     fallback: <DefaultFallback />,
//     preload: true
//   }
// )

// 规则管理页面
// export const LazyRuleManagement = createLazyComponent(
//   () => import('@/components/supervision-rules/rule-management'),
//   {
//     fallback: <DefaultFallback />,
//     preload: false
//   }
// )

// 数据可视化组件
// export const LazyDataVisualization = createLazyComponent(
//   () => import('@/components/analytics/data-visualization'),
//   {
//     fallback: <DefaultFallback />,
//     preload: false,
//     timeout: 15000 // 图表组件可能需要更长加载时间
//   }
// )

// 性能监控页面
export const LazyPerformanceMonitoring = createLazyComponent(
  () => import('@/app/monitoring/performance/page'),
  {
    fallback: <DefaultFallback />,
    preload: false
  }
)

// 用户管理页面
// export const LazyUserManagement = createLazyComponent(
//   () => import('@/components/user-management/user-list'),
//   {
//     fallback: <DefaultFallback />,
//     preload: false
//   }
// )

/**
 * 动态导入工具函数
 */
export const dynamicImport = {
  /**
   * 动态导入组件
   */
  component: <T extends ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    config?: LazyLoadConfig
  ) => createLazyComponent(importFn, config),

  /**
   * 动态导入工具函数
   */
  utility: async function<T>(importFn: () => Promise<{ default: T }>): Promise<T> {
    const timer = monitoring.startTimer('dynamic_utility_import')
    
    try {
      const module = await importFn()
      timer.end(true)
      return module.default
    } catch (error) {
      timer.end(false, (error as Error).message)
      throw error
    }
  },

  /**
   * 动态导入JSON数据
   */
  json: async function<T>(importFn: () => Promise<T>): Promise<T> {
    const timer = monitoring.startTimer('dynamic_json_import')
    
    try {
      const data = await importFn()
      timer.end(true)
      return data
    } catch (error) {
      timer.end(false, (error as Error).message)
      throw error
    }
  }
}

/**
 * 组件预加载管理器
 */
export class ComponentPreloader {
  private preloadedComponents = new Set<string>()
  private preloadQueue: Array<() => Promise<any>> = []
  private isProcessing = false

  /**
   * 添加组件到预加载队列
   */
  add(name: string, importFn: () => Promise<any>) {
    if (this.preloadedComponents.has(name)) {
      return
    }

    this.preloadQueue.push(async () => {
      try {
        await importFn()
        this.preloadedComponents.add(name)
        log.debug('Component preloaded successfully', { component: name })
      } catch (error) {
        log.warn('Component preload failed', { component: name, error: (error as Error).message })
      }
    })

    this.processQueue()
  }

  /**
   * 处理预加载队列
   */
  private async processQueue() {
    if (this.isProcessing || this.preloadQueue.length === 0) {
      return
    }

    this.isProcessing = true

    while (this.preloadQueue.length > 0) {
      const preloadFn = this.preloadQueue.shift()!
      
      // 在空闲时间执行预加载
      if ('requestIdleCallback' in window) {
        await new Promise<void>(resolve => {
          requestIdleCallback(async () => {
            await preloadFn()
            resolve()
          })
        })
      } else {
        await preloadFn()
      }
    }

    this.isProcessing = false
  }

  /**
   * 获取预加载状态
   */
  getStatus() {
    return {
      preloaded: this.preloadedComponents.size,
      pending: this.preloadQueue.length,
      processing: this.isProcessing
    }
  }
}

// 全局预加载管理器实例
export const componentPreloader = new ComponentPreloader()
