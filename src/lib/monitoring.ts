/**
 * 企业级监控和可观测性服务
 */

export interface MetricData {
  name: string
  value: number
  timestamp: number
  tags?: Record<string, string>
  unit?: string
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  message: string
  timestamp: number
  context?: Record<string, any>
  userId?: string
  requestId?: string
  module?: string
}

export interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: number
  success: boolean
  errorMessage?: string
  metadata?: Record<string, any>
}

class MonitoringService {
  private metrics: MetricData[] = []
  private logs: LogEntry[] = []
  private performances: PerformanceMetric[] = []

  /**
   * 记录业务指标
   */
  recordMetric(metric: Omit<MetricData, 'timestamp'>) {
    this.metrics.push({
      ...metric,
      timestamp: Date.now()
    })

    // 在生产环境中，这里应该发送到监控系统（如 Prometheus、DataDog）
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringSystem('metric', metric)
    }
  }

  /**
   * 记录结构化日志
   */
  log(entry: Omit<LogEntry, 'timestamp'>) {
    const logEntry: LogEntry = {
      ...entry,
      timestamp: Date.now()
    }

    this.logs.push(logEntry)

    // 控制台输出（开发环境）
    if (process.env.NODE_ENV === 'development') {
      const emoji = this.getLogEmoji(entry.level)
      console.log(`${emoji} [${entry.level.toUpperCase()}] ${entry.message}`, entry.context || '')
    }

    // 生产环境发送到日志聚合系统（如 ELK、Splunk）
    if (process.env.NODE_ENV === 'production') {
      this.sendToLoggingSystem(logEntry)
    }
  }

  /**
   * 记录性能指标
   */
  recordPerformance(metric: Omit<PerformanceMetric, 'timestamp'>) {
    const perfMetric: PerformanceMetric = {
      ...metric,
      timestamp: Date.now()
    }

    this.performances.push(perfMetric)

    // 性能告警
    if (metric.duration > 5000) { // 超过5秒
      this.log({
        level: 'warn',
        message: `Slow operation detected: ${metric.operation}`,
        context: { duration: metric.duration, ...metric.metadata }
      })
    }

    // 发送到APM系统
    if (process.env.NODE_ENV === 'production') {
      this.sendToAPMSystem(perfMetric)
    }
  }

  /**
   * 创建性能计时器
   */
  startTimer(operation: string, metadata?: Record<string, any>) {
    const startTime = Date.now()
    
    return {
      end: (success: boolean = true, errorMessage?: string) => {
        const duration = Date.now() - startTime
        this.recordPerformance({
          operation,
          duration,
          success,
          errorMessage,
          metadata
        })
        return duration
      }
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    checks: Record<string, { status: string; latency?: number; error?: string }>
  }> {
    const checks: Record<string, any> = {}

    // 数据库连接检查
    try {
      const dbTimer = this.startTimer('health_check_database')
      // 这里应该实际检查数据库连接
      await new Promise(resolve => setTimeout(resolve, 10)) // 模拟检查
      const latency = dbTimer.end(true)
      checks['database'] = { status: 'healthy', latency }
    } catch (error) {
      checks['database'] = { status: 'unhealthy', error: (error as Error).message }
    }

    // 内存使用检查（仅在 Node.js 环境中可用）
    try {
      // 使用动态访问避免 webpack 静态分析
      const processObj = typeof process !== 'undefined' ? process : null
      const memoryUsageFn = processObj && processObj['memoryUsage']

      if (memoryUsageFn && typeof memoryUsageFn === 'function') {
        const memUsage = memoryUsageFn()
        const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100
        checks['memory'] = {
          status: memUsagePercent > 90 ? 'unhealthy' : memUsagePercent > 70 ? 'degraded' : 'healthy',
          usage: `${memUsagePercent.toFixed(2)}%`
        }
      } else {
        checks['memory'] = { status: 'unknown', error: 'Memory usage check not available in this environment' }
      }
    } catch (error) {
      checks['memory'] = { status: 'unknown', error: 'Memory usage check failed' }
    }

    // 确定整体状态
    const hasUnhealthy = Object.values(checks).some((check: any) => check.status === 'unhealthy')
    const hasDegraded = Object.values(checks).some((check: any) => check.status === 'degraded')
    
    const status = hasUnhealthy ? 'unhealthy' : hasDegraded ? 'degraded' : 'healthy'

    return { status, checks }
  }

  /**
   * 获取指标摘要
   */
  getMetricsSummary(timeRange: number = 3600000): any { // 默认1小时
    const now = Date.now()
    const cutoff = now - timeRange

    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff)
    const recentLogs = this.logs.filter(l => l.timestamp > cutoff)
    const recentPerformances = this.performances.filter(p => p.timestamp > cutoff)

    return {
      metrics: {
        total: recentMetrics.length,
        byName: this.groupBy(recentMetrics, 'name')
      },
      logs: {
        total: recentLogs.length,
        byLevel: this.groupBy(recentLogs, 'level')
      },
      performance: {
        total: recentPerformances.length,
        avgDuration: recentPerformances.reduce((sum, p) => sum + p.duration, 0) / recentPerformances.length || 0,
        successRate: (recentPerformances.filter(p => p.success).length / recentPerformances.length) * 100 || 0
      }
    }
  }

  private getLogEmoji(level: string): string {
    const emojis = {
      debug: '🐛',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌',
      fatal: '💀'
    }
    return emojis[level as keyof typeof emojis] || 'ℹ️'
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((groups, item) => {
      const group = String(item[key])
      groups[group] = (groups[group] || 0) + 1
      return groups
    }, {} as Record<string, number>)
  }

  private async sendToMonitoringSystem(type: string, data: any) {
    // 实现发送到监控系统的逻辑
    // 例如：Prometheus、DataDog、CloudWatch
  }

  private async sendToLoggingSystem(logEntry: LogEntry) {
    // 实现发送到日志系统的逻辑
    // 例如：ELK Stack、Splunk、Fluentd
  }

  private async sendToAPMSystem(perfMetric: PerformanceMetric) {
    // 实现发送到APM系统的逻辑
    // 例如：New Relic、Datadog APM、Elastic APM
  }
}

// 单例实例
export const monitoring = new MonitoringService()

// 便捷方法
export const recordMetric = (metric: Omit<MetricData, 'timestamp'>) => monitoring.recordMetric(metric)
export const log = (entry: Omit<LogEntry, 'timestamp'>) => monitoring.log(entry)
export const startTimer = (operation: string, metadata?: Record<string, any>) => monitoring.startTimer(operation, metadata)

// 装饰器：自动性能监控
export function withPerformanceMonitoring(operation: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const timer = startTimer(`${target.constructor.name}.${operation}`, {
        method: propertyName,
        args: args.length
      })

      try {
        const result = await method.apply(this, args)
        timer.end(true)
        return result
      } catch (error) {
        timer.end(false, (error as Error).message)
        throw error
      }
    }

    return descriptor
  }
}

export default monitoring
