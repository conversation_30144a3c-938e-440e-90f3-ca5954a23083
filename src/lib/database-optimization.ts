/**
 * 数据库查询优化工具库
 * 提供高性能的查询构建和执行方法
 */

import { executeQuery, buildPaginationSQL, buildCountSQL } from './database'
import { cacheManager, CACHE_CONFIGS } from './cache-manager'
import { log } from './logger'
import { monitoring } from './monitoring'

/**
 * 优化的查询参数接口
 */
export interface OptimizedQueryParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
  filters?: Record<string, any>
  useCache?: boolean
  cacheKey?: string
  cacheTTL?: number
}

/**
 * 查询结果接口
 */
export interface QueryResult<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * 优化的医疗案例查询
 */
export async function getOptimizedMedicalCases(params: OptimizedQueryParams = {}): Promise<QueryResult<any>> {
  const timer = monitoring.startTimer('db_query_medical_cases')
  
  try {
    const {
      page = 1,
      pageSize = 10,
      sortBy = 'CREATED_AT',
      sortOrder = 'DESC',
      filters = {},
      useCache = true,
      cacheTTL = 300000 // 5分钟
    } = params

    // 生成缓存键
    const cacheKey = `medical_cases:${JSON.stringify({ page, pageSize, sortBy, sortOrder, filters })}`
    
    // 尝试从缓存获取
    if (useCache) {
      const cached = await cacheManager.get<QueryResult<any>>(cacheKey)
      if (cached) {
        timer.end(true)
        return cached
      }
    }

    // 构建优化的查询SQL
    let baseSQL = `
      SELECT 
        mc.ID,
        mc.CASE_NUMBER,
        mc.PATIENT_NAME,
        mc.PATIENT_ID_CARD,
        mc.CASE_TYPE,
        mc.MEDICAL_CATEGORY,
        mc.HOSPITAL_NAME,
        mc.HOSPITAL_CODE,
        mc.TOTAL_COST,
        mc.ADMISSION_DATE,
        mc.DISCHARGE_DATE,
        mc.CREATED_AT
      FROM MEDICAL_CASE mc
      WHERE mc.IS_DELETED = 0
    `

    const binds: Record<string, any> = {}

    // 添加筛选条件
    if (filters['caseType']) {
      baseSQL += ` AND mc.CASE_TYPE = :caseType`
      binds['caseType'] = filters['caseType']
    }

    if (filters['medicalCategory']) {
      baseSQL += ` AND mc.MEDICAL_CATEGORY = :medicalCategory`
      binds['medicalCategory'] = filters['medicalCategory']
    }

    if (filters['hospitalCode']) {
      baseSQL += ` AND mc.HOSPITAL_CODE = :hospitalCode`
      binds['hospitalCode'] = filters['hospitalCode']
    }

    if (filters['patientName']) {
      baseSQL += ` AND UPPER(mc.PATIENT_NAME) LIKE UPPER(:patientName)`
      binds['patientName'] = `%${filters['patientName']}%`
    }

    if (filters['patientIdCard']) {
      baseSQL += ` AND mc.PATIENT_ID_CARD = :patientIdCard`
      binds['patientIdCard'] = filters['patientIdCard']
    }

    if (filters['startDate']) {
      baseSQL += ` AND mc.CREATED_AT >= :startDate`
      binds['startDate'] = new Date(filters['startDate'])
    }

    if (filters['endDate']) {
      baseSQL += ` AND mc.CREATED_AT <= :endDate`
      binds['endDate'] = new Date(filters['endDate'])
    }

    if (filters['minCost']) {
      baseSQL += ` AND mc.TOTAL_COST >= :minCost`
      binds['minCost'] = filters['minCost']
    }

    if (filters['maxCost']) {
      baseSQL += ` AND mc.TOTAL_COST <= :maxCost`
      binds['maxCost'] = filters['maxCost']
    }

    // 获取总数
    const countSQL = buildCountSQL(baseSQL)
    const countResult = await executeQuery(countSQL, binds)
    const total = countResult.rows?.[0]?.TOTAL || 0

    // 构建分页查询
    const paginatedSQL = buildPaginationSQL(baseSQL, page, pageSize, `${sortBy} ${sortOrder}`)
    const dataResult = await executeQuery(paginatedSQL, binds)

    const result: QueryResult<any> = {
      data: dataResult.rows || [],
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      hasNext: page < Math.ceil(total / pageSize),
      hasPrev: page > 1
    }

    // 缓存结果
    if (useCache) {
      await cacheManager.set(cacheKey, result, { ttl: cacheTTL })
    }

    timer.end(true)
    return result

  } catch (error) {
    timer.end(false, (error as Error).message)
    log.error('Optimized medical cases query failed', { params, error: (error as Error).message })
    throw error
  }
}

/**
 * 优化的规则执行日志查询
 */
export async function getOptimizedRuleExecutionLogs(params: OptimizedQueryParams = {}): Promise<QueryResult<any>> {
  const timer = monitoring.startTimer('db_query_rule_execution_logs')
  
  try {
    const {
      page = 1,
      pageSize = 10,
      sortBy = 'STARTED_AT',
      sortOrder = 'DESC',
      filters = {},
      useCache = true,
      cacheTTL = 180000 // 3分钟
    } = params

    const cacheKey = `rule_execution_logs:${JSON.stringify({ page, pageSize, sortBy, sortOrder, filters })}`
    
    if (useCache) {
      const cached = await cacheManager.get<QueryResult<any>>(cacheKey)
      if (cached) {
        timer.end(true)
        return cached
      }
    }

    let baseSQL = `
      SELECT 
        rel.ID,
        rel.RULE_ID,
        rel.EXECUTION_ID,
        rel.EXECUTION_STATUS,
        rel.STARTED_AT,
        rel.ENDED_AT,
        rel.EXECUTION_DURATION,
        rel.PROCESSED_RECORD_COUNT,
        rel.MATCHED_RECORD_COUNT,
        rel.ERROR_MESSAGE,
        rs.RULE_NAME,
        rs.RULE_TYPE,
        rs.RULE_CATEGORY
      FROM RULE_EXECUTION_LOG rel
      LEFT JOIN RULE_SUPERVISION rs ON rel.RULE_ID = rs.ID
      WHERE 1=1
    `

    const binds: Record<string, any> = {}

    // 添加筛选条件
    if (filters['ruleId']) {
      baseSQL += ` AND rel.RULE_ID = :ruleId`
      binds['ruleId'] = filters['ruleId']
    }

    if (filters['executionStatus']) {
      baseSQL += ` AND rel.EXECUTION_STATUS = :executionStatus`
      binds['executionStatus'] = filters['executionStatus']
    }

    if (filters['ruleType']) {
      baseSQL += ` AND rs.RULE_TYPE = :ruleType`
      binds['ruleType'] = filters['ruleType']
    }

    if (filters['startDate']) {
      baseSQL += ` AND rel.STARTED_AT >= :startDate`
      binds['startDate'] = new Date(filters['startDate'])
    }

    if (filters['endDate']) {
      baseSQL += ` AND rel.STARTED_AT <= :endDate`
      binds['endDate'] = new Date(filters['endDate'])
    }

    if (filters['executedBy']) {
      baseSQL += ` AND rel.EXECUTED_BY = :executedBy`
      binds['executedBy'] = filters['executedBy']
    }

    // 获取总数和分页数据
    const countSQL = buildCountSQL(baseSQL)
    const countResult = await executeQuery(countSQL, binds)
    const total = countResult.rows?.[0]?.TOTAL || 0

    const paginatedSQL = buildPaginationSQL(baseSQL, page, pageSize, `${sortBy} ${sortOrder}`)
    const dataResult = await executeQuery(paginatedSQL, binds)

    const result: QueryResult<any> = {
      data: dataResult.rows || [],
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      hasNext: page < Math.ceil(total / pageSize),
      hasPrev: page > 1
    }

    if (useCache) {
      await cacheManager.set(cacheKey, result, { ttl: cacheTTL })
    }

    timer.end(true)
    return result

  } catch (error) {
    timer.end(false, (error as Error).message)
    log.error('Optimized rule execution logs query failed', { params, error: (error as Error).message })
    throw error
  }
}

/**
 * 优化的统计查询
 */
export async function getOptimizedStatistics(type: string, params: Record<string, any> = {}): Promise<any> {
  const timer = monitoring.startTimer('db_query_statistics')
  
  try {
    const cacheKey = `statistics:${type}:${JSON.stringify(params)}`
    
    // 尝试从缓存获取
    const cached = await cacheManager.get(cacheKey)
    if (cached) {
      timer.end(true)
      return cached
    }

    let sql = ''
    const binds: Record<string, any> = {}

    switch (type) {
      case 'medical_cases_overview':
        sql = `
          SELECT 
            COUNT(*) as total_cases,
            COUNT(CASE WHEN CASE_TYPE = 'INPATIENT' THEN 1 END) as inpatient_cases,
            COUNT(CASE WHEN CASE_TYPE = 'OUTPATIENT' THEN 1 END) as outpatient_cases,
            ROUND(AVG(TOTAL_COST), 2) as avg_cost,
            SUM(TOTAL_COST) as total_cost,
            COUNT(DISTINCT HOSPITAL_CODE) as hospital_count,
            COUNT(DISTINCT PATIENT_ID_CARD) as patient_count
          FROM MEDICAL_CASE 
          WHERE IS_DELETED = 0
            AND CREATED_AT >= SYSDATE - 30
        `
        break

      case 'rule_execution_overview':
        sql = `
          SELECT 
            COUNT(*) as total_executions,
            COUNT(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 END) as success_count,
            COUNT(CASE WHEN EXECUTION_STATUS = 'FAILED' THEN 1 END) as failed_count,
            ROUND(AVG(EXECUTION_DURATION), 2) as avg_duration,
            SUM(PROCESSED_RECORD_COUNT) as total_processed,
            SUM(MATCHED_RECORD_COUNT) as total_matched
          FROM RULE_EXECUTION_LOG 
          WHERE STARTED_AT >= SYSDATE - 7
        `
        break

      case 'system_activity_overview':
        sql = `
          SELECT 
            COUNT(*) as total_operations,
            COUNT(CASE WHEN IS_SUCCESS = 1 THEN 1 END) as success_operations,
            COUNT(CASE WHEN IS_SUCCESS = 0 THEN 1 END) as failed_operations,
            COUNT(DISTINCT USER_ID) as active_users,
            ROUND(AVG(EXECUTION_TIME), 2) as avg_response_time
          FROM SYSTEM_OPERATION_LOG 
          WHERE CREATED_AT >= SYSDATE - 1
        `
        break

      default:
        throw new Error(`Unknown statistics type: ${type}`)
    }

    const result = await executeQuery(sql, binds)
    const data = result.rows?.[0] || {}

    // 缓存结果（统计数据缓存时间较长）
    await cacheManager.set(cacheKey, data, CACHE_CONFIGS.MEDIUM)

    timer.end(true)
    return data

  } catch (error) {
    timer.end(false, (error as Error).message)
    log.error('Optimized statistics query failed', { type, params, error: (error as Error).message })
    throw error
  }
}

/**
 * 批量查询优化
 */
export async function executeBatchQueries<T>(queries: Array<{
  sql: string
  binds?: Record<string, any>
  cacheKey?: string
  cacheTTL?: number
}>): Promise<T[]> {
  const timer = monitoring.startTimer('db_batch_queries')
  
  try {
    const results = await Promise.allSettled(
      queries.map(async (query) => {
        // 尝试从缓存获取
        if (query.cacheKey) {
          const cached = await cacheManager.get<T>(query.cacheKey)
          if (cached) return cached
        }

        // 执行查询
        const result = await executeQuery(query.sql, query.binds || {})
        const data = result.rows as T

        // 缓存结果
        if (query.cacheKey && query.cacheTTL) {
          await cacheManager.set(query.cacheKey, data, { ttl: query.cacheTTL })
        }

        return data
      })
    )

    timer.end(true)
    return results.map(result => 
      result.status === 'fulfilled' ? result.value : null
    ).filter(Boolean) as T[]

  } catch (error) {
    timer.end(false, (error as Error).message)
    throw error
  }
}

/**
 * 查询性能分析
 */
export async function analyzeQueryPerformance(sql: string, binds: Record<string, any> = {}): Promise<{
  executionTime: number
  rowsProcessed: number
  planHash: string
}> {
  const startTime = Date.now()
  
  try {
    // 执行查询
    const result = await executeQuery(sql, binds)
    const executionTime = Date.now() - startTime
    
    // 获取执行计划信息
    const planResult = await executeQuery(`
      SELECT plan_hash_value, rows_processed 
      FROM v$sql 
      WHERE sql_text LIKE :sqlPattern 
      AND rownum = 1
    `, { sqlPattern: `${sql.substring(0, 50)}%` })

    return {
      executionTime,
      rowsProcessed: result.rows?.length || 0,
      planHash: planResult.rows?.[0]?.PLAN_HASH_VALUE || 'unknown'
    }

  } catch (error) {
    log.error('Query performance analysis failed', { sql, error: (error as Error).message })
    throw error
  }
}
