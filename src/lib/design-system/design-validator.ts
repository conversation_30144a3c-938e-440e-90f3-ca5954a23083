/**
 * 设计系统验证工具
 * 实现P1-001 shadcn/UI组件标准化目标：
 * - 检查组件使用一致性
 * - 验证设计token使用
 * - 计算设计一致性评分
 */

import { DESIGN_CONSISTENCY_RULES, DESIGN_TOKENS } from './design-tokens'

export interface DesignValidationResult {
  score: number
  issues: DesignIssue[]
  summary: {
    totalChecks: number
    passedChecks: number
    failedChecks: number
    componentUsage: ComponentUsageStats
    tokenUsage: TokenUsageStats
  }
}

export interface DesignIssue {
  type: 'error' | 'warning' | 'info'
  category: 'component' | 'token' | 'style' | 'accessibility'
  message: string
  file: string
  line?: number
  suggestion?: string
  severity: 'high' | 'medium' | 'low'
}

export interface ComponentUsageStats {
  shadcnComponents: number
  customComponents: number
  totalComponents: number
  complianceRate: number
}

export interface TokenUsageStats {
  designTokens: number
  hardcodedValues: number
  totalValues: number
  complianceRate: number
}

/**
 * 设计系统验证器
 */
export class DesignSystemValidator {
  private issues: DesignIssue[] = []
  private componentStats: ComponentUsageStats = {
    shadcnComponents: 0,
    customComponents: 0,
    totalComponents: 0,
    complianceRate: 0
  }
  private tokenStats: TokenUsageStats = {
    designTokens: 0,
    hardcodedValues: 0,
    totalValues: 0,
    complianceRate: 0
  }

  /**
   * 验证文件的设计系统合规性
   */
  async validateFile(filePath: string, content: string): Promise<DesignValidationResult> {
    this.issues = []
    this.resetStats()

    // 1. 检查组件使用
    this.validateComponentUsage(filePath, content)

    // 2. 检查设计token使用
    this.validateTokenUsage(filePath, content)

    // 3. 检查自定义样式
    this.validateCustomStyles(filePath, content)

    // 4. 检查可访问性
    this.validateAccessibility(filePath, content)

    // 计算总体评分
    const score = this.calculateScore()

    return {
      score,
      issues: this.issues,
      summary: {
        totalChecks: this.getTotalChecks(),
        passedChecks: this.getPassedChecks(),
        failedChecks: this.getFailedChecks(),
        componentUsage: this.componentStats,
        tokenUsage: this.tokenStats
      }
    }
  }

  /**
   * 验证组件使用
   */
  private validateComponentUsage(filePath: string, content: string): void {
    const requiredComponents = DESIGN_CONSISTENCY_RULES.requiredComponents
    
    // 检查shadcn/UI组件导入
    const importMatches = content.match(/import\s+{[^}]+}\s+from\s+['"]@\/components\/ui\/[^'"]+['"]/g) || []
    
    importMatches.forEach(importStatement => {
      const components = this.extractComponentsFromImport(importStatement)
      components.forEach(component => {
        if (requiredComponents.includes(component as any)) {
          this.componentStats.shadcnComponents++
        }
      })
    })

    // 检查自定义组件使用
    const customComponentMatches = content.match(/<[A-Z][a-zA-Z0-9]*[^>]*>/g) || []
    customComponentMatches.forEach(match => {
      const componentName = match.match(/<([A-Z][a-zA-Z0-9]*)/)?.[1]
      if (componentName && !requiredComponents.includes(componentName as any)) {
        this.componentStats.customComponents++
        this.addIssue({
          type: 'warning',
          category: 'component',
          message: `使用了自定义组件 ${componentName}，建议使用shadcn/UI标准组件`,
          file: filePath,
          suggestion: `考虑使用shadcn/UI提供的标准组件替代 ${componentName}`,
          severity: 'medium'
        })
      }
    })

    this.componentStats.totalComponents = this.componentStats.shadcnComponents + this.componentStats.customComponents
    this.componentStats.complianceRate = this.componentStats.totalComponents > 0 
      ? (this.componentStats.shadcnComponents / this.componentStats.totalComponents) * 100 
      : 100
  }

  /**
   * 验证设计token使用
   */
  private validateTokenUsage(filePath: string, content: string): void {
    const requiredTokens = DESIGN_CONSISTENCY_RULES.requiredTokens
    
    // 检查CSS变量使用
    const cssVarMatches = content.match(/var\(--[^)]+\)/g) || []
    cssVarMatches.forEach(match => {
      const isRequiredToken = requiredTokens.some(token => match.includes(token.replace('var(', '').replace(')', '')))
      if (isRequiredToken) {
        this.tokenStats.designTokens++
      }
    })

    // 检查硬编码颜色值
    const hardcodedColors = content.match(/#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)|hsl\([^)]+\)|hsla\([^)]+\)/g) || []
    hardcodedColors.forEach(color => {
      this.tokenStats.hardcodedValues++
      this.addIssue({
        type: 'error',
        category: 'token',
        message: `发现硬编码颜色值: ${color}`,
        file: filePath,
        suggestion: '使用设计token替代硬编码颜色值，如 hsl(var(--primary))',
        severity: 'high'
      })
    })

    // 检查硬编码间距值
    const hardcodedSpacing = content.match(/(?:margin|padding|gap|space)-\d+/g) || []
    hardcodedSpacing.forEach(spacing => {
      if (!spacing.match(/-(xs|sm|md|lg|xl|\d+xl)$/)) {
        this.tokenStats.hardcodedValues++
        this.addIssue({
          type: 'warning',
          category: 'token',
          message: `发现硬编码间距值: ${spacing}`,
          file: filePath,
          suggestion: '使用标准间距token，如 space-md, space-lg',
          severity: 'medium'
        })
      }
    })

    this.tokenStats.totalValues = this.tokenStats.designTokens + this.tokenStats.hardcodedValues
    this.tokenStats.complianceRate = this.tokenStats.totalValues > 0 
      ? (this.tokenStats.designTokens / this.tokenStats.totalValues) * 100 
      : 100
  }

  /**
   * 验证自定义样式
   */
  private validateCustomStyles(filePath: string, content: string): void {
    const forbiddenStyles = DESIGN_CONSISTENCY_RULES.forbiddenStyles
    
    // 检查内联样式
    const inlineStyles = content.match(/style\s*=\s*{[^}]+}/g) || []
    if (inlineStyles.length > 0) {
      this.addIssue({
        type: 'error',
        category: 'style',
        message: `发现 ${inlineStyles.length} 个内联样式`,
        file: filePath,
        suggestion: '使用Tailwind CSS类或shadcn/UI组件样式替代内联样式',
        severity: 'high'
      })
    }

    // 检查自定义CSS类
    const customClasses = content.match(/className\s*=\s*["'][^"']*custom-[^"']*["']/g) || []
    customClasses.forEach(customClass => {
      this.addIssue({
        type: 'warning',
        category: 'style',
        message: `发现自定义CSS类: ${customClass}`,
        file: filePath,
        suggestion: '使用shadcn/UI标准样式或Tailwind CSS类',
        severity: 'medium'
      })
    })
  }

  /**
   * 验证可访问性
   */
  private validateAccessibility(filePath: string, content: string): void {
    // 检查按钮是否有适当的标签
    const buttons = content.match(/<Button[^>]*>[^<]*<\/Button>/g) || []
    buttons.forEach(button => {
      if (!button.includes('aria-label') && !button.match(/>[\s\S]*\w+[\s\S]*</)) {
        this.addIssue({
          type: 'warning',
          category: 'accessibility',
          message: '按钮缺少可访问性标签',
          file: filePath,
          suggestion: '为按钮添加 aria-label 或确保按钮有可见文本',
          severity: 'medium'
        })
      }
    })

    // 检查图片是否有alt属性
    const images = content.match(/<img[^>]*>/g) || []
    images.forEach(img => {
      if (!img.includes('alt=')) {
        this.addIssue({
          type: 'error',
          category: 'accessibility',
          message: '图片缺少alt属性',
          file: filePath,
          suggestion: '为所有图片添加描述性的alt属性',
          severity: 'high'
        })
      }
    })
  }

  /**
   * 从导入语句中提取组件名称
   */
  private extractComponentsFromImport(importStatement: string): string[] {
    const match = importStatement.match(/{\s*([^}]+)\s*}/)
    if (!match) return []
    
    return (match[1] || '')
      .split(',')
      .map(component => component.trim())
      .filter(component => component.length > 0)
  }

  /**
   * 添加问题
   */
  private addIssue(issue: DesignIssue): void {
    this.issues.push(issue)
  }

  /**
   * 重置统计数据
   */
  private resetStats(): void {
    this.componentStats = {
      shadcnComponents: 0,
      customComponents: 0,
      totalComponents: 0,
      complianceRate: 0
    }
    this.tokenStats = {
      designTokens: 0,
      hardcodedValues: 0,
      totalValues: 0,
      complianceRate: 0
    }
  }

  /**
   * 计算设计一致性评分
   */
  private calculateScore(): number {
    const componentScore = this.componentStats.complianceRate
    const tokenScore = this.tokenStats.complianceRate
    const issueScore = Math.max(0, 100 - (this.getHighSeverityIssues() * 10) - (this.getMediumSeverityIssues() * 5))
    
    return Math.round((componentScore * 0.4 + tokenScore * 0.4 + issueScore * 0.2))
  }

  /**
   * 获取总检查数
   */
  private getTotalChecks(): number {
    return this.componentStats.totalComponents + this.tokenStats.totalValues + this.issues.length
  }

  /**
   * 获取通过检查数
   */
  private getPassedChecks(): number {
    return this.componentStats.shadcnComponents + this.tokenStats.designTokens
  }

  /**
   * 获取失败检查数
   */
  private getFailedChecks(): number {
    return this.getTotalChecks() - this.getPassedChecks()
  }

  /**
   * 获取高严重性问题数量
   */
  private getHighSeverityIssues(): number {
    return this.issues.filter(issue => issue.severity === 'high').length
  }

  /**
   * 获取中等严重性问题数量
   */
  private getMediumSeverityIssues(): number {
    return this.issues.filter(issue => issue.severity === 'medium').length
  }
}

/**
 * 创建设计系统验证器实例
 */
export function createDesignValidator(): DesignSystemValidator {
  return new DesignSystemValidator()
}

/**
 * 快速验证单个文件
 */
export async function validateDesignCompliance(filePath: string, content: string): Promise<DesignValidationResult> {
  const validator = createDesignValidator()
  return await validator.validateFile(filePath, content)
}
