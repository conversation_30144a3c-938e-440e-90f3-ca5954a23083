/**
 * 设计系统标准化配置
 * 实现P1-001 shadcn/UI组件标准化目标：
 * - 统一设计token
 * - 标准化组件使用
 * - 确保设计一致性
 */

export const DESIGN_TOKENS = {
  // 颜色系统 - 基于shadcn/UI标准
  colors: {
    // 主色调
    primary: {
      DEFAULT: 'hsl(var(--primary))',
      foreground: 'hsl(var(--primary-foreground))',
      50: 'hsl(var(--primary) / 0.05)',
      100: 'hsl(var(--primary) / 0.1)',
      200: 'hsl(var(--primary) / 0.2)',
      500: 'hsl(var(--primary) / 0.5)',
      900: 'hsl(var(--primary) / 0.9)'
    },
    
    // 辅助色调
    secondary: {
      DEFAULT: 'hsl(var(--secondary))',
      foreground: 'hsl(var(--secondary-foreground))',
      50: 'hsl(var(--secondary) / 0.05)',
      100: 'hsl(var(--secondary) / 0.1)',
      500: 'hsl(var(--secondary) / 0.5)'
    },
    
    // 危险色调
    destructive: {
      DEFAULT: 'hsl(var(--destructive))',
      foreground: 'hsl(var(--destructive-foreground))',
      50: 'hsl(var(--destructive) / 0.05)',
      100: 'hsl(var(--destructive) / 0.1)',
      500: 'hsl(var(--destructive) / 0.5)'
    },
    
    // 静音色调
    muted: {
      DEFAULT: 'hsl(var(--muted))',
      foreground: 'hsl(var(--muted-foreground))',
      50: 'hsl(var(--muted) / 0.05)',
      100: 'hsl(var(--muted) / 0.1)'
    },
    
    // 强调色调
    accent: {
      DEFAULT: 'hsl(var(--accent))',
      foreground: 'hsl(var(--accent-foreground))'
    },
    
    // 边框和输入
    border: 'hsl(var(--border))',
    input: 'hsl(var(--input))',
    ring: 'hsl(var(--ring))',
    
    // 背景和前景
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    
    // 卡片和弹出层
    card: {
      DEFAULT: 'hsl(var(--card))',
      foreground: 'hsl(var(--card-foreground))'
    },
    popover: {
      DEFAULT: 'hsl(var(--popover))',
      foreground: 'hsl(var(--popover-foreground))'
    }
  },

  // 间距系统 - 基于8px网格
  spacing: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '2.5rem',  // 40px
    '3xl': '3rem',    // 48px
    '4xl': '4rem',    // 64px
    '5xl': '5rem',    // 80px
    '6xl': '6rem'     // 96px
  },

  // 字体系统
  typography: {
    // 显示级别
    display: {
      large: {
        fontSize: '2.25rem',    // 36px
        lineHeight: '2.5rem',   // 40px
        fontWeight: '700',
        letterSpacing: '-0.025em'
      },
      medium: {
        fontSize: '1.875rem',   // 30px
        lineHeight: '2.25rem',  // 36px
        fontWeight: '700',
        letterSpacing: '-0.025em'
      },
      small: {
        fontSize: '1.5rem',     // 24px
        lineHeight: '2rem',     // 32px
        fontWeight: '700',
        letterSpacing: '-0.025em'
      }
    },
    
    // 标题级别
    headline: {
      large: {
        fontSize: '1.25rem',    // 20px
        lineHeight: '1.75rem',  // 28px
        fontWeight: '600',
        letterSpacing: '0'
      },
      medium: {
        fontSize: '1.125rem',   // 18px
        lineHeight: '1.75rem',  // 28px
        fontWeight: '600',
        letterSpacing: '0'
      },
      small: {
        fontSize: '1rem',       // 16px
        lineHeight: '1.5rem',   // 24px
        fontWeight: '600',
        letterSpacing: '0'
      }
    },
    
    // 正文级别
    body: {
      large: {
        fontSize: '1rem',       // 16px
        lineHeight: '1.5rem',   // 24px
        fontWeight: '400',
        letterSpacing: '0'
      },
      medium: {
        fontSize: '0.875rem',   // 14px
        lineHeight: '1.25rem',  // 20px
        fontWeight: '400',
        letterSpacing: '0'
      },
      small: {
        fontSize: '0.75rem',    // 12px
        lineHeight: '1rem',     // 16px
        fontWeight: '400',
        letterSpacing: '0'
      }
    },
    
    // 标签级别
    label: {
      large: {
        fontSize: '0.875rem',   // 14px
        lineHeight: '1.25rem',  // 20px
        fontWeight: '500',
        letterSpacing: '0'
      },
      medium: {
        fontSize: '0.75rem',    // 12px
        lineHeight: '1rem',     // 16px
        fontWeight: '500',
        letterSpacing: '0'
      },
      small: {
        fontSize: '0.6875rem',  // 11px
        lineHeight: '1rem',     // 16px
        fontWeight: '500',
        letterSpacing: '0.025em'
      }
    }
  },

  // 圆角系统
  borderRadius: {
    none: '0',
    sm: 'calc(var(--radius) - 4px)',
    md: 'calc(var(--radius) - 2px)',
    lg: 'var(--radius)',
    xl: 'calc(var(--radius) + 4px)',
    full: '9999px'
  },

  // 阴影系统
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)'
  },

  // 动画系统
  animations: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms'
    },
    easing: {
      linear: 'linear',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
} as const

// 组件变体标准化
export const COMPONENT_VARIANTS = {
  // 按钮变体
  button: {
    variant: {
      default: 'bg-primary text-primary-foreground hover:bg-primary/90',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'text-primary underline-offset-4 hover:underline'
    },
    size: {
      default: 'h-10 px-4 py-2',
      sm: 'h-9 rounded-md px-3',
      lg: 'h-11 rounded-md px-8',
      icon: 'h-10 w-10'
    }
  },

  // 徽章变体
  badge: {
    variant: {
      default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
      secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
      destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
      outline: 'text-foreground'
    }
  },

  // 输入框变体
  input: {
    variant: {
      default: 'border-input bg-background',
      destructive: 'border-destructive bg-background',
      ghost: 'border-transparent bg-transparent'
    },
    size: {
      default: 'h-10 px-3 py-2',
      sm: 'h-9 px-3 py-2',
      lg: 'h-11 px-3 py-2'
    }
  }
} as const

// 医保平台专用设计规范
export const MEDICAL_DESIGN_PATTERNS = {
  // 状态颜色映射
  statusColors: {
    success: 'text-green-600 bg-green-50 border-green-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    error: 'text-red-600 bg-red-50 border-red-200',
    info: 'text-blue-600 bg-blue-50 border-blue-200',
    pending: 'text-gray-600 bg-gray-50 border-gray-200'
  },

  // 优先级颜色
  priorityColors: {
    high: 'text-red-600 bg-red-50',
    medium: 'text-yellow-600 bg-yellow-50',
    low: 'text-green-600 bg-green-50'
  },

  // 案例类型颜色
  caseTypeColors: {
    inpatient: 'text-blue-600 bg-blue-50',
    outpatient: 'text-green-600 bg-green-50',
    emergency: 'text-red-600 bg-red-50',
    chronic: 'text-purple-600 bg-purple-50'
  }
} as const

// 设计一致性检查规则
export const DESIGN_CONSISTENCY_RULES = {
  // 必须使用的shadcn/UI组件
  requiredComponents: [
    'Button',
    'Card',
    'Badge',
    'Input',
    'Select',
    'Dialog',
    'Sheet',
    'Table',
    'Form',
    'Tabs',
    'Alert',
    'Progress',
    'Switch',
    'Checkbox',
    'RadioGroup'
  ],

  // 禁止的自定义样式
  forbiddenStyles: [
    'button',
    'card',
    'input',
    'inline-styles',
    'hardcoded-colors',
    'hardcoded-spacing'
  ],

  // 必须使用的设计token
  requiredTokens: [
    'var(--primary)',
    'var(--secondary)',
    'var(--destructive)',
    'var(--muted)',
    'var(--accent)',
    'var(--border)',
    'var(--background)',
    'var(--foreground)'
  ]
} as const
