/**
 * API 工具函数 - 减少重复代码
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken, hasRole, extractTokenFromHeader } from '@/lib/jwt'
import { ApiResponse, UserRole } from '@/types/auth'

/**
 * 统一的错误响应格式
 */
export function createErrorResponse(
  message: string, 
  status: number = 500,
  error?: any
): NextResponse<ApiResponse> {
  console.error(`❌ API错误 (${status}):`, message, error)
  
  return NextResponse.json({
    success: false,
    message,
  } as ApiResponse, { status })
}

/**
 * 统一的成功响应格式
 */
export function createSuccessResponse<T>(
  data?: T,
  message: string = '操作成功',
  status: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    message,
    data,
  } as ApiResponse<T>, { status })
}

/**
 * 验证必填字段
 */
export function validateRequiredFields(
  body: Record<string, any>,
  requiredFields: string[]
): string | null {
  for (const field of requiredFields) {
    if (body[field] === undefined || body[field] === null || body[field] === '') {
      return `缺少必填字段: ${field}`
    }
  }
  return null
}

/**
 * 验证数值范围
 */
export function validateNumberRange(
  value: number,
  min: number,
  max: number,
  fieldName: string
): string | null {
  if (value < min || value > max) {
    return `${fieldName}必须在${min}-${max}之间`
  }
  return null
}

/**
 * 验证枚举值
 */
export function validateEnum(
  value: string,
  allowedValues: string[],
  fieldName: string
): string | null {
  if (!allowedValues.includes(value)) {
    return `${fieldName}必须是以下值之一: ${allowedValues.join(', ')}`
  }
  return null
}

/**
 * 统一的身份验证和权限检查
 */
export async function authenticateAndAuthorize(
  request: NextRequest,
  requiredRoles?: UserRole[]
): Promise<{ success: true; payload: any } | { success: false; response: NextResponse }> {
  try {
    // 提取和验证token
    const authHeader = request.headers.get('authorization')
    const token = extractTokenFromHeader(authHeader)
    if (!token) {
      return {
        success: false,
        response: createErrorResponse('未提供访问令牌', 401)
      }
    }

    const payload = verifyAccessToken(token)
    if (!payload) {
      return {
        success: false,
        response: createErrorResponse('访问令牌无效或已过期', 401)
      }
    }

    // 检查角色权限
    if (requiredRoles && requiredRoles.length > 0) {
      const hasRequiredRole = requiredRoles.some(role => hasRole(payload.roles, role))
      if (!hasRequiredRole) {
        return {
          success: false,
          response: createErrorResponse('权限不足', 403)
        }
      }
    }

    return { success: true, payload }
  } catch (error) {
    return {
      success: false,
      response: createErrorResponse('身份验证失败', 401, error)
    }
  }
}

/**
 * 统一的请求体验证
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  requiredFields: string[],
  validators?: Array<(body: T) => string | null>
): Promise<{ success: true; body: T } | { success: false; response: NextResponse }> {
  try {
    const body = await request.json() as T

    // 验证必填字段
    const fieldError = validateRequiredFields(body as any, requiredFields)
    if (fieldError) {
      return {
        success: false,
        response: createErrorResponse(fieldError, 400)
      }
    }

    // 执行自定义验证器
    if (validators) {
      for (const validator of validators) {
        const error = validator(body)
        if (error) {
          return {
            success: false,
            response: createErrorResponse(error, 400)
          }
        }
      }
    }

    return { success: true, body }
  } catch (error) {
    return {
      success: false,
      response: createErrorResponse('请求体格式错误', 400, error)
    }
  }
}

/**
 * 统一的API处理器包装器
 */
export function withApiHandler<T = any>(
  handler: (request: NextRequest, context: { payload: any; body?: T }) => Promise<NextResponse>,
  options: {
    requiredRoles?: UserRole[]
    requiredFields?: string[]
    validators?: Array<(body: T) => string | null>
    requireAuth?: boolean
    requireBody?: boolean
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const context: { payload?: any; body?: T } = {}

      // 身份验证
      if (options.requireAuth !== false) {
        const authResult = await authenticateAndAuthorize(request, options.requiredRoles)
        if (!authResult.success) {
          return authResult.response
        }
        context.payload = authResult.payload
      }

      // 请求体验证
      if (options.requireBody && options.requiredFields) {
        const bodyResult = await validateRequestBody<T>(
          request,
          options.requiredFields,
          options.validators
        )
        if (!bodyResult.success) {
          return bodyResult.response
        }
        context.body = bodyResult.body
      }

      // 执行处理器
      return await handler(request, context as { payload: any; body?: T })
    } catch (error) {
      return createErrorResponse(
        error instanceof Error ? error.message : '服务器内部错误',
        500,
        error
      )
    }
  }
}

/**
 * 常用的验证器 - 使用统一的验证函数
 */
export const validators = {
  priorityLevel: (body: any) => validateNumberRange(body.priorityLevel, 1, 10, '优先级'),

  templateType: (body: any) => validateEnum(
    body.templateType,
    ['SQL', 'DSL', 'JAVASCRIPT'],
    '模板类型'
  ),

  email: (body: any) => {
    if (!body.email) return null
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(body.email) ? null : '邮箱格式不正确'
  },

  phoneNumber: (body: any) => {
    if (!body.phoneNumber) return null
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(body.phoneNumber) ? null : '手机号格式不正确'
  },

  idCard: (body: any) => {
    if (!body.idCard) return null
    // 简化的身份证验证，详细验证可使用 data-validation.ts 中的 schema
    const idCardRegex = /^\d{17}[\dXx]$/
    return idCardRegex.test(body.idCard) ? null : '身份证号格式不正确'
  }
}
