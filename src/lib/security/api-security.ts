/**
 * API安全防护
 * 实现P0-009安全机制强化目标：
 * - API安全评分 > 95分
 * - 防止常见的Web攻击
 * - 实现请求验证和限流
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/jwt'

export interface SecurityConfig {
  rateLimit: {
    windowMs: number
    maxRequests: number
    skipSuccessfulRequests?: boolean
  }
  cors: {
    allowedOrigins: string[]
    allowedMethods: string[]
    allowedHeaders: string[]
  }
  validation: {
    maxBodySize: number
    allowedContentTypes: string[]
  }
  security: {
    enableCSRF: boolean
    enableXSS: boolean
    enableSQLInjection: boolean
    enableIPWhitelist: boolean
    whitelistedIPs?: string[]
  }
}

const defaultSecurityConfig: SecurityConfig = {
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 100,
    skipSuccessfulRequests: false
  },
  cors: {
    allowedOrigins: ['http://localhost:3000', 'https://yourdomain.com'],
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  },
  validation: {
    maxBodySize: 10 * 1024 * 1024, // 10MB
    allowedContentTypes: ['application/json', 'multipart/form-data', 'application/x-www-form-urlencoded']
  },
  security: {
    enableCSRF: true,
    enableXSS: true,
    enableSQLInjection: true,
    enableIPWhitelist: false,
    whitelistedIPs: []
  }
}

/**
 * API安全中间件
 */
export class APISecurityMiddleware {
  private config: SecurityConfig
  private rateLimitStore = new Map<string, { count: number; resetTime: number }>()

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = { ...defaultSecurityConfig, ...config }
  }

  /**
   * 主要安全检查方法
   */
  async checkSecurity(request: NextRequest): Promise<NextResponse | null> {
    try {
      // 1. CORS检查
      const corsResponse = this.checkCORS(request)
      if (corsResponse) return corsResponse

      // 2. 请求验证
      await this.validateRequest(request)

      // 3. 速率限制
      await this.checkRateLimit(request)

      // 4. IP白名单检查
      if (this.config.security.enableIPWhitelist) {
        this.checkIPWhitelist(request)
      }

      // 5. 安全攻击检测
      await this.detectSecurityThreats(request)

      // 6. 身份验证
      await this.authenticateRequest(request)

      return null // 通过所有安全检查

    } catch (error) {
      return this.handleSecurityError(request, error)
    }
  }

  /**
   * CORS检查
   */
  private checkCORS(request: NextRequest): NextResponse | null {
    const origin = request.headers.get('origin')
    const method = request.method

    // 预检请求处理
    if (method === 'OPTIONS') {
      if (origin && this.config.cors.allowedOrigins.includes(origin)) {
        return new NextResponse(null, {
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': origin,
            'Access-Control-Allow-Methods': this.config.cors.allowedMethods.join(', '),
            'Access-Control-Allow-Headers': this.config.cors.allowedHeaders.join(', '),
            'Access-Control-Max-Age': '86400'
          }
        })
      } else {
        return new NextResponse('CORS not allowed', { status: 403 })
      }
    }

    // 检查方法是否允许
    if (!this.config.cors.allowedMethods.includes(method)) {
      throw new Error(`Method ${method} not allowed`)
    }

    return null
  }

  /**
   * 请求验证
   */
  private async validateRequest(request: NextRequest): Promise<void> {
    // 检查Content-Type
    const contentType = request.headers.get('content-type')
    if (request.method !== 'GET' && contentType) {
      const isAllowed = this.config.validation.allowedContentTypes.some(type =>
        contentType.includes(type)
      )
      if (!isAllowed) {
        throw new Error(`Content-Type ${contentType} not allowed`)
      }
    }

    // 检查请求体大小
    if (request.method !== 'GET') {
      const contentLength = request.headers.get('content-length')
      if (contentLength && parseInt(contentLength) > this.config.validation.maxBodySize) {
        throw new Error('Request body too large')
      }
    }
  }

  /**
   * 速率限制检查
   */
  private async checkRateLimit(request: NextRequest): Promise<void> {
    const clientIP = this.getClientIP(request)
    const now = Date.now()
    const windowMs = this.config.rateLimit.windowMs
    const maxRequests = this.config.rateLimit.maxRequests

    const key = `rate_limit_${clientIP}`
    const record = this.rateLimitStore.get(key)

    if (!record || now > record.resetTime) {
      // 新的时间窗口
      this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now + windowMs
      })
    } else {
      // 在当前时间窗口内
      record.count++
      if (record.count > maxRequests) {
        console.warn('🚨 Security Alert: Rate limit exceeded', {
          ip: clientIP,
          count: record.count,
          userAgent: request.headers.get('user-agent') || 'Unknown'
        })
        throw new Error('Rate limit exceeded')
      }
    }
  }

  /**
   * IP白名单检查
   */
  private checkIPWhitelist(request: NextRequest): void {
    const clientIP = this.getClientIP(request)
    const whitelistedIPs = this.config.security.whitelistedIPs || []

    if (whitelistedIPs.length > 0 && !whitelistedIPs.includes(clientIP)) {
      throw new Error(`IP ${clientIP} not in whitelist`)
    }
  }

  /**
   * 安全威胁检测
   */
  private async detectSecurityThreats(request: NextRequest): Promise<void> {
    const url = request.url
    const userAgent = request.headers.get('user-agent') || ''
    
    // SQL注入检测
    if (this.config.security.enableSQLInjection) {
      await this.detectSQLInjection(request)
    }

    // XSS攻击检测
    if (this.config.security.enableXSS) {
      await this.detectXSS(request)
    }

    // 可疑User-Agent检测
    if (this.isSuspiciousUserAgent(userAgent)) {
      console.warn('🚨 Security Alert: Suspicious User-Agent detected', {
        ip: this.getClientIP(request),
        userAgent
      })
    }
  }

  /**
   * SQL注入检测
   */
  private async detectSQLInjection(request: NextRequest): Promise<void> {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(\'|\"|;|--|\*|\|)/,
      /(\bSCRIPT\b)/i
    ]

    const checkString = (str: string) => {
      return sqlPatterns.some(pattern => pattern.test(str))
    }

    // 检查URL参数
    const { searchParams } = new URL(request.url)
    for (const [key, value] of searchParams.entries()) {
      if (checkString(value)) {
        console.error('🚨 Security Alert: Potential SQL injection in URL parameter', {
          ip: this.getClientIP(request),
          parameter: key,
          value,
          userAgent: request.headers.get('user-agent') || 'Unknown'
        })
        throw new Error('Potential SQL injection detected')
      }
    }

    // 检查请求体
    if (request.method !== 'GET') {
      try {
        const body = await request.clone().text()
        if (checkString(body)) {
          console.error('🚨 Security Alert: Potential SQL injection in request body', {
            ip: this.getClientIP(request),
            userAgent: request.headers.get('user-agent') || 'Unknown'
          })
          throw new Error('Potential SQL injection detected')
        }
      } catch (error) {
        // 忽略解析错误
      }
    }
  }

  /**
   * XSS攻击检测
   */
  private async detectXSS(request: NextRequest): Promise<void> {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
    ]

    const checkString = (str: string) => {
      return xssPatterns.some(pattern => pattern.test(str))
    }

    // 检查URL参数
    const { searchParams } = new URL(request.url)
    for (const [key, value] of searchParams.entries()) {
      if (checkString(decodeURIComponent(value))) {
        console.error('🚨 Security Alert: Potential XSS attack in URL parameter', {
          ip: this.getClientIP(request),
          parameter: key,
          value,
          userAgent: request.headers.get('user-agent') || 'Unknown'
        })
        throw new Error('Potential XSS attack detected')
      }
    }
  }

  /**
   * 身份验证
   */
  private async authenticateRequest(request: NextRequest): Promise<void> {
    // 跳过公开API
    if (this.isPublicAPI(request.nextUrl.pathname)) {
      return
    }

    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header')
    }

    const token = authHeader.substring(7)
    const payload = verifyAccessToken(token)

    if (!payload) {
      console.warn('🚨 Security Alert: Invalid or expired token', {
        ip: this.getClientIP(request),
        userAgent: request.headers.get('user-agent') || 'Unknown'
      })
      throw new Error('Invalid or expired token')
    }
  }

  /**
   * 检查是否为可疑User-Agent
   */
  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scanner/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i
    ]

    return suspiciousPatterns.some(pattern => pattern.test(userAgent))
  }

  /**
   * 检查是否为公开API
   */
  private isPublicAPI(pathname: string): boolean {
    const publicPaths = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
      '/api/health'
    ]

    return publicPaths.some(path => pathname.startsWith(path))
  }

  /**
   * 获取客户端IP
   */
  private getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    
    if (forwarded) {
      return forwarded.split(',')[0]?.trim() || 'unknown'
    }
    
    return realIP || 'unknown'
  }

  /**
   * 处理安全错误
   */
  private handleSecurityError(request: NextRequest, error: any): NextResponse {
    const message = error instanceof Error ? error.message : 'Security check failed'
    
    return NextResponse.json({
      success: false,
      message: 'Access denied',
      error: process.env.NODE_ENV === 'development' ? message : undefined
    }, { 
      status: 403,
      headers: {
        'X-Security-Error': 'true'
      }
    })
  }
}

/**
 * 创建API安全中间件实例
 */
export function createAPISecurityMiddleware(config?: Partial<SecurityConfig>): APISecurityMiddleware {
  return new APISecurityMiddleware(config)
}

/**
 * 默认安全中间件
 */
export const defaultAPISecurityMiddleware = createAPISecurityMiddleware()
