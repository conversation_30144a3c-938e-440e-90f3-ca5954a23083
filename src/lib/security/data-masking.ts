/**
 * 数据脱敏工具
 * 实现P0-009安全机制强化目标：
 * - 敏感数据100%脱敏显示
 * - 支持多种脱敏策略
 * - 保护用户隐私数据
 */

export interface MaskingRule {
  field: string
  strategy: MaskingStrategy
  condition?: (value: any) => boolean
}

export type MaskingStrategy = 
  | 'phone'
  | 'idCard'
  | 'email'
  | 'bankCard'
  | 'name'
  | 'address'
  | 'custom'

export interface MaskingConfig {
  rules: MaskingRule[]
  customMaskers?: Record<string, (value: string) => string>
  preserveLength?: boolean
  maskChar?: string
}

/**
 * 数据脱敏器
 */
export class DataMasker {
  private config: MaskingConfig
  private defaultMaskChar = '*'

  constructor(config: MaskingConfig) {
    this.config = {
      preserveLength: true,
      maskChar: this.defaultMaskChar,
      ...config
    }
  }

  /**
   * 脱敏单个对象
   */
  mask<T>(data: T): T {
    if (!data || typeof data !== 'object') {
      return data
    }

    if (Array.isArray(data)) {
      return data.map(item => this.mask(item)) as T
    }

    const masked = { ...data } as any

    for (const rule of this.config.rules) {
      if (this.shouldApplyRule(masked, rule)) {
        const value = this.getNestedValue(masked, rule.field)
        if (value !== undefined && value !== null) {
          this.setNestedValue(masked, rule.field, this.applyMasking(value, rule.strategy))
        }
      }
    }

    return masked
  }

  /**
   * 批量脱敏
   */
  maskBatch<T>(dataArray: T[]): T[] {
    return dataArray.map(item => this.mask(item))
  }

  /**
   * 检查是否应该应用规则
   */
  private shouldApplyRule(data: any, rule: MaskingRule): boolean {
    if (!rule.condition) {
      return true
    }
    
    const value = this.getNestedValue(data, rule.field)
    return rule.condition(value)
  }

  /**
   * 获取嵌套属性值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * 设置嵌套属性值
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()!
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {}
      return current[key]
    }, obj)
    target[lastKey] = value
  }

  /**
   * 应用脱敏策略
   */
  private applyMasking(value: string, strategy: MaskingStrategy): string {
    if (typeof value !== 'string') {
      return value
    }

    switch (strategy) {
      case 'phone':
        return this.maskPhone(value)
      case 'idCard':
        return this.maskIdCard(value)
      case 'email':
        return this.maskEmail(value)
      case 'bankCard':
        return this.maskBankCard(value)
      case 'name':
        return this.maskName(value)
      case 'address':
        return this.maskAddress(value)
      case 'custom':
        return this.maskCustom(value)
      default:
        return this.maskDefault(value)
    }
  }

  /**
   * 手机号脱敏
   */
  public maskPhone(phone: string): string {
    if (!phone || phone.length < 7) return phone
    
    // 中国手机号：138****1234
    if (/^1[3-9]\d{9}$/.test(phone)) {
      return phone.replace(/(\d{3})\d{4}(\d{4})/, `$1${this.config.maskChar!.repeat(4)}$2`)
    }
    
    // 其他格式：保留前3位和后4位
    if (phone.length >= 7) {
      const start = phone.substring(0, 3)
      const end = phone.substring(phone.length - 4)
      const middle = this.config.maskChar!.repeat(phone.length - 7)
      return `${start}${middle}${end}`
    }
    
    return phone
  }

  /**
   * 身份证号脱敏
   */
  public maskIdCard(idCard: string): string {
    if (!idCard) return idCard
    
    // 18位身份证：123456********1234
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, `$1${this.config.maskChar!.repeat(8)}$2`)
    }
    
    // 15位身份证：123456*****123
    if (idCard.length === 15) {
      return idCard.replace(/(\d{6})\d{5}(\d{3})/, `$1${this.config.maskChar!.repeat(5)}$2`)
    }
    
    // 其他格式：保留前6位和后4位
    if (idCard.length > 10) {
      const start = idCard.substring(0, 6)
      const end = idCard.substring(idCard.length - 4)
      const middle = this.config.maskChar!.repeat(idCard.length - 10)
      return `${start}${middle}${end}`
    }
    
    return idCard
  }

  /**
   * 邮箱脱敏
   */
  public maskEmail(email: string): string {
    if (!email || !email.includes('@')) return email
    
    const [username, domain] = email.split('@')
    if (!username || username.length <= 2) return email

    const maskedUsername = username.length <= 4
      ? `${username[0]}${this.config.maskChar!.repeat(username.length - 1)}`
      : `${username.substring(0, 2)}${this.config.maskChar!.repeat(username.length - 4)}${username.substring(username.length - 2)}`
    
    return `${maskedUsername}@${domain}`
  }

  /**
   * 银行卡号脱敏
   */
  public maskBankCard(bankCard: string): string {
    if (!bankCard || bankCard.length < 8) return bankCard
    
    // 保留前4位和后4位
    const start = bankCard.substring(0, 4)
    const end = bankCard.substring(bankCard.length - 4)
    const middle = this.config.maskChar!.repeat(bankCard.length - 8)
    
    return `${start}${middle}${end}`
  }

  /**
   * 姓名脱敏
   */
  public maskName(name: string): string {
    if (!name) return name
    
    if (name.length === 1) {
      return name
    } else if (name.length === 2) {
      return `${name[0]}${this.config.maskChar}`
    } else {
      return `${name[0]}${this.config.maskChar!.repeat(name.length - 2)}${name[name.length - 1]}`
    }
  }

  /**
   * 地址脱敏
   */
  public maskAddress(address: string): string {
    if (!address || address.length < 6) return address
    
    // 保留前面的省市信息，脱敏详细地址
    const preserveLength = Math.min(6, Math.floor(address.length * 0.3))
    const start = address.substring(0, preserveLength)
    const masked = this.config.maskChar!.repeat(address.length - preserveLength)
    
    return `${start}${masked}`
  }

  /**
   * 自定义脱敏
   */
  private maskCustom(value: string): string {
    // 可以根据具体需求实现自定义脱敏逻辑
    return this.maskDefault(value)
  }

  /**
   * 默认脱敏
   */
  private maskDefault(value: string): string {
    if (!value || value.length <= 2) return value
    
    const visibleLength = Math.max(1, Math.floor(value.length * 0.3))
    const start = value.substring(0, visibleLength)
    const masked = this.config.maskChar!.repeat(value.length - visibleLength)
    
    return `${start}${masked}`
  }
}

/**
 * 预定义的脱敏配置
 */
export const MedicalDataMaskingConfig: MaskingConfig = {
  rules: [
    { field: 'patientName', strategy: 'name' },
    { field: 'patientIdCard', strategy: 'idCard' },
    { field: 'patientPhone', strategy: 'phone' },
    { field: 'patientEmail', strategy: 'email' },
    { field: 'patientAddress', strategy: 'address' },
    { field: 'doctorPhone', strategy: 'phone' },
    { field: 'doctorEmail', strategy: 'email' },
    { field: 'bankAccount', strategy: 'bankCard' },
    { field: 'emergencyContactPhone', strategy: 'phone' },
    { field: 'guardianIdCard', strategy: 'idCard' },
    { field: 'guardianPhone', strategy: 'phone' },
  ],
  preserveLength: true,
  maskChar: '*'
}

export const UserDataMaskingConfig: MaskingConfig = {
  rules: [
    { field: 'realName', strategy: 'name' },
    { field: 'idCard', strategy: 'idCard' },
    { field: 'phone', strategy: 'phone' },
    { field: 'email', strategy: 'email' },
    { field: 'address', strategy: 'address' },
  ],
  preserveLength: true,
  maskChar: '*'
}

/**
 * 创建医疗数据脱敏器
 */
export function createMedicalDataMasker(): DataMasker {
  return new DataMasker(MedicalDataMaskingConfig)
}

/**
 * 创建用户数据脱敏器
 */
export function createUserDataMasker(): DataMasker {
  return new DataMasker(UserDataMaskingConfig)
}

/**
 * 便捷的脱敏函数
 */
export function maskMedicalData<T>(data: T): T {
  const masker = createMedicalDataMasker()
  return masker.mask(data)
}

export function maskUserData<T>(data: T): T {
  const masker = createUserDataMasker()
  return masker.mask(data)
}

/**
 * 快速脱敏函数
 */
export const quickMask = {
  phone: (phone: string) => new DataMasker({ rules: [] }).maskPhone(phone),
  idCard: (idCard: string) => new DataMasker({ rules: [] }).maskIdCard(idCard),
  email: (email: string) => new DataMasker({ rules: [] }).maskEmail(email),
  name: (name: string) => new DataMasker({ rules: [] }).maskName(name),
  bankCard: (bankCard: string) => new DataMasker({ rules: [] }).maskBankCard(bankCard),
  address: (address: string) => new DataMasker({ rules: [] }).maskAddress(address)
}
