/**
 * 审计日志中间件
 * 实现P0-009安全机制强化目标：
 * - 记录所有用户操作的审计日志
 * - 实现完整的操作追踪
 * - 支持安全事件监控
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/jwt'

export interface AuditLogEntry {
  userId: number
  userName?: string
  action: string
  resource: string
  resourceId?: string
  resourceType?: string
  operationResult: 'SUCCESS' | 'FAILED' | 'UNAUTHORIZED'
  operationDescription: string
  requestData?: any
  responseData?: any
  ipAddress: string
  userAgent: string
  timestamp: Date
  executionTime: number
  errorMessage?: string
}

export interface SecurityEvent {
  eventType: 'LOGIN_ATTEMPT' | 'UNAUTHORIZED_ACCESS' | 'SUSPICIOUS_ACTIVITY' | 'DATA_ACCESS' | 'PERMISSION_ESCALATION'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  userId?: number
  ipAddress: string
  userAgent: string
  description: string
  metadata?: any
  timestamp: Date
}

/**
 * 审计日志装饰器
 */
export function withAuditLog(
  operationType: string,
  operationModule: string,
  getDescription?: (req: NextRequest, result: any) => string
) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value

    descriptor.value = async function (request: NextRequest, ...args: any[]) {
      const startTime = Date.now()
      let auditEntry: Partial<AuditLogEntry> = {
        action: operationType,
        resource: operationModule,
        timestamp: new Date(),
        ipAddress: getClientIP(request),
        userAgent: request.headers.get('user-agent') || 'Unknown'
      }

      try {
        // 验证用户身份
        const authHeader = request.headers.get('authorization')
        if (authHeader && authHeader.startsWith('Bearer ')) {
          const token = authHeader.substring(7)
          const payload = verifyAccessToken(token)
          if (payload) {
            auditEntry.userId = payload.userId
            auditEntry.userName = payload.username
          }
        }

        // 记录请求数据（敏感数据脱敏）
        if (request.method !== 'GET') {
          try {
            const requestBody = await request.clone().json()
            auditEntry.requestData = maskSensitiveData(requestBody)
          } catch {
            // 忽略非JSON请求
          }
        }

        // 执行原始方法
        const result = await originalMethod.call(this, request, ...args)
        
        // 记录执行结果
        auditEntry.operationResult = 'SUCCESS'
        auditEntry.executionTime = Date.now() - startTime
        
        if (getDescription) {
          auditEntry.operationDescription = getDescription(request, result)
        } else {
          auditEntry.operationDescription = `${operationType} ${operationModule}`
        }

        // 记录响应数据（敏感数据脱敏）
        if (result instanceof NextResponse) {
          try {
            const responseData = await result.clone().json()
            auditEntry.responseData = maskSensitiveData(responseData)
          } catch {
            // 忽略非JSON响应
          }
        }

        // 记录审计日志
        await recordAuditLog(auditEntry as AuditLogEntry)

        return result

      } catch (error) {
        // 记录失败的操作
        auditEntry.operationResult = 'FAILED'
        auditEntry.executionTime = Date.now() - startTime
        auditEntry.errorMessage = error instanceof Error ? error.message : String(error)
        auditEntry.operationDescription = `${operationType} ${operationModule} - FAILED`

        await recordAuditLog(auditEntry as AuditLogEntry)

        throw error
      }
    }

    return descriptor
  }
}

/**
 * 记录审计日志
 */
export async function recordAuditLog(entry: AuditLogEntry): Promise<void> {
  try {
    // 在API路由中使用时，动态导入数据库服务
    if (typeof window === 'undefined') {
      const { logSystemOperation } = await import('@/lib/system-log-service')
      // 映射操作模块到数据库约束允许的值
      const moduleMapping: { [key: string]: string } = {
        'users': 'USER',
        'roles': 'ROLE',
        'medical-cases': 'MEDICAL_CASE',
        'supervision-rules': 'SUPERVISION_RULE',
        'knowledge-base': 'KNOWLEDGE',
        'system': 'SYSTEM',
        'auth': 'AUTH'
      }

      // 映射操作类型到数据库约束允许的值
      const actionMapping: { [key: string]: string } = {
        'GET': 'QUERY',
        'POST': 'CREATE',
        'PUT': 'UPDATE',
        'DELETE': 'DELETE',
        'PATCH': 'UPDATE',
        'create': 'CREATE',
        'update': 'UPDATE',
        'delete': 'DELETE',
        'query': 'QUERY',
        'login': 'LOGIN',
        'logout': 'LOGOUT',
        'export': 'EXPORT',
        'import': 'IMPORT',
        'execute': 'EXECUTE'
      }

      const mappedModule = moduleMapping[entry.resource] || 'SYSTEM'
      const mappedAction = actionMapping[entry.action] || 'QUERY'

      await logSystemOperation(
        entry.userId || 0,
        mappedAction,
        mappedModule,
        entry.operationDescription,
        entry.operationResult,
        entry.ipAddress,
        entry.userAgent,
        entry.requestData ? JSON.stringify(entry.requestData) : undefined,
        entry.responseData ? JSON.stringify(entry.responseData) : undefined,
        entry.executionTime
      )
    } else {
      // 在客户端环境中，只记录到控制台
      console.log('Audit Log:', entry)
    }
  } catch (error) {
    console.error('Failed to record audit log:', error)
    // 不抛出错误，避免影响主要业务流程
  }
}

/**
 * 记录安全事件
 */
export async function recordSecurityEvent(event: SecurityEvent): Promise<void> {
  try {
    // 在API路由中使用时，动态导入数据库服务
    if (typeof window === 'undefined') {
      const { logSystemError } = await import('@/lib/system-log-service')

      await logSystemError(
        event.eventType,
        event.severity,
        event.description,
        JSON.stringify(event.metadata || {}),
        undefined, // requestUrl
        undefined, // requestMethod
        event.userId,
        event.ipAddress,
        event.userAgent
      )
    } else {
      // 在客户端环境中，只记录到控制台
      console.warn('Security Event:', event)
    }

    // 如果是高危事件，发送告警
    if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {
      await sendSecurityAlert(event)
    }
  } catch (error) {
    console.error('Failed to record security event:', error)
  }
}

/**
 * 发送安全告警
 */
async function sendSecurityAlert(event: SecurityEvent): Promise<void> {
  // 这里可以集成邮件、短信、钉钉等告警渠道
  console.warn('🚨 Security Alert:', {
    type: event.eventType,
    severity: event.severity,
    description: event.description,
    userId: event.userId,
    ipAddress: event.ipAddress,
    timestamp: event.timestamp
  })
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown'
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * 敏感数据脱敏
 */
function maskSensitiveData(data: any): any {
  if (!data || typeof data !== 'object') {
    return data
  }

  const sensitiveFields = [
    'password', 'token', 'secret', 'key', 'idCard', 'phone', 'email',
    'patientIdCard', 'patientPhone', 'doctorPhone', 'bankAccount'
  ]

  const masked = { ...data }

  for (const field of sensitiveFields) {
    if (masked[field]) {
      if (field.includes('phone') || field.includes('Phone')) {
        masked[field] = maskPhone(masked[field])
      } else if (field.includes('idCard') || field.includes('IdCard')) {
        masked[field] = maskIdCard(masked[field])
      } else if (field.includes('email') || field.includes('Email')) {
        masked[field] = maskEmail(masked[field])
      } else {
        masked[field] = '***'
      }
    }
  }

  // 递归处理嵌套对象
  for (const key in masked) {
    if (typeof masked[key] === 'object' && masked[key] !== null) {
      masked[key] = maskSensitiveData(masked[key])
    }
  }

  return masked
}

/**
 * 手机号脱敏
 */
function maskPhone(phone: string): string {
  if (!phone || phone.length < 7) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 身份证号脱敏
 */
function maskIdCard(idCard: string): string {
  if (!idCard || idCard.length < 10) return idCard
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

/**
 * 邮箱脱敏
 */
function maskEmail(email: string): string {
  if (!email || !email.includes('@')) return email
  const [username, domain] = email.split('@')
  if (!username || username.length <= 2) return email
  return `${username.substring(0, 2)}***@${domain}`
}

/**
 * API安全中间件
 */
export function createSecurityMiddleware() {
  return async (request: NextRequest) => {
    const startTime = Date.now()
    
    try {
      // 检查请求频率限制
      await checkRateLimit(request)
      
      // 检查IP白名单/黑名单
      await checkIPRestrictions(request)
      
      // 检查用户权限
      await checkUserPermissions(request)
      
      return NextResponse.next()
      
    } catch (error) {
      // 记录安全事件
      await recordSecurityEvent({
        eventType: 'UNAUTHORIZED_ACCESS',
        severity: 'HIGH',
        ipAddress: getClientIP(request),
        userAgent: request.headers.get('user-agent') || 'Unknown',
        description: `Security check failed: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date()
      })
      
      return NextResponse.json({
        success: false,
        message: 'Access denied'
      }, { status: 403 })
    }
  }
}

/**
 * 检查请求频率限制
 */
async function checkRateLimit(request: NextRequest): Promise<void> {
  // 简单的内存限流实现，生产环境建议使用Redis
  const ip = getClientIP(request)
  const key = `rate_limit_${ip}`
  
  // 这里可以实现更复杂的限流逻辑
  // 暂时跳过实现
}

/**
 * 检查IP限制
 */
async function checkIPRestrictions(request: NextRequest): Promise<void> {
  const ip = getClientIP(request)
  
  // 检查IP黑名单
  const blacklistedIPs = ['*************'] // 示例黑名单
  if (blacklistedIPs.includes(ip)) {
    throw new Error(`IP ${ip} is blacklisted`)
  }
}

/**
 * 检查用户权限
 */
async function checkUserPermissions(request: NextRequest): Promise<void> {
  // 对于需要认证的API，检查token有效性
  if (request.nextUrl.pathname.startsWith('/api/') && 
      !request.nextUrl.pathname.startsWith('/api/auth/')) {
    
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header')
    }
    
    const token = authHeader.substring(7)
    const payload = verifyAccessToken(token)
    
    if (!payload) {
      throw new Error('Invalid or expired token')
    }
  }
}
