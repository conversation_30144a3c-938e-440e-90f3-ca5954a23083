/**
 * Edge Runtime兼容的安全检查
 * 不依赖数据库，适用于middleware环境
 */

import { NextRequest, NextResponse } from 'next/server'

/**
 * 简单的内存限流存储
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

/**
 * 基础安全检查（Edge Runtime兼容）
 */
export async function basicSecurityCheck(request: NextRequest): Promise<NextResponse | null> {
  try {
    // 1. 基础请求验证
    if (!validateBasicRequest(request)) {
      return createSecurityResponse('Invalid request format', 400)
    }

    // 2. 简单的速率限制
    if (!await checkBasicRateLimit(request)) {
      return createSecurityResponse('Rate limit exceeded', 429)
    }

    // 3. 基础攻击检测
    if (detectBasicThreats(request)) {
      return createSecurityResponse('Security threat detected', 403)
    }

    return null // 通过检查

  } catch (error) {
    console.error('Basic security check error:', error)
    return null // 不阻断请求
  }
}

/**
 * 基础请求验证
 */
function validateBasicRequest(request: NextRequest): boolean {
  const { pathname } = request.nextUrl
  const method = request.method

  // 检查方法是否合法
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
  if (!allowedMethods.includes(method)) {
    return false
  }

  // 检查路径是否合法
  if (pathname.includes('..') || pathname.includes('//')) {
    return false
  }

  // 检查Content-Length
  const contentLength = request.headers.get('content-length')
  if (contentLength && parseInt(contentLength) > 50 * 1024 * 1024) { // 50MB限制
    return false
  }

  return true
}

/**
 * 基础速率限制
 */
async function checkBasicRateLimit(request: NextRequest): Promise<boolean> {
  const ip = getClientIP(request)
  const now = Date.now()
  const windowMs = 15 * 60 * 1000 // 15分钟
  const maxRequests = 1000 // 每15分钟最多1000请求

  const key = `rate_limit_${ip}`
  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    // 新的时间窗口
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    })
    return true
  } else {
    // 在当前时间窗口内
    record.count++
    if (record.count > maxRequests) {
      return false
    }
    return true
  }
}

/**
 * 基础威胁检测
 */
function detectBasicThreats(request: NextRequest): boolean {
  const url = request.url.toLowerCase()
  const userAgent = request.headers.get('user-agent')?.toLowerCase() || ''

  // SQL注入基础检测
  const sqlPatterns = [
    'union select',
    'drop table',
    'insert into',
    'delete from',
    'update set',
    'exec(',
    'execute(',
    'sp_',
    'xp_'
  ]

  if (sqlPatterns.some(pattern => url.includes(pattern))) {
    return true
  }

  // XSS基础检测
  const xssPatterns = [
    '<script',
    'javascript:',
    'onload=',
    'onerror=',
    'onclick=',
    'onmouseover='
  ]

  if (xssPatterns.some(pattern => url.includes(pattern))) {
    return true
  }

  // 可疑User-Agent检测
  const suspiciousAgents = [
    'sqlmap',
    'nikto',
    'nessus',
    'openvas',
    'nmap',
    'masscan',
    'zap',
    'burp'
  ]

  if (suspiciousAgents.some(agent => userAgent.includes(agent))) {
    return true
  }

  return false
}

/**
 * 获取客户端IP
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown'
  }
  
  return realIP || 'unknown'
}

/**
 * 创建安全响应
 */
function createSecurityResponse(message: string, status: number): NextResponse {
  return NextResponse.json({
    success: false,
    message: 'Access denied',
    error: process.env.NODE_ENV === 'development' ? message : undefined
  }, { 
    status,
    headers: {
      'X-Security-Check': 'failed'
    }
  })
}

/**
 * 清理过期的限流记录
 */
export function cleanupRateLimitStore(): void {
  const now = Date.now()
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}

// 定期清理限流存储
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimitStore, 5 * 60 * 1000) // 每5分钟清理一次
}
