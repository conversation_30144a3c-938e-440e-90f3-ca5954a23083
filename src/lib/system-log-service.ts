/**
 * 系统日志服务
 * 实现P0-008数据库设计与API实现一致性修复目标：
 * - 替换模拟数据，实现真实的数据库查询
 * - 确保SYSTEM_OPERATION_LOG等表与API接口完全匹配
 */

import { executeQuery } from '@/lib/database'

export interface SystemOperationLog {
  id: number
  userId: number
  userName?: string
  operationType: string
  operationModule: string
  operationDescription: string
  operationResult: string
  ipAddress: string
  userAgent: string
  requestData?: string
  responseData?: string
  executionTime: number
  createdAt: Date
}

export interface SystemLoginLog {
  id: number
  userId: number
  userName?: string
  loginType: string
  loginResult: string
  ipAddress: string
  userAgent: string
  loginTime: Date
  logoutTime?: Date
  sessionDuration?: number
}

export interface SystemErrorLog {
  id: number
  errorType: string
  errorLevel: string
  errorMessage: string
  errorStack?: string
  requestUrl?: string
  requestMethod?: string
  userId?: number
  userName?: string
  ipAddress?: string
  userAgent?: string
  createdAt: Date
}

export interface LogQueryParams {
  page?: number
  pageSize?: number
  startDate?: Date
  endDate?: Date
  userId?: number
  operationType?: string
  operationModule?: string
  logLevel?: string
  search?: string
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 获取系统操作日志
 */
export async function getSystemOperationLogs(params: LogQueryParams = {}) {
  const {
    page = 1,
    pageSize = 20,
    startDate,
    endDate,
    userId,
    operationType,
    operationModule,
    search,
    sortBy = 'CREATED_AT',
    sortOrder = 'DESC'
  } = params

  const offset = (page - 1) * pageSize
  const conditions: string[] = []
  const queryParams: any = { offset, pageSize }

  // 构建查询条件
  if (startDate) {
    conditions.push('sol.CREATED_AT >= :startDate')
    queryParams.startDate = startDate
  }

  if (endDate) {
    conditions.push('sol.CREATED_AT <= :endDate')
    queryParams.endDate = endDate
  }

  if (userId) {
    conditions.push('sol.USER_ID = :userId')
    queryParams.userId = userId
  }

  if (operationType) {
    conditions.push('sol.OPERATION_TYPE = :operationType')
    queryParams.operationType = operationType
  }

  if (operationModule) {
    conditions.push('sol.OPERATION_MODULE = :operationModule')
    queryParams.operationModule = operationModule
  }

  if (search) {
    conditions.push('(UPPER(sol.OPERATION_DESCRIPTION) LIKE UPPER(:search) OR UPPER(u.REAL_NAME) LIKE UPPER(:searchUser))')
    queryParams.search = `%${search}%`
    queryParams.searchUser = `%${search}%`
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM SYSTEM_OPERATION_LOG sol
    LEFT JOIN USER_INFO u ON sol.USER_ID = u.ID
    ${whereClause}
  `

  const countResult = await executeQuery(countSql, queryParams)
  const total = countResult.rows[0]?.TOTAL || 0

  // 查询数据
  const dataSql = `
    SELECT 
      sol.ID, sol.USER_ID, sol.OPERATION_TYPE, sol.OPERATION_MODULE,
      sol.OPERATION_DESCRIPTION, sol.OPERATION_RESULT, sol.IP_ADDRESS,
      sol.USER_AGENT, sol.REQUEST_DATA, sol.RESPONSE_DATA,
      sol.EXECUTION_TIME, sol.CREATED_AT,
      u.REAL_NAME as USER_NAME
    FROM SYSTEM_OPERATION_LOG sol
    LEFT JOIN USER_INFO u ON sol.USER_ID = u.ID
    ${whereClause}
    ORDER BY sol.${sortBy} ${sortOrder}
    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
  `

  const dataResult = await executeQuery(dataSql, queryParams)

  const items = dataResult.rows.map(row => ({
    id: row.ID,
    userId: row.USER_ID,
    userName: row.USER_NAME,
    operationType: row.OPERATION_TYPE,
    operationModule: row.OPERATION_MODULE,
    operationDescription: row.OPERATION_DESCRIPTION,
    operationResult: row.OPERATION_RESULT,
    ipAddress: row.IP_ADDRESS,
    userAgent: row.USER_AGENT,
    requestData: row.REQUEST_DATA,
    responseData: row.RESPONSE_DATA,
    executionTime: row.EXECUTION_TIME || 0,
    createdAt: row.CREATED_AT
  }))

  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 获取系统登录日志
 */
export async function getSystemLoginLogs(params: LogQueryParams = {}) {
  const {
    page = 1,
    pageSize = 20,
    startDate,
    endDate,
    userId,
    search,
    sortBy = 'LOGIN_TIME',
    sortOrder = 'DESC'
  } = params

  const offset = (page - 1) * pageSize
  const conditions: string[] = []
  const queryParams: any = { offset, pageSize }

  // 构建查询条件
  if (startDate) {
    conditions.push('sll.LOGIN_TIME >= :startDate')
    queryParams.startDate = startDate
  }

  if (endDate) {
    conditions.push('sll.LOGIN_TIME <= :endDate')
    queryParams.endDate = endDate
  }

  if (userId) {
    conditions.push('sll.USER_ID = :userId')
    queryParams.userId = userId
  }

  if (search) {
    conditions.push('(UPPER(u.REAL_NAME) LIKE UPPER(:search) OR UPPER(sll.IP_ADDRESS) LIKE UPPER(:searchIP))')
    queryParams.search = `%${search}%`
    queryParams.searchIP = `%${search}%`
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM SYSTEM_LOGIN_LOG sll
    LEFT JOIN USER_INFO u ON sll.USER_ID = u.ID
    ${whereClause}
  `

  const countResult = await executeQuery(countSql, queryParams)
  const total = countResult.rows[0]?.TOTAL || 0

  // 查询数据
  const dataSql = `
    SELECT 
      sll.ID, sll.USER_ID, sll.LOGIN_TYPE, sll.LOGIN_RESULT,
      sll.IP_ADDRESS, sll.USER_AGENT, sll.LOGIN_TIME, sll.LOGOUT_TIME,
      sll.SESSION_DURATION,
      u.REAL_NAME as USER_NAME
    FROM SYSTEM_LOGIN_LOG sll
    LEFT JOIN USER_INFO u ON sll.USER_ID = u.ID
    ${whereClause}
    ORDER BY sll.${sortBy} ${sortOrder}
    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
  `

  const dataResult = await executeQuery(dataSql, queryParams)

  const items = dataResult.rows.map(row => ({
    id: row.ID,
    userId: row.USER_ID,
    userName: row.USER_NAME,
    loginType: row.LOGIN_TYPE,
    loginResult: row.LOGIN_RESULT,
    ipAddress: row.IP_ADDRESS,
    userAgent: row.USER_AGENT,
    loginTime: row.LOGIN_TIME,
    logoutTime: row.LOGOUT_TIME,
    sessionDuration: row.SESSION_DURATION
  }))

  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 获取系统错误日志
 */
export async function getSystemErrorLogs(params: LogQueryParams = {}) {
  const {
    page = 1,
    pageSize = 20,
    startDate,
    endDate,
    logLevel,
    search,
    sortBy = 'CREATED_AT',
    sortOrder = 'DESC'
  } = params

  const offset = (page - 1) * pageSize
  const conditions: string[] = []
  const queryParams: any = { offset, pageSize }

  // 构建查询条件
  if (startDate) {
    conditions.push('sel.CREATED_AT >= :startDate')
    queryParams.startDate = startDate
  }

  if (endDate) {
    conditions.push('sel.CREATED_AT <= :endDate')
    queryParams.endDate = endDate
  }

  if (logLevel) {
    conditions.push('sel.ERROR_LEVEL = :logLevel')
    queryParams.logLevel = logLevel
  }

  if (search) {
    conditions.push('(UPPER(sel.ERROR_MESSAGE) LIKE UPPER(:search) OR UPPER(sel.REQUEST_URL) LIKE UPPER(:searchUrl))')
    queryParams.search = `%${search}%`
    queryParams.searchUrl = `%${search}%`
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

  // 查询总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM SYSTEM_ERROR_LOG sel
    ${whereClause}
  `

  const countResult = await executeQuery(countSql, queryParams)
  const total = countResult.rows[0]?.TOTAL || 0

  // 查询数据
  const dataSql = `
    SELECT 
      sel.ID, sel.ERROR_TYPE, sel.ERROR_LEVEL, sel.ERROR_MESSAGE,
      sel.ERROR_STACK, sel.REQUEST_URL, sel.REQUEST_METHOD,
      sel.USER_ID, sel.IP_ADDRESS, sel.USER_AGENT, sel.CREATED_AT,
      u.REAL_NAME as USER_NAME
    FROM SYSTEM_ERROR_LOG sel
    LEFT JOIN USER_INFO u ON sel.USER_ID = u.ID
    ${whereClause}
    ORDER BY sel.${sortBy} ${sortOrder}
    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
  `

  const dataResult = await executeQuery(dataSql, queryParams)

  const items = dataResult.rows.map(row => ({
    id: row.ID,
    errorType: row.ERROR_TYPE,
    errorLevel: row.ERROR_LEVEL,
    errorMessage: row.ERROR_MESSAGE,
    errorStack: row.ERROR_STACK,
    requestUrl: row.REQUEST_URL,
    requestMethod: row.REQUEST_METHOD,
    userId: row.USER_ID,
    userName: row.USER_NAME,
    ipAddress: row.IP_ADDRESS,
    userAgent: row.USER_AGENT,
    createdAt: row.CREATED_AT
  }))

  return {
    items,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  }
}

/**
 * 记录系统操作日志
 */
export async function logSystemOperation(
  userId: number,
  operationType: string,
  operationModule: string,
  operationDescription: string,
  operationResult: string,
  ipAddress: string,
  userAgent: string,
  requestData?: string,
  responseData?: string,
  executionTime?: number
): Promise<void> {
  const sql = `
    INSERT INTO SYSTEM_OPERATION_LOG (
      ID, USER_ID, OPERATION_TYPE, OPERATION_MODULE, OPERATION_DESC,
      REQUEST_METHOD, REQUEST_URL, REQUEST_PARAMS, RESPONSE_STATUS, RESPONSE_DATA,
      IP_ADDRESS, USER_AGENT, EXECUTION_TIME, IS_SUCCESS, CREATED_AT
    ) VALUES (
      SEQ_SYSTEM_OPERATION_LOG.NEXTVAL,
      :userId, :operationType, :operationModule, :operationDescription,
      :requestMethod, :requestUrl, :requestData, :responseStatus, :responseData,
      :ipAddress, :userAgent, :executionTime, :isSuccess, CURRENT_TIMESTAMP
    )
  `

  await executeQuery(sql, {
    userId,
    operationType,
    operationModule,
    operationDescription,
    requestMethod: 'POST',
    requestUrl: '',
    requestData: requestData || '',
    responseStatus: 200,
    responseData: responseData || '',
    ipAddress,
    userAgent,
    executionTime: executionTime || 0,
    isSuccess: 1
  })
}

/**
 * 记录系统登录日志
 */
export async function logSystemLogin(
  userId: number,
  loginType: string,
  loginResult: string,
  ipAddress: string,
  userAgent: string
): Promise<void> {
  const sql = `
    INSERT INTO SYSTEM_LOGIN_LOG (
      ID, USER_ID, LOGIN_TYPE, LOGIN_RESULT, IP_ADDRESS,
      USER_AGENT, LOGIN_TIME, CREATED_AT
    ) VALUES (
      SEQ_SYSTEM_LOGIN_LOG.NEXTVAL,
      :userId, :loginType, :loginResult, :ipAddress,
      :userAgent, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
  `

  await executeQuery(sql, {
    userId,
    loginType,
    loginResult,
    ipAddress,
    userAgent
  })
}

/**
 * 记录系统错误日志
 */
export async function logSystemError(
  errorType: string,
  errorLevel: string,
  errorMessage: string,
  errorStack?: string,
  requestUrl?: string,
  requestMethod?: string,
  userId?: number,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  const sql = `
    INSERT INTO SYSTEM_ERROR_LOG (
      ID, ERROR_TYPE, ERROR_LEVEL, ERROR_MESSAGE, ERROR_STACK,
      REQUEST_URL, REQUEST_METHOD, USER_ID, IP_ADDRESS,
      USER_AGENT, CREATED_AT
    ) VALUES (
      SEQ_SYSTEM_ERROR_LOG.NEXTVAL,
      :errorType, :errorLevel, :errorMessage, :errorStack,
      :requestUrl, :requestMethod, :userId, :ipAddress,
      :userAgent, CURRENT_TIMESTAMP
    )
  `

  await executeQuery(sql, {
    errorType,
    errorLevel,
    errorMessage,
    errorStack: errorStack || '',
    requestUrl: requestUrl || '',
    requestMethod: requestMethod || '',
    userId: userId || null,
    ipAddress: ipAddress || '',
    userAgent: userAgent || ''
  })
}
