@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 0.5rem;
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* 功能性色彩扩展 */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* 状态色彩 */
    --pending: 43 74% 66%;
    --approved: 142 76% 36%;
    --rejected: 0 84% 60%;
    --draft: 215 20% 65%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* 功能性色彩扩展 - 暗色模式 */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* 状态色彩 - 暗色模式 */
    --pending: 43 74% 66%;
    --approved: 142 76% 36%;
    --rejected: 0 84% 60%;
    --draft: 215 20% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* 企业级字体系统 - 参考Material Design和Ant Design */
@layer base {
  /* 字体层级定义 */
  .text-display-large {
    @apply text-4xl font-bold leading-tight tracking-tight;
  }

  .text-display-medium {
    @apply text-3xl font-bold leading-tight tracking-tight;
  }

  .text-display-small {
    @apply text-2xl font-bold leading-tight tracking-tight;
  }

  .text-headline-large {
    @apply text-xl font-semibold leading-normal tracking-normal;
  }

  .text-headline-medium {
    @apply text-lg font-semibold leading-normal tracking-normal;
  }

  .text-headline-small {
    @apply text-base font-semibold leading-normal tracking-normal;
  }

  .text-title-large {
    @apply text-base font-medium leading-normal tracking-normal;
  }

  .text-title-medium {
    @apply text-sm font-medium leading-normal tracking-normal;
  }

  .text-title-small {
    @apply text-xs font-medium leading-normal tracking-wide;
  }

  .text-body-large {
    @apply text-base font-normal leading-relaxed tracking-normal;
  }

  .text-body-medium {
    @apply text-sm font-normal leading-relaxed tracking-normal;
  }

  .text-body-small {
    @apply text-xs font-normal leading-relaxed tracking-normal;
  }

  .text-label-large {
    @apply text-sm font-medium leading-normal tracking-wide;
  }

  .text-label-medium {
    @apply text-xs font-medium leading-normal tracking-wide;
  }

  .text-label-small {
    @apply text-xs font-medium leading-tight tracking-wide;
  }
}

/* 医保平台专用组件样式 */
@layer components {
  .medical-card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }

  /* 现代化布局组件 */
  .layout-header {
    @apply sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60;
  }

  .layout-sidebar {
    @apply flex h-full flex-col border-r bg-sidebar text-sidebar-foreground;
  }

  .layout-main {
    @apply flex-1 overflow-auto bg-background;
  }

  .layout-container {
    @apply container mx-auto max-w-7xl p-6 space-y-6;
  }

  /* 页面头部样式 */
  .page-header {
    @apply border-b bg-muted/30;
  }

  .page-header-content {
    @apply flex items-center justify-between px-6 py-4;
  }

  /* 认证布局样式 */
  .auth-layout {
    @apply min-h-screen grid lg:grid-cols-2;
  }

  .auth-brand {
    @apply relative hidden lg:flex lg:flex-col lg:justify-center lg:items-center bg-gradient-to-br from-primary/10 via-primary/5 to-background p-8;
  }

  .auth-form {
    @apply flex flex-col;
  }

  /* 加载布局样式 */
  .loading-layout {
    @apply min-h-screen bg-background flex items-center justify-center;
  }

  /* 响应式字体 */
  .font-display {
    font-family: var(--font-inter), var(--font-noto-sans-sc), system-ui, sans-serif;
  }

  /* 优化的滚动条 */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--border) / 0.8);
  }

  /* 状态指示器 */
  .status-indicator {
    @apply inline-flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .status-indicator.online {
    @apply bg-green-50 text-green-700 ring-1 ring-green-600/20;
  }

  .status-indicator.offline {
    @apply bg-gray-50 text-gray-600 ring-1 ring-gray-500/20;
  }

  .status-indicator.warning {
    @apply bg-yellow-50 text-yellow-800 ring-1 ring-yellow-600/20;
  }

  .status-indicator.error {
    @apply bg-red-50 text-red-700 ring-1 ring-red-600/20;
  }
}

/* 滚动条样式 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
}
