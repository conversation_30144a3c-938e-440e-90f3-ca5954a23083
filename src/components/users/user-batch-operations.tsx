/**
 * 用户批量操作组件
 * 实现P0-007页面功能架构重新设计目标：
 * - 添加用户状态批量管理
 * - 简化权限分配界面
 * - 添加用户操作日志查看
 */

'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/lib/toast'
import { useBatchOperation } from '@/hooks/use-page-state'
// import { User } from '@/types/user'
interface User {
  id: number
  username: string
  email: string
  isActive: boolean
  role: string
  status: string
  createdAt: string
}
import {
  Users,
  UserCheck,
  UserX,
  Shield,
  Settings,
  Activity,
  Download,
  Upload,
  Mail,
  Lock,
  Unlock,
  Trash2,
  Eye,
  RefreshCw
} from 'lucide-react'

interface UserBatchOperationsProps {
  selectedUsers: number[]
  users: User[]
  onRefresh: () => void
  onClearSelection: () => void
}

export function UserBatchOperations({
  selectedUsers,
  users,
  onRefresh,
  onClearSelection
}: UserBatchOperationsProps) {
  const { toast } = useToast()
  const { executeBatch } = useBatchOperation()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [operationType, setOperationType] = useState<string>('')

  const selectedUserData = users.filter(user => selectedUsers.includes(user.id))

  const handleBatchStatusChange = async (newStatus: string) => {
    if (selectedUsers.length === 0) return

    try {
      await executeBatch(
        selectedUsers,
        async (userId: number) => {
          const response = await fetch(`/api/users/${userId}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify({ status: newStatus })
          })
          
          if (!response.ok) {
            throw new Error(`更新用户 ${userId} 状态失败`)
          }
        },
        {
          operationId: `batch-status-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量更新 ${selectedUsers.length} 个用户状态为 ${newStatus}`
        }
      )

      onRefresh()
      onClearSelection()
      
      toast({
        title: '批量更新成功',
        description: `已更新 ${selectedUsers.length} 个用户的状态`
      })
    } catch (error) {
      console.error('批量更新状态失败:', error)
    }
  }

  const handleBatchRoleChange = async (newRole: string) => {
    if (selectedUsers.length === 0) return

    try {
      await executeBatch(
        selectedUsers,
        async (userId: number) => {
          const response = await fetch(`/api/users/${userId}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify({ role: newRole })
          })
          
          if (!response.ok) {
            throw new Error(`更新用户 ${userId} 角色失败`)
          }
        },
        {
          operationId: `batch-role-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量更新 ${selectedUsers.length} 个用户角色为 ${newRole}`
        }
      )

      onRefresh()
      onClearSelection()
      
      toast({
        title: '批量更新成功',
        description: `已更新 ${selectedUsers.length} 个用户的角色`
      })
    } catch (error) {
      console.error('批量更新角色失败:', error)
    }
  }

  const handleBatchDelete = async () => {
    if (selectedUsers.length === 0) return
    if (!confirm(`确定要删除选中的 ${selectedUsers.length} 个用户吗？此操作不可撤销。`)) return

    try {
      await executeBatch(
        selectedUsers,
        async (userId: number) => {
          const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
          })
          
          if (!response.ok) {
            throw new Error(`删除用户 ${userId} 失败`)
          }
        },
        {
          operationId: `batch-delete-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量删除 ${selectedUsers.length} 个用户`
        }
      )

      onRefresh()
      onClearSelection()
      
      toast({
        title: '批量删除成功',
        description: `已删除 ${selectedUsers.length} 个用户`
      })
    } catch (error) {
      console.error('批量删除失败:', error)
    }
  }

  const handleBatchNotification = async () => {
    if (selectedUsers.length === 0) return

    try {
      await executeBatch(
        selectedUsers,
        async (userId: number) => {
          const response = await fetch(`/api/users/${userId}/notify`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify({
              type: 'system_notification',
              message: '系统通知：请及时更新您的个人信息'
            })
          })
          
          if (!response.ok) {
            throw new Error(`发送通知给用户 ${userId} 失败`)
          }
        },
        {
          operationId: `batch-notify-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量发送通知给 ${selectedUsers.length} 个用户`
        }
      )

      toast({
        title: '批量通知成功',
        description: `已发送通知给 ${selectedUsers.length} 个用户`
      })
    } catch (error) {
      console.error('批量发送通知失败:', error)
    }
  }

  const exportSelectedUsers = () => {
    const csvContent = [
      ['用户名', '邮箱', '角色', '状态', '创建时间'].join(','),
      ...selectedUserData.map(user => [
        user.username,
        user.email,
        user.role,
        user.status,
        new Date(user.createdAt).toLocaleDateString()
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `selected_users_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Users className="h-4 w-4" />
          批量操作
          {selectedUsers.length > 0 && (
            <Badge variant="secondary">{selectedUsers.length}</Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {selectedUsers.length === 0 ? (
          <div className="text-center text-muted-foreground py-4">
            <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">请先选择用户</p>
          </div>
        ) : (
          <>
            {/* 选中用户信息 */}
            <div className="text-sm text-muted-foreground">
              已选择 <Badge variant="secondary">{selectedUsers.length}</Badge> 个用户
            </div>
            
            <Separator />
            
            {/* 状态管理 */}
            <div className="space-y-2">
              <div className="text-sm font-medium">状态管理</div>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusChange('ACTIVE')}
                  className="justify-start"
                >
                  <UserCheck className="h-4 w-4 mr-2" />
                  批量激活
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusChange('INACTIVE')}
                  className="justify-start"
                >
                  <UserX className="h-4 w-4 mr-2" />
                  批量禁用
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusChange('PENDING')}
                  className="justify-start"
                >
                  <Lock className="h-4 w-4 mr-2" />
                  设为待审核
                </Button>
              </div>
            </div>
            
            <Separator />
            
            {/* 角色管理 */}
            <div className="space-y-2">
              <div className="text-sm font-medium">角色管理</div>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchRoleChange('VIEWER')}
                  className="justify-start"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  设为查看者
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchRoleChange('OPERATOR')}
                  className="justify-start"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  设为操作员
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchRoleChange('SUPERVISOR')}
                  className="justify-start"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  设为监督员
                </Button>
              </div>
            </div>
            
            <Separator />
            
            {/* 其他操作 */}
            <div className="space-y-2">
              <div className="text-sm font-medium">其他操作</div>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchNotification}
                  className="justify-start"
                >
                  <Mail className="h-4 w-4 mr-2" />
                  发送通知
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportSelectedUsers}
                  className="justify-start"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出数据
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchDelete}
                  className="justify-start text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  批量删除
                </Button>
              </div>
            </div>
            
            <Separator />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearSelection}
              className="w-full justify-start"
            >
              清除选择
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 用户操作日志查看组件
 */
interface UserActivityLogProps {
  userId?: number
}

export function UserActivityLog({ userId }: UserActivityLogProps) {
  const [logs, setLogs] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const loadLogs = async () => {
    if (!userId) return
    
    try {
      setIsLoading(true)
      const response = await fetch(`/api/users/${userId}/activity-logs`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setLogs(data.data || [])
      }
    } catch (error) {
      console.error('加载操作日志失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  React.useEffect(() => {
    if (userId) {
      loadLogs()
    }
  }, [userId])

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Activity className="h-4 w-4 mr-2" />
          查看日志
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>用户操作日志</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="text-center py-4">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">加载中...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-4">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm text-muted-foreground">暂无操作日志</p>
            </div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="border-l-2 border-muted pl-4 pb-4">
                <div className="flex items-center justify-between">
                  <div className="font-medium">{log.action}</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(log.createdAt).toLocaleString()}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {log.description}
                </div>
                {log.ipAddress && (
                  <div className="text-xs text-muted-foreground mt-1">
                    IP: {log.ipAddress}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
