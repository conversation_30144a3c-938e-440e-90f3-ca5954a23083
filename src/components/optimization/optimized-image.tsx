'use client'

import { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { Loader2 } from 'lucide-react'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
  lazy?: boolean
}

/**
 * 优化的图片组件
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  style,
  onLoad,
  onError,
  lazy = true
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isVisible, setIsVisible] = useState(!lazy || priority)
  const imgRef = useRef<HTMLDivElement>(null)

  // 懒加载逻辑
  useEffect(() => {
    if (!lazy || priority || isVisible) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry && entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [lazy, priority, isVisible])

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }

  // 生成模糊占位符
  const generateBlurDataURL = (w: number = 10, h: number = 10) => {
    const canvas = document.createElement('canvas')
    canvas.width = w
    canvas.height = h
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.fillStyle = 'hsl(var(--muted))'
      ctx.fillRect(0, 0, w, h)
    }
    return canvas.toDataURL()
  }

  const defaultBlurDataURL = blurDataURL || generateBlurDataURL(width || 10, height || 10)

  if (!isVisible) {
    return (
      <div
        ref={imgRef}
        className={`bg-gray-100 ${className}`}
        style={{
          width: fill ? '100%' : width,
          height: fill ? '100%' : height,
          ...style
        }}
      >
        <div className="flex items-center justify-center h-full">
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    )
  }

  if (hasError) {
    return (
      <div
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        style={{
          width: fill ? '100%' : width,
          height: fill ? '100%' : height,
          ...style
        }}
      >
        <div className="text-gray-400 text-sm">图片加载失败</div>
      </div>
    )
  }

  return (
    <div ref={imgRef} className={`relative ${className}`} style={style}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      )}
      
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={placeholder === 'blur' ? defaultBlurDataURL : undefined}
        sizes={sizes}
        className={`transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          objectFit: 'cover',
          objectPosition: 'center'
        }}
      />
    </div>
  )
}

/**
 * 响应式图片组件
 */
interface ResponsiveImageProps extends OptimizedImageProps {
  breakpoints?: {
    sm?: string
    md?: string
    lg?: string
    xl?: string
  }
}

export function ResponsiveImage({
  breakpoints = {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px'
  },
  sizes,
  ...props
}: ResponsiveImageProps) {
  const responsiveSizes = sizes || `
    (max-width: ${breakpoints.sm}) 100vw,
    (max-width: ${breakpoints.md}) 50vw,
    (max-width: ${breakpoints.lg}) 33vw,
    25vw
  `

  return <OptimizedImage {...props} sizes={responsiveSizes} />
}

/**
 * 头像图片组件
 */
interface AvatarImageProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallback?: string
  className?: string
}

export function AvatarImage({
  src,
  alt,
  size = 'md',
  fallback,
  className = ''
}: AvatarImageProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }

  const sizePixels = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 96
  }

  const [hasError, setHasError] = useState(false)

  if (!src || hasError) {
    return (
      <div className={`${sizeClasses[size]} rounded-full bg-gray-200 flex items-center justify-center ${className}`}>
        <span className="text-gray-500 font-medium">
          {fallback || alt.charAt(0).toUpperCase()}
        </span>
      </div>
    )
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={sizePixels[size]}
      height={sizePixels[size]}
      className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      onError={() => setHasError(true)}
      quality={90}
    />
  )
}

/**
 * 图片画廊组件
 */
interface ImageGalleryProps {
  images: Array<{
    src: string
    alt: string
    caption?: string
  }>
  columns?: number
  gap?: number
  className?: string
}

export function ImageGallery({
  images,
  columns = 3,
  gap = 4,
  className = ''
}: ImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<number | null>(null)

  return (
    <>
      <div
        className={`grid gap-${gap} ${className}`}
        style={{
          gridTemplateColumns: `repeat(${columns}, 1fr)`
        }}
      >
        {images.map((image, index) => (
          <div
            key={index}
            className="cursor-pointer group"
            onClick={() => setSelectedImage(index)}
          >
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              width={300}
              height={200}
              className="w-full h-48 object-cover rounded-lg group-hover:opacity-90 transition-opacity"
              lazy={true}
            />
            {image.caption && (
              <p className="mt-2 text-sm text-gray-600">{image.caption}</p>
            )}
          </div>
        ))}
      </div>

      {/* 图片预览模态框 */}
      {selectedImage !== null && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setSelectedImage(null)}
        >
          <div className="max-w-4xl max-h-full p-4">
            <OptimizedImage
              src={images[selectedImage]?.src || ''}
              alt={images[selectedImage]?.alt || ''}
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain"
              priority={true}
            />
            {images[selectedImage]?.caption && (
              <p className="mt-4 text-white text-center">
                {images[selectedImage]?.caption}
              </p>
            )}
          </div>
        </div>
      )}
    </>
  )
}

/**
 * 图片预加载器
 */
export class ImagePreloader {
  private static cache = new Map<string, HTMLImageElement>()
  
  static preload(src: string): Promise<void> {
    if (this.cache.has(src)) {
      return Promise.resolve()
    }
    
    return new Promise<void>((resolve, reject) => {
      if (typeof window === 'undefined') {
        resolve()
        return
      }

      const img = new window.Image()
      img.onload = () => {
        this.cache.set(src, img)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }
  
  static preloadMultiple(sources: string[]): Promise<void[]> {
    return Promise.all(sources.map(src => this.preload(src)))
  }
  
  static isPreloaded(src: string): boolean {
    return this.cache.has(src)
  }
  
  static clear(): void {
    this.cache.clear()
  }
  
  static getStats(): { preloadedCount: number; preloadedImages: string[] } {
    return {
      preloadedCount: this.cache.size,
      preloadedImages: Array.from(this.cache.keys())
    }
  }
}
