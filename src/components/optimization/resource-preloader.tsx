'use client'

import { useEffect } from 'react'
import Head from 'next/head'

interface ResourcePreloaderProps {
  criticalResources?: string[]
  preloadFonts?: string[]
  preloadImages?: string[]
  preloadScripts?: string[]
  dnsPrefetch?: string[]
}

/**
 * 资源预加载组件
 */
export function ResourcePreloader({
  criticalResources = [],
  preloadFonts = [],
  preloadImages = [],
  preloadScripts = [],
  dnsPrefetch = []
}: ResourcePreloaderProps) {
  
  useEffect(() => {
    // 预加载关键路由
    const criticalRoutes = [
      '/dashboard',
      '/medical-cases',
      '/analytics',
      '/supervision-rules'
    ]
    
    criticalRoutes.forEach(route => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = route
      document.head.appendChild(link)
    })
    
    // 预连接到外部域名
    const externalDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net'
    ]
    
    externalDomains.forEach(domain => {
      const link = document.createElement('link')
      link.rel = 'preconnect'
      link.href = domain
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })
    
    // 预加载关键CSS
    const criticalCSS = [
      '/_next/static/css/app.css'
    ]
    
    criticalCSS.forEach(css => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = css
      link.as = 'style'
      document.head.appendChild(link)
    })
    
  }, [])

  return (
    <Head>
      {/* DNS预解析 */}
      {dnsPrefetch.map((domain, index) => (
        <link key={`dns-${index}`} rel="dns-prefetch" href={domain} />
      ))}
      
      {/* 预连接 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* 预加载字体 */}
      {preloadFonts.map((font, index) => (
        <link
          key={`font-${index}`}
          rel="preload"
          href={font}
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
      ))}
      
      {/* 预加载图片 */}
      {preloadImages.map((image, index) => (
        <link
          key={`image-${index}`}
          rel="preload"
          href={image}
          as="image"
        />
      ))}
      
      {/* 预加载脚本 */}
      {preloadScripts.map((script, index) => (
        <link
          key={`script-${index}`}
          rel="preload"
          href={script}
          as="script"
        />
      ))}
      
      {/* 预加载关键资源 */}
      {criticalResources.map((resource, index) => (
        <link
          key={`critical-${index}`}
          rel="preload"
          href={resource}
          as="fetch"
          crossOrigin="anonymous"
        />
      ))}
      
      {/* 关键CSS内联 */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* 关键CSS - 首屏渲染优化 */
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
          }
          
          .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
          }
          
          @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
          }
          
          .fade-in {
            animation: fadeIn 0.3s ease-in-out;
          }
          
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `
      }} />
      
      {/* Web App Manifest */}
      <link rel="manifest" href="/manifest.json" />
      
      {/* PWA相关meta标签 */}
      <meta name="theme-color" content="hsl(var(--primary))" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="医保监管" />
      
      {/* 苹果设备图标 */}
      <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
      <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
      
      {/* 性能优化meta标签 */}
      <meta httpEquiv="x-dns-prefetch-control" content="on" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="format-detection" content="email=no" />
      <meta name="format-detection" content="address=no" />
      
      {/* 安全相关meta标签 */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta name="referrer" content="strict-origin-when-cross-origin" />
    </Head>
  )
}

/**
 * Service Worker注册组件
 */
export function ServiceWorkerRegistration() {
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then(registration => {
          console.log('✅ Service Worker 注册成功:', registration.scope)
          
          // 检查更新
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // 有新版本可用
                  if (confirm('发现新版本，是否立即更新？')) {
                    newWorker.postMessage({ type: 'SKIP_WAITING' })
                    window.location.reload()
                  }
                }
              })
            }
          })
        })
        .catch(error => {
          console.error('❌ Service Worker 注册失败:', error)
        })
      
      // 监听Service Worker控制器变化
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload()
      })
    }
  }, [])

  return null
}

/**
 * 预加载管理器
 */
export class PreloadManager {
  private static preloadedResources = new Set<string>()
  
  /**
   * 预加载资源
   */
  static preload(url: string, as: string = 'fetch', crossOrigin?: string) {
    if (this.preloadedResources.has(url)) {
      return
    }
    
    this.preloadedResources.add(url)
    
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    link.as = as
    if (crossOrigin) {
      link.crossOrigin = crossOrigin
    }
    
    document.head.appendChild(link)
  }
  
  /**
   * 预获取资源
   */
  static prefetch(url: string) {
    if (this.preloadedResources.has(url)) {
      return
    }
    
    this.preloadedResources.add(url)
    
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = url
    
    document.head.appendChild(link)
  }
  
  /**
   * 预连接到域名
   */
  static preconnect(url: string, crossOrigin?: string) {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = url
    if (crossOrigin) {
      link.crossOrigin = crossOrigin
    }
    
    document.head.appendChild(link)
  }
  
  /**
   * 清理预加载资源
   */
  static clear() {
    this.preloadedResources.clear()
  }
  
  /**
   * 获取预加载统计
   */
  static getStats() {
    return {
      preloadedCount: this.preloadedResources.size,
      preloadedResources: Array.from(this.preloadedResources)
    }
  }
}

/**
 * 智能预加载Hook
 */
export function useSmartPreload() {
  useEffect(() => {
    // 在空闲时间预加载非关键资源
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        // 预加载图表库
        PreloadManager.preload('/_next/static/chunks/recharts.js', 'script')
        
        // 预加载常用页面
        PreloadManager.prefetch('/analytics')
        PreloadManager.prefetch('/supervision-rules')
        PreloadManager.prefetch('/knowledge-base/documents')
      })
    }
    
    // 监听网络状态
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      // 在快速网络下预加载更多资源
      if (connection.effectiveType === '4g') {
        PreloadManager.preload('/api/analytics/dashboard', 'fetch')
        PreloadManager.preload('/api/medical-cases/stats', 'fetch')
      }
    }
  }, [])
  
  return {
    preload: PreloadManager.preload,
    prefetch: PreloadManager.prefetch,
    preconnect: PreloadManager.preconnect
  }
}
