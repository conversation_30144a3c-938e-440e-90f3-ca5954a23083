'use client'

/**
 * Monaco Editor 规则编辑器
 * 
 * @description 基于Monaco Editor的专业代码编辑器，支持语法高亮、智能补全和实时验证
 * @features 
 * - SQL/DSL/JavaScript语法高亮
 * - 智能代码补全
 * - 实时语法检查
 * - 代码格式化
 * - 错误提示
 */

import { useState, useEffect, useRef, useCallback } from 'react'
import { Editor, OnMount, OnChange } from '@monaco-editor/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Code, 
  Wand2,
  Settings,
  Eye,
  Save,
  RotateCcw
} from 'lucide-react'
import { toast } from 'sonner'
import type { editor } from 'monaco-editor'

interface MonacoRuleEditorProps {
  value: string
  onChange: (value: string) => void
  ruleType: 'SQL' | 'DSL' | 'JAVASCRIPT'
  height?: number
  readOnly?: boolean
  onValidate?: (isValid: boolean, errors: string[]) => void
  onTest?: (code: string) => Promise<any>
  className?: string
}

interface ValidationError {
  line: number
  column: number
  message: string
  severity: 'error' | 'warning' | 'info'
}

export function MonacoRuleEditor({
  value,
  onChange,
  ruleType,
  height = 400,
  readOnly = false,
  onValidate,
  onTest,
  className
}: MonacoRuleEditorProps) {
  const [isValidating, setIsValidating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [isValid, setIsValid] = useState(true)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [showPreview, setShowPreview] = useState(false)
  
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null)
  const monacoRef = useRef<typeof import('monaco-editor') | null>(null)

  /**
   * 获取语言配置
   */
  const getLanguageConfig = useCallback(() => {
    switch (ruleType) {
      case 'SQL':
        return {
          language: 'sql',
          theme: 'vs-dark',
          suggestions: [
            'SELECT', 'FROM', 'WHERE', 'JOIN', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN',
            'GROUP BY', 'ORDER BY', 'HAVING', 'LIMIT', 'OFFSET',
            'COUNT', 'SUM', 'AVG', 'MAX', 'MIN',
            'MEDICAL_CASE', 'SUPERVISION_RULE', 'USER_MANAGEMENT_USER',
            'CASE_NUMBER', 'PATIENT_NAME', 'TOTAL_COST', 'CASE_TYPE'
          ]
        }
      case 'DSL':
        return {
          language: 'yaml',
          theme: 'vs-dark',
          suggestions: [
            'rules:', 'conditions:', 'actions:', 'when:', 'then:',
            'field:', 'operator:', 'value:', 'type:',
            'eq', 'ne', 'gt', 'lt', 'gte', 'lte', 'in', 'contains'
          ]
        }
      case 'JAVASCRIPT':
        return {
          language: 'javascript',
          theme: 'vs-dark',
          suggestions: [
            'function', 'return', 'if', 'else', 'for', 'while',
            'const', 'let', 'var', 'async', 'await',
            'medicalCase', 'totalCost', 'caseType', 'patientAge'
          ]
        }
      default:
        return { language: 'plaintext', theme: 'vs-light', suggestions: [] }
    }
  }, [ruleType])

  /**
   * Monaco Editor 挂载回调
   */
  const handleEditorDidMount: OnMount = useCallback((editor, monaco) => {
    editorRef.current = editor
    monacoRef.current = monaco

    const config = getLanguageConfig()

    // 注册自定义补全提供者
    monaco.languages.registerCompletionItemProvider(config.language, {
      provideCompletionItems: (model, position) => {
        const suggestions = config.suggestions.map((suggestion, index) => ({
          label: suggestion,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: suggestion,
          range: {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: position.column,
            endColumn: position.column
          }
        }))

        return { suggestions }
      }
    })

    // 配置编辑器选项
    editor.updateOptions({
      fontSize: 14,
      lineHeight: 20,
      minimap: { enabled: height > 300 },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      wordWrap: 'on',
      lineNumbers: 'on',
      glyphMargin: true,
      folding: true,
      lineDecorationsWidth: 10,
      lineNumbersMinChars: 3,
      renderLineHighlight: 'all',
      selectOnLineNumbers: true,
      roundedSelection: false,
      readOnly,
      cursorStyle: 'line',
    })

    // 添加快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave()
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyR, () => {
      handleTest()
    })

    // 实时验证
    editor.onDidChangeModelContent(() => {
      validateCode(editor.getValue())
    })

  }, [getLanguageConfig, height, readOnly])

  /**
   * 代码变更回调
   */
  const handleEditorChange: OnChange = useCallback((value) => {
    if (value !== undefined) {
      onChange(value)
    }
  }, [onChange])

  /**
   * 验证代码
   */
  const validateCode = useCallback(async (code: string) => {
    if (!code.trim()) {
      setValidationErrors([])
      setIsValid(true)
      onValidate?.(true, [])
      return
    }

    setIsValidating(true)
    
    try {
      const response = await fetch('/api/rule-engine/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code,
          type: ruleType
        })
      })

      const result = await response.json()
      
      if (result.success) {
        const errors = result.data?.errors || []
        setValidationErrors(errors)
        setIsValid(errors.length === 0)
        onValidate?.(errors.length === 0, errors.map((e: ValidationError) => e.message))

        // 在编辑器中显示错误标记
        if (editorRef.current && monacoRef.current) {
          const markers = errors.map((error: ValidationError) => ({
            startLineNumber: error.line,
            startColumn: error.column,
            endLineNumber: error.line,
            endColumn: error.column + 10,
            message: error.message,
            severity: error.severity === 'error' 
              ? monacoRef.current!.MarkerSeverity.Error
              : monacoRef.current!.MarkerSeverity.Warning
          }))
          
          monacoRef.current.editor.setModelMarkers(
            editorRef.current.getModel()!,
            'validation',
            markers
          )
        }
      }
    } catch (error) {
      console.error('代码验证失败:', error)
    } finally {
      setIsValidating(false)
    }
  }, [ruleType, onValidate])

  /**
   * 测试代码
   */
  const handleTest = useCallback(async () => {
    if (!onTest || !value.trim()) {
      toast.error('请先输入代码内容')
      return
    }

    setIsTesting(true)
    try {
      const result = await onTest(value)
      setTestResult(result)
      setShowPreview(true)
      toast.success('代码测试完成')
    } catch (error) {
      toast.error('代码测试失败')
      console.error('测试失败:', error)
    } finally {
      setIsTesting(false)
    }
  }, [onTest, value])

  /**
   * 保存代码
   */
  const handleSave = useCallback(() => {
    toast.success('代码已保存')
  }, [])

  /**
   * 格式化代码
   */
  const handleFormat = useCallback(() => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument')?.run()
      toast.success('代码已格式化')
    }
  }, [])

  /**
   * 重置代码
   */
  const handleReset = useCallback(() => {
    if (confirm('确定要重置代码吗？此操作不可恢复。')) {
      onChange('')
      toast.success('代码已重置')
    }
  }, [onChange])

  const config = getLanguageConfig()

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 编辑器工具栏 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              <CardTitle className="text-lg">
                {ruleType} 规则编辑器
              </CardTitle>
              <Badge variant={isValid ? 'default' : 'destructive'}>
                {isValid ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />语法正确</>
                ) : (
                  <><AlertCircle className="h-3 w-3 mr-1" />语法错误</>
                )}
              </Badge>
              {isValidating && (
                <Badge variant="secondary">验证中...</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleFormat}
                disabled={readOnly}
              >
                <Wand2 className="h-4 w-4 mr-1" />
                格式化
              </Button>
              
              {onTest && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTest}
                  disabled={isTesting || !isValid}
                >
                  {isTesting ? (
                    <>测试中...</>
                  ) : (
                    <><Play className="h-4 w-4 mr-1" />测试</>
                  )}
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                <Eye className="h-4 w-4 mr-1" />
                {showPreview ? '隐藏' : '预览'}
              </Button>
              
              {!readOnly && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSave}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    保存
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleReset}
                  >
                    <RotateCcw className="h-4 w-4 mr-1" />
                    重置
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="editor" className="space-y-4">
            <TabsList>
              <TabsTrigger value="editor">代码编辑</TabsTrigger>
              {showPreview && <TabsTrigger value="preview">预览结果</TabsTrigger>}
              {validationErrors.length > 0 && (
                <TabsTrigger value="errors">
                  错误信息 ({validationErrors.length})
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="editor">
              <div className="border rounded-md overflow-hidden">
                <Editor
                  height={height}
                  language={config.language}
                  theme={config.theme}
                  value={value}
                  onChange={handleEditorChange}
                  onMount={handleEditorDidMount}
                  options={{
                    selectOnLineNumbers: true,
                    roundedSelection: false,
                    readOnly,
                    cursorStyle: 'line',
                    automaticLayout: true,
                  }}
                />
              </div>
            </TabsContent>

            {showPreview && (
              <TabsContent value="preview">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">测试结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {testResult ? (
                      <pre className="bg-muted p-4 rounded-md text-sm overflow-auto">
                        {JSON.stringify(testResult, null, 2)}
                      </pre>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        点击"测试"按钮查看执行结果
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {validationErrors.length > 0 && (
              <TabsContent value="errors">
                <div className="space-y-2">
                  {validationErrors.map((error, index) => (
                    <Alert key={index} variant={error.severity === 'error' ? 'destructive' : 'default'}>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>第 {error.line} 行，第 {error.column} 列:</strong> {error.message}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
