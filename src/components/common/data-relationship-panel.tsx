'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ExternalLink, 
  FileText, 
  Shield, 
  Users, 
  BookOpen,
  Activity,
  TrendingUp,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { useCrossModuleContext } from '@/hooks/use-operation-history'
import { formatDate, formatCurrency } from '@/lib/format-utils'

/**
 * 关联数据类型
 */
export interface RelatedData {
  id: string
  type: 'medical-case' | 'rule' | 'user' | 'document' | 'execution' | 'audit'
  title: string
  description?: string
  status?: string
  metadata?: Record<string, any>
  createdAt?: string
  updatedAt?: string
  relationshipType?: 'direct' | 'indirect' | 'historical'
  relationshipStrength?: number
}

/**
 * 关联数据分组
 */
interface RelatedDataGroup {
  type: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  items: RelatedData[]
  count: number
}

interface DataRelationshipPanelProps {
  primaryData: {
    id: string
    type: string
    title: string
  }
  className?: string
  maxItems?: number
  showMetadata?: boolean
}

/**
 * 数据关联展示面板
 * 显示与主要数据相关的所有关联数据
 */
export function DataRelationshipPanel({ 
  primaryData, 
  className,
  maxItems = 10,
  showMetadata = false
}: DataRelationshipPanelProps) {
  const [relatedData, setRelatedData] = useState<RelatedData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { navigateWithContext } = useCrossModuleContext()

  /**
   * 加载关联数据
   */
  const loadRelatedData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/relationships/${primaryData.type}/${primaryData.id}`)
      if (!response.ok) {
        throw new Error('获取关联数据失败')
      }

      const result = await response.json()
      if (result.success) {
        setRelatedData(result.data || [])
      } else {
        throw new Error(result.message || '获取关联数据失败')
      }
    } catch (error) {
      console.error('加载关联数据失败:', error)
      setError(error instanceof Error ? error.message : '加载失败')
    } finally {
      setIsLoading(false)
    }
  }, [primaryData.id, primaryData.type])

  useEffect(() => {
    loadRelatedData()
  }, [loadRelatedData])

  /**
   * 按类型分组关联数据
   */
  const groupedData: RelatedDataGroup[] = [
    {
      type: 'medical-case',
      label: '医疗案例',
      icon: FileText,
      items: relatedData.filter(item => item.type === 'medical-case'),
      count: relatedData.filter(item => item.type === 'medical-case').length
    },
    {
      type: 'rule',
      label: '监管规则',
      icon: Shield,
      items: relatedData.filter(item => item.type === 'rule'),
      count: relatedData.filter(item => item.type === 'rule').length
    },
    {
      type: 'execution',
      label: '执行记录',
      icon: Activity,
      items: relatedData.filter(item => item.type === 'execution'),
      count: relatedData.filter(item => item.type === 'execution').length
    },
    {
      type: 'document',
      label: '知识文档',
      icon: BookOpen,
      items: relatedData.filter(item => item.type === 'document'),
      count: relatedData.filter(item => item.type === 'document').length
    },
    {
      type: 'user',
      label: '相关用户',
      icon: Users,
      items: relatedData.filter(item => item.type === 'user'),
      count: relatedData.filter(item => item.type === 'user').length
    }
  ].filter(group => group.count > 0)

  /**
   * 导航到关联项目
   */
  const navigateToRelated = useCallback((item: RelatedData) => {
    const pathMap = {
      'medical-case': `/medical-cases/${item.id}`,
      'rule': `/supervision-rules/${item.id}`,
      'execution': `/supervision-rules/executions/${item.id}`,
      'document': `/knowledge-base/${item.id}`,
      'user': `/users/${item.id}`,
      'audit': `/audit/${item.id}`
    }

    const targetPath = pathMap[item.type]
    if (targetPath) {
      navigateWithContext(targetPath, {
        returnUrl: window.location.pathname,
        preserveState: true,
        metadata: {
          fromRelationship: true,
          primaryData,
          relatedItem: item
        }
      })
    }
  }, [navigateWithContext, primaryData])

  /**
   * 获取关系强度颜色
   */
  const getRelationshipColor = (strength?: number) => {
    if (!strength) return 'bg-gray-100'
    if (strength >= 0.8) return 'bg-red-100 text-red-800'
    if (strength >= 0.6) return 'bg-orange-100 text-orange-800'
    if (strength >= 0.4) return 'bg-yellow-100 text-yellow-800'
    return 'bg-green-100 text-green-800'
  }

  /**
   * 渲染关联项目
   */
  const renderRelatedItem = (item: RelatedData) => (
    <div
      key={item.id}
      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors"
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium truncate">{item.title}</span>
          {item.relationshipStrength && (
            <Badge 
              variant="outline" 
              className={`text-xs ${getRelationshipColor(item.relationshipStrength)}`}
            >
              {Math.round(item.relationshipStrength * 100)}%
            </Badge>
          )}
          {item.status && (
            <Badge variant="secondary" className="text-xs">
              {item.status}
            </Badge>
          )}
        </div>
        
        {item.description && (
          <p className="text-sm text-muted-foreground truncate">
            {item.description}
          </p>
        )}
        
        {showMetadata && item.metadata && (
          <div className="flex gap-2 mt-1">
            {Object.entries(item.metadata).slice(0, 2).map(([key, value]) => (
              <span key={key} className="text-xs text-muted-foreground">
                {key}: {String(value)}
              </span>
            ))}
          </div>
        )}
        
        {item.updatedAt && (
          <div className="flex items-center gap-1 mt-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {formatDate(item.updatedAt)}
            </span>
          </div>
        )}
      </div>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => navigateToRelated(item)}
        className="ml-2 shrink-0"
      >
        查看
        <ExternalLink className="ml-1 h-3 w-3" />
      </Button>
    </div>
  )

  /**
   * 渲染加载状态
   */
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>相关数据</CardTitle>
          <CardDescription>加载关联数据中...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-8 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  /**
   * 渲染错误状态
   */
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>相关数据</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadRelatedData}
            className="mt-3"
          >
            重试
          </Button>
        </CardContent>
      </Card>
    )
  }

  /**
   * 渲染空状态
   */
  if (relatedData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>相关数据</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <TrendingUp className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              暂无相关数据
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          相关数据
        </CardTitle>
        <CardDescription>
          与 {primaryData.title} 相关的 {relatedData.length} 项数据
        </CardDescription>
      </CardHeader>
      <CardContent>
        {groupedData.length === 1 ? (
          // 单一类型，直接显示列表
          <ScrollArea className="h-96">
            <div className="space-y-2">
              {groupedData[0]?.items.slice(0, maxItems).map(renderRelatedItem)}
              {(groupedData[0]?.items.length || 0) > maxItems && (
                <div className="text-center pt-2">
                  <Button variant="outline" size="sm">
                    查看更多 ({(groupedData[0]?.items.length || 0) - maxItems} 项)
                  </Button>
                </div>
              )}
            </div>
          </ScrollArea>
        ) : (
          // 多种类型，使用标签页
          <Tabs defaultValue={groupedData[0]?.type} className="w-full">
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3">
              {groupedData.slice(0, 3).map((group) => {
                const Icon = group.icon
                return (
                  <TabsTrigger key={group.type} value={group.type} className="flex items-center gap-1">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{group.label}</span>
                    <Badge variant="secondary" className="ml-1">
                      {group.count}
                    </Badge>
                  </TabsTrigger>
                )
              })}
            </TabsList>
            
            {groupedData.map((group) => (
              <TabsContent key={group.type} value={group.type}>
                <ScrollArea className="h-80">
                  <div className="space-y-2">
                    {group.items.slice(0, maxItems).map(renderRelatedItem)}
                    {group.items.length > maxItems && (
                      <div className="text-center pt-2">
                        <Button variant="outline" size="sm">
                          查看更多 ({group.items.length - maxItems} 项)
                        </Button>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}
