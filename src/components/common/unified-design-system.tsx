'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

// 统一的企业级设计模式组件

/**
 * 企业级列表页面布局组件
 * 适用于所有管理模块的主列表页面
 */
interface EnterpriseListLayoutProps {
  title: string
  description?: string
  children: React.ReactNode
  actions?: React.ReactNode
  statistics?: React.ReactNode
  filters?: React.ReactNode
  className?: string
}

export function EnterpriseListLayout({
  title,
  description,
  children,
  actions,
  statistics,
  filters,
  className
}: EnterpriseListLayoutProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {/* 统一的页面标题区域 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>

      {/* 统计卡片区域 */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statistics}
        </div>
      )}

      {/* 筛选器区域 */}
      {filters && (
        <Card>
          <CardContent className="p-6">
            {filters}
          </CardContent>
        </Card>
      )}

      {/* 主要内容区域 */}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

/**
 * 统一的详情查看组件
 * 根据内容复杂度自动选择模态框或抽屉
 */
interface UnifiedDetailViewProps {
  isOpen: boolean
  onClose: () => void
  title: string
  data: any
  renderContent: (data: any) => React.ReactNode
  actions?: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
  type?: 'modal' | 'drawer'
}

export function UnifiedDetailView({
  isOpen,
  onClose,
  title,
  data,
  renderContent,
  actions,
  size = 'lg',
  type = 'modal'
}: UnifiedDetailViewProps) {
  if (type === 'drawer') {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent className={cn(
          "w-full sm:max-w-md",
          size === 'lg' && "sm:max-w-lg",
          size === 'xl' && "sm:max-w-xl"
        )}>
          <SheetHeader>
            <SheetTitle>{title}</SheetTitle>
          </SheetHeader>
          <div className="mt-6 space-y-6">
            {renderContent(data)}
            {actions && (
              <div className="flex justify-end gap-2 pt-4 border-t">
                {actions}
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn(
        "max-w-md",
        size === 'lg' && "max-w-2xl",
        size === 'xl' && "max-w-4xl",
        "max-h-[80vh] overflow-y-auto"
      )}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {renderContent(data)}
          {actions && (
            <div className="flex justify-end gap-2 pt-4 border-t">
              {actions}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

/**
 * 统一的表单编辑组件
 * 根据表单复杂度选择模态框或独立页面
 */
interface UnifiedFormEditProps {
  isOpen: boolean
  onClose: () => void
  title: string
  data?: any
  renderForm: (data?: any) => React.ReactNode
  onSubmit: (formData: any) => void
  isSubmitting?: boolean
  size?: 'sm' | 'md' | 'lg' | 'xl'
  type?: 'modal' | 'page'
}

export function UnifiedFormEdit({
  isOpen,
  onClose,
  title,
  data,
  renderForm,
  onSubmit,
  isSubmitting = false,
  size = 'lg',
  type = 'modal'
}: UnifiedFormEditProps) {
  if (type === 'page') {
    // 独立页面模式 - 返回完整页面布局
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">{title}</h1>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            {renderForm(data)}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn(
        "max-w-md",
        size === 'lg' && "max-w-2xl",
        size === 'xl' && "max-w-4xl",
        "max-h-[80vh] overflow-y-auto"
      )}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {renderForm(data)}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              取消
            </Button>
            <Button onClick={() => onSubmit(data)} disabled={isSubmitting}>
              {isSubmitting ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

/**
 * 统一的批量操作工具栏
 */
interface UnifiedBatchToolbarProps {
  selectedCount: number
  onClearSelection: () => void
  actions: Array<{
    label: string
    icon?: React.ComponentType<{ className?: string }>
    onClick: () => void
    variant?: 'default' | 'outline' | 'destructive'
    disabled?: boolean
  }>
  className?: string
}

export function UnifiedBatchToolbar({
  selectedCount,
  onClearSelection,
  actions,
  className
}: UnifiedBatchToolbarProps) {
  if (selectedCount === 0) return null

  return (
    <Card className={cn("border-dashed border-primary/50 bg-primary/5", className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Badge variant="secondary" className="px-3 py-1">
              已选择 {selectedCount} 项
            </Badge>
            <Button variant="ghost" size="sm" onClick={onClearSelection}>
              清除选择
            </Button>
          </div>
          <div className="flex items-center gap-2">
            {actions.map((action, index) => {
              const Icon = action.icon
              return (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size="sm"
                  onClick={action.onClick}
                  disabled={action.disabled}
                >
                  {Icon && <Icon className="h-4 w-4 mr-2" />}
                  {action.label}
                </Button>
              )
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 统一的操作按钮组
 */
interface UnifiedActionButtonsProps {
  actions: Array<{
    label: string
    icon?: React.ComponentType<{ className?: string }>
    onClick: () => void
    variant?: 'default' | 'outline' | 'ghost' | 'destructive'
    size?: 'sm' | 'md' | 'lg'
    disabled?: boolean
  }>
  className?: string
}

export function UnifiedActionButtons({ actions, className }: UnifiedActionButtonsProps) {
  return (
    <div className={cn("flex items-center gap-1", className)}>
      {actions.map((action, index) => {
        const Icon = action.icon
        return (
          <Button
            key={index}
            variant={action.variant || 'ghost'}
            size={action.size === 'md' ? 'default' : (action.size || 'sm')}
            onClick={action.onClick}
            disabled={action.disabled}
          >
            {Icon && <Icon className="h-4 w-4" />}
            <span className="sr-only">{action.label}</span>
          </Button>
        )
      })}
    </div>
  )
}

/**
 * 统一的状态标签组件
 */
interface UnifiedStatusBadgeProps {
  status: string
  variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  colorMap?: Record<string, string>
  className?: string
}

export function UnifiedStatusBadge({
  status,
  variant = 'outline',
  colorMap,
  className
}: UnifiedStatusBadgeProps) {
  const getStatusColor = (status: string) => {
    if (colorMap && colorMap[status]) {
      return colorMap[status]
    }
    
    // 默认颜色映射
    const defaultColors: Record<string, string> = {
      'active': 'bg-green-50 text-green-700 border-green-200',
      'inactive': 'bg-gray-50 text-gray-700 border-gray-200',
      'pending': 'bg-yellow-50 text-yellow-700 border-yellow-200',
      'error': 'bg-red-50 text-red-700 border-red-200',
      'success': 'bg-green-50 text-green-700 border-green-200',
      'warning': 'bg-orange-50 text-orange-700 border-orange-200'
    }
    
    return defaultColors[status.toLowerCase()] || defaultColors['inactive']
  }

  return (
    <Badge 
      variant={variant} 
      className={cn(getStatusColor(status), className)}
    >
      {status}
    </Badge>
  )
}

/**
 * 企业级设计模式配置
 */
export const ENTERPRISE_DESIGN_PATTERNS = {
  // 简单操作：使用模态框
  SIMPLE_OPERATIONS: {
    view: { type: 'modal', size: 'lg' },
    edit: { type: 'modal', size: 'lg' },
    delete: { type: 'dialog', size: 'sm' }
  },
  
  // 复杂操作：使用独立页面
  COMPLEX_OPERATIONS: {
    create: { type: 'page', route: '/[module]/new' },
    complexEdit: { type: 'page', route: '/[module]/[id]/edit' },
    workflow: { type: 'page', route: '/[module]/[id]/workflow' }
  },
  
  // 数据展示：根据数据量选择
  DATA_DISPLAY: {
    list: { type: 'table', pagination: true },
    detail: { type: 'modal', size: 'xl' },
    comparison: { type: 'drawer', size: 'xl' }
  }
} as const
