'use client'

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Play, 
  Pause, 
  Square, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  BarChart3,
  FileText,
  Zap
} from 'lucide-react'
import { useOperationTracker } from '@/hooks/use-operation-history'
import { toast } from 'sonner'

/**
 * 批量操作预览信息
 */
export interface BatchPreview {
  estimatedTime: string
  affectedItems: number
  potentialIssues: string[]
  recommendations: string[]
  riskLevel: 'low' | 'medium' | 'high'
  breakdown: {
    category: string
    count: number
    estimatedTime: number
  }[]
}

/**
 * 批量操作结果
 */
export interface BatchResult {
  item: any
  status: 'success' | 'error' | 'skipped' | 'warning'
  result?: any
  error?: string
  warning?: string
  duration?: number
}

/**
 * 批量操作状态
 */
interface BatchOperationState {
  status: 'idle' | 'previewing' | 'running' | 'paused' | 'completed' | 'failed'
  progress: number
  currentItem?: string
  results: BatchResult[]
  startTime?: Date
  endTime?: Date
}

interface SmartBatchOperationProps {
  selectedItems: any[]
  operation: {
    id: string
    name: string
    description: string
    icon?: React.ComponentType<{ className?: string }>
  }
  onExecute: (items: any[], params?: any) => Promise<BatchResult[]>
  onPreview?: (items: any[]) => Promise<BatchPreview>
  className?: string
  maxConcurrency?: number
}

/**
 * 智能批量操作组件
 * 提供操作预览、进度跟踪、错误处理等功能
 */
export function SmartBatchOperation({
  selectedItems,
  operation,
  onExecute,
  onPreview,
  className,
  maxConcurrency = 5
}: SmartBatchOperationProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [preview, setPreview] = useState<BatchPreview | null>(null)
  const [state, setState] = useState<BatchOperationState>({
    status: 'idle',
    progress: 0,
    results: []
  })
  
  const { trackBatchOperation } = useOperationTracker('batch-operations')

  /**
   * 生成操作预览
   */
  const generatePreview = useCallback(async () => {
    if (!onPreview) {
      // 默认预览逻辑
      const estimatedTimePerItem = 0.5 // 秒
      const totalTime = selectedItems.length * estimatedTimePerItem
      
      setPreview({
        estimatedTime: `约 ${Math.ceil(totalTime)} 秒`,
        affectedItems: selectedItems.length,
        potentialIssues: selectedItems.length > 100 ? ['大量数据操作可能影响系统性能'] : [],
        recommendations: ['建议在系统负载较低时执行'],
        riskLevel: selectedItems.length > 1000 ? 'high' : selectedItems.length > 100 ? 'medium' : 'low',
        breakdown: [
          {
            category: '数据处理',
            count: selectedItems.length,
            estimatedTime: totalTime
          }
        ]
      })
      return
    }

    try {
      setState(prev => ({ ...prev, status: 'previewing' }))
      const previewData = await onPreview(selectedItems)
      setPreview(previewData)
    } catch (error) {
      console.error('生成预览失败:', error)
      toast.error('生成预览失败')
    } finally {
      setState(prev => ({ ...prev, status: 'idle' }))
    }
  }, [selectedItems, onPreview])

  /**
   * 执行批量操作
   */
  const executeBatch = useCallback(async () => {
    try {
      setState(prev => ({
        ...prev,
        status: 'running',
        progress: 0,
        results: [],
        startTime: new Date()
      }))

      // 跟踪批量操作
      await trackBatchOperation(
        operation.id,
        operation.name,
        selectedItems.length,
        async () => {
          const results = await onExecute(selectedItems)
          
          setState(prev => ({
            ...prev,
            status: 'completed',
            progress: 100,
            results,
            endTime: new Date()
          }))

          // 显示结果摘要
          const successCount = results.filter(r => r.status === 'success').length
          const errorCount = results.filter(r => r.status === 'error').length
          
          if (errorCount === 0) {
            toast.success(`批量操作完成`, {
              description: `成功处理 ${successCount} 项`
            })
          } else {
            toast.warning(`批量操作部分完成`, {
              description: `成功 ${successCount} 项，失败 ${errorCount} 项`
            })
          }
        }
      )
    } catch (error) {
      console.error('批量操作失败:', error)
      setState(prev => ({
        ...prev,
        status: 'failed',
        endTime: new Date()
      }))
      toast.error('批量操作失败')
    }
  }, [selectedItems, operation, onExecute, trackBatchOperation])

  /**
   * 暂停/恢复操作
   */
  const togglePause = useCallback(() => {
    setState(prev => ({
      ...prev,
      status: prev.status === 'running' ? 'paused' : 'running'
    }))
  }, [])

  /**
   * 停止操作
   */
  const stopOperation = useCallback(() => {
    setState(prev => ({
      ...prev,
      status: 'completed',
      endTime: new Date()
    }))
  }, [])

  /**
   * 重置状态
   */
  const resetState = useCallback(() => {
    setState({
      status: 'idle',
      progress: 0,
      results: []
    })
    setPreview(null)
  }, [])

  // 打开对话框时生成预览
  useEffect(() => {
    if (isOpen && !preview && state.status === 'idle') {
      generatePreview()
    }
  }, [isOpen, preview, state.status, generatePreview])

  /**
   * 获取风险等级颜色
   */
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-50'
      case 'medium': return 'text-orange-600 bg-orange-50'
      case 'low': return 'text-green-600 bg-green-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  /**
   * 渲染操作预览
   */
  const renderPreview = () => {
    if (!preview) return null

    return (
      <div className="space-y-4">
        <div className={`p-4 rounded-lg ${getRiskColor(preview.riskLevel)}`}>
          <div className="flex items-center gap-2 mb-2">
            <BarChart3 className="h-4 w-4" />
            <h4 className="font-medium">操作预览</h4>
            <Badge variant="outline" className={getRiskColor(preview.riskLevel)}>
              {preview.riskLevel === 'high' ? '高风险' : 
               preview.riskLevel === 'medium' ? '中风险' : '低风险'}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">影响项目：</span>
              {preview.affectedItems} 个
            </div>
            <div>
              <span className="font-medium">预计耗时：</span>
              {preview.estimatedTime}
            </div>
          </div>
        </div>

        {preview.breakdown.length > 0 && (
          <div className="space-y-2">
            <h5 className="font-medium text-sm">操作分解</h5>
            {preview.breakdown.map((item, index) => (
              <div key={index} className="flex justify-between items-center p-2 bg-muted rounded">
                <span className="text-sm">{item.category}</span>
                <div className="text-sm text-muted-foreground">
                  {item.count} 项 · {item.estimatedTime}s
                </div>
              </div>
            ))}
          </div>
        )}

        {preview.potentialIssues.length > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <div className="font-medium">潜在问题：</div>
                {preview.potentialIssues.map((issue, index) => (
                  <div key={index} className="text-sm">• {issue}</div>
                ))}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {preview.recommendations.length > 0 && (
          <div className="space-y-1">
            <div className="font-medium text-sm">建议：</div>
            {preview.recommendations.map((rec, index) => (
              <div key={index} className="text-sm text-muted-foreground">• {rec}</div>
            ))}
          </div>
        )}
      </div>
    )
  }

  /**
   * 渲染执行进度
   */
  const renderProgress = () => {
    if (state.status === 'idle' || state.status === 'previewing') return null

    const { results, progress, currentItem, startTime, endTime } = state
    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length
    const warningCount = results.filter(r => r.status === 'warning').length

    const duration = endTime 
      ? endTime.getTime() - (startTime?.getTime() || 0)
      : Date.now() - (startTime?.getTime() || 0)

    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>执行进度</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="w-full" />
          {currentItem && (
            <p className="text-xs text-muted-foreground">
              正在处理: {currentItem}
            </p>
          )}
        </div>

        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-lg font-bold text-green-600">{successCount}</div>
            <div className="text-xs text-muted-foreground">成功</div>
          </div>
          <div className="space-y-1">
            <div className="text-lg font-bold text-red-600">{errorCount}</div>
            <div className="text-xs text-muted-foreground">失败</div>
          </div>
          <div className="space-y-1">
            <div className="text-lg font-bold text-orange-600">{warningCount}</div>
            <div className="text-xs text-muted-foreground">警告</div>
          </div>
        </div>

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>耗时: {Math.round(duration / 1000)}s</span>
          <span>剩余: {selectedItems.length - results.length} 项</span>
        </div>
      </div>
    )
  }

  const OperationIcon = operation.icon || Zap

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setIsOpen(true)}
        disabled={selectedItems.length === 0}
        className={className}
      >
        <OperationIcon className="h-4 w-4 mr-2" />
        {operation.name}
        {selectedItems.length > 0 && (
          <Badge variant="secondary" className="ml-2">
            {selectedItems.length}
          </Badge>
        )}
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <OperationIcon className="h-5 w-5" />
              {operation.name}
            </DialogTitle>
            <DialogDescription>
              {operation.description}
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="preview" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="preview">操作预览</TabsTrigger>
              <TabsTrigger value="progress" disabled={state.status === 'idle'}>
                执行进度
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="space-y-4">
              {state.status === 'previewing' ? (
                <div className="text-center py-8">
                  <Clock className="h-8 w-8 text-muted-foreground mx-auto mb-2 animate-spin" />
                  <p className="text-sm text-muted-foreground">生成预览中...</p>
                </div>
              ) : (
                renderPreview()
              )}
            </TabsContent>

            <TabsContent value="progress" className="space-y-4">
              {renderProgress()}
            </TabsContent>
          </Tabs>

          <div className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              将处理 {selectedItems.length} 个项目
            </div>
            
            <div className="flex gap-2">
              {state.status === 'idle' && (
                <>
                  <Button variant="outline" onClick={() => setIsOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={executeBatch} disabled={!preview}>
                    <Play className="h-4 w-4 mr-2" />
                    开始执行
                  </Button>
                </>
              )}
              
              {state.status === 'running' && (
                <>
                  <Button variant="outline" onClick={togglePause}>
                    <Pause className="h-4 w-4 mr-2" />
                    暂停
                  </Button>
                  <Button variant="destructive" onClick={stopOperation}>
                    <Square className="h-4 w-4 mr-2" />
                    停止
                  </Button>
                </>
              )}
              
              {state.status === 'paused' && (
                <>
                  <Button variant="outline" onClick={togglePause}>
                    <Play className="h-4 w-4 mr-2" />
                    继续
                  </Button>
                  <Button variant="destructive" onClick={stopOperation}>
                    <Square className="h-4 w-4 mr-2" />
                    停止
                  </Button>
                </>
              )}
              
              {(state.status === 'completed' || state.status === 'failed') && (
                <>
                  <Button variant="outline" onClick={resetState}>
                    重置
                  </Button>
                  <Button onClick={() => setIsOpen(false)}>
                    关闭
                  </Button>
                </>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
