'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  History, 
  Search, 
  Filter, 
  Download, 
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  User,
  FileText,
  Shield,
  Users,
  BookOpen,
  Activity
} from 'lucide-react'
import { useOperationHistory, OperationHistory } from '@/hooks/use-operation-history'
import { formatDate, formatDuration } from '@/lib/format-utils'

interface OperationHistoryPanelProps {
  className?: string
  maxItems?: number
  showFilters?: boolean
  compactMode?: boolean
}

/**
 * 操作历史面板组件
 * 显示用户的操作历史记录，支持筛选和搜索
 */
export function OperationHistoryPanel({
  className,
  maxItems = 50,
  showFilters = true,
  compactMode = false
}: OperationHistoryPanelProps) {
  const { history, clearHistory, exportHistory } = useOperationHistory()
  const [searchTerm, setSearchTerm] = useState('')
  const [moduleFilter, setModuleFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showDetails, setShowDetails] = useState<OperationHistory | null>(null)

  /**
   * 模块图标映射
   */
  const moduleIcons = {
    'medical-cases': FileText,
    'supervision-rules': Shield,
    'users': Users,
    'knowledge-base': BookOpen,
    'navigation': Activity,
    'batch-operations': Activity,
    'default': Activity
  }

  /**
   * 获取模块图标
   */
  const getModuleIcon = (module: string) => {
    return moduleIcons[module as keyof typeof moduleIcons] || moduleIcons.default
  }

  /**
   * 筛选和搜索历史记录
   */
  const filteredHistory = useMemo(() => {
    let filtered = history

    // 模块筛选
    if (moduleFilter !== 'all') {
      filtered = filtered.filter(op => op.module === moduleFilter)
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      const isSuccess = statusFilter === 'success'
      filtered = filtered.filter(op => op.success === isSuccess)
    }

    // 搜索筛选
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(op => 
        op.action.toLowerCase().includes(term) ||
        op.target.toLowerCase().includes(term) ||
        op.module.toLowerCase().includes(term)
      )
    }

    return filtered.slice(0, maxItems)
  }, [history, moduleFilter, statusFilter, searchTerm, maxItems])

  /**
   * 获取唯一模块列表
   */
  const availableModules = useMemo(() => {
    const modules = Array.from(new Set(history.map(op => op.module)))
    return modules.sort()
  }, [history])

  /**
   * 渲染操作项
   */
  const renderOperationItem = (operation: OperationHistory) => {
    const ModuleIcon = getModuleIcon(operation.module)
    
    return (
      <div
        key={operation.id}
        className={`flex items-center gap-3 p-3 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer ${
          compactMode ? 'p-2' : ''
        }`}
        onClick={() => setShowDetails(operation)}
      >
        <div className="flex items-center justify-center w-8 h-8 rounded-md bg-muted">
          <ModuleIcon className="h-4 w-4" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className={`font-medium truncate ${compactMode ? 'text-sm' : ''}`}>
              {operation.action}
            </span>
            <Badge 
              variant={operation.success ? 'default' : 'destructive'}
              className="text-xs"
            >
              {operation.success ? (
                <CheckCircle className="h-3 w-3 mr-1" />
              ) : (
                <XCircle className="h-3 w-3 mr-1" />
              )}
              {operation.success ? '成功' : '失败'}
            </Badge>
            {operation.metadata?.['batchOperation'] && (
              <Badge variant="outline" className="text-xs">
                批量操作
              </Badge>
            )}
          </div>
          
          <div className={`text-muted-foreground truncate ${compactMode ? 'text-xs' : 'text-sm'}`}>
            {operation.target}
            {operation.metadata?.['itemCount'] && (
              <span className="ml-2">({operation.metadata['itemCount']} 项)</span>
            )}
          </div>
          
          {!compactMode && (
            <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatDate(operation.timestamp)}
              </div>
              {operation.duration && (
                <div>
                  耗时: {formatDuration(operation.duration)}
                </div>
              )}
            </div>
          )}
        </div>
        
        <Badge variant="outline" className="text-xs">
          {operation.module}
        </Badge>
      </div>
    )
  }

  /**
   * 渲染操作详情
   */
  const renderOperationDetails = (operation: OperationHistory) => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium">操作类型</label>
          <p className="text-sm text-muted-foreground">{operation.action}</p>
        </div>
        <div>
          <label className="text-sm font-medium">目标</label>
          <p className="text-sm text-muted-foreground">{operation.target}</p>
        </div>
        <div>
          <label className="text-sm font-medium">模块</label>
          <p className="text-sm text-muted-foreground">{operation.module}</p>
        </div>
        <div>
          <label className="text-sm font-medium">状态</label>
          <Badge variant={operation.success ? 'default' : 'destructive'}>
            {operation.success ? '成功' : '失败'}
          </Badge>
        </div>
        <div>
          <label className="text-sm font-medium">执行时间</label>
          <p className="text-sm text-muted-foreground">
            {formatDate(operation.timestamp)}
          </p>
        </div>
        {operation.duration && (
          <div>
            <label className="text-sm font-medium">耗时</label>
            <p className="text-sm text-muted-foreground">
              {formatDuration(operation.duration)}
            </p>
          </div>
        )}
      </div>

      {operation.targetId && (
        <div>
          <label className="text-sm font-medium">目标ID</label>
          <p className="text-sm text-muted-foreground font-mono">{operation.targetId}</p>
        </div>
      )}

      {operation.metadata && Object.keys(operation.metadata).length > 0 && (
        <div>
          <label className="text-sm font-medium">元数据</label>
          <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
            {JSON.stringify(operation.metadata, null, 2)}
          </pre>
        </div>
      )}

      {operation.context && (
        <div>
          <label className="text-sm font-medium">上下文</label>
          <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
            {typeof operation.context === 'string' 
              ? operation.context 
              : JSON.stringify(operation.context, null, 2)
            }
          </pre>
        </div>
      )}
    </div>
  )

  return (
    <Card className={className}>
      <CardHeader className={compactMode ? 'pb-3' : ''}>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          操作历史
          {history.length > 0 && (
            <Badge variant="secondary">{history.length}</Badge>
          )}
        </CardTitle>
        {!compactMode && (
          <CardDescription>
            最近的操作记录和系统活动
          </CardDescription>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {showFilters && !compactMode && (
          <div className="space-y-3">
            <div className="flex gap-2">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="搜索操作..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>
              </div>
              <Select value={moduleFilter} onValueChange={setModuleFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="模块" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部模块</SelectItem>
                  {availableModules.map(module => (
                    <SelectItem key={module} value={module}>
                      {module}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="success">成功</SelectItem>
                  <SelectItem value="error">失败</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {(searchTerm || moduleFilter !== 'all' || statusFilter !== 'all') && (
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  显示 {filteredHistory.length} / {history.length} 条记录
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('')
                    setModuleFilter('all')
                    setStatusFilter('all')
                  }}
                >
                  清除筛选
                </Button>
              </div>
            )}
          </div>
        )}

        {filteredHistory.length === 0 ? (
          <div className="text-center py-8">
            <History className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              {history.length === 0 ? '暂无操作记录' : '没有匹配的记录'}
            </p>
          </div>
        ) : (
          <ScrollArea className={compactMode ? 'h-64' : 'h-96'}>
            <div className="space-y-2">
              {filteredHistory.map(renderOperationItem)}
            </div>
          </ScrollArea>
        )}

        {!compactMode && history.length > 0 && (
          <>
            <Separator />
            <div className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                共 {history.length} 条记录
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportHistory}
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearHistory}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  清空
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>

      {/* 操作详情对话框 */}
      <Dialog open={!!showDetails} onOpenChange={() => setShowDetails(null)}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>操作详情</DialogTitle>
            <DialogDescription>
              查看操作的详细信息和上下文
            </DialogDescription>
          </DialogHeader>
          {showDetails && renderOperationDetails(showDetails)}
        </DialogContent>
      </Dialog>
    </Card>
  )
}
