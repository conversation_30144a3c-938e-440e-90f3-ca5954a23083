'use client'

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { 
  Menu,
  X,
  Sun,
  Moon,
  Monitor,
  Palette,
  Github,
  ExternalLink
} from "lucide-react"
import { CompleteShowcase } from './complete-showcase'

// 导航项配置
const navigationItems = [
  {
    title: "Getting Started",
    items: [
      { title: "Introduction", href: "#introduction" },
      { title: "Installation", href: "#installation" },
    ]
  },
  {
    title: "Components",
    items: [
      { title: "Button", href: "#button" },
      { title: "Badge", href: "#badge" },
      { title: "Input", href: "#input" },
      { title: "Select", href: "#select" },
      { title: "Checkbox", href: "#checkbox" },
      { title: "Switch", href: "#switch" },
      { title: "Radio Group", href: "#radio-group" },
      { title: "Textarea", href: "#textarea" },
      { title: "Slider", href: "#slider" },
      { title: "Avatar", href: "#avatar" },
      { title: "Progress", href: "#progress" },
      { title: "Skeleton", href: "#skeleton" },
      { title: "Alert", href: "#alert" },
      { title: "Accordion", href: "#accordion" },
      { title: "Calendar", href: "#calendar" },
      { title: "Dialog", href: "#dialog" },
      { title: "Popover", href: "#popover" },
      { title: "Tooltip", href: "#tooltip" },
      { title: "Table", href: "#table" },
      { title: "Tabs", href: "#tabs" },
      { title: "Toggle", href: "#toggle" },
    ]
  }
]

// 主题切换组件
function ThemeToggle() {
  const { setTheme, theme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 px-0">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Sun className="mr-2 h-4 w-4" />
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          <Monitor className="mr-2 h-4 w-4" />
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// 侧边栏组件
function Sidebar({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      onClose() // 移动端点击后关闭侧边栏
    }
  }

  return (
    <>
      {/* 移动端遮罩 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={onClose}
        />
      )}
      
      {/* 侧边栏 */}
      <div className={`
        fixed top-0 left-0 z-50 h-full w-64 bg-background border-r transform transition-transform duration-200 ease-in-out
        md:relative md:translate-x-0 md:z-auto
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <div className="h-6 w-6 bg-primary rounded-sm flex items-center justify-center">
              <Palette className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="font-semibold">shadcn/ui</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden h-8 w-8 p-0"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <ScrollArea className="h-[calc(100vh-73px)]">
          <div className="p-4 space-y-6">
            {navigationItems.map((section, index) => (
              <div key={index} className="space-y-2">
                <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider">
                  {section.title}
                </h4>
                <div className="space-y-1">
                  {section.items.map((item, itemIndex) => (
                    <button
                      key={itemIndex}
                      onClick={() => scrollToSection(item.href)}
                      className="block w-full text-left text-sm py-1.5 px-2 rounded-md hover:bg-muted transition-colors"
                    >
                      {item.title}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </>
  )
}

// 顶部导航栏
function TopNav({ onMenuClick }: { onMenuClick: () => void }) {
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden mr-2 h-8 w-8 p-0"
          onClick={onMenuClick}
        >
          <Menu className="h-4 w-4" />
        </Button>
        
        <div className="mr-4 hidden md:flex">
          <div className="flex items-center gap-2">
            <div className="h-6 w-6 bg-primary rounded-sm flex items-center justify-center">
              <Palette className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="font-semibold">shadcn/ui</span>
          </div>
        </div>
        
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <div className="hidden md:flex items-center space-x-4 text-sm">
              <span className="text-muted-foreground">Components Showcase</span>
              <Badge variant="secondary">v1.0</Badge>
            </div>
          </div>
          
          <nav className="flex items-center space-x-1">
            <Button variant="ghost" size="sm" asChild>
              <a
                href="https://github.com/shadcn-ui/ui"
                target="_blank"
                rel="noopener noreferrer"
                className="h-8 w-8 p-0"
              >
                <Github className="h-4 w-4" />
                <span className="sr-only">GitHub</span>
              </a>
            </Button>
            <ThemeToggle />
          </nav>
        </div>
      </div>
    </header>
  )
}

export function ShowcaseLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-background">
      <TopNav onMenuClick={() => setSidebarOpen(true)} />
      
      <div className="flex">
        <Sidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)} 
        />
        
        <main className="flex-1 md:ml-0">
          <div className="container py-8 space-y-16 max-w-4xl">
            {/* 页面标题 */}
            <div id="introduction" className="space-y-4 text-center scroll-mt-20">
              <h1 className="text-4xl font-bold tracking-tight">shadcn/ui Components</h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Beautifully designed components that you can copy and paste into your apps. 
                Accessible. Customizable. Open Source.
              </p>
              <div className="flex items-center justify-center gap-4">
                <Button asChild>
                  <a href="https://ui.shadcn.com/docs/installation" target="_blank" rel="noopener noreferrer">
                    Get Started
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="https://github.com/shadcn-ui/ui" target="_blank" rel="noopener noreferrer">
                    <Github className="mr-2 h-4 w-4" />
                    GitHub
                  </a>
                </Button>
              </div>
            </div>

            <Separator />

            {/* 组件展示 */}
            <CompleteShowcase />
          </div>
        </main>
      </div>
    </div>
  )
}
