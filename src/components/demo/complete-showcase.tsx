'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Calendar } from "@/components/ui/calendar"
import { Slider } from "@/components/ui/slider"
import { Toggle } from "@/components/ui/toggle"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Copy,
  Eye,
  Code2,
  Mail,
  Loader2,
  GitBranch,
  AlertTriangle,
  Info,
  User,
  Search,
  Bold,
  Italic,
  Underline,
  CalendarDays,
  Settings,
  ChevronDown,
  MoreHorizontal
} from "lucide-react"
import { toast } from "sonner"

// 组件展示容器
interface ComponentShowcaseProps {
  id: string
  title: string
  description: string
  children: React.ReactNode
  code?: string
}

function ComponentShowcase({ id, title, description, children, code }: ComponentShowcaseProps) {
  const [activeTab, setActiveTab] = useState("preview")

  const copyCode = () => {
    if (code) {
      navigator.clipboard.writeText(code)
      toast.success("Code copied to clipboard!")
    }
  }

  return (
    <div id={id} className="space-y-4 scroll-mt-20">
      <div>
        <h3 className="text-lg font-semibold">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-fit grid-cols-2">
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="code" className="flex items-center gap-2">
              <Code2 className="h-4 w-4" />
              Code
            </TabsTrigger>
          </TabsList>
          
          {code && (
            <Button
              variant="outline"
              size="sm"
              onClick={copyCode}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              Copy
            </Button>
          )}
        </div>
        
        <TabsContent value="preview" className="mt-4">
          <div className="rounded-lg border bg-background p-8">
            <div className="flex items-center justify-center min-h-[200px]">
              {children}
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="code" className="mt-4">
          <div className="rounded-lg border bg-muted/50">
            <ScrollArea className="h-[200px] w-full">
              <pre className="p-4 text-sm">
                <code>{code || "// Code example coming soon..."}</code>
              </pre>
            </ScrollArea>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// 组件分组
interface ComponentGroupProps {
  id: string
  title: string
  description: string
  children: React.ReactNode
}

function ComponentGroup({ id, title, description, children }: ComponentGroupProps) {
  return (
    <div id={id} className="space-y-8 scroll-mt-20">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
        <p className="text-muted-foreground">{description}</p>
      </div>
      <div className="space-y-12">
        {children}
      </div>
    </div>
  )
}

export function CompleteShowcase() {
  const [progress, setProgress] = useState(33)
  const [sliderValue, setSliderValue] = useState([50])
  const [date, setDate] = useState<Date | undefined>(new Date())

  React.useEffect(() => {
    const timer = setTimeout(() => setProgress(66), 500)
    return () => clearTimeout(timer)
  }, [])

  return (
    <TooltipProvider>
      <div className="space-y-16">
        {/* Button Components */}
        <ComponentGroup
          id="button"
          title="Button"
          description="Displays a button or a component that looks like a button."
        >
          <ComponentShowcase
            id="button-default"
            title="Default"
            description="The default button style."
            code={`import { Button } from "@/components/ui/button"

export function ButtonDemo() {
  return <Button>Button</Button>
}`}
          >
            <Button>Button</Button>
          </ComponentShowcase>

          <ComponentShowcase
            id="button-variants"
            title="Variants"
            description="Different button variants for various use cases."
            code={`import { Button } from "@/components/ui/button"

export function ButtonVariants() {
  return (
    <div className="flex flex-wrap gap-2">
      <Button variant="default">Default</Button>
      <Button variant="destructive">Destructive</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
    </div>
  )
}`}
          >
            <div className="flex flex-wrap gap-2">
              <Button variant="default">Default</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="button-sizes"
            title="Sizes"
            description="Different button sizes."
            code={`import { Button } from "@/components/ui/button"

export function ButtonSizes() {
  return (
    <div className="flex items-center gap-2">
      <Button size="sm">Small</Button>
      <Button size="default">Default</Button>
      <Button size="lg">Large</Button>
    </div>
  )
}`}
          >
            <div className="flex items-center gap-2">
              <Button size="sm">Small</Button>
              <Button size="default">Default</Button>
              <Button size="lg">Large</Button>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="button-with-icon"
            title="With Icon"
            description="Buttons with icons for enhanced visual communication."
            code={`import { Button } from "@/components/ui/button"
import { Mail, GitBranch } from "lucide-react"

export function ButtonWithIcon() {
  return (
    <div className="flex gap-2">
      <Button>
        <Mail className="mr-2 h-4 w-4" />
        Login with Email
      </Button>
      <Button variant="outline">
        <GitBranch className="mr-2 h-4 w-4" />
        New Branch
      </Button>
    </div>
  )
}`}
          >
            <div className="flex gap-2">
              <Button>
                <Mail className="mr-2 h-4 w-4" />
                Login with Email
              </Button>
              <Button variant="outline">
                <GitBranch className="mr-2 h-4 w-4" />
                New Branch
              </Button>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="button-loading"
            title="Loading"
            description="Button with loading state."
            code={`import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

export function ButtonLoading() {
  return (
    <Button disabled>
      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      Please wait
    </Button>
  )
}`}
          >
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Please wait
            </Button>
          </ComponentShowcase>
        </ComponentGroup>

        <Separator />

        {/* Badge Components */}
        <ComponentGroup
          id="badge"
          title="Badge"
          description="Displays a badge or a component that looks like a badge."
        >
          <ComponentShowcase
            id="badge-default"
            title="Default"
            description="The default badge style."
            code={`import { Badge } from "@/components/ui/badge"

export function BadgeDemo() {
  return <Badge>Badge</Badge>
}`}
          >
            <Badge>Badge</Badge>
          </ComponentShowcase>

          <ComponentShowcase
            id="badge-variants"
            title="Variants"
            description="Different badge variants for various states."
            code={`import { Badge } from "@/components/ui/badge"

export function BadgeVariants() {
  return (
    <div className="flex gap-2">
      <Badge variant="default">Default</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="destructive">Destructive</Badge>
      <Badge variant="outline">Outline</Badge>
    </div>
  )
}`}
          >
            <div className="flex gap-2">
              <Badge variant="default">Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="outline">Outline</Badge>
            </div>
          </ComponentShowcase>
        </ComponentGroup>

        <Separator />

        {/* Form Components */}
        <ComponentGroup
          id="form"
          title="Form Components"
          description="Input fields, selects, checkboxes, and other form controls."
        >
          <ComponentShowcase
            id="input"
            title="Input"
            description="Displays a form input field or a component that looks like an input field."
            code={`import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function InputDemo() {
  return (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="email">Email</Label>
      <Input type="email" id="email" placeholder="Email" />
    </div>
  )
}`}
          >
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="email">Email</Label>
              <Input type="email" id="email" placeholder="Email" />
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="select"
            title="Select"
            description="Displays a list of options for the user to pick from—triggered by a button."
            code={`import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function SelectDemo() {
  return (
    <Select>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select a fruit" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="apple">Apple</SelectItem>
        <SelectItem value="banana">Banana</SelectItem>
        <SelectItem value="blueberry">Blueberry</SelectItem>
      </SelectContent>
    </Select>
  )
}`}
          >
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a fruit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="apple">Apple</SelectItem>
                <SelectItem value="banana">Banana</SelectItem>
                <SelectItem value="blueberry">Blueberry</SelectItem>
              </SelectContent>
            </Select>
          </ComponentShowcase>

          <ComponentShowcase
            id="checkbox"
            title="Checkbox"
            description="A control that allows the user to toggle between checked and not checked."
            code={`import { Checkbox } from "@/components/ui/checkbox"

export function CheckboxDemo() {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox id="terms" />
      <label htmlFor="terms" className="text-sm font-medium leading-none">
        Accept terms and conditions
      </label>
    </div>
  )
}`}
          >
            <div className="flex items-center space-x-2">
              <Checkbox id="terms" />
              <label htmlFor="terms" className="text-sm font-medium leading-none">
                Accept terms and conditions
              </label>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="switch"
            title="Switch"
            description="A control that allows the user to toggle between checked and not checked."
            code={`import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export function SwitchDemo() {
  return (
    <div className="flex items-center space-x-2">
      <Switch id="airplane-mode" />
      <Label htmlFor="airplane-mode">Airplane Mode</Label>
    </div>
  )
}`}
          >
            <div className="flex items-center space-x-2">
              <Switch id="airplane-mode" />
              <Label htmlFor="airplane-mode">Airplane Mode</Label>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="radio-group"
            title="Radio Group"
            description="A set of checkable buttons—known as radio buttons—where no more than one of the buttons can be checked at a time."
            code={`import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"

export function RadioGroupDemo() {
  return (
    <RadioGroup defaultValue="comfortable">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="default" id="r1" />
        <Label htmlFor="r1">Default</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="comfortable" id="r2" />
        <Label htmlFor="r2">Comfortable</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="compact" id="r3" />
        <Label htmlFor="r3">Compact</Label>
      </div>
    </RadioGroup>
  )
}`}
          >
            <RadioGroup defaultValue="comfortable">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="default" id="r1" />
                <Label htmlFor="r1">Default</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="comfortable" id="r2" />
                <Label htmlFor="r2">Comfortable</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="compact" id="r3" />
                <Label htmlFor="r3">Compact</Label>
              </div>
            </RadioGroup>
          </ComponentShowcase>

          <ComponentShowcase
            id="textarea"
            title="Textarea"
            description="Displays a form textarea or a component that looks like a textarea."
            code={`import { Textarea } from "@/components/ui/textarea"

export function TextareaDemo() {
  return <Textarea placeholder="Type your message here." />
}`}
          >
            <Textarea placeholder="Type your message here." className="max-w-sm" />
          </ComponentShowcase>

          <ComponentShowcase
            id="slider"
            title="Slider"
            description="An input where the user selects a value from within a given range."
            code={`import { Slider } from "@/components/ui/slider"

export function SliderDemo() {
  return <Slider defaultValue={[50]} max={100} step={1} className="w-[60%]" />
}`}
          >
            <div className="w-[60%] space-y-2">
              <div className="text-sm text-muted-foreground">Value: {sliderValue[0]}</div>
              <Slider
                value={sliderValue}
                onValueChange={setSliderValue}
                max={100}
                step={1}
                className="w-full"
              />
            </div>
          </ComponentShowcase>
        </ComponentGroup>

        <Separator />

        {/* Display Components */}
        <ComponentGroup
          id="display"
          title="Display Components"
          description="Components for displaying content and data."
        >
          <ComponentShowcase
            id="avatar"
            title="Avatar"
            description="An image element with a fallback for representing the user."
            code={`import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function AvatarDemo() {
  return (
    <Avatar>
      <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
      <AvatarFallback>CN</AvatarFallback>
    </Avatar>
  )
}`}
          >
            <div className="flex gap-2">
              <Avatar>
                <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
              <Avatar>
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="progress"
            title="Progress"
            description="Displays an indicator showing the completion progress of a task, typically displayed as a progress bar."
            code={`import { Progress } from "@/components/ui/progress"

export function ProgressDemo() {
  return <Progress value={33} className="w-[60%]" />
}`}
          >
            <div className="w-[60%] space-y-2">
              <div className="text-sm text-muted-foreground">Progress: {progress}%</div>
              <Progress value={progress} className="w-full" />
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="skeleton"
            title="Skeleton"
            description="Use to show a placeholder while content is loading."
            code={`import { Skeleton } from "@/components/ui/skeleton"

export function SkeletonDemo() {
  return (
    <div className="flex items-center space-x-4">
      <Skeleton className="h-12 w-12 rounded-full" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-[250px]" />
        <Skeleton className="h-4 w-[200px]" />
      </div>
    </div>
  )
}`}
          >
            <div className="flex items-center space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            </div>
          </ComponentShowcase>
        </ComponentGroup>

        <Separator />

        {/* Feedback Components */}
        <ComponentGroup
          id="feedback"
          title="Feedback Components"
          description="Components for providing feedback to users."
        >
          <ComponentShowcase
            id="alert"
            title="Alert"
            description="Displays a callout for user attention."
            code={`import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Info } from "lucide-react"

export function AlertDemo() {
  return (
    <Alert>
      <Info className="h-4 w-4" />
      <AlertTitle>Heads up!</AlertTitle>
      <AlertDescription>
        You can add components to your app using the cli.
      </AlertDescription>
    </Alert>
  )
}`}
          >
            <div className="w-full max-w-md space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Heads up!</AlertTitle>
                <AlertDescription>
                  You can add components to your app using the cli.
                </AlertDescription>
              </Alert>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  Your session has expired. Please log in again.
                </AlertDescription>
              </Alert>
            </div>
          </ComponentShowcase>
        </ComponentGroup>

        <Separator />

        {/* Layout Components */}
        <ComponentGroup
          id="layout"
          title="Layout Components"
          description="Components for organizing and structuring content."
        >
          <ComponentShowcase
            id="accordion"
            title="Accordion"
            description="A vertically stacked set of interactive headings that each reveal a section of content."
            code={`import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export function AccordionDemo() {
  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value="item-1">
        <AccordionTrigger>Is it accessible?</AccordionTrigger>
        <AccordionContent>
          Yes. It adheres to the WAI-ARIA design pattern.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-2">
        <AccordionTrigger>Is it styled?</AccordionTrigger>
        <AccordionContent>
          Yes. It comes with default styles that matches the other components' aesthetic.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
}`}
          >
            <div className="w-full max-w-md">
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>Is it accessible?</AccordionTrigger>
                  <AccordionContent>
                    Yes. It adheres to the WAI-ARIA design pattern.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger>Is it styled?</AccordionTrigger>
                  <AccordionContent>
                    Yes. It comes with default styles that matches the other components&apos; aesthetic.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger>Is it animated?</AccordionTrigger>
                  <AccordionContent>
                    Yes. It&apos;s animated by default, but you can disable it if you prefer.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </ComponentShowcase>

          <ComponentShowcase
            id="tabs"
            title="Tabs"
            description="A set of layered sections of content—known as tab panels—that are displayed one at a time."
            code={`import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function TabsDemo() {
  return (
    <Tabs defaultValue="account" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="account">Account</TabsTrigger>
        <TabsTrigger value="password">Password</TabsTrigger>
      </TabsList>
      <TabsContent value="account">Make changes to your account here.</TabsContent>
      <TabsContent value="password">Change your password here.</TabsContent>
    </Tabs>
  )
}`}
          >
            <Tabs defaultValue="account" className="w-[400px]">
              <TabsList>
                <TabsTrigger value="account">Account</TabsTrigger>
                <TabsTrigger value="password">Password</TabsTrigger>
              </TabsList>
              <TabsContent value="account" className="mt-4">
                <p className="text-sm text-muted-foreground">Make changes to your account here. Click save when you&apos;re done.</p>
              </TabsContent>
              <TabsContent value="password" className="mt-4">
                <p className="text-sm text-muted-foreground">Change your password here. After saving, you&apos;ll be logged out.</p>
              </TabsContent>
            </Tabs>
          </ComponentShowcase>
        </ComponentGroup>

        <Separator />

        {/* Interactive Components */}
        <ComponentGroup
          id="interactive"
          title="Interactive Components"
          description="Components for user interaction and overlays."
        >
          <ComponentShowcase
            id="dialog"
            title="Dialog"
            description="A window overlaid on either the primary window or another dialog window, rendering the content underneath inert."
            code={`import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

export function DialogDemo() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">Edit Profile</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit profile</DialogTitle>
          <DialogDescription>
            Make changes to your profile here. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
}`}
          >
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">Edit Profile</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Edit profile</DialogTitle>
                  <DialogDescription>
                    Make changes to your profile here. Click save when you&apos;re done.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <Input id="name" defaultValue="Pedro Duarte" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="username" className="text-right">
                      Username
                    </Label>
                    <Input id="username" defaultValue="@peduarte" className="col-span-3" />
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </ComponentShowcase>

          <ComponentShowcase
            id="popover"
            title="Popover"
            description="Displays rich content in a portal, triggered by a button."
            code={`import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"

export function PopoverDemo() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline">Open popover</Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Dimensions</h4>
            <p className="text-sm text-muted-foreground">
              Set the dimensions for the layer.
            </p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}`}
          >
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline">Open popover</Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">Dimensions</h4>
                    <p className="text-sm text-muted-foreground">
                      Set the dimensions for the layer.
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="width">Width</Label>
                      <Input
                        id="width"
                        defaultValue="100%"
                        className="col-span-2 h-8"
                      />
                    </div>
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="maxWidth">Max. width</Label>
                      <Input
                        id="maxWidth"
                        defaultValue="300px"
                        className="col-span-2 h-8"
                      />
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </ComponentShowcase>

          <ComponentShowcase
            id="tooltip"
            title="Tooltip"
            description="A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it."
            code={`import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Button } from "@/components/ui/button"

export function TooltipDemo() {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="outline">Hover</Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Add to library</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}`}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline">Hover</Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Add to library</p>
              </TooltipContent>
            </Tooltip>
          </ComponentShowcase>

          <ComponentShowcase
            id="toggle"
            title="Toggle"
            description="A two-state button that can be either on or off."
            code={`import { Toggle } from "@/components/ui/toggle"
import { Bold } from "lucide-react"

export function ToggleDemo() {
  return (
    <Toggle aria-label="Toggle italic">
      <Bold className="h-4 w-4" />
    </Toggle>
  )
}`}
          >
            <div className="flex gap-2">
              <Toggle aria-label="Toggle bold">
                <Bold className="h-4 w-4" />
              </Toggle>
              <Toggle aria-label="Toggle italic">
                <Italic className="h-4 w-4" />
              </Toggle>
              <Toggle aria-label="Toggle underline">
                <Underline className="h-4 w-4" />
              </Toggle>
            </div>
          </ComponentShowcase>
        </ComponentGroup>
      </div>
    </TooltipProvider>
  )
}
