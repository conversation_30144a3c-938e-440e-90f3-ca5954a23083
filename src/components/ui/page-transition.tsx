"use client"

import React, { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

interface PageTransitionProps {
  children: React.ReactNode
  className?: string
  duration?: number
  type?: 'fade' | 'slide' | 'scale' | 'blur'
}

export function PageTransition({ 
  children, 
  className,
  duration = 300,
  type = 'fade'
}: PageTransitionProps) {
  const pathname = usePathname()
  const [isVisible, setIsVisible] = useState(false)
  const [displayChildren, setDisplayChildren] = useState(children)

  useEffect(() => {
    setIsVisible(false)
    
    const timer = setTimeout(() => {
      setDisplayChildren(children)
      setIsVisible(true)
    }, duration / 2)

    return () => clearTimeout(timer)
  }, [pathname, children, duration])

  const getTransitionClasses = () => {
    const baseClasses = `transition-all duration-${duration}`
    
    switch (type) {
      case 'fade':
        return cn(
          baseClasses,
          isVisible ? 'opacity-100' : 'opacity-0'
        )
      case 'slide':
        return cn(
          baseClasses,
          isVisible 
            ? 'opacity-100 transform translate-x-0' 
            : 'opacity-0 transform translate-x-4'
        )
      case 'scale':
        return cn(
          baseClasses,
          isVisible 
            ? 'opacity-100 transform scale-100' 
            : 'opacity-0 transform scale-95'
        )
      case 'blur':
        return cn(
          baseClasses,
          isVisible 
            ? 'opacity-100 blur-0' 
            : 'opacity-0 blur-sm'
        )
      default:
        return baseClasses
    }
  }

  return (
    <div className={cn(getTransitionClasses(), className)}>
      {displayChildren}
    </div>
  )
}

// 卡片过渡动画
interface CardTransitionProps {
  children: React.ReactNode
  delay?: number
  className?: string
}

export function CardTransition({ 
  children, 
  delay = 0,
  className 
}: CardTransitionProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, delay)

    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div 
      className={cn(
        "transition-all duration-500 ease-out",
        isVisible 
          ? "opacity-100 transform translate-y-0" 
          : "opacity-0 transform translate-y-4",
        className
      )}
    >
      {children}
    </div>
  )
}

// 列表项过渡动画
interface ListItemTransitionProps {
  children: React.ReactNode
  index: number
  className?: string
}

export function ListItemTransition({ 
  children, 
  index,
  className 
}: ListItemTransitionProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, index * 100) // 每个项目延迟100ms

    return () => clearTimeout(timer)
  }, [index])

  return (
    <div 
      className={cn(
        "transition-all duration-300 ease-out",
        isVisible 
          ? "opacity-100 transform translate-x-0" 
          : "opacity-0 transform -translate-x-4",
        className
      )}
    >
      {children}
    </div>
  )
}

// 标签页过渡动画
interface TabTransitionProps {
  children: React.ReactNode
  isActive: boolean
  className?: string
}

export function TabTransition({ 
  children, 
  isActive,
  className 
}: TabTransitionProps) {
  return (
    <div 
      className={cn(
        "transition-all duration-200 ease-in-out",
        isActive 
          ? "opacity-100 transform scale-100" 
          : "opacity-0 transform scale-95 pointer-events-none absolute",
        className
      )}
    >
      {children}
    </div>
  )
}

// 加载过渡动画
interface LoadingTransitionProps {
  isLoading: boolean
  children: React.ReactNode
  loadingComponent?: React.ReactNode
  className?: string
}

export function LoadingTransition({ 
  isLoading, 
  children, 
  loadingComponent,
  className 
}: LoadingTransitionProps) {
  return (
    <div className={cn("relative", className)}>
      <div 
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {children}
      </div>
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          {loadingComponent || (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              <span className="text-sm text-muted-foreground">加载中...</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// 悬浮动画
interface HoverTransitionProps {
  children: React.ReactNode
  className?: string
  hoverScale?: number
}

export function HoverTransition({ 
  children, 
  className,
  hoverScale = 1.02
}: HoverTransitionProps) {
  return (
    <div 
      className={cn(
        "transition-transform duration-200 ease-out cursor-pointer",
        `hover:scale-[${hoverScale}]`,
        className
      )}
    >
      {children}
    </div>
  )
}

// 弹出动画
interface PopTransitionProps {
  children: React.ReactNode
  isVisible: boolean
  className?: string
}

export function PopTransition({ 
  children, 
  isVisible,
  className 
}: PopTransitionProps) {
  return (
    <div 
      className={cn(
        "transition-all duration-200 ease-out",
        isVisible 
          ? "opacity-100 transform scale-100" 
          : "opacity-0 transform scale-90",
        className
      )}
    >
      {children}
    </div>
  )
}

// 滑入动画
interface SlideInTransitionProps {
  children: React.ReactNode
  direction?: 'left' | 'right' | 'up' | 'down'
  isVisible: boolean
  className?: string
}

export function SlideInTransition({ 
  children, 
  direction = 'right',
  isVisible,
  className 
}: SlideInTransitionProps) {
  const getTransformClasses = () => {
    if (isVisible) return "opacity-100 transform translate-x-0 translate-y-0"
    
    switch (direction) {
      case 'left':
        return "opacity-0 transform -translate-x-4"
      case 'right':
        return "opacity-0 transform translate-x-4"
      case 'up':
        return "opacity-0 transform -translate-y-4"
      case 'down':
        return "opacity-0 transform translate-y-4"
      default:
        return "opacity-0 transform translate-x-4"
    }
  }

  return (
    <div 
      className={cn(
        "transition-all duration-300 ease-out",
        getTransformClasses(),
        className
      )}
    >
      {children}
    </div>
  )
}
