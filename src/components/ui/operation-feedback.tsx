/**
 * 操作反馈组件
 * 提供统一的操作进度反馈和状态显示
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useOperationFeedback } from '@/hooks/use-page-state'
import { type OperationProgress } from '@/lib/state/page-state-manager'
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader2,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { useState } from 'react'

interface OperationFeedbackProps {
  className?: string
  showCompleted?: boolean
  maxVisible?: number
}

export function OperationFeedback({ 
  className = '', 
  showCompleted = true, 
  maxVisible = 3 
}: OperationFeedbackProps) {
  const { operations, runningOperations, completedOperations } = useOperationFeedback()
  const [isExpanded, setIsExpanded] = useState(false)

  if (operations.length === 0) {
    return null
  }

  const visibleOperations = isExpanded 
    ? operations 
    : operations.slice(0, maxVisible)

  const hasMore = operations.length > maxVisible

  return (
    <div className={`space-y-2 ${className}`}>
      {visibleOperations.map((operation) => (
        <OperationCard key={operation.id} operation={operation} />
      ))}
      
      {hasMore && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full"
        >
          {isExpanded ? (
            <>
              <ChevronUp className="h-4 w-4 mr-2" />
              收起
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4 mr-2" />
              显示更多 ({operations.length - maxVisible})
            </>
          )}
        </Button>
      )}
    </div>
  )
}

interface OperationCardProps {
  operation: OperationProgress
}

function OperationCard({ operation }: OperationCardProps) {
  const [isDismissed, setIsDismissed] = useState(false)

  if (isDismissed) {
    return null
  }

  const getStatusIcon = () => {
    switch (operation.status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = () => {
    switch (operation.status) {
      case 'pending':
        return <Badge variant="secondary">等待中</Badge>
      case 'running':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">进行中</Badge>
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">成功</Badge>
      case 'error':
        return <Badge variant="destructive">失败</Badge>
      case 'cancelled':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">已取消</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getOperationTypeText = () => {
    switch (operation.type) {
      case 'create':
        return '创建'
      case 'update':
        return '更新'
      case 'delete':
        return '删除'
      case 'batch':
        return '批量操作'
      default:
        return '操作'
    }
  }

  const formatDuration = () => {
    const duration = (operation.endTime || Date.now()) - operation.startTime
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (minutes > 0) {
      return `${minutes}分${seconds % 60}秒`
    }
    return `${seconds}秒`
  }

  const canDismiss = operation.status === 'success' || operation.status === 'error' || operation.status === 'cancelled'

  return (
    <Card className="relative">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-sm">
            {getStatusIcon()}
            <span>{getOperationTypeText()}</span>
            {getStatusBadge()}
          </CardTitle>
          {canDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDismissed(true)}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        <CardDescription className="text-xs">
          {operation.message}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* 进度条 */}
        {operation.status === 'running' && (
          <div className="space-y-2">
            <Progress value={operation.progress} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{operation.progress}%</span>
              {operation.total && operation.completed !== undefined && (
                <span>{operation.completed}/{operation.total}</span>
              )}
            </div>
          </div>
        )}

        {/* 完成状态信息 */}
        {(operation.status === 'success' || operation.status === 'error') && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>耗时: {formatDuration()}</span>
              {operation.total && operation.completed !== undefined && (
                <span>完成: {operation.completed}/{operation.total}</span>
              )}
            </div>
            
            {/* 错误信息 */}
            {operation.errors && operation.errors.length > 0 && (
              <Alert variant="destructive" className="mt-2">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  <div className="space-y-1">
                    <div className="font-medium">发现 {operation.errors.length} 个错误：</div>
                    <ul className="list-disc list-inside space-y-1">
                      {operation.errors.slice(0, 3).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                      {operation.errors.length > 3 && (
                        <li>还有 {operation.errors.length - 3} 个错误...</li>
                      )}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 浮动操作反馈组件
 * 显示在页面右下角
 */
export function FloatingOperationFeedback() {
  const { runningOperations, completedOperations } = useOperationFeedback()
  const [isMinimized, setIsMinimized] = useState(false)

  const hasOperations = runningOperations.length > 0 || completedOperations.length > 0

  if (!hasOperations) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 max-w-[calc(100vw-2rem)]">
      <Card className="shadow-lg border-2">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center space-x-2">
              <Loader2 className={`h-4 w-4 ${runningOperations.length > 0 ? 'animate-spin text-blue-500' : 'text-gray-400'}`} />
              <span>操作状态</span>
              {runningOperations.length > 0 && (
                <Badge variant="default" className="bg-blue-100 text-blue-800">
                  {runningOperations.length}
                </Badge>
              )}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-6 w-6 p-0"
            >
              {isMinimized ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          </div>
        </CardHeader>
        
        {!isMinimized && (
          <CardContent className="pt-0 max-h-96 overflow-y-auto">
            <OperationFeedback maxVisible={5} />
          </CardContent>
        )}
      </Card>
    </div>
  )
}

/**
 * 操作状态指示器
 * 显示在页面顶部
 */
export function OperationStatusIndicator() {
  const { runningOperations } = useOperationFeedback()

  if (runningOperations.length === 0) {
    return null
  }

  return (
    <div className="bg-blue-50 border-b border-blue-200 px-4 py-2">
      <div className="flex items-center space-x-2 text-sm text-blue-800">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>
          正在执行 {runningOperations.length} 个操作
        </span>
        {runningOperations.length === 1 && (
          <span className="text-blue-600">
            - {runningOperations[0]?.message}
          </span>
        )}
      </div>
    </div>
  )
}
