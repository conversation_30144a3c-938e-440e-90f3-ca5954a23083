'use client'

import { lazy, Suspense, ComponentType, ReactNode } from 'react'
import { Loader2 } from 'lucide-react'

interface LazyComponentProps {
  fallback?: ReactNode
  error?: ReactNode
  className?: string
}

/**
 * 创建懒加载组件的高阶函数
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: ReactNode
) {
  const LazyComponent = lazy(importFunc)
  
  return function LazyWrapper(props: React.ComponentProps<T> & LazyComponentProps) {
    const { fallback: customFallback, error, className, ...componentProps } = props
    
    const defaultFallback = (
      <div className={`flex items-center justify-center p-8 ${className || ''}`}>
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>加载中...</span>
      </div>
    )
    
    return (
      <Suspense fallback={customFallback || fallback || defaultFallback}>
        <LazyComponent {...(componentProps as any)} />
      </Suspense>
    )
  }
}

/**
 * 预加载组件
 */
export function preloadComponent(importFunc: () => Promise<any>) {
  // 在空闲时间预加载组件
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      importFunc()
    })
  } else {
    // 降级到setTimeout
    setTimeout(() => {
      importFunc()
    }, 100)
  }
}

/**
 * 创建页面级加载界面
 */
function createPageLoadingFallback(title: string, subtitle?: string) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-lg font-medium">{title}</p>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </div>
    </div>
  )
}

/**
 * 路由级别的懒加载组件
 */
export const LazyMedicalCasesPage = createLazyComponent(
  () => import('@/app/medical-cases/page'),
  createPageLoadingFallback('加载医疗案例管理...', '请稍候')
)

export const LazyAnalyticsPage = createLazyComponent(
  () => import('@/app/analytics/page'),
  createPageLoadingFallback('加载数据分析中心...', '正在准备图表和数据')
)

export const LazySupervisionRulesPage = createLazyComponent(
  () => import('@/app/supervision-rules/page'),
  createPageLoadingFallback('加载监管规则...', '请稍候')
)

export const LazyKnowledgeBasePage = createLazyComponent(
  () => import('@/app/knowledge-base/documents/page'),
  createPageLoadingFallback('加载知识库...', '正在加载文档')
)

export const LazySettingsPage = createLazyComponent(
  () => import('@/app/settings/general/page'),
  createPageLoadingFallback('加载系统设置...', '请稍候')
)

/**
 * 创建组件级加载界面
 */
function createComponentLoadingFallback(message: string, className?: string) {
  return (
    <div className={`w-full h-64 flex items-center justify-center border rounded-lg bg-gray-50 ${className || ''}`}>
      <div className="text-center">
        <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

/**
 * 组件级别的懒加载
 */
export const LazyChart = createLazyComponent(
  () => import('recharts').then(module => ({ default: module.LineChart })),
  createComponentLoadingFallback('加载图表组件...')
)

/**
 * 智能预加载钩子
 */
export function useSmartPreload() {
  const preloadOnHover = (importFunc: () => Promise<any>) => {
    return {
      onMouseEnter: () => preloadComponent(importFunc),
      onFocus: () => preloadComponent(importFunc)
    }
  }
  
  const preloadOnVisible = (importFunc: () => Promise<any>) => {
    return (element: HTMLElement | null) => {
      if (!element) return
      
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              preloadComponent(importFunc)
              observer.unobserve(element)
            }
          })
        },
        { threshold: 0.1 }
      )
      
      observer.observe(element)
    }
  }
  
  return {
    preloadOnHover,
    preloadOnVisible,
    preloadComponent
  }
}

/**
 * 资源预加载组件
 */
interface ResourcePreloaderProps {
  resources: Array<{
    href: string
    as: 'script' | 'style' | 'font' | 'image'
    type?: string
    crossOrigin?: 'anonymous' | 'use-credentials'
  }>
}

export function ResourcePreloader({ resources }: ResourcePreloaderProps) {
  return (
    <>
      {resources.map((resource, index) => (
        <link
          key={index}
          rel="preload"
          href={resource.href}
          as={resource.as}
          type={resource.type}
          crossOrigin={resource.crossOrigin}
        />
      ))}
    </>
  )
}

/**
 * 关键CSS内联组件
 */
interface CriticalCSSProps {
  css: string
}

export function CriticalCSS({ css }: CriticalCSSProps) {
  return (
    <style
      dangerouslySetInnerHTML={{ __html: css }}
      data-critical="true"
    />
  )
}

/**
 * 延迟加载脚本组件
 */
interface DeferredScriptProps {
  src: string
  onLoad?: () => void
  onError?: () => void
}

export function DeferredScript({ src, onLoad, onError }: DeferredScriptProps) {
  return (
    <script
      src={src}
      defer
      onLoad={onLoad}
      onError={onError}
    />
  )
}

/**
 * 模块预加载器
 */
export class ModulePreloader {
  private static preloadedModules = new Set<string>()
  
  static preload(moduleId: string, importFunc: () => Promise<any>) {
    if (this.preloadedModules.has(moduleId)) {
      return
    }
    
    this.preloadedModules.add(moduleId)
    
    if (typeof window !== 'undefined') {
      // 使用动态导入预加载模块
      importFunc().catch(error => {
        console.warn(`预加载模块 ${moduleId} 失败:`, error)
        this.preloadedModules.delete(moduleId)
      })
    }
  }
  
  static isPreloaded(moduleId: string): boolean {
    return this.preloadedModules.has(moduleId)
  }
  
  static clear() {
    this.preloadedModules.clear()
  }
}

/**
 * 性能优化的图片组件
 */
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  placeholder = 'empty',
  blurDataURL
}: OptimizedImageProps) {
  return (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      loading={priority ? 'eager' : 'lazy'}
      decoding="async"
      style={{
        backgroundImage: placeholder === 'blur' && blurDataURL 
          ? `url(${blurDataURL})` 
          : undefined,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    />
  )
}
