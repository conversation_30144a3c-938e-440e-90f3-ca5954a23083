"use client"

import React, { useState, useMemo, useCallback } from 'react'
import { VirtualTable } from '@/components/ui/virtual-scroll'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search, ChevronUp, ChevronDown, Filter } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Column<T> {
  key: string
  title: string
  width?: number
  sortable?: boolean
  filterable?: boolean
  render?: (item: T, index: number) => React.ReactNode
  className?: string
}

interface OptimizedTableProps<T> {
  data: T[]
  columns: Column<T>[]
  rowHeight?: number
  maxHeight?: number
  searchable?: boolean
  sortable?: boolean
  filterable?: boolean
  className?: string
  onRowClick?: (item: T, index: number) => void
  loading?: boolean
  emptyMessage?: string
  virtualScrollThreshold?: number // 超过这个数量才使用虚拟滚动
}

export function OptimizedTable<T extends Record<string, any>>({
  data,
  columns,
  rowHeight = 60,
  maxHeight = 400,
  searchable = true,
  sortable = true,
  filterable = false,
  className,
  onRowClick,
  loading = false,
  emptyMessage = "暂无数据",
  virtualScrollThreshold = 100
}: OptimizedTableProps<T>) {
  // 确保 data 是数组
  const safeData = Array.isArray(data) ? data : []

  const [searchTerm, setSearchTerm] = useState('')
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)
  const [filters, setFilters] = useState<Record<string, string>>({})

  // 搜索过滤
  const searchedData = useMemo(() => {
    if (!searchTerm) return safeData
    
    return safeData.filter(item =>
      Object.values(item).some(value =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
  }, [safeData, searchTerm])

  // 列过滤
  const filteredData = useMemo(() => {
    if (Object.keys(filters).length === 0) return searchedData
    
    return searchedData.filter(item =>
      Object.entries(filters).every(([key, filterValue]) => {
        if (!filterValue) return true
        return String(item[key]).toLowerCase().includes(filterValue.toLowerCase())
      })
    )
  }, [searchedData, filters])

  // 排序
  const sortedData = useMemo(() => {
    if (!sortConfig) return filteredData
    
    return [...filteredData].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]
      
      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [filteredData, sortConfig])

  // 处理排序
  const handleSort = useCallback((key: string) => {
    setSortConfig(current => {
      if (current?.key === key) {
        return current.direction === 'asc' 
          ? { key, direction: 'desc' }
          : null
      }
      return { key, direction: 'asc' }
    })
  }, [])

  // 处理过滤
  const handleFilter = useCallback((key: string, value: string) => {
    setFilters(current => ({
      ...current,
      [key]: value
    }))
  }, [])

  // 渲染排序图标
  const renderSortIcon = (key: string) => {
    if (sortConfig?.key !== key) return null
    return sortConfig.direction === 'asc' 
      ? <ChevronUp className="h-4 w-4" />
      : <ChevronDown className="h-4 w-4" />
  }

  // 渲染表头
  const renderTableHeader = () => (
    <TableHeader>
      <TableRow>
        {columns.map((column) => (
          <TableHead 
            key={column.key}
            className={cn(
              "relative",
              column.sortable && sortable && "cursor-pointer hover:bg-muted/50",
              column.className
            )}
            style={{ width: column.width }}
            onClick={() => column.sortable && sortable && handleSort(column.key)}
          >
            <div className="flex items-center justify-between">
              <span>{column.title}</span>
              {column.sortable && sortable && (
                <div className="ml-2">
                  {renderSortIcon(column.key)}
                </div>
              )}
            </div>
            {column.filterable && filterable && (
              <div className="mt-2">
                <Input
                  placeholder={`筛选${column.title}`}
                  value={filters[column.key] || ''}
                  onChange={(e) => handleFilter(column.key, e.target.value)}
                  className="h-8"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            )}
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  )

  // 渲染普通表格行
  const renderTableRow = (item: T, index: number) => (
    <TableRow 
      key={index}
      className={cn(
        "cursor-pointer hover:bg-muted/50",
        onRowClick && "cursor-pointer"
      )}
      onClick={() => onRowClick?.(item, index)}
    >
      {columns.map((column) => (
        <TableCell 
          key={column.key}
          className={column.className}
          style={{ width: column.width }}
        >
          {column.render ? column.render(item, index) : String(item[column.key] || '')}
        </TableCell>
      ))}
    </TableRow>
  )

  // 渲染虚拟表格列配置
  const virtualColumns = useMemo(() => 
    columns.map(column => ({
      key: column.key,
      title: column.title,
      width: column.width,
      render: column.render
    }))
  , [columns])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-muted-foreground">加载中...</div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索栏 */}
      {searchable && (
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索数据..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          {filterable && (
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              筛选
            </Button>
          )}
        </div>
      )}

      {/* 数据统计 */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          显示 {sortedData.length} 条记录
          {searchTerm && ` (从 ${safeData.length} 条中筛选)`}
        </span>
        {sortConfig && (
          <Badge variant="secondary" className="text-xs">
            按 {columns.find(c => c.key === sortConfig.key)?.title} {sortConfig.direction === 'asc' ? '升序' : '降序'}
          </Badge>
        )}
      </div>

      {/* 表格内容 */}
      {sortedData.length === 0 ? (
        <div className="flex items-center justify-center h-32 border rounded-lg">
          <div className="text-center text-muted-foreground">
            <div className="text-lg font-medium">{emptyMessage}</div>
            {searchTerm && (
              <div className="text-sm mt-1">
                尝试调整搜索条件
              </div>
            )}
          </div>
        </div>
      ) : sortedData.length > virtualScrollThreshold ? (
        // 使用虚拟滚动
        <VirtualTable
          items={sortedData}
          columns={virtualColumns}
          rowHeight={rowHeight}
          containerHeight={maxHeight}
          onRowClick={onRowClick}
          className="border rounded-lg"
        />
      ) : (
        // 使用普通表格
        <div className="border rounded-lg overflow-hidden">
          <Table>
            {renderTableHeader()}
            <TableBody>
              {sortedData.map((item, index) => renderTableRow(item, index))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
