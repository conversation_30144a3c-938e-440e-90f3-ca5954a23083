'use client'

import React from 'react'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { Home } from 'lucide-react'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { getBreadcrumbs, type BreadcrumbItem as BreadcrumbItemType } from '@/config/navigation'

interface BreadcrumbItemData {
  title: string
  href: string
  preserveState?: boolean
}

interface SmartBreadcrumbProps {
  items?: BreadcrumbItemData[]
  dynamicData?: any
  className?: string
}

/**
 * 智能面包屑组件
 * 支持状态保持的导航，特别是医疗案例列表的状态保持
 * 如果没有传入items，会自动根据当前路径生成面包屑
 */
export function SmartBreadcrumb({ items, dynamicData, className }: SmartBreadcrumbProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()

  // 如果没有传入items，自动生成面包屑
  const breadcrumbItems = items || getBreadcrumbs(pathname, dynamicData).slice(1) // 移除首页，因为我们单独处理

  const handleClick = (item: BreadcrumbItemData, index: number) => {
    // 如果是最后一项（当前页面），不执行跳转
    if (items && index === items.length - 1) {
      return
    }

    // 如果需要保持状态（通常是医疗案例列表）
    if (item.preserveState && item.href === '/medical-cases') {
      // 构建带有返回状态的URL
      const params = new URLSearchParams()

      // 从当前URL中读取返回参数
      const returnPage = searchParams.get('returnPage')
      const returnPageSize = searchParams.get('returnPageSize')
      const returnSearch = searchParams.get('returnSearch')
      const returnSortBy = searchParams.get('returnSortBy')
      const returnSortOrder = searchParams.get('returnSortOrder')

      console.log('🔍 面包屑导航 - 读取到的返回参数:', { returnPage, returnPageSize, returnSearch, returnSortBy, returnSortOrder })

      // 如果有返回参数，使用返回参数构建URL
      if (returnPage || returnPageSize || returnSearch || returnSortBy || returnSortOrder) {
        if (returnPage && returnPage !== '1') params.set('page', returnPage)
        if (returnPageSize && returnPageSize !== '10') params.set('pageSize', returnPageSize)
        if (returnSearch) params.set('search', returnSearch)
        if (returnSortBy && returnSortBy !== 'id') params.set('sortBy', returnSortBy)
        if (returnSortOrder && returnSortOrder !== 'desc') params.set('sortOrder', returnSortOrder)
      } else {
        // 如果没有返回参数，尝试从当前URL参数中读取（用于列表页面内的导航）
        const page = searchParams.get('page')
        const pageSize = searchParams.get('pageSize')
        const search = searchParams.get('search')
        const sortBy = searchParams.get('sortBy')
        const sortOrder = searchParams.get('sortOrder')

        if (page && page !== '1') params.set('page', page)
        if (pageSize && pageSize !== '10') params.set('pageSize', pageSize)
        if (search) params.set('search', search)
        if (sortBy && sortBy !== 'id') params.set('sortBy', sortBy)
        if (sortOrder && sortOrder !== 'desc') params.set('sortOrder', sortOrder)
      }

      const queryString = params.toString()
      const urlWithState = queryString ? `${item.href}?${queryString}` : item.href

      console.log('🔍 面包屑导航 - 构建的返回URL:', urlWithState)
      router.push(urlWithState)
    } else {
      console.log('🔍 面包屑导航 - 普通跳转:', item.href)
      router.push(item.href)
    }
  }

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {/* 首页链接 */}
        <BreadcrumbItem>
          <BreadcrumbLink 
            href="/dashboard"
            className="flex items-center hover:text-gray-700 transition-colors"
          >
            <Home className="h-4 w-4" />
          </BreadcrumbLink>
        </BreadcrumbItem>
        
        {/* 动态面包屑项 */}
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={`${item.href}-${index}`}>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem className={index === 0 ? "hidden md:block" : ""}>
              {index === breadcrumbItems.length - 1 ? (
                <BreadcrumbPage>{item.title}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    console.log('🔍 面包屑导航被点击:', item.title, item.href)
                    handleClick(item, index)
                  }}
                  className="hover:text-gray-700 transition-colors cursor-pointer"
                >
                  {item.title}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
