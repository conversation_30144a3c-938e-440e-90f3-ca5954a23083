'use client'

import { useState, useEffect, useRef, ReactNode, Suspense, lazy } from 'react'
import { Loader2 } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface LazyLoadingProps {
  children: ReactNode
  fallback?: ReactNode
  threshold?: number
  rootMargin?: string
  className?: string
}

/**
 * 懒加载组件 - 当元素进入视口时才渲染内容
 */
export function LazyLoading({ 
  children, 
  fallback = <div className="flex items-center justify-center p-4"><Loader2 className="h-6 w-6 animate-spin" /></div>,
  threshold = 0.1,
  rootMargin = '50px',
  className = ''
}: LazyLoadingProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry && entry.isIntersecting && !hasLoaded) {
          setIsVisible(true)
          setHasLoaded(true)
          observer.unobserve(element)
        }
      },
      {
        threshold,
        rootMargin,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [threshold, rootMargin, hasLoaded])

  return (
    <div ref={elementRef} className={className}>
      {isVisible ? children : fallback}
    </div>
  )
}

interface VirtualizedListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => ReactNode
  overscan?: number
  className?: string
}

/**
 * 虚拟化列表组件 - 只渲染可见的列表项
 */
export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = ''
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const visibleCount = Math.ceil(containerHeight / itemHeight)
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(items.length - 1, startIndex + visibleCount + overscan * 2)

  const visibleItems = items.slice(startIndex, endIndex + 1)
  const offsetY = startIndex * itemHeight

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div key={startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

interface ImageLazyLoadProps {
  src: string
  alt: string
  placeholder?: string
  className?: string
  width?: number
  height?: number
  onLoad?: () => void
  onError?: () => void
}

/**
 * 图片懒加载组件
 */
export function ImageLazyLoad({
  src,
  alt,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
  className = '',
  width,
  height,
  onLoad,
  onError
}: ImageLazyLoadProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    const img = imgRef.current
    if (!img) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry && entry.isIntersecting) {
          setIsVisible(true)
          observer.unobserve(img)
        }
      },
      { threshold: 0.1 }
    )

    observer.observe(img)

    return () => {
      observer.unobserve(img)
    }
  }, [])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      <img
        ref={imgRef}
        src={isVisible ? (hasError ? placeholder : src) : placeholder}
        alt={alt}
        className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        style={{ width, height }}
        onLoad={handleLoad}
        onError={handleError}
      />
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      )}
    </div>
  )
}

interface ProgressiveImageProps {
  src: string
  lowQualitySrc?: string
  alt: string
  className?: string
  width?: number
  height?: number
}

/**
 * 渐进式图片加载组件
 */
export function ProgressiveImage({
  src,
  lowQualitySrc,
  alt,
  className = '',
  width,
  height
}: ProgressiveImageProps) {
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false)
  const [isLowQualityLoaded, setIsLowQualityLoaded] = useState(false)

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {/* 低质量图片 */}
      {lowQualitySrc && (
        <img
          src={lowQualitySrc}
          alt={alt}
          className={`absolute inset-0 transition-opacity duration-300 ${
            isLowQualityLoaded && !isHighQualityLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ width, height, filter: 'blur(5px)' }}
          onLoad={() => setIsLowQualityLoaded(true)}
        />
      )}
      
      {/* 高质量图片 */}
      <img
        src={src}
        alt={alt}
        className={`transition-opacity duration-500 ${
          isHighQualityLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        style={{ width, height }}
        onLoad={() => setIsHighQualityLoaded(true)}
      />
      
      {/* 加载指示器 */}
      {!isHighQualityLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
        </div>
      )}
    </div>
  )
}

interface InfiniteScrollProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => ReactNode
  loadMore: () => Promise<void>
  hasMore: boolean
  loading: boolean
  threshold?: number
  className?: string
}

/**
 * 无限滚动组件
 */
export function InfiniteScroll<T>({
  items,
  renderItem,
  loadMore,
  hasMore,
  loading,
  threshold = 100,
  className = ''
}: InfiniteScrollProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const handleScroll = async () => {
      const { scrollTop, scrollHeight, clientHeight } = container
      
      if (
        scrollHeight - scrollTop - clientHeight < threshold &&
        hasMore &&
        !loading &&
        !isLoadingMore
      ) {
        setIsLoadingMore(true)
        try {
          await loadMore()
        } finally {
          setIsLoadingMore(false)
        }
      }
    }

    container.addEventListener('scroll', handleScroll)
    return () => container.removeEventListener('scroll', handleScroll)
  }, [hasMore, loading, isLoadingMore, loadMore, threshold])

  return (
    <div ref={containerRef} className={`overflow-auto ${className}`}>
      {items.map((item, index) => (
        <div key={index}>
          {renderItem(item, index)}
        </div>
      ))}
      
      {(loading || isLoadingMore) && (
        <div className="flex items-center justify-center p-4">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      )}
      
      {!hasMore && items.length > 0 && (
        <div className="text-center p-4 text-gray-500">
          没有更多数据了
        </div>
      )}
    </div>
  )
}

/**
 * 懒加载图片组件
 */
interface LazyImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  placeholder?: string
  quality?: number
  priority?: boolean
  onLoad?: () => void
  onError?: () => void
}

export function LazyImage({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder,
  quality = 85,
  priority = false,
  onLoad,
  onError
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isInView, setIsInView] = useState(priority)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    if (priority) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry && entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => {
      observer.disconnect()
    }
  }, [priority])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setIsError(true)
    onError?.()
  }

  // 生成优化的图片URL
  const optimizedSrc = isInView ? `${src}?w=${width}&h=${height}&q=${quality}` : ''

  return (
    <div
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      {/* 占位符 */}
      {!isLoaded && !isError && (
        <div className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center">
          {placeholder ? (
            <img src={placeholder} alt="" className="w-full h-full object-cover opacity-50" />
          ) : (
            <Skeleton className="w-full h-full" />
          )}
        </div>
      )}

      {/* 实际图片 */}
      {isInView && !isError && (
        <img
          src={optimizedSrc}
          alt={alt}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={handleLoad}
          onError={handleError}
          loading={priority ? 'eager' : 'lazy'}
        />
      )}

      {/* 错误状态 */}
      {isError && (
        <div className="absolute inset-0 bg-muted flex items-center justify-center text-muted-foreground">
          <span className="text-sm">加载失败</span>
        </div>
      )}
    </div>
  )
}

/**
 * 创建懒加载的React组件
 */
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: ReactNode
) {
  const LazyComp = lazy(importFn)

  return function LazyWrapper(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback || <Skeleton className="w-full h-32" />}>
        <LazyComp {...props} />
      </Suspense>
    )
  }
}

/**
 * 预加载组件
 */
export function preloadComponent(importFn: () => Promise<any>): void {
  // 在空闲时间预加载
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFn().catch(() => {
        // 忽略预加载错误
      })
    })
  } else {
    // 降级到setTimeout
    setTimeout(() => {
      importFn().catch(() => {
        // 忽略预加载错误
      })
    }, 100)
  }
}

/**
 * 智能预加载Hook
 */
export function useSmartPreload(
  importFn: () => Promise<any>,
  condition: boolean = true,
  delay: number = 1000
) {
  useEffect(() => {
    if (!condition) return

    const timer = setTimeout(() => {
      preloadComponent(importFn)
    }, delay)

    return () => clearTimeout(timer)
  }, [importFn, condition, delay])
}

// 导出常用的懒加载组件
// export const LazyMedicalCaseDetail = createLazyComponent(
//   () => import('@/components/medical-cases/case-detail'),
//   <Skeleton className="w-full h-96" />
// )

export const LazySupervisionRuleDetail = createLazyComponent(
  () => import('@/components/supervision-rules/rule-detail').then(module => ({ default: module.RuleDetail })),
  <Skeleton className="w-full h-96" />
)
