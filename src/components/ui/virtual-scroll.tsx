"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react'

interface VirtualScrollProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
  className?: string
  onScroll?: (scrollTop: number) => void
}

/**
 * 虚拟滚动组件
 * 用于优化大列表的渲染性能
 */
export function VirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll
}: VirtualScrollProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    )

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan])

  // 可见项目
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1)
  }, [items, visibleRange])

  // 总高度
  const totalHeight = items.length * itemHeight

  // 偏移量
  const offsetY = visibleRange.start * itemHeight

  // 处理滚动
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop
    setScrollTop(newScrollTop)
    onScroll?.(newScrollTop)
  }

  // 滚动到指定索引
  const scrollToIndex = (index: number) => {
    if (scrollElementRef.current) {
      const scrollTop = index * itemHeight
      scrollElementRef.current.scrollTop = scrollTop
      setScrollTop(scrollTop)
    }
  }

  // 滚动到顶部
  const scrollToTop = () => {
    scrollToIndex(0)
  }

  // 滚动到底部
  const scrollToBottom = () => {
    scrollToIndex(items.length - 1)
  }

  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => (
            <div
              key={visibleRange.start + index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, visibleRange.start + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// 导出滚动控制方法
export interface VirtualScrollRef {
  scrollToIndex: (index: number) => void
  scrollToTop: () => void
  scrollToBottom: () => void
}

/**
 * 带引用的虚拟滚动组件
 */
export const VirtualScrollWithRef = React.forwardRef<VirtualScrollRef, VirtualScrollProps<any>>(
  function VirtualScrollWithRef(props, ref) {
    const scrollElementRef = useRef<HTMLDivElement>(null)
    const [scrollTop, setScrollTop] = useState(0)

    const scrollToIndex = (index: number) => {
      if (scrollElementRef.current) {
        const scrollTop = index * props.itemHeight
        scrollElementRef.current.scrollTop = scrollTop
        setScrollTop(scrollTop)
      }
    }

    const scrollToTop = () => {
      scrollToIndex(0)
    }

    const scrollToBottom = () => {
      scrollToIndex(props.items.length - 1)
    }

    React.useImperativeHandle(ref, () => ({
      scrollToIndex,
      scrollToTop,
      scrollToBottom
    }))

    return (
      <VirtualScroll
        {...props}
        onScroll={(scrollTop) => {
          setScrollTop(scrollTop)
          props.onScroll?.(scrollTop)
        }}
      />
    )
  }
)

/**
 * 虚拟表格组件
 */
interface VirtualTableProps<T> {
  items: T[]
  columns: Array<{
    key: string
    title: string
    width?: number
    render?: (item: T, index: number) => React.ReactNode
  }>
  rowHeight: number
  containerHeight: number
  className?: string
  onRowClick?: (item: T, index: number) => void
}

export function VirtualTable<T>({
  items,
  columns,
  rowHeight,
  containerHeight,
  className = '',
  onRowClick
}: VirtualTableProps<T>) {
  const renderRow = (item: T, index: number) => (
    <div
      className={`flex border-b hover:bg-muted/50 cursor-pointer ${className}`}
      onClick={() => onRowClick?.(item, index)}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="px-4 py-2 flex items-center"
          style={{ width: column.width || 'auto', flex: column.width ? 'none' : 1 }}
        >
          {column.render ? column.render(item, index) : String((item as any)[column.key] || '')}
        </div>
      ))}
    </div>
  )

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* 表头 */}
      <div className="flex bg-muted font-medium border-b">
        {columns.map((column) => (
          <div
            key={column.key}
            className="px-4 py-3"
            style={{ width: column.width || 'auto', flex: column.width ? 'none' : 1 }}
          >
            {column.title}
          </div>
        ))}
      </div>

      {/* 虚拟滚动内容 */}
      <VirtualScroll
        items={items}
        itemHeight={rowHeight}
        containerHeight={containerHeight}
        renderItem={renderRow}
        className="bg-background"
      />
    </div>
  )
}
