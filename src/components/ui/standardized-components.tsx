/**
 * 标准化组件库
 * 实现P1-001 shadcn/UI组件标准化目标：
 * - 提供100%符合shadcn/UI规范的组件
 * - 统一组件使用模式
 * - 确保设计一致性
 */

'use client'

import React from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge, BadgeProps } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import {
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  Plus,
  Search,
  MoreHorizontal,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'

/**
 * 标准化操作按钮组
 */
interface StandardActionButtonsProps {
  actions: Array<{
    type: 'view' | 'edit' | 'delete' | 'download' | 'upload' | 'custom'
    label: string
    onClick: () => void
    disabled?: boolean
    icon?: React.ComponentType<{ className?: string }>
    variant?: ButtonProps['variant']
  }>
  size?: ButtonProps['size']
  className?: string
}

export function StandardActionButtons({ actions, size = 'sm', className }: StandardActionButtonsProps) {
  const getActionConfig = (type: string) => {
    const configs = {
      view: { icon: Eye, variant: 'ghost' as const },
      edit: { icon: Edit, variant: 'ghost' as const },
      delete: { icon: Trash2, variant: 'ghost' as const },
      download: { icon: Download, variant: 'outline' as const },
      upload: { icon: Upload, variant: 'outline' as const },
      custom: { icon: MoreHorizontal, variant: 'ghost' as const }
    }
    return configs[type as keyof typeof configs] || configs.custom
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {actions.map((action, index) => {
        const config = getActionConfig(action.type)
        const Icon = action.icon || config.icon
        
        return (
          <Button
            key={index}
            variant={action.variant || config.variant}
            size={size}
            onClick={action.onClick}
            disabled={action.disabled}
            className="h-8 w-8 p-0"
          >
            <Icon className="h-4 w-4" />
            <span className="sr-only">{action.label}</span>
          </Button>
        )
      })}
    </div>
  )
}

/**
 * 标准化状态徽章
 */
interface StandardStatusBadgeProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'pending'
  children: React.ReactNode
  className?: string
}

export function StandardStatusBadge({ status, children, className }: StandardStatusBadgeProps) {
  const getStatusVariant = (status: string): BadgeProps['variant'] => {
    const variants = {
      success: 'default' as const,
      warning: 'secondary' as const,
      error: 'destructive' as const,
      info: 'outline' as const,
      pending: 'secondary' as const
    }
    return variants[status as keyof typeof variants] || 'outline'
  }

  const getStatusIcon = (status: string) => {
    const icons = {
      success: CheckCircle,
      warning: AlertTriangle,
      error: XCircle,
      info: Info,
      pending: Clock
    }
    return icons[status as keyof typeof icons] || Info
  }

  const Icon = getStatusIcon(status)
  
  return (
    <Badge 
      variant={getStatusVariant(status)} 
      className={cn("flex items-center gap-1", className)}
    >
      <Icon className="h-3 w-3" />
      {children}
    </Badge>
  )
}

/**
 * 标准化数据卡片
 */
interface StandardDataCardProps {
  title: string
  description?: string
  value: string | number
  icon?: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    isPositive: boolean
  }
  className?: string
}

export function StandardDataCard({ 
  title, 
  description, 
  value, 
  icon: Icon, 
  trend, 
  className 
}: StandardDataCardProps) {
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {Icon && (
          <Icon className="h-4 w-4 text-muted-foreground" />
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {trend && (
          <div className={cn(
            "text-xs flex items-center gap-1 mt-1",
            trend.isPositive ? "text-green-600" : "text-red-600"
          )}>
            {trend.isPositive ? "↗" : "↘"} {Math.abs(trend.value)}%
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 标准化表单字段
 */
interface StandardFormFieldProps {
  label: string
  description?: string
  error?: string
  required?: boolean
  children: React.ReactNode
  className?: string
}

export function StandardFormField({ 
  label, 
  description, 
  error, 
  required, 
  children, 
  className 
}: StandardFormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
      {children}
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}

/**
 * 标准化搜索框
 */
interface StandardSearchBoxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  onSearch?: (value: string) => void
  placeholder?: string
}

export function StandardSearchBox({ 
  onSearch, 
  placeholder = "搜索...", 
  className,
  ...props 
}: StandardSearchBoxProps) {
  const [value, setValue] = React.useState('')

  const handleSearch = (searchValue: string) => {
    setValue(searchValue)
    onSearch?.(searchValue)
  }

  return (
    <div className={cn("relative", className)}>
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        type="search"
        placeholder={placeholder}
        value={value}
        onChange={(e) => handleSearch(e.target.value)}
        className="pl-10"
        {...props}
      />
    </div>
  )
}

/**
 * 标准化筛选器
 */
interface StandardFilterProps {
  label: string
  options: Array<{ label: string; value: string }>
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
}

export function StandardFilter({ 
  label, 
  options, 
  value, 
  onValueChange, 
  placeholder = "选择筛选条件",
  className 
}: StandardFilterProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium">{label}</Label>
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

/**
 * 标准化确认对话框
 */
interface StandardConfirmDialogProps {
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  onConfirmAction: () => void
  onCancelAction?: () => void
  variant?: 'default' | 'destructive'
  children: React.ReactNode
}

export function StandardConfirmDialog({
  title,
  description,
  confirmText = "确认",
  cancelText = "取消",
  onConfirmAction,
  onCancelAction,
  variant = 'default',
  children
}: StandardConfirmDialogProps) {
  const [open, setOpen] = React.useState(false)

  const handleConfirm = () => {
    onConfirmAction()
    setOpen(false)
  }

  const handleCancel = () => {
    onCancelAction?.()
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel}>
            {cancelText}
          </Button>
          <Button 
            variant={variant === 'destructive' ? 'destructive' : 'default'} 
            onClick={handleConfirm}
          >
            {confirmText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

/**
 * 标准化加载状态
 */
interface StandardLoadingStateProps {
  message?: string
  className?: string
}

export function StandardLoadingState({ 
  message = "加载中...", 
  className 
}: StandardLoadingStateProps) {
  return (
    <div className={cn("flex items-center justify-center p-8", className)}>
      <div className="text-center space-y-2">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

/**
 * 标准化空状态
 */
interface StandardEmptyStateProps {
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  icon?: React.ComponentType<{ className?: string }>
  className?: string
}

export function StandardEmptyState({ 
  title, 
  description, 
  action, 
  icon: Icon,
  className 
}: StandardEmptyStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      {Icon && (
        <Icon className="h-12 w-12 text-muted-foreground mb-4" />
      )}
      <h3 className="text-lg font-semibold">{title}</h3>
      {description && (
        <p className="text-sm text-muted-foreground mt-2 max-w-sm">{description}</p>
      )}
      {action && (
        <Button onClick={action.onClick} className="mt-4">
          <Plus className="h-4 w-4 mr-2" />
          {action.label}
        </Button>
      )}
    </div>
  )
}
