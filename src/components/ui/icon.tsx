'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { type LucideIcon } from 'lucide-react'

// 从Lucide Icons导入现有图标
import {
  LayoutDashboard,
  Users,
  Shield,
  FileText,
  BarChart3,
  Settings,
  BookOpen,
  Activity,
  Briefcase,
  TrendingUp,
  Monitor,
  Cog,
  // 新增语义化图标
  FileCheck,
  Play,
  Eye,
  FileBarChart,
  LineChart,
  Download,
  // 其他图标
  Bell,
  Search,
  ChevronRight,
  ChevronDown,
  ChevronsUpDown,
  User,
  LogOut,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Upload,
  RefreshCw,
  EyeOff,
  Calendar,
  Clock,
  MapPin,
  Phone,
  Mail,
  Home,
  Building,
  Car,
  Heart,
  Star,
  Filter,
  SortAsc,
  SortDesc,
  MoreHorizontal,
  MoreVertical,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ExternalLink,
  Copy,
  Share,
  Save,
  Printer,
  HelpCircle,
  AlertCircle,
  CheckCircle2,
  X,
  Check,
  Loader2
} from 'lucide-react'

// 图标映射配置
export const ICON_MAP = {
  // 导航图标
  dashboard: LayoutDashboard,
  users: Users,
  rules: Shield,
  cases: FileText,
  analytics: BarChart3,
  settings: Settings,
  knowledge: BookOpen,
  activity: Activity,
  business: Briefcase,
  trends: TrendingUp,
  monitor: Monitor,
  config: Cog,

  // 新增语义化图标
  'file-check': FileCheck,
  play: Play,
  'file-bar-chart': FileBarChart,
  'line-chart': LineChart,
  
  // 界面图标
  bell: Bell,
  search: Search,
  'chevron-right': ChevronRight,
  'chevron-down': ChevronDown,
  'chevrons-up-down': ChevronsUpDown,
  user: User,
  logout: LogOut,
  
  // 状态图标
  success: CheckCircle,
  warning: AlertTriangle,
  error: XCircle,
  info: Info,
  'check-circle': CheckCircle2,
  'alert-circle': AlertCircle,
  'help-circle': HelpCircle,
  
  // 操作图标
  plus: Plus,
  minus: Minus,
  edit: Edit,
  delete: Trash2,
  download: Download,
  upload: Upload,
  refresh: RefreshCw,
  eye: Eye,
  'eye-off': EyeOff,
  copy: Copy,
  share: Share,
  save: Save,
  print: Printer,
  'external-link': ExternalLink,
  
  // 通用图标
  calendar: Calendar,
  clock: Clock,
  location: MapPin,
  phone: Phone,
  mail: Mail,
  home: Home,
  building: Building,
  car: Car,
  heart: Heart,
  star: Star,
  filter: Filter,
  'sort-asc': SortAsc,
  'sort-desc': SortDesc,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  
  // 方向图标
  'arrow-left': ArrowLeft,
  'arrow-right': ArrowRight,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  
  // 状态指示
  x: X,
  check: Check,
  loading: Loader2,
} as const

export type IconName = keyof typeof ICON_MAP

// 图标尺寸配置
const ICON_SIZES = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10',
} as const

export type IconSize = keyof typeof ICON_SIZES

// 图标颜色配置
const ICON_COLORS = {
  default: '',
  primary: 'text-primary',
  secondary: 'text-secondary-foreground',
  muted: 'text-muted-foreground',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  error: 'text-red-600',
  info: 'text-blue-600',
} as const

export type IconColor = keyof typeof ICON_COLORS

// 图标组件属性
export interface IconProps {
  name: IconName
  size?: IconSize
  color?: IconColor
  className?: string
  onClick?: () => void
  'aria-label'?: string
}

/**
 * 统一图标组件
 * 
 * 提供一致的图标使用方式，支持：
 * - 标准化尺寸
 * - 语义化颜色
 * - 可访问性支持
 * - TypeScript类型安全
 */
export const Icon: React.FC<IconProps> = ({
  name,
  size = 'md',
  color = 'default',
  className,
  onClick,
  'aria-label': ariaLabel,
  ...props
}) => {
  const IconComponent = ICON_MAP[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in ICON_MAP`)
    return null
  }
  
  return (
    <IconComponent
      className={cn(
        ICON_SIZES[size],
        ICON_COLORS[color],
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
      aria-label={ariaLabel || name}
      {...props}
    />
  )
}

/**
 * 图标按钮组件
 * 
 * 为图标添加按钮行为和样式
 */
export interface IconButtonProps extends Omit<IconProps, 'onClick'> {
  onClick: () => void
  variant?: 'ghost' | 'outline' | 'default'
  disabled?: boolean
  loading?: boolean
}

export const IconButton: React.FC<IconButtonProps> = ({
  name,
  size = 'md',
  color = 'default',
  variant = 'ghost',
  disabled = false,
  loading = false,
  className,
  onClick,
  'aria-label': ariaLabel,
  ...props
}) => {
  const buttonVariants = {
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
  }
  
  return (
    <button
      type="button"
      disabled={disabled || loading}
      onClick={onClick}
      className={cn(
        'inline-flex items-center justify-center rounded-md p-2 transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
        'disabled:pointer-events-none disabled:opacity-50',
        buttonVariants[variant],
        className
      )}
      aria-label={ariaLabel || name}
      {...props}
    >
      {loading ? (
        <Icon name="loading" size={size} className="animate-spin" />
      ) : (
        <Icon name={name} size={size} color={color} />
      )}
    </button>
  )
}

// 导出图标相关类型和常量
export { ICON_SIZES, ICON_COLORS }
export type { LucideIcon }
