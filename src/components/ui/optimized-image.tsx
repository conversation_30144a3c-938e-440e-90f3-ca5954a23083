'use client'

import React, { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
  lazy?: boolean
  webpFallback?: boolean
  responsive?: boolean
}

/**
 * 优化的图片组件
 * 支持懒加载、WebP格式、响应式、渐进式加载等特性
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  placeholder = 'blur',
  blurDataURL,
  sizes,
  fill = false,
  style,
  onLoad,
  onError,
  lazy = true,
  webpFallback = true,
  responsive = true,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(!lazy || priority)
  const imgRef = useRef<HTMLDivElement>(null)

  // 生成WebP格式的图片URL
  const getOptimizedSrc = (originalSrc: string): string => {
    if (!webpFallback || originalSrc.includes('data:')) {
      return originalSrc
    }

    // 如果是外部URL，直接返回
    if (originalSrc.startsWith('http')) {
      return originalSrc
    }

    // 对于本地图片，尝试WebP格式
    const extension = originalSrc.split('.').pop()?.toLowerCase()
    if (['jpg', 'jpeg', 'png'].includes(extension || '')) {
      const webpSrc = originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp')
      return webpSrc
    }

    return originalSrc
  }

  // 生成低质量占位图
  const generateBlurDataURL = (src: string): string => {
    if (blurDataURL) return blurDataURL
    
    // 生成简单的灰色占位图
    const canvas = document.createElement('canvas')
    canvas.width = 10
    canvas.height = 10
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.fillStyle = 'hsl(var(--muted))'
      ctx.fillRect(0, 0, 10, 10)
      return canvas.toDataURL()
    }
    
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjRjNGNEY2Ii8+Cjwvc3ZnPgo='
  }

  // 懒加载观察器
  useEffect(() => {
    if (!lazy || priority || isInView) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry && entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    )

    const currentRef = imgRef.current
    if (currentRef) {
      observer.observe(currentRef)
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef)
      }
    }
  }, [lazy, priority, isInView])

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  // 处理图片加载错误
  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  // 生成响应式sizes属性
  const getResponsiveSizes = (): string => {
    if (sizes) return sizes
    if (!responsive) return ''
    
    return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw'
  }

  // 错误状态显示
  if (hasError) {
    return (
      <div
        ref={imgRef}
        className={cn(
          'flex items-center justify-center bg-gray-100 text-gray-400',
          className
        )}
        style={{
          width: fill ? '100%' : width,
          height: fill ? '100%' : height,
          ...style
        }}
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    )
  }

  // 如果还未进入视口，显示占位符
  if (!isInView) {
    return (
      <div
        ref={imgRef}
        className={cn(
          'bg-gray-100 animate-pulse',
          className
        )}
        style={{
          width: fill ? '100%' : width,
          height: fill ? '100%' : height,
          ...style
        }}
      />
    )
  }

  const optimizedSrc = getOptimizedSrc(src)
  const finalBlurDataURL = placeholder === 'blur' ? generateBlurDataURL(src) : undefined

  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden',
        !isLoaded && 'animate-pulse',
        className
      )}
      style={fill ? { position: 'relative', ...style } : style}
    >
      <Image
        src={optimizedSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={finalBlurDataURL}
        sizes={getResponsiveSizes()}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0'
        )}
        {...props}
      />
      
      {/* 加载指示器 */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

/**
 * 图片画廊组件 - 支持懒加载和虚拟滚动
 */
interface ImageGalleryProps {
  images: Array<{
    src: string
    alt: string
    width?: number
    height?: number
  }>
  columns?: number
  gap?: number
  className?: string
  onImageClick?: (index: number) => void
}

export function ImageGallery({
  images,
  columns = 3,
  gap = 16,
  className,
  onImageClick
}: ImageGalleryProps) {
  return (
    <div
      className={cn('grid', className)}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap}px`
      }}
    >
      {images.map((image, index) => (
        <div
          key={index}
          className="cursor-pointer hover:opacity-80 transition-opacity"
          onClick={() => onImageClick?.(index)}
        >
          <OptimizedImage
            src={image.src}
            alt={image.alt}
            width={image.width || 300}
            height={image.height || 200}
            className="w-full h-auto rounded-lg"
            responsive
          />
        </div>
      ))}
    </div>
  )
}

/**
 * 头像组件 - 优化的用户头像显示
 */
interface AvatarImageProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallback?: string
  className?: string
}

export function AvatarImage({
  src,
  alt,
  size = 'md',
  fallback,
  className
}: AvatarImageProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }

  const sizePixels = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 96
  }

  // 生成默认头像
  const generateDefaultAvatar = (name: string): string => {
    const initials = name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)

    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
      '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
    ]
    
    const colorIndex = name.charCodeAt(0) % colors.length
    const bgColor = colors[colorIndex]

    const svg = `
      <svg width="${sizePixels[size]}" height="${sizePixels[size]}" viewBox="0 0 ${sizePixels[size]} ${sizePixels[size]}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${sizePixels[size]}" height="${sizePixels[size]}" fill="${bgColor}" rx="${sizePixels[size] / 2}"/>
        <text x="50%" y="50%" text-anchor="middle" dy="0.35em" fill="white" font-family="Arial, sans-serif" font-size="${sizePixels[size] * 0.4}" font-weight="bold">
          ${initials}
        </text>
      </svg>
    `

    return `data:image/svg+xml;base64,${btoa(svg)}`
  }

  const avatarSrc = src || fallback || generateDefaultAvatar(alt)

  return (
    <div className={cn('relative rounded-full overflow-hidden', sizeClasses[size], className)}>
      <OptimizedImage
        src={avatarSrc}
        alt={alt}
        width={sizePixels[size]}
        height={sizePixels[size]}
        className="object-cover"
        priority
        quality={90}
      />
    </div>
  )
}
