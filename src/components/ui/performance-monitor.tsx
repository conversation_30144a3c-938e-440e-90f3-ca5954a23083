'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Activity, 
  Zap, 
  Database, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'

interface PerformanceMetrics {
  // 页面性能
  pageLoadTime: number
  domContentLoaded: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  
  // 资源性能
  resourceCount: number
  totalResourceSize: number
  cacheHitRate: number
  
  // 运行时性能
  memoryUsage: number
  jsHeapSize: number
  frameRate: number
  
  // 网络性能
  networkLatency: number
  downloadSpeed: number
  
  // 用户体验
  timeToInteractive: number
  cumulativeLayoutShift: number
}

/**
 * 性能监控组件
 */
export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [isCollecting, setIsCollecting] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout>()

  // 收集性能指标
  const collectMetrics = async (): Promise<PerformanceMetrics> => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')
    const resources = performance.getEntriesByType('resource')
    
    // 页面性能指标
    const pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
    const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart
    const firstContentfulPaint = paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
    
    // 资源性能指标
    const resourceCount = resources.length
    const totalResourceSize = resources.reduce((total, resource) => {
      return total + ((resource as any).transferSize || 0)
    }, 0)
    
    // 内存使用情况
    const memoryInfo = (performance as any).memory
    const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : 0
    const jsHeapSize = memoryInfo ? memoryInfo.totalJSHeapSize / 1024 / 1024 : 0
    
    // 网络延迟
    const networkLatency = navigation.responseStart - navigation.requestStart
    
    // 模拟一些指标（实际项目中应该从真实数据源获取）
    const largestContentfulPaint = firstContentfulPaint + Math.random() * 1000
    const cacheHitRate = Math.random() * 100
    const frameRate = 60 - Math.random() * 10
    const downloadSpeed = Math.random() * 100 + 50
    const timeToInteractive = pageLoadTime + Math.random() * 500
    const cumulativeLayoutShift = Math.random() * 0.1
    
    return {
      pageLoadTime,
      domContentLoaded,
      firstContentfulPaint,
      largestContentfulPaint,
      resourceCount,
      totalResourceSize,
      cacheHitRate,
      memoryUsage,
      jsHeapSize,
      frameRate,
      networkLatency,
      downloadSpeed,
      timeToInteractive,
      cumulativeLayoutShift
    }
  }

  // 更新性能指标
  const updateMetrics = async () => {
    setIsCollecting(true)
    try {
      const newMetrics = await collectMetrics()
      setMetrics(newMetrics)
    } catch (error) {
      console.error('收集性能指标失败:', error)
    } finally {
      setIsCollecting(false)
    }
  }

  // 开始监控
  const startMonitoring = () => {
    updateMetrics()
    intervalRef.current = setInterval(updateMetrics, 5000) // 每5秒更新一次
  }

  // 停止监控
  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = undefined
    }
  }

  useEffect(() => {
    if (isVisible) {
      startMonitoring()
    } else {
      stopMonitoring()
    }

    return () => stopMonitoring()
  }, [isVisible])

  // 格式化数字
  const formatNumber = (num: number, unit: string = '') => {
    if (num < 1000) {
      return `${num.toFixed(1)}${unit}`
    } else if (num < 1000000) {
      return `${(num / 1000).toFixed(1)}K${unit}`
    } else {
      return `${(num / 1000000).toFixed(1)}M${unit}`
    }
  }

  // 格式化时间
  const formatTime = (ms: number) => {
    if (ms < 1000) {
      return `${ms.toFixed(0)}ms`
    } else {
      return `${(ms / 1000).toFixed(1)}s`
    }
  }

  // 获取性能等级
  const getPerformanceGrade = (value: number, thresholds: number[]) => {
    if (thresholds[0] !== undefined && value <= thresholds[0]) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-100' }
    if (thresholds[1] !== undefined && value <= thresholds[1]) return { grade: 'B', color: 'text-yellow-600', bg: 'bg-yellow-100' }
    if (thresholds[2] !== undefined && value <= thresholds[2]) return { grade: 'C', color: 'text-orange-600', bg: 'bg-orange-100' }
    return { grade: 'D', color: 'text-red-600', bg: 'bg-red-100' }
  }

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50"
      >
        <Activity className="h-4 w-4 mr-2" />
        性能监控
      </Button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-y-auto">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              性能监控
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={updateMetrics}
                disabled={isCollecting}
              >
                <RefreshCw className={`h-4 w-4 ${isCollecting ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
              >
                ×
              </Button>
            </div>
          </div>
          <CardDescription>
            实时监控系统性能指标
          </CardDescription>
        </CardHeader>
        
        {metrics && (
          <CardContent className="space-y-4">
            {/* 页面性能 */}
            <div>
              <h4 className="font-medium mb-2 flex items-center">
                <Zap className="h-4 w-4 mr-1" />
                页面性能
              </h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>页面加载:</span>
                  <Badge className={getPerformanceGrade(metrics.pageLoadTime, [1000, 2000, 3000]).bg}>
                    {formatTime(metrics.pageLoadTime)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>DOM就绪:</span>
                  <Badge className={getPerformanceGrade(metrics.domContentLoaded, [800, 1500, 2500]).bg}>
                    {formatTime(metrics.domContentLoaded)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>首次绘制:</span>
                  <Badge className={getPerformanceGrade(metrics.firstContentfulPaint, [1000, 2000, 3000]).bg}>
                    {formatTime(metrics.firstContentfulPaint)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>最大绘制:</span>
                  <Badge className={getPerformanceGrade(metrics.largestContentfulPaint, [1500, 2500, 4000]).bg}>
                    {formatTime(metrics.largestContentfulPaint)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* 资源性能 */}
            <div>
              <h4 className="font-medium mb-2 flex items-center">
                <Database className="h-4 w-4 mr-1" />
                资源性能
              </h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>资源数量:</span>
                  <span>{metrics.resourceCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>总大小:</span>
                  <span>{formatNumber(metrics.totalResourceSize, 'B')}</span>
                </div>
                <div className="flex justify-between">
                  <span>缓存命中:</span>
                  <Badge className={getPerformanceGrade(100 - metrics.cacheHitRate, [10, 30, 50]).bg}>
                    {metrics.cacheHitRate.toFixed(1)}%
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>网络延迟:</span>
                  <Badge className={getPerformanceGrade(metrics.networkLatency, [100, 300, 500]).bg}>
                    {formatTime(metrics.networkLatency)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* 运行时性能 */}
            <div>
              <h4 className="font-medium mb-2 flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                运行时性能
              </h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>内存使用:</span>
                  <Badge className={getPerformanceGrade(metrics.memoryUsage, [50, 100, 200]).bg}>
                    {formatNumber(metrics.memoryUsage, 'MB')}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>JS堆大小:</span>
                  <span>{formatNumber(metrics.jsHeapSize, 'MB')}</span>
                </div>
                <div className="flex justify-between">
                  <span>帧率:</span>
                  <Badge className={getPerformanceGrade(60 - metrics.frameRate, [5, 15, 30]).bg}>
                    {metrics.frameRate.toFixed(0)} FPS
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>交互时间:</span>
                  <Badge className={getPerformanceGrade(metrics.timeToInteractive, [2000, 4000, 6000]).bg}>
                    {formatTime(metrics.timeToInteractive)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* 用户体验 */}
            <div>
              <h4 className="font-medium mb-2 flex items-center">
                <TrendingUp className="h-4 w-4 mr-1" />
                用户体验
              </h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>布局偏移:</span>
                  <Badge className={getPerformanceGrade(metrics.cumulativeLayoutShift, [0.1, 0.25, 0.5]).bg}>
                    {metrics.cumulativeLayoutShift.toFixed(3)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>下载速度:</span>
                  <span>{formatNumber(metrics.downloadSpeed, 'Mbps')}</span>
                </div>
              </div>
            </div>

            {/* 总体评分 */}
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="font-medium">总体评分:</span>
                <div className="flex items-center space-x-2">
                  {metrics.pageLoadTime < 2000 && metrics.firstContentfulPaint < 1500 ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <Badge className="bg-green-100 text-green-800">优秀</Badge>
                    </>
                  ) : metrics.pageLoadTime < 4000 ? (
                    <>
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <Badge className="bg-yellow-100 text-yellow-800">良好</Badge>
                    </>
                  ) : (
                    <>
                      <TrendingDown className="h-4 w-4 text-red-600" />
                      <Badge className="bg-red-100 text-red-800">需优化</Badge>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
