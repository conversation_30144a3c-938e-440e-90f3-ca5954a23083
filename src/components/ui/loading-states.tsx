"use client"

import React from 'react'
import { <PERSON>ader2, <PERSON>ert<PERSON><PERSON><PERSON>, Refresh<PERSON>w } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

/**
 * 通用骨架屏配置
 */
interface SkeletonConfig {
  width?: string
  height?: string
  className?: string
}

interface SkeletonPattern {
  avatar?: boolean
  title?: SkeletonConfig
  description?: SkeletonConfig
  content?: SkeletonConfig[]
}

/**
 * 通用骨架屏组件
 */
interface UniversalSkeletonProps {
  type: 'list' | 'table' | 'card' | 'custom'
  rows?: number
  columns?: number
  count?: number
  pattern?: SkeletonPattern
  className?: string
}

export function UniversalSkeleton({
  type,
  rows = 3,
  columns = 4,
  count = 3,
  pattern,
  className = ''
}: UniversalSkeletonProps) {
  const renderListSkeleton = () => (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="flex items-start space-x-4">
          {pattern?.avatar && (
            <Skeleton className="h-12 w-12 rounded-full" />
          )}
          <div className="flex-1 space-y-2">
            {pattern?.title && (
              <Skeleton className={`h-4 ${pattern.title.width || 'w-3/4'} ${pattern.title.className || ''}`} />
            )}
            {pattern?.description && (
              <Skeleton className={`h-4 ${pattern.description.width || 'w-1/2'} ${pattern.description.className || ''}`} />
            )}
          </div>
        </div>
      ))}
    </div>
  )

  const renderTableSkeleton = () => (
    <div className={`space-y-3 ${className}`}>
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={index} className="h-4 flex-1" />
        ))}
      </div>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-8 flex-1" />
          ))}
        </div>
      ))}
    </div>
  )

  const renderCardSkeleton = () => (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-3 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index}>
          <CardContent className="p-6">
            <div className="space-y-3">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-8 w-1/2" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  switch (type) {
    case 'list':
      return renderListSkeleton()
    case 'table':
      return renderTableSkeleton()
    case 'card':
      return renderCardSkeleton()
    default:
      return renderListSkeleton()
  }
}

// 向后兼容的组件
export const SkeletonLoader = (props: any) => (
  <UniversalSkeleton
    type="list"
    rows={props.rows}
    pattern={{
      avatar: props.showAvatar,
      title: props.showTitle ? {} : undefined,
      description: props.showDescription ? {} : undefined
    }}
    className={props.className}
  />
)

export const TableSkeleton = (props: any) => (
  <UniversalSkeleton
    type="table"
    rows={props.rows}
    columns={props.columns}
    className={props.className}
  />
)

export const CardSkeleton = (props: any) => (
  <UniversalSkeleton
    type="card"
    count={props.count}
    className={props.className}
  />
)

/**
 * 加载状态组件
 */
interface LoadingStateProps {
  isLoading: boolean
  error?: string | null
  isEmpty?: boolean
  onRetry?: () => void
  loadingComponent?: React.ReactNode
  errorComponent?: React.ReactNode
  emptyComponent?: React.ReactNode
  children: React.ReactNode
  className?: string
}

export function LoadingState({
  isLoading,
  error,
  isEmpty = false,
  onRetry,
  loadingComponent,
  errorComponent,
  emptyComponent,
  children,
  className = ''
}: LoadingStateProps) {
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        {loadingComponent || (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-muted-foreground">加载中...</span>
          </div>
        )}
      </div>
    )
  }

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center py-8 ${className}`}>
        {errorComponent || (
          <>
            <AlertCircle className="h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-semibold mb-2">加载失败</h3>
            <p className="text-muted-foreground mb-4 text-center max-w-md">
              {error}
            </p>
            {onRetry && (
              <Button onClick={onRetry} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                重试
              </Button>
            )}
          </>
        )}
      </div>
    )
  }

  if (isEmpty) {
    return (
      <div className={`flex flex-col items-center justify-center py-8 ${className}`}>
        {emptyComponent || (
          <>
            <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">暂无数据</h3>
            <p className="text-muted-foreground text-center max-w-md">
              当前没有可显示的内容
            </p>
          </>
        )}
      </div>
    )
  }

  return <>{children}</>
}

/**
 * 智能加载状态Hook
 */
export function useLoadingState<T>(
  data: T[] | null,
  isLoading: boolean,
  error?: string | null
) {
  const isEmpty = !isLoading && !error && (!data || data.length === 0)
  
  return {
    isLoading,
    error,
    isEmpty,
    hasData: !isLoading && !error && data && data.length > 0
  }
}

/**
 * 延迟加载组件
 */
interface LazyLoadProps {
  delay?: number
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function LazyLoad({
  delay = 200,
  fallback,
  children
}: LazyLoadProps) {
  const [shouldRender, setShouldRender] = React.useState(false)

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRender(true)
    }, delay)

    return () => clearTimeout(timer)
  }, [delay])

  if (!shouldRender) {
    return <>{fallback || <SkeletonLoader rows={1} />}</>
  }

  return <>{children}</>
}

/**
 * 渐进式加载组件
 */
interface ProgressiveLoadProps {
  stages: Array<{
    delay: number
    component: React.ReactNode
  }>
  fallback?: React.ReactNode
}

export function ProgressiveLoad({
  stages,
  fallback
}: ProgressiveLoadProps) {
  const [currentStage, setCurrentStage] = React.useState(-1)

  React.useEffect(() => {
    stages.forEach((stage, index) => {
      setTimeout(() => {
        setCurrentStage(index)
      }, stage.delay)
    })
  }, [stages])

  if (currentStage === -1) {
    return <>{fallback || <SkeletonLoader rows={1} />}</>
  }

  return <>{stages[currentStage]?.component}</>
}
