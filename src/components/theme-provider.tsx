"use client"

import * as React from "react"

type Theme = "default" | "red" | "rose" | "orange" | "green" | "blue" | "yellow" | "violet"
type Mode = "light" | "dark"

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  defaultMode?: Mode
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  mode: Mode
  setTheme: (theme: Theme) => void
  setMode: (mode: Mode) => void
  toggleMode: () => void
}

const initialState: ThemeProviderState = {
  theme: "default",
  mode: "light",
  setTheme: () => null,
  setMode: () => null,
  toggleMode: () => null,
}

const ThemeProviderContext = React.createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = "default",
  defaultMode = "light",
  storageKey = "mediinspect-theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = React.useState<Theme>(defaultTheme)
  const [mode, setMode] = React.useState<Mode>(defaultMode)

  React.useEffect(() => {
    const root = window.document.documentElement

    // 移除所有主题类
    root.classList.remove(
      "theme-default", 
      "theme-red", 
      "theme-rose", 
      "theme-orange", 
      "theme-green", 
      "theme-blue", 
      "theme-yellow", 
      "theme-violet"
    )

    // 移除模式类
    root.classList.remove("light", "dark")

    // 添加当前主题类
    if (theme !== "default") {
      root.classList.add(`theme-${theme}`)
    }

    // 添加当前模式类
    root.classList.add(mode)
  }, [theme, mode])

  React.useEffect(() => {
    // 从localStorage读取保存的主题设置
    try {
      const stored = localStorage.getItem(storageKey)
      if (stored) {
        const { theme: storedTheme, mode: storedMode } = JSON.parse(stored)
        if (storedTheme) setTheme(storedTheme)
        if (storedMode) setMode(storedMode)
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error)
    }
  }, [storageKey])

  const value = {
    theme,
    mode,
    setTheme: (theme: Theme) => {
      setTheme(theme)
      try {
        localStorage.setItem(storageKey, JSON.stringify({ theme, mode }))
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error)
      }
    },
    setMode: (mode: Mode) => {
      setMode(mode)
      try {
        localStorage.setItem(storageKey, JSON.stringify({ theme, mode }))
      } catch (error) {
        console.warn('Failed to save mode to localStorage:', error)
      }
    },
    toggleMode: () => {
      const newMode = mode === "light" ? "dark" : "light"
      setMode(newMode)
      try {
        localStorage.setItem(storageKey, JSON.stringify({ theme, mode: newMode }))
      } catch (error) {
        console.warn('Failed to save mode to localStorage:', error)
      }
    },
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = React.useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")

  return context
}
