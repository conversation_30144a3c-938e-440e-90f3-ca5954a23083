'use client'

import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Star, 
  Zap, 
  Clock, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Sparkles,
  Flame
} from 'lucide-react'
import { NavigationItem } from '@/config/navigation'

interface StatusIndicatorProps {
  item: NavigationItem
  size?: 'sm' | 'md' | 'lg'
  showTooltip?: boolean
}

/**
 * 导航状态指示器组件
 * 显示新功能、热门、更新等状态标识
 */
export function NavigationStatusIndicator({ 
  item, 
  size = 'sm', 
  showTooltip = true 
}: StatusIndicatorProps) {
  const indicators = []

  // 新功能标识
  if (item.isNew) {
    const newBadge = (
      <Badge 
        key="new" 
        variant="secondary" 
        className={`
          ${size === 'sm' ? 'text-xs px-1.5 py-0.5' : size === 'md' ? 'text-sm px-2 py-1' : 'text-base px-3 py-1.5'}
          bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 animate-pulse
        `}
      >
        <Zap className={`${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'} mr-1`} />
        新功能
      </Badge>
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="new-tooltip">
          <TooltipTrigger asChild>
            {newBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>这是一个新功能，快来体验吧！</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(newBadge)
    }
  }

  // 热门标识
  if (item.badge === 'hot') {
    const hotBadge = (
      <Badge 
        key="hot" 
        variant="secondary" 
        className={`
          ${size === 'sm' ? 'text-xs px-1.5 py-0.5' : size === 'md' ? 'text-sm px-2 py-1' : 'text-base px-3 py-1.5'}
          bg-gradient-to-r from-red-500 to-orange-500 text-white border-0
        `}
      >
        <Flame className={`${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'} mr-1`} />
        热门
      </Badge>
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="hot-tooltip">
          <TooltipTrigger asChild>
            {hotBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>这是一个热门功能，使用频率很高</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(hotBadge)
    }
  }

  // 数字徽章（如通知数量）
  if (typeof item.badge === 'number' && item.badge > 0) {
    const numberBadge = (
      <Badge 
        key="number" 
        variant="destructive" 
        className={`
          ${size === 'sm' ? 'text-xs px-1.5 py-0.5 min-w-[20px]' : size === 'md' ? 'text-sm px-2 py-1 min-w-[24px]' : 'text-base px-3 py-1.5 min-w-[28px]'}
          rounded-full flex items-center justify-center
        `}
      >
        {item.badge > 99 ? '99+' : item.badge}
      </Badge>
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="number-tooltip">
          <TooltipTrigger asChild>
            {numberBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>{item.badge} 个未处理项目</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(numberBadge)
    }
  }

  // 字符串徽章（自定义状态）
  if (typeof item.badge === 'string' && item.badge !== 'hot') {
    const customBadge = (
      <Badge 
        key="custom" 
        variant="outline" 
        className={`
          ${size === 'sm' ? 'text-xs px-1.5 py-0.5' : size === 'md' ? 'text-sm px-2 py-1' : 'text-base px-3 py-1.5'}
        `}
      >
        {item.badge}
      </Badge>
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="custom-tooltip">
          <TooltipTrigger asChild>
            {customBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>状态: {item.badge}</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(customBadge)
    }
  }

  if (indicators.length === 0) {
    return null
  }

  return (
    <div className="flex items-center gap-1">
      {indicators}
    </div>
  )
}

/**
 * 扩展的状态指示器，支持更多状态类型
 */
interface ExtendedStatusIndicatorProps {
  item: NavigationItem
  additionalStatus?: {
    isUpdated?: boolean
    isFavorite?: boolean
    isRecentlyUsed?: boolean
    hasNotifications?: boolean
    isDisabled?: boolean
  }
  size?: 'sm' | 'md' | 'lg'
  showTooltip?: boolean
}

export function ExtendedNavigationStatusIndicator({ 
  item, 
  additionalStatus = {},
  size = 'sm', 
  showTooltip = true 
}: ExtendedStatusIndicatorProps) {
  const indicators = []

  // 基础状态指示器
  const baseIndicator = (
    <NavigationStatusIndicator 
      key="base"
      item={item} 
      size={size} 
      showTooltip={showTooltip} 
    />
  )
  
  if (baseIndicator) {
    indicators.push(baseIndicator)
  }

  // 更新状态
  if (additionalStatus.isUpdated) {
    const updatedBadge = (
      <Badge 
        key="updated" 
        variant="secondary" 
        className={`
          ${size === 'sm' ? 'text-xs px-1.5 py-0.5' : size === 'md' ? 'text-sm px-2 py-1' : 'text-base px-3 py-1.5'}
          bg-green-100 text-green-700 border-green-200
        `}
      >
        <CheckCircle className={`${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'} mr-1`} />
        已更新
      </Badge>
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="updated-tooltip">
          <TooltipTrigger asChild>
            {updatedBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>此功能最近有更新</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(updatedBadge)
    }
  }

  // 收藏状态
  if (additionalStatus.isFavorite) {
    const favoriteBadge = (
      <Star 
        key="favorite"
        className={`
          ${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'}
          text-yellow-500 fill-yellow-500
        `}
      />
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="favorite-tooltip">
          <TooltipTrigger asChild>
            {favoriteBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>已收藏</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(favoriteBadge)
    }
  }

  // 最近使用
  if (additionalStatus.isRecentlyUsed) {
    const recentBadge = (
      <Clock 
        key="recent"
        className={`
          ${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'}
          text-blue-500
        `}
      />
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="recent-tooltip">
          <TooltipTrigger asChild>
            {recentBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>最近使用过</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(recentBadge)
    }
  }

  // 通知状态
  if (additionalStatus.hasNotifications) {
    indicators.push(
      <div 
        key="notification"
        className={`
          ${size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'}
          bg-red-500 rounded-full animate-pulse
        `}
      />
    )
  }

  // 禁用状态
  if (additionalStatus.isDisabled) {
    const disabledBadge = (
      <Badge 
        key="disabled" 
        variant="secondary" 
        className={`
          ${size === 'sm' ? 'text-xs px-1.5 py-0.5' : size === 'md' ? 'text-sm px-2 py-1' : 'text-base px-3 py-1.5'}
          bg-gray-100 text-gray-500 border-gray-200
        `}
      >
        <AlertCircle className={`${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'} mr-1`} />
        禁用
      </Badge>
    )

    if (showTooltip) {
      indicators.push(
        <Tooltip key="disabled-tooltip">
          <TooltipTrigger asChild>
            {disabledBadge}
          </TooltipTrigger>
          <TooltipContent>
            <p>此功能暂时不可用</p>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      indicators.push(disabledBadge)
    }
  }

  if (indicators.length === 0) {
    return null
  }

  return (
    <div className="flex items-center gap-1 flex-wrap">
      {indicators}
    </div>
  )
}
