'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import { Home, ArrowLeft } from 'lucide-react'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { getBreadcrumbs, type BreadcrumbItem as BreadcrumbItemType } from '@/config/navigation'
import { cn } from '@/lib/utils'

interface SimpleBreadcrumbProps {
  items?: BreadcrumbItemType[]
  dynamicData?: any
  showBackButton?: boolean
  className?: string
}

/**
 * 简化的面包屑导航组件
 * 专注于核心导航功能，移除复制、刷新等多余操作
 */
export function SimpleBreadcrumb({
  items,
  dynamicData,
  showBackButton = true,
  className
}: SimpleBreadcrumbProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItemType[]>([])

  // 获取面包屑数据
  useEffect(() => {
    if (items) {
      setBreadcrumbItems(items)
    } else {
      const generatedItems = getBreadcrumbs(pathname, dynamicData)
      setBreadcrumbItems(generatedItems)
    }
  }, [pathname, items, dynamicData])

  // 返回上一页
  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back()
    }
  }

  if (breadcrumbItems.length === 0) {
    return null
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* 返回按钮 */}
      {showBackButton && breadcrumbItems.length > 1 && (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleGoBack}
          className="h-8 w-8 shrink-0"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">返回上一页</span>
        </Button>
      )}

      {/* 面包屑导航 */}
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbItems.map((item, index) => {
            const isLast = index === breadcrumbItems.length - 1

            return (
              <div key={item.href} className="flex items-center">
                <BreadcrumbItem>
                  {isLast ? (
                    <BreadcrumbPage className="max-w-20 truncate md:max-w-none">
                      {item.title}
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link href={item.href} className="max-w-20 truncate md:max-w-none">
                        {index === 0 && (
                          <Home className="h-4 w-4 mr-1" />
                        )}
                        {item.title}
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {!isLast && <BreadcrumbSeparator />}
              </div>
            )
          })}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
}

/**
 * 桌面端面包屑组件（移除移动端适配和多余操作）
 */
export function DesktopBreadcrumb({
  items,
  dynamicData,
  className
}: Omit<SimpleBreadcrumbProps, 'showBackButton'>) {
  return (
    <SimpleBreadcrumb
      items={items}
      dynamicData={dynamicData}
      showBackButton={false}
      className={className}
    />
  )
}
