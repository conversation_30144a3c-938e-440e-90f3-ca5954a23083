'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Search, Star, Zap, Clock, ArrowRight, Hash, Command as CommandIcon } from 'lucide-react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import {
  Dialog,
  DialogContent,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Kbd } from '@/components/ui/kbd'
import { NavigationItem } from '@/config/navigation'
import { useNavigationPermissions } from '@/hooks/use-navigation-permissions'
import { navigationConfig } from '@/config/navigation'
import { cn } from '@/lib/utils'

interface SearchResult extends NavigationItem {
  category: string
  matchScore: number
  recentlyUsed?: boolean
  popular?: boolean
  path: string[]
}

interface SearchHistory {
  href: string
  title: string
  timestamp: Date
  count: number
}

interface NavigationSearchProps {
  className?: string
  placeholder?: string
  showTrigger?: boolean
}

/**
 * 增强的导航搜索组件
 * 支持快捷键、智能搜索、历史记录等功能
 */
export function NavigationSearch({ 
  className, 
  placeholder = "搜索功能...",
  showTrigger = true 
}: NavigationSearchProps) {
  const router = useRouter()
  const { filteredNavigation } = useNavigationPermissions(navigationConfig)
  
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [popularItems, setPopularItems] = useState<string[]>([])

  /**
   * 扁平化导航项，包含完整路径信息
   */
  const flattenedNavigation = useMemo(() => {
    const flatten = (
      items: NavigationItem[], 
      category = '主要功能',
      path: string[] = []
    ): SearchResult[] => {
      const results: SearchResult[] = []
      
      items.forEach(item => {
        const currentPath = [...path, item.title]
        
        if (item.href && item.href !== '#') {
          results.push({
            ...item,
            category,
            matchScore: 0,
            recentlyUsed: searchHistory.some(h => h.href === item.href),
            popular: popularItems.includes(item.href),
            path: currentPath
          })
        }
        
        if (item.children) {
          results.push(...flatten(item.children, item.title, currentPath))
        }
      })
      
      return results
    }
    
    return flatten(filteredNavigation)
  }, [filteredNavigation, searchHistory, popularItems])

  /**
   * 智能搜索算法
   */
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) {
      // 无搜索词时显示最近使用和热门项目
      const recentItems = flattenedNavigation
        .filter(item => item.recentlyUsed)
        .sort((a, b) => {
          const aHistory = searchHistory.find(h => h.href === a.href)
          const bHistory = searchHistory.find(h => h.href === b.href)
          return (bHistory?.timestamp.getTime() || 0) - (aHistory?.timestamp.getTime() || 0)
        })
        .slice(0, 5)

      const popularItemsList = flattenedNavigation
        .filter(item => item.popular || item.badge === 'hot' || item.isNew)
        .slice(0, 4)

      return { recent: recentItems, popular: popularItemsList, search: [] }
    }

    const query = searchQuery.toLowerCase()
    const searchResults = flattenedNavigation
      .map(item => {
        let score = 0
        const title = item.title.toLowerCase()
        const description = item.description?.toLowerCase() || ''
        const category = item.category.toLowerCase()
        const pathText = item.path.join(' ').toLowerCase()
        
        // 精确匹配 - 最高分
        if (title === query) score += 100
        else if (title.includes(query)) score += 60
        else if (description.includes(query)) score += 40
        else if (category.includes(query)) score += 30
        else if (pathText.includes(query)) score += 20
        
        // 首字母匹配
        if (title.startsWith(query)) score += 50
        
        // 拼音首字母匹配（简单实现）
        const titlePinyin = title.replace(/[\u4e00-\u9fa5]/g, '')
        if (titlePinyin.toLowerCase().includes(query)) score += 25
        
        // 分词匹配
        const queryChars = query.split('')
        const titleChars = title.split('')
        const matchingChars = queryChars.filter(char => titleChars.includes(char))
        score += (matchingChars.length / queryChars.length) * 15
        
        // 权重加分
        if (item.popular || item.badge === 'hot') score += 15
        if (item.isNew) score += 10
        if (item.recentlyUsed) score += 20
        
        return { ...item, matchScore: score }
      })
      .filter(item => item.matchScore > 0)
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 8)

    return { recent: [], popular: [], search: searchResults }
  }, [searchQuery, flattenedNavigation, searchHistory, popularItems])

  /**
   * 处理搜索项选择
   */
  const handleSelect = useCallback((item: SearchResult) => {
    // 记录搜索历史
    setSearchHistory(prev => {
      const existingIndex = prev.findIndex(h => h.href === item.href)
      if (existingIndex >= 0) {
        const updated = [...prev]
        const existingItem = updated[existingIndex]
        if (existingItem) {
          updated[existingIndex] = {
            ...existingItem,
            count: existingItem.count + 1,
            timestamp: new Date(),
            href: existingItem.href || '',
            title: existingItem.title || ''
          }
        }
        return updated
      } else {
        return [
          { href: item.href, title: item.title, timestamp: new Date(), count: 1 },
          ...prev.slice(0, 9) // 保留最近10条
        ]
      }
    })

    // 跳转
    router.push(item.href)
    setOpen(false)
    setSearchQuery('')
  }, [router])

  /**
   * 快捷键处理
   */
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setOpen(true)
      }
      if (e.key === 'Escape') {
        setOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  /**
   * 渲染状态指示器
   */
  const renderStatusIndicators = (item: SearchResult) => {
    const indicators = []
    
    if (item.isNew) {
      indicators.push(
        <Badge key="new" variant="secondary" className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
          <Zap className="h-3 w-3 mr-1" />
          新功能
        </Badge>
      )
    }
    
    if (item.badge === 'hot' || item.popular) {
      indicators.push(
        <Badge key="hot" variant="secondary" className="text-xs bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300">
          <Star className="h-3 w-3 mr-1" />
          热门
        </Badge>
      )
    }
    
    if (item.recentlyUsed) {
      indicators.push(
        <Badge key="recent" variant="outline" className="text-xs">
          <Clock className="h-3 w-3 mr-1" />
          最近使用
        </Badge>
      )
    }
    
    return indicators
  }

  /**
   * 渲染搜索结果项
   */
  const renderSearchItem = (item: SearchResult, showCategory = true) => (
    <CommandItem
      key={item.href}
      value={`${item.title} ${item.description} ${item.category}`}
      onSelect={() => handleSelect(item)}
      className="flex items-center gap-3 p-3 cursor-pointer"
    >
      <div className="flex items-center justify-center w-8 h-8 rounded-md bg-muted">
        {item.icon && <item.icon className="h-4 w-4" />}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium truncate">{item.title}</span>
          {renderStatusIndicators(item)}
        </div>
        
        {item.description && (
          <p className="text-xs text-muted-foreground truncate">
            {item.description}
          </p>
        )}
        
        {showCategory && item.path.length > 1 && (
          <div className="flex items-center gap-1 mt-1">
            <Hash className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {item.path.slice(0, -1).join(' > ')}
            </span>
          </div>
        )}
      </div>
      
      <ArrowRight className="h-4 w-4 text-muted-foreground" />
    </CommandItem>
  )

  // 加载搜索历史和热门项目
  useEffect(() => {
    const savedHistory = localStorage.getItem('navigation-search-history')
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
        setSearchHistory(history)
      } catch (error) {
        console.error('加载搜索历史失败:', error)
      }
    }

    // 设置热门项目
    setPopularItems(['/medical-cases', '/supervision-rules', '/analytics'])
  }, [])

  // 保存搜索历史
  useEffect(() => {
    if (searchHistory.length > 0) {
      localStorage.setItem('navigation-search-history', JSON.stringify(searchHistory))
    }
  }, [searchHistory])

  return (
    <>
      {showTrigger && (
        <div className={cn("relative", className)}>
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={placeholder}
            className="w-64 pl-9 pr-16 cursor-pointer"
            onClick={() => setOpen(true)}
            readOnly
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
            <Kbd>⌘</Kbd>
            <Kbd>K</Kbd>
          </div>
        </div>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="p-0 max-w-2xl">
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="搜索功能、页面或设置..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="border-none focus:ring-0 text-base"
              />
            </div>
            
            <CommandList className="max-h-96">
              {searchResults.search.length > 0 && (
                <CommandGroup heading="搜索结果">
                  {searchResults.search.map(item => renderSearchItem(item))}
                </CommandGroup>
              )}
              
              {searchResults.recent.length > 0 && (
                <>
                  {searchResults.search.length > 0 && <CommandSeparator />}
                  <CommandGroup heading="最近使用">
                    {searchResults.recent.map(item => renderSearchItem(item, false))}
                  </CommandGroup>
                </>
              )}
              
              {searchResults.popular.length > 0 && (
                <>
                  {(searchResults.search.length > 0 || searchResults.recent.length > 0) && <CommandSeparator />}
                  <CommandGroup heading="热门功能">
                    {searchResults.popular.map(item => renderSearchItem(item, false))}
                  </CommandGroup>
                </>
              )}
              
              {searchQuery && searchResults.search.length === 0 && (
                <CommandEmpty>
                  <div className="text-center py-6">
                    <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      未找到匹配的功能
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      尝试使用不同的关键词
                    </p>
                  </div>
                </CommandEmpty>
              )}
              
              {!searchQuery && searchResults.recent.length === 0 && searchResults.popular.length === 0 && (
                <div className="text-center py-8">
                  <CommandIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    开始输入以搜索功能
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    支持功能名称、描述和拼音搜索
                  </p>
                </div>
              )}
            </CommandList>
          </Command>
        </DialogContent>
      </Dialog>
    </>
  )
}
