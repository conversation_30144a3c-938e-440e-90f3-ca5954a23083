'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { useNavigationPermissions } from '@/hooks/use-navigation-permissions'
import { navigationConfig } from '@/config/navigation'
import { toast } from 'sonner'
import { Shield, AlertTriangle, CheckCircle, Info } from 'lucide-react'

interface PermissionChange {
  type: 'granted' | 'revoked' | 'updated'
  navigationItem: string
  timestamp: Date
  reason?: string
}

/**
 * 权限监控组件
 * 监控用户权限变更并提供实时通知
 */
export function PermissionMonitor() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const {
    permissionVersion,
    validateNavigationPath,
    addPermissionListener,
    permissionSummary
  } = useNavigationPermissions(navigationConfig)

  const [previousPermissionVersion, setPreviousPermissionVersion] = useState<number>(0)
  const [permissionChanges, setPermissionChanges] = useState<PermissionChange[]>([])

  /**
   * 检查当前页面权限
   */
  const checkCurrentPagePermission = () => {
    if (!isAuthenticated) return

    const currentPath = window.location.pathname
    const hasPermission = validateNavigationPath(currentPath)

    if (!hasPermission) {
      toast.error('权限已变更', {
        description: '您已失去访问当前页面的权限，即将跳转到首页',
        icon: <AlertTriangle className="h-4 w-4" />,
        duration: 5000,
      })

      // 延迟跳转，给用户时间看到通知
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)
    }
  }

  /**
   * 处理权限变更
   */
  const handlePermissionChange = () => {
    if (previousPermissionVersion === 0) {
      // 首次加载，不显示通知
      setPreviousPermissionVersion(permissionVersion)
      return
    }

    if (permissionVersion > previousPermissionVersion) {
      // 权限发生变更
      const change: PermissionChange = {
        type: 'updated',
        navigationItem: '导航权限',
        timestamp: new Date(),
        reason: '用户角色或权限已更新'
      }

      setPermissionChanges(prev => [change, ...prev.slice(0, 9)]) // 保留最近10条记录

      // 显示权限变更通知
      toast.info('权限已更新', {
        description: `您的访问权限已更新，可访问 ${permissionSummary.accessibleItems} 个功能模块`,
        icon: <Info className="h-4 w-4" />,
        duration: 4000,
      })

      // 检查当前页面权限
      checkCurrentPagePermission()

      setPreviousPermissionVersion(permissionVersion)
    }
  }

  /**
   * 显示权限摘要
   */
  const showPermissionSummary = () => {
    const { permissionLevel, userRoles, accessibleItems } = permissionSummary

    let icon = <Shield className="h-4 w-4" />
    let title = '权限信息'
    let description = ''

    switch (permissionLevel) {
      case 'admin':
        icon = <CheckCircle className="h-4 w-4 text-green-500" />
        title = '管理员权限'
        description = `您拥有完整的系统管理权限，可访问所有 ${accessibleItems} 个功能模块`
        break
      case 'standard':
        icon = <Shield className="h-4 w-4 text-blue-500" />
        title = '标准权限'
        description = `您拥有监督员权限，可访问 ${accessibleItems} 个功能模块`
        break
      case 'limited':
        icon = <Info className="h-4 w-4 text-yellow-500" />
        title = '受限权限'
        description = `您拥有有限权限，可访问 ${accessibleItems} 个功能模块`
        break
      default:
        icon = <AlertTriangle className="h-4 w-4 text-red-500" />
        title = '无权限'
        description = '您当前没有任何访问权限'
    }

    toast.info(title, {
      description,
      icon,
      duration: 3000,
    })
  }

  // 监听权限变更
  useEffect(() => {
    const removeListener = addPermissionListener(handlePermissionChange)
    return removeListener
  }, [addPermissionListener, permissionVersion, previousPermissionVersion])

  // 监听路由变更，检查页面权限
  useEffect(() => {
    if (isAuthenticated) {
      checkCurrentPagePermission()
    }
  }, [isAuthenticated])

  // 定期检查权限状态（可选）
  useEffect(() => {
    if (!isAuthenticated || !user) return

    // 每5分钟检查一次权限状态
    const interval = setInterval(() => {
      // 这里可以调用API检查权限是否有变更
      // 实际项目中可以通过WebSocket实现实时权限推送
      console.log('🔍 定期权限检查:', new Date().toLocaleTimeString())
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [isAuthenticated, user])

  // 开发环境下的权限调试信息
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && user) {
      console.log('🔐 权限监控状态:', {
        用户: user.username,
        角色: user.roles.map(r => r.roleName).join(', '),
        权限版本: permissionVersion,
        可访问模块: permissionSummary.accessibleItems,
        权限级别: permissionSummary.permissionLevel
      })
    }
  }, [user, permissionVersion, permissionSummary])

  // 这个组件不渲染任何UI，只负责权限监控
  return null
}

/**
 * 权限状态指示器组件
 * 可以在需要的地方显示当前权限状态
 */
export function PermissionStatusIndicator() {
  const { permissionSummary } = useNavigationPermissions(navigationConfig)
  const { user } = useAuth()

  if (!user) return null

  const { permissionLevel, accessibleItems } = permissionSummary

  const getStatusColor = () => {
    switch (permissionLevel) {
      case 'admin': return 'text-green-600'
      case 'standard': return 'text-blue-600'
      case 'limited': return 'text-yellow-600'
      default: return 'text-red-600'
    }
  }

  const getStatusIcon = () => {
    switch (permissionLevel) {
      case 'admin': return <CheckCircle className="h-3 w-3" />
      case 'standard': return <Shield className="h-3 w-3" />
      case 'limited': return <Info className="h-3 w-3" />
      default: return <AlertTriangle className="h-3 w-3" />
    }
  }

  return (
    <div className={`flex items-center gap-1 text-xs ${getStatusColor()}`}>
      {getStatusIcon()}
      <span>{accessibleItems} 个模块</span>
    </div>
  )
}
