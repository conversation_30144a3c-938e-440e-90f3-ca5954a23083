'use client'

import { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Search, Star, Zap, Clock, ArrowRight, Hash } from 'lucide-react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { NavigationItem } from '@/config/navigation'
import { useNavigationPermissions } from '@/hooks/use-navigation-permissions'
import { navigationConfig } from '@/config/navigation'

interface SearchResult extends NavigationItem {
  category: string
  matchScore: number
  recentlyUsed?: boolean
  popular?: boolean
}

interface SearchHistory {
  href: string
  title: string
  timestamp: Date
  count: number
}

/**
 * 增强的导航搜索组件
 */
export function EnhancedNavigationSearch() {
  const router = useRouter()
  const { filteredNavigation, checkNavigationPermission } = useNavigationPermissions(navigationConfig)
  
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [popularItems, setPopularItems] = useState<string[]>([])

  /**
   * 扁平化导航项，包含分类信息
   */
  const flattenedNavigation = useMemo(() => {
    const flatten = (items: NavigationItem[], category = '主要功能'): SearchResult[] => {
      const results: SearchResult[] = []
      
      items.forEach(item => {
        if (item.href && item.href !== '#') {
          results.push({
            ...item,
            category,
            matchScore: 0,
            recentlyUsed: searchHistory.some(h => h.href === item.href),
            popular: popularItems.includes(item.href)
          })
        }
        
        if (item.children) {
          results.push(...flatten(item.children, item.title))
        }
      })
      
      return results
    }
    
    return flatten(filteredNavigation)
  }, [filteredNavigation, searchHistory, popularItems])

  /**
   * 搜索算法 - 支持拼音、关键词、模糊匹配
   */
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) {
      // 无搜索词时显示最近使用和热门项目
      const recentItems = flattenedNavigation
        .filter(item => item.recentlyUsed)
        .sort((a, b) => {
          const aHistory = searchHistory.find(h => h.href === a.href)
          const bHistory = searchHistory.find(h => h.href === b.href)
          return (bHistory?.timestamp.getTime() || 0) - (aHistory?.timestamp.getTime() || 0)
        })
        .slice(0, 5)

      const popularItemsList = flattenedNavigation
        .filter(item => item.popular || item.badge === 'hot')
        .slice(0, 3)

      return { recent: recentItems, popular: popularItemsList, search: [] }
    }

    const query = searchQuery.toLowerCase()
    const searchResults = flattenedNavigation
      .map(item => {
        let score = 0
        const title = item.title.toLowerCase()
        const description = item.description?.toLowerCase() || ''
        
        // 精确匹配
        if (title === query) score += 100
        else if (title.includes(query)) score += 50
        else if (description.includes(query)) score += 30
        
        // 首字母匹配
        if (title.startsWith(query)) score += 40
        
        // 分词匹配
        const queryWords = query.split('')
        const titleWords = title.split('')
        const matchingWords = queryWords.filter(word => titleWords.includes(word))
        score += (matchingWords.length / queryWords.length) * 20
        
        // 热门项目加分
        if (item.popular || item.badge === 'hot') score += 10
        
        // 最近使用加分
        if (item.recentlyUsed) score += 15
        
        return { ...item, matchScore: score }
      })
      .filter(item => item.matchScore > 0)
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 8)

    return { recent: [], popular: [], search: searchResults }
  }, [searchQuery, flattenedNavigation, searchHistory, popularItems])

  /**
   * 处理搜索项选择
   */
  const handleSelect = (item: SearchResult) => {
    // 记录搜索历史
    const existingHistory = searchHistory.find(h => h.href === item.href)
    if (existingHistory) {
      existingHistory.count += 1
      existingHistory.timestamp = new Date()
    } else {
      setSearchHistory(prev => [
        { href: item.href, title: item.title, timestamp: new Date(), count: 1 },
        ...prev.slice(0, 9) // 保留最近10条
      ])
    }

    // 跳转
    router.push(item.href)
    setOpen(false)
    setSearchQuery('')
  }

  /**
   * 渲染状态指示器
   */
  const renderStatusIndicators = (item: SearchResult) => {
    const indicators = []
    
    if (item.isNew) {
      indicators.push(
        <Badge key="new" variant="secondary" className="text-xs bg-blue-100 text-blue-700">
          <Zap className="h-3 w-3 mr-1" />
          新功能
        </Badge>
      )
    }
    
    if (item.badge === 'hot' || item.popular) {
      indicators.push(
        <Badge key="hot" variant="secondary" className="text-xs bg-red-100 text-red-700">
          <Star className="h-3 w-3 mr-1" />
          热门
        </Badge>
      )
    }
    
    if (item.recentlyUsed) {
      indicators.push(
        <Badge key="recent" variant="outline" className="text-xs">
          <Clock className="h-3 w-3 mr-1" />
          最近使用
        </Badge>
      )
    }
    
    return indicators
  }

  /**
   * 渲染搜索结果项
   */
  const renderSearchItem = (item: SearchResult, showCategory = true) => (
    <CommandItem
      key={item.href}
      value={`${item.title} ${item.description} ${item.category}`}
      onSelect={() => handleSelect(item)}
      className="flex items-center gap-3 p-3 cursor-pointer"
    >
      <div className="flex items-center justify-center w-8 h-8 rounded-md bg-muted">
        {item.icon && <item.icon className="h-4 w-4" />}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium truncate">{item.title}</span>
          {renderStatusIndicators(item)}
        </div>
        
        {item.description && (
          <p className="text-xs text-muted-foreground truncate">
            {item.description}
          </p>
        )}
        
        {showCategory && (
          <div className="flex items-center gap-1 mt-1">
            <Hash className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">{item.category}</span>
          </div>
        )}
      </div>
      
      <ArrowRight className="h-4 w-4 text-muted-foreground" />
    </CommandItem>
  )

  // 加载搜索历史和热门项目
  useEffect(() => {
    const savedHistory = localStorage.getItem('navigation-search-history')
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
        setSearchHistory(history)
      } catch (error) {
        console.error('加载搜索历史失败:', error)
      }
    }

    // 模拟热门项目（实际项目中可以从API获取）
    setPopularItems(['/medical-cases', '/supervision-rules', '/analytics'])
  }, [])

  // 保存搜索历史
  useEffect(() => {
    if (searchHistory.length > 0) {
      localStorage.setItem('navigation-search-history', JSON.stringify(searchHistory))
    }
  }, [searchHistory])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="搜索功能..."
            className="w-64 pl-9 cursor-pointer"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => setOpen(true)}
          />
        </div>
      </PopoverTrigger>
      
      <PopoverContent className="w-96 p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="搜索功能、页面或设置..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="border-none focus:ring-0"
          />
          
          <CommandList className="max-h-96">
            {searchResults.search.length > 0 && (
              <CommandGroup heading="搜索结果">
                {searchResults.search.map(item => renderSearchItem(item))}
              </CommandGroup>
            )}
            
            {searchResults.recent.length > 0 && (
              <>
                {searchResults.search.length > 0 && <CommandSeparator />}
                <CommandGroup heading="最近使用">
                  {searchResults.recent.map(item => renderSearchItem(item, false))}
                </CommandGroup>
              </>
            )}
            
            {searchResults.popular.length > 0 && (
              <>
                {(searchResults.search.length > 0 || searchResults.recent.length > 0) && <CommandSeparator />}
                <CommandGroup heading="热门功能">
                  {searchResults.popular.map(item => renderSearchItem(item, false))}
                </CommandGroup>
              </>
            )}
            
            {searchQuery && searchResults.search.length === 0 && (
              <CommandEmpty>
                <div className="text-center py-6">
                  <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    未找到匹配的功能
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    尝试使用不同的关键词
                  </p>
                </div>
              </CommandEmpty>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
