"use client"

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { UserRole } from '@/types/auth'
import { Shield, AlertTriangle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface RouteGuardProps {
  children: React.ReactNode
  requiredRoles?: UserRole[]
  fallbackPath?: string
  showUnauthorized?: boolean
}

export function RouteGuard({
  children,
  requiredRoles = [],
  fallbackPath = '/auth/login',
  showUnauthorized = true
}: RouteGuardProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // 如果正在加载，不做任何操作
    if (isLoading) return

    // 如果未认证，直接重定向到登录页（企业级方案：无中间页面）
    if (!isAuthenticated) {
      const redirectUrl = encodeURIComponent(pathname)
      // 使用 Next.js 路由进行重定向
      router.replace(`${fallbackPath}?redirect=${redirectUrl}`)
      return
    }

    // 如果需要特定角色但用户没有权限
    if (requiredRoles.length > 0 && user) {
      const userRoles = user.roles.map(role => role.roleCode)
      const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role))

      if (!hasRequiredRole) {
        // 如果不显示未授权页面，重定向到仪表板
        if (!showUnauthorized) {
          router.replace('/dashboard')
        }
      }
    }
  }, [isAuthenticated, isLoading, user, requiredRoles, router, pathname, fallbackPath, showUnauthorized])

  // 加载状态 - 显示简单的加载界面避免水合错误
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">正在验证身份...</h3>
            <p className="text-sm text-muted-foreground">请稍候</p>
          </div>
        </div>
      </div>
    )
  }

  // 未认证状态 - 企业级方案：直接重定向，不显示中间页面
  // 这里只是防御性代码，正常情况下 useEffect 已经处理了重定向
  if (!isAuthenticated) {
    // 如果 useEffect 还没执行重定向，显示加载状态而不是登录提示页面
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">正在跳转...</h3>
            <p className="text-sm text-muted-foreground">请稍候</p>
          </div>
        </div>
      </div>
    )
  }

  // 权限检查
  if (requiredRoles.length > 0 && user) {
    const userRoles = user.roles.map(role => role.roleCode)
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role))
    
    if (!hasRequiredRole) {
      if (showUnauthorized) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-background">
            <Card className="w-full max-w-md">
              <CardHeader className="text-center">
                <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
                <CardTitle>访问被拒绝</CardTitle>
                <CardDescription>
                  您没有权限访问此页面
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  <p>需要的角色: {requiredRoles.join('、')}</p>
                  <p>您的角色: {user.roles.map(role => role.roleName).join('、')}</p>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => router.back()}
                    className="flex-1"
                  >
                    返回上页
                  </Button>
                  <Button 
                    onClick={() => router.push('/dashboard')}
                    className="flex-1"
                  >
                    回到首页
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )
      }
      
      // 不显示未授权页面，返回null（会被useEffect重定向）
      return null
    }
  }

  // 通过所有检查，渲染子组件
  return <>{children}</>
}

/**
 * 高阶组件：为页面添加路由守卫
 */
export function withRouteGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<RouteGuardProps, 'children'> = {}
) {
  return function GuardedComponent(props: P) {
    return (
      <RouteGuard {...options}>
        <Component {...props} />
      </RouteGuard>
    )
  }
}

/**
 * Hook：检查当前用户是否有指定权限
 */
export function usePermission(requiredRoles: UserRole[] = []) {
  const { user, isAuthenticated } = useAuth()
  
  if (!isAuthenticated || !user) {
    return {
      hasPermission: false,
      userRoles: [],
      missingRoles: requiredRoles
    }
  }
  
  const userRoles = user.roles.map(role => role.roleCode)
  const hasPermission = requiredRoles.length === 0 || 
    requiredRoles.some(role => userRoles.includes(role))
  
  const missingRoles = requiredRoles.filter(role => !userRoles.includes(role))
  
  return {
    hasPermission,
    userRoles,
    missingRoles
  }
}
