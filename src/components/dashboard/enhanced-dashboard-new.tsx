"use client"

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/lib/toast'
import { 
  Activity, 
  AlertTriangle, 
  BarChart3, 
  Shield, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Settings,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  FileText,
  Users
} from 'lucide-react'

// 增强型数据接口
interface EnhancedDashboardMetrics {
  // 监管核心指标
  supervisionMetrics: {
    totalRules: number
    activeRules: number
    executingRules: number
    triggeredToday: number
    riskCasesDetected: number
    fraudDetectionRate: number
    avgExecutionTime: number
    systemHealthScore: number
  }
  
  // 实时执行状态
  ruleExecutionStatus: {
    runningExecutions: RuleExecution[]
    recentCompletions: RuleExecution[]
    failedExecutions: RuleExecution[]
    queuedExecutions: number
  }
  
  // 风险分析
  riskAnalysis: {
    criticalAlerts: Alert[]
    riskTrends: RiskTrend[]
    complianceScore: number
    anomalyDetections: AnomalyDetection[]
  }
  
  // 业务影响
  businessImpact: {
    fundsSaved: number
    casesReviewed: number
    complianceRate: number
    processingEfficiency: number
  }
}

interface RuleExecution {
  id: string
  ruleId: string
  ruleName: string
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED'
  startTime: Date
  endTime?: Date
  progress?: number
  matchedCases?: number
  errorMessage?: string
}

interface Alert {
  id: string
  type: 'CRITICAL' | 'WARNING' | 'INFO'
  title: string
  description: string
  timestamp: Date
  ruleId?: string
  caseId?: string
  acknowledged: boolean
}

interface RiskTrend {
  date: string
  riskScore: number
  detectedCases: number
  category: string
}

interface AnomalyDetection {
  id: string
  type: string
  description: string
  severity: 'HIGH' | 'MEDIUM' | 'LOW'
  confidence: number
  timestamp: Date
  relatedData: any
}

// 实时数据Hook
const useRealTimeDashboardData = () => {
  const [metrics, setMetrics] = useState<EnhancedDashboardMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const { toast } = useToast()

  // 获取初始数据
  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true)
      
      // 模拟API调用 - 实际项目中应该调用真实API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockData: EnhancedDashboardMetrics = {
        supervisionMetrics: {
          totalRules: 89,
          activeRules: 76,
          executingRules: 3,
          triggeredToday: 23,
          riskCasesDetected: 42,
          fraudDetectionRate: 8.7,
          avgExecutionTime: 245,
          systemHealthScore: 92
        },
        ruleExecutionStatus: {
          runningExecutions: [
            {
              id: '1',
              ruleId: 'rule-123',
              ruleName: '单次费用上限检查',
              status: 'RUNNING',
              startTime: new Date(Date.now() - 5 * 60 * 1000),
              progress: 65,
              matchedCases: 12
            },
            {
              id: '2',
              ruleId: 'rule-456',
              ruleName: '重复报销检测',
              status: 'PENDING',
              startTime: new Date(Date.now() - 2 * 60 * 1000)
            }
          ],
          recentCompletions: [
            {
              id: '3',
              ruleId: 'rule-789',
              ruleName: '医疗服务合规性检查',
              status: 'SUCCESS',
              startTime: new Date(Date.now() - 30 * 60 * 1000),
              endTime: new Date(Date.now() - 25 * 60 * 1000),
              matchedCases: 5
            }
          ],
          failedExecutions: [
            {
              id: '4',
              ruleId: 'rule-101',
              ruleName: '异常费用模式检测',
              status: 'FAILED',
              startTime: new Date(Date.now() - 45 * 60 * 1000),
              endTime: new Date(Date.now() - 44 * 60 * 1000),
              errorMessage: '数据源连接超时'
            }
          ],
          queuedExecutions: 2
        },
        riskAnalysis: {
          criticalAlerts: [
            {
              id: 'alert-1',
              type: 'CRITICAL',
              title: '检测到可疑的重复报销行为',
              description: '患者张三在多家医院提交了相同诊断的报销申请',
              timestamp: new Date(Date.now() - 15 * 60 * 1000),
              caseId: 'case-123',
              acknowledged: false
            },
            {
              id: 'alert-2',
              type: 'WARNING',
              title: '单次费用超出阈值',
              description: '李四的住院费用超出了同类治疗的平均值的150%',
              timestamp: new Date(Date.now() - 120 * 60 * 1000),
              caseId: 'case-456',
              acknowledged: false
            }
          ],
          riskTrends: [
            { date: '2023-01-01', riskScore: 75, detectedCases: 12, category: '费用异常' },
            { date: '2023-01-02', riskScore: 68, detectedCases: 8, category: '费用异常' },
            { date: '2023-01-03', riskScore: 82, detectedCases: 15, category: '费用异常' }
          ],
          complianceScore: 87,
          anomalyDetections: [
            {
              id: 'anomaly-1',
              type: '费用异常',
              description: '检测到某医院心脏手术费用异常增长',
              severity: 'HIGH',
              confidence: 0.92,
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              relatedData: { hospitalId: 'hosp-123', procedureCode: 'CARD-001' }
            },
            {
              id: 'anomaly-2',
              type: '诊断异常',
              description: '患者诊断与治疗方案不匹配',
              severity: 'MEDIUM',
              confidence: 0.78,
              timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
              relatedData: { patientId: 'pat-456', diagnosisCode: 'DIAG-789' }
            }
          ]
        },
        businessImpact: {
          fundsSaved: 12500000,
          casesReviewed: 5678,
          complianceRate: 94.5,
          processingEfficiency: 87.2
        }
      }
      
      setMetrics(mockData)
      setLastUpdated(new Date())
      
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
      toast({
        variant: "destructive",
        title: "数据加载失败",
        description: error instanceof Error ? error.message : "请检查网络连接后重试"
      })
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  // 初始化数据加载
  useEffect(() => {
    fetchInitialData()
    
    // 设置定时刷新
    const interval = setInterval(() => {
      if (document.visibilityState === 'visible') {
        fetchInitialData()
      }
    }, 30000) // 30秒刷新一次
    
    return () => clearInterval(interval)
  }, [fetchInitialData])

  return {
    metrics,
    isLoading,
    lastUpdated,
    refetch: fetchInitialData
  }
}

// 工作台页面头部组件
const DashboardHeader: React.FC<{
  user: any
  lastUpdated: Date | null
  onRefresh: () => void
}> = ({ user, lastUpdated, onRefresh }) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">工作台</h1>
        <p className="text-muted-foreground">
          欢迎回来，{user?.realName}！
          {lastUpdated && (
            <span className="ml-2">
              最后更新: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={onRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          刷新
        </Button>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          配置
        </Button>
      </div>
    </div>
  )
}

// 加载状态组件
const LoadingState: React.FC = () => {
  return (
    <div className="flex items-center justify-center h-96">
      <div className="flex items-center space-x-2">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span>加载工作台数据...</span>
      </div>
    </div>
  )
}

// 主要的增强型工作台组件
export const EnhancedDashboard: React.FC = () => {
  const { user } = useAuth()
  const router = useRouter()
  const { metrics, isLoading, lastUpdated, refetch } = useRealTimeDashboardData()
  
  // 格式化数字显示
  const formatNumber = useCallback((num: number) => {
    if (num >= 100000000) return (num / 100000000).toFixed(1) + '亿'
    if (num >= 10000) return (num / 10000).toFixed(1) + '万'
    return num.toLocaleString()
  }, [])

  // 格式化金额
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }, [])

  if (isLoading && !metrics) {
    return <LoadingState />
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <DashboardHeader 
        user={user} 
        lastUpdated={lastUpdated}
        onRefresh={refetch}
      />
      
      {/* 核心指标卡片 - 始终可见 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃规则</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.supervisionMetrics.activeRules || 0}</div>
            <p className="text-xs text-muted-foreground">
              总计 {metrics?.supervisionMetrics.totalRules || 0} 个规则
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日检测</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.supervisionMetrics.riskCasesDetected || 0}</div>
            <p className="text-xs text-muted-foreground">
              风险案例检出
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">合规评分</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.riskAnalysis.complianceScore || 0}</div>
            <p className="text-xs text-muted-foreground">
              系统健康度 {metrics?.supervisionMetrics.systemHealthScore || 0}%
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃告警</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.riskAnalysis.criticalAlerts.filter(a => !a.acknowledged).length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              需要处理的告警
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 告警和待处理事项 - 高优先级内容 */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* 活跃告警卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              活跃告警
            </CardTitle>
            <CardDescription>需要立即关注和处理的告警</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {metrics?.riskAnalysis.criticalAlerts.slice(0, 3).map(alert => (
                <div key={alert.id} className="flex items-start justify-between p-3 border rounded-lg">
                  <div className="flex items-start space-x-3">
                    <div className={`p-1 rounded-full ${
                      alert.type === 'CRITICAL' ? 'bg-red-50' :
                      alert.type === 'WARNING' ? 'bg-yellow-50' : 'bg-blue-50'
                    }`}>
                      <AlertTriangle className={`h-3 w-3 ${
                        alert.type === 'CRITICAL' ? 'text-red-600' :
                        alert.type === 'WARNING' ? 'text-yellow-600' : 'text-blue-600'
                      }`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{alert.title}</p>
                      <p className="text-xs text-muted-foreground mt-1">{alert.description}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {alert.timestamp.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={
                      alert.type === 'CRITICAL' ? 'destructive' :
                      alert.type === 'WARNING' ? 'secondary' : 'default'
                    } className="text-xs">
                      {alert.type}
                    </Badge>
                    {!alert.acknowledged && (
                      <Button size="sm" variant="outline" className="text-xs">
                        处理
                      </Button>
                    )}
                  </div>
                </div>
              ))}

              {(!metrics?.riskAnalysis.criticalAlerts.length) && (
                <div className="text-center py-6 text-muted-foreground">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p>当前没有活跃告警</p>
                </div>
              )}

              {(metrics?.riskAnalysis.criticalAlerts.length || 0) > 3 && (
                <Button
                  variant="ghost"
                  className="w-full"
                  onClick={() => router.push('/analytics/monitoring')}
                >
                  查看全部告警 ({metrics?.riskAnalysis.criticalAlerts.length})
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 规则执行状态卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-500" />
              规则执行状态
            </CardTitle>
            <CardDescription>实时监控规则执行情况</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 执行统计 */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {metrics?.ruleExecutionStatus.runningExecutions.length || 0}
                  </div>
                  <div className="text-xs text-muted-foreground">执行中</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-600">
                    {metrics?.ruleExecutionStatus.queuedExecutions || 0}
                  </div>
                  <div className="text-xs text-muted-foreground">排队中</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">
                    {metrics?.ruleExecutionStatus.failedExecutions.length || 0}
                  </div>
                  <div className="text-xs text-muted-foreground">失败</div>
                </div>
              </div>

              {/* 正在执行的规则 */}
              <div className="space-y-2">
                {metrics?.ruleExecutionStatus.runningExecutions.slice(0, 2).map(execution => (
                  <div key={execution.id} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-blue-600 animate-spin" />
                      <div>
                        <p className="text-sm font-medium">{execution.ruleName}</p>
                        <p className="text-xs text-muted-foreground">
                          开始: {execution.startTime.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                    {execution.progress && (
                      <div className="text-right">
                        <div className="text-sm font-medium">{execution.progress}%</div>
                        <Progress value={execution.progress} className="w-16 h-1" />
                      </div>
                    )}
                  </div>
                ))}

                {(!metrics?.ruleExecutionStatus.runningExecutions.length) && (
                  <div className="text-center py-4 text-muted-foreground">
                    <Activity className="h-6 w-6 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">当前没有正在执行的规则</p>
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                className="w-full"
                onClick={() => router.push('/supervision-rules')}
              >
                查看规则管理
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 业务影响概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-green-500" />
            业务影响概览
          </CardTitle>
          <CardDescription>监管系统对业务的实际影响和价值</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {metrics && formatCurrency(metrics.businessImpact.fundsSaved)}
              </div>
              <div className="text-sm text-muted-foreground">节省资金</div>
              <div className="text-xs text-muted-foreground mt-1">本月累计</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {metrics && formatNumber(metrics.businessImpact.casesReviewed)}
              </div>
              <div className="text-sm text-muted-foreground">审核案例</div>
              <div className="text-xs text-muted-foreground mt-1">本月处理</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {metrics && metrics.businessImpact.complianceRate}%
              </div>
              <div className="text-sm text-muted-foreground">合规率</div>
              <div className="text-xs text-muted-foreground mt-1">系统平均</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {metrics && metrics.businessImpact.processingEfficiency}%
              </div>
              <div className="text-sm text-muted-foreground">处理效率</div>
              <div className="text-xs text-muted-foreground mt-1">较上月提升</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 快速操作和详情卡片区域 */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* 快速操作卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-500" />
              快速操作
            </CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push('/supervision-rules')}
            >
              <Shield className="mr-2 h-4 w-4" />
              监管规则管理
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push('/medical-cases')}
            >
              <FileText className="mr-2 h-4 w-4" />
              医疗案例审核
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push('/analytics/reports')}
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              数据分析报告
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push('/analytics/monitoring')}
            >
              <Activity className="mr-2 h-4 w-4" />
              实时监控
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push('/users')}
            >
              <Users className="mr-2 h-4 w-4" />
              用户管理
            </Button>
          </CardContent>
        </Card>

        {/* 异常检测卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              异常检测
            </CardTitle>
            <CardDescription>AI驱动的异常模式识别</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics?.riskAnalysis.anomalyDetections.slice(0, 3).map(anomaly => (
                <div key={anomaly.id} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{anomaly.type}</p>
                      <p className="text-xs text-muted-foreground mt-1">{anomaly.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={
                          anomaly.severity === 'HIGH' ? 'destructive' :
                          anomaly.severity === 'MEDIUM' ? 'secondary' : 'outline'
                        } className="text-xs">
                          {anomaly.severity}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          置信度: {(anomaly.confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {(!metrics?.riskAnalysis.anomalyDetections.length) && (
                <div className="text-center py-6 text-muted-foreground">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">未检测到异常模式</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 系统健康卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              系统健康
            </CardTitle>
            <CardDescription>系统运行状态和性能指标</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">系统健康度</span>
                  <span className="text-sm text-muted-foreground">
                    {metrics?.supervisionMetrics.systemHealthScore || 0}%
                  </span>
                </div>
                <Progress
                  value={metrics?.supervisionMetrics.systemHealthScore || 0}
                  className="h-2"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">平均响应时间</span>
                  <span className="text-sm text-muted-foreground">
                    {metrics?.supervisionMetrics.avgExecutionTime || 0}ms
                  </span>
                </div>
                <Progress
                  value={Math.min((metrics?.supervisionMetrics.avgExecutionTime || 0) / 10, 100)}
                  className="h-2"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">欺诈检测率</span>
                  <span className="text-sm text-muted-foreground">
                    {metrics?.supervisionMetrics.fraudDetectionRate || 0}%
                  </span>
                </div>
                <Progress
                  value={metrics?.supervisionMetrics.fraudDetectionRate || 0}
                  className="h-2"
                />
              </div>

              <div className="pt-2">
                <Button
                  variant="ghost"
                  className="w-full"
                  onClick={() => router.push('/analytics/monitoring')}
                >
                  查看详细监控
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default EnhancedDashboard
