'use client'

import React from 'react'
import { Shield, Loader2 } from 'lucide-react'
import { Progress } from '@/components/ui/progress'

export function LoadingLayout() {
  const [progress, setProgress] = React.useState(0)

  React.useEffect(() => {
    const timer = setTimeout(() => setProgress(66), 500)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="max-w-md w-full mx-auto px-6">
        <div className="text-center space-y-8">
          {/* Logo 和标题 */}
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center shadow-lg animate-pulse">
              <Shield className="w-8 h-8 text-primary-foreground" />
            </div>
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight text-foreground">
                医保基金监管平台
              </h1>
              <p className="text-muted-foreground">
                正在加载系统...
              </p>
            </div>
          </div>

          {/* 加载进度 */}
          <div className="space-y-4">
            <Progress value={progress} className="w-full" />
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>初始化系统组件</span>
            </div>
          </div>

          {/* 加载提示 */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p>首次加载可能需要几秒钟时间</p>
            <p>请耐心等待...</p>
          </div>
        </div>
      </div>
    </div>
  )
}
