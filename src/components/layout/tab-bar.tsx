'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Icon } from '@/components/ui/icon'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useTabs, TabState } from '@/contexts/tab-context'

/**
 * 标签页栏组件
 * 
 * 提供多标签页功能，支持：
 * - 标签页切换
 * - 标签页关闭
 * - 右键菜单操作
 * - 拖拽排序（未来实现）
 * - 滚动支持
 */
export function TabBar() {
  const {
    tabs,
    activeTabId,
    activateTab,
    closeTab,
    closeOtherTabs,
    closeAllTabs,
    closeTabsToRight,
    refreshTab
  } = useTabs()

  const [contextMenuTab, setContextMenuTab] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)

  // 避免 hydration 错误
  useEffect(() => {
    setMounted(true)
  }, [])

  // 在客户端挂载前不渲染，避免 hydration 错误
  if (!mounted) {
    return null
  }

  // 如果没有标签页，不显示标签栏
  if (tabs.length === 0) {
    return null
  }
  
  // 处理标签页点击
  const handleTabClick = (tabId: string) => {
    activateTab(tabId)
  }
  
  // 处理标签页关闭
  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation()
    closeTab(tabId)
  }
  
  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, tabId: string) => {
    e.preventDefault()
    setContextMenuTab(tabId)
  }
  
  // 渲染单个标签页
  const renderTab = (tab: TabState) => {
    const isActive = tab.id === activeTabId
    
    return (
      <div
        key={tab.id}
        className={cn(
          "group relative flex items-center gap-2 px-2 py-1 text-xs font-medium border-r border-border cursor-pointer transition-colors min-w-0 max-w-40",
          isActive
            ? "bg-background text-foreground border-b-2 border-b-primary"
            : "bg-transparent text-muted-foreground hover:bg-muted/30 hover:text-foreground"
        )}
        onClick={() => handleTabClick(tab.id)}
        onContextMenu={(e) => handleContextMenu(e, tab.id)}
        title={tab.title}
      >
        {/* 图标 */}
        {tab.icon && (
          <Icon name={tab.icon as any} className="h-3 w-3 shrink-0" />
        )}

        {/* 标题 */}
        <span className="truncate flex-1 text-xs">
          {tab.title}
          {tab.isDirty && <span className="ml-1 text-orange-500">•</span>}
        </span>

        {/* 关闭按钮 */}
        {tab.closable !== false && (
          <button
            className={cn(
              "ml-1 size-3 rounded-full hover:bg-accent flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",
              isActive && "opacity-100"
            )}
            onClick={(e) => handleTabClose(e, tab.id)}
            title="关闭标签页"
          >
            <Icon name="x" className="h-2 w-2" />
          </button>
        )}
      </div>
    )
  }
  
  return (
    <div className="flex items-center min-w-0 flex-1">
      {/* 标签页容器 */}
      <div className="flex overflow-x-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border hover:scrollbar-thumb-muted-foreground min-w-0 flex-1">
        {tabs.map(renderTab)}
      </div>

      {/* 标签页操作菜单 */}
      <div className="flex items-center pl-2 shrink-0">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Icon name="chevrons-up-down" className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => closeOtherTabs(activeTabId!)}>
              <Icon name="x" className="mr-2 h-4 w-4" />
              关闭其他标签页
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => closeTabsToRight(activeTabId!)}>
              <Icon name="arrow-right" className="mr-2 h-4 w-4" />
              关闭右侧标签页
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => refreshTab(activeTabId!)}>
              <Icon name="refresh" className="mr-2 h-4 w-4" />
              刷新当前标签页
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={closeAllTabs} className="text-destructive">
              <Icon name="x" className="mr-2 h-4 w-4" />
              关闭所有标签页
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* 右键菜单 */}
      {contextMenuTab && (
        <DropdownMenu open={!!contextMenuTab} onOpenChange={() => setContextMenuTab(null)}>
          <DropdownMenuTrigger asChild>
            <div className="hidden" />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => {
              refreshTab(contextMenuTab)
              setContextMenuTab(null)
            }}>
              <Icon name="refresh" className="mr-2 h-4 w-4" />
              刷新
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => {
              closeOtherTabs(contextMenuTab)
              setContextMenuTab(null)
            }}>
              <Icon name="x" className="mr-2 h-4 w-4" />
              关闭其他
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => {
              closeTabsToRight(contextMenuTab)
              setContextMenuTab(null)
            }}>
              <Icon name="arrow-right" className="mr-2 h-4 w-4" />
              关闭右侧
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => {
                closeTab(contextMenuTab)
                setContextMenuTab(null)
              }}
              className="text-destructive"
              disabled={tabs.find(t => t.id === contextMenuTab)?.closable === false}
            >
              <Icon name="x" className="mr-2 h-4 w-4" />
              关闭
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}
