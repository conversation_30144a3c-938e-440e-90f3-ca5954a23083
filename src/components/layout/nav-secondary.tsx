'use client'

import * as React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  HelpCircle,
  ChevronUp,
  ChevronDown,
} from 'lucide-react'

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'

// 次要功能导航数据
const data = {
  navSecondary: [
    {
      title: '帮助中心',
      url: '/help',
    },
    {
      title: 'API 文档',
      url: '/api-docs',
      external: true,
    },
    {
      title: '系统状态',
      url: '/status',
    },
    {
      title: '反馈建议',
      url: '/feedback',
    },
  ],
}

export function NavSecondary() {
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = React.useState(false)

  return (
    <SidebarGroup className="mt-auto">
      <SidebarGroupContent>
        <SidebarMenu>
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton size="sm" className="w-full">
                  <HelpCircle className="size-4" />
                  <span className="group-data-[state=collapsed]:hidden">帮助与支持</span>
                  {isExpanded ? (
                    <ChevronUp className="ml-auto size-4 group-data-[state=collapsed]:hidden" />
                  ) : (
                    <ChevronDown className="ml-auto size-4 group-data-[state=collapsed]:hidden" />
                  )}
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenu className="ml-4 mt-1">
                  {data.navSecondary.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        size="sm"
                        isActive={pathname === item.url}
                        className="h-8"
                      >
                        <Link
                          href={item.url}
                          target={item.external ? '_blank' : undefined}
                          rel={item.external ? 'noopener noreferrer' : undefined}
                        >
                          <span className="text-xs">{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
