'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ReactNode
}

// 路径到面包屑的映射
const pathToBreadcrumbs = (pathname: string): BreadcrumbItem[] => {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = [
    { label: '首页', href: '/dashboard', icon: <Home className="w-4 h-4" /> }
  ]

  // 路径映射配置
  const pathMap: Record<string, string> = {
    'dashboard': '仪表板',
    'medical-cases': '医疗病例',
    'supervision-rules': '监管规则',
    'knowledge-base': '知识库',
    'analytics': '数据统计',
    'settings': '系统设置',
    'users': '用户管理',
    'roles': '角色管理',
    'permissions': '权限管理',
    'new': '新增',
    'edit': '编辑',
    'detail': '详情',
  }

  let currentPath = ''
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const label = pathMap[segment] || segment
    
    // 最后一个段不添加链接
    const isLast = index === segments.length - 1
    breadcrumbs.push({
      label,
      href: isLast ? undefined : currentPath,
    })
  })

  return breadcrumbs
}

export function Breadcrumb() {
  const pathname = usePathname()
  const breadcrumbs = pathToBreadcrumbs(pathname)

  if (breadcrumbs.length <= 1) {
    return null
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="w-4 h-4 shrink-0" />
          )}
          {item.href ? (
            <Link
              href={item.href}
              className={cn(
                "flex items-center gap-1 hover:text-foreground transition-colors",
                "truncate max-w-[120px]"
              )}
            >
              {item.icon}
              <span>{item.label}</span>
            </Link>
          ) : (
            <span className={cn(
              "flex items-center gap-1 text-foreground font-medium",
              "truncate max-w-[120px]"
            )}>
              {item.icon}
              <span>{item.label}</span>
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}
