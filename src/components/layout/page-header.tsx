'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface PageHeaderProps {
  title?: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export function PageHeader({ 
  title, 
  description, 
  children, 
  className 
}: PageHeaderProps) {
  if (!title && !description && !children) {
    return null
  }

  return (
    <div className={cn("space-y-1", className)}>
      {title && (
        <h1 className="text-2xl font-semibold tracking-tight text-foreground">
          {title}
        </h1>
      )}
      {description && (
        <p className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      {children}
    </div>
  )
}
