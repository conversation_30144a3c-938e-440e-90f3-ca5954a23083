'use client'

import * as React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRight } from 'lucide-react'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarMenuBadge,
} from '@/components/ui/sidebar'
import { Icon, type IconName } from '@/components/ui/icon'
import { type NavigationItem } from '@/config/navigation'

interface NavMainProps {
  items: NavigationItem[]
}

// 获取图标名称的辅助函数 - 支持 Lucide 图标组件
function getIconName(icon: any): IconName {
  // 处理 Lucide 图标组件（React.forwardRef 对象）
  if (icon && typeof icon === 'object' && icon.displayName) {
    const iconMap: Record<string, IconName> = {
      // 主要功能模块图标 - 使用实际的 displayName
      'LayoutDashboard': 'dashboard',
      'FileText': 'cases',
      'Shield': 'rules',
      'BookOpen': 'knowledge',
      'ChartColumn': 'analytics', // BarChart3 的实际 displayName 是 ChartColumn
      'Settings': 'settings',

      // 子功能图标
      'Users': 'users',
      'TrendingUp': 'trends',
      'Briefcase': 'business',
      'Cog': 'config',

      // 新增语义化图标
      'FileCheck': 'file-check',
      'Play': 'play',
      'Eye': 'eye',
      'FileBarChart': 'file-bar-chart',
      'LineChart': 'line-chart',
      'Download': 'download'
    }

    // 使用 displayName 匹配图标
    const iconName = icon.displayName
    return iconMap[iconName] || 'dashboard'
  }

  // 处理字符串类型
  if (typeof icon === 'string') {
    const iconMap: Record<string, IconName> = {
      'LayoutDashboard': 'dashboard',
      'FileText': 'cases',
      'Shield': 'rules',
      'BookOpen': 'knowledge',
      'ChartColumn': 'analytics', // BarChart3 的实际 displayName
      'Settings': 'settings',
      'Users': 'users',
      'TrendingUp': 'trends',
      'Briefcase': 'business',
      'Cog': 'config',
      'FileCheck': 'file-check',
      'Play': 'play',
      'Eye': 'eye',
      'FileBarChart': 'file-bar-chart',
      'LineChart': 'line-chart',
      'Download': 'download'
    }
    return iconMap[icon] || 'dashboard'
  }

  // 处理有 name 属性的对象
  if (icon?.name) {
    const iconMap: Record<string, IconName> = {
      'LayoutDashboard': 'dashboard',
      'FileText': 'cases',
      'Shield': 'rules',
      'BookOpen': 'knowledge',
      'ChartColumn': 'analytics', // BarChart3 的实际 displayName
      'Settings': 'settings',
      'Users': 'users',
      'TrendingUp': 'trends',
      'Briefcase': 'business',
      'Cog': 'config',
      'FileCheck': 'file-check',
      'Play': 'play',
      'Eye': 'eye',
      'FileBarChart': 'file-bar-chart',
      'LineChart': 'line-chart',
      'Download': 'download'
    }
    return iconMap[icon.name] || 'dashboard'
  }

  return 'dashboard'
}

export function NavMain({ items }: NavMainProps) {
  const pathname = usePathname()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>平台功能</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = item.children 
            ? item.children.some(child => pathname.startsWith(child.href))
            : pathname === item.href || pathname.startsWith(item.href + '/')

          // 如果有子菜单，使用可折叠菜单
          if (item.children && item.children.length > 0) {
            return (
              <Collapsible
                key={item.href}
                asChild
                defaultOpen={isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={item.title}>
                      {item.icon && <Icon name={getIconName(item.icon)} className="size-4" />}
                      <span>{item.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  {item.badge && (
                    <SidebarMenuBadge>{item.badge}</SidebarMenuBadge>
                  )}
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.children.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.href}>
                          <SidebarMenuSubButton asChild isActive={pathname === subItem.href}>
                            <Link href={subItem.href}>
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                          {subItem.badge && (
                            <SidebarMenuBadge>{subItem.badge}</SidebarMenuBadge>
                          )}
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            )
          }

          // 单页面菜单项（仪表盘、病例管理、知识库）
          return (
            <SidebarMenuItem key={item.href}>
              <SidebarMenuButton asChild isActive={isActive} tooltip={item.title}>
                <Link href={item.href}>
                  {item.icon && <Icon name={getIconName(item.icon)} className="size-4" />}
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
              {item.badge && (
                <SidebarMenuBadge>{item.badge}</SidebarMenuBadge>
              )}
              {item.isNew && (
                <SidebarMenuBadge className="bg-green-500 text-white">新</SidebarMenuBadge>
              )}
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
