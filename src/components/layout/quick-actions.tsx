'use client'

import React from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { Plus, Upload, Download, Filter, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface QuickAction {
  label: string
  icon: React.ComponentType<any>
  action: string
  primary?: boolean
}

// 根据路径获取快速操作配置
const getQuickActions = (pathname: string): QuickAction[] => {
  const actions = {
    '/dashboard': [
      { label: '刷新数据', icon: RefreshCw, action: 'refresh' },
      { label: '导出报表', icon: Download, action: 'export' },
    ],
    '/medical-cases': [
      { label: '新增病例', icon: Plus, action: 'create', primary: true },
      { label: '批量导入', icon: Upload, action: 'import' },
      { label: '导出数据', icon: Download, action: 'export' },
      { label: '高级筛选', icon: Filter, action: 'filter' },
    ],
    '/supervision-rules': [
      { label: '新增规则', icon: Plus, action: 'create', primary: true },
      { label: '导入模板', icon: Upload, action: 'import' },
      { label: '执行规则', icon: RefreshCw, action: 'execute' },
    ],
    '/knowledge-base': [
      { label: '新增文档', icon: Plus, action: 'create', primary: true },
      { label: '批量上传', icon: Upload, action: 'upload' },
    ],
  }

  // 匹配最具体的路径
  const matchedPath = Object.keys(actions)
    .sort((a, b) => b.length - a.length)
    .find(path => pathname.startsWith(path))

  return matchedPath ? actions[matchedPath as keyof typeof actions] : []
}

export function QuickActions() {
  const pathname = usePathname()
  const router = useRouter()
  const actions = getQuickActions(pathname)

  if (actions.length === 0) {
    return null
  }

  const handleAction = (action: string) => {
    switch (action) {
      case 'create':
        router.push(`${pathname}/new`)
        break
      case 'refresh':
        window.location.reload()
        break
      case 'import':
        // 触发导入对话框
        console.log('Import action')
        break
      case 'export':
        // 触发导出功能
        console.log('Export action')
        break
      case 'filter':
        // 触发筛选面板
        console.log('Filter action')
        break
      case 'execute':
        // 触发规则执行
        console.log('Execute action')
        break
      case 'upload':
        // 触发文件上传
        console.log('Upload action')
        break
      default:
        console.log('Unknown action:', action)
    }
  }

  // 如果只有一个主要操作，直接显示按钮
  const primaryAction = actions.find(action => action.primary)
  const secondaryActions = actions.filter(action => !action.primary)

  return (
    <div className="flex items-center gap-2">
      {/* 主要操作按钮 */}
      {primaryAction && (
        <Button
          onClick={() => handleAction(primaryAction.action)}
          className="gap-2"
        >
          <primaryAction.icon className="w-4 h-4" />
          {primaryAction.label}
        </Button>
      )}

      {/* 次要操作下拉菜单 */}
      {secondaryActions.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              更多操作
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {secondaryActions.map((action, index) => (
              <React.Fragment key={action.action}>
                {index > 0 && index % 2 === 0 && <DropdownMenuSeparator />}
                <DropdownMenuItem
                  onClick={() => handleAction(action.action)}
                  className="gap-2"
                >
                  <action.icon className="w-4 h-4" />
                  {action.label}
                </DropdownMenuItem>
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}
