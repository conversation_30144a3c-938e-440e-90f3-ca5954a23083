'use client'

import * as React from 'react'
import Link from 'next/link'
import { Shield } from 'lucide-react'
import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar'
import { NavMain } from '@/components/layout/nav-main'
import { NavSecondary } from '@/components/layout/nav-secondary'
import { NavUser } from '@/components/layout/nav-user'
import { type NavigationItem } from '@/config/navigation'
import { type User } from '@/types/auth'

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  navigation: NavigationItem[]
  user?: User | null
  onCollapsedChange?: (collapsed: boolean) => void
}

export function AppSidebar({ navigation, user, onCollapsedChange, ...props }: AppSidebarProps) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/dashboard" className="group-data-[state=collapsed]:justify-center">
                <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
                  <Shield className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight group-data-[state=collapsed]:hidden">
                  <span className="truncate font-semibold text-sm">医保基金监管平台</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navigation} />
        <NavSecondary />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export default AppSidebar
