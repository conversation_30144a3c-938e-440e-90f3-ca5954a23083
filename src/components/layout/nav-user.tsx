'use client'

import * as React from 'react'
import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
} from 'lucide-react'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { type User } from '@/types/auth'
import { useAuth } from '@/contexts/auth-context'

interface NavUserProps {
  user?: User | null
}

export function NavUser({ user }: NavUserProps) {
  const { logout } = useAuth()

  const handleLogout = () => {
    // logout 函数已经包含了跳转逻辑，直接调用即可
    logout()
  }



  // 默认用户数据（如果没有传入用户信息）
  const userData = user || {
    realName: '系统管理员',
    email: '<EMAIL>',
    username: 'admin',
    roles: [{ roleCode: 'ADMIN', roleName: '系统管理员' }]
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-full">
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/initials/svg?seed=${userData.realName}`}
                  alt={userData.realName || '用户'}
                />
                <AvatarFallback className="rounded-full">
                  {userData.realName?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{userData.realName || '未知用户'}</span>
                <span className="truncate text-xs">{userData.email || userData.username || ''}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side="top"
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-full">
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/initials/svg?seed=${userData.realName}`}
                    alt={userData.realName || '用户'}
                  />
                  <AvatarFallback className="rounded-full">
                    {userData.realName?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{userData.realName || '未知用户'}</span>
                  <span className="truncate text-xs">{userData.email || userData.username || ''}</span>
                </div>
              </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <BadgeCheck className="size-4" />
                个人资料
              </DropdownMenuItem>
              <DropdownMenuItem>
                <CreditCard className="size-4" />
                账户设置
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bell className="size-4" />
                通知设置
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="size-4" />
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
