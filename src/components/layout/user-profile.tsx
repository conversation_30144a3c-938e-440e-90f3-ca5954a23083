'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { 
  User, 
  Settings, 
  LogOut, 
  Shield, 
  Bell, 
  HelpCircle,
  ChevronDown 
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'

interface UserProfileProps {
  user: any // 从 auth context 传入的用户信息
}

export function UserProfile({ user }: UserProfileProps) {
  const router = useRouter()
  const { logout } = useAuth()

  const handleLogout = async () => {
    try {
      await logout()
      router.push('/auth/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleProfileClick = () => {
    router.push('/settings/profile')
  }

  const handleSettingsClick = () => {
    router.push('/settings')
  }

  // 获取用户头像的首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // 获取角色显示名称
  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      'admin': '系统管理员',
      'operator': '业务操作员',
      'auditor': '审计员',
      'viewer': '查看者',
    }
    return roleMap[role] || role
  }

  // 获取角色颜色
  const getRoleColor = (role: string) => {
    const colorMap: Record<string, string> = {
      'admin': 'destructive',
      'operator': 'default',
      'auditor': 'secondary',
      'viewer': 'outline',
    }
    return colorMap[role] || 'default'
  }

  if (!user) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-9 w-auto px-2 gap-2">
          <Avatar className="h-7 w-7">
            <AvatarImage src={user.avatar} alt={user.realName || user.username} />
            <AvatarFallback className="text-xs">
              {getInitials(user.realName || user.username)}
            </AvatarFallback>
          </Avatar>
          <div className="hidden md:flex flex-col items-start text-left">
            <span className="text-sm font-medium leading-none">
              {user.realName || user.username}
            </span>
            <span className="text-xs text-muted-foreground">
              {user.email}
            </span>
          </div>
          <ChevronDown className="h-3 w-3 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end" forceMount>
        {/* 用户信息头部 */}
        <DropdownMenuLabel className="p-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user.avatar} alt={user.realName || user.username} />
              <AvatarFallback>
                {getInitials(user.realName || user.username)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium leading-none">
                {user.realName || user.username}
              </p>
              <p className="text-xs text-muted-foreground mt-1 truncate">
                {user.email}
              </p>
              {user.role && (
                <Badge 
                  variant={getRoleColor(user.role) as any} 
                  className="mt-2 text-xs"
                >
                  {getRoleDisplayName(user.role)}
                </Badge>
              )}
            </div>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        {/* 菜单项 */}
        <DropdownMenuItem onClick={handleProfileClick} className="gap-2 cursor-pointer">
          <User className="h-4 w-4" />
          个人资料
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleSettingsClick} className="gap-2 cursor-pointer">
          <Settings className="h-4 w-4" />
          系统设置
        </DropdownMenuItem>
        
        <DropdownMenuItem className="gap-2 cursor-pointer">
          <Bell className="h-4 w-4" />
          通知设置
        </DropdownMenuItem>
        
        <DropdownMenuItem className="gap-2 cursor-pointer">
          <Shield className="h-4 w-4" />
          安全设置
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem className="gap-2 cursor-pointer">
          <HelpCircle className="h-4 w-4" />
          帮助中心
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleLogout}
          className="gap-2 cursor-pointer text-red-600 focus:text-red-600"
        >
          <LogOut className="h-4 w-4" />
          退出登录
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
