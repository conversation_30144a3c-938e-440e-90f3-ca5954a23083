'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { MainLayout } from '@/components/layout/main-layout'

interface LayoutProviderProps {
  children: React.ReactNode
}

// 定义不同的布局类型
type LayoutType = 'main' | 'auth' | 'loading' | 'minimal'

// 路径到布局类型的映射
const getLayoutType = (pathname: string): LayoutType => {
  // 认证相关页面使用认证布局
  if (pathname.startsWith('/auth') || pathname.startsWith('/login')) {
    return 'auth'
  }

  // 加载页面使用加载布局
  if (pathname === '/loading') {
    return 'loading'
  }

  // 首页重定向，使用最小布局
  if (pathname === '/') {
    return 'minimal'
  }

  // 其他页面使用主布局
  return 'main'
}

export function LayoutProvider({ children }: LayoutProviderProps) {
  const pathname = usePathname()
  const { user, isLoading } = useAuth()

  // 简化的布局逻辑，避免复杂的动态导入
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  const pathLayoutType = getLayoutType(pathname)

  // 如果用户未登录且不在认证页面，重定向到登录页
  if (!user && pathLayoutType === 'main') {
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <p className="text-muted-foreground">重定向中...</p>
      </div>
    )
  }

  // 如果用户已登录且在认证页面，重定向到仪表板
  if (user && pathLayoutType === 'auth') {
    if (typeof window !== 'undefined') {
      window.location.href = '/dashboard'
    }
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <p className="text-muted-foreground">重定向中...</p>
      </div>
    )
  }

  // 根据布局类型渲染不同的布局
  if (pathLayoutType === 'auth') {
    // 认证页面使用简单布局
    return (
      <div className="min-h-screen bg-background">
        {children}
      </div>
    )
  }

  if (pathLayoutType === 'minimal') {
    // 最小布局
    return (
      <div className="min-h-screen bg-background">
        {children}
      </div>
    )
  }

  // 主布局
  return (
    <MainLayout>
      {children}
    </MainLayout>
  )
}
