'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { SimpleMainLayout } from './simple-main-layout'
import { SimpleAuthLayout } from './simple-auth-layout'

interface LayoutProviderProps {
  children: React.ReactNode
}

// 路径到布局类型的映射
const getLayoutType = (pathname: string) => {
  // 认证相关页面
  if (pathname.startsWith('/auth')) {
    return 'auth'
  }

  // 首页重定向
  if (pathname === '/') {
    return 'redirect'
  }

  // 其他页面使用主布局
  return 'main'
}

export function LayoutProvider({ children }: LayoutProviderProps) {
  const pathname = usePathname()
  const { user, isLoading } = useAuth()

  // 加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  const layoutType = getLayoutType(pathname)

  // 处理重定向逻辑
  if (layoutType === 'redirect') {
    if (user) {
      // 已登录用户重定向到仪表盘
      if (typeof window !== 'undefined') {
        window.location.href = '/dashboard'
      }
    } else {
      // 未登录用户重定向到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
    }
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p className="text-gray-600">重定向中...</p>
      </div>
    )
  }

  // 认证页面
  if (layoutType === 'auth') {
    // 已登录用户访问认证页面，重定向到仪表盘
    if (user) {
      if (typeof window !== 'undefined') {
        window.location.href = '/dashboard'
      }
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <p className="text-gray-600">重定向中...</p>
        </div>
      )
    }

    return (
      <SimpleAuthLayout>
        {children}
      </SimpleAuthLayout>
    )
  }

  // 主应用页面
  if (layoutType === 'main') {
    // 未登录用户访问主应用，重定向到登录页
    if (!user) {
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <p className="text-gray-600">重定向中...</p>
        </div>
      )
    }

    return (
      <SimpleMainLayout>
        {children}
      </SimpleMainLayout>
    )
  }

  // 默认布局
  return (
    <div className="min-h-screen bg-gray-50">
      {children}
    </div>
  )
}
