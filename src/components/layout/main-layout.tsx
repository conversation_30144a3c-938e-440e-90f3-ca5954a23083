'use client'

import React, { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { FloatingOperationFeedback, OperationStatusIndicator } from '@/components/ui/operation-feedback'
import { ThemeSwitcher, ModeToggle } from '@/components/theme-switcher'
import { useAuth } from '@/contexts/auth-context'
import { PerformanceMonitor } from '@/components/ui/performance-monitor'
import { navigationConfig } from '@/config/navigation'
import { useNavigationPermissions } from '@/hooks/use-navigation-permissions'
import { PermissionMonitor } from '@/components/navigation/permission-monitor'
import { EnhancedNavigationSearch } from '@/components/navigation/enhanced-search'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/layout/app-sidebar'
import { TabProvider } from '@/contexts/tab-context'
import { TabBar } from '@/components/layout/tab-bar'
import { Breadcrumb } from '@/components/layout/breadcrumb'
import { PageHeader } from '@/components/layout/page-header'
import { QuickActions } from '@/components/layout/quick-actions'
import { NotificationCenter } from '@/components/layout/notification-center'
import { UserProfile } from '@/components/layout/user-profile'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: React.ReactNode
}

// 页面配置类型
interface PageConfig {
  title?: string
  description?: string
  showBreadcrumb?: boolean
  showQuickActions?: boolean
  headerActions?: React.ReactNode
  className?: string
}

// 根据路径获取页面配置
const getPageConfig = (pathname: string): PageConfig => {
  const configs: Record<string, PageConfig> = {
    '/dashboard': {
      title: '仪表板',
      description: '系统概览和关键指标',
      showBreadcrumb: false,
      showQuickActions: true,
    },
    '/medical-cases': {
      title: '医疗病例',
      description: '病例管理和查询',
      showBreadcrumb: true,
      showQuickActions: true,
    },
    '/supervision-rules': {
      title: '监管规则',
      description: '规则配置和执行管理',
      showBreadcrumb: true,
      showQuickActions: true,
    },
    '/knowledge-base': {
      title: '知识库',
      description: '政策文档和知识管理',
      showBreadcrumb: true,
      showQuickActions: false,
    },
    '/analytics': {
      title: '数据统计',
      description: '数据分析和报表',
      showBreadcrumb: true,
      showQuickActions: false,
    },
    '/settings': {
      title: '系统设置',
      description: '系统配置和管理',
      showBreadcrumb: true,
      showQuickActions: false,
    },
  }

  // 匹配最具体的路径
  for (const path of Object.keys(configs).sort((a, b) => b.length - a.length)) {
    if (pathname.startsWith(path)) {
      return configs[path]!
    }
  }

  return {
    showBreadcrumb: true,
    showQuickActions: false,
  }
}

export function MainLayout({ children }: MainLayoutProps) {
  const { user } = useAuth()
  const pathname = usePathname()
  const { filteredNavigation } = useNavigationPermissions(navigationConfig)
  const [pageConfig, setPageConfig] = useState<PageConfig>({})


  // 根据路径更新页面配置
  useEffect(() => {
    setPageConfig(getPageConfig(pathname))
  }, [pathname])

  return (
    <TabProvider>
      <SidebarProvider>
        <AppSidebar
          navigation={filteredNavigation}
          user={user}
        />
        <SidebarInset>
          {/* 顶部导航栏 - 重新设计为更现代的样式 */}
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-14 items-center gap-4 px-4">
              {/* 左侧：侧边栏切换 + 面包屑导航 */}
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <SidebarTrigger className="-ml-1 shrink-0" />
                {pageConfig.showBreadcrumb && (
                  <div className="hidden md:block">
                    <Breadcrumb />
                  </div>
                )}
              </div>

              {/* 中间：标签页导航 */}
              <div className="flex-1 max-w-2xl min-w-0">
                <TabBar />
              </div>

              {/* 右侧：功能区域 */}
              <div className="flex items-center gap-2 shrink-0">
                <EnhancedNavigationSearch />
                <NotificationCenter />
                <ThemeSwitcher />
                <ModeToggle />
                <UserProfile user={user} />
              </div>
            </div>
          </header>

          {/* 页面头部 - 可选的页面标题和操作区域 */}
          {(pageConfig.title || pageConfig.showQuickActions) && (
            <div className="border-b bg-muted/30">
              <div className="flex items-center justify-between px-6 py-4">
                <PageHeader
                  title={pageConfig.title}
                  description={pageConfig.description}
                />
                {pageConfig.showQuickActions && <QuickActions />}
                {pageConfig.headerActions}
              </div>
            </div>
          )}

          {/* 主内容区域 */}
          <main className={cn(
            "flex-1 overflow-auto bg-background",
            pageConfig.className
          )}>
            {/* 操作状态指示器 */}
            <OperationStatusIndicator />

            {/* 内容容器 */}
            <div className="container mx-auto max-w-7xl p-6 space-y-6">
              {children}
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>

      {/* 浮动组件 */}
      <FloatingOperationFeedback />
      <PerformanceMonitor />
      <PermissionMonitor />
    </TabProvider>
  )
}
