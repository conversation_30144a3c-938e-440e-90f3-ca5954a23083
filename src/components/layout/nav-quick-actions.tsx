'use client'

import * as React from 'react'
import Link from 'next/link'
import { Plus, Play, FileText, MessageCircle, MoreHorizontal } from 'lucide-react'

import {
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// 快速操作数据
const quickActions = [
  {
    title: '新建病例',
    url: '/cases?action=new',
    icon: Plus,
    description: '快速录入新病例',
    hotkey: 'Ctrl+N'
  },
  {
    title: '执行规则',
    url: '/supervision/execution?action=run',
    icon: Play,
    description: '立即执行监管规则',
    hotkey: 'Ctrl+R'
  },
  {
    title: '智能问答',
    url: '/knowledge?focus=qa',
    icon: MessageCircle,
    description: '咨询知识库',
    hotkey: 'Ctrl+Q'
  },
  {
    title: '生成报告',
    url: '/supervision/reports?action=generate',
    icon: FileText,
    description: '生成监管报告',
    hotkey: 'Ctrl+G'
  }
]

// 更多操作
const moreActions = [
  {
    title: '导入病例',
    url: '/cases?action=import',
    description: '批量导入病例数据'
  },
  {
    title: '数据导出',
    url: '/analytics/export',
    description: '导出统计数据'
  },
  {
    title: '系统备份',
    url: '/system/backup',
    description: '备份系统数据'
  }
]

export function NavQuickActions() {
  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>快速操作</SidebarGroupLabel>
      <SidebarGroupAction title="更多操作">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex size-4 items-center justify-center">
              <MoreHorizontal className="size-4" />
              <span className="sr-only">更多操作</span>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="right" align="start">
            {moreActions.map((action) => (
              <DropdownMenuItem key={action.title} asChild>
                <Link href={action.url}>
                  <div>
                    <div className="font-medium">{action.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {action.description}
                    </div>
                  </div>
                </Link>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <div>
                <div className="font-medium">自定义操作</div>
                <div className="text-xs text-muted-foreground">
                  配置常用操作
                </div>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarGroupAction>
      <SidebarGroupContent>
        <SidebarMenu>
          {quickActions.map((action) => (
            <SidebarMenuItem key={action.title}>
              <SidebarMenuButton asChild tooltip={`${action.description} (${action.hotkey})`}>
                <Link href={action.url}>
                  <action.icon className="size-4" />
                  <span>{action.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
