'use client'

import React from 'react'
import Image from 'next/image'
import { Shield, Sparkles } from 'lucide-react'
import { ThemeSwitcher, ModeToggle } from '@/components/theme-switcher'
import { cn } from '@/lib/utils'

interface AuthLayoutProps {
  children: React.ReactNode
}

export function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen grid lg:grid-cols-2">
      {/* 左侧：品牌展示区域 */}
      <div className="relative hidden lg:flex lg:flex-col lg:justify-center lg:items-center bg-gradient-to-br from-primary/10 via-primary/5 to-background p-8">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-grid-white/10 bg-grid-16 [mask-image:radial-gradient(white,transparent_70%)]" />
        
        {/* 主要内容 */}
        <div className="relative z-10 max-w-md text-center space-y-6">
          {/* Logo 和标题 */}
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center shadow-lg">
              <Shield className="w-8 h-8 text-primary-foreground" />
            </div>
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight text-foreground">
                医保基金监管平台
              </h1>
              <p className="text-lg text-muted-foreground">
                智能化医疗保险基金监督管理系统
              </p>
            </div>
          </div>

          {/* 特性展示 */}
          <div className="space-y-4 text-left">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-foreground">智能监管</h3>
                <p className="text-sm text-muted-foreground">
                  基于AI的智能规则引擎，实时监控医保基金使用情况
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-foreground">数据分析</h3>
                <p className="text-sm text-muted-foreground">
                  多维度数据分析，为决策提供科学依据
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-foreground">安全可靠</h3>
                <p className="text-sm text-muted-foreground">
                  企业级安全保障，确保数据安全和系统稳定
                </p>
              </div>
            </div>
          </div>

          {/* 装饰元素 */}
          <div className="flex justify-center pt-8">
            <div className="flex items-center gap-2 text-primary">
              <Sparkles className="w-4 h-4" />
              <span className="text-sm font-medium">现代化 · 智能化 · 专业化</span>
              <Sparkles className="w-4 h-4" />
            </div>
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="absolute bottom-8 left-8 right-8 text-center">
          <p className="text-xs text-muted-foreground">
            © 2024 医保基金监管平台. 保留所有权利.
          </p>
        </div>
      </div>

      {/* 右侧：认证表单区域 */}
      <div className="flex flex-col">
        {/* 顶部工具栏 */}
        <div className="flex justify-between items-center p-6 border-b">
          {/* 移动端Logo */}
          <div className="flex items-center gap-2 lg:hidden">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Shield className="w-4 h-4 text-primary-foreground" />
            </div>
            <span className="font-semibold text-foreground">医保监管平台</span>
          </div>
          
          {/* 主题切换 */}
          <div className="flex items-center gap-2">
            <ThemeSwitcher />
            <ModeToggle />
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="w-full max-w-sm space-y-6">
            {/* 移动端标题 */}
            <div className="text-center space-y-2 lg:hidden">
              <h1 className="text-2xl font-bold tracking-tight">
                欢迎回来
              </h1>
              <p className="text-muted-foreground">
                请登录您的账户以继续
              </p>
            </div>

            {/* 认证表单 */}
            {children}
          </div>
        </div>

        {/* 底部帮助信息 */}
        <div className="p-6 border-t bg-muted/30">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              遇到问题？请联系系统管理员
            </p>
            <div className="flex justify-center gap-4 text-xs text-muted-foreground">
              <span>技术支持: <EMAIL></span>
              <span>•</span>
              <span>服务热线: 400-123-4567</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
