'use client'

import { useEffect } from 'react'
import { webVitalsMonitor } from '@/lib/performance/web-vitals-monitor'

/**
 * Web Vitals 跟踪组件
 * 在应用程序中自动初始化性能监控
 */
export function WebVitalsTracker() {
  useEffect(() => {
    // Web Vitals监控已在模块加载时自动初始化
    // 这里可以添加额外的配置或事件监听
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Web Vitals tracking initialized')
    }
  }, [])

  // 这个组件不渲染任何内容
  return null
}
