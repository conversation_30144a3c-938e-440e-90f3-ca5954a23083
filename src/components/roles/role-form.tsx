"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/lib/toast'
import { Role, UserRole, Permission, PermissionGroup } from '@/types/auth'
import { Loader2, Shield } from 'lucide-react'

// 表单验证schema
const roleFormSchema = z.object({
  roleCode: z.enum(['ADMIN', 'SUPERVISOR', 'OPERATOR', 'AUDITOR', 'VIEWER'] as const),
  roleName: z.string().min(1, '请输入角色名称'),
  description: z.string().optional(),
  permissionIds: z.array(z.number()).min(1, '请至少选择一个权限'),
})

type RoleFormData = z.infer<typeof roleFormSchema>

interface RoleFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role?: Role | null
  onSuccess: () => void
}

export function RoleForm({ open, onOpenChange, role, onSuccess }: RoleFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<Set<number>>(new Set())
  const { toast } = useToast()

  const isEdit = !!role

  const form = useForm<RoleFormData>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      roleCode: 'VIEWER',
      roleName: '',
      description: '',
      permissionIds: [],
    },
  })

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) return

      const response = await fetch('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()
      if (result.success) {
        setPermissionGroups(result.data.groups)
      }
    } catch (error) {
      console.error('获取权限列表失败:', error)
    }
  }

  // 当角色数据变化时更新表单
  useEffect(() => {
    if (role) {
      const permissionIds = role.permissions.map(p => p.id)
      form.reset({
        roleCode: role.roleCode,
        roleName: role.roleName,
        description: role.description || '',
        permissionIds,
      })
      setSelectedPermissions(new Set(permissionIds))
    } else {
      form.reset({
        roleCode: 'VIEWER',
        roleName: '',
        description: '',
        permissionIds: [],
      })
      setSelectedPermissions(new Set())
    }
  }, [role, form])

  // 初始化时获取权限列表
  useEffect(() => {
    if (open) {
      fetchPermissions()
    }
  }, [open])

  // 处理权限选择
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    const newSelected = new Set(selectedPermissions)
    if (checked) {
      newSelected.add(permissionId)
    } else {
      newSelected.delete(permissionId)
    }
    setSelectedPermissions(newSelected)
    form.setValue('permissionIds', Array.from(newSelected))
  }

  // 全选/取消全选模块权限
  const handleModuleToggle = (modulePermissions: Permission[], checked: boolean) => {
    const newSelected = new Set(selectedPermissions)
    modulePermissions.forEach(permission => {
      if (checked) {
        newSelected.add(permission.id)
      } else {
        newSelected.delete(permission.id)
      }
    })
    setSelectedPermissions(newSelected)
    form.setValue('permissionIds', Array.from(newSelected))
  }

  const onSubmit = async (data: RoleFormData) => {
    try {
      setIsLoading(true)

      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const url = isEdit ? `/api/roles/${role.id}` : '/api/roles'
      const method = isEdit ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "成功",
          description: isEdit ? "角色信息更新成功" : "角色创建成功",
        })
        onSuccess()
        onOpenChange(false)
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('角色表单提交错误:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "操作失败，请稍后重试",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {isEdit ? '编辑角色' : '新增角色'}
          </DialogTitle>
          <DialogDescription>
            {isEdit ? '修改角色信息和权限配置' : '创建新的角色并配置权限'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="roleCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色代码 *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isEdit || isLoading}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择角色代码" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ADMIN">ADMIN - 系统管理员</SelectItem>
                        <SelectItem value="SUPERVISOR">SUPERVISOR - 监管主管</SelectItem>
                        <SelectItem value="OPERATOR">OPERATOR - 业务操作员</SelectItem>
                        <SelectItem value="AUDITOR">AUDITOR - 审核员</SelectItem>
                        <SelectItem value="VIEWER">VIEWER - 查看员</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="roleName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色名称 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入角色名称"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>角色描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入角色描述"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 权限配置 */}
            <FormField
              control={form.control}
              name="permissionIds"
              render={() => (
                <FormItem>
                  <FormLabel>权限配置 *</FormLabel>
                  <FormDescription>
                    选择该角色拥有的权限
                  </FormDescription>
                  <div className="space-y-4">
                    {permissionGroups.map((group) => {
                      const modulePermissions = group.permissions
                      const selectedCount = modulePermissions.filter(p => selectedPermissions.has(p.id)).length
                      const isAllSelected = selectedCount === modulePermissions.length
                      const isPartialSelected = selectedCount > 0 && selectedCount < modulePermissions.length

                      return (
                        <Card key={group.module}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-base flex items-center gap-2">
                                <Checkbox
                                  checked={isAllSelected}
                                  onCheckedChange={(checked) => 
                                    handleModuleToggle(modulePermissions, checked as boolean)
                                  }
                                  className={isPartialSelected ? "data-[state=checked]:bg-orange-500" : ""}
                                />
                                {group.moduleName}
                              </CardTitle>
                              <Badge variant="outline">
                                {selectedCount}/{modulePermissions.length}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                              {modulePermissions.map((permission) => (
                                <div key={permission.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`permission-${permission.id}`}
                                    checked={selectedPermissions.has(permission.id)}
                                    onCheckedChange={(checked) => 
                                      handlePermissionChange(permission.id, checked as boolean)
                                    }
                                  />
                                  <label
                                    htmlFor={`permission-${permission.id}`}
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                  >
                                    {permission.name}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEdit ? '更新' : '创建'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
