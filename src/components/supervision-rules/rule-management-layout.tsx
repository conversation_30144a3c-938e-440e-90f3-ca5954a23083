/**
 * 监管规则管理布局组件
 * 实现P0-007页面功能架构重新设计目标：
 * - 拆分大型组件，遵循单一职责原则
 * - 实现合理的功能分层
 * - 提升代码可维护性
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/lib/toast'
import { SupervisionRule, RuleListParams, RuleStatistics } from '@/types/supervision-rule'
import { useListPageState } from '@/hooks/use-page-state'
import {
  Shield,
  Plus,
  Settings,
  BarChart3,
  Activity,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react'

interface RuleManagementLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  actions?: React.ReactNode
  sidebar?: React.ReactNode
  showStatistics?: boolean
  statistics?: RuleStatistics
}

/**
 * 监管规则管理主布局
 */
export function RuleManagementLayout({
  children,
  title = "监管规则",
  description = "管理和执行医保基金监管规则",
  actions,
  sidebar,
  showStatistics = true,
  statistics
}: RuleManagementLayoutProps) {
  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            {title}
          </h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>

      {/* 统计概览 */}
      {showStatistics && statistics && (
        <RuleStatisticsOverview statistics={statistics} />
      )}

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 侧边栏 */}
        {sidebar && (
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">快速操作</CardTitle>
              </CardHeader>
              <CardContent>
                {sidebar}
              </CardContent>
            </Card>
          </div>
        )}
        
        {/* 主内容 */}
        <div className={sidebar ? "lg:col-span-3" : "lg:col-span-4"}>
          {children}
        </div>
      </div>
    </div>
  )
}

/**
 * 规则统计概览组件
 */
function RuleStatisticsOverview({ statistics }: { statistics: RuleStatistics }) {
  const stats = [
    {
      title: "总规则数",
      value: statistics.totalRules,
      icon: Shield,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "活跃规则",
      value: statistics.activeRules,
      icon: Activity,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "今日执行",
      value: statistics.totalRules,
      icon: BarChart3,
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      title: "成功率",
      value: `${Math.round((statistics.activeRules / Math.max(statistics.totalRules, 1)) * 100)}%`,
      icon: Settings,
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-full ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 规则操作面板组件
 */
interface RuleActionPanelProps {
  selectedCount: number
  onBatchExecute: () => void
  onBatchToggle: () => void
  onBatchDelete: () => void
  onRefresh: () => void
  isLoading?: boolean
}

export function RuleActionPanel({
  selectedCount,
  onBatchExecute,
  onBatchToggle,
  onBatchDelete,
  onRefresh,
  isLoading = false
}: RuleActionPanelProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">批量操作</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="text-sm text-muted-foreground">
          已选择 <Badge variant="secondary">{selectedCount}</Badge> 个规则
        </div>
        
        <Separator />
        
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onBatchExecute}
            disabled={selectedCount === 0 || isLoading}
            className="w-full justify-start"
          >
            <Activity className="h-4 w-4 mr-2" />
            批量执行
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onBatchToggle}
            disabled={selectedCount === 0 || isLoading}
            className="w-full justify-start"
          >
            <Settings className="h-4 w-4 mr-2" />
            批量启用/禁用
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onBatchDelete}
            disabled={selectedCount === 0 || isLoading}
            className="w-full justify-start text-red-600 hover:text-red-700"
          >
            <Shield className="h-4 w-4 mr-2" />
            批量删除
          </Button>
        </div>
        
        <Separator />
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          disabled={isLoading}
          className="w-full justify-start"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          刷新数据
        </Button>
      </CardContent>
    </Card>
  )
}

/**
 * 规则快速筛选面板
 */
interface RuleQuickFilterProps {
  onFilterChange: (filter: string, value: any) => void
  activeFilters: Record<string, any>
}

export function RuleQuickFilter({ onFilterChange, activeFilters }: RuleQuickFilterProps) {
  const quickFilters = [
    { key: 'isActive', label: '活跃规则', value: true },
    { key: 'isActive', label: '禁用规则', value: false },
    { key: 'ruleType', label: 'SQL规则', value: 'SQL' },
    { key: 'ruleType', label: 'DSL规则', value: 'DSL' },
    { key: 'priority', label: '高优先级', value: 'HIGH' },
    { key: 'priority', label: '中优先级', value: 'MEDIUM' },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">快速筛选</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {quickFilters.map((filter, index) => (
          <Button
            key={index}
            variant={activeFilters[filter.key] === filter.value ? "default" : "ghost"}
            size="sm"
            onClick={() => onFilterChange(filter.key, filter.value)}
            className="w-full justify-start"
          >
            <Filter className="h-4 w-4 mr-2" />
            {filter.label}
          </Button>
        ))}
        
        <Separator />
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            quickFilters.forEach(filter => {
              onFilterChange(filter.key, undefined)
            })
          }}
          className="w-full justify-start text-muted-foreground"
        >
          清除筛选
        </Button>
      </CardContent>
    </Card>
  )
}

/**
 * 规则创建快捷入口
 */
interface RuleCreateShortcutProps {
  onCreateRule: (type: string) => void
}

export function RuleCreateShortcut({ onCreateRule }: RuleCreateShortcutProps) {
  const ruleTypes = [
    { type: 'SQL', label: 'SQL规则', description: '基于SQL查询的规则' },
    { type: 'DSL', label: 'DSL规则', description: '领域特定语言规则' },
    { type: 'JAVASCRIPT', label: 'JS规则', description: 'JavaScript脚本规则' }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">创建规则</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {ruleTypes.map((ruleType, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => onCreateRule(ruleType.type)}
            className="w-full justify-start"
          >
            <Plus className="h-4 w-4 mr-2" />
            <div className="text-left">
              <div className="font-medium">{ruleType.label}</div>
              <div className="text-xs text-muted-foreground">{ruleType.description}</div>
            </div>
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}
