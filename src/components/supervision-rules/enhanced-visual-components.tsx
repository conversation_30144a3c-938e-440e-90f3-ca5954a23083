'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'
import {
  Shield,
  Zap,
  TrendingUp,
  Activity,
  CheckCircle,
  AlertTriangle,
  Clock,
  Database,
  Code,
  Sparkles,
  Target,
  BarChart3
} from 'lucide-react'

// 增强的规则卡片组件 - 采用卡片式布局
interface EnhancedRuleCardProps {
  rule: {
    id: number
    ruleName: string
    ruleCode: string
    ruleType: 'SQL' | 'DSL' | 'JAVASCRIPT'
    ruleCategory: string
    isActive: boolean
    priorityLevel: number
    executionCount: number
    lastExecutedAt?: string
    description?: string
  }
  onView: () => void
  onEdit: () => void
  onExecute: () => void
  isSelected?: boolean
  onSelect?: () => void
}

export function EnhancedRuleCard({ 
  rule, 
  onView, 
  onEdit, 
  onExecute, 
  isSelected = false,
  onSelect 
}: EnhancedRuleCardProps) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SQL': return Database
      case 'DSL': return Code
      case 'JAVASCRIPT': return Zap
      default: return Shield
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'SQL': return 'text-blue-600 bg-blue-50'
      case 'DSL': return 'text-purple-600 bg-purple-50'
      case 'JAVASCRIPT': return 'text-orange-600 bg-orange-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const TypeIcon = getTypeIcon(rule.ruleType)

  return (
    <Card className={cn(
      "group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
      "border-2 cursor-pointer",
      isSelected ? "border-primary shadow-md" : "border-border hover:border-primary/50",
      !rule.isActive && "opacity-75"
    )} onClick={onSelect}>
      {/* 选择指示器 */}
      {isSelected && (
        <div className="absolute top-2 right-2 z-10">
          <div className="w-3 h-3 bg-primary rounded-full animate-pulse" />
        </div>
      )}

      {/* 状态指示条 */}
      <div className={cn(
        "absolute top-0 left-0 right-0 h-1",
        rule.isActive ? "bg-green-500" : "bg-gray-300"
      )} />

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={cn("p-2 rounded-lg", getTypeColor(rule.ruleType))}>
              <TypeIcon className="h-5 w-5" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold line-clamp-1">
                {rule.ruleName}
              </CardTitle>
              <p className="text-sm text-muted-foreground font-mono">
                {rule.ruleCode}
              </p>
            </div>
          </div>
          
          <Badge variant={rule.isActive ? "default" : "secondary"} className="shrink-0">
            {rule.isActive ? "启用" : "禁用"}
          </Badge>
        </div>

        {rule.description && (
          <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
            {rule.description}
          </p>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">{rule.priorityLevel}</p>
            <p className="text-xs text-muted-foreground">优先级</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{rule.executionCount}</p>
            <p className="text-xs text-muted-foreground">执行次数</p>
          </div>
        </div>

        {/* 最后执行时间 */}
        {rule.lastExecutedAt && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground mb-4">
            <Clock className="h-3 w-3" />
            <span>最后执行: {new Date(rule.lastExecutedAt).toLocaleDateString()}</span>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={(e) => { e.stopPropagation(); onView(); }} className="flex-1">
            <Activity className="h-3 w-3 mr-1" />
            查看
          </Button>
          <Button variant="outline" size="sm" onClick={(e) => { e.stopPropagation(); onEdit(); }} className="flex-1">
            <Code className="h-3 w-3 mr-1" />
            编辑
          </Button>
          <Button 
            size="sm" 
            onClick={(e) => { e.stopPropagation(); onExecute(); }} 
            disabled={!rule.isActive}
            className="flex-1"
          >
            <Zap className="h-3 w-3 mr-1" />
            执行
          </Button>
        </div>
      </CardContent>

      {/* 悬浮效果装饰 */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </Card>
  )
}

// 增强的统计仪表盘
interface EnhancedStatsDashboardProps {
  statistics: {
    totalRules: number
    activeRules: number
    todayExecutions: number
    anomaliesDetected: number
    averageExecutionTime: number
    successRate: number
  }
}

export function EnhancedStatsDashboard({ statistics }: EnhancedStatsDashboardProps) {
  const stats = [
    {
      title: "总规则数",
      value: statistics.totalRules,
      icon: Shield,
      color: "blue",
      trend: "+12%",
      description: "较上月增长"
    },
    {
      title: "活跃规则",
      value: statistics.activeRules,
      icon: CheckCircle,
      color: "green",
      trend: "+8%",
      description: "启用率提升"
    },
    {
      title: "今日执行",
      value: statistics.todayExecutions,
      icon: Activity,
      color: "purple",
      trend: "+25%",
      description: "执行频次增加"
    },
    {
      title: "异常检测",
      value: statistics.anomaliesDetected,
      icon: AlertTriangle,
      color: "orange",
      trend: "-15%",
      description: "检测准确率提升"
    }
  ]

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "text-blue-600 bg-blue-50 border-blue-200",
      green: "text-green-600 bg-green-50 border-green-200",
      purple: "text-purple-600 bg-purple-50 border-purple-200",
      orange: "text-orange-600 bg-orange-50 border-orange-200"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        const colorClasses = getColorClasses(stat.color)
        
        return (
          <Card key={index} className="relative overflow-hidden group hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={cn("p-3 rounded-xl", colorClasses)}>
                  <Icon className="h-6 w-6" />
                </div>
                <div className="text-right">
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                  <p className="text-xs text-muted-foreground">{stat.description}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">{stat.title}</h3>
                <p className="text-3xl font-bold">{stat.value.toLocaleString()}</p>
              </div>

              {/* 进度条装饰 */}
              <div className="mt-4">
                <Progress 
                  value={Math.min((stat.value / 1000) * 100, 100)} 
                  className="h-1"
                />
              </div>
            </CardContent>

            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-full -translate-y-16 translate-x-16 group-hover:scale-110 transition-transform duration-500" />
          </Card>
        )
      })}
    </div>
  )
}

// 增强的搜索和筛选组件
interface EnhancedSearchFiltersProps {
  onSearch: (query: string) => void
  onFilter: (filters: any) => void
  searchQuery: string
  activeFilters: number
}

export function EnhancedSearchFilters({ 
  onSearch, 
  onFilter, 
  searchQuery, 
  activeFilters 
}: EnhancedSearchFiltersProps) {
  return (
    <Card className="border-dashed border-2 border-muted-foreground/25 bg-muted/30">
      <CardContent className="p-6">
        <div className="flex items-center gap-4">
          {/* 智能搜索 */}
          <div className="flex-1 relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
              <Sparkles className="h-4 w-4 text-muted-foreground" />
            </div>
            <Input
              type="text"
              placeholder="智能搜索规则名称、编码、描述..."
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg"
            />
            {searchQuery && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Badge variant="secondary" className="text-xs">
                  {searchQuery.length} 字符
                </Badge>
              </div>
            )}
          </div>

          {/* 快速筛选 */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="relative">
              <Target className="h-4 w-4 mr-2" />
              筛选
              {activeFilters > 0 && (
                <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs">
                  {activeFilters}
                </Badge>
              )}
            </Button>
            
            <Button variant="outline" size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              分析
            </Button>
          </div>
        </div>

        {/* 快速筛选标签 */}
        <div className="flex items-center gap-2 mt-4">
          <span className="text-sm text-muted-foreground">快速筛选:</span>
          <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
            启用规则
          </Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
            高优先级
          </Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
            最近执行
          </Badge>
          <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
            SQL规则
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}

// 增强的操作工具栏
interface EnhancedActionToolbarProps {
  selectedCount: number
  onBatchExecute: () => void
  onBatchEdit: () => void
  onExport: () => void
  onImport: () => void
  onCreateNew: () => void
}

export function EnhancedActionToolbar({
  selectedCount,
  onBatchExecute,
  onBatchEdit,
  onExport,
  onImport,
  onCreateNew
}: EnhancedActionToolbarProps) {
  return (
    <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border">
      <div className="flex items-center gap-4">
        {selectedCount > 0 ? (
          <>
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                  {selectedCount}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">已选择 {selectedCount} 个规则</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Button size="sm" onClick={onBatchExecute}>
                <Zap className="h-4 w-4 mr-2" />
                批量执行
              </Button>
              <Button variant="outline" size="sm" onClick={onBatchEdit}>
                <Code className="h-4 w-4 mr-2" />
                批量编辑
              </Button>
              <Button variant="outline" size="sm" onClick={onExport}>
                <TrendingUp className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </>
        ) : (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Shield className="h-4 w-4" />
            <span className="text-sm">选择规则以进行批量操作</span>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={onImport}>
          <Database className="h-4 w-4 mr-2" />
          导入
        </Button>
        <Button onClick={onCreateNew}>
          <Sparkles className="h-4 w-4 mr-2" />
          新建规则
        </Button>
      </div>
    </div>
  )
}
