'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/lib/toast'
import { SupervisionRule } from '@/types/supervision-rule'
import {
  Save,
  Loader2,
  Code,
  Database,
  Zap,
  AlertTriangle,
  Info,
  CheckCircle,
  X
} from 'lucide-react'

interface RuleFormProps {
  rule?: SupervisionRule
  isEditing?: boolean
  isSubmitting?: boolean
  onSubmit: (formData: Partial<SupervisionRule>) => void
  onCancel: () => void
}

// 规则类型选项
const ruleTypes = [
  { 
    value: 'SQL', 
    label: 'SQL规则', 
    description: '基于SQL查询的规则',
    icon: Database,
    color: 'text-blue-600'
  },
  { 
    value: 'DSL', 
    label: 'DSL规则', 
    description: '领域特定语言规则',
    icon: Code,
    color: 'text-green-600'
  },
  { 
    value: 'JAVASCRIPT', 
    label: 'JavaScript规则', 
    description: '基于JavaScript的复杂规则',
    icon: Zap,
    color: 'text-purple-600'
  }
]

// 规则分类选项
const ruleCategories = [
  { 
    value: 'COST_CONTROL', 
    label: '费用控制', 
    description: '医疗费用相关监管',
    color: 'bg-red-50 text-red-700 border-red-200'
  },
  { 
    value: 'FRAUD_DETECTION', 
    label: '欺诈检测', 
    description: '医保欺诈行为检测',
    color: 'bg-orange-50 text-orange-700 border-orange-200'
  },
  { 
    value: 'COMPLIANCE_CHECK', 
    label: '合规检查', 
    description: '医保政策合规性检查',
    color: 'bg-blue-50 text-blue-700 border-blue-200'
  },
  { 
    value: 'QUALITY_ASSURANCE', 
    label: '质量保证', 
    description: '医疗质量监管',
    color: 'bg-green-50 text-green-700 border-green-200'
  }
]

// 严重程度选项
const severityLevels = [
  { value: 'LOW', label: '低', color: 'text-green-600' },
  { value: 'MEDIUM', label: '中', color: 'text-yellow-600' },
  { value: 'HIGH', label: '高', color: 'text-orange-600' },
  { value: 'CRITICAL', label: '严重', color: 'text-red-600' }
]

export function RuleForm({ 
  rule, 
  isEditing = false, 
  isSubmitting = false, 
  onSubmit, 
  onCancel 
}: RuleFormProps) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    ruleName: '',
    ruleCode: '',
    ruleType: 'SQL',
    ruleCategory: 'COST_CONTROL',
    description: '',
    ruleContent: '',
    ruleSql: '',
    ruleDsl: '',
    ruleJavascript: '',
    priorityLevel: 5,
    severityLevel: 'MEDIUM',
    isActive: true,
    effectiveDate: new Date().toISOString().split('T')[0],
    expiryDate: '',
    versionNumber: '1.0',
    tags: '',
    ...rule
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // 更新表单数据
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.ruleName.trim()) {
      newErrors['ruleName'] = '规则名称不能为空'
    }

    if (!formData.ruleCode.trim()) {
      newErrors['ruleCode'] = '规则编码不能为空'
    }

    if (!formData.ruleContent.trim()) {
      newErrors['ruleContent'] = '规则内容不能为空'
    }

    // 根据规则类型验证对应内容
    if (formData.ruleType === 'SQL' && !formData.ruleSql?.trim()) {
      newErrors['ruleSql'] = 'SQL内容不能为空'
    }

    if (formData.ruleType === 'DSL' && !formData.ruleDsl?.trim()) {
      newErrors['ruleDsl'] = 'DSL内容不能为空'
    }

    if (formData.ruleType === 'JAVASCRIPT' && !formData.ruleJavascript?.trim()) {
      newErrors['ruleJavascript'] = 'JavaScript内容不能为空'
    }

    if (formData.priorityLevel < 1 || formData.priorityLevel > 10) {
      newErrors['priorityLevel'] = '优先级必须在1-10之间'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: '表单验证失败',
        description: '请检查并修正表单中的错误',
        variant: 'destructive'
      })
      return
    }

    // 处理标签
    const processedData = {
      ...formData,
      tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : []
    }

    onSubmit(processedData as Partial<SupervisionRule>)
  }

  // 获取当前规则类型的配置
  const currentRuleType = ruleTypes.find(type => type.value === formData.ruleType)
  const currentCategory = ruleCategories.find(cat => cat.value === formData.ruleCategory)

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            基本信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ruleName">规则名称 *</Label>
              <Input
                id="ruleName"
                value={formData.ruleName}
                onChange={(e) => updateFormData('ruleName', e.target.value)}
                placeholder="请输入规则名称"
                className={errors['ruleName'] ? 'border-red-500' : ''}
              />
              {errors['ruleName'] && (
                <p className="text-sm text-red-500">{errors['ruleName']}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ruleCode">规则编码 *</Label>
              <Input
                id="ruleCode"
                value={formData.ruleCode}
                onChange={(e) => updateFormData('ruleCode', e.target.value)}
                placeholder="请输入规则编码"
                className={errors['ruleCode'] ? 'border-red-500' : ''}
              />
              {errors['ruleCode'] && (
                <p className="text-sm text-red-500">{errors['ruleCode']}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">规则描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => updateFormData('description', e.target.value)}
              placeholder="请输入规则描述"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* 规则分类 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            规则分类
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>规则类型</Label>
              <Select
                value={formData.ruleType}
                onValueChange={(value) => updateFormData('ruleType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ruleTypes.map((type) => {
                    const Icon = type.icon
                    return (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <Icon className={`h-4 w-4 ${type.color}`} />
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-xs text-muted-foreground">{type.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
              {currentRuleType && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <currentRuleType.icon className={`h-4 w-4 ${currentRuleType.color}`} />
                  {currentRuleType.description}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>规则分类</Label>
              <Select
                value={formData.ruleCategory}
                onValueChange={(value) => updateFormData('ruleCategory', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ruleCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      <div>
                        <div className="font-medium">{category.label}</div>
                        <div className="text-xs text-muted-foreground">{category.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {currentCategory && (
                <Badge className={currentCategory.color}>
                  {currentCategory.label}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 规则内容 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {currentRuleType && <currentRuleType.icon className={`h-5 w-5 ${currentRuleType.color}`} />}
            规则内容
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="ruleContent">规则描述 *</Label>
            <Textarea
              id="ruleContent"
              value={formData.ruleContent}
              onChange={(e) => updateFormData('ruleContent', e.target.value)}
              placeholder="请输入规则的详细描述"
              rows={3}
              className={errors['ruleContent'] ? 'border-red-500' : ''}
            />
            {errors['ruleContent'] && (
              <p className="text-sm text-red-500">{errors['ruleContent']}</p>
            )}
          </div>

          {/* 根据规则类型显示对应的内容编辑器 */}
          {formData.ruleType === 'SQL' && (
            <div className="space-y-2">
              <Label htmlFor="ruleSql">SQL内容 *</Label>
              <Textarea
                id="ruleSql"
                value={formData.ruleSql || ''}
                onChange={(e) => updateFormData('ruleSql', e.target.value)}
                placeholder="请输入SQL查询语句"
                rows={8}
                className={`font-mono text-sm ${errors['ruleSql'] ? 'border-red-500' : ''}`}
              />
              {errors['ruleSql'] && (
                <p className="text-sm text-red-500">{errors['ruleSql']}</p>
              )}
            </div>
          )}

          {formData.ruleType === 'DSL' && (
            <div className="space-y-2">
              <Label htmlFor="ruleDsl">DSL内容 *</Label>
              <Textarea
                id="ruleDsl"
                value={formData.ruleDsl || ''}
                onChange={(e) => updateFormData('ruleDsl', e.target.value)}
                placeholder="请输入DSL规则表达式"
                rows={8}
                className={`font-mono text-sm ${errors['ruleDsl'] ? 'border-red-500' : ''}`}
              />
              {errors['ruleDsl'] && (
                <p className="text-sm text-red-500">{errors['ruleDsl']}</p>
              )}
            </div>
          )}

          {formData.ruleType === 'JAVASCRIPT' && (
            <div className="space-y-2">
              <Label htmlFor="ruleJavascript">JavaScript内容 *</Label>
              <Textarea
                id="ruleJavascript"
                value={formData.ruleJavascript || ''}
                onChange={(e) => updateFormData('ruleJavascript', e.target.value)}
                placeholder="请输入JavaScript函数代码"
                rows={8}
                className={`font-mono text-sm ${errors['ruleJavascript'] ? 'border-red-500' : ''}`}
              />
              {errors['ruleJavascript'] && (
                <p className="text-sm text-red-500">{errors['ruleJavascript']}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 规则配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            规则配置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priorityLevel">优先级 (1-10)</Label>
              <Input
                id="priorityLevel"
                type="number"
                min="1"
                max="10"
                value={formData.priorityLevel}
                onChange={(e) => updateFormData('priorityLevel', parseInt(e.target.value))}
                className={errors['priorityLevel'] ? 'border-red-500' : ''}
              />
              {errors['priorityLevel'] && (
                <p className="text-sm text-red-500">{errors['priorityLevel']}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>严重程度</Label>
              <Select
                value={formData.severityLevel}
                onValueChange={(value) => updateFormData('severityLevel', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {severityLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      <span className={level.color}>{level.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="versionNumber">版本号</Label>
              <Input
                id="versionNumber"
                value={formData.versionNumber}
                onChange={(e) => updateFormData('versionNumber', e.target.value)}
                placeholder="1.0"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="effectiveDate">生效日期</Label>
              <Input
                id="effectiveDate"
                type="date"
                value={formData.effectiveDate}
                onChange={(e) => updateFormData('effectiveDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="expiryDate">失效日期</Label>
              <Input
                id="expiryDate"
                type="date"
                value={formData.expiryDate}
                onChange={(e) => updateFormData('expiryDate', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">标签 (用逗号分隔)</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => updateFormData('tags', e.target.value)}
              placeholder="标签1, 标签2, 标签3"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => updateFormData('isActive', checked)}
            />
            <Label htmlFor="isActive">启用规则</Label>
          </div>
        </CardContent>
      </Card>

      {/* 提交按钮 */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="h-4 w-4 mr-2" />
          取消
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isEditing ? '更新规则' : '创建规则'}
        </Button>
      </div>
    </form>
  )
}
