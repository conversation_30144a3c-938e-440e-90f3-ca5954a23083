'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/lib/toast'
import { SupervisionRule, RuleListParams, RuleStatistics } from '@/types/supervision-rule'
import {
  RuleList,
  RuleStatisticsCards,
  RuleFilters,
  RuleForm,
  RuleDetail
} from '@/components/supervision-rules'
import {
  EnterpriseListLayout,
  UnifiedDetailView,
  UnifiedFormEdit
} from '@/components/common/unified-design-system'
import {
  mockSupervisionRules,
  mockRuleStatistics,
  mockApiDelay
} from '@/lib/mock-supervision-rule-data'
import {
  Shield,
  Plus,
  Play,
  Loader2,
  BarChart3,
  Activity,
  Download,
  Settings,
  Upload,
  FileText
} from 'lucide-react'

interface UnifiedRuleManagementProps {
  initialRules?: SupervisionRule[]
  initialStatistics?: RuleStatistics
}

export function UnifiedRuleManagement({ 
  initialRules = [], 
  initialStatistics 
}: UnifiedRuleManagementProps) {
  const router = useRouter()
  const { toast } = useToast()

  // 状态管理
  const [rules, setRules] = useState<SupervisionRule[]>(initialRules)
  const [statistics, setStatistics] = useState<RuleStatistics | null>(initialStatistics || null)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedRules, setSelectedRules] = useState<number[]>([])
  const [activeTab, setActiveTab] = useState('list')

  // 模态框状态
  const [detailModalOpen, setDetailModalOpen] = useState(false)
  const [formModalOpen, setFormModalOpen] = useState(false)
  const [selectedRule, setSelectedRule] = useState<SupervisionRule | null>(null)
  const [isEditing, setIsEditing] = useState(false)

  // 过滤器状态
  const [filters, setFilters] = useState<RuleListParams>({
    page: 1,
    pageSize: 20,
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  })

  // 获取认证头
  const getAuthHeaders = () => {
    const token = localStorage.getItem('access_token')
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }

  // 加载规则列表
  const loadRules = async () => {
    try {
      setIsLoading(true)

      // 尝试从API加载数据
      const searchParams = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value))
        }
      })
      searchParams.set('page', filters.page?.toString() || '1')
      searchParams.set('pageSize', filters.pageSize?.toString() || '20')

      const response = await fetch('/api/supervision-rules?' + searchParams.toString(), {
        headers: getAuthHeaders()
      })

      if (!response.ok) throw new Error('加载规则失败')

      const data = await response.json()
      // data.data 是分页结果对象，包含 items 数组
      const rulesData = data.data?.items || []
      setRules(rulesData)
    } catch (error) {
      console.warn('API加载失败，使用模拟数据:', error)

      // 使用模拟数据作为回退
      await mockApiDelay(300)
      setRules(mockSupervisionRules)

      toast({
        title: '使用演示数据',
        description: '当前使用模拟数据进行演示',
        variant: 'default'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/supervision-rules/statistics', {
        headers: getAuthHeaders()
      })

      if (!response.ok) throw new Error('加载统计信息失败')

      const data = await response.json()
      setStatistics(data.data)
    } catch (error) {
      console.warn('统计API加载失败，使用模拟数据:', error)

      // 使用模拟数据作为回退
      await mockApiDelay(200)
      setStatistics(mockRuleStatistics)
    }
  }

  // 初始化加载
  useEffect(() => {
    if (initialRules.length === 0) {
      loadRules()
    }
    if (!initialStatistics) {
      loadStatistics()
    }
  }, [filters])

  // 处理规则查看
  const handleRuleView = (ruleId: number) => {
    const rule = rules.find(r => r.id === ruleId)
    if (rule) {
      setSelectedRule(rule)
      setIsEditing(false)
      setDetailModalOpen(true)
    }
  }

  // 处理规则编辑
  const handleRuleEdit = (ruleId?: number) => {
    if (ruleId) {
      const rule = rules.find(r => r.id === ruleId)
      setSelectedRule(rule || null)
      setIsEditing(true)
    } else {
      setSelectedRule(null)
      setIsEditing(false)
    }
    setFormModalOpen(true)
  }

  // 处理规则删除
  const handleRuleDelete = async (ruleId: number) => {
    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })

      if (!response.ok) throw new Error('删除规则失败')

      setRules(prev => prev.filter(rule => rule.id !== ruleId))
      setSelectedRules(prev => prev.filter(id => id !== ruleId))

      toast({
        title: '删除成功',
        description: '规则已删除'
      })
    } catch (error) {
      toast({
        title: '删除失败',
        description: '无法删除规则',
        variant: 'destructive'
      })
    }
  }

  // 处理表单提交
  const handleFormSubmit = async (formData: Partial<SupervisionRule>) => {
    try {
      const url = isEditing && selectedRule
        ? `/api/supervision-rules/${selectedRule.id}`
        : '/api/supervision-rules'

      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(),
        body: JSON.stringify(formData)
      })

      if (!response.ok) throw new Error('保存规则失败')

      const data = await response.json()
      
      if (isEditing) {
        setRules(prev => prev.map(rule => 
          rule.id === selectedRule?.id ? data.data : rule
        ))
      } else {
        setRules(prev => [data.data, ...prev])
      }

      setFormModalOpen(false)
      setSelectedRule(null)
      
      toast({
        title: '保存成功',
        description: isEditing ? '规则已更新' : '规则已创建'
      })
    } catch (error) {
      toast({
        title: '保存失败',
        description: '无法保存规则',
        variant: 'destructive'
      })
    }
  }

  // 页面操作按钮
  const pageActions = (
    <>
      <Button variant="outline" size="sm">
        <Upload className="h-4 w-4 mr-2" />
        导入规则
      </Button>
      <Button variant="outline" size="sm">
        <FileText className="h-4 w-4 mr-2" />
        规则模板
      </Button>
      <Button onClick={() => handleRuleEdit()}>
        <Plus className="h-4 w-4 mr-2" />
        新建规则
      </Button>
    </>
  )

  // 统计卡片
  const statisticsCards = statistics && (
    <RuleStatisticsCards
      statistics={statistics}
      isLoading={!statistics}
    />
  )

  return (
    <EnterpriseListLayout
      title="监管规则"
      description="管理和执行医保基金监管规则，支持多维度筛选和批量操作"
      actions={pageActions}
      statistics={statisticsCards}
      filters={
        <RuleFilters
          filters={filters}
          onFiltersChange={setFilters}
          onReset={() => setFilters({
            page: 1,
            pageSize: 20,
            sortBy: 'createdAt',
            sortOrder: 'DESC'
          })}
        />
      }
    >

      {/* 主要内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            规则列表
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            数据分析
          </TabsTrigger>
          <TabsTrigger value="execution" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            执行监控
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          <RuleList
            rules={rules}
            isLoading={isLoading}
            selectedRules={selectedRules}
            onRuleSelect={setSelectedRules}
            onRuleToggle={async (ruleId, isActive) => {
              // 处理规则状态切换
              try {
                const response = await fetch(`/api/supervision-rules/${ruleId}`, {
                  method: 'PATCH',
                  headers: getAuthHeaders(),
                  body: JSON.stringify({ isActive })
                })

                if (!response.ok) throw new Error('更新失败')

                setRules(prev => prev.map(rule =>
                  rule.id === ruleId ? { ...rule, isActive } : rule
                ))
              } catch (error) {
                toast({
                  title: '更新失败',
                  description: '无法更新规则状态',
                  variant: 'destructive'
                })
              }
            }}
            onRuleExecute={async (ruleIds) => {
              // 处理规则执行
              console.log('执行规则:', ruleIds)
            }}
            onRuleEdit={handleRuleEdit}
            onRuleView={handleRuleView}
            onRuleDelete={handleRuleDelete}
            onRuleCopy={async (ruleId) => {
              // 处理规则复制
              console.log('复制规则:', ruleId)
            }}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-12 text-muted-foreground">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>数据分析功能开发中...</p>
          </div>
        </TabsContent>

        <TabsContent value="execution" className="space-y-6">
          <div className="text-center py-12 text-muted-foreground">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>执行监控功能开发中...</p>
          </div>
        </TabsContent>
      </Tabs>

      {/* 规则详情模态框 */}
      <UnifiedDetailView
        isOpen={detailModalOpen}
        onClose={() => setDetailModalOpen(false)}
        title="规则详情"
        data={selectedRule}
        size="xl"
        type="modal"
        renderContent={(rule) => (
          <RuleDetail
            rule={rule}
            onEdit={() => {
              setDetailModalOpen(false)
              handleRuleEdit(rule.id)
            }}
            onExecute={() => {
              console.log('执行规则:', rule.id)
            }}
            onCopy={() => {
              console.log('复制规则:', rule.id)
            }}
            onBack={() => setDetailModalOpen(false)}
          />
        )}
      />

      {/* 规则表单模态框 */}
      <UnifiedFormEdit
        isOpen={formModalOpen}
        onClose={() => {
          setFormModalOpen(false)
          setSelectedRule(null)
        }}
        title={isEditing ? '编辑规则' : '新建规则'}
        data={selectedRule}
        size="xl"
        type="modal"
        onSubmit={handleFormSubmit}
        isSubmitting={false}
        renderForm={(rule) => (
          <RuleForm
            rule={rule}
            isEditing={isEditing}
            isSubmitting={false}
            onSubmit={handleFormSubmit}
            onCancel={() => {
              setFormModalOpen(false)
              setSelectedRule(null)
            }}
          />
        )}
      />
    </EnterpriseListLayout>
  )
}
