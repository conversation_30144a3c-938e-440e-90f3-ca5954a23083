/**
 * 监管规则管理视图组件
 * 实现不同的视图模式：列表视图、卡片视图、详情视图
 */

'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { OptimizedTable } from '@/components/ui/optimized-table'
import { SupervisionRule } from '@/types/supervision-rule'
import { formatDate } from '@/lib/format-utils'
import {
  Shield,
  Play,
  Pause,
  Edit,
  Copy,
  Trash2,
  Eye,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  MoreHorizontal,
  Grid3X3,
  List,
  BarChart3
} from 'lucide-react'

interface RuleManagementViewsProps {
  rules: SupervisionRule[]
  selectedRules: number[]
  onRuleSelect: (ruleIds: number[]) => void
  onRuleToggle: (ruleId: number, isActive: boolean) => void
  onRuleExecute: (ruleIds: number[]) => void
  onRuleEdit: (ruleId: number) => void
  onRuleView: (ruleId: number) => void
  onRuleDelete: (ruleId: number) => void
  onRuleCopy: (ruleId: number) => void
  isLoading?: boolean
}

export function RuleManagementViews({
  rules,
  selectedRules,
  onRuleSelect,
  onRuleToggle,
  onRuleExecute,
  onRuleEdit,
  onRuleView,
  onRuleDelete,
  onRuleCopy,
  isLoading = false
}: RuleManagementViewsProps) {
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'analytics'>('list')

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>规则管理</CardTitle>
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
            <TabsList>
              <TabsTrigger value="list" className="flex items-center gap-2">
                <List className="h-4 w-4" />
                列表
              </TabsTrigger>
              <TabsTrigger value="grid" className="flex items-center gap-2">
                <Grid3X3 className="h-4 w-4" />
                卡片
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                分析
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={viewMode}>
          <TabsContent value="list">
            <RuleListView
              rules={rules}
              selectedRules={selectedRules}
              onRuleSelect={onRuleSelect}
              onRuleToggle={onRuleToggle}
              onRuleExecute={onRuleExecute}
              onRuleEdit={onRuleEdit}
              onRuleView={onRuleView}
              onRuleDelete={onRuleDelete}
              onRuleCopy={onRuleCopy}
              isLoading={isLoading}
            />
          </TabsContent>
          
          <TabsContent value="grid">
            <RuleGridView
              rules={rules}
              selectedRules={selectedRules}
              onRuleSelect={onRuleSelect}
              onRuleToggle={onRuleToggle}
              onRuleExecute={onRuleExecute}
              onRuleEdit={onRuleEdit}
              onRuleView={onRuleView}
              onRuleDelete={onRuleDelete}
              onRuleCopy={onRuleCopy}
            />
          </TabsContent>
          
          <TabsContent value="analytics">
            <RuleAnalyticsView rules={rules} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

/**
 * 规则列表视图
 */
function RuleListView({
  rules,
  selectedRules,
  onRuleSelect,
  onRuleToggle,
  onRuleExecute,
  onRuleEdit,
  onRuleView,
  onRuleDelete,
  onRuleCopy,
  isLoading
}: RuleManagementViewsProps) {
  const columns = [
    {
      key: 'name',
      title: '规则名称',
      width: 200,
      sortable: true,
      render: (rule: SupervisionRule) => (
        <div>
          <div className="font-medium">{rule.ruleName}</div>
          <div className="text-sm text-muted-foreground">{rule.ruleCode}</div>
        </div>
      )
    },
    {
      key: 'ruleType',
      title: '类型',
      width: 100,
      render: (rule: SupervisionRule) => (
        <Badge variant="outline">{rule.ruleType}</Badge>
      )
    },
    {
      key: 'priority',
      title: '优先级',
      width: 100,
      render: (rule: SupervisionRule) => {
        const variant = rule.severityLevel === 'HIGH' ? 'destructive' :
                      rule.severityLevel === 'MEDIUM' ? 'default' : 'secondary'
        return <Badge variant={variant}>{rule.severityLevel}</Badge>
      }
    },
    {
      key: 'isActive',
      title: '状态',
      width: 100,
      render: (rule: SupervisionRule) => (
        <div className="flex items-center gap-2">
          {rule.isActive ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <Clock className="h-4 w-4 text-gray-400" />
          )}
          <span className={rule.isActive ? 'text-green-600' : 'text-gray-500'}>
            {rule.isActive ? '活跃' : '禁用'}
          </span>
        </div>
      )
    },
    {
      key: 'lastExecutedAt',
      title: '最后执行',
      width: 150,
      render: (rule: SupervisionRule) => (
        <div className="text-sm">
          {rule.lastExecutedAt ? formatDate(rule.lastExecutedAt) : '从未执行'}
        </div>
      )
    },
    {
      key: 'actions',
      title: '操作',
      width: 200,
      render: (rule: SupervisionRule) => (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRuleView(rule.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRuleEdit(rule.id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRuleToggle(rule.id, !rule.isActive)}
          >
            {rule.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRuleExecute([rule.id])}
            disabled={!rule.isActive}
          >
            <Activity className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRuleCopy(rule.id)}
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRuleDelete(rule.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ]

  return (
    <OptimizedTable
      data={rules}
      columns={columns}
      rowHeight={60}
      maxHeight={600}
      searchable={true}
      sortable={true}
      filterable={true}


      loading={isLoading}
      emptyMessage="暂无监管规则"
      virtualScrollThreshold={50}
      className="w-full"

    />
  )
}

/**
 * 规则卡片视图
 */
function RuleGridView({
  rules,
  selectedRules,
  onRuleSelect,
  onRuleToggle,
  onRuleExecute,
  onRuleEdit,
  onRuleView,
  onRuleDelete,
  onRuleCopy
}: RuleManagementViewsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {rules.map((rule) => (
        <Card key={rule.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <CardTitle className="text-base truncate">{rule.ruleName}</CardTitle>
                <p className="text-sm text-muted-foreground font-mono">{rule.ruleCode}</p>
              </div>
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="text-xs">{rule.ruleType}</Badge>
                {rule.isActive ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <Clock className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-3">
            <div className="text-sm text-muted-foreground line-clamp-2">
              {rule.description || '暂无描述'}
            </div>
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>优先级: {rule.priorityLevel}</span>
              <span>
                {rule.lastExecutedAt ? formatDate(rule.lastExecutedAt) : '从未执行'}
              </span>
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRuleView(rule.id)}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-1" />
                查看
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRuleEdit(rule.id)}
                className="flex-1"
              >
                <Edit className="h-4 w-4 mr-1" />
                编辑
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRuleToggle(rule.id, !rule.isActive)}
              >
                {rule.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 规则分析视图
 */
function RuleAnalyticsView({ rules }: { rules: SupervisionRule[] }) {
  const analytics = {
    totalRules: rules.length,
    activeRules: rules.filter(r => r.isActive).length,
    rulesByType: rules.reduce((acc, rule) => {
      acc[rule.ruleType] = (acc[rule.ruleType] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    rulesByPriority: rules.reduce((acc, rule) => {
      acc[rule.severityLevel] = (acc[rule.severityLevel] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold">{analytics.totalRules}</div>
            <div className="text-sm text-muted-foreground">总规则数</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-green-600">{analytics.activeRules}</div>
            <div className="text-sm text-muted-foreground">活跃规则</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round((analytics.activeRules / Math.max(analytics.totalRules, 1)) * 100)}%
            </div>
            <div className="text-sm text-muted-foreground">活跃率</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Object.keys(analytics.rulesByType).length}
            </div>
            <div className="text-sm text-muted-foreground">规则类型</div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>按类型分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(analytics.rulesByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span>{type}</span>
                  <Badge variant="outline">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>按优先级分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(analytics.rulesByPriority).map(([priority, count]) => (
                <div key={priority} className="flex items-center justify-between">
                  <span>{priority}</span>
                  <Badge variant="outline">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
