'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader2,
  Database,
  Code,
  Zap,
  Shield,
  Target,
  Activity
} from 'lucide-react'

// shadcn/UI 标准颜色系统
export const ruleColors = {
  // 规则状态颜色
  status: {
    active: "bg-green-50 text-green-700 border-green-200 hover:bg-green-100",
    inactive: "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100",
    error: "bg-red-50 text-red-700 border-red-200 hover:bg-red-100",
    warning: "bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100"
  },
  
  // 规则类型颜色
  type: {
    SQL: "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100",
    DSL: "bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100",
    JAVASCRIPT: "bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100"
  },
  
  // 规则分类颜色
  category: {
    COST_CONTROL: "bg-red-50 text-red-700 border-red-200 hover:bg-red-100",
    FRAUD_DETECTION: "bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100",
    COMPLIANCE_CHECK: "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100",
    QUALITY_ASSURANCE: "bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
  },
  
  // 严重程度颜色
  severity: {
    LOW: "bg-green-50 text-green-700 border-green-200",
    MEDIUM: "bg-yellow-50 text-yellow-700 border-yellow-200",
    HIGH: "bg-orange-50 text-orange-700 border-orange-200",
    CRITICAL: "bg-red-50 text-red-700 border-red-200"
  }
}

// 规则状态标签组件
interface RuleStatusBadgeProps {
  isActive: boolean
  className?: string
}

export function RuleStatusBadge({ isActive, className }: RuleStatusBadgeProps) {
  return (
    <Badge 
      variant="outline" 
      className={cn(
        isActive ? ruleColors.status.active : ruleColors.status.inactive,
        className
      )}
    >
      {isActive ? (
        <CheckCircle className="h-3 w-3 mr-1" />
      ) : (
        <XCircle className="h-3 w-3 mr-1" />
      )}
      {isActive ? '启用' : '禁用'}
    </Badge>
  )
}

// 规则类型标签组件
interface RuleTypeBadgeProps {
  type: string // 支持新旧两种类型格式
  className?: string
}

export function RuleTypeBadge({ type, className }: RuleTypeBadgeProps) {
  // 类型转换：将旧类型映射到新类型
  const getDisplayType = (ruleType: string) => {
    switch (ruleType) {
      case 'BASIC':
      case 'TEMPLATE':
        return 'SQL'
      case 'ADVANCED':
        return 'DSL'
      case 'CUSTOM':
        return 'JAVASCRIPT'
      // 新类型直接返回
      case 'SQL':
      case 'DSL':
      case 'JAVASCRIPT':
        return ruleType
      default:
        return 'SQL' // 默认值
    }
  }

  const displayType = getDisplayType(type)

  const icons = {
    SQL: Database,
    DSL: Code,
    JAVASCRIPT: Zap
  }

  const Icon = icons[displayType as keyof typeof icons]

  return (
    <Badge
      variant="outline"
      className={cn(ruleColors.type[displayType], className)}
    >
      <Icon className="h-3 w-3 mr-1" />
      {displayType}
    </Badge>
  )
}

// 规则分类标签组件
interface RuleCategoryBadgeProps {
  category: 'COST_CONTROL' | 'FRAUD_DETECTION' | 'COMPLIANCE_CHECK' | 'QUALITY_ASSURANCE'
  className?: string
}

export function RuleCategoryBadge({ category, className }: RuleCategoryBadgeProps) {
  const labels = {
    COST_CONTROL: '费用控制',
    FRAUD_DETECTION: '欺诈检测',
    COMPLIANCE_CHECK: '合规检查',
    QUALITY_ASSURANCE: '质量保证'
  }
  
  const icons = {
    COST_CONTROL: Shield,
    FRAUD_DETECTION: AlertTriangle,
    COMPLIANCE_CHECK: CheckCircle,
    QUALITY_ASSURANCE: Target
  }
  
  const Icon = icons[category]
  
  return (
    <Badge 
      variant="outline" 
      className={cn(ruleColors.category[category], className)}
    >
      <Icon className="h-3 w-3 mr-1" />
      {labels[category]}
    </Badge>
  )
}

// 严重程度标签组件
interface RuleSeverityBadgeProps {
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  className?: string
}

export function RuleSeverityBadge({ severity, className }: RuleSeverityBadgeProps) {
  const labels = {
    LOW: '低',
    MEDIUM: '中',
    HIGH: '高',
    CRITICAL: '严重'
  }
  
  return (
    <Badge 
      variant="outline" 
      className={cn(ruleColors.severity[severity], className)}
    >
      {labels[severity]}
    </Badge>
  )
}

// 优先级显示组件
interface RulePriorityProps {
  level: number
  className?: string
}

export function RulePriority({ level, className }: RulePriorityProps) {
  const getPriorityColor = (level: number) => {
    if (level >= 8) return "text-red-600 bg-red-50"
    if (level >= 5) return "text-yellow-600 bg-yellow-50"
    return "text-green-600 bg-green-50"
  }
  
  const getPriorityLabel = (level: number) => {
    if (level >= 8) return "高"
    if (level >= 5) return "中"
    return "低"
  }
  
  return (
    <div className={cn(
      "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
      getPriorityColor(level),
      className
    )}>
      <span className="font-bold mr-1">{getPriorityLabel(level)}</span>
      <span className="text-muted-foreground">({level})</span>
    </div>
  )
}

// 执行状态组件
interface ExecutionStatusProps {
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  progress?: number
  className?: string
}

export function ExecutionStatus({ status, progress, className }: ExecutionStatusProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          icon: Clock,
          color: "text-yellow-600 bg-yellow-50",
          label: '等待中'
        }
      case 'RUNNING':
        return {
          icon: Loader2,
          color: "text-blue-600 bg-blue-50",
          label: '执行中',
          animate: true
        }
      case 'SUCCESS':
        return {
          icon: CheckCircle,
          color: "text-green-600 bg-green-50",
          label: '成功'
        }
      case 'FAILED':
        return {
          icon: XCircle,
          color: "text-red-600 bg-red-50",
          label: '失败'
        }
      case 'CANCELLED':
        return {
          icon: XCircle,
          color: "text-gray-600 bg-gray-50",
          label: '已取消'
        }
      default:
        return {
          icon: AlertTriangle,
          color: "text-gray-600 bg-gray-50",
          label: '未知'
        }
    }
  }
  
  const config = getStatusConfig(status)
  const Icon = config.icon
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className={cn(
        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
        config.color
      )}>
        <Icon className={cn(
          "h-3 w-3 mr-1",
          config.animate && "animate-spin"
        )} />
        {config.label}
        {status === 'RUNNING' && progress !== undefined && (
          <span className="ml-1">({progress}%)</span>
        )}
      </div>
      
      {status === 'RUNNING' && progress !== undefined && (
        <Progress value={progress} className="h-1 w-20" />
      )}
    </div>
  )
}

// 规则统计卡片组件
interface RuleStatsCardProps {
  title: string
  value: string | number
  description?: string
  icon: React.ComponentType<{ className?: string }>
  trend?: number
  trendLabel?: string
  color?: 'blue' | 'green' | 'orange' | 'red' | 'purple'
  className?: string
}

export function RuleStatsCard({ 
  title, 
  value, 
  description, 
  icon: Icon, 
  trend, 
  trendLabel,
  color = 'blue',
  className 
}: RuleStatsCardProps) {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-50",
    green: "text-green-600 bg-green-50",
    orange: "text-orange-600 bg-orange-50",
    red: "text-red-600 bg-red-50",
    purple: "text-purple-600 bg-purple-50"
  }
  
  return (
    <div className={cn(
      "relative overflow-hidden rounded-lg border bg-card p-6 shadow-sm",
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">
            {title}
          </p>
          <p className={cn("text-2xl font-bold", colorClasses[color].split(' ')[0])}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          {description && (
            <p className="text-xs text-muted-foreground">
              {description}
            </p>
          )}
        </div>
        <div className={cn(
          "p-3 rounded-full",
          colorClasses[color]
        )}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      
      {trend !== undefined && trendLabel && (
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">{trendLabel}</span>
            <span className={cn(
              trend >= 80 ? 'text-green-600' : 
              trend >= 60 ? 'text-yellow-600' : 'text-red-600'
            )}>
              {trend.toFixed(1)}%
            </span>
          </div>
          <Progress value={trend} className="h-1" />
        </div>
      )}
      
      {/* 装饰性渐变 */}
      <div className={cn(
        "absolute top-0 right-0 w-20 h-20 opacity-10 rounded-full -translate-y-10 translate-x-10",
        colorClasses[color]
      )} />
    </div>
  )
}

// 操作按钮组组件
interface RuleActionButtonsProps {
  onView?: () => void
  onEdit?: () => void
  onExecute?: () => void
  onCopy?: () => void
  onDelete?: () => void
  isExecuting?: boolean
  isActive?: boolean
  className?: string
}

export function RuleActionButtons({
  onView,
  onEdit,
  onExecute,
  onCopy,
  onDelete,
  isExecuting = false,
  isActive = true,
  className
}: RuleActionButtonsProps) {
  return (
    <div className={cn("flex items-center gap-1", className)}>
      {onView && (
        <Button variant="ghost" size="sm" onClick={onView}>
          <Activity className="h-4 w-4" />
        </Button>
      )}
      
      {onEdit && (
        <Button variant="ghost" size="sm" onClick={onEdit}>
          <Code className="h-4 w-4" />
        </Button>
      )}
      
      {onExecute && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onExecute}
          disabled={!isActive || isExecuting}
        >
          {isExecuting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Zap className="h-4 w-4" />
          )}
        </Button>
      )}
      
      {onCopy && (
        <Button variant="ghost" size="sm" onClick={onCopy}>
          <Target className="h-4 w-4" />
        </Button>
      )}
      
      {onDelete && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onDelete}
          className="text-destructive hover:text-destructive"
        >
          <XCircle className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}
