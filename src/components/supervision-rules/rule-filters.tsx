'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { RuleListParams } from '@/types/supervision-rule'
import {
  Search,
  Filter,
  X,
  RotateCcw,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

interface RuleFiltersProps {
  filters: RuleListParams
  onFiltersChange: (filters: RuleListParams) => void
  onReset: () => void
}

export function RuleFilters({ filters, onFiltersChange, onReset }: RuleFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeFiltersCount, setActiveFiltersCount] = useState(0)

  // 更新过滤器
  const updateFilter = (key: keyof RuleListParams, value: any) => {
    const newFilters = { ...filters, [key]: value }
    onFiltersChange(newFilters)
    
    // 计算活跃过滤器数量
    const count = Object.entries(newFilters).filter(([k, v]) => {
      if (k === 'page' || k === 'pageSize' || k === 'sortBy' || k === 'sortOrder') return false
      return v !== undefined && v !== '' && v !== null
    }).length
    setActiveFiltersCount(count)
  }

  // 清除单个过滤器
  const clearFilter = (key: keyof RuleListParams) => {
    updateFilter(key, undefined)
  }

  // 规则类型选项（基于数据库实际存储的值）
  const ruleTypes = [
    { value: 'SQL', label: 'SQL规则' },
    { value: 'DSL', label: 'DSL规则' },
    { value: 'JAVASCRIPT', label: 'JavaScript规则' }
  ]

  // 规则分类选项
  const ruleCategories = [
    { value: 'COST_CONTROL', label: '费用控制' },
    { value: 'FRAUD_DETECTION', label: '欺诈检测' },
    { value: 'COMPLIANCE_CHECK', label: '合规检查' },
    { value: 'QUALITY_ASSURANCE', label: '质量保证' }
  ]

  // 严重程度选项
  const severityLevels = [
    { value: 'LOW', label: '低' },
    { value: 'MEDIUM', label: '中' },
    { value: 'HIGH', label: '高' },
    { value: 'CRITICAL', label: '严重' }
  ]

  // 状态选项
  const statusOptions = [
    { value: 'active', label: '启用' },
    { value: 'inactive', label: '禁用' }
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            筛选条件
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                className="text-muted-foreground"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 搜索框 - 始终显示 */}
        <div className="space-y-2">
          <Label htmlFor="search">搜索规则</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="搜索规则名称、编码或描述..."
              value={filters.search || ''}
              onChange={(e) => updateFilter('search', e.target.value)}
              className="pl-10"
            />
            {filters.search && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('search')}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* 展开的过滤器 */}
        {isExpanded && (
          <>
            <Separator />
            
            {/* 规则类型和分类 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>规则类型</Label>
                <Select
                  value={filters.ruleType || ''}
                  onValueChange={(value) => updateFilter('ruleType', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择规则类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部类型</SelectItem>
                    {ruleTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>规则分类</Label>
                <Select
                  value={filters.ruleCategory || ''}
                  onValueChange={(value) => updateFilter('ruleCategory', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择规则分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部分类</SelectItem>
                    {ruleCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 优先级和严重程度 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>优先级范围</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="1"
                    max="10"
                    placeholder="最低"
                    value={filters.minPriority || ''}
                    onChange={(e) => updateFilter('minPriority', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-20"
                  />
                  <span className="text-muted-foreground">-</span>
                  <Input
                    type="number"
                    min="1"
                    max="10"
                    placeholder="最高"
                    value={filters.maxPriority || ''}
                    onChange={(e) => updateFilter('maxPriority', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-20"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>严重程度</Label>
                <Select
                  value={filters.severityLevel || ''}
                  onValueChange={(value) => updateFilter('severityLevel', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择严重程度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部程度</SelectItem>
                    {severityLevels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 状态和日期 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>规则状态</Label>
                <Select
                  value={filters.isActive !== undefined ? (filters.isActive ? 'active' : 'inactive') : ''}
                  onValueChange={(value) => {
                    if (value === '') {
                      updateFilter('isActive', undefined)
                    } else {
                      updateFilter('isActive', value === 'active')
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>创建日期</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="date"
                    value={filters.createdAfter || ''}
                    onChange={(e) => updateFilter('createdAfter', e.target.value || undefined)}
                    className="flex-1"
                  />
                  <span className="text-muted-foreground">至</span>
                  <Input
                    type="date"
                    value={filters.createdBefore || ''}
                    onChange={(e) => updateFilter('createdBefore', e.target.value || undefined)}
                    className="flex-1"
                  />
                </div>
              </div>
            </div>

            {/* 标签过滤 */}
            <div className="space-y-2">
              <Label htmlFor="tags">标签</Label>
              <Input
                id="tags"
                placeholder="输入标签名称..."
                value={filters.tags || ''}
                onChange={(e) => updateFilter('tags', e.target.value || undefined)}
              />
            </div>

            {/* 高级选项 */}
            <Separator />
            <div className="space-y-3">
              <Label className="text-sm font-medium">高级选项</Label>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasExecutions"
                  checked={filters.hasExecutions || false}
                  onCheckedChange={(checked) => updateFilter('hasExecutions', checked || undefined)}
                />
                <Label htmlFor="hasExecutions" className="text-sm">
                  仅显示已执行的规则
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="recentlyModified"
                  checked={filters.recentlyModified || false}
                  onCheckedChange={(checked) => updateFilter('recentlyModified', checked || undefined)}
                />
                <Label htmlFor="recentlyModified" className="text-sm">
                  仅显示最近修改的规则
                </Label>
              </div>
            </div>
          </>
        )}

        {/* 活跃过滤器标签 */}
        {activeFiltersCount > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-medium">当前过滤条件</Label>
              <div className="flex flex-wrap gap-2">
                {filters.search && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    搜索: {filters.search}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFilter('search')}
                      className="h-4 w-4 p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                
                {filters.ruleType && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    类型: {ruleTypes.find(t => t.value === filters.ruleType)?.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFilter('ruleType')}
                      className="h-4 w-4 p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                
                {filters.ruleCategory && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    分类: {ruleCategories.find(c => c.value === filters.ruleCategory)?.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFilter('ruleCategory')}
                      className="h-4 w-4 p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                
                {filters.isActive !== undefined && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    状态: {filters.isActive ? '启用' : '禁用'}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFilter('isActive')}
                      className="h-4 w-4 p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
