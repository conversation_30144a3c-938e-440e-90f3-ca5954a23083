'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface EnhancedStatsCardProps {
  title: string
  value: string | number
  description?: string
  icon: LucideIcon
  trend?: {
    value: number
    label: string
    isPositive?: boolean
  }
  progress?: {
    value: number
    max?: number
    label?: string
  }
  variant?: 'default' | 'success' | 'warning' | 'danger'
  className?: string
}

const variantStyles = {
  default: {
    icon: 'text-muted-foreground',
    value: 'text-foreground',
    trend: 'text-muted-foreground'
  },
  success: {
    icon: 'text-green-600',
    value: 'text-green-700',
    trend: 'text-green-600'
  },
  warning: {
    icon: 'text-yellow-600',
    value: 'text-yellow-700',
    trend: 'text-yellow-600'
  },
  danger: {
    icon: 'text-red-600',
    value: 'text-red-700',
    trend: 'text-red-600'
  }
}

export function EnhancedStatsCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  progress,
  variant = 'default',
  className
}: EnhancedStatsCardProps) {
  const styles = variantStyles[variant]

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent className="pt-0">
        <div className={cn("text-2xl font-bold", styles.value)}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>

        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}

        {trend && (
          <div className="flex items-center mt-2">
            <span className={cn(
              "text-xs font-medium",
              trend.isPositive !== false ? 'text-green-600' : 'text-red-600'
            )}>
              {trend.isPositive !== false ? '+' : ''}{trend.value}%
            </span>
            <span className="text-xs text-muted-foreground ml-1">
              {trend.label}
            </span>
          </div>
        )}

        {progress && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-xs mb-2">
              <span className="text-muted-foreground">
                {progress.label || '进度'}
              </span>
              <span className={cn("font-medium", styles.trend)}>
                {progress.value.toFixed(1)}%
              </span>
            </div>
            <Progress
              value={progress.value}
              max={progress.max || 100}
              className="h-2"
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 预设的统计卡片变体
export function StatsCardSuccess(props: Omit<EnhancedStatsCardProps, 'variant'>) {
  return <EnhancedStatsCard {...props} variant="success" />
}

export function StatsCardWarning(props: Omit<EnhancedStatsCardProps, 'variant'>) {
  return <EnhancedStatsCard {...props} variant="warning" />
}

export function StatsCardDanger(props: Omit<EnhancedStatsCardProps, 'variant'>) {
  return <EnhancedStatsCard {...props} variant="danger" />
}
