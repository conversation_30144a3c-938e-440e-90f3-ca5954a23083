'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { OptimizedTable } from '@/components/ui/optimized-table'
import { useToast } from '@/lib/toast'
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Eye,
  Download,
  Loader2,
  Activity,
  Zap
} from 'lucide-react'

interface RuleExecution {
  id: string
  ruleId: number
  ruleName: string
  executionId: string
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  startedAt: string
  endedAt?: string
  duration?: number
  processedCount: number
  matchedCount: number
  errorMessage?: string
  progress?: number
}

interface RuleExecutionProps {
  executions: RuleExecution[]
  isLoading?: boolean
  onRefresh: () => void
  onViewDetails: (executionId: string) => void
  onCancelExecution: (executionId: string) => void
  onDownloadResults: (executionId: string) => void
}

export function RuleExecutionList({
  executions,
  isLoading = false,
  onRefresh,
  onViewDetails,
  onCancelExecution,
  onDownloadResults
}: RuleExecutionProps) {
  const { toast } = useToast()
  const [cancellingExecutions, setCancellingExecutions] = useState<Set<string>>(new Set())

  // 获取状态图标和样式
  const getStatusDisplay = (status: RuleExecution['status'], progress?: number) => {
    switch (status) {
      case 'PENDING':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          label: '等待中',
          variant: 'secondary' as const
        }
      case 'RUNNING':
        return {
          icon: Loader2,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          label: `执行中 ${progress ? `(${progress}%)` : ''}`,
          variant: 'default' as const,
          animate: true
        }
      case 'SUCCESS':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          label: '成功',
          variant: 'default' as const
        }
      case 'FAILED':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          label: '失败',
          variant: 'destructive' as const
        }
      case 'CANCELLED':
        return {
          icon: Square,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          label: '已取消',
          variant: 'outline' as const
        }
      default:
        return {
          icon: AlertTriangle,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          label: '未知',
          variant: 'outline' as const
        }
    }
  }

  // 处理取消执行
  const handleCancelExecution = async (executionId: string) => {
    setCancellingExecutions(prev => new Set([...prev, executionId]))
    try {
      await onCancelExecution(executionId)
      toast({
        title: '取消成功',
        description: '规则执行已取消'
      })
    } catch (error) {
      toast({
        title: '取消失败',
        description: '无法取消规则执行',
        variant: 'destructive'
      })
    } finally {
      setCancellingExecutions(prev => {
        const newSet = new Set(prev)
        newSet.delete(executionId)
        return newSet
      })
    }
  }

  // 格式化持续时间
  const formatDuration = (duration?: number) => {
    if (!duration) return '-'
    if (duration < 1000) return `${duration}ms`
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`
    return `${(duration / 60000).toFixed(1)}min`
  }

  // 表格列定义
  const columns = [
    {
      key: 'ruleInfo',
      title: '规则信息',
      width: 250,
      render: (execution: RuleExecution) => (
        <div className="space-y-1">
          <div className="font-medium">{execution.ruleName}</div>
          <div className="text-sm text-muted-foreground">ID: {execution.ruleId}</div>
          <div className="text-xs text-muted-foreground font-mono">
            {execution.executionId}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: '状态',
      width: 150,
      render: (execution: RuleExecution) => {
        const status = getStatusDisplay(execution.status, execution.progress)
        const Icon = status.icon
        return (
          <div className="flex items-center gap-2">
            <div className={`p-1 rounded-full ${status.bgColor}`}>
              <Icon className={`h-4 w-4 ${status.color} ${status.animate ? 'animate-spin' : ''}`} />
            </div>
            <Badge variant={status.variant}>
              {status.label}
            </Badge>
          </div>
        )
      }
    },
    {
      key: 'progress',
      title: '进度',
      width: 120,
      render: (execution: RuleExecution) => {
        if (execution.status === 'RUNNING' && execution.progress !== undefined) {
          return (
            <div className="space-y-1">
              <Progress value={execution.progress} className="h-2" />
              <div className="text-xs text-center text-muted-foreground">
                {execution.progress}%
              </div>
            </div>
          )
        }
        if (execution.status === 'SUCCESS') {
          return (
            <div className="text-center">
              <div className="text-sm font-medium text-green-600">100%</div>
              <div className="text-xs text-muted-foreground">已完成</div>
            </div>
          )
        }
        return <div className="text-center text-muted-foreground">-</div>
      }
    },
    {
      key: 'timing',
      title: '时间信息',
      width: 180,
      render: (execution: RuleExecution) => (
        <div className="space-y-1 text-sm">
          <div>
            开始: {new Date(execution.startedAt).toLocaleString()}
          </div>
          {execution.endedAt && (
            <div>
              结束: {new Date(execution.endedAt).toLocaleString()}
            </div>
          )}
          <div className="text-muted-foreground">
            耗时: {formatDuration(execution.duration)}
          </div>
        </div>
      )
    },
    {
      key: 'results',
      title: '执行结果',
      width: 150,
      render: (execution: RuleExecution) => (
        <div className="space-y-1 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">处理:</span>
            <span className="font-medium">{execution.processedCount.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">匹配:</span>
            <span className="font-medium text-orange-600">{execution.matchedCount.toLocaleString()}</span>
          </div>
          {execution.matchedCount > 0 && execution.processedCount > 0 && (
            <div className="text-xs text-muted-foreground">
              命中率: {((execution.matchedCount / execution.processedCount) * 100).toFixed(1)}%
            </div>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      title: '操作',
      width: 150,
      className: 'text-right',
      render: (execution: RuleExecution) => (
        <div className="flex items-center justify-end gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onViewDetails(execution.executionId)}
            title="查看详情"
          >
            <Eye className="h-4 w-4" />
          </Button>
          
          {execution.status === 'SUCCESS' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDownloadResults(execution.executionId)}
              title="下载结果"
            >
              <Download className="h-4 w-4" />
            </Button>
          )}
          
          {(execution.status === 'PENDING' || execution.status === 'RUNNING') && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCancelExecution(execution.executionId)}
              disabled={cancellingExecutions.has(execution.executionId)}
              title="取消执行"
              className="text-destructive hover:text-destructive"
            >
              {cancellingExecutions.has(execution.executionId) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Square className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      )
    }
  ]

  // 统计信息
  const stats = {
    total: executions.length,
    running: executions.filter(e => e.status === 'RUNNING').length,
    success: executions.filter(e => e.status === 'SUCCESS').length,
    failed: executions.filter(e => e.status === 'FAILED').length,
    pending: executions.filter(e => e.status === 'PENDING').length
  }

  return (
    <div className="space-y-6">
      {/* 统计概览 */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">总执行数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">执行中</p>
                <p className="text-2xl font-bold text-blue-600">{stats.running}</p>
              </div>
              <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">成功</p>
                <p className="text-2xl font-bold text-green-600">{stats.success}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">失败</p>
                <p className="text-2xl font-bold text-red-600">{stats.failed}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">等待中</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 执行列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              执行记录
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <OptimizedTable
            data={executions}
            columns={columns}
            rowHeight={80}
            maxHeight={600}
            searchable={true}
            sortable={true}
            onRowClick={(execution) => onViewDetails(execution.executionId)}
            loading={isLoading}
            emptyMessage="暂无执行记录"
            virtualScrollThreshold={50}
            className="w-full"
          />
        </CardContent>
      </Card>
    </div>
  )
}
