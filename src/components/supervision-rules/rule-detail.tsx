'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { SupervisionRule } from '@/types/supervision-rule'
import {
  Shield,
  Edit,
  Play,
  Copy,
  ArrowLeft,
  Calendar,
  User,
  Code,
  Database,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  FileText,
  Tag
} from 'lucide-react'

interface RuleDetailProps {
  rule: SupervisionRule
  onEdit: () => void
  onExecute: () => void
  onCopy: () => void
  onBack: () => void
}

export function RuleDetail({ rule, onEdit, onExecute, onCopy, onBack }: RuleDetailProps) {
  const [activeTab, setActiveTab] = useState('overview')

  // 获取规则类型图标
  const getRuleTypeIcon = (ruleType: string) => {
    switch (ruleType) {
      case 'SQL': return Database
      case 'DSL': return Code
      case 'JAVASCRIPT': return Zap
      default: return FileText
    }
  }

  // 获取规则类型颜色
  const getRuleTypeColor = (ruleType: string) => {
    switch (ruleType) {
      case 'SQL': return 'text-blue-600'
      case 'DSL': return 'text-green-600'
      case 'JAVASCRIPT': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  // 获取分类标签样式
  const getCategoryBadge = (category: string) => {
    const variants = {
      'COST_CONTROL': 'destructive',
      'FRAUD_DETECTION': 'default',
      'COMPLIANCE_CHECK': 'secondary',
      'QUALITY_ASSURANCE': 'outline'
    } as const
    return variants[category as keyof typeof variants] || 'outline'
  }

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 bg-red-50'
      case 'HIGH': return 'text-orange-600 bg-orange-50'
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50'
      case 'LOW': return 'text-green-600 bg-green-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const RuleTypeIcon = getRuleTypeIcon(rule.ruleType)

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">{rule.ruleName}</h1>
            <Badge variant={rule.isActive ? 'default' : 'secondary'}>
              {rule.isActive ? '启用' : '禁用'}
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onCopy}>
            <Copy className="h-4 w-4 mr-2" />
            复制规则
          </Button>
          <Button variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            编辑规则
          </Button>
          <Button 
            onClick={onExecute}
            disabled={!rule.isActive}
          >
            <Play className="h-4 w-4 mr-2" />
            执行规则
          </Button>
        </div>
      </div>

      {/* 基本信息卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full bg-blue-50`}>
                <RuleTypeIcon className={`h-6 w-6 ${getRuleTypeColor(rule.ruleType)}`} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">规则类型</p>
                <p className="font-medium">{rule.ruleType}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-green-50">
                <Tag className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">规则分类</p>
                <Badge variant={getCategoryBadge(rule.ruleCategory)}>
                  {rule.ruleCategory}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-orange-50">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">严重程度</p>
                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severityLevel)}`}>
                  {rule.severityLevel}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="content">规则内容</TabsTrigger>
          <TabsTrigger value="execution">执行历史</TabsTrigger>
          <TabsTrigger value="settings">配置信息</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  基本信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">规则编码</label>
                  <p className="font-mono text-sm">{rule.ruleCode}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">规则描述</label>
                  <p className="text-sm">{rule.description || '暂无描述'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">优先级</label>
                  <p className="text-sm">{rule.priorityLevel}/10</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">版本号</label>
                  <p className="text-sm">{rule.versionNumber || '1.0'}</p>
                </div>
              </CardContent>
            </Card>

            {/* 时间信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  时间信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">创建时间</label>
                  <p className="text-sm">{new Date(rule.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">更新时间</label>
                  <p className="text-sm">{new Date(rule.updatedAt).toLocaleString()}</p>
                </div>
                {rule.effectiveDate && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">生效日期</label>
                    <p className="text-sm">{new Date(rule.effectiveDate).toLocaleDateString()}</p>
                  </div>
                )}
                {rule.expiryDate && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">失效日期</label>
                    <p className="text-sm">{new Date(rule.expiryDate).toLocaleDateString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 执行统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                执行统计
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{rule.executionCount || 0}</p>
                  <p className="text-sm text-muted-foreground">总执行次数</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {rule.lastExecutedAt ? '已执行' : '未执行'}
                  </p>
                  <p className="text-sm text-muted-foreground">执行状态</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">0</p>
                  <p className="text-sm text-muted-foreground">检测异常</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">-</p>
                  <p className="text-sm text-muted-foreground">平均耗时</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RuleTypeIcon className={`h-5 w-5 ${getRuleTypeColor(rule.ruleType)}`} />
                规则内容
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">规则描述</label>
                  <div className="mt-2 p-3 bg-gray-50 rounded-md">
                    <p className="text-sm">{rule.ruleContent || '暂无内容'}</p>
                  </div>
                </div>

                {rule.ruleType === 'SQL' && rule.ruleSql && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">SQL内容</label>
                    <div className="mt-2 p-3 bg-gray-50 rounded-md">
                      <pre className="text-sm font-mono whitespace-pre-wrap">{rule.ruleSql}</pre>
                    </div>
                  </div>
                )}

                {rule.ruleType === 'DSL' && rule.ruleDsl && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">DSL内容</label>
                    <div className="mt-2 p-3 bg-gray-50 rounded-md">
                      <pre className="text-sm font-mono whitespace-pre-wrap">{rule.ruleDsl}</pre>
                    </div>
                  </div>
                )}

                {rule.ruleType === 'JAVASCRIPT' && rule.ruleContent && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">JavaScript内容</label>
                    <div className="mt-2 p-3 bg-gray-50 rounded-md">
                      <pre className="text-sm font-mono whitespace-pre-wrap">{rule.ruleContent}</pre>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="execution" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                执行历史
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无执行历史记录</p>
                <p className="text-sm">规则执行后将在此显示历史记录</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                配置信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">创建人</label>
                  <p className="text-sm">{rule.createdBy || '系统'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">更新人</label>
                  <p className="text-sm">{rule.updatedBy || '系统'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">是否删除</label>
                  <Badge variant={rule.isDeleted ? 'destructive' : 'default'}>
                    {rule.isDeleted ? '已删除' : '正常'}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">规则状态</label>
                  <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                    {rule.isActive ? '启用' : '禁用'}
                  </Badge>
                </div>
              </div>


            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
