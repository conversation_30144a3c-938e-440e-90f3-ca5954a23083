'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { RuleStatistics } from '@/types/supervision-rule'
import { cn } from '@/lib/utils'

import {
  Shield,
  Activity,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Clock,
  Target,
  Zap,
  BarChart3
} from 'lucide-react'

interface RuleStatisticsProps {
  statistics: RuleStatistics
  isLoading?: boolean
}

// 数据适配器：将后端数据格式转换为前端期望的格式
interface AdaptedStatistics {
  totalRules: number
  activeRules: number
  todayExecutions: number
  successfulExecutions: number
  failedExecutions: number
  anomaliesDetected: number
  averageExecutionTime: number
}

function adaptStatistics(statistics: RuleStatistics): AdaptedStatistics {
  return {
    totalRules: statistics.totalRules,
    activeRules: statistics.activeRules,
    todayExecutions: statistics.executionStats.totalExecutions,
    successfulExecutions: statistics.executionStats.successfulExecutions,
    failedExecutions: statistics.executionStats.failedExecutions,
    anomaliesDetected: statistics.resultStats.violationResults,
    averageExecutionTime: Math.round(statistics.executionStats.avgExecutionTime * 100) / 100
  }
}

export function RuleStatisticsCards({ statistics, isLoading = false }: RuleStatisticsProps) {
  if (isLoading) {
    return (
      <>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </>
    )
  }

  // 使用适配器转换数据格式
  const adaptedStats = adaptStatistics(statistics)

  const activeRate = adaptedStats.totalRules > 0
    ? (adaptedStats.activeRules / adaptedStats.totalRules) * 100
    : 0

  const successRate = adaptedStats.todayExecutions > 0
    ? (adaptedStats.successfulExecutions / adaptedStats.todayExecutions) * 100
    : 0

  return (
    <>
      {/* 总规则数卡片 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总规则数</CardTitle>
          <Shield className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{adaptedStats.totalRules}</div>
          <p className="text-xs text-muted-foreground">
            活跃 {adaptedStats.activeRules} · 禁用 {adaptedStats.totalRules - adaptedStats.activeRules}
          </p>
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs mb-1">
              <span className="text-muted-foreground">活跃率</span>
              <span className="font-medium">{activeRate.toFixed(1)}%</span>
            </div>
            <Progress value={activeRate} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* 今日执行卡片 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">今日执行</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{adaptedStats.todayExecutions.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            成功 {adaptedStats.successfulExecutions} · 失败 {adaptedStats.failedExecutions}
          </p>
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs mb-1">
              <span className="text-muted-foreground">成功率</span>
              <span className="font-medium">{successRate.toFixed(1)}%</span>
            </div>
            <Progress value={successRate} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* 异常检测卡片 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">异常检测</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{adaptedStats.anomaliesDetected}</div>
          <p className="text-xs text-muted-foreground">
            本月检测到的异常数量
          </p>
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs mb-1">
              <span className="text-muted-foreground">风险等级</span>
              <span className="font-medium">
                {adaptedStats.anomaliesDetected > 100 ? '高' : adaptedStats.anomaliesDetected > 50 ? '中' : '低'}
              </span>
            </div>
            <Progress
              value={Math.min((adaptedStats.anomaliesDetected / 200) * 100, 100)}
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>

      {/* 平均响应卡片 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">平均响应</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{adaptedStats.averageExecutionTime}ms</div>
          <p className="text-xs text-muted-foreground">
            规则执行平均响应时间
          </p>
          <div className="mt-2">
            <div className="flex items-center justify-between text-xs mb-1">
              <span className="text-muted-foreground">性能等级</span>
              <span className="font-medium">
                {adaptedStats.averageExecutionTime < 500 ? '优秀' : adaptedStats.averageExecutionTime < 1000 ? '良好' : '需优化'}
              </span>
            </div>
            <Progress
              value={Math.max(100 - (adaptedStats.averageExecutionTime / 20), 10)}
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>
    </>
  )
}

// 详细统计组件
export function DetailedRuleStatistics({ statistics }: RuleStatisticsProps) {
  const categoryStats = [
    { name: '费用控制', count: Math.floor(statistics.totalRules * 0.4), color: 'bg-red-500' },
    { name: '欺诈检测', count: Math.floor(statistics.totalRules * 0.3), color: 'bg-blue-500' },
    { name: '合规检查', count: Math.floor(statistics.totalRules * 0.2), color: 'bg-green-500' },
    { name: '质量保证', count: Math.floor(statistics.totalRules * 0.1), color: 'bg-yellow-500' }
  ]

  const typeStats = [
    { name: 'SQL规则', count: Math.floor(statistics.totalRules * 0.5), color: 'bg-purple-500' },
    { name: 'DSL规则', count: Math.floor(statistics.totalRules * 0.3), color: 'bg-indigo-500' },
    { name: 'JavaScript规则', count: Math.floor(statistics.totalRules * 0.2), color: 'bg-pink-500' }
  ]

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 规则分类统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            规则分类分布
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {categoryStats.map((category, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{category.name}</span>
                <Badge variant="outline">{category.count}</Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${category.color}`}
                  style={{ width: `${(category.count / statistics.totalRules) * 100}%` }}
                />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 规则类型统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            规则类型分布
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {typeStats.map((type, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{type.name}</span>
                <Badge variant="outline">{type.count}</Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${type.color}`}
                  style={{ width: `${(type.count / statistics.totalRules) * 100}%` }}
                />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}

// 执行趋势组件
export function ExecutionTrendCard({ statistics }: RuleStatisticsProps) {
  // 模拟最近7天的执行数据
  const trendData = Array.from({ length: 7 }, (_, i) => ({
    date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
    executions: Math.floor(Math.random() * 100) + 50,
    success: Math.floor(Math.random() * 80) + 40,
    failed: Math.floor(Math.random() * 20) + 5
  }))

  const maxExecutions = Math.max(...trendData.map(d => d.executions))

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          执行趋势 (最近7天)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {trendData.map((day, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">{day.date}</span>
                <div className="flex items-center gap-4">
                  <span className="text-green-600">成功: {day.success}</span>
                  <span className="text-red-600">失败: {day.failed}</span>
                  <span className="font-medium">总计: {day.executions}</span>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 relative">
                <div 
                  className="h-2 bg-green-500 rounded-full absolute left-0"
                  style={{ width: `${(day.success / maxExecutions) * 100}%` }}
                />
                <div 
                  className="h-2 bg-red-500 rounded-full absolute"
                  style={{ 
                    left: `${(day.success / maxExecutions) * 100}%`,
                    width: `${(day.failed / maxExecutions) * 100}%` 
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
