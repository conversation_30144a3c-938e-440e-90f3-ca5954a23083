'use client'

import React, { useState, useMemo } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { OptimizedTable } from '@/components/ui/optimized-table'
import { useToast } from '@/lib/toast'
import { SupervisionRule } from '@/types/supervision-rule'
import {
  UnifiedActionButtons,
  UnifiedStatusBadge,
  UnifiedBatchToolbar
} from '@/components/common/unified-design-system'
import {
  RuleStatusBadge,
  RuleTypeBadge,
  RuleCategoryBadge,
  RulePriority
} from '@/components/supervision-rules/rule-status-components'
import {
  Shield,
  Play,
  Edit,
  Eye,
  Trash2,
  Copy,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Loader2,
  Zap,
  Code,
  Activity
} from 'lucide-react'

interface RuleListProps {
  rules: SupervisionRule[]
  isLoading?: boolean
  selectedRules: number[]
  onRuleSelect: (ruleIds: number[]) => void
  onRuleToggle: (ruleId: number, isActive: boolean) => void
  onRuleExecute: (ruleIds: number[]) => void
  onRuleEdit: (ruleId: number) => void
  onRuleView: (ruleId: number) => void
  onRuleDelete: (ruleId: number) => void
  onRuleCopy: (ruleId: number) => void
}

export function RuleList({
  rules,
  isLoading = false,
  selectedRules,
  onRuleSelect,
  onRuleToggle,
  onRuleExecute,
  onRuleEdit,
  onRuleView,
  onRuleDelete,
  onRuleCopy
}: RuleListProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [executingRules, setExecutingRules] = useState<Set<number>>(new Set())

  // 批量操作配置
  const batchActions = [
    {
      label: '批量执行',
      icon: Zap,
      onClick: () => onRuleExecute(selectedRules),
      variant: 'default' as const,
      disabled: selectedRules.length === 0
    },
    {
      label: '批量编辑',
      icon: Edit,
      onClick: () => {
        // 批量编辑逻辑
        console.log('批量编辑:', selectedRules)
      },
      variant: 'outline' as const,
      disabled: selectedRules.length === 0
    },
    {
      label: '批量删除',
      icon: Trash2,
      onClick: () => {
        // 批量删除逻辑
        if (confirm(`确定要删除选中的 ${selectedRules.length} 个规则吗？`)) {
          selectedRules.forEach(ruleId => onRuleDelete(ruleId))
        }
      },
      variant: 'destructive' as const,
      disabled: selectedRules.length === 0
    }
  ]

  // 处理单个规则执行
  const handleSingleRuleExecute = async (ruleId: number) => {
    setExecutingRules(prev => new Set([...prev, ruleId]))
    try {
      await onRuleExecute([ruleId])
    } finally {
      setExecutingRules(prev => {
        const newSet = new Set(prev)
        newSet.delete(ruleId)
        return newSet
      })
    }
  }

  // 表格列定义
  const columns = [
    {
      key: 'select',
      title: '',
      width: 50,
      render: (rule: SupervisionRule) => (
        <Checkbox
          checked={selectedRules.includes(rule.id)}
          onCheckedChange={(checked) => {
            if (checked) {
              onRuleSelect([...selectedRules, rule.id])
            } else {
              onRuleSelect(selectedRules.filter(id => id !== rule.id))
            }
          }}
        />
      )
    },
    {
      key: 'ruleInfo',
      title: '规则信息',
      width: 300,
      sortable: true,
      render: (rule: SupervisionRule) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-blue-500" />
            <span className="font-medium">{rule.ruleName}</span>
          </div>
          <div className="text-sm text-muted-foreground">{rule.ruleCode}</div>
          {rule.description && (
            <div className="text-xs text-muted-foreground line-clamp-2 max-w-xs">
              {rule.description}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'typeCategory',
      title: '类型分类',
      width: 180,
      filterable: false,
      render: (rule: SupervisionRule) => (
        <div className="space-y-2">
          <RuleTypeBadge type={rule.ruleType} />
          <RuleCategoryBadge
            category={rule.ruleCategory as 'COST_CONTROL' | 'FRAUD_DETECTION' | 'COMPLIANCE_CHECK' | 'QUALITY_ASSURANCE'}
          />
        </div>
      )
    },
    {
      key: 'priority',
      title: '优先级',
      width: 100,
      sortable: true,
      render: (rule: SupervisionRule) => (
        <RulePriority level={rule.priorityLevel || 5} />
      )
    },
    {
      key: 'status',
      title: '状态',
      width: 120,
      render: (rule: SupervisionRule) => (
        <div className="flex items-center gap-2">
          <RuleStatusBadge isActive={rule.isActive} />
          <Switch
            checked={rule.isActive}
            onCheckedChange={(checked) => onRuleToggle(rule.id, checked)}

          />
        </div>
      )
    },
    {
      key: 'execution',
      title: '执行统计',
      width: 150,
      render: (rule: SupervisionRule) => (
        <div className="text-sm space-y-1">
          <div>执行次数: {rule.executionCount || 0}</div>
          <div className="text-xs text-muted-foreground">
            {rule.lastExecutedAt ? 
              `最后执行: ${new Date(rule.lastExecutedAt).toLocaleDateString()}` : 
              '未执行'
            }
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      title: '操作',
      width: 200,
      className: 'text-right',
      render: (rule: SupervisionRule) => (
        <UnifiedActionButtons
          actions={[
            {
              label: '查看详情',
              icon: Eye,
              onClick: () => onRuleView(rule.id),
              variant: 'ghost'
            },
            {
              label: '编辑规则',
              icon: Edit,
              onClick: () => onRuleEdit(rule.id),
              variant: 'ghost'
            },
            {
              label: '执行规则',
              icon: executingRules.has(rule.id) ? Loader2 : Play,
              onClick: () => handleSingleRuleExecute(rule.id),
              variant: 'ghost',
              disabled: !rule.isActive || executingRules.has(rule.id)
            },
            {
              label: '复制规则',
              icon: Copy,
              onClick: () => onRuleCopy(rule.id),
              variant: 'ghost'
            },
            {
              label: '删除规则',
              icon: Trash2,
              onClick: () => onRuleDelete(rule.id),
              variant: 'destructive'
            }
          ]}
          className="justify-end"
        />
      )
    }
  ]

  return (
    <div className="space-y-4">
      {/* 批量操作工具栏 */}
      <UnifiedBatchToolbar
        selectedCount={selectedRules.length}
        onClearSelection={() => onRuleSelect([])}
        actions={batchActions}
      />

      {/* 规则列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            监管规则列表
            <Badge variant="outline" className="ml-auto">
              {rules.length} 条规则
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <OptimizedTable
            data={rules}
            columns={columns}
            rowHeight={80}
            maxHeight={600}
            searchable={true}
            sortable={true}
            filterable={true}
            onRowClick={(rule) => onRuleView(rule.id)}
            loading={isLoading}
            emptyMessage="暂无监管规则"
            virtualScrollThreshold={50}
            className="w-full"

          />
        </CardContent>
      </Card>
    </div>
  )
}
