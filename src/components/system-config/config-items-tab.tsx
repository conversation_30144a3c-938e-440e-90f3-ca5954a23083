'use client'

/**
 * 配置项管理标签页
 * 
 * @description 管理系统配置项的增删改查
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Settings,
  CheckCircle,
  XCircle,
  Filter
} from 'lucide-react'
import { toast } from 'sonner'

interface ConfigItem {
  id: number
  categoryId: number
  configKey: string
  configValue: string
  configLabel: string
  description: string
  dataType: string
  isRequired: boolean
  sortOrder: number
  isActive: boolean
  categoryCode: string
  categoryName: string
  createdAt: string
  updatedAt: string
}

interface ConfigCategory {
  id: number
  categoryCode: string
  categoryName: string
}

interface ConfigItemsTabProps {
  onStatsChange?: () => void
}

export function ConfigItemsTab({ onStatsChange }: ConfigItemsTabProps) {
  const [items, setItems] = useState<ConfigItem[]>([])
  const [categories, setCategories] = useState<ConfigCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<ConfigItem | null>(null)
  const [formData, setFormData] = useState({
    categoryId: 0,
    configKey: '',
    configValue: '',
    configLabel: '',
    description: '',
    dataType: 'STRING',
    isRequired: true,
    sortOrder: 0,
    isActive: true
  })

  /**
   * 获取配置项列表
   */
  const fetchItems = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        pageSize: '100',
        ...(selectedCategory && { categoryCode: selectedCategory })
      })
      
      const response = await fetch(`/api/system-config/items?${params}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setItems(data.data.items || [])
        }
      }
    } catch (error) {
      toast.error('获取配置项失败')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 获取配置分类列表
   */
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/system-config/categories?pageSize=100')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setCategories(data.data.categories || [])
        }
      }
    } catch (error) {
      console.error('获取配置分类失败:', error)
    }
  }

  /**
   * 创建配置项
   */
  const createItem = async () => {
    try {
      const response = await fetch('/api/system-config/items', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置项创建成功')
          setIsCreateDialogOpen(false)
          resetForm()
          await fetchItems()
          onStatsChange?.()
        } else {
          toast.error(data.message || '创建失败')
        }
      }
    } catch (error) {
      toast.error('创建配置项时发生错误')
    }
  }

  /**
   * 更新配置项
   */
  const updateItem = async () => {
    if (!editingItem) return

    try {
      const response = await fetch(`/api/system-config/items/${editingItem.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          changeReason: '通过管理界面更新配置项'
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置项更新成功')
          setEditingItem(null)
          resetForm()
          await fetchItems()
          onStatsChange?.()
        } else {
          toast.error(data.message || '更新失败')
        }
      }
    } catch (error) {
      toast.error('更新配置项时发生错误')
    }
  }

  /**
   * 删除配置项
   */
  const deleteItem = async (id: number) => {
    if (!confirm('确定要删除这个配置项吗？此操作不可恢复，可能影响系统功能。')) {
      return
    }

    try {
      const response = await fetch(`/api/system-config/items/${id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          changeReason: '通过管理界面删除配置项'
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置项删除成功')
          await fetchItems()
          onStatsChange?.()
        } else {
          toast.error(data.message || '删除失败')
        }
      }
    } catch (error) {
      toast.error('删除配置项时发生错误')
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    setFormData({
      categoryId: 0,
      configKey: '',
      configValue: '',
      configLabel: '',
      description: '',
      dataType: 'STRING',
      isRequired: true,
      sortOrder: 0,
      isActive: true
    })
  }

  /**
   * 开始编辑
   */
  const startEdit = (item: ConfigItem) => {
    setEditingItem(item)
    setFormData({
      categoryId: item.categoryId,
      configKey: item.configKey,
      configValue: item.configValue,
      configLabel: item.configLabel,
      description: item.description,
      dataType: item.dataType,
      isRequired: item.isRequired,
      sortOrder: item.sortOrder,
      isActive: item.isActive
    })
  }

  /**
   * 过滤配置项
   */
  const filteredItems = items.filter(item =>
    item.configLabel.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.configKey.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.configValue.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    fetchItems()
  }, [selectedCategory])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              配置项管理
            </CardTitle>
            <CardDescription>
              管理具体的配置项，这些配置项用于API的动态验证
            </CardDescription>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                新建配置项
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>创建配置项</DialogTitle>
                <DialogDescription>
                  创建新的配置项，用于API的动态验证
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="categoryId">所属分类</Label>
                  <Select
                    value={formData.categoryId.toString()}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: parseInt(value) }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName} ({category.categoryCode})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="configKey">配置键</Label>
                  <Input
                    id="configKey"
                    placeholder="例如：MALE"
                    value={formData.configKey}
                    onChange={(e) => setFormData(prev => ({ ...prev, configKey: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="configValue">配置值</Label>
                  <Input
                    id="configValue"
                    placeholder="例如：MALE"
                    value={formData.configValue}
                    onChange={(e) => setFormData(prev => ({ ...prev, configValue: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="configLabel">显示标签</Label>
                  <Input
                    id="configLabel"
                    placeholder="例如：男性"
                    value={formData.configLabel}
                    onChange={(e) => setFormData(prev => ({ ...prev, configLabel: e.target.value }))}
                  />
                </div>
                
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    placeholder="配置项的详细描述"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dataType">数据类型</Label>
                  <Select
                    value={formData.dataType}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, dataType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="STRING">字符串</SelectItem>
                      <SelectItem value="NUMBER">数字</SelectItem>
                      <SelectItem value="BOOLEAN">布尔值</SelectItem>
                      <SelectItem value="JSON">JSON对象</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="sortOrder">排序号</Label>
                  <Input
                    id="sortOrder"
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="isRequired">是否必需</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isRequired"
                      checked={formData.isRequired}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRequired: checked }))}
                    />
                    <Label htmlFor="isRequired">
                      {formData.isRequired ? '必需' : '可选'}
                    </Label>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="isActive">启用状态</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                    />
                    <Label htmlFor="isActive">
                      {formData.isActive ? '启用' : '禁用'}
                    </Label>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={createItem}>
                  创建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 搜索和过滤栏 */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 flex-1">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索配置项..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部分类</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.categoryCode} value={category.categoryCode}>
                    {category.categoryName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 配置项列表 */}
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>分类</TableHead>
                <TableHead>配置键</TableHead>
                <TableHead>配置值</TableHead>
                <TableHead>显示标签</TableHead>
                <TableHead>数据类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>排序</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : filteredItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    {searchTerm || selectedCategory ? '没有找到匹配的配置项' : '暂无配置项'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Badge variant="outline">
                        {item.categoryName}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.configKey}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.configValue}
                    </TableCell>
                    <TableCell className="font-medium">
                      {item.configLabel}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {item.dataType}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={item.isActive ? 'default' : 'secondary'}>
                        {item.isActive ? (
                          <><CheckCircle className="h-3 w-3 mr-1" />启用</>
                        ) : (
                          <><XCircle className="h-3 w-3 mr-1" />禁用</>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.sortOrder}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => startEdit(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* 编辑对话框 */}
      <Dialog open={!!editingItem} onOpenChange={(open) => !open && setEditingItem(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑配置项</DialogTitle>
            <DialogDescription>
              修改配置项的信息，变更会记录到历史中
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-categoryId">所属分类</Label>
              <Select
                value={formData.categoryId.toString()}
                onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.categoryName} ({category.categoryCode})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-configKey">配置键</Label>
              <Input
                id="edit-configKey"
                value={formData.configKey}
                onChange={(e) => setFormData(prev => ({ ...prev, configKey: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-configValue">配置值</Label>
              <Input
                id="edit-configValue"
                value={formData.configValue}
                onChange={(e) => setFormData(prev => ({ ...prev, configValue: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-configLabel">显示标签</Label>
              <Input
                id="edit-configLabel"
                value={formData.configLabel}
                onChange={(e) => setFormData(prev => ({ ...prev, configLabel: e.target.value }))}
              />
            </div>
            
            <div className="col-span-2 space-y-2">
              <Label htmlFor="edit-description">描述</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-dataType">数据类型</Label>
              <Select
                value={formData.dataType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, dataType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="STRING">字符串</SelectItem>
                  <SelectItem value="NUMBER">数字</SelectItem>
                  <SelectItem value="BOOLEAN">布尔值</SelectItem>
                  <SelectItem value="JSON">JSON对象</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-sortOrder">排序号</Label>
              <Input
                id="edit-sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-isRequired">是否必需</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isRequired"
                  checked={formData.isRequired}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRequired: checked }))}
                />
                <Label htmlFor="edit-isRequired">
                  {formData.isRequired ? '必需' : '可选'}
                </Label>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-isActive">启用状态</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
                <Label htmlFor="edit-isActive">
                  {formData.isActive ? '启用' : '禁用'}
                </Label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingItem(null)}>
              取消
            </Button>
            <Button onClick={updateItem}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
