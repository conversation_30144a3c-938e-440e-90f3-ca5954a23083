'use client'

/**
 * 配置预览标签页
 * 
 * @description 实时预览当前系统配置和其对API验证的影响
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Info, 
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Code,
  Database,
  Eye,
  Download
} from 'lucide-react'
import { toast } from 'sonner'

interface DynamicConfig {
  [categoryCode: string]: string[]
}

interface ConfigPreview {
  categoryCode: string
  categoryName: string
  items: Array<{
    key: string
    value: string
    label: string
    isActive: boolean
  }>
  affectedAPIs: string[]
}

export function ConfigPreviewTab() {
  const [dynamicConfig, setDynamicConfig] = useState<DynamicConfig>({})
  const [configPreviews, setConfigPreviews] = useState<ConfigPreview[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<string>('')

  /**
   * 获取动态配置
   */
  const fetchDynamicConfig = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/system-config/dynamic')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setDynamicConfig(data.data.configs || {})
          setLastRefresh(new Date().toLocaleString())
        }
      }
    } catch (error) {
      toast.error('获取动态配置失败')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 获取配置预览详情
   */
  const fetchConfigPreviews = async () => {
    try {
      const response = await fetch('/api/system-config/preview')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setConfigPreviews(data.data.previews || [])
        }
      }
    } catch (error) {
      console.error('获取配置预览失败:', error)
    }
  }

  /**
   * 刷新配置缓存
   */
  const refreshCache = async () => {
    try {
      const response = await fetch('/api/system-config/dynamic', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置缓存刷新成功')
          await fetchDynamicConfig()
        } else {
          toast.error('配置缓存刷新失败')
        }
      }
    } catch (error) {
      toast.error('刷新缓存时发生错误')
    }
  }

  /**
   * 导出当前配置
   */
  const exportCurrentConfig = () => {
    const configData = {
      timestamp: new Date().toISOString(),
      configs: dynamicConfig,
      metadata: {
        totalCategories: Object.keys(dynamicConfig).length,
        totalItems: Object.values(dynamicConfig).reduce((sum, items) => sum + items.length, 0)
      }
    }

    const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `current-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    toast.success('当前配置导出成功')
  }

  /**
   * 获取API影响说明
   */
  const getAPIImpact = (categoryCode: string) => {
    const impactMap: Record<string, string[]> = {
      MEDICAL_CATEGORY: ['医疗案例API', '案例查询API', '案例统计API'],
      CASE_TYPE: ['医疗案例API', '案例分类API'],
      GENDER_TYPE: ['医疗案例API', '用户管理API', '患者信息API'],
      RULE_TYPE: ['监管规则API', '规则执行API'],
      RULE_CATEGORY: ['监管规则API', '规则分类API', '规则统计API'],
      SEVERITY_LEVEL: ['监管规则API', '告警API', '风险评估API'],
      DOCUMENT_TYPE: ['知识库文档API', '文档分类API'],
      DOCUMENT_STATUS: ['知识库文档API', '文档审核API', '发布API']
    }
    return impactMap[categoryCode] || ['相关API']
  }

  /**
   * 获取配置状态
   */
  const getConfigStatus = (categoryCode: string) => {
    const items = dynamicConfig[categoryCode] || []
    if (items.length === 0) {
      return { status: 'warning', message: '无配置项', color: 'text-yellow-600' }
    }
    if (items.length < 2) {
      return { status: 'warning', message: '配置项较少', color: 'text-yellow-600' }
    }
    return { status: 'success', message: '配置正常', color: 'text-green-600' }
  }

  useEffect(() => {
    fetchDynamicConfig()
    fetchConfigPreviews()
  }, [])

  return (
    <div className="space-y-6">
      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">配置分类</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(dynamicConfig).length}</div>
            <p className="text-xs text-muted-foreground">
              当前生效的配置分类
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">配置项总数</CardTitle>
            <Info className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(dynamicConfig).reduce((sum, items) => sum + items.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              所有分类的配置项数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">最后刷新</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium">{lastRefresh || '未知'}</div>
            <p className="text-xs text-muted-foreground">
              配置缓存更新时间
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="选择配置分类查看详情" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部分类</SelectItem>
              {Object.keys(dynamicConfig).map((categoryCode) => (
                <SelectItem key={categoryCode} value={categoryCode}>
                  {categoryCode}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshCache}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            刷新缓存
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={exportCurrentConfig}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            导出配置
          </Button>
        </div>
      </div>

      {/* 主要内容 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">配置概览</TabsTrigger>
          <TabsTrigger value="details">详细配置</TabsTrigger>
          <TabsTrigger value="impact">影响分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                配置概览
              </CardTitle>
              <CardDescription>
                查看所有配置分类的当前状态和配置项数量
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(dynamicConfig).map(([categoryCode, items]) => {
                  const status = getConfigStatus(categoryCode)
                  return (
                    <Card key={categoryCode} className="border-l-4 border-l-blue-500">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{categoryCode}</CardTitle>
                          <Badge variant="outline">{items.length} 项</Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          <div className={`flex items-center gap-2 text-sm ${status.color}`}>
                            {status.status === 'success' ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              <AlertTriangle className="h-4 w-4" />
                            )}
                            {status.message}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            影响 {getAPIImpact(categoryCode).length} 个API
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                详细配置
              </CardTitle>
              <CardDescription>
                查看具体的配置项值，这些值用于API的动态验证
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedCategory ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">{selectedCategory}</h3>
                    <Badge variant="outline">
                      {dynamicConfig[selectedCategory]?.length || 0} 个配置项
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {(dynamicConfig[selectedCategory] || []).map((value, index) => (
                      <div
                        key={index}
                        className="p-3 bg-muted rounded-md font-mono text-sm"
                      >
                        {value}
                      </div>
                    ))}
                  </div>
                  
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      这些配置值用于 <strong>{getAPIImpact(selectedCategory).join('、')}</strong> 的动态验证。
                      修改这些值会立即影响相关API的验证规则。
                    </AlertDescription>
                  </Alert>
                </div>
              ) : (
                <div className="space-y-4">
                  {Object.entries(dynamicConfig).map(([categoryCode, items]) => (
                    <div key={categoryCode} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold">{categoryCode}</h3>
                        <Badge variant="outline">{items.length} 项</Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                        {items.map((value, index) => (
                          <div
                            key={index}
                            className="p-2 bg-muted rounded text-sm font-mono truncate"
                            title={value}
                          >
                            {value}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="impact" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                影响分析
              </CardTitle>
              <CardDescription>
                分析配置变更对系统API和业务功能的影响
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.keys(dynamicConfig).map((categoryCode) => {
                  const affectedAPIs = getAPIImpact(categoryCode)
                  const itemCount = dynamicConfig[categoryCode]?.length || 0
                  
                  return (
                    <div key={categoryCode} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold text-lg">{categoryCode}</h3>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{itemCount} 个配置项</Badge>
                          <Badge variant="secondary">{affectedAPIs.length} 个API</Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">影响的API:</h4>
                          <ul className="space-y-1">
                            {affectedAPIs.map((api, index) => (
                              <li key={index} className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="text-sm">{api}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium mb-2">风险评估:</h4>
                          <div className="space-y-2">
                            {itemCount === 0 && (
                              <Alert>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription className="text-sm">
                                  <strong>高风险:</strong> 无配置项，相关API验证将失败
                                </AlertDescription>
                              </Alert>
                            )}
                            {itemCount > 0 && itemCount < 2 && (
                              <Alert>
                                <Info className="h-4 w-4" />
                                <AlertDescription className="text-sm">
                                  <strong>中风险:</strong> 配置项较少，可能限制业务灵活性
                                </AlertDescription>
                              </Alert>
                            )}
                            {itemCount >= 2 && (
                              <Alert>
                                <CheckCircle className="h-4 w-4" />
                                <AlertDescription className="text-sm">
                                  <strong>低风险:</strong> 配置项充足，API验证正常
                                </AlertDescription>
                              </Alert>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
