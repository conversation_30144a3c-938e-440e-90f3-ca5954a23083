'use client'

/**
 * 配置分类管理标签页
 * 
 * @description 管理系统配置分类的增删改查
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Database,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface ConfigCategory {
  id: number
  categoryCode: string
  categoryName: string
  description: string
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  itemCount?: number
}

interface ConfigCategoriesTabProps {
  onStatsChange?: () => void
}

export function ConfigCategoriesTab({ onStatsChange }: ConfigCategoriesTabProps) {
  const [categories, setCategories] = useState<ConfigCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ConfigCategory | null>(null)
  const [formData, setFormData] = useState({
    categoryCode: '',
    categoryName: '',
    description: '',
    sortOrder: 0,
    isActive: true
  })

  /**
   * 获取配置分类列表
   */
  const fetchCategories = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/system-config/categories?pageSize=100')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setCategories(data.data.categories || [])
        }
      }
    } catch (error) {
      toast.error('获取配置分类失败')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 创建配置分类
   */
  const createCategory = async () => {
    try {
      const response = await fetch('/api/system-config/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置分类创建成功')
          setIsCreateDialogOpen(false)
          resetForm()
          await fetchCategories()
          onStatsChange?.()
        } else {
          toast.error(data.message || '创建失败')
        }
      }
    } catch (error) {
      toast.error('创建配置分类时发生错误')
    }
  }

  /**
   * 更新配置分类
   */
  const updateCategory = async () => {
    if (!editingCategory) return

    try {
      const response = await fetch(`/api/system-config/categories/${editingCategory.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置分类更新成功')
          setEditingCategory(null)
          resetForm()
          await fetchCategories()
          onStatsChange?.()
        } else {
          toast.error(data.message || '更新失败')
        }
      }
    } catch (error) {
      toast.error('更新配置分类时发生错误')
    }
  }

  /**
   * 删除配置分类
   */
  const deleteCategory = async (id: number) => {
    if (!confirm('确定要删除这个配置分类吗？此操作不可恢复。')) {
      return
    }

    try {
      const response = await fetch(`/api/system-config/categories/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置分类删除成功')
          await fetchCategories()
          onStatsChange?.()
        } else {
          toast.error(data.message || '删除失败')
        }
      }
    } catch (error) {
      toast.error('删除配置分类时发生错误')
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    setFormData({
      categoryCode: '',
      categoryName: '',
      description: '',
      sortOrder: 0,
      isActive: true
    })
  }

  /**
   * 开始编辑
   */
  const startEdit = (category: ConfigCategory) => {
    setEditingCategory(category)
    setFormData({
      categoryCode: category.categoryCode,
      categoryName: category.categoryName,
      description: category.description,
      sortOrder: category.sortOrder,
      isActive: category.isActive
    })
  }

  /**
   * 过滤分类
   */
  const filteredCategories = categories.filter(category =>
    category.categoryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.categoryCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  useEffect(() => {
    fetchCategories()
  }, [])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              配置分类管理
            </CardTitle>
            <CardDescription>
              管理系统配置的分类，每个分类包含多个相关的配置项
            </CardDescription>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                新建分类
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>创建配置分类</DialogTitle>
                <DialogDescription>
                  创建新的配置分类来组织相关的配置项
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="categoryCode">分类编码</Label>
                  <Input
                    id="categoryCode"
                    placeholder="例如：USER_SETTINGS"
                    value={formData.categoryCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, categoryCode: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="categoryName">分类名称</Label>
                  <Input
                    id="categoryName"
                    placeholder="例如：用户设置"
                    value={formData.categoryName}
                    onChange={(e) => setFormData(prev => ({ ...prev, categoryName: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    placeholder="分类的详细描述"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sortOrder">排序号</Label>
                    <Input
                      id="sortOrder"
                      type="number"
                      value={formData.sortOrder}
                      onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="isActive">启用状态</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isActive"
                        checked={formData.isActive}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                      />
                      <Label htmlFor="isActive">
                        {formData.isActive ? '启用' : '禁用'}
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={createCategory}>
                  创建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 搜索栏 */}
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索分类名称、编码或描述..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        {/* 分类列表 */}
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>分类编码</TableHead>
                <TableHead>分类名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>排序</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>配置项数量</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : filteredCategories.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    {searchTerm ? '没有找到匹配的分类' : '暂无配置分类'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredCategories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell className="font-mono text-sm">
                      {category.categoryCode}
                    </TableCell>
                    <TableCell className="font-medium">
                      {category.categoryName}
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {category.description}
                    </TableCell>
                    <TableCell>{category.sortOrder}</TableCell>
                    <TableCell>
                      <Badge variant={category.isActive ? 'default' : 'secondary'}>
                        {category.isActive ? (
                          <><CheckCircle className="h-3 w-3 mr-1" />启用</>
                        ) : (
                          <><XCircle className="h-3 w-3 mr-1" />禁用</>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {category.itemCount || 0} 项
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => startEdit(category)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteCategory(category.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* 编辑对话框 */}
      <Dialog open={!!editingCategory} onOpenChange={(open) => !open && setEditingCategory(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑配置分类</DialogTitle>
            <DialogDescription>
              修改配置分类的信息
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-categoryCode">分类编码</Label>
              <Input
                id="edit-categoryCode"
                value={formData.categoryCode}
                onChange={(e) => setFormData(prev => ({ ...prev, categoryCode: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-categoryName">分类名称</Label>
              <Input
                id="edit-categoryName"
                value={formData.categoryName}
                onChange={(e) => setFormData(prev => ({ ...prev, categoryName: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-description">描述</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-sortOrder">排序号</Label>
                <Input
                  id="edit-sortOrder"
                  type="number"
                  value={formData.sortOrder}
                  onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-isActive">启用状态</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                  <Label htmlFor="edit-isActive">
                    {formData.isActive ? '启用' : '禁用'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingCategory(null)}>
              取消
            </Button>
            <Button onClick={updateCategory}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
