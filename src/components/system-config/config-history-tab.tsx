'use client'

/**
 * 配置变更历史标签页
 * 
 * @description 显示系统配置的变更历史和审计日志
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  History, 
  Search,
  Filter,
  Eye,
  Calendar,
  User,
  FileText,
  ArrowRight
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface ConfigHistory {
  id: number
  configItemId: number
  oldValue: string | null
  newValue: string
  changeReason: string
  changeType: string
  createdAt: string
  createdBy: number
  configKey?: string
  configLabel?: string
  createdByName?: string
}

export function ConfigHistoryTab() {
  const [history, setHistory] = useState<ConfigHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('')
  const [selectedItem, setSelectedItem] = useState<ConfigHistory | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  /**
   * 获取配置变更历史
   */
  const fetchHistory = async (page = 1) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: '20',
        ...(selectedType && { changeType: selectedType }),
        ...(searchTerm && { search: searchTerm })
      })
      
      const response = await fetch(`/api/system-config/history?${params}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setHistory(data.data.history || [])
          setTotalPages(data.data.pagination?.totalPages || 1)
          setCurrentPage(page)
        }
      }
    } catch (error) {
      console.error('获取变更历史失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 格式化变更类型
   */
  const formatChangeType = (type: string) => {
    const typeMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      CREATE: { label: '创建', variant: 'default' },
      UPDATE: { label: '更新', variant: 'secondary' },
      DELETE: { label: '删除', variant: 'destructive' },
      ACTIVATE: { label: '启用', variant: 'default' },
      DEACTIVATE: { label: '禁用', variant: 'outline' }
    }
    return typeMap[type] || { label: type, variant: 'outline' }
  }

  /**
   * 格式化时间
   */
  const formatTime = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
    } catch {
      return dateString
    }
  }

  /**
   * 过滤历史记录
   */
  const filteredHistory = history.filter(item => {
    const matchesSearch = !searchTerm || 
      item.configKey?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.configLabel?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.changeReason?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.createdByName?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = !selectedType || item.changeType === selectedType
    
    return matchesSearch && matchesType
  })

  useEffect(() => {
    fetchHistory()
  }, [])

  useEffect(() => {
    if (currentPage === 1) {
      fetchHistory(1)
    } else {
      setCurrentPage(1)
      fetchHistory(1)
    }
  }, [searchTerm, selectedType])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              配置变更历史
            </CardTitle>
            <CardDescription>
              查看系统配置的所有变更记录和审计日志
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 搜索和过滤栏 */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 flex-1">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索配置项、变更原因或操作人..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="变更类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                <SelectItem value="CREATE">创建</SelectItem>
                <SelectItem value="UPDATE">更新</SelectItem>
                <SelectItem value="DELETE">删除</SelectItem>
                <SelectItem value="ACTIVATE">启用</SelectItem>
                <SelectItem value="DEACTIVATE">禁用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 历史记录列表 */}
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>配置项</TableHead>
                <TableHead>变更类型</TableHead>
                <TableHead>变更内容</TableHead>
                <TableHead>变更原因</TableHead>
                <TableHead>操作人</TableHead>
                <TableHead>操作时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : filteredHistory.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    {searchTerm || selectedType ? '没有找到匹配的变更记录' : '暂无变更历史'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredHistory.map((item) => {
                  const changeTypeInfo = formatChangeType(item.changeType)
                  return (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{item.configLabel}</div>
                          <div className="text-sm text-muted-foreground font-mono">
                            {item.configKey}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={changeTypeInfo.variant}>
                          {changeTypeInfo.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 text-sm">
                          {item.oldValue && (
                            <>
                              <span className="font-mono bg-red-50 text-red-700 px-2 py-1 rounded">
                                {item.oldValue}
                              </span>
                              <ArrowRight className="h-3 w-3 text-muted-foreground" />
                            </>
                          )}
                          <span className="font-mono bg-green-50 text-green-700 px-2 py-1 rounded">
                            {item.newValue}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-xs">
                        <div className="truncate" title={item.changeReason}>
                          {item.changeReason}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span>{item.createdByName || `用户${item.createdBy}`}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{formatTime(item.createdAt)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedItem(item)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                变更详情
                              </DialogTitle>
                              <DialogDescription>
                                查看配置项变更的详细信息
                              </DialogDescription>
                            </DialogHeader>
                            
                            {selectedItem && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium text-muted-foreground">配置项</label>
                                    <div className="mt-1">
                                      <div className="font-medium">{selectedItem.configLabel}</div>
                                      <div className="text-sm text-muted-foreground font-mono">
                                        {selectedItem.configKey}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <label className="text-sm font-medium text-muted-foreground">变更类型</label>
                                    <div className="mt-1">
                                      <Badge variant={formatChangeType(selectedItem.changeType).variant}>
                                        {formatChangeType(selectedItem.changeType).label}
                                      </Badge>
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <label className="text-sm font-medium text-muted-foreground">操作人</label>
                                    <div className="mt-1">
                                      {selectedItem.createdByName || `用户${selectedItem.createdBy}`}
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <label className="text-sm font-medium text-muted-foreground">操作时间</label>
                                    <div className="mt-1">
                                      {formatTime(selectedItem.createdAt)}
                                    </div>
                                  </div>
                                </div>
                                
                                <div>
                                  <label className="text-sm font-medium text-muted-foreground">变更内容</label>
                                  <div className="mt-1 space-y-2">
                                    {selectedItem.oldValue && (
                                      <div>
                                        <span className="text-sm text-muted-foreground">原值：</span>
                                        <span className="font-mono bg-red-50 text-red-700 px-2 py-1 rounded ml-2">
                                          {selectedItem.oldValue}
                                        </span>
                                      </div>
                                    )}
                                    <div>
                                      <span className="text-sm text-muted-foreground">新值：</span>
                                      <span className="font-mono bg-green-50 text-green-700 px-2 py-1 rounded ml-2">
                                        {selectedItem.newValue}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                
                                <div>
                                  <label className="text-sm font-medium text-muted-foreground">变更原因</label>
                                  <div className="mt-1 p-3 bg-muted rounded-md">
                                    {selectedItem.changeReason}
                                  </div>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  )
                })
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页控制 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              第 {currentPage} 页，共 {totalPages} 页
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchHistory(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchHistory(currentPage + 1)}
                disabled={currentPage >= totalPages}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
