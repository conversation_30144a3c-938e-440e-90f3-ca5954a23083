"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  Filter,
  X,
  Calendar,
  DollarSign,
  User,
  Hospital,
  ChevronDown,
  ChevronRight,
  RotateCcw,
  Settings2
} from 'lucide-react'

export interface FilterParams {
  caseType?: string
  medicalCategory?: string
  patientGender?: string
  hospitalName?: string
  admissionDateStart?: string
  admissionDateEnd?: string
  dischargeDateStart?: string
  dischargeDateEnd?: string
  totalCostMin?: number
  totalCostMax?: number
  patientAgeMin?: number
  patientAgeMax?: number
}

interface EnhancedFilterProps {
  filters: FilterParams
  onFiltersChange: (filters: FilterParams) => void
  onReset: () => void
}

// 筛选配置
const FILTER_CONFIGS = {
  caseType: {
    label: '案例类型',
    icon: Hospital,
    type: 'select' as const,
    options: [
      { value: 'INPATIENT', label: '住院' },
      { value: 'OUTPATIENT', label: '门诊' }
    ]
  },
  patientGender: {
    label: '患者性别',
    icon: User,
    type: 'select' as const,
    options: [
      { value: 'MALE', label: '男' },
      { value: 'FEMALE', label: '女' },
      { value: 'OTHER', label: '其他' }
    ]
  },
  hospitalName: {
    label: '医院名称',
    icon: Hospital,
    type: 'search' as const,
    placeholder: '搜索医院名称...'
  },
  medicalCategory: {
    label: '医疗类别',
    icon: Hospital,
    type: 'select' as const,
    options: [
      { value: '异地住院', label: '异地住院' },
      { value: '急诊住院', label: '急诊住院' },
      { value: '普通住院', label: '普通住院' },
      { value: '门诊慢特病', label: '门诊慢特病' },
      { value: '转外诊治住院', label: '转外诊治住院' },
      { value: '生育住院', label: '生育住院' },
      { value: '统筹区内转院', label: '统筹区内转院' },
      { value: '住院前急诊', label: '住院前急诊' },
      { value: '特药购药', label: '特药购药' },
      { value: '急诊转住院', label: '急诊转住院' },
      { value: '普通门诊', label: '普通门诊' },
      { value: '药店购慢特病药', label: '药店购慢特病药' },
      { value: '定点药店购药', label: '定点药店购药' },
      { value: '村卫门诊', label: '村卫门诊' },
      { value: '特药门诊', label: '特药门诊' },
      { value: '外伤住院', label: '外伤住院' },
      { value: '日间手术', label: '日间手术' },
      { value: '急诊', label: '急诊' },
      { value: '急诊2级危重', label: '急诊2级危重' },
      { value: '急诊3级急症', label: '急诊3级急症' },
      { value: '急诊1级濒危', label: '急诊1级濒危' },
      { value: '新冠门诊', label: '新冠门诊' },
      { value: '4级非急症', label: '4级非急症' },
      { value: '辅助生殖门诊', label: '辅助生殖门诊' },
      { value: '急诊(死亡)', label: '急诊(死亡)' },
      { value: '家庭医生签约', label: '家庭医生签约' }
    ]
  }
}





export function EnhancedFilter({ filters, onFiltersChange, onReset }: EnhancedFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    range: false,
    date: false
  })

  // 获取活跃筛选条件数量
  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value =>
      value !== undefined && value !== '' && value !== null
    ).length
  }

  // 更新筛选条件（立即应用）
  const updateFilter = (key: keyof FilterParams, value: string | number | undefined) => {
    const finalValue = value === '' ? undefined : value
    onFiltersChange({
      ...filters,
      [key]: finalValue
    })
  }

  // 重置筛选条件
  const handleReset = () => {
    onReset()
  }

  // 切换展开状态
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="relative gap-2"
        >
          <Filter className="h-4 w-4" />
          筛选
          {activeFiltersCount > 0 && (
            <Badge
              variant="secondary"
              className="h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent
        side="right"
        className="w-[400px] sm:w-[540px] flex flex-col h-full max-h-screen overflow-hidden"
      >
        <SheetHeader className="space-y-3 flex-shrink-0 pb-4">
          <SheetTitle className="flex items-center gap-2 text-lg">
            <Settings2 className="h-5 w-5" />
            高级筛选
          </SheetTitle>
          <SheetDescription className="text-sm text-muted-foreground">
            设置筛选条件来精确查找您需要的医疗案例
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 flex flex-col min-h-0 overflow-hidden py-6">
          {/* 活跃筛选条件显示 */}
          {activeFiltersCount > 0 && (
            <div className="space-y-3 flex-shrink-0">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">已选筛选条件</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                  className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
                >
                  <RotateCcw className="h-3 w-3 mr-1" />
                  清除全部
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(filters).map(([key, value]) => {
                  if (value === undefined || value === '' || value === null) return null
                  const config = FILTER_CONFIGS[key as keyof typeof FILTER_CONFIGS]
                  if (!config) return null

                  let displayValue = value
                  if (config.type === 'select' && config.options) {
                    const option = config.options.find(opt => opt.value === value)
                    displayValue = option?.label || value
                  }

                  return (
                    <Badge
                      key={key}
                      variant="secondary"
                      className="gap-1 pr-1"
                    >
                      <span className="text-xs">{config.label}: {displayValue}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto w-auto p-0.5 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => updateFilter(key as keyof FilterParams, undefined)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  )
                })}
              </div>
              <Separator />
            </div>
          )}

          {/* 筛选条件列表 - 可滚动区域 */}
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-full pr-4">
            <div className="space-y-4">
              {/* 基础筛选 */}
              <Collapsible
                open={expandedSections.basic}
                onOpenChange={() => toggleSection('basic')}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-between p-0 h-auto font-medium"
                  >
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      <span>基础筛选</span>
                    </div>
                    {expandedSections.basic ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-3 mt-3">
                  {Object.entries(FILTER_CONFIGS).map(([key, config]) => {
                    const Icon = config.icon
                    const currentValue = filters[key as keyof FilterParams]

                    return (
                      <div key={key} className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-2">
                          <Icon className="h-4 w-4 text-muted-foreground" />
                          {config.label}
                        </Label>
                        {config.type === 'select' && config.options ? (
                          <Select
                            value={currentValue as string || ''}
                            onValueChange={(value) => updateFilter(key as keyof FilterParams, value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={`选择${config.label}`} />
                            </SelectTrigger>
                            <SelectContent>
                              {config.options.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <Input
                            placeholder={config.type === 'search' ? config.placeholder : `输入${config.label}`}
                            value={currentValue as string || ''}
                            onChange={(e) => updateFilter(key as keyof FilterParams, e.target.value)}
                          />
                        )}
                      </div>
                    )
                  })}
                </CollapsibleContent>
              </Collapsible>

              {/* 数值范围筛选 */}
              <Collapsible
                open={expandedSections.range}
                onOpenChange={() => toggleSection('range')}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-between p-0 h-auto font-medium"
                  >
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      <span>数值范围</span>
                    </div>
                    {expandedSections.range ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 mt-3">
                  {/* 费用范围 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      总费用范围
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        placeholder="最小值"
                        value={filters.totalCostMin || ''}
                        onChange={(e) => updateFilter('totalCostMin', e.target.value ? Number(e.target.value) : undefined)}
                      />
                      <Input
                        type="number"
                        placeholder="最大值"
                        value={filters.totalCostMax || ''}
                        onChange={(e) => updateFilter('totalCostMax', e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                  </div>

                  {/* 年龄范围 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      患者年龄范围
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        placeholder="最小年龄"
                        value={filters.patientAgeMin || ''}
                        onChange={(e) => updateFilter('patientAgeMin', e.target.value ? Number(e.target.value) : undefined)}
                      />
                      <Input
                        type="number"
                        placeholder="最大年龄"
                        value={filters.patientAgeMax || ''}
                        onChange={(e) => updateFilter('patientAgeMax', e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 日期范围筛选 */}
              <Collapsible
                open={expandedSections.date}
                onOpenChange={() => toggleSection('date')}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-between p-0 h-auto font-medium"
                  >
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>日期范围</span>
                    </div>
                    {expandedSections.date ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 mt-3">
                  {/* 入院日期 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      入院日期
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="date"
                        placeholder="开始日期"
                        value={filters.admissionDateStart || ''}
                        onChange={(e) => updateFilter('admissionDateStart', e.target.value)}
                      />
                      <Input
                        type="date"
                        placeholder="结束日期"
                        value={filters.admissionDateEnd || ''}
                        onChange={(e) => updateFilter('admissionDateEnd', e.target.value)}
                      />
                    </div>
                  </div>

                  {/* 出院日期 */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      出院日期
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="date"
                        placeholder="开始日期"
                        value={filters.dischargeDateStart || ''}
                        onChange={(e) => updateFilter('dischargeDateStart', e.target.value)}
                      />
                      <Input
                        type="date"
                        placeholder="结束日期"
                        value={filters.dischargeDateEnd || ''}
                        onChange={(e) => updateFilter('dischargeDateEnd', e.target.value)}
                      />
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
              </div>
            </ScrollArea>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
