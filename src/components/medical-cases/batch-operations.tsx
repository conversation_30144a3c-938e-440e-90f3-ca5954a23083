"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { businessToast } from '@/lib/toast'
import { MedicalCase } from '@/types/medical-case'
import {
  ChevronDown,
  Download,
  Trash2,
  FileText,
  Archive,
  Loader2,
  CheckSquare,
  Square
} from 'lucide-react'

interface BatchOperationsProps {
  cases: MedicalCase[]
  selectedIds: string[]
  onSelectionChange: (selectedIds: string[]) => void
  onRefresh: () => void
}

export function BatchOperations({ 
  cases, 
  selectedIds, 
  onSelectionChange, 
  onRefresh 
}: BatchOperationsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isExporting, setIsExporting] = useState(false)

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(cases.map(c => c.id.toString()))
    } else {
      onSelectionChange([])
    }
  }

  // 单个选择
  const handleSelectOne = (caseId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, caseId])
    } else {
      onSelectionChange(selectedIds.filter(id => id !== caseId))
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedIds.length === 0) {
      businessToast.deleteError('案例')
      return
    }

    setIsDeleting(true)
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        return
      }

      const response = await fetch('/api/medical-cases/batch', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ ids: selectedIds }),
      })

      const result = await response.json()

      if (result.success) {
        businessToast.deleteSuccess('案例')
        onSelectionChange([])
        onRefresh()
      } else {
        businessToast.deleteError('案例', result.message)
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      businessToast.deleteError('案例')
    } finally {
      setIsDeleting(false)
      setIsDeleteDialogOpen(false)
    }
  }

  // 批量导出
  const handleBatchExport = async (format: 'excel' | 'csv' | 'pdf') => {
    if (selectedIds.length === 0) {
      businessToast.exportError()
      return
    }

    setIsExporting(true)
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        return
      }

      const response = await fetch('/api/medical-cases/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ 
          ids: selectedIds,
          format 
        }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `medical-cases-${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        businessToast.exportSuccess()
      } else {
        const result = await response.json()
        businessToast.exportError()
      }
    } catch (error) {
      console.error('批量导出失败:', error)
      businessToast.exportError()
    } finally {
      setIsExporting(false)
    }
  }

  // 批量归档
  const handleBatchArchive = async () => {
    if (selectedIds.length === 0) {
      businessToast.deleteError('案例')
      return
    }

    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        return
      }

      const response = await fetch('/api/medical-cases/batch-archive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ ids: selectedIds }),
      })

      const result = await response.json()

      if (result.success) {
        businessToast.saveSuccess('案例')
        onSelectionChange([])
        onRefresh()
      } else {
        businessToast.saveError('案例')
      }
    } catch (error) {
      console.error('批量归档失败:', error)
      businessToast.saveError('案例')
    }
  }

  const isAllSelected = cases.length > 0 && selectedIds.length === cases.length
  const isPartialSelected = selectedIds.length > 0 && selectedIds.length < cases.length

  return (
    <>
      {/* 批量操作工具栏 */}
      <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center space-x-4">
          {/* 全选复选框 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={isAllSelected}
              ref={(el) => {
                if (el) {
                  const input = el.querySelector('input')
                  if (input) input.indeterminate = isPartialSelected
                }
              }}
              onCheckedChange={handleSelectAll}
            />
            <span className="text-sm text-muted-foreground">
              {selectedIds.length > 0 ? (
                <>已选择 <Badge variant="secondary">{selectedIds.length}</Badge> 项</>
              ) : (
                '全选'
              )}
            </span>
          </div>
        </div>

        {/* 批量操作按钮 */}
        {selectedIds.length > 0 && (
          <div className="flex items-center space-x-2">
            {/* 导出操作 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" disabled={isExporting}>
                  {isExporting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  导出
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleBatchExport('excel')}>
                  <FileText className="h-4 w-4 mr-2" />
                  导出为 Excel
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBatchExport('csv')}>
                  <FileText className="h-4 w-4 mr-2" />
                  导出为 CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBatchExport('pdf')}>
                  <FileText className="h-4 w-4 mr-2" />
                  导出为 PDF
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* 归档操作 */}
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleBatchArchive}
            >
              <Archive className="h-4 w-4 mr-2" />
              归档
            </Button>

            {/* 删除操作 */}
            <Button 
              variant="destructive" 
              size="sm"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </Button>
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除选中的 {selectedIds.length} 个案例吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBatchDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  删除中...
                </>
              ) : (
                '确认删除'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

// 表格行选择组件
interface CaseRowSelectorProps {
  caseId: string
  isSelected: boolean
  onSelectionChange: (caseId: string, checked: boolean) => void
}

export function CaseRowSelector({ caseId, isSelected, onSelectionChange }: CaseRowSelectorProps) {
  return (
    <Checkbox
      checked={isSelected}
      onCheckedChange={(checked) => onSelectionChange(caseId, checked as boolean)}
    />
  )
}
