/**
 * 医疗案例快速筛选面板
 * 实现P0-007页面功能架构重新设计目标：
 * - 添加快速筛选面板
 * - 提升用户操作效率
 */

'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { FilterParams } from '@/components/medical-cases/enhanced-filter'
import {
  Filter,
  Calendar,
  DollarSign,
  Hospital,
  User,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  X
} from 'lucide-react'

interface QuickFilterPanelProps {
  onFilterChange: (filters: Partial<FilterParams>) => void
  activeFilters: FilterParams
  onClearFilters: () => void
}

export function QuickFilterPanel({
  onFilterChange,
  activeFilters,
  onClearFilters
}: QuickFilterPanelProps) {
  const quickFilters = [
    {
      category: '案例类型',
      icon: Hospital,
      filters: [
        { key: 'caseType', label: '住院案例', value: 'INPATIENT' },
        { key: 'caseType', label: '门诊案例', value: 'OUTPATIENT' }
      ]
    },
    {
      category: '金额范围',
      icon: DollarSign,
      filters: [
        { key: 'amountRange', label: '< 1000元', value: 'low' },
        { key: 'amountRange', label: '1000-5000元', value: 'medium' },
        { key: 'amountRange', label: '5000-10000元', value: 'high' },
        { key: 'amountRange', label: '> 10000元', value: 'very_high' }
      ]
    },
    {
      category: '时间范围',
      icon: Calendar,
      filters: [
        { key: 'dateRange', label: '今天', value: 'today' },
        { key: 'dateRange', label: '本周', value: 'week' },
        { key: 'dateRange', label: '本月', value: 'month' },
        { key: 'dateRange', label: '本季度', value: 'quarter' }
      ]
    },
    {
      category: '风险等级',
      icon: AlertTriangle,
      filters: [
        { key: 'riskLevel', label: '高风险', value: 'HIGH' },
        { key: 'riskLevel', label: '中风险', value: 'MEDIUM' },
        { key: 'riskLevel', label: '低风险', value: 'LOW' }
      ]
    },
    {
      category: '审核状态',
      icon: CheckCircle,
      filters: [
        { key: 'auditStatus', label: '待审核', value: 'PENDING' },
        { key: 'auditStatus', label: '已通过', value: 'APPROVED' },
        { key: 'auditStatus', label: '已拒绝', value: 'REJECTED' }
      ]
    }
  ]

  const handleFilterClick = (key: string, value: string) => {
    const currentValue = activeFilters[key as keyof FilterParams]
    const newValue = currentValue === value ? undefined : value
    onFilterChange({ [key]: newValue })
  }

  const getActiveFilterCount = () => {
    return Object.values(activeFilters).filter(value => 
      value !== undefined && value !== '' && value !== null
    ).length
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <Filter className="h-4 w-4" />
            快速筛选
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </CardTitle>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4 mr-1" />
              清除
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {quickFilters.map((category, categoryIndex) => (
          <div key={categoryIndex} className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <category.icon className="h-4 w-4" />
              {category.category}
            </div>
            
            <div className="grid grid-cols-1 gap-1">
              {category.filters.map((filter, filterIndex) => {
                const isActive = activeFilters[filter.key as keyof FilterParams] === filter.value
                
                return (
                  <Button
                    key={filterIndex}
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handleFilterClick(filter.key, filter.value)}
                    className="justify-start h-8 text-xs"
                  >
                    {filter.label}
                  </Button>
                )
              })}
            </div>
            
            {categoryIndex < quickFilters.length - 1 && (
              <Separator className="mt-3" />
            )}
          </div>
        ))}
        
        {/* 常用组合筛选 */}
        <div className="space-y-2">
          <Separator />
          <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
            <Activity className="h-4 w-4" />
            常用组合
          </div>
          
          <div className="space-y-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFilterChange({
                caseType: 'INPATIENT'
              })}
              className="justify-start h-8 text-xs w-full"
            >
              高风险住院待审核
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFilterChange({
                totalCostMin: 100000
              })}
              className="justify-start h-8 text-xs w-full"
            >
              本周高额案例
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFilterChange({
                caseType: 'OUTPATIENT'
              })}
              className="justify-start h-8 text-xs w-full"
            >
              今日门诊待审核
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 活跃筛选器显示组件
 */
interface ActiveFiltersDisplayProps {
  activeFilters: FilterParams
  onRemoveFilter: (key: string) => void
  onClearAll: () => void
}

export function ActiveFiltersDisplay({
  activeFilters,
  onRemoveFilter,
  onClearAll
}: ActiveFiltersDisplayProps) {
  const getFilterLabel = (key: string, value: any): string => {
    const filterLabels: Record<string, Record<string, string>> = {
      caseType: {
        'INPATIENT': '住院案例',
        'OUTPATIENT': '门诊案例'
      },
      amountRange: {
        'low': '< 1000元',
        'medium': '1000-5000元',
        'high': '5000-10000元',
        'very_high': '> 10000元'
      },
      dateRange: {
        'today': '今天',
        'week': '本周',
        'month': '本月',
        'quarter': '本季度'
      },
      riskLevel: {
        'HIGH': '高风险',
        'MEDIUM': '中风险',
        'LOW': '低风险'
      },
      auditStatus: {
        'PENDING': '待审核',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝'
      }
    }

    return filterLabels[key]?.[value] || `${key}: ${value}`
  }

  const activeFilterEntries = Object.entries(activeFilters).filter(
    ([_, value]) => value !== undefined && value !== '' && value !== null
  )

  if (activeFilterEntries.length === 0) {
    return null
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium">当前筛选条件</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAll}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4 mr-1" />
            清除全部
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {activeFilterEntries.map(([key, value]) => (
            <Badge
              key={key}
              variant="secondary"
              className="flex items-center gap-1 pr-1"
            >
              {getFilterLabel(key, value)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveFilter(key)}
                className="h-4 w-4 p-0 hover:bg-transparent"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
