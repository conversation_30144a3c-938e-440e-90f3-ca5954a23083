"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Filter, X, Calendar, DollarSign, User, Hospital } from 'lucide-react'

export interface FilterParams {
  caseType?: string
  medicalCategory?: string
  patientGender?: string
  hospitalName?: string
  admissionDateStart?: string
  admissionDateEnd?: string
  dischargeDateStart?: string
  dischargeDateEnd?: string
  totalCostMin?: number
  totalCostMax?: number
  patientAgeMin?: number
  patientAgeMax?: number
}

interface AdvancedFilterProps {
  filters: FilterParams
  onFiltersChange: (filters: FilterParams) => void
  onReset: () => void
}

export function AdvancedFilter({ filters, onFiltersChange, onReset }: AdvancedFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [localFilters, setLocalFilters] = useState<FilterParams>(filters)

  // 应用筛选
  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
    setIsOpen(false)
  }

  // 重置筛选
  const handleResetFilters = () => {
    const emptyFilters: FilterParams = {}
    setLocalFilters(emptyFilters)
    onFiltersChange(emptyFilters)
    onReset()
    setIsOpen(false)
  }

  // 更新本地筛选条件
  const updateLocalFilter = (key: keyof FilterParams, value: string | number | undefined) => {
    // 将 "ALL" 值转换为 undefined，表示不筛选
    const finalValue = value === 'ALL' ? undefined : (value || undefined)
    setLocalFilters(prev => ({
      ...prev,
      [key]: finalValue
    }))
  }

  // 获取活跃筛选条件数量
  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length
  }

  // 获取筛选条件标签
  const getFilterLabels = () => {
    const labels: string[] = []
    
    if (filters.caseType) {
      labels.push(`类型: ${filters.caseType === 'INPATIENT' ? '住院' : '门诊'}`)
    }
    if (filters.medicalCategory) {
      labels.push(`医疗类别: ${filters.medicalCategory}`)
    }
    if (filters.patientGender) {
      const genderMap = { 'MALE': '男', 'FEMALE': '女', 'OTHER': '其他' }
      labels.push(`性别: ${genderMap[filters.patientGender as keyof typeof genderMap] || filters.patientGender}`)
    }
    if (filters.hospitalName) {
      labels.push(`医院: ${filters.hospitalName}`)
    }
    if (filters.admissionDateStart || filters.admissionDateEnd) {
      const start = filters.admissionDateStart || '开始'
      const end = filters.admissionDateEnd || '结束'
      labels.push(`入院日期: ${start} ~ ${end}`)
    }
    if (filters.dischargeDateStart || filters.dischargeDateEnd) {
      const start = filters.dischargeDateStart || '开始'
      const end = filters.dischargeDateEnd || '结束'
      labels.push(`出院日期: ${start} ~ ${end}`)
    }
    if (filters.totalCostMin !== undefined || filters.totalCostMax !== undefined) {
      const min = filters.totalCostMin !== undefined ? `¥${filters.totalCostMin}` : '最小'
      const max = filters.totalCostMax !== undefined ? `¥${filters.totalCostMax}` : '最大'
      labels.push(`费用: ${min} ~ ${max}`)
    }
    if (filters.patientAgeMin !== undefined || filters.patientAgeMax !== undefined) {
      const min = filters.patientAgeMin !== undefined ? `${filters.patientAgeMin}岁` : '最小'
      const max = filters.patientAgeMax !== undefined ? `${filters.patientAgeMax}岁` : '最大'
      labels.push(`年龄: ${min} ~ ${max}`)
    }

    return labels
  }

  const activeFiltersCount = getActiveFiltersCount()
  const filterLabels = getFilterLabels()

  return (
    <div className="space-y-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="relative">
            <Filter className="h-4 w-4 mr-2" />
            高级筛选
            {activeFiltersCount > 0 && (
              <Badge variant="default" className="ml-2 h-4 w-4 rounded-full p-0 text-xs bg-blue-600 hover:bg-blue-600">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="start">
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">高级筛选</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <Separator />

            {/* 基本信息筛选 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-medium">基本信息</Label>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="caseType" className="text-xs">案例类型</Label>
                  <Select
                    value={localFilters.caseType || 'ALL'}
                    onValueChange={(value) => updateLocalFilter('caseType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">全部</SelectItem>
                      <SelectItem value="INPATIENT">住院</SelectItem>
                      <SelectItem value="OUTPATIENT">门诊</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="patientGender" className="text-xs">患者性别</Label>
                  <Select
                    value={localFilters.patientGender || 'ALL'}
                    onValueChange={(value) => updateLocalFilter('patientGender', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">全部</SelectItem>
                      <SelectItem value="MALE">男</SelectItem>
                      <SelectItem value="FEMALE">女</SelectItem>
                      <SelectItem value="OTHER">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="medicalCategory" className="text-xs">医疗类别</Label>
                <Input
                  id="medicalCategory"
                  placeholder="输入医疗类别"
                  value={localFilters.medicalCategory || ''}
                  onChange={(e) => updateLocalFilter('medicalCategory', e.target.value)}
                />
              </div>
            </div>

            <Separator />

            {/* 医院信息筛选 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Hospital className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-medium">医院信息</Label>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="hospitalName" className="text-xs">医院名称</Label>
                <Input
                  id="hospitalName"
                  placeholder="输入医院名称"
                  value={localFilters.hospitalName || ''}
                  onChange={(e) => updateLocalFilter('hospitalName', e.target.value)}
                />
              </div>
            </div>

            <Separator />

            {/* 日期范围筛选 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-medium">日期范围</Label>
              </div>
              
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label className="text-xs">入院日期</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="date"
                      placeholder="开始日期"
                      value={localFilters.admissionDateStart || ''}
                      onChange={(e) => updateLocalFilter('admissionDateStart', e.target.value)}
                    />
                    <Input
                      type="date"
                      placeholder="结束日期"
                      value={localFilters.admissionDateEnd || ''}
                      onChange={(e) => updateLocalFilter('admissionDateEnd', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs">出院日期</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="date"
                      placeholder="开始日期"
                      value={localFilters.dischargeDateStart || ''}
                      onChange={(e) => updateLocalFilter('dischargeDateStart', e.target.value)}
                    />
                    <Input
                      type="date"
                      placeholder="结束日期"
                      value={localFilters.dischargeDateEnd || ''}
                      onChange={(e) => updateLocalFilter('dischargeDateEnd', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 数值范围筛选 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-medium">数值范围</Label>
              </div>
              
              <div className="space-y-3">
                <div className="space-y-2">
                  <Label className="text-xs">总费用 (元)</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="最小值"
                      value={localFilters.totalCostMin || ''}
                      onChange={(e) => updateLocalFilter('totalCostMin', e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                    <Input
                      type="number"
                      placeholder="最大值"
                      value={localFilters.totalCostMax || ''}
                      onChange={(e) => updateLocalFilter('totalCostMax', e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs">患者年龄</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="最小年龄"
                      value={localFilters.patientAgeMin || ''}
                      onChange={(e) => updateLocalFilter('patientAgeMin', e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                    <Input
                      type="number"
                      placeholder="最大年龄"
                      value={localFilters.patientAgeMax || ''}
                      onChange={(e) => updateLocalFilter('patientAgeMax', e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 操作按钮 */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={handleResetFilters}>
                重置
              </Button>
              <Button onClick={handleApplyFilters}>
                应用筛选
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* 活跃筛选条件显示 */}
      {filterLabels.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {filterLabels.slice(0, 3).map((label, index) => (
            <Badge key={index} variant="outline" className="text-xs h-6 bg-blue-50 text-blue-700 border-blue-200">
              {label}
            </Badge>
          ))}
          {filterLabels.length > 3 && (
            <Badge variant="outline" className="text-xs h-6 bg-gray-50 text-gray-600 border-gray-200">
              +{filterLabels.length - 3} 更多
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleResetFilters}
            className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
          >
            <X className="h-3 w-3 mr-1" />
            清除
          </Button>
        </div>
      )}
    </div>
  )
}
