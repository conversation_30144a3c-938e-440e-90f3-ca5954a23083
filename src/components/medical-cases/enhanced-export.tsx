'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { toast } from 'sonner'
import { 
  Download, 
  FileSpreadsheet, 
  FileText, 
  Database,
  Settings,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'

interface ExportConfig {
  format: 'xlsx' | 'csv' | 'pdf' | 'json'
  scope: 'selected' | 'filtered' | 'all'
  includeFields: string[]
  includeRelations: boolean
  dateRange?: {
    start: string
    end: string
  }
}

interface EnhancedExportProps {
  selectedIds: string[]
  totalCount: number
  filteredCount: number
  onExport: (config: ExportConfig) => Promise<void>
  className?: string
}

const EXPORT_FIELDS = [
  { id: 'basic', label: '基本信息', fields: ['caseNumber', 'patientName', 'patientAge', 'patientGender'] },
  { id: 'medical', label: '医疗信息', fields: ['medicalCategory', 'hospitalName', 'department', 'doctorName'] },
  { id: 'dates', label: '时间信息', fields: ['admissionDate', 'dischargeDate', 'createdAt'] },
  { id: 'financial', label: '费用信息', fields: ['totalCost', 'costDetails', 'settlements'] },
  { id: 'diagnosis', label: '诊断信息', fields: ['diagnoses', 'surgeries'] },
  { id: 'system', label: '系统信息', fields: ['createdBy', 'updatedBy', 'updatedAt'] }
]

const EXPORT_FORMATS = [
  { 
    value: 'xlsx', 
    label: 'Excel文件 (.xlsx)', 
    icon: FileSpreadsheet,
    description: '适合数据分析和进一步处理',
    maxRecords: 100000
  },
  { 
    value: 'csv', 
    label: 'CSV文件 (.csv)', 
    icon: FileText,
    description: '通用格式，兼容性最好',
    maxRecords: 500000
  },
  { 
    value: 'pdf', 
    label: 'PDF报告 (.pdf)', 
    icon: FileText,
    description: '适合打印和分享',
    maxRecords: 1000
  },
  { 
    value: 'json', 
    label: 'JSON数据 (.json)', 
    icon: Database,
    description: '适合程序处理和API集成',
    maxRecords: 50000
  }
]

/**
 * 增强的数据导出组件
 * 支持多种格式、字段选择、关联数据等
 */
export function EnhancedExport({ 
  selectedIds, 
  totalCount, 
  filteredCount, 
  onExport,
  className 
}: EnhancedExportProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [config, setConfig] = useState<ExportConfig>({
    format: 'xlsx',
    scope: selectedIds.length > 0 ? 'selected' : 'filtered',
    includeFields: ['basic', 'medical', 'dates', 'financial'],
    includeRelations: false
  })

  const selectedFormat = EXPORT_FORMATS.find(f => f.value === config.format)
  const recordCount = config.scope === 'selected' ? selectedIds.length : 
                     config.scope === 'filtered' ? filteredCount : totalCount

  /**
   * 处理导出
   */
  const handleExport = async () => {
    if (recordCount === 0) {
      toast.error('没有可导出的数据')
      return
    }

    if (selectedFormat && recordCount > selectedFormat.maxRecords) {
      toast.error(`${selectedFormat.label} 格式最多支持导出 ${selectedFormat.maxRecords.toLocaleString()} 条记录`)
      return
    }

    try {
      setIsExporting(true)
      setExportProgress(0)

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + Math.random() * 20
        })
      }, 500)

      await onExport(config)

      clearInterval(progressInterval)
      setExportProgress(100)

      toast.success('导出成功', {
        description: `已成功导出 ${recordCount.toLocaleString()} 条记录`
      })

      setTimeout(() => {
        setIsOpen(false)
        setIsExporting(false)
        setExportProgress(0)
      }, 1000)

    } catch (error) {
      console.error('导出失败:', error)
      toast.error('导出失败', {
        description: error instanceof Error ? error.message : '请稍后重试'
      })
      setIsExporting(false)
      setExportProgress(0)
    }
  }

  /**
   * 更新配置
   */
  const updateConfig = (key: keyof ExportConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  /**
   * 切换字段组
   */
  const toggleFieldGroup = (groupId: string) => {
    setConfig(prev => ({
      ...prev,
      includeFields: prev.includeFields.includes(groupId)
        ? prev.includeFields.filter(id => id !== groupId)
        : [...prev.includeFields, groupId]
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className={className}>
          <Download className="h-4 w-4 mr-2" />
          导出数据
          {selectedIds.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {selectedIds.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            导出医疗案例数据
          </DialogTitle>
          <DialogDescription>
            选择导出格式、范围和字段，生成所需的数据文件
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 导出范围 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">导出范围</CardTitle>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={config.scope}
                onValueChange={(value) => updateConfig('scope', value)}
                className="space-y-3"
              >
                {selectedIds.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="selected" id="selected" />
                    <Label htmlFor="selected" className="flex items-center gap-2">
                      已选择的案例
                      <Badge variant="outline">{selectedIds.length} 条</Badge>
                    </Label>
                  </div>
                )}
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="filtered" id="filtered" />
                  <Label htmlFor="filtered" className="flex items-center gap-2">
                    当前筛选结果
                    <Badge variant="outline">{filteredCount.toLocaleString()} 条</Badge>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all" className="flex items-center gap-2">
                    全部案例
                    <Badge variant="outline">{totalCount.toLocaleString()} 条</Badge>
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* 导出格式 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">导出格式</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3">
                {EXPORT_FORMATS.map((format) => {
                  const Icon = format.icon
                  const isSelected = config.format === format.value
                  const exceedsLimit = recordCount > format.maxRecords

                  return (
                    <div
                      key={format.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        isSelected 
                          ? 'border-primary bg-primary/5' 
                          : exceedsLimit 
                            ? 'border-muted bg-muted/50 cursor-not-allowed opacity-50'
                            : 'border-muted hover:border-primary/50'
                      }`}
                      onClick={() => !exceedsLimit && updateConfig('format', format.value)}
                    >
                      <div className="flex items-start gap-3">
                        <Icon className="h-5 w-5 mt-0.5 text-muted-foreground" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{format.label}</span>
                            {exceedsLimit && (
                              <Badge variant="destructive" className="text-xs">
                                超出限制
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {format.description}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            最大支持: {format.maxRecords.toLocaleString()} 条记录
                          </p>
                        </div>
                        {isSelected && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* 字段选择 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">包含字段</CardTitle>
              <CardDescription>
                选择要导出的数据字段组
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {EXPORT_FIELDS.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={group.id}
                      checked={config.includeFields.includes(group.id)}
                      onCheckedChange={() => toggleFieldGroup(group.id)}
                    />
                    <Label htmlFor={group.id} className="flex-1">
                      <div className="font-medium">{group.label}</div>
                      <div className="text-sm text-muted-foreground">
                        {group.fields.join(', ')}
                      </div>
                    </Label>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeRelations"
                  checked={config.includeRelations}
                  onCheckedChange={(checked) => updateConfig('includeRelations', checked)}
                />
                <Label htmlFor="includeRelations">
                  <div className="font-medium">包含关联数据</div>
                  <div className="text-sm text-muted-foreground">
                    诊断信息、手术记录、费用明细等
                  </div>
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* 导出进度 */}
          {isExporting && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-primary animate-spin" />
                    <span className="text-sm font-medium">正在导出数据...</span>
                  </div>
                  <Progress value={exportProgress} className="w-full" />
                  <p className="text-xs text-muted-foreground">
                    已处理 {Math.round(recordCount * exportProgress / 100).toLocaleString()} / {recordCount.toLocaleString()} 条记录
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              将导出 <span className="font-medium">{recordCount.toLocaleString()}</span> 条记录
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isExporting}
              >
                取消
              </Button>
              <Button
                onClick={handleExport}
                disabled={isExporting || recordCount === 0 || config.includeFields.length === 0}
              >
                {isExporting ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    导出中...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    开始导出
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
