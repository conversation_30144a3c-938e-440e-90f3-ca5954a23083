'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { 
  Settings, 
  Trash2, 
  <PERSON>, 
  Archive, 
  Tag,
  User<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  X
} from 'lucide-react'

interface BatchOperation {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  requiresConfirmation: boolean
  requiresInput?: boolean
  inputLabel?: string
  inputPlaceholder?: string
  variant?: 'default' | 'destructive' | 'secondary'
}

interface BatchOperationProgress {
  total: number
  completed: number
  failed: number
  current?: string
}

interface EnhancedBatchOperationsProps {
  selectedIds: string[]
  onClearSelection: () => void
  onBatchOperation: (operation: string, params?: any) => Promise<void>
  className?: string
}

const BATCH_OPERATIONS: BatchOperation[] = [
  {
    id: 'delete',
    label: '批量删除',
    icon: Trash2,
    description: '永久删除选中的医疗案例',
    requiresConfirmation: true,
    variant: 'destructive'
  },
  {
    id: 'archive',
    label: '批量归档',
    icon: Archive,
    description: '将选中案例移至归档状态',
    requiresConfirmation: true,
    variant: 'secondary'
  },
  {
    id: 'updateCategory',
    label: '更新分类',
    icon: Tag,
    description: '批量更新医疗分类',
    requiresConfirmation: false,
    requiresInput: true,
    inputLabel: '新的医疗分类',
    inputPlaceholder: '选择新的医疗分类'
  },
  {
    id: 'assignReviewer',
    label: '分配审核员',
    icon: UserCheck,
    description: '为选中案例分配审核员',
    requiresConfirmation: false,
    requiresInput: true,
    inputLabel: '审核员',
    inputPlaceholder: '选择审核员'
  },
  {
    id: 'addNote',
    label: '添加备注',
    icon: Edit,
    description: '为选中案例添加统一备注',
    requiresConfirmation: false,
    requiresInput: true,
    inputLabel: '备注内容',
    inputPlaceholder: '输入备注内容...'
  }
]

const MEDICAL_CATEGORIES = [
  { value: '异地住院', label: '异地住院' },
  { value: '急诊住院', label: '急诊住院' },
  { value: '普通住院', label: '普通住院' },
  { value: '门诊慢特病', label: '门诊慢特病' },
  { value: '转外诊治住院', label: '转外诊治住院' },
  { value: '生育住院', label: '生育住院' }
]

const REVIEWERS = [
  { value: '1', label: '张医生' },
  { value: '2', label: '李医生' },
  { value: '3', label: '王医生' },
  { value: '4', label: '赵医生' }
]

/**
 * 增强的批量操作组件
 * 支持多种批量操作、进度跟踪、错误处理等
 */
export function EnhancedBatchOperations({ 
  selectedIds, 
  onClearSelection, 
  onBatchOperation,
  className 
}: EnhancedBatchOperationsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedOperation, setSelectedOperation] = useState<BatchOperation | null>(null)
  const [inputValue, setInputValue] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState<BatchOperationProgress | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  /**
   * 执行批量操作
   */
  const executeBatchOperation = async () => {
    if (!selectedOperation) return

    try {
      setIsProcessing(true)
      setProgress({
        total: selectedIds.length,
        completed: 0,
        failed: 0
      })

      // 准备参数
      let params: any = {}
      if (selectedOperation.requiresInput && inputValue) {
        switch (selectedOperation.id) {
          case 'updateCategory':
            params.category = inputValue
            break
          case 'assignReviewer':
            params.reviewerId = inputValue
            break
          case 'addNote':
            params.note = inputValue
            break
        }
      }

      // 模拟批量处理进度
      const batchSize = 10
      const batches = Math.ceil(selectedIds.length / batchSize)
      
      for (let i = 0; i < batches; i++) {
        const batchIds = selectedIds.slice(i * batchSize, (i + 1) * batchSize)
        
        try {
          await onBatchOperation(selectedOperation.id, {
            ids: batchIds,
            ...params
          })
          
          setProgress(prev => prev ? {
            ...prev,
            completed: prev.completed + batchIds.length,
            current: `处理第 ${i + 1}/${batches} 批`
          } : null)
        } catch (error) {
          console.error(`批次 ${i + 1} 处理失败:`, error)
          setProgress(prev => prev ? {
            ...prev,
            failed: prev.failed + batchIds.length
          } : null)
        }

        // 添加延迟以显示进度
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      const finalProgress = progress
      if (finalProgress) {
        if (finalProgress.failed === 0) {
          toast.success('批量操作完成', {
            description: `成功处理 ${finalProgress.completed} 条记录`
          })
        } else {
          toast.warning('批量操作部分完成', {
            description: `成功 ${finalProgress.completed} 条，失败 ${finalProgress.failed} 条`
          })
        }
      }

      // 清除选择
      onClearSelection()
      
      // 关闭对话框
      setTimeout(() => {
        setIsOpen(false)
        setSelectedOperation(null)
        setInputValue('')
        setIsProcessing(false)
        setProgress(null)
      }, 1500)

    } catch (error) {
      console.error('批量操作失败:', error)
      toast.error('批量操作失败', {
        description: error instanceof Error ? error.message : '请稍后重试'
      })
      setIsProcessing(false)
      setProgress(null)
    }
  }

  /**
   * 处理操作选择
   */
  const handleOperationSelect = (operation: BatchOperation) => {
    setSelectedOperation(operation)
    setInputValue('')
    
    if (operation.requiresConfirmation) {
      setShowConfirmDialog(true)
    } else {
      // 如果需要输入，保持对话框打开等待输入
      if (!operation.requiresInput) {
        executeBatchOperation()
      }
    }
  }

  /**
   * 渲染输入组件
   */
  const renderInput = () => {
    if (!selectedOperation?.requiresInput) return null

    switch (selectedOperation.id) {
      case 'updateCategory':
        return (
          <Select value={inputValue} onValueChange={setInputValue}>
            <SelectTrigger>
              <SelectValue placeholder={selectedOperation.inputPlaceholder} />
            </SelectTrigger>
            <SelectContent>
              {MEDICAL_CATEGORIES.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      
      case 'assignReviewer':
        return (
          <Select value={inputValue} onValueChange={setInputValue}>
            <SelectTrigger>
              <SelectValue placeholder={selectedOperation.inputPlaceholder} />
            </SelectTrigger>
            <SelectContent>
              {REVIEWERS.map((reviewer) => (
                <SelectItem key={reviewer.value} value={reviewer.value}>
                  {reviewer.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      
      case 'addNote':
        return (
          <Textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={selectedOperation.inputPlaceholder}
            rows={3}
          />
        )
      
      default:
        return null
    }
  }

  if (selectedIds.length === 0) {
    return null
  }

  return (
    <>
      <Card className={className}>
        <CardContent className="pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">
                已选择 {selectedIds.length} 条记录
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    批量操作
                  </Button>
                </DialogTrigger>

                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>批量操作</DialogTitle>
                    <DialogDescription>
                      对选中的 {selectedIds.length} 条记录执行批量操作
                    </DialogDescription>
                  </DialogHeader>

                  {isProcessing && progress ? (
                    <div className="space-y-4">
                      <div className="text-center">
                        <Clock className="h-8 w-8 mx-auto text-primary animate-spin" />
                        <p className="text-sm font-medium mt-2">正在处理...</p>
                        {progress.current && (
                          <p className="text-xs text-muted-foreground">{progress.current}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Progress 
                          value={(progress.completed + progress.failed) / progress.total * 100} 
                          className="w-full" 
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>已完成: {progress.completed}</span>
                          <span>失败: {progress.failed}</span>
                          <span>总计: {progress.total}</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {selectedOperation ? (
                        <div className="space-y-4">
                          <div className="flex items-start gap-3 p-3 border rounded-lg">
                            <selectedOperation.icon className="h-5 w-5 mt-0.5 text-muted-foreground" />
                            <div>
                              <h4 className="font-medium">{selectedOperation.label}</h4>
                              <p className="text-sm text-muted-foreground">
                                {selectedOperation.description}
                              </p>
                            </div>
                          </div>

                          {selectedOperation.requiresInput && (
                            <div className="space-y-2">
                              <Label>{selectedOperation.inputLabel}</Label>
                              {renderInput()}
                            </div>
                          )}

                          <div className="flex justify-between">
                            <Button
                              variant="outline"
                              onClick={() => setSelectedOperation(null)}
                            >
                              返回
                            </Button>
                            <Button
                              onClick={executeBatchOperation}
                              disabled={selectedOperation.requiresInput && !inputValue}
                              variant={selectedOperation.variant}
                            >
                              执行操作
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {BATCH_OPERATIONS.map((operation) => {
                            const Icon = operation.icon
                            return (
                              <Button
                                key={operation.id}
                                variant="ghost"
                                className="w-full justify-start h-auto p-3"
                                onClick={() => handleOperationSelect(operation)}
                              >
                                <Icon className="h-4 w-4 mr-3" />
                                <div className="text-left">
                                  <div className="font-medium">{operation.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {operation.description}
                                  </div>
                                </div>
                              </Button>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )}
                </DialogContent>
              </Dialog>

              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
              >
                <X className="h-4 w-4 mr-2" />
                清除选择
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 确认对话框 */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              确认操作
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedOperation && (
                <>
                  您即将对 <span className="font-medium">{selectedIds.length}</span> 条记录执行
                  <span className="font-medium text-destructive"> {selectedOperation.label}</span> 操作。
                  <br />
                  此操作不可撤销，请确认是否继续？
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedOperation(null)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setShowConfirmDialog(false)
                executeBatchOperation()
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认执行
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
