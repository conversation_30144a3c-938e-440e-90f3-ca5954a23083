"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { CaseStatistics } from '@/types/medical-case'
import { formatCurrency, formatLargeNumber } from '@/lib/format-utils'
import {
  FileText,
  DollarSign,
  Hospital,
  Activity,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Calendar,
  Users
} from 'lucide-react'

interface StatisticsCardsProps {
  statistics: CaseStatistics | null
  isLoading?: boolean
  error?: string | null
}

// 使用统一的格式化函数

// 生成模拟趋势数据
const generateTrendData = (value: number, type: 'cases' | 'cost' | 'inpatient' | 'outpatient') => {
  // 基于数据类型生成合理的趋势
  const trends = {
    cases: { min: 5, max: 20, positive: 0.7 },
    cost: { min: 3, max: 15, positive: 0.6 },
    inpatient: { min: -5, max: 10, positive: 0.4 },
    outpatient: { min: 0, max: 25, positive: 0.8 }
  }

  const config = trends[type]
  const isPositive = Math.random() < config.positive
  const change = isPositive
    ? Math.random() * (config.max - Math.max(0, config.min)) + Math.max(0, config.min)
    : Math.random() * Math.abs(Math.min(0, config.min))

  return {
    value: Number(change.toFixed(1)),
    isPositive,
    period: '较上月'
  }
}

// 骨架屏组件
const StatisticCardSkeleton = () => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-4 w-4 rounded" />
    </CardHeader>
    <CardContent>
      <Skeleton className="h-8 w-24 mb-2" />
      <Skeleton className="h-3 w-32 mb-3" />
      <div className="flex items-center gap-2">
        <Skeleton className="h-5 w-12 rounded-full" />
        <Skeleton className="h-3 w-16" />
      </div>
    </CardContent>
  </Card>
)

export function StatisticsCards({ statistics, isLoading = false, error }: StatisticsCardsProps) {
  // 错误状态
  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="md:col-span-2 lg:col-span-4">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">统计数据加载失败：{error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 加载状态
  if (isLoading || !statistics) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatisticCardSkeleton />
        <StatisticCardSkeleton />
        <StatisticCardSkeleton />
        <StatisticCardSkeleton />
      </div>
    )
  }

  // 计算统计数据
  const totalCases = statistics.totalCases || 0
  const inpatientCases = statistics.inpatientCases || 0
  const outpatientCases = statistics.outpatientCases || 0
  const totalCost = statistics.totalCost || 0

  const inpatientPercentage = totalCases > 0
    ? (inpatientCases / totalCases * 100).toFixed(1)
    : '0.0'

  const outpatientPercentage = totalCases > 0
    ? (outpatientCases / totalCases * 100).toFixed(1)
    : '0.0'

  // 计算平均费用
  const avgCost = totalCases > 0 ? totalCost / totalCases : 0

  // 生成趋势数据
  const trends = {
    totalCases: generateTrendData(totalCases, 'cases'),
    totalCost: generateTrendData(totalCost, 'cost'),
    inpatientCases: generateTrendData(inpatientCases, 'inpatient'),
    outpatientCases: generateTrendData(outpatientCases, 'outpatient')
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* 总案例数 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总案例数</CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalCases.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            医疗案例总数
          </p>
        </CardContent>
      </Card>

      {/* 总费用 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总费用</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(totalCost, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</div>
          <p className="text-xs text-muted-foreground">
            累计医疗费用
          </p>
        </CardContent>
      </Card>

      {/* 住院案例 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">住院案例</CardTitle>
          <Hospital className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatLargeNumber(inpatientCases)}</div>
          <p className="text-xs text-muted-foreground">
            占比 {inpatientPercentage}%
          </p>
        </CardContent>
      </Card>

      {/* 门诊案例 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">门诊案例</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatLargeNumber(outpatientCases)}</div>
          <p className="text-xs text-muted-foreground">
            占比 {outpatientPercentage}%
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
