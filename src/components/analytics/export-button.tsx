'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Download, FileText, Image, Table } from 'lucide-react'
import { useToast } from '@/lib/toast'
import {
  exportToCSV,
  exportToJSON,
  exportChartAsImage,
  formatDataForExport,
  getExportFilename,
  validateExportData
} from '@/lib/export-utils'

interface ExportButtonProps {
  data: any
  filename: string
  dataType?: 'case' | 'cost' | 'rule' | 'raw'
  chartRef?: React.RefObject<any>
  className?: string
}

export function ExportButton({
  data,
  filename,
  dataType = 'raw',
  chartRef,
  className
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const handleExport = async (format: 'csv' | 'json' | 'image') => {
    if (!validateExportData(data)) {
      toast({
        title: '导出失败',
        description: '没有可导出的数据',
        variant: 'destructive',
      })
      return
    }

    setIsExporting(true)

    try {
      const exportFilename = getExportFilename(filename)

      switch (format) {
        case 'csv':
          if (Array.isArray(data)) {
            const formattedData = dataType !== 'raw' 
              ? formatDataForExport(data, dataType)
              : data
            exportToCSV(formattedData, exportFilename)
          } else {
            toast({
              title: '导出失败',
              description: 'CSV格式只支持数组数据',
              variant: 'destructive',
            })
            return
          }
          break

        case 'json':
          exportToJSON(data, exportFilename)
          break

        case 'image':
          if (!chartRef) {
            toast({
              title: '导出失败',
              description: '图表引用不存在',
              variant: 'destructive',
            })
            return
          }
          await exportChartAsImage(chartRef, exportFilename)
          break

        default:
          throw new Error('不支持的导出格式')
      }

      toast({
        title: '导出成功',
        description: `数据已导出为 ${format.toUpperCase()} 格式`,
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          disabled={isExporting}
          className={className}
        >
          <Download className="h-4 w-4 mr-2" />
          {isExporting ? '导出中...' : '导出'}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleExport('csv')}>
          <Table className="h-4 w-4 mr-2" />
          导出为 CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('json')}>
          <FileText className="h-4 w-4 mr-2" />
          导出为 JSON
        </DropdownMenuItem>
        {chartRef && (
          <DropdownMenuItem onClick={() => handleExport('image')}>
            <Image className="h-4 w-4 mr-2" />
            导出为图片
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 简化的导出按钮（只支持CSV）
 */
export function SimpleExportButton({
  data,
  filename,
  dataType = 'raw',
  className
}: Omit<ExportButtonProps, 'chartRef'>) {
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const handleExport = async () => {
    if (!validateExportData(data) || !Array.isArray(data)) {
      toast({
        title: '导出失败',
        description: '没有可导出的数据',
        variant: 'destructive',
      })
      return
    }

    setIsExporting(true)

    try {
      const formattedData = dataType !== 'raw' 
        ? formatDataForExport(data, dataType)
        : data
      const exportFilename = getExportFilename(filename)
      
      exportToCSV(formattedData, exportFilename)

      toast({
        title: '导出成功',
        description: '数据已导出为 CSV 格式',
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExport}
      disabled={isExporting}
      className={className}
    >
      <Download className="h-4 w-4 mr-2" />
      {isExporting ? '导出中...' : '导出CSV'}
    </Button>
  )
}
