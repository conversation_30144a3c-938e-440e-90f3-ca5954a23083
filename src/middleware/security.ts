import { NextRequest, NextResponse } from 'next/server'

/**
 * 安全中间件 - 检测和阻止不安全的请求
 */

// 敏感参数列表
const SENSITIVE_PARAMS = [
  'password',
  'passwd',
  'pwd',
  'secret',
  'token',
  'key',
  'auth',
  'credential'
]

// 敏感路径列表
const SENSITIVE_PATHS = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/reset-password',
  '/api/users',
  '/api/admin'
]

/**
 * 检测URL中是否包含敏感信息
 */
export function detectSensitiveDataInUrl(request: NextRequest): {
  hasSensitiveData: boolean
  sensitiveParams: string[]
  securityLevel: 'low' | 'medium' | 'high' | 'critical'
} {
  const url = new URL(request.url)
  const searchParams = url.searchParams
  const sensitiveParams: string[] = []
  
  // 检查URL参数
  for (const [key, value] of searchParams.entries()) {
    const lowerKey = key.toLowerCase()
    
    // 检查参数名是否敏感
    if (SENSITIVE_PARAMS.some(param => lowerKey.includes(param))) {
      sensitiveParams.push(key)
    }
    
    // 检查参数值是否像密码（长度和复杂度）
    if (value && value.length >= 6 && /[A-Za-z]/.test(value) && /[0-9!@#$%^&*]/.test(value)) {
      sensitiveParams.push(`${key}(suspected_password)`)
    }
  }
  
  // 确定安全级别
  let securityLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'
  
  if (sensitiveParams.length > 0) {
    const hasPassword = sensitiveParams.some(param => 
      param.toLowerCase().includes('password') || param.toLowerCase().includes('pwd')
    )
    
    if (hasPassword) {
      securityLevel = 'critical'
    } else if (sensitiveParams.length >= 3) {
      securityLevel = 'high'
    } else if (sensitiveParams.length >= 2) {
      securityLevel = 'medium'
    } else {
      securityLevel = 'medium'
    }
  }
  
  return {
    hasSensitiveData: sensitiveParams.length > 0,
    sensitiveParams,
    securityLevel
  }
}

/**
 * 安全日志记录
 */
export function logSecurityEvent(
  type: 'SENSITIVE_URL' | 'BLOCKED_REQUEST' | 'SUSPICIOUS_ACTIVITY',
  request: NextRequest,
  details: any
) {
  const timestamp = new Date().toISOString()
  const ip = request.headers.get('x-forwarded-for') || 
             request.headers.get('x-real-ip') || 
             'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  const logEntry = {
    timestamp,
    type,
    ip,
    userAgent,
    url: request.url,
    method: request.method,
    details
  }
  
  // 根据严重程度选择日志级别
  switch (details.securityLevel) {
    case 'critical':
      console.error('🚨 CRITICAL SECURITY EVENT:', JSON.stringify(logEntry, null, 2))
      break
    case 'high':
      console.warn('⚠️ HIGH SECURITY EVENT:', JSON.stringify(logEntry, null, 2))
      break
    case 'medium':
      console.warn('⚠️ MEDIUM SECURITY EVENT:', JSON.stringify(logEntry, null, 2))
      break
    default:
      console.log('ℹ️ SECURITY EVENT:', JSON.stringify(logEntry, null, 2))
  }
  
  // TODO: 在生产环境中，应该将这些日志发送到安全监控系统
}

/**
 * 生成安全响应
 */
export function createSecurityResponse(
  message: string,
  securityLevel: 'low' | 'medium' | 'high' | 'critical',
  additionalHeaders: Record<string, string> = {}
): NextResponse {
  const statusCode = securityLevel === 'critical' ? 403 : 400
  
  const response = NextResponse.json({
    success: false,
    message,
    error: 'SECURITY_VIOLATION',
    securityLevel,
    timestamp: new Date().toISOString()
  }, { status: statusCode })
  
  // 添加安全头
  response.headers.set('X-Security-Warning', 'Potential security violation detected')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  
  // 添加自定义头
  Object.entries(additionalHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  return response
}

/**
 * 安全中间件主函数
 */
export function withSecurity(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    const url = new URL(request.url)
    const pathname = url.pathname
    
    // 检查是否是敏感路径
    const isSensitivePath = SENSITIVE_PATHS.some(path => pathname.startsWith(path))
    
    if (isSensitivePath) {
      // 检测URL中的敏感数据
      const detection = detectSensitiveDataInUrl(request)
      
      if (detection.hasSensitiveData) {
        // 记录安全事件
        logSecurityEvent('SENSITIVE_URL', request, {
          sensitiveParams: detection.sensitiveParams,
          securityLevel: detection.securityLevel,
          path: pathname
        })
        
        // 如果是关键安全问题，直接阻止请求
        if (detection.securityLevel === 'critical') {
          return createSecurityResponse(
            '检测到敏感信息通过URL传输，请求已被阻止',
            'critical',
            {
              'X-Blocked-Reason': 'Sensitive data in URL',
              'X-Sensitive-Params': detection.sensitiveParams.join(', ')
            }
          )
        }
        
        // 对于其他级别，记录警告但允许继续
        console.warn(`⚠️ 检测到敏感参数在URL中: ${detection.sensitiveParams.join(', ')}`)
      }
    }
    
    // 检查请求方法和路径的组合
    if (pathname === '/api/auth/login' && request.method === 'GET') {
      logSecurityEvent('BLOCKED_REQUEST', request, {
        reason: 'GET method not allowed for login',
        securityLevel: 'high'
      })
      
      return createSecurityResponse(
        '不允许使用GET方法进行登录，这是不安全的做法',
        'high',
        {
          'Allow': 'POST',
          'X-Blocked-Reason': 'Unsafe login method'
        }
      )
    }
    
    // 继续执行原始处理器
    return handler(request, context)
  }
}

/**
 * 密码安全检查
 */
export function validatePasswordSecurity(password: string): {
  isSecure: boolean
  issues: string[]
  score: number
} {
  const issues: string[] = []
  let score = 0
  
  // 长度检查
  if (password.length < 8) {
    issues.push('密码长度至少需要8位')
  } else if (password.length >= 12) {
    score += 2
  } else {
    score += 1
  }
  
  // 复杂度检查
  if (!/[a-z]/.test(password)) {
    issues.push('密码需要包含小写字母')
  } else {
    score += 1
  }
  
  if (!/[A-Z]/.test(password)) {
    issues.push('密码需要包含大写字母')
  } else {
    score += 1
  }
  
  if (!/[0-9]/.test(password)) {
    issues.push('密码需要包含数字')
  } else {
    score += 1
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    issues.push('密码需要包含特殊字符')
  } else {
    score += 1
  }
  
  // 常见密码检查
  const commonPasswords = [
    'password', '123456', 'admin', 'qwerty', 'letmein',
    'welcome', 'monkey', '1234567890', 'password123'
  ]
  
  if (commonPasswords.includes(password.toLowerCase())) {
    issues.push('不能使用常见密码')
    score = 0
  }
  
  return {
    isSecure: issues.length === 0 && score >= 4,
    issues,
    score
  }
}

/**
 * IP地址安全检查
 */
export function checkIpSecurity(ip: string): {
  isSafe: boolean
  reason?: string
  riskLevel: 'low' | 'medium' | 'high'
} {
  // 简单的IP黑名单检查（实际应用中应该使用更完善的威胁情报）
  const suspiciousIps = [
    '0.0.0.0',
    '127.0.0.1', // 本地回环（在某些情况下可能是可疑的）
  ]
  
  // 检查是否是内网IP
  const isPrivateIp = /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/.test(ip)
  
  if (suspiciousIps.includes(ip)) {
    return {
      isSafe: false,
      reason: 'IP地址在黑名单中',
      riskLevel: 'high'
    }
  }
  
  if (isPrivateIp) {
    return {
      isSafe: true,
      reason: '内网IP地址',
      riskLevel: 'low'
    }
  }
  
  return {
    isSafe: true,
    riskLevel: 'low'
  }
}

export default {
  withSecurity,
  detectSensitiveDataInUrl,
  logSecurityEvent,
  createSecurityResponse,
  validatePasswordSecurity,
  checkIpSecurity
}
