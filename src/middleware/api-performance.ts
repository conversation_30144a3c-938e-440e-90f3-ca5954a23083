import { NextRequest, NextResponse } from 'next/server'

// API性能监控数据
interface ApiMetrics {
  endpoint: string
  method: string
  responseTime: number
  statusCode: number
  timestamp: number
  userAgent?: string
  ip?: string
  userId?: string
}

// 内存中存储最近的API调用记录
const apiMetricsStore: ApiMetrics[] = []
const MAX_METRICS_STORE = 1000

// 性能统计
const performanceStats = new Map<string, {
  totalCalls: number
  totalTime: number
  averageTime: number
  minTime: number
  maxTime: number
  errorCount: number
  lastCall: number
}>()

/**
 * API性能监控中间件
 */
export function withApiPerformance(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    const startTime = Date.now()
    const endpoint = request.nextUrl.pathname
    const method = request.method
    
    // 获取客户端信息
    const userAgent = request.headers.get('user-agent') || undefined
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'
    
    let response: NextResponse
    let statusCode = 200
    let userId: string | undefined
    
    try {
      // 执行原始处理器
      response = await handler(request, context)
      statusCode = response.status
      
      // 尝试从响应中获取用户ID（如果有的话）
      try {
        const responseData = await response.clone().json()
        userId = responseData.userId || responseData.user?.id
      } catch {
        // 忽略JSON解析错误
      }
      
    } catch (error) {
      console.error('API处理错误:', error)
      statusCode = 500
      response = NextResponse.json({
        success: false,
        message: '服务器内部错误'
      }, { status: 500 })
    }
    
    const endTime = Date.now()
    const responseTime = endTime - startTime
    
    // 记录性能指标
    const metrics: ApiMetrics = {
      endpoint,
      method,
      responseTime,
      statusCode,
      timestamp: startTime,
      userAgent,
      ip,
      userId
    }
    
    // 存储指标
    storeMetrics(metrics)
    
    // 更新统计信息
    updatePerformanceStats(endpoint, method, responseTime, statusCode)
    
    // 添加性能头信息
    response.headers.set('X-Response-Time', `${responseTime}ms`)
    response.headers.set('X-Timestamp', startTime.toString())
    
    // 慢API警告
    if (responseTime > 2000) {
      console.warn(`🐌 慢API检测: ${method} ${endpoint} - ${responseTime}ms`)
    }
    
    return response
  }
}

/**
 * 存储性能指标
 */
function storeMetrics(metrics: ApiMetrics) {
  apiMetricsStore.push(metrics)
  
  // 保持存储大小限制
  if (apiMetricsStore.length > MAX_METRICS_STORE) {
    apiMetricsStore.shift()
  }
}

/**
 * 更新性能统计
 */
function updatePerformanceStats(
  endpoint: string, 
  method: string, 
  responseTime: number, 
  statusCode: number
) {
  const key = `${method} ${endpoint}`
  const existing = performanceStats.get(key)
  
  if (existing) {
    existing.totalCalls++
    existing.totalTime += responseTime
    existing.averageTime = existing.totalTime / existing.totalCalls
    existing.minTime = Math.min(existing.minTime, responseTime)
    existing.maxTime = Math.max(existing.maxTime, responseTime)
    existing.lastCall = Date.now()
    
    if (statusCode >= 400) {
      existing.errorCount++
    }
  } else {
    performanceStats.set(key, {
      totalCalls: 1,
      totalTime: responseTime,
      averageTime: responseTime,
      minTime: responseTime,
      maxTime: responseTime,
      errorCount: statusCode >= 400 ? 1 : 0,
      lastCall: Date.now()
    })
  }
}

/**
 * 获取API性能统计
 */
export function getApiPerformanceStats() {
  const stats = Array.from(performanceStats.entries()).map(([key, data]) => ({
    endpoint: key,
    ...data,
    errorRate: data.totalCalls > 0 ? (data.errorCount / data.totalCalls) * 100 : 0
  }))
  
  return {
    totalEndpoints: stats.length,
    totalCalls: stats.reduce((sum, s) => sum + s.totalCalls, 0),
    averageResponseTime: stats.length > 0 
      ? stats.reduce((sum, s) => sum + s.averageTime, 0) / stats.length 
      : 0,
    slowestEndpoints: stats
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10),
    mostUsedEndpoints: stats
      .sort((a, b) => b.totalCalls - a.totalCalls)
      .slice(0, 10),
    errorProneEndpoints: stats
      .filter(s => s.errorRate > 0)
      .sort((a, b) => b.errorRate - a.errorRate)
      .slice(0, 10)
  }
}

/**
 * 获取最近的API调用记录
 */
export function getRecentApiCalls(limit: number = 100) {
  return apiMetricsStore
    .slice(-limit)
    .sort((a, b) => b.timestamp - a.timestamp)
}

/**
 * 获取特定时间范围的API指标
 */
export function getApiMetricsByTimeRange(
  startTime: number, 
  endTime: number
): ApiMetrics[] {
  return apiMetricsStore.filter(
    metric => metric.timestamp >= startTime && metric.timestamp <= endTime
  )
}

/**
 * 清理旧的性能数据
 */
export function cleanupOldMetrics(maxAge: number = 24 * 60 * 60 * 1000) {
  const cutoffTime = Date.now() - maxAge
  
  // 清理指标存储
  const initialLength = apiMetricsStore.length
  for (let i = apiMetricsStore.length - 1; i >= 0; i--) {
    const metric = apiMetricsStore[i]
    if (metric && metric.timestamp < cutoffTime) {
      apiMetricsStore.splice(i, 1)
    }
  }
  
  console.log(`🧹 清理了 ${initialLength - apiMetricsStore.length} 条旧的API指标`)
}

/**
 * API限流中间件
 */
export function withRateLimit(
  maxRequests: number = 100, 
  windowMs: number = 60 * 1000 // 1分钟
) {
  const requestCounts = new Map<string, { count: number; resetTime: number }>()
  
  return function(handler: Function) {
    return async (request: NextRequest, context?: any) => {
      const ip = request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown'
      
      const now = Date.now()
      const clientData = requestCounts.get(ip)
      
      if (clientData) {
        if (now > clientData.resetTime) {
          // 重置计数器
          clientData.count = 1
          clientData.resetTime = now + windowMs
        } else {
          clientData.count++
          
          if (clientData.count > maxRequests) {
            return NextResponse.json({
              success: false,
              message: '请求过于频繁，请稍后再试',
              retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
            }, { 
              status: 429,
              headers: {
                'Retry-After': Math.ceil((clientData.resetTime - now) / 1000).toString(),
                'X-RateLimit-Limit': maxRequests.toString(),
                'X-RateLimit-Remaining': Math.max(0, maxRequests - clientData.count).toString(),
                'X-RateLimit-Reset': clientData.resetTime.toString()
              }
            })
          }
        }
      } else {
        requestCounts.set(ip, {
          count: 1,
          resetTime: now + windowMs
        })
      }
      
      const response = await handler(request, context)
      
      // 添加限流头信息
      const currentData = requestCounts.get(ip)!
      response.headers.set('X-RateLimit-Limit', maxRequests.toString())
      response.headers.set('X-RateLimit-Remaining', Math.max(0, maxRequests - currentData.count).toString())
      response.headers.set('X-RateLimit-Reset', currentData.resetTime.toString())
      
      return response
    }
  }
}

/**
 * API缓存中间件
 */
export function withApiCache(ttl: number = 5 * 60 * 1000) { // 默认5分钟
  const cache = new Map<string, { data: any; expiry: number }>()
  
  return function(handler: Function) {
    return async (request: NextRequest, context?: any) => {
      // 只缓存GET请求
      if (request.method !== 'GET') {
        return handler(request, context)
      }
      
      const cacheKey = `${request.method}:${request.url}`
      const cached = cache.get(cacheKey)
      
      if (cached && Date.now() < cached.expiry) {
        console.log(`🎯 API缓存命中: ${cacheKey}`)
        const response = NextResponse.json(cached.data)
        response.headers.set('X-Cache', 'HIT')
        response.headers.set('X-Cache-TTL', Math.ceil((cached.expiry - Date.now()) / 1000).toString())
        return response
      }
      
      const response = await handler(request, context)
      
      // 只缓存成功的响应
      if (response.status === 200) {
        try {
          const responseData = await response.clone().json()
          cache.set(cacheKey, {
            data: responseData,
            expiry: Date.now() + ttl
          })
          
          response.headers.set('X-Cache', 'MISS')
          console.log(`💾 API缓存存储: ${cacheKey}`)
        } catch {
          // 忽略非JSON响应
        }
      }
      
      return response
    }
  }
}

// 定期清理旧数据
setInterval(() => {
  cleanupOldMetrics()
}, 60 * 60 * 1000) // 每小时清理一次
