// 监管规则相关类型定义（基于实际数据库表结构）

// 规则类型（基于数据库约束）
export type RuleType = 'SQL' | 'DSL' | 'JAVASCRIPT'

// 规则分类（基于数据库约束）
export type RuleCategory = 
  | 'COST_CONTROL'        // 费用控制
  | 'FRAUD_DETECTION'     // 欺诈检测
  | 'COMPLIANCE_CHECK'    // 合规检查
  | 'QUALITY_ASSURANCE'   // 质量保证
  | 'STATISTICAL_ANALYSIS' // 统计分析

// 严重程度（基于数据库约束）
export type SeverityLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

// 创建来源（基于数据库约束）
export type CreatedFrom = 'MANUAL' | 'TEMPLATE' | 'IMPORT' | 'SYSTEM'

// 执行状态（基于数据库约束）
export type ExecutionStatus = 'RUNNING' | 'SUCCESS' | 'FAILED' | 'TIMEOUT' | 'CANCELLED'

// 结果类型（基于数据库约束）
export type ResultType = 'VIOLATION' | 'SUSPICIOUS' | 'NORMAL' | 'ERROR'

// 风险等级（基于数据库约束）
export type RiskLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

// 结果状态（基于数据库约束）
export type ResultStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'IGNORED'

// 审核类型（基于数据库约束）
export type AuditType = 'INITIAL' | 'REVIEW' | 'APPEAL' | 'FINAL'

// 审核状态（基于数据库约束）
export type AuditStatus = 'PENDING' | 'AUDITING' | 'COMPLETED' | 'SUSPENDED'

// 审核结论（基于数据库约束）
export type AuditConclusion = 'CONFIRMED' | 'REJECTED' | 'PARTIAL' | 'DEFERRED'

// 审核级别（基于数据库约束）
export type AuditLevel = 'FIRST' | 'SECOND' | 'THIRD' | 'FINAL'

// 申诉状态（基于数据库约束）
export type AppealStatus = 'NONE' | 'SUBMITTED' | 'PROCESSING' | 'ACCEPTED' | 'REJECTED'

// 最终决定（基于数据库约束）
export type FinalDecision = 'CONFIRMED' | 'OVERTURNED' | 'MODIFIED' | 'DISMISSED'

// 监管规则主表（基于RULE_SUPERVISION表结构）
export interface SupervisionRule {
  id: number
  ruleCode: string                    // 规则编码 RULE_CODE
  ruleName: string                    // 规则名称 RULE_NAME
  ruleType: RuleType                  // 规则类型 RULE_TYPE
  ruleCategory: RuleCategory          // 规则分类 RULE_CATEGORY
  description?: string                // 规则描述 DESCRIPTION
  ruleContent: string                 // 规则内容 RULE_CONTENT
  ruleSql?: string                    // SQL查询语句 RULE_SQL
  ruleDsl?: string                    // DSL规则表达式 RULE_DSL
  priorityLevel: number               // 优先级(1-10) PRIORITY_LEVEL
  severityLevel: SeverityLevel        // 严重程度 SEVERITY_LEVEL
  isActive: boolean                   // 是否启用 IS_ACTIVE
  effectiveDate: string               // 生效日期 EFFECTIVE_DATE
  expiryDate?: string                 // 失效日期 EXPIRY_DATE
  versionNumber: string               // 版本号 VERSION_NUMBER
  parentRuleId?: number               // 父规则ID PARENT_RULE_ID
  ruleSource?: string                 // 规则出处 RULE_SOURCE
  createdFrom: CreatedFrom            // 创建来源 CREATED_FROM
  executionCount: number              // 执行次数 EXECUTION_COUNT
  successCount: number                // 成功次数 SUCCESS_COUNT
  lastExecutedAt?: string             // 最后执行时间 LAST_EXECUTED_AT
  isDeleted: boolean                  // 是否删除 IS_DELETED
  createdAt: string                   // 创建时间 CREATED_AT
  updatedAt: string                   // 更新时间 UPDATED_AT
  createdBy?: number                  // 创建人 CREATED_BY
  updatedBy?: number                  // 更新人 UPDATED_BY

  // 关联数据
  executionLogs?: RuleExecutionLog[]      // 执行记录
  executionResults?: RuleExecutionResult[] // 执行结果
  parentRule?: SupervisionRule            // 父规则
  childRules?: SupervisionRule[]          // 子规则
}

// 规则执行记录（基于RULE_EXECUTION_LOG表结构）
export interface RuleExecutionLog {
  id: number
  ruleId: number                      // 规则ID RULE_ID
  executionId: string                 // 执行批次ID EXECUTION_ID
  executionStatus: ExecutionStatus    // 执行状态 EXECUTION_STATUS
  startedAt: string                   // 开始时间 STARTED_AT
  endedAt?: string                    // 结束时间 ENDED_AT
  executionDuration?: number          // 执行时长(毫秒) EXECUTION_DURATION
  processedRecordCount: number        // 处理记录数 PROCESSED_RECORD_COUNT
  matchedRecordCount: number          // 匹配记录数 MATCHED_RECORD_COUNT
  errorMessage?: string               // 错误信息 ERROR_MESSAGE
  executionParams?: string            // 执行参数 EXECUTION_PARAMS
  executionResult?: string            // 执行结果 EXECUTION_RESULT
  executedBy?: number                 // 执行人 EXECUTED_BY
  createdAt: string                   // 创建时间 CREATED_AT

  // 关联数据
  rule?: SupervisionRule              // 关联规则
  results?: RuleExecutionResult[]     // 执行结果
}

// 规则模板（基于RULE_TEMPLATE表结构）
export interface RuleTemplate {
  id: number
  templateCode: string                // 模板编码 TEMPLATE_CODE
  templateName: string                // 模板名称 TEMPLATE_NAME
  templateCategory: RuleCategory      // 模板分类 TEMPLATE_CATEGORY
  description?: string                // 模板描述 DESCRIPTION
  templateContent: string             // 模板内容 TEMPLATE_CONTENT
  parameterSchema?: string            // 参数结构定义 PARAMETER_SCHEMA
  usageCount: number                  // 使用次数 USAGE_COUNT
  isSystemTemplate: boolean           // 是否系统模板 IS_SYSTEM_TEMPLATE
  isActive: boolean                   // 是否启用 IS_ACTIVE
  isDeleted: boolean                  // 是否删除 IS_DELETED
  createdAt: string                   // 创建时间 CREATED_AT
  updatedAt: string                   // 更新时间 UPDATED_AT
  createdBy?: number                  // 创建人 CREATED_BY
  updatedBy?: number                  // 更新人 UPDATED_BY
}

// 规则执行结果（基于RULE_EXECUTION_RESULT表结构）
export interface RuleExecutionResult {
  id: number
  executionLogId: number              // 执行记录ID EXECUTION_LOG_ID
  ruleId: number                      // 规则ID RULE_ID
  caseId?: number                     // 病例ID CASE_ID
  resultType: ResultType              // 结果类型 RESULT_TYPE
  riskLevel: RiskLevel                // 风险等级 RISK_LEVEL
  violationDescription?: string       // 违规描述 VIOLATION_DESCRIPTION
  violationAmount: number             // 违规金额 VIOLATION_AMOUNT
  evidenceData?: string               // 证据数据 EVIDENCE_DATA
  ruleMatchedFields?: string          // 规则匹配字段 RULE_MATCHED_FIELDS
  confidenceScore: number             // 置信度分数(0-100) CONFIDENCE_SCORE
  resultStatus: ResultStatus          // 结果状态 RESULT_STATUS
  isAutoProcessed: boolean            // 是否自动处理 IS_AUTO_PROCESSED
  isFollowUpRequired: boolean         // 是否需要跟进 IS_FOLLOW_UP_REQUIRED
  relatedCaseCount: number            // 关联病例数量 RELATED_CASE_COUNT
  createdAt: string                   // 创建时间 CREATED_AT
  updatedAt: string                   // 更新时间 UPDATED_AT
  createdBy?: number                  // 创建人 CREATED_BY
  updatedBy?: number                  // 更新人 UPDATED_BY

  // 关联数据
  executionLog?: RuleExecutionLog     // 执行记录
  rule?: SupervisionRule              // 规则
  auditResults?: RuleAuditResult[]    // 审核结果
}

// 规则审核结果（基于RULE_AUDIT_RESULT表结构）
export interface RuleAuditResult {
  id: number
  executionResultId: number           // 执行结果ID EXECUTION_RESULT_ID
  auditType: AuditType                // 审核类型 AUDIT_TYPE
  auditStatus: AuditStatus            // 审核状态 AUDIT_STATUS
  auditConclusion: AuditConclusion    // 审核结论 AUDIT_CONCLUSION
  auditOpinion?: string               // 审核意见 AUDIT_OPINION
  correctiveAction?: string           // 整改措施 CORRECTIVE_ACTION
  penaltyAmount: number               // 处罚金额 PENALTY_AMOUNT
  recoveryAmount: number              // 追回金额 RECOVERY_AMOUNT
  auditEvidence?: string              // 审核证据 AUDIT_EVIDENCE
  auditLevel: AuditLevel              // 审核级别 AUDIT_LEVEL
  appealStatus?: AppealStatus         // 申诉状态 APPEAL_STATUS
  appealDeadline?: string             // 申诉截止日期 APPEAL_DEADLINE
  finalDecision?: FinalDecision       // 最终决定 FINAL_DECISION
  decisionDate?: string               // 决定日期 DECISION_DATE
  auditorId: number                   // 审核员ID AUDITOR_ID
  auditorName: string                 // 审核员姓名 AUDITOR_NAME
  auditDepartment?: string            // 审核部门 AUDIT_DEPARTMENT
  auditStartDate: string              // 审核开始日期 AUDIT_START_DATE
  auditEndDate?: string               // 审核结束日期 AUDIT_END_DATE
  isDeleted: boolean                  // 是否删除 IS_DELETED
  createdAt: string                   // 创建时间 CREATED_AT
  updatedAt: string                   // 更新时间 UPDATED_AT
  createdBy?: number                  // 创建人 CREATED_BY
  updatedBy?: number                  // 更新人 UPDATED_BY

  // 关联数据
  executionResult?: RuleExecutionResult // 执行结果
}

// 规则查询参数
export interface RuleListParams {
  page?: number
  pageSize?: number
  search?: string                     // 搜索关键词
  ruleType?: RuleType                 // 规则类型筛选
  ruleCategory?: RuleCategory         // 规则分类筛选
  severityLevel?: SeverityLevel       // 严重程度筛选
  isActive?: boolean                  // 是否启用筛选
  createdFrom?: CreatedFrom           // 创建来源筛选
  startDate?: string                  // 开始日期
  endDate?: string                    // 结束日期
  sortBy?: string                     // 排序字段
  sortOrder?: 'ASC' | 'DESC'          // 排序方向
  // 扩展筛选字段
  minPriority?: number                // 最低优先级
  maxPriority?: number                // 最高优先级
  createdAfter?: string               // 创建日期起始
  createdBefore?: string              // 创建日期结束
  tags?: string                       // 标签筛选
  hasExecutions?: boolean             // 是否有执行记录
  recentlyModified?: boolean          // 是否最近修改
}

// 规则创建请求
export interface RuleCreateRequest {
  ruleCode: string
  ruleName: string
  ruleType: RuleType
  ruleCategory: RuleCategory
  description?: string
  ruleContent: string
  ruleSql?: string
  ruleDsl?: string
  priorityLevel: number
  severityLevel: SeverityLevel
  effectiveDate: string
  expiryDate?: string
  versionNumber: string
  parentRuleId?: number
  ruleSource?: string
  createdFrom: CreatedFrom
}

// 规则更新请求
export interface RuleUpdateRequest {
  ruleName?: string
  ruleCategory?: RuleCategory
  description?: string
  ruleContent?: string
  ruleSql?: string
  ruleDsl?: string
  priorityLevel?: number
  severityLevel?: SeverityLevel
  isActive?: boolean
  effectiveDate?: string
  expiryDate?: string
  ruleSource?: string
}

// 规则执行请求
export interface RuleExecutionRequest {
  ruleIds: number[]                   // 要执行的规则ID列表
  executionParams?: Record<string, any> // 执行参数
  targetCaseIds?: number[]            // 目标案例ID（可选）
  isScheduled?: boolean               // 是否定时执行
  scheduledTime?: string              // 定时执行时间
}

// 规则统计信息
export interface RuleStatistics {
  totalRules: number
  activeRules: number
  inactiveRules: number
  rulesByType: Record<RuleType, number>
  rulesByCategory: Record<RuleCategory, number>
  rulesBySeverity: Record<SeverityLevel, number>
  executionStats: {
    totalExecutions: number
    successfulExecutions: number
    failedExecutions: number
    avgExecutionTime: number
  }
  resultStats: {
    totalResults: number
    violationResults: number
    suspiciousResults: number
    normalResults: number
    avgConfidenceScore: number
  }
  monthlyTrend: {
    month: string
    executionCount: number
    violationCount: number
    avgConfidenceScore: number
  }[]
}

// 分页响应
export interface RulePaginationResponse {
  items: SupervisionRule[]
  page: number
  pageSize: number
  total: number
  totalPages: number
}

// API响应类型
export interface RuleApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}
