/**
 * 规则模板相关类型定义
 */

// 模板类型
export type TemplateType = 'SQL' | 'DSL' | 'JAVASCRIPT'

// 规则分类
export type RuleCategory = 
  | 'COST_CONTROL'        // 费用控制
  | 'FRAUD_DETECTION'     // 欺诈检测
  | 'COMPLIANCE_CHECK'    // 合规检查
  | 'QUALITY_ASSURANCE'   // 质量保证
  | 'STATISTICAL_ANALYSIS' // 统计分析

// 严重程度
export type SeverityLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

// 使用类型
export type UsageType = 'CREATE_RULE' | 'PREVIEW' | 'TEST' | 'EXPORT'

// 执行状态
export type SuccessStatus = 'SUCCESS' | 'FAILED' | 'PARTIAL'

// 模板分类接口
export interface RuleTemplateCategory {
  id: number
  categoryCode: string
  categoryName: string
  categoryDesc?: string
  parentCategoryId?: number
  categoryLevel: number
  sortOrder: number
  iconName?: string
  colorScheme?: string
  isActive: boolean
  isDeleted: boolean
  createdAt: string
  updatedAt: string
  createdBy?: number
  updatedBy?: number
  // 关联数据
  children?: RuleTemplateCategory[]
  templateCount?: number
}

// 参数定义接口
export interface TemplateParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object'
  required: boolean
  defaultValue?: any
  description?: string
  placeholder?: string
  validation?: {
    min?: number
    max?: number
    pattern?: string
    options?: any[]
    message?: string
  }
  displayName?: string
  group?: string
}

// 参数结构定义
export interface ParameterSchema {
  version: string
  parameters: TemplateParameter[]
  groups?: {
    name: string
    title: string
    description?: string
    parameters: string[]
  }[]
}

// 预览数据接口
export interface PreviewData {
  sampleInput?: Record<string, any>
  sampleOutput?: {
    description: string
    matchedRecords: number
    riskLevel: SeverityLevel
    evidence: string[]
  }
  screenshots?: string[]
  documentation?: {
    usage: string
    examples: string[]
    notes?: string[]
  }
}

// 规则模板接口
export interface RuleTemplate {
  id: number
  templateCode: string
  templateName: string
  templateDesc?: string
  categoryId: number
  templateType: TemplateType
  ruleCategory: RuleCategory
  severityLevel: SeverityLevel
  priorityLevel: number
  templateContent: string
  templateSql?: string
  templateDsl?: string
  parameterSchema?: ParameterSchema
  usageCount: number
  successRate: number
  lastUsedAt?: string
  tags?: string[]
  previewData?: PreviewData
  isOfficial: boolean
  isFeatured: boolean
  isActive: boolean
  isDeleted: boolean
  versionNumber: string
  createdAt: string
  updatedAt: string
  createdBy?: number
  updatedBy?: number
  // 关联数据
  category?: RuleTemplateCategory
  averageRating?: number
  ratingCount?: number
  userRating?: number
}

// 模板使用记录接口
export interface RuleTemplateUsage {
  id: number
  templateId: number
  ruleId?: number
  userId: number
  usageType: UsageType
  parametersUsed?: Record<string, any>
  successStatus: SuccessStatus
  errorMessage?: string
  executionTime?: number
  createdAt: string
  // 关联数据
  template?: RuleTemplate
  user?: {
    id: number
    username: string
    realName: string
  }
  rule?: {
    id: number
    ruleName: string
    ruleCode: string
  }
}

// 模板评价接口
export interface RuleTemplateRating {
  id: number
  templateId: number
  userId: number
  ratingScore: number
  ratingComment?: string
  isHelpful: boolean
  createdAt: string
  updatedAt: string
  // 关联数据
  user?: {
    id: number
    username: string
    realName: string
  }
}

// 模板列表查询参数
export interface TemplateListParams {
  page?: number
  pageSize?: number
  search?: string
  categoryId?: number
  templateType?: TemplateType
  ruleCategory?: RuleCategory
  severityLevel?: SeverityLevel
  isOfficial?: boolean
  isFeatured?: boolean
  tags?: string[]
  sortBy?: 'usageCount' | 'successRate' | 'averageRating' | 'createdAt' | 'updatedAt'
  sortOrder?: 'ASC' | 'DESC'
}

// 模板分页响应
export interface TemplatePaginationResponse {
  data: RuleTemplate[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  aggregations?: {
    categoryDistribution: Record<string, number>
    typeDistribution: Record<string, number>
    severityDistribution: Record<string, number>
    tagCloud: { tag: string; count: number }[]
  }
}

// 模板创建/更新请求
export interface TemplateCreateRequest {
  templateCode: string
  templateName: string
  templateDesc?: string
  categoryId: number
  templateType: TemplateType
  ruleCategory: RuleCategory
  severityLevel: SeverityLevel
  priorityLevel: number
  templateContent: string
  templateSql?: string
  templateDsl?: string
  parameterSchema?: ParameterSchema
  tags?: string[]
  previewData?: PreviewData
  isOfficial?: boolean
  isFeatured?: boolean
  versionNumber?: string
}

export interface TemplateUpdateRequest extends Partial<TemplateCreateRequest> {
  id: number
}

// 从模板创建规则请求
export interface CreateRuleFromTemplateRequest {
  templateId: number
  ruleName: string
  ruleCode?: string
  description?: string
  parameters: Record<string, any>
  effectiveDate?: string
  expiryDate?: string
  isActive?: boolean
}

// 模板统计信息
export interface TemplateStatistics {
  totalTemplates: number
  officialTemplates: number
  featuredTemplates: number
  averageUsageCount: number
  averageSuccessRate: number
  averageRating: number
  categoryDistribution: Record<string, number>
  typeDistribution: Record<string, number>
  popularTemplates: RuleTemplate[]
  recentTemplates: RuleTemplate[]
}

// 模板验证结果
export interface TemplateValidationResult {
  isValid: boolean
  errors: {
    field: string
    message: string
    code: string
  }[]
  warnings: {
    field: string
    message: string
    code: string
  }[]
  suggestions?: string[]
}

// 模板导入/导出
export interface TemplateExportData {
  version: string
  exportedAt: string
  templates: RuleTemplate[]
  categories: RuleTemplateCategory[]
  metadata: {
    totalCount: number
    exportedBy: number
    description?: string
  }
}

export interface TemplateImportRequest {
  data: TemplateExportData
  options: {
    overwriteExisting: boolean
    createCategories: boolean
    validateBeforeImport: boolean
  }
}

export interface TemplateImportResult {
  success: boolean
  imported: {
    templates: number
    categories: number
  }
  skipped: {
    templates: number
    categories: number
    reasons: string[]
  }
  errors: {
    message: string
    details?: any
  }[]
}
