// 用户角色类型
export type UserRole = 'ADMIN' | 'SUPERVISOR' | 'OPERATOR' | 'AUDITOR' | 'VIEWER'

// 用户状态类型
export type UserStatus = 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'LOCKED'

// 用户信息接口
export interface User {
  id: number
  username: string
  realName: string
  email: string
  department?: string
  position?: string
  status: UserStatus
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  roles: Role[]
}

// 角色信息接口
export interface Role {
  id: number
  roleCode: UserRole
  roleName: string
  description?: string
  isActive: boolean
  permissions: Permission[]
}

// 权限信息接口
export interface Permission {
  id: number
  code: string
  name: string
  description?: string
  module: string
  action: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 权限模块类型
export type PermissionModule =
  | 'USER_MANAGEMENT'
  | 'MEDICAL_CASE'
  | 'SUPERVISION_RULE'
  | 'KNOWLEDGE_BASE'
  | 'ANALYTICS'
  | 'SYSTEM_SETTINGS'

// 权限操作类型
export type PermissionAction =
  | 'CREATE'
  | 'READ'
  | 'UPDATE'
  | 'DELETE'
  | 'EXPORT'
  | 'IMPORT'
  | 'APPROVE'
  | 'REJECT'
  | 'EXECUTE'

// 权限组接口
export interface PermissionGroup {
  module: PermissionModule
  moduleName: string
  permissions: Permission[]
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  message: string
  data?: {
    user: User
    token: string
    refreshToken: string
    expiresIn: number
  }
}

// 注册请求接口
export interface RegisterRequest {
  username: string
  password: string
  confirmPassword: string
  realName: string
  email: string
  department?: string
  position?: string
  roleIds?: number[]
}

// JWT Token载荷接口
export interface JWTPayload {
  userId: number
  username: string
  roles: UserRole[]
  iat: number
  exp: number
}

// 认证上下文接口
export interface AuthContextType {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (credentials: LoginRequest) => Promise<LoginResponse>
  logout: () => void
  register: (data: RegisterRequest) => Promise<LoginResponse>
  refreshToken: () => Promise<boolean>
  hasPermission: (permission: string) => boolean
  hasRole: (role: UserRole) => boolean
}

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  code?: number
}

// 分页请求接口
export interface PaginationRequest {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  search?: string
}

// 分页响应接口
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 用户列表查询参数
export interface UserListParams extends PaginationRequest {
  status?: UserStatus
  role?: UserRole
  department?: string
}

// 用户列表响应接口
export interface UserListResponse {
  users: User[]
  pagination: {
    page: number
    pageSize: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 密码重置请求接口
export interface PasswordResetRequest {
  email: string
}

// 密码更改请求接口
export interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 用户更新请求接口
export interface UserUpdateRequest {
  realName?: string
  email?: string
  department?: string
  position?: string
  status?: UserStatus
  roleIds?: number[]
}

// 角色分配请求接口
export interface RoleAssignRequest {
  userId: number
  roleIds: number[]
}

// 角色创建请求接口
export interface RoleCreateRequest {
  roleCode: UserRole
  roleName: string
  description?: string
  permissionIds: number[]
}

// 角色更新请求接口
export interface RoleUpdateRequest {
  roleName?: string
  description?: string
  isActive?: boolean
  permissionIds?: number[]
}

// 权限创建请求接口
export interface PermissionCreateRequest {
  code: string
  name: string
  description?: string
  module: PermissionModule
  action: PermissionAction
}

// 权限更新请求接口
export interface PermissionUpdateRequest {
  name?: string
  description?: string
  isActive?: boolean
}

// 角色权限矩阵接口
export interface RolePermissionMatrix {
  roleId: number
  roleCode: UserRole
  roleName: string
  permissions: {
    [module: string]: {
      [action: string]: boolean
    }
  }
}
