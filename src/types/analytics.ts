// 数据分析报表相关类型定义

// 报表类型
export type ReportType = 
  | 'CASE_SUMMARY'        // 案例汇总报表
  | 'COST_ANALYSIS'       // 费用分析报表
  | 'RULE_PERFORMANCE'    // 规则执行效果报表
  | 'VIOLATION_ANALYSIS'  // 违规分析报表
  | 'HOSPITAL_RANKING'    // 医院排名报表
  | 'TREND_ANALYSIS'      // 趋势分析报表
  | 'CUSTOM'              // 自定义报表

// 时间范围类型
export type TimeRange = 
  | 'TODAY'               // 今天
  | 'YESTERDAY'           // 昨天
  | 'LAST_7_DAYS'         // 最近7天
  | 'LAST_30_DAYS'        // 最近30天
  | 'LAST_90_DAYS'        // 最近90天
  | 'THIS_MONTH'          // 本月
  | 'LAST_MONTH'          // 上月
  | 'THIS_QUARTER'        // 本季度
  | 'LAST_QUARTER'        // 上季度
  | 'THIS_YEAR'           // 今年
  | 'LAST_YEAR'           // 去年
  | 'CUSTOM'              // 自定义时间范围

// 图表类型
export type ChartType = 
  | 'LINE'                // 折线图
  | 'BAR'                 // 柱状图
  | 'PIE'                 // 饼图
  | 'AREA'                // 面积图
  | 'SCATTER'             // 散点图
  | 'HEATMAP'             // 热力图
  | 'TABLE'               // 表格

// 聚合类型
export type AggregationType = 
  | 'COUNT'               // 计数
  | 'SUM'                 // 求和
  | 'AVG'                 // 平均值
  | 'MAX'                 // 最大值
  | 'MIN'                 // 最小值
  | 'DISTINCT_COUNT'      // 去重计数

// 数据维度
export type DataDimension = 
  | 'TIME'                // 时间维度
  | 'HOSPITAL'            // 医院维度
  | 'DEPARTMENT'          // 科室维度
  | 'CASE_TYPE'           // 案例类型维度
  | 'MEDICAL_CATEGORY'    // 医疗类别维度
  | 'RULE_CATEGORY'       // 规则分类维度
  | 'SEVERITY_LEVEL'      // 严重程度维度
  | 'RISK_LEVEL'          // 风险等级维度

// 基础统计数据
export interface BaseStatistics {
  totalCases: number                    // 总案例数
  totalCost: number                     // 总费用
  avgCostPerCase: number                // 平均每案例费用
  totalViolations: number               // 总违规数
  violationRate: number                 // 违规率
  totalRecoveryAmount: number           // 总追回金额
  totalPenaltyAmount: number            // 总处罚金额
  activeRules: number                   // 活跃规则数
  ruleExecutions: number                // 规则执行次数
  ruleSuccessRate: number               // 规则执行成功率
}

// 时间序列数据点
export interface TimeSeriesDataPoint {
  date: string                          // 日期
  value: number                         // 数值
  label?: string                        // 标签
  category?: string                     // 分类
}

// 分类统计数据
export interface CategoryStatistics {
  category: string                      // 分类名称
  count: number                         // 数量
  percentage: number                    // 百分比
  value: number                         // 数值
  trend?: 'UP' | 'DOWN' | 'STABLE'      // 趋势
  changeRate?: number                   // 变化率
}

// 排名数据
export interface RankingData {
  rank: number                          // 排名
  name: string                          // 名称
  code?: string                         // 编码
  value: number                         // 数值
  count: number                         // 数量
  percentage: number                    // 百分比
  trend?: 'UP' | 'DOWN' | 'STABLE'      // 趋势
  changeFromLastPeriod?: number         // 与上期对比变化
}

// 案例汇总报表
export interface CaseSummaryReport {
  baseStats: BaseStatistics
  casesByType: CategoryStatistics[]
  casesByCategory: CategoryStatistics[]
  casesByHospital: RankingData[]
  monthlyTrend: TimeSeriesDataPoint[]
  costDistribution: CategoryStatistics[]
  topDepartments: RankingData[]
}

// 费用分析报表
export interface CostAnalysisReport {
  totalCostStats: {
    totalCost: number
    avgCost: number
    medianCost: number
    maxCost: number
    minCost: number
  }
  costByType: CategoryStatistics[]
  costByCategory: CategoryStatistics[]
  costTrend: TimeSeriesDataPoint[]
  hospitalCostRanking: RankingData[]
  departmentCostRanking: RankingData[]
  costDistributionByRange: CategoryStatistics[]
  abnormalCostCases: {
    caseId: number
    caseNumber: string
    patientName: string
    hospitalName: string
    totalCost: number
    avgCostInCategory: number
    deviationRate: number
  }[]
}

// 规则执行效果报表
export interface RulePerformanceReport {
  executionStats: {
    totalExecutions: number
    successfulExecutions: number
    failedExecutions: number
    avgExecutionTime: number
    successRate: number
  }
  rulesByCategory: CategoryStatistics[]
  rulesBySeverity: CategoryStatistics[]
  executionTrend: TimeSeriesDataPoint[]
  topPerformingRules: RankingData[]
  ruleEffectiveness: {
    ruleId: number
    ruleName: string
    ruleCode: string
    executionCount: number
    violationCount: number
    effectivenessRate: number
    avgConfidenceScore: number
  }[]
}

// 违规分析报表
export interface ViolationAnalysisReport {
  violationStats: {
    totalViolations: number
    violationRate: number
    totalViolationAmount: number
    avgViolationAmount: number
    totalRecoveryAmount: number
    recoveryRate: number
  }
  violationsByType: CategoryStatistics[]
  violationsByRisk: CategoryStatistics[]
  violationTrend: TimeSeriesDataPoint[]
  hospitalViolationRanking: RankingData[]
  ruleViolationRanking: RankingData[]
  violationsByMonth: TimeSeriesDataPoint[]
  topViolationCases: {
    caseId: number
    caseNumber: string
    patientName: string
    hospitalName: string
    violationAmount: number
    riskLevel: string
    ruleName: string
    confidenceScore: number
  }[]
}

// 医院排名报表
export interface HospitalRankingReport {
  hospitalStats: {
    totalHospitals: number
    avgCasesPerHospital: number
    avgCostPerHospital: number
    avgViolationRate: number
  }
  hospitalRankingByCases: RankingData[]
  hospitalRankingByCost: RankingData[]
  hospitalRankingByViolations: RankingData[]
  hospitalPerformanceMatrix: {
    hospitalName: string
    hospitalCode: string
    caseCount: number
    totalCost: number
    violationCount: number
    violationRate: number
    avgCostPerCase: number
    riskScore: number
    performanceGrade: 'A' | 'B' | 'C' | 'D'
  }[]
}

// 趋势分析报表
export interface TrendAnalysisReport {
  caseCountTrend: TimeSeriesDataPoint[]
  costTrend: TimeSeriesDataPoint[]
  violationTrend: TimeSeriesDataPoint[]
  ruleExecutionTrend: TimeSeriesDataPoint[]
  seasonalAnalysis: {
    season: string
    avgCases: number
    avgCost: number
    avgViolations: number
    trend: 'UP' | 'DOWN' | 'STABLE'
  }[]
  yearOverYearComparison: {
    metric: string
    currentYear: number
    lastYear: number
    changeRate: number
    trend: 'UP' | 'DOWN' | 'STABLE'
  }[]
  forecastData: {
    date: string
    predictedCases: number
    predictedCost: number
    predictedViolations: number
    confidenceInterval: {
      lower: number
      upper: number
    }
  }[]
}

// 报表查询参数
export interface ReportQueryParams {
  reportType: ReportType
  timeRange: TimeRange
  startDate?: string
  endDate?: string
  hospitalCodes?: string[]
  departments?: string[]
  caseTypes?: string[]
  medicalCategories?: string[]
  ruleCategories?: string[]
  severityLevels?: string[]
  riskLevels?: string[]
  groupBy?: DataDimension[]
  aggregationType?: AggregationType
  limit?: number
  offset?: number
}

// 报表导出参数
export interface ReportExportParams {
  reportType: ReportType
  format: 'PDF' | 'EXCEL' | 'CSV'
  queryParams: ReportQueryParams
  includeCharts?: boolean
  chartTypes?: ChartType[]
  title?: string
  description?: string
}

// 实时监控数据
export interface RealTimeMonitoringData {
  timestamp: string
  activeCases: number
  pendingRules: number
  recentViolations: number
  systemLoad: number
  alertCount: number
  onlineUsers: number
  recentAlerts: {
    id: string
    type: 'VIOLATION' | 'SYSTEM' | 'RULE_FAILURE'
    message: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    timestamp: string
  }[]
}

// 异常监控配置
export interface AnomalyMonitoringConfig {
  enabled: boolean
  thresholds: {
    costAnomalyThreshold: number        // 费用异常阈值
    violationRateThreshold: number      // 违规率阈值
    ruleFailureRateThreshold: number    // 规则失败率阈值
    systemLoadThreshold: number         // 系统负载阈值
  }
  alertChannels: {
    email: boolean
    sms: boolean
    webhook: boolean
  }
  recipients: string[]
}

// API响应类型
export interface AnalyticsApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  generatedAt?: string
  queryTime?: number
}
