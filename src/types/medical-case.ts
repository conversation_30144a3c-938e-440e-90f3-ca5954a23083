// 医疗案例相关类型定义（基于实际数据库表结构）

// 案例类型（基于数据库约束）
export type CaseType = 'INPATIENT' | 'OUTPATIENT'

// 性别类型（基于数据库约束）
export type Gender = 'MALE' | 'FEMALE' | 'OTHER'

// 医疗类别（基于数据库约束）
export type MedicalCategory =
  | '异地住院' | '急诊住院' | '普通住院' | '门诊慢特病' | '转外诊治住院'
  | '生育住院' | '统筹区内转院' | '住院前急诊' | '特药购药' | '急诊转住院'
  | '普通门诊' | '药店购慢特病药' | '定点药店购药' | '村卫门诊' | '特药门诊'
  | '外伤住院' | '日间手术' | '急诊' | '急诊2级危重' | '急诊3级急症'
  | '急诊1级濒危' | '新冠门诊' | '4级非急症' | '辅助生殖门诊' | '急诊(死亡)'
  | '家庭医生签约'

// 诊断类型（基于数据库约束）
export type DiagnosisType = 'ADMISSION' | 'DISCHARGE' | 'DIFFERENTIAL'

// 手术级别（基于数据库注释）
export type SurgeryLevel = 'LEVEL1' | 'LEVEL2' | 'LEVEL3' | 'LEVEL4'

// 费用项目类型（基于数据库约束）
export type CostItemType =
  | '检查费' | '化验费' | '护理费' | '治疗费' | '卫生材料费' | '中成药费'
  | '手术费' | '其他费' | '中药饮片费' | '诊察费' | '西药费' | '床位费'

// 结算介质类型（基于数据库约束）
export type MediumType = '社保卡' | '医保卡' | '身份证' | '临时卡' | '其他'

// 分组类型（基于数据库约束）
export type GroupType = 'DRG' | 'DIP' | 'OTHER'

// 医疗案例主表（基于MEDICAL_CASE表结构）
export interface MedicalCase {
  id: number
  caseNumber: string           // 案例编号 CASE_NUMBER
  patientName: string          // 患者姓名 PATIENT_NAME
  patientIdCard: string        // 身份证号 PATIENT_ID_CARD
  patientPhone?: string        // 患者电话 PATIENT_PHONE
  patientAge?: number          // 患者年龄 PATIENT_AGE
  patientGender?: Gender       // 患者性别 PATIENT_GENDER
  caseType: CaseType          // 病例类型 CASE_TYPE
  medicalCategory: MedicalCategory // 医疗类别 MEDICAL_CATEGORY
  hospitalName: string         // 医院名称 HOSPITAL_NAME
  hospitalCode?: string        // 医院编码 HOSPITAL_CODE
  department?: string          // 科室 DEPARTMENT
  doctorName?: string          // 主治医生 DOCTOR_NAME
  admissionDate?: string       // 入院日期 ADMISSION_DATE
  dischargeDate?: string       // 出院日期 DISCHARGE_DATE
  totalCost: number           // 总费用 TOTAL_COST
  isDeleted: boolean          // 是否删除 IS_DELETED
  createdAt: string           // 创建时间 CREATED_AT
  updatedAt: string           // 更新时间 UPDATED_AT
  createdBy?: number          // 创建人 CREATED_BY
  updatedBy?: number          // 更新人 UPDATED_BY

  // 关联数据
  diagnoses?: MedicalDiagnosis[]     // 诊断信息
  surgeries?: MedicalSurgery[]       // 手术信息
  costDetails?: MedicalCostDetail[]  // 费用明细
  settlements?: MedicalSettlement[]  // 结算信息
  caseGroups?: MedicalCaseGroup[]    // 分组信息
}

// 诊断信息（基于MEDICAL_DIAGNOSIS表结构）
export interface MedicalDiagnosis {
  id: number
  caseId: number              // 病例ID CASE_ID
  diagnosisType: DiagnosisType // 诊断类型 DIAGNOSIS_TYPE
  diagnosisCode?: string      // 诊断编码 DIAGNOSIS_CODE
  diagnosisName: string       // 诊断名称 DIAGNOSIS_NAME
  diagnosisDesc?: string      // 诊断描述 DIAGNOSIS_DESC
  isPrimary: boolean          // 是否主要诊断 IS_PRIMARY
  createdAt: string           // 创建时间 CREATED_AT
  updatedAt: string           // 更新时间 UPDATED_AT
  createdBy?: number          // 创建人 CREATED_BY
  updatedBy?: number          // 更新人 UPDATED_BY
}

// 手术信息（基于MEDICAL_SURGERY表结构）
export interface MedicalSurgery {
  id: number
  caseId: number              // 病例ID CASE_ID
  surgeryCode?: string        // 手术编码 SURGERY_CODE
  surgeryName: string         // 手术名称 SURGERY_NAME
  surgeryDate: string         // 手术日期 SURGERY_DATE
  surgeonName?: string        // 主刀医生 SURGEON_NAME
  anesthesiaType?: string     // 麻醉方式 ANESTHESIA_TYPE
  surgeryLevel?: SurgeryLevel // 手术级别 SURGERY_LEVEL
  surgeryDuration?: number    // 手术时长(分钟) SURGERY_DURATION
  surgeryNotes?: string       // 手术备注 SURGERY_NOTES
  createdAt: string           // 创建时间 CREATED_AT
  updatedAt: string           // 更新时间 UPDATED_AT
  createdBy?: number          // 创建人 CREATED_BY
  updatedBy?: number          // 更新人 UPDATED_BY
}

// 费用明细（基于MEDICAL_COST_DETAIL表结构）
export interface MedicalCostDetail {
  id: number
  caseId: number              // 病例ID CASE_ID
  itemCode?: string           // 项目编码 ITEM_CODE
  itemName: string            // 项目名称 ITEM_NAME
  insuranceItemCode?: string  // 医保项目编码 INSURANCE_ITEM_CODE
  insuranceItemName?: string  // 医保项目名称 INSURANCE_ITEM_NAME
  itemType: CostItemType      // 项目类型 ITEM_TYPE
  unitPrice: number           // 单价 UNIT_PRICE
  quantity: number            // 数量 QUANTITY
  totalAmount: number         // 总金额 TOTAL_AMOUNT
  compliantAmount: number     // 符合政策金额 COMPLIANT_AMOUNT
  chargedAt: string           // 收费时间 CHARGED_AT
  department?: string         // 科室 DEPARTMENT
  doctorName?: string         // 医生姓名 DOCTOR_NAME
  createdAt: string           // 创建时间 CREATED_AT
  updatedAt: string           // 更新时间 UPDATED_AT
  createdBy?: number          // 创建人 CREATED_BY
  updatedBy?: number          // 更新人 UPDATED_BY
}

// 结算信息（基于MEDICAL_SETTLEMENT表结构）
export interface MedicalSettlement {
  id: number
  caseId: number                      // 病例ID CASE_ID
  settlementNumber: string            // 结算单号 SETTLEMENT_NUMBER
  totalMedicalCost: number           // 医疗费总额 TOTAL_MEDICAL_COST
  fullSelfPayAmount: number          // 全自费金额 FULL_SELF_PAY_AMOUNT
  overLimitSelfPayAmount: number     // 超限价自费费用 OVER_LIMIT_SELF_PAY_AMOUNT
  advanceSelfPayAmount: number       // 先行自付金额 ADVANCE_SELF_PAY_AMOUNT
  eligibleAmount: number             // 符合范围金额 ELIGIBLE_AMOUNT
  actualDeductible: number           // 实际支付起付线 ACTUAL_DEDUCTIBLE
  basicMedicalPayRatio: number       // 基本医疗统筹支付比例 BASIC_MEDICAL_PAY_RATIO
  totalFundPayment: number           // 基金支付总额 TOTAL_FUND_PAYMENT
  poolingFundPayment: number         // 统筹基金支付 POOLING_FUND_PAYMENT
  policyRangeSelfPay: number         // 政策范围内自付 POLICY_RANGE_SELF_PAY
  outOfPolicyRangeAmount: number     // 政策范围外金额 OUT_OF_POLICY_RANGE_AMOUNT
  isRefund: boolean                  // 是否退费 IS_REFUND
  isValid: boolean                   // 是否有效 IS_VALID
  mediumType?: MediumType            // 介质类型 MEDIUM_TYPE
  settlementStaffCode?: string       // 结算员工编码 SETTLEMENT_STAFF_CODE
  settlementStaffName?: string       // 结算员工姓名 SETTLEMENT_STAFF_NAME
  settledAt: string                  // 结算时间 SETTLED_AT
  isDeleted: boolean                 // 是否删除 IS_DELETED
  createdAt: string                  // 创建时间 CREATED_AT
  updatedAt: string                  // 更新时间 UPDATED_AT
  createdBy?: number                 // 创建人 CREATED_BY
  updatedBy?: number                 // 更新人 UPDATED_BY
}

// 案例分组信息（基于MEDICAL_CASE_GROUP表结构）
export interface MedicalCaseGroup {
  id: number
  caseId: number                      // 病例ID CASE_ID
  groupCode: string                   // 分组编码 GROUP_CODE
  groupName: string                   // 分组名称 GROUP_NAME
  groupWeight: number                 // 权重 GROUP_WEIGHT
  groupRate: number                   // 费率 GROUP_RATE
  monthlyPaymentStandard: number      // 月结支付标准 MONTHLY_PAYMENT_STANDARD
  settlementPaymentStandard: number   // 清算支付标准 SETTLEMENT_PAYMENT_STANDARD
  groupType: GroupType                // 分组类型 GROUP_TYPE
  isValid: boolean                    // 是否有效 IS_VALID
  groupedAt: string                   // 分组日期时间 GROUPED_AT
  isDeleted: boolean                  // 是否删除 IS_DELETED
  createdAt: string                   // 创建时间 CREATED_AT
  updatedAt: string                   // 更新时间 UPDATED_AT
  createdBy?: number                  // 创建人 CREATED_BY
  updatedBy?: number                  // 更新人 UPDATED_BY
}

// 案例查询参数
export interface CaseListParams {
  page?: number
  pageSize?: number
  search?: string                // 搜索关键词
  caseType?: CaseType           // 类型筛选
  medicalCategory?: string      // 医疗类别筛选
  hospitalCode?: string         // 医院编码筛选
  hospitalName?: string         // 医院名称筛选
  department?: string           // 科室筛选
  patientGender?: Gender       // 患者性别筛选
  // 日期范围筛选
  admissionDateStart?: string   // 入院开始日期
  admissionDateEnd?: string     // 入院结束日期
  dischargeDateStart?: string   // 出院开始日期
  dischargeDateEnd?: string     // 出院结束日期
  // 数值范围筛选
  totalCostMin?: number        // 最小费用
  totalCostMax?: number        // 最大费用
  patientAgeMin?: number       // 最小年龄
  patientAgeMax?: number       // 最大年龄
  // 排序参数
  sortBy?: string              // 排序字段
  sortOrder?: 'ASC' | 'DESC'   // 排序方向
}

// 案例创建请求
export interface CaseCreateRequest {
  caseNumber: string
  patientName: string
  patientIdCard: string
  patientPhone?: string
  patientAge?: number
  patientGender?: Gender
  caseType: CaseType
  medicalCategory: MedicalCategory
  hospitalName: string
  hospitalCode?: string
  department?: string
  doctorName?: string
  admissionDate?: string
  dischargeDate?: string
  totalCost: number

  // 关联数据
  diagnoses?: Omit<MedicalDiagnosis, 'id' | 'caseId' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'>[]
  surgeries?: Omit<MedicalSurgery, 'id' | 'caseId' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'>[]
  costDetails?: Omit<MedicalCostDetail, 'id' | 'caseId' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'>[]
}

// 案例更新请求
export interface CaseUpdateRequest {
  patientName?: string
  patientPhone?: string
  patientAge?: number
  patientGender?: Gender
  medicalCategory?: MedicalCategory
  hospitalName?: string
  hospitalCode?: string
  department?: string
  doctorName?: string
  admissionDate?: string
  dischargeDate?: string
  totalCost?: number
}

// 案例统计信息
export interface CaseStatistics {
  totalCases: number
  inpatientCases: number
  outpatientCases: number
  totalCost: number
  avgCostPerCase: number
  casesByType: Record<CaseType, number>
  casesByCategory: Record<MedicalCategory, number>
  casesByGender: Record<Gender, number>
  monthlyTrend: {
    month: string
    caseCount: number
    totalCost: number
    avgCost: number
  }[]
  hospitalStats: {
    hospitalName: string
    hospitalCode?: string
    caseCount: number
    totalCost: number
  }[]
}

// 分页响应
export interface CasePaginationResponse {
  items: MedicalCase[]
  page: number
  pageSize: number
  total: number
  totalPages: number
}

// API响应类型
export interface CaseApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}
