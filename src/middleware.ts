import { NextRequest, NextResponse } from 'next/server'
import { detectSensitiveDataInUrl, logSecurityEvent, createSecurityResponse } from '@/middleware/security'
import { isUserAuthenticated } from '@/lib/middleware-auth'
import { monitoring, log } from '@/lib/monitoring'
import { checkRateLimit, RATE_LIMIT_CONFIGS, ddosProtection } from '@/lib/rate-limiting'
import { basicSecurityCheck } from '@/lib/security/edge-security'

/**
 * Next.js中间件 - 全局安全检查、性能监控和认证重定向
 */
export async function middleware(request: NextRequest) {
  const startTime = Date.now()
  const url = new URL(request.url)
  const pathname = url.pathname
  const method = request.method
  const userAgent = request.headers.get('user-agent') || 'unknown'
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
             request.headers.get('x-real-ip') || 'unknown'

  // 生成请求ID用于追踪
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // 跳过静态资源和Next.js内部路径
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico' ||
    pathname.startsWith('/icons/') ||
    pathname === '/manifest.json'
  ) {
    return NextResponse.next()
  }

  // 记录请求开始
  log({
    level: 'debug',
    message: `Request started: ${method} ${pathname}`,
    context: {
      requestId,
      method,
      pathname,
      ip,
      userAgent: userAgent.substring(0, 100) // 限制长度
    }
  })

  try {
    // 1. DDoS 防护检查 (开发环境禁用)
    if (process.env.NODE_ENV !== 'development') {
      const ddosCheck = ddosProtection.checkForDDoS(request)
      if (ddosCheck.isBlocked) {
        log({
          level: 'error',
          message: 'Request blocked by DDoS protection',
          context: { requestId, ip, reason: ddosCheck.reason }
        })

        return new NextResponse('Too Many Requests', {
          status: 429,
          headers: {
            'Retry-After': '3600',
            'X-Request-ID': requestId
          }
        })
      }
    }

    // 2. 限流检查 (开发环境禁用)
    const rateLimitConfig = getRateLimitConfig(pathname, method)
    if (rateLimitConfig && process.env.NODE_ENV !== 'development') {
      const rateLimitResult = await checkRateLimit(request, rateLimitConfig)

      if (!rateLimitResult.allowed) {
        log({
          level: 'warn',
          message: 'Request rate limited',
          context: {
            requestId,
            ip,
            pathname,
            remaining: rateLimitResult.remaining,
            resetTime: new Date(rateLimitResult.resetTime).toISOString()
          }
        })

        return new NextResponse(rateLimitConfig.message || 'Too Many Requests', {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimitConfig.maxRequests.toString(),
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
            'X-Request-ID': requestId,
            'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
          }
        })
      }

      // 限流头信息将在后面统一添加
    }

    // 3. 认证检查和重定向逻辑
    const isAuthenticated = isUserAuthenticated(request)

  // 处理根路径重定向
  if (pathname === '/') {
    if (isAuthenticated) {
      console.log('✅ 用户已认证，重定向到仪表板')
      return NextResponse.redirect(new URL('/dashboard', request.url))
    } else {
      console.log('❌ 用户未认证，重定向到登录页面')
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  // 检查受保护的路由
  const protectedRoutes = ['/dashboard', '/medical-cases', '/analytics', '/supervision-rules', '/knowledge-base', '/users', '/settings']
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))

  if (isProtectedRoute && !isAuthenticated) {
    console.log('🔒 访问受保护路由但未认证，重定向到登录页面')
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }

  // 检查认证页面
  const authRoutes = ['/auth/login', '/auth/register']
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route))

  if (isAuthRoute && isAuthenticated) {
    console.log('✅ 已认证用户访问认证页面，重定向到仪表板')
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // 4. 基础API安全检查（仅对API路由）
  if (pathname.startsWith('/api/')) {
    const securityResponse = await basicSecurityCheck(request)
    if (securityResponse) {
      log({
        level: 'warn',
        message: 'Basic security check failed',
        context: { requestId, pathname, ip }
      })
      return securityResponse
    }
  }

  // 5. 基础安全检查：检测URL中的敏感信息
  const detection = detectSensitiveDataInUrl(request)
  
  if (detection.hasSensitiveData) {
    console.warn(`⚠️ 检测到敏感参数: ${detection.sensitiveParams.join(', ')}`)
    
    // 记录安全事件
    logSecurityEvent('SENSITIVE_URL', request, {
      sensitiveParams: detection.sensitiveParams,
      securityLevel: detection.securityLevel,
      path: pathname
    })
    
    // 如果是关键安全问题，阻止请求
    if (detection.securityLevel === 'critical') {
      console.error('🚨 阻止关键安全威胁请求')
      
      return createSecurityResponse(
        '检测到敏感信息通过URL传输，请求已被阻止。请使用POST方法并将敏感数据放在请求体中。',
        'critical',
        {
          'X-Blocked-Reason': 'Sensitive data in URL',
          'X-Sensitive-Params': detection.sensitiveParams.join(', '),
          'X-Security-Advice': 'Use POST method with request body for sensitive data'
        }
      )
    }
  }
  
  // 特殊检查：登录API的GET请求
  if (pathname === '/api/auth/login' && request.method === 'GET') {
    console.error('🚨 阻止不安全的GET登录请求')
    
    logSecurityEvent('BLOCKED_REQUEST', request, {
      reason: 'GET method not allowed for login',
      securityLevel: 'critical'
    })
    
    return createSecurityResponse(
      '不允许使用GET方法进行登录。这会导致密码在URL中明文显示，存在严重安全风险。请使用POST方法。',
      'critical',
      {
        'Allow': 'POST',
        'X-Blocked-Reason': 'Unsafe login method',
        'X-Security-Advice': 'Use POST method for login'
      }
    )
  }
  
  // 检查其他敏感API的GET请求
  const sensitiveApiPaths = [
    '/api/auth/register',
    '/api/auth/reset-password',
    '/api/users/create'
  ]
  
  if (sensitiveApiPaths.some(path => pathname.startsWith(path)) && request.method === 'GET') {
    console.warn('⚠️ 敏感API使用GET方法')
    
    logSecurityEvent('SUSPICIOUS_ACTIVITY', request, {
      reason: 'GET method on sensitive API',
      securityLevel: 'medium'
    })
  }
  
    // 添加安全头和请求ID
    const response = NextResponse.next()
    response.headers.set('X-Request-ID', requestId)

    // 基本安全头
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // 对于API路径，添加额外的安全头
  if (pathname.startsWith('/api/')) {
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
  }
  
  // 对于认证相关的API，添加更严格的安全头
  if (pathname.startsWith('/api/auth/')) {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
    response.headers.set('X-Permitted-Cross-Domain-Policies', 'none')
  }



    // 记录请求完成
    const duration = Date.now() - startTime
    log({
      level: 'info',
      message: `Request completed: ${method} ${pathname}`,
      context: {
        requestId,
        method,
        pathname,
        duration,
        ip,
        status: response.status
      }
    })

    // 记录性能指标
    monitoring.recordPerformance({
      operation: `middleware_${method}_${pathname}`,
      duration,
      success: true,
      metadata: { method, pathname, ip }
    })

    return response

  } catch (error) {
    // 错误处理和监控
    const duration = Date.now() - startTime
    const errorMessage = (error as Error).message

    log({
      level: 'error',
      message: `Middleware error: ${errorMessage}`,
      context: {
        requestId,
        method,
        pathname,
        duration,
        ip,
        error: errorMessage,
        stack: (error as Error).stack
      }
    })

    // 记录错误指标
    monitoring.recordMetric({
      name: 'middleware_error',
      value: 1,
      tags: {
        method,
        pathname: pathname.split('/')[1] || 'root', // 只记录第一级路径
        error_type: (error as Error).name
      }
    })

    // 返回通用错误响应
    return new NextResponse('Internal Server Error', {
      status: 500,
      headers: {
        'X-Request-ID': requestId
      }
    })
  }
}

/**
 * 根据路径和方法获取限流配置
 */
function getRateLimitConfig(pathname: string, method: string) {
  // 认证相关API - 严格限流
  if (pathname.startsWith('/api/auth/')) {
    return RATE_LIMIT_CONFIGS.STRICT
  }

  // 用户管理API - 中等限流
  if (pathname.startsWith('/api/users/') && ['POST', 'PUT', 'DELETE'].includes(method)) {
    return RATE_LIMIT_CONFIGS.MODERATE
  }

  // 查询API - 宽松限流
  if (method === 'GET' && pathname.startsWith('/api/')) {
    return RATE_LIMIT_CONFIGS.LENIENT
  }

  // 其他API - 中等限流
  if (pathname.startsWith('/api/')) {
    return RATE_LIMIT_CONFIGS.MODERATE
  }

  // 页面访问 - 宽松限流
  return RATE_LIMIT_CONFIGS.LENIENT
}

/**
 * 配置中间件匹配路径
 */
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了：
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
