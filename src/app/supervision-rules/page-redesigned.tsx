/**
 * 重构后的监管规则页面
 * 实现P0-007页面功能架构重新设计目标：
 * - 拆分大型组件，遵循单一职责原则
 * - 实现合理的功能分层
 * - 提升代码可维护性
 */

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { useListPageState, useBatchOperation } from '@/hooks/use-page-state'
import { SupervisionRule, RuleListParams, RuleStatistics } from '@/types/supervision-rule'
import {
  RuleManagementLayout,
  RuleActionPanel,
  RuleQuickFilter,
  RuleCreateShortcut
} from '@/components/supervision-rules/rule-management-layout'
import { RuleManagementViews } from '@/components/supervision-rules/rule-management-views'
import { RuleFilters } from '@/components/supervision-rules/rule-filters'
import {
  Shield,
  Plus,
  Download,
  Upload,
  Settings
} from 'lucide-react'

export default function SupervisionRulesPageRedesigned() {
  const router = useRouter()
  const { toast } = useToast()
  
  // 状态管理
  const [rules, setRules] = useState<SupervisionRule[]>([])
  const [statistics, setStatistics] = useState<RuleStatistics | null>(null)
  const [selectedRules, setSelectedRules] = useState<number[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [filters, setFilters] = useState<RuleListParams>({
    page: 1,
    pageSize: 20,
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  })

  // 页面状态管理
  const { saveListState, navigateWithContext } = useListPageState('supervision-rules-redesigned')
  
  // 批量操作
  const { executeBatch } = useBatchOperation()

  // 加载规则列表
  const loadRules = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString())
        }
      })
      params.set('page', filters.page?.toString() || '1')
      params.set('pageSize', filters.pageSize?.toString() || '20')

      const response = await fetch('/api/supervision-rules?' + params.toString())
      
      if (!response.ok) {
        throw new Error('加载规则失败')
      }
      
      const data = await response.json()
      setRules(data.data || [])
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载监管规则列表',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 加载统计数据
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/supervision-rules/statistics')
      if (response.ok) {
        const data = await response.json()
        setStatistics(data.data)
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadRules()
    loadStatistics()
  }, [filters])

  // 规则操作处理
  const handleRuleView = (ruleId: number) => {
    saveListState({
      page: filters.page || 1,
      pageSize: filters.pageSize || 20,
      filters: filters
    })
    
    navigateWithContext(`/supervision-rules/${ruleId}`, {
      returnUrl: '/supervision-rules',
      preserveState: true
    })
  }

  const handleRuleEdit = (ruleId: number) => {
    saveListState({
      page: filters.page || 1,
      pageSize: filters.pageSize || 20,
      filters: filters
    })
    
    navigateWithContext(`/supervision-rules/${ruleId}/edit`, {
      returnUrl: '/supervision-rules',
      preserveState: true
    })
  }

  const handleRuleToggle = async (ruleId: number, isActive: boolean) => {
    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({ isActive })
      })

      if (!response.ok) throw new Error('更新失败')

      setRules(prev => prev.map(rule =>
        rule.id === ruleId ? { ...rule, isActive } : rule
      ))
      
      toast({
        title: '更新成功',
        description: `规则已${isActive ? '启用' : '禁用'}`
      })
    } catch (error) {
      toast({
        title: '更新失败',
        description: '无法更新规则状态',
        variant: 'destructive'
      })
    }
  }

  const handleRuleExecute = async (ruleIds: number[]) => {
    try {
      const response = await fetch('/api/supervision-rules/engine', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          ruleIds,
          async: true
        })
      })

      if (!response.ok) throw new Error('执行规则失败')

      toast({
        title: '执行成功',
        description: `已启动 ${ruleIds.length} 个规则的执行`
      })
    } catch (error) {
      toast({
        title: '执行失败',
        description: '无法执行选中的规则',
        variant: 'destructive'
      })
    }
  }

  const handleRuleDelete = async (ruleId: number) => {
    if (!confirm('确定要删除这个规则吗？此操作不可撤销。')) return

    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      })

      if (!response.ok) throw new Error('删除失败')

      setRules(prev => prev.filter(rule => rule.id !== ruleId))
      setSelectedRules(prev => prev.filter(id => id !== ruleId))
      
      toast({
        title: '删除成功',
        description: '规则已删除'
      })
    } catch (error) {
      toast({
        title: '删除失败',
        description: '无法删除规则',
        variant: 'destructive'
      })
    }
  }

  const handleRuleCopy = async (ruleId: number) => {
    const rule = rules.find(r => r.id === ruleId)
    if (!rule) return

    saveListState({
      page: filters.page || 1,
      pageSize: filters.pageSize || 20,
      filters: filters
    })
    
    navigateWithContext('/supervision-rules/new', {
      returnUrl: '/supervision-rules',
      preserveState: true,
      metadata: { copyFrom: rule }
    })
  }

  // 批量操作处理
  const handleBatchExecute = () => {
    if (selectedRules.length === 0) return
    handleRuleExecute(selectedRules)
  }

  const handleBatchToggle = async () => {
    if (selectedRules.length === 0) return

    try {
      await executeBatch(
        selectedRules,
        async (ruleId: number) => {
          const rule = rules.find(r => r.id === ruleId)
          if (rule) {
            await handleRuleToggle(ruleId, !rule.isActive)
          }
        },
        {
          operationId: `batch-toggle-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量切换 ${selectedRules.length} 个规则状态`
        }
      )
    } catch (error) {
      console.error('批量切换失败:', error)
    }
  }

  const handleBatchDelete = async () => {
    if (selectedRules.length === 0) return
    if (!confirm(`确定要删除选中的 ${selectedRules.length} 个规则吗？此操作不可撤销。`)) return

    try {
      await executeBatch(
        selectedRules,
        async (ruleId: number) => {
          const response = await fetch(`/api/supervision-rules/${ruleId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
          })
          
          if (!response.ok) {
            throw new Error(`删除规则 ${ruleId} 失败`)
          }
        },
        {
          operationId: `batch-delete-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量删除 ${selectedRules.length} 个规则`
        }
      )

      // 刷新数据
      loadRules()
      setSelectedRules([])
    } catch (error) {
      console.error('批量删除失败:', error)
    }
  }

  const handleCreateRule = (type?: string) => {
    navigateWithContext('/supervision-rules/new', {
      returnUrl: '/supervision-rules',
      preserveState: true,
      metadata: { ruleType: type }
    })
  }

  const handleQuickFilter = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: (prev as any)[key] === value ? undefined : value,
      page: 1
    }))
  }

  // 页面操作
  const pageActions = (
    <>
      <Button variant="outline" onClick={() => loadRules()}>
        <Download className="h-4 w-4 mr-2" />
        导出
      </Button>
      <Button variant="outline">
        <Upload className="h-4 w-4 mr-2" />
        导入
      </Button>
      <Button onClick={() => handleCreateRule()}>
        <Plus className="h-4 w-4 mr-2" />
        新建规则
      </Button>
    </>
  )

  // 侧边栏内容
  const sidebar = (
    <div className="space-y-4">
      <RuleActionPanel
        selectedCount={selectedRules.length}
        onBatchExecute={handleBatchExecute}
        onBatchToggle={handleBatchToggle}
        onBatchDelete={handleBatchDelete}
        onRefresh={loadRules}
        isLoading={isLoading}
      />
      
      <RuleQuickFilter
        onFilterChange={handleQuickFilter}
        activeFilters={filters}
      />
      
      <RuleCreateShortcut
        onCreateRule={handleCreateRule}
      />
    </div>
  )

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
      <MainLayout>
        <RuleManagementLayout
          title="监管规则"
          description="管理和执行医保基金监管规则，支持多维度筛选和批量操作"
          actions={pageActions}
          sidebar={sidebar}
          statistics={statistics || undefined}
        >
          <div className="space-y-6">
            {/* 高级筛选器 */}
            <RuleFilters
              filters={filters}
              onFiltersChange={setFilters}
              onReset={() => setFilters({
                page: 1,
                pageSize: 20,
                sortBy: 'createdAt',
                sortOrder: 'DESC'
              })}
            />

            {/* 规则管理视图 */}
            <RuleManagementViews
              rules={rules}
              selectedRules={selectedRules}
              onRuleSelect={setSelectedRules}
              onRuleToggle={handleRuleToggle}
              onRuleExecute={handleRuleExecute}
              onRuleEdit={handleRuleEdit}
              onRuleView={handleRuleView}
              onRuleDelete={handleRuleDelete}
              onRuleCopy={handleRuleCopy}
              isLoading={isLoading}
            />
          </div>
        </RuleManagementLayout>
      </MainLayout>
    </RouteGuard>
  )
}
