'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { RuleForm } from '@/components/supervision-rules'
import { useToast } from '@/lib/toast'
import { SupervisionRule } from '@/types/supervision-rule'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Shield } from 'lucide-react'

export default function NewRulePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 处理表单提交
  const handleSubmit = async (formData: Partial<SupervisionRule>) => {
    try {
      setIsSubmitting(true)
      
      const response = await fetch('/api/supervision-rules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('创建规则失败')
      }

      const data = await response.json()
      
      toast({
        title: '创建成功',
        description: '监管规则已创建'
      })

      // 跳转到规则详情页
      router.push(`/supervision-rules/${data.data.id}`)
    } catch (error) {
      toast({
        title: '创建失败',
        description: '无法创建规则',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    router.push('/supervision-rules')
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <h1 className="text-2xl font-bold">新建监管规则</h1>
            </div>
          </div>

          {/* 规则表单 */}
          <RuleForm
            isEditing={false}
            isSubmitting={isSubmitting}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
