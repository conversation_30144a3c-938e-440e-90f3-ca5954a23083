'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  Play, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  Code,
  Database,
  Zap
} from 'lucide-react'

interface TestResult {
  success: boolean
  executionId?: string
  matchedCount?: number
  processedCount?: number
  error?: string
  duration?: number
}

export default function RuleTestPage() {
  const { toast } = useToast()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<TestResult | null>(null)
  const [formData, setFormData] = useState({
    ruleIds: '',
    parameters: '{}',
    async: false
  })

  // 从URL参数获取规则ID
  useEffect(() => {
    const ruleId = searchParams.get('ruleId')
    if (ruleId) {
      setFormData(prev => ({ ...prev, ruleIds: ruleId }))
    }
  }, [searchParams])

  // 执行规则测试
  const executeTest = async () => {
    if (!formData.ruleIds.trim()) {
      toast({
        title: '测试失败',
        description: '请输入要测试的规则ID',
        variant: 'destructive',
      })
      return
    }

    try {
      setIsLoading(true)
      setTestResult(null)

      // 解析规则ID
      const ruleIds = formData.ruleIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      
      if (ruleIds.length === 0) {
        throw new Error('请输入有效的规则ID')
      }

      // 解析参数
      let parameters = {}
      if (formData.parameters.trim()) {
        try {
          parameters = JSON.parse(formData.parameters)
        } catch {
          throw new Error('参数格式不正确，请输入有效的JSON')
        }
      }

      const startTime = Date.now()

      const response = await fetch('/api/supervision-rules/engine', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'execute',
          ruleIds,
          parameters,
          async: formData.async
        }),
      })

      const result = await response.json()
      const duration = Date.now() - startTime

      if (result.success) {
        if (formData.async) {
          setTestResult({
            success: true,
            executionId: result.data.executionIds[0],
            duration
          })
          
          toast({
            title: '测试启动成功',
            description: `规则执行已启动，执行ID: ${result.data.executionIds[0]}`,
          })
        } else {
          const firstResult = result.data.results[0]
          setTestResult({
            success: firstResult.success,
            executionId: firstResult.executionId,
            matchedCount: firstResult.matchedCount,
            processedCount: firstResult.processedCount,
            error: firstResult.error,
            duration
          })
          
          toast({
            title: firstResult.success ? '测试成功' : '测试失败',
            description: firstResult.success 
              ? `匹配 ${firstResult.matchedCount} 条记录` 
              : firstResult.error,
            variant: firstResult.success ? 'default' : 'destructive',
          })
        }
      } else {
        setTestResult({
          success: false,
          error: result.message,
          duration
        })
        
        toast({
          title: '测试失败',
          description: result.message,
          variant: 'destructive',
        })
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '测试执行失败'
      
      setTestResult({
        success: false,
        error: errorMessage,
        duration: Date.now() - Date.now()
      })
      
      toast({
        title: '测试失败',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清除测试结果
  const clearResult = () => {
    setTestResult(null)
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">规则引擎测试</h1>
              <p className="text-muted-foreground">测试和验证监管规则的执行效果</p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 测试配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  测试配置
                </CardTitle>
                <CardDescription>
                  配置要测试的规则和参数
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ruleIds">规则ID</Label>
                  <Input
                    id="ruleIds"
                    placeholder="输入规则ID，多个ID用逗号分隔"
                    value={formData.ruleIds}
                    onChange={(e) => setFormData(prev => ({ ...prev, ruleIds: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">
                    例如: 1,2,3
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parameters">执行参数 (JSON)</Label>
                  <Textarea
                    id="parameters"
                    placeholder='{"startDate": "2024-01-01", "endDate": "2024-12-31"}'
                    value={formData.parameters}
                    onChange={(e) => setFormData(prev => ({ ...prev, parameters: e.target.value }))}
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground">
                    输入JSON格式的参数，留空表示无参数
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="async"
                    checked={formData.async}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, async: checked as boolean }))}
                  />
                  <Label htmlFor="async">异步执行</Label>
                </div>

                <Separator />

                <div className="flex gap-2">
                  <Button 
                    onClick={executeTest} 
                    disabled={isLoading}
                    className="flex-1"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        执行中...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        执行测试
                      </>
                    )}
                  </Button>
                  
                  {testResult && (
                    <Button variant="outline" onClick={clearResult}>
                      清除结果
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 测试结果 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  测试结果
                </CardTitle>
                <CardDescription>
                  规则执行的详细结果
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!testResult ? (
                  <div className="flex items-center justify-center h-32 text-muted-foreground">
                    <div className="text-center">
                      <Zap className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>点击"执行测试"开始测试规则</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* 执行状态 */}
                    <div className="flex items-center gap-2">
                      {testResult.success ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <Badge variant={testResult.success ? 'default' : 'destructive'}>
                        {testResult.success ? '执行成功' : '执行失败'}
                      </Badge>
                    </div>

                    {/* 执行详情 */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {testResult.executionId && (
                        <div>
                          <Label className="text-muted-foreground">执行ID</Label>
                          <p className="font-mono">{testResult.executionId}</p>
                        </div>
                      )}
                      
                      {testResult.duration !== undefined && (
                        <div>
                          <Label className="text-muted-foreground">执行时间</Label>
                          <p>{testResult.duration}ms</p>
                        </div>
                      )}
                      
                      {testResult.processedCount !== undefined && (
                        <div>
                          <Label className="text-muted-foreground">处理记录数</Label>
                          <p>{testResult.processedCount}</p>
                        </div>
                      )}
                      
                      {testResult.matchedCount !== undefined && (
                        <div>
                          <Label className="text-muted-foreground">匹配记录数</Label>
                          <p>{testResult.matchedCount}</p>
                        </div>
                      )}
                    </div>

                    {/* 错误信息 */}
                    {testResult.error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          <Label className="text-red-700">错误信息</Label>
                        </div>
                        <p className="text-sm text-red-600">{testResult.error}</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
