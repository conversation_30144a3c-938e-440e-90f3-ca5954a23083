'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { UnifiedRuleManagement } from '@/components/supervision-rules/unified-rule-management'

export default function SupervisionRulesPage() {

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
      <MainLayout>
        <UnifiedRuleManagement />
      </MainLayout>
    </RouteGuard>
  )
}


