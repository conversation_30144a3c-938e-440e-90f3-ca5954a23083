'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useRouter, useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { RuleDetail } from '@/components/supervision-rules/rule-detail'
import { useToast } from '@/lib/toast'
import { SupervisionRule } from '@/types/supervision-rule'

export default function RuleDetailPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const [rule, setRule] = useState<SupervisionRule | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const ruleId = params['id'] as string

  // 加载规则数据
  useEffect(() => {
    const loadRule = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/supervision-rules/${ruleId}`)
        
        if (!response.ok) {
          throw new Error('加载规则失败')
        }
        
        const data = await response.json()
        setRule(data.data)
      } catch (error) {
        toast({
          title: '加载失败',
          description: '无法加载规则信息',
          variant: 'destructive'
        })
        router.push('/supervision-rules')
      } finally {
        setIsLoading(false)
      }
    }

    if (ruleId) {
      loadRule()
    }
  }, [ruleId, router, toast])

  // 处理编辑
  const handleEdit = () => {
    router.push(`/supervision-rules/${ruleId}/edit`)
  }

  // 处理执行
  const handleExecute = async () => {
    try {
      const response = await fetch('/api/supervision-rules/engine', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ruleIds: [parseInt(ruleId)],
          async: true
        })
      })

      if (!response.ok) {
        throw new Error('执行规则失败')
      }

      toast({
        title: '执行成功',
        description: '规则执行已启动'
      })
    } catch (error) {
      toast({
        title: '执行失败',
        description: '无法执行规则',
        variant: 'destructive'
      })
    }
  }

  // 处理复制
  const handleCopy = async () => {
    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}/copy`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('复制规则失败')
      }

      const data = await response.json()
      toast({
        title: '复制成功',
        description: '规则已复制，正在跳转到编辑页面'
      })

      router.push(`/supervision-rules/${data.data.id}/edit`)
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制规则',
        variant: 'destructive'
      })
    }
  }

  // 处理返回
  const handleBack = () => {
    router.push('/supervision-rules')
  }

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'OPERATOR', 'VIEWER']}>
        <MainLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  if (!rule) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'OPERATOR', 'VIEWER']}>
        <MainLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <p className="text-lg font-medium text-muted-foreground mb-2">规则不存在</p>
              <p className="text-sm text-muted-foreground mb-4">请检查规则ID是否正确</p>
              <Button onClick={handleBack} className="text-blue-600 hover:text-blue-800 underline"> 返回规则列表 </Button>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR', 'VIEWER']}>
      <MainLayout>
        <RuleDetail
          rule={rule}
          onEdit={handleEdit}
          onExecute={handleExecute}
          onCopy={handleCopy}
          onBack={handleBack}
        />
      </MainLayout>
    </RouteGuard>
  )
}
