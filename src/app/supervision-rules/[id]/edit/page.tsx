'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { RuleForm } from '@/components/supervision-rules'
import { useToast } from '@/lib/toast'
import { SupervisionRule } from '@/types/supervision-rule'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Shield } from 'lucide-react'

export default function EditRulePage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const [rule, setRule] = useState<SupervisionRule | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const ruleId = params['id'] as string

  // 加载规则数据
  useEffect(() => {
    const loadRule = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/supervision-rules/${ruleId}`)
        
        if (!response.ok) {
          throw new Error('加载规则失败')
        }
        
        const data = await response.json()
        setRule(data.data)
      } catch (error) {
        toast({
          title: '加载失败',
          description: '无法加载规则信息',
          variant: 'destructive'
        })
        router.push('/supervision-rules')
      } finally {
        setIsLoading(false)
      }
    }

    if (ruleId && ruleId !== 'new') {
      loadRule()
    } else {
      setIsLoading(false)
    }
  }, [ruleId, router, toast])

  // 处理表单提交
  const handleSubmit = async (formData: Partial<SupervisionRule>) => {
    try {
      setIsSubmitting(true)
      
      const url = ruleId === 'new' 
        ? '/api/supervision-rules'
        : `/api/supervision-rules/${ruleId}`
      
      const method = ruleId === 'new' ? 'POST' : 'PUT'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('保存规则失败')
      }

      const data = await response.json()
      
      toast({
        title: '保存成功',
        description: ruleId === 'new' ? '规则已创建' : '规则已更新'
      })

      // 跳转到规则详情页
      router.push(`/supervision-rules/${data.data.id}`)
    } catch (error) {
      toast({
        title: '保存失败',
        description: '无法保存规则',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    if (ruleId === 'new') {
      router.push('/supervision-rules')
    } else {
      router.push(`/supervision-rules/${ruleId}`)
    }
  }

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
        <MainLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <h1 className="text-2xl font-bold">
                {ruleId === 'new' ? '新建监管规则' : '编辑监管规则'}
              </h1>
            </div>
          </div>

          {/* 规则表单 */}
          <RuleForm
            rule={rule || undefined}
            isEditing={ruleId !== 'new'}
            isSubmitting={isSubmitting}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
