'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { SupervisionRule, RuleListParams, RuleStatistics } from '@/types/supervision-rule'
import {
  RuleList,
  RuleStatisticsCards,
  DetailedRuleStatistics,
  ExecutionTrendCard,
  RuleFilters
} from '@/components/supervision-rules'
import {
  Shield,
  Plus,
  Play,
  Loader2,
  Settings,
  BarChart3,
  Activity,
  Download
} from 'lucide-react'

export default function SupervisionRulesPage() {
  const router = useRouter()
  const { toast } = useToast()

  // 状态管理
  const [rules, setRules] = useState<SupervisionRule[]>([])
  const [statistics, setStatistics] = useState<RuleStatistics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedRules, setSelectedRules] = useState<number[]>([])
  const [isExecuting, setIsExecuting] = useState(false)
  const [activeTab, setActiveTab] = useState('list')

  // 过滤器状态
  const [filters, setFilters] = useState<RuleListParams>({
    page: 1,
    pageSize: 20,
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  })

  // 加载规则列表
  const loadRules = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString())
        }
      })
      params.set('page', filters.page?.toString() || '1')
      params.set('pageSize', filters.pageSize?.toString() || '20')

      const response = await fetch('/api/supervision-rules?' + params.toString())
      
      if (!response.ok) {
        throw new Error('加载规则失败')
      }
      
      const data = await response.json()
      setRules(data.data || [])
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载监管规则列表',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/supervision-rules/statistics')
      if (!response.ok) {
        throw new Error('加载统计信息失败')
      }
      
      const data = await response.json()
      setStatistics(data.data)
    } catch (error) {
      console.error('加载统计信息失败:', error)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadRules()
    loadStatistics()
  }, [filters])

  // 处理规则选择
  const handleRuleSelect = (ruleIds: number[]) => {
    setSelectedRules(ruleIds)
  }

  // 处理规则状态切换
  const handleRuleToggle = async (ruleId: number, isActive: boolean) => {
    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive })
      })

      if (!response.ok) {
        throw new Error('更新规则状态失败')
      }

      // 更新本地状态
      setRules(prev => prev.map(rule => 
        rule.id === ruleId ? { ...rule, isActive } : rule
      ))

      toast({
        title: '更新成功',
        description: `规则已${isActive ? '启用' : '禁用'}`
      })
    } catch (error) {
      toast({
        title: '更新失败',
        description: '无法更新规则状态',
        variant: 'destructive'
      })
    }
  }

  // 处理规则执行
  const handleRuleExecute = async (ruleIds: number[]) => {
    try {
      setIsExecuting(true)
      const response = await fetch('/api/supervision-rules/engine', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ruleIds,
          async: true
        })
      })

      if (!response.ok) {
        throw new Error('执行规则失败')
      }

      const data = await response.json()
      toast({
        title: '执行成功',
        description: `已启动 ${ruleIds.length} 个规则的执行`
      })

      // 刷新规则列表
      loadRules()
    } catch (error) {
      toast({
        title: '执行失败',
        description: '无法执行选中的规则',
        variant: 'destructive'
      })
    } finally {
      setIsExecuting(false)
    }
  }

  // 处理规则查看
  const handleRuleView = (ruleId: number) => {
    router.push(`/supervision-rules/${ruleId}`)
  }

  // 处理规则编辑
  const handleRuleEdit = (ruleId: number) => {
    router.push(`/supervision-rules/${ruleId}/edit`)
  }

  // 处理规则删除
  const handleRuleDelete = async (ruleId: number) => {
    if (!confirm('确定要删除这个规则吗？此操作不可撤销。')) {
      return
    }

    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('删除规则失败')
      }

      // 更新本地状态
      setRules(prev => prev.filter(rule => rule.id !== ruleId))
      setSelectedRules(prev => prev.filter(id => id !== ruleId))

      toast({
        title: '删除成功',
        description: '规则已删除'
      })
    } catch (error) {
      toast({
        title: '删除失败',
        description: '无法删除规则',
        variant: 'destructive'
      })
    }
  }

  // 处理规则复制
  const handleRuleCopy = async (ruleId: number) => {
    try {
      const response = await fetch(`/api/supervision-rules/${ruleId}/copy`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('复制规则失败')
      }

      const data = await response.json()
      toast({
        title: '复制成功',
        description: '规则已复制，正在跳转到编辑页面'
      })

      router.push(`/supervision-rules/${data.data.id}/edit`)
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制规则',
        variant: 'destructive'
      })
    }
  }

  // 处理过滤器变化
  const handleFiltersChange = (newFilters: RuleListParams) => {
    setFilters(newFilters)
  }

  // 重置过滤器
  const handleFiltersReset = () => {
    setFilters({
      page: 1,
      pageSize: 20,
      sortBy: 'createdAt',
      sortOrder: 'DESC'
    })
  }

  // 批量执行选中规则
  const handleBatchExecute = () => {
    if (selectedRules.length === 0) {
      toast({
        title: '请选择规则',
        description: '请先选择要执行的规则',
        variant: 'destructive'
      })
      return
    }

    handleRuleExecute(selectedRules)
  }

  // 导出规则
  const handleExportRules = async () => {
    try {
      const response = await fetch('/api/supervision-rules/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ruleIds: selectedRules })
      })

      if (!response.ok) {
        throw new Error('导出失败')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `supervision-rules-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: '导出成功',
        description: '规则已导出到文件'
      })
    } catch (error) {
      toast({
        title: '导出失败',
        description: '无法导出规则',
        variant: 'destructive'
      })
    }
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题和操作 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <h1 className="text-2xl font-bold">监管规则管理</h1>
            </div>
            <div className="flex items-center gap-2">
              {selectedRules.length > 0 && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleBatchExecute}
                    disabled={isExecuting}
                  >
                    {isExecuting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4 mr-2" />
                    )}
                    批量执行 ({selectedRules.length})
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleExportRules}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    导出规则
                  </Button>
                </>
              )}
              <Button onClick={() => router.push('/supervision-rules/new')}>
                <Plus className="h-4 w-4 mr-2" />
                新建规则
              </Button>
            </div>
          </div>

          {/* 统计卡片 */}
          {statistics && (
            <RuleStatisticsCards 
              statistics={statistics} 
              isLoading={!statistics} 
            />
          )}

          {/* 主要内容 */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="list" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                规则列表
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                数据分析
              </TabsTrigger>
              <TabsTrigger value="execution" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                执行监控
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-6">
              {/* 过滤器 */}
              <RuleFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onReset={handleFiltersReset}
              />

              {/* 规则列表 */}
              <RuleList
                rules={rules}
                isLoading={isLoading}
                selectedRules={selectedRules}
                onRuleSelect={handleRuleSelect}
                onRuleToggle={handleRuleToggle}
                onRuleExecute={handleRuleExecute}
                onRuleEdit={handleRuleEdit}
                onRuleView={handleRuleView}
                onRuleDelete={handleRuleDelete}
                onRuleCopy={handleRuleCopy}
              />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6">
              {statistics && (
                <>
                  <DetailedRuleStatistics statistics={statistics} />
                  <ExecutionTrendCard statistics={statistics} />
                </>
              )}
            </TabsContent>

            <TabsContent value="execution" className="space-y-6">
              {/* 这里可以添加执行监控组件 */}
              <div className="text-center py-12 text-muted-foreground">
                执行监控功能开发中...
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
