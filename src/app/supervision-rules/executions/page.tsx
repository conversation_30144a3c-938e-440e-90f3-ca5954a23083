'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  Activity, 
  Search, 
  Filter, 
  Eye, 
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader2,
  Calendar,
  BarChart3
} from 'lucide-react'

interface ExecutionLog {
  id: number
  ruleId: number
  executionId: string
  executionStatus: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'TIMEOUT'
  startedAt: string
  endedAt?: string
  executionDuration?: number
  processedRecordCount: number
  matchedRecordCount: number
  errorMessage?: string
  ruleName?: string
  ruleCode?: string
  ruleCategory?: string
  severityLevel?: string
}

interface ExecutionStatistics {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageExecutionTime: number
  totalProcessedRecords: number
  totalMatchedRecords: number
}

// 获取状态显示
const getStatusDisplay = (status: string) => {
  switch (status) {
    case 'PENDING':
      return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />待执行</Badge>
    case 'RUNNING':
      return <Badge variant="default"><Loader2 className="w-3 h-3 mr-1 animate-spin" />执行中</Badge>
    case 'SUCCESS':
      return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />成功</Badge>
    case 'FAILED':
      return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />失败</Badge>
    case 'TIMEOUT':
      return <Badge variant="destructive"><AlertTriangle className="w-3 h-3 mr-1" />超时</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

// 格式化执行时间
const formatDuration = (duration?: number) => {
  if (!duration) return '-'
  if (duration < 1000) return `${duration}ms`
  if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`
  return `${(duration / 60000).toFixed(1)}m`
}

export default function RuleExecutionsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [executions, setExecutions] = useState<ExecutionLog[]>([])
  const [statistics, setStatistics] = useState<ExecutionStatistics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  })

  // 获取执行记录列表
  const fetchExecutions = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        search: searchTerm,
        ...(statusFilter !== 'all' && { executionStatus: statusFilter }),
      })

      const response = await fetch(`/api/supervision-rules/executions?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setExecutions(result.data.data)
        setPagination(prev => ({
          ...prev,
          total: result.data.pagination.total,
          totalPages: result.data.pagination.totalPages,
        }))
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取执行记录失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取执行记录失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) return

      const response = await fetch('/api/supervision-rules/executions?action=statistics', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setStatistics(result.data)
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  useEffect(() => {
    fetchExecutions()
    fetchStatistics()
  }, [pagination.page, searchTerm, statusFilter])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">执行记录</h1>
              <p className="text-muted-foreground">查看和管理规则执行历史记录</p>
            </div>
            <Button onClick={() => fetchExecutions()} disabled={isLoading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>

          {/* 统计卡片 */}
          {statistics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总执行次数</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.totalExecutions}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">成功率</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statistics.totalExecutions > 0 
                      ? `${((statistics.successfulExecutions / statistics.totalExecutions) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">平均执行时间</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatDuration(statistics.averageExecutionTime)}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">匹配记录数</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{statistics.totalMatchedRecords}</div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 搜索和筛选 */}
          <Card>
            <CardHeader>
              <CardTitle>执行记录列表</CardTitle>
              <CardDescription>查看所有规则执行记录和结果</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索执行ID、规则名称..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="PENDING">待执行</SelectItem>
                    <SelectItem value="RUNNING">执行中</SelectItem>
                    <SelectItem value="SUCCESS">成功</SelectItem>
                    <SelectItem value="FAILED">失败</SelectItem>
                    <SelectItem value="TIMEOUT">超时</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 执行记录表格 */}
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>执行ID</TableHead>
                        <TableHead>规则信息</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>执行时间</TableHead>
                        <TableHead>处理/匹配</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {executions.map((execution) => (
                        <TableRow key={execution.id}>
                          <TableCell>
                            <div className="font-mono text-sm">
                              {execution.executionId}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{execution.ruleName}</div>
                              <div className="text-sm text-muted-foreground">
                                {execution.ruleCode} • {execution.ruleCategory}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusDisplay(execution.executionStatus)}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{new Date(execution.startedAt).toLocaleString()}</div>
                              {execution.executionDuration && (
                                <div className="text-muted-foreground">
                                  耗时: {formatDuration(execution.executionDuration)}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>处理: {execution.processedRecordCount}</div>
                              <div>匹配: {execution.matchedRecordCount}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => router.push(`/supervision-rules/executions/${execution.id}`)}
                              title="查看详情"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* 分页 */}
              {!isLoading && pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    显示第 {((pagination.page - 1) * pagination.pageSize) + 1} - {Math.min(pagination.page * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                      disabled={pagination.page >= pagination.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
