/**
 * 规则引擎验证API
 * 
 * @description 提供规则代码的语法验证和错误检查
 * @route POST /api/rule-engine/validate - 验证规则代码
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'
import { validateRuleContent } from '@/lib/rule-engine/utils/rule-utils'

// 验证请求参数
const validateRequestSchema = z.object({
  code: z.string().min(1, '代码内容不能为空'),
  type: z.enum(['SQL', 'DSL', 'JAVASCRIPT'], {
    errorMap: () => ({ message: '规则类型必须是 SQL、DSL 或 JAVASCRIPT' })
  })
})

interface ValidationError {
  line: number
  column: number
  message: string
  severity: 'error' | 'warning' | 'info'
}

/**
 * POST API - 验证规则代码
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validatedData = validateRequestSchema.parse(body)
    
    const { code, type } = validatedData
    const errors: ValidationError[] = []

    /**
     * 根据规则类型进行相应的语法验证
     */
    switch (type) {
      case 'SQL':
        validateSqlCode(code, errors)
        break
      case 'DSL':
        validateDslCode(code, errors)
        break
      case 'JAVASCRIPT':
        validateJavaScriptCode(code, errors)
        break
    }

    // 使用现有的规则验证工具进行基础验证
    const isBasicValid = validateRuleContent(code, type)
    if (!isBasicValid && errors.length === 0) {
      errors.push({
        line: 1,
        column: 1,
        message: '规则内容格式不正确',
        severity: 'error'
      })
    }

    return {
      isValid: errors.filter(e => e.severity === 'error').length === 0,
      errors,
      warnings: errors.filter(e => e.severity === 'warning'),
      suggestions: generateSuggestions(code, type)
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    console.error('规则验证失败:', error)
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '规则验证失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * 验证SQL代码
 */
function validateSqlCode(code: string, errors: ValidationError[]) {
  const lines = code.split('\n')
  const trimmed = code.trim().toUpperCase()

  // 检查是否以SELECT开头
  if (!trimmed.startsWith('SELECT')) {
    errors.push({
      line: 1,
      column: 1,
      message: 'SQL查询必须以SELECT开头',
      severity: 'error'
    })
  }

  // 检查危险操作
  const dangerousKeywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE', 'EXEC']
  dangerousKeywords.forEach(keyword => {
    if (trimmed.includes(keyword)) {
      const lineIndex = lines.findIndex(line => line.toUpperCase().includes(keyword))
      const line = lines[lineIndex]
      errors.push({
        line: lineIndex + 1,
        column: line ? line.toUpperCase().indexOf(keyword) + 1 : 1,
        message: `不允许使用 ${keyword} 操作`,
        severity: 'error'
      })
    }
  })

  // 检查基本SQL语法
  const requiredKeywords = ['FROM']
  requiredKeywords.forEach(keyword => {
    if (!trimmed.includes(keyword)) {
      errors.push({
        line: 1,
        column: 1,
        message: `缺少必需的关键字: ${keyword}`,
        severity: 'error'
      })
    }
  })

  // 检查括号匹配
  let parenthesesCount = 0
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    if (!line) continue
    for (let j = 0; j < line.length; j++) {
      if (line[j] === '(') parenthesesCount++
      if (line[j] === ')') parenthesesCount--
      
      if (parenthesesCount < 0) {
        errors.push({
          line: i + 1,
          column: j + 1,
          message: '括号不匹配：多余的右括号',
          severity: 'error'
        })
        break
      }
    }
  }
  
  if (parenthesesCount > 0) {
    errors.push({
      line: lines.length,
      column: lines[lines.length - 1]?.length || 1,
      message: '括号不匹配：缺少右括号',
      severity: 'error'
    })
  }

  // 性能建议
  if (!trimmed.includes('LIMIT') && !trimmed.includes('WHERE')) {
    errors.push({
      line: 1,
      column: 1,
      message: '建议添加WHERE条件或LIMIT限制以提高查询性能',
      severity: 'warning'
    })
  }
}

/**
 * 验证DSL代码
 */
function validateDslCode(code: string, errors: ValidationError[]) {
  try {
    // 尝试解析YAML格式
    const lines = code.split('\n')
    
    // 检查基本结构
    if (!code.includes('rules:') && !code.includes('conditions:')) {
      errors.push({
        line: 1,
        column: 1,
        message: 'DSL规则必须包含 rules 或 conditions 定义',
        severity: 'error'
      })
    }

    // 检查缩进
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      if (line && line.trim() && line.match(/^\s+/) && line.match(/^\s+/)![0].length % 2 !== 0) {
        errors.push({
          line: i + 1,
          column: 1,
          message: 'YAML缩进应该使用2个空格',
          severity: 'warning'
        })
      }
    }

    // 检查冒号后的空格
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      if (line && line.includes(':') && !line.match(/:\s/) && !line.endsWith(':')) {
        errors.push({
          line: i + 1,
          column: line.indexOf(':') + 1,
          message: '冒号后应该有空格',
          severity: 'warning'
        })
      }
    }

  } catch (error) {
    errors.push({
      line: 1,
      column: 1,
      message: 'DSL格式解析失败',
      severity: 'error'
    })
  }
}

/**
 * 验证JavaScript代码
 */
function validateJavaScriptCode(code: string, errors: ValidationError[]) {
  const lines = code.split('\n')

  // 检查基本语法
  try {
    // 简单的语法检查
    new Function(code)
  } catch (error) {
    const errorMessage = (error as Error).message
    const lineMatch = errorMessage.match(/line (\d+)/)
    const line = lineMatch && lineMatch[1] ? parseInt(lineMatch[1]) : 1
    
    errors.push({
      line,
      column: 1,
      message: `JavaScript语法错误: ${errorMessage}`,
      severity: 'error'
    })
  }

  // 检查是否有返回值
  if (!code.includes('return')) {
    errors.push({
      line: lines.length,
      column: 1,
      message: '函数应该有返回值',
      severity: 'warning'
    })
  }

  // 检查危险操作
  const dangerousPatterns = [
    { pattern: /eval\s*\(/, message: '不建议使用 eval() 函数' },
    { pattern: /document\./, message: '不允许操作DOM' },
    { pattern: /window\./, message: '不允许访问window对象' },
    { pattern: /process\./, message: '不允许访问process对象' },
    { pattern: /require\s*\(/, message: '不允许使用require()' },
    { pattern: /import\s+/, message: '不允许使用import语句' }
  ]

  dangerousPatterns.forEach(({ pattern, message }) => {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      if (!line) continue
      const match = line.match(pattern)
      if (match) {
        errors.push({
          line: i + 1,
          column: match.index! + 1,
          message,
          severity: 'error'
        })
      }
    }
  })

  // 检查括号和大括号匹配
  let braceCount = 0
  let parenthesesCount = 0
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    if (!line) continue
    for (let j = 0; j < line.length; j++) {
      const char = line[j]
      if (char === '{') braceCount++
      if (char === '}') braceCount--
      if (char === '(') parenthesesCount++
      if (char === ')') parenthesesCount--
      
      if (braceCount < 0) {
        errors.push({
          line: i + 1,
          column: j + 1,
          message: '大括号不匹配：多余的右大括号',
          severity: 'error'
        })
        break
      }
      
      if (parenthesesCount < 0) {
        errors.push({
          line: i + 1,
          column: j + 1,
          message: '括号不匹配：多余的右括号',
          severity: 'error'
        })
        break
      }
    }
  }
  
  if (braceCount > 0) {
    errors.push({
      line: lines.length,
      column: lines[lines.length - 1]?.length || 1,
      message: '大括号不匹配：缺少右大括号',
      severity: 'error'
    })
  }
  
  if (parenthesesCount > 0) {
    errors.push({
      line: lines.length,
      column: lines[lines.length - 1]?.length || 1,
      message: '括号不匹配：缺少右括号',
      severity: 'error'
    })
  }
}

/**
 * 生成代码建议
 */
function generateSuggestions(code: string, type: string): string[] {
  const suggestions: string[] = []

  switch (type) {
    case 'SQL':
      if (!code.toUpperCase().includes('WHERE')) {
        suggestions.push('添加WHERE条件以提高查询效率')
      }
      if (!code.toUpperCase().includes('LIMIT')) {
        suggestions.push('添加LIMIT限制返回结果数量')
      }
      if (code.toUpperCase().includes('SELECT *')) {
        suggestions.push('避免使用SELECT *，明确指定需要的字段')
      }
      break
      
    case 'JAVASCRIPT':
      if (!code.includes('const') && !code.includes('let')) {
        suggestions.push('使用const或let声明变量，避免使用var')
      }
      if (code.includes('==') && !code.includes('===')) {
        suggestions.push('使用===进行严格相等比较')
      }
      break
      
    case 'DSL':
      if (!code.includes('description:')) {
        suggestions.push('添加description字段描述规则用途')
      }
      break
  }

  return suggestions
}
