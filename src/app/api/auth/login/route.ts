// 注意：此API使用withAuth处理器，自动处理：
// - 身份验证 (verifyAccessToken)
// - 权限检查 (hasRole)
// - 统一错误处理
// - 审计日志记录

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { authenticateUser } from '@/lib/user-service'
import { generateTokenPair } from '@/lib/jwt'
import { publicApi } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 登录请求验证schema
const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(6, '密码至少6位')
})

/**
 * 用户登录API
 * @description 用户通过用户名和密码进行登录认证
 * @param request 登录请求，包含用户名和密码
 * @returns 登录成功返回访问令牌和用户信息
 * @example
 * POST /api/auth/login
 * {
 *   "username": "admin",
 *   "password": "password123"
 * }
 *
 * Response:
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "登录成功",
 *   "data": {
 *     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *     "user": {
 *       "id": 1,
 *       "username": "admin",
 *       "roles": ["ADMIN"]
 *     }
 *   }
 * }
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const POST = publicApi(async (request: NextRequest) => {
  // 解析和验证请求数据
  const body = await request.json()
  const validatedData = loginSchema.parse(body)

  // 验证用户凭据
  const user = await authenticateUser(validatedData)

  if (!user) {
    throw new ApiError(ErrorCode.UNAUTHORIZED, '用户名或密码错误')
  }

  // 生成真正的JWT token
  const { accessToken, refreshToken } = generateTokenPair(user)

  // 返回登录成功响应
  return {
    user: {
      id: user.id,
      username: user.username,
      realName: user.realName,
      email: user.email,
      department: user.department,
      position: user.position,
      status: user.status,
      roles: user.roles,
      // 不返回敏感信息如密码
    },
    token: accessToken,
    refreshToken: refreshToken,
    expiresIn: 24 * 60 * 60 // 24小时，以秒为单位
  }
})
