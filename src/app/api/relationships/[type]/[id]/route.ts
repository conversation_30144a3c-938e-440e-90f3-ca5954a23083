import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database'
import { verifyAuth } from '@/lib/auth-utils'

/**
 * 获取数据关联关系API
 * 根据数据类型和ID查找相关联的数据
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { type: string; id: string } }
) {
  try {
    // 验证用户身份
    const authResult = await verifyAuth(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const { type, id } = params
    const relatedData = await getRelatedData(type, id)

    return NextResponse.json({
      success: true,
      data: relatedData
    })

  } catch (error) {
    console.error('获取关联数据失败:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '获取关联数据失败' 
      },
      { status: 500 }
    )
  }
}

/**
 * 根据数据类型和ID获取关联数据
 */
async function getRelatedData(type: string, id: string) {
  const relatedData: any[] = []

  switch (type) {
    case 'medical-case':
      relatedData.push(...await getMedicalCaseRelations(id))
      break
    case 'rule':
      relatedData.push(...await getRuleRelations(id))
      break
    case 'user':
      relatedData.push(...await getUserRelations(id))
      break
    case 'document':
      relatedData.push(...await getDocumentRelations(id))
      break
    default:
      throw new Error(`不支持的数据类型: ${type}`)
  }

  return relatedData
}

/**
 * 获取医疗案例的关联数据
 */
async function getMedicalCaseRelations(caseId: string) {
  const relations: any[] = []

  try {
    // 1. 相关的规则执行记录
    const ruleExecutions = await executeQuery(`
      SELECT 
        rer.ID,
        rer.RULE_ID,
        rer.RESULT_TYPE,
        rer.SEVERITY,
        rer.VIOLATION_AMOUNT,
        rer.EXECUTED_AT,
        sr.RULE_NAME,
        sr.RULE_DESCRIPTION
      FROM RULE_EXECUTION_RESULT rer
      LEFT JOIN SUPERVISION_RULE sr ON rer.RULE_ID = sr.ID
      WHERE rer.CASE_ID = :caseId
      ORDER BY rer.EXECUTED_AT DESC
      FETCH FIRST 10 ROWS ONLY
    `, { caseId })

    ruleExecutions.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'execution',
        title: `规则执行: ${row.RULE_NAME}`,
        description: `${row.RESULT_TYPE} - ${row.SEVERITY}`,
        status: row.RESULT_TYPE,
        metadata: {
          ruleId: row.RULE_ID,
          violationAmount: row.VIOLATION_AMOUNT,
          severity: row.SEVERITY
        },
        updatedAt: row.EXECUTED_AT,
        relationshipType: 'direct',
        relationshipStrength: row.RESULT_TYPE === 'VIOLATION' ? 0.9 : 0.6
      })
    })

    // 2. 相关的监管规则
    const relatedRules = await executeQuery(`
      SELECT DISTINCT
        sr.ID,
        sr.RULE_NAME,
        sr.RULE_DESCRIPTION,
        sr.CATEGORY,
        sr.SEVERITY_LEVEL,
        sr.STATUS
      FROM SUPERVISION_RULE sr
      INNER JOIN RULE_EXECUTION_RESULT rer ON sr.ID = rer.RULE_ID
      WHERE rer.CASE_ID = :caseId
      ORDER BY sr.RULE_NAME
      FETCH FIRST 5 ROWS ONLY
    `, { caseId })

    relatedRules.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'rule',
        title: row.RULE_NAME,
        description: row.RULE_DESCRIPTION,
        status: row.STATUS,
        metadata: {
          category: row.CATEGORY,
          severityLevel: row.SEVERITY_LEVEL
        },
        relationshipType: 'direct',
        relationshipStrength: 0.8
      })
    })

    // 3. 相关的知识库文档
    const relatedDocs = await executeQuery(`
      SELECT 
        kb.ID,
        kb.TITLE,
        kb.CATEGORY,
        kb.STATUS,
        kb.UPDATED_AT
      FROM KNOWLEDGE_BASE kb
      WHERE kb.CATEGORY IN (
        SELECT DISTINCT mc.MEDICAL_CATEGORY 
        FROM MEDICAL_CASE mc 
        WHERE mc.ID = :caseId
      )
      AND kb.IS_DELETED = 0
      ORDER BY kb.UPDATED_AT DESC
      FETCH FIRST 5 ROWS ONLY
    `, { caseId })

    relatedDocs.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'document',
        title: row.TITLE,
        description: `知识库文档 - ${row.CATEGORY}`,
        status: row.STATUS,
        metadata: {
          category: row.CATEGORY
        },
        updatedAt: row.UPDATED_AT,
        relationshipType: 'indirect',
        relationshipStrength: 0.4
      })
    })

  } catch (error) {
    console.error('获取医疗案例关联数据失败:', error)
  }

  return relations
}

/**
 * 获取规则的关联数据
 */
async function getRuleRelations(ruleId: string) {
  const relations: any[] = []

  try {
    // 1. 相关的医疗案例
    const relatedCases = await executeQuery(`
      SELECT DISTINCT
        mc.ID,
        mc.CASE_NUMBER,
        mc.PATIENT_NAME,
        mc.HOSPITAL_NAME,
        mc.MEDICAL_CATEGORY,
        mc.TOTAL_COST,
        rer.RESULT_TYPE,
        rer.EXECUTED_AT
      FROM MEDICAL_CASE mc
      INNER JOIN RULE_EXECUTION_RESULT rer ON mc.ID = rer.CASE_ID
      WHERE rer.RULE_ID = :ruleId
      ORDER BY rer.EXECUTED_AT DESC
      FETCH FIRST 10 ROWS ONLY
    `, { ruleId })

    relatedCases.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'medical-case',
        title: `${row.CASE_NUMBER} - ${row.PATIENT_NAME}`,
        description: `${row.HOSPITAL_NAME} - ${row.MEDICAL_CATEGORY}`,
        status: row.RESULT_TYPE,
        metadata: {
          totalCost: row.TOTAL_COST,
          medicalCategory: row.MEDICAL_CATEGORY
        },
        updatedAt: row.EXECUTED_AT,
        relationshipType: 'direct',
        relationshipStrength: row.RESULT_TYPE === 'VIOLATION' ? 0.9 : 0.6
      })
    })

    // 2. 执行记录统计
    const executionStats = await executeQuery(`
      SELECT 
        COUNT(*) as TOTAL_EXECUTIONS,
        COUNT(CASE WHEN RESULT_TYPE = 'VIOLATION' THEN 1 END) as VIOLATIONS,
        AVG(EXECUTION_TIME_MS) as AVG_EXECUTION_TIME
      FROM RULE_EXECUTION_RESULT
      WHERE RULE_ID = :ruleId
    `, { ruleId })

    if (executionStats.rows && executionStats.rows.length > 0) {
      const stats = executionStats.rows[0]
      relations.push({
        id: `stats-${ruleId}`,
        type: 'execution',
        title: '执行统计',
        description: `总执行次数: ${stats.TOTAL_EXECUTIONS}, 违规次数: ${stats.VIOLATIONS}`,
        metadata: {
          totalExecutions: stats.TOTAL_EXECUTIONS,
          violations: stats.VIOLATIONS,
          avgExecutionTime: stats.AVG_EXECUTION_TIME
        },
        relationshipType: 'direct',
        relationshipStrength: 0.7
      })
    }

  } catch (error) {
    console.error('获取规则关联数据失败:', error)
  }

  return relations
}

/**
 * 获取用户的关联数据
 */
async function getUserRelations(userId: string) {
  const relations: any[] = []

  try {
    // 1. 用户创建的医疗案例
    const userCases = await executeQuery(`
      SELECT 
        ID,
        CASE_NUMBER,
        PATIENT_NAME,
        HOSPITAL_NAME,
        MEDICAL_CATEGORY,
        CREATED_AT
      FROM MEDICAL_CASE
      WHERE CREATED_BY = :userId
      AND IS_DELETED = 0
      ORDER BY CREATED_AT DESC
      FETCH FIRST 10 ROWS ONLY
    `, { userId })

    userCases.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'medical-case',
        title: `${row.CASE_NUMBER} - ${row.PATIENT_NAME}`,
        description: `创建的案例 - ${row.HOSPITAL_NAME}`,
        metadata: {
          medicalCategory: row.MEDICAL_CATEGORY
        },
        updatedAt: row.CREATED_AT,
        relationshipType: 'direct',
        relationshipStrength: 0.8
      })
    })

    // 2. 用户执行的规则
    const userRuleExecutions = await executeQuery(`
      SELECT DISTINCT
        sr.ID,
        sr.RULE_NAME,
        sr.CATEGORY,
        COUNT(rel.ID) as EXECUTION_COUNT,
        MAX(rel.STARTED_AT) as LAST_EXECUTION
      FROM SUPERVISION_RULE sr
      INNER JOIN RULE_EXECUTION_LOG rel ON sr.ID = rel.RULE_ID
      WHERE rel.CREATED_BY = :userId
      GROUP BY sr.ID, sr.RULE_NAME, sr.CATEGORY
      ORDER BY LAST_EXECUTION DESC
      FETCH FIRST 5 ROWS ONLY
    `, { userId })

    userRuleExecutions.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'rule',
        title: row.RULE_NAME,
        description: `执行了 ${row.EXECUTION_COUNT} 次`,
        metadata: {
          category: row.CATEGORY,
          executionCount: row.EXECUTION_COUNT
        },
        updatedAt: row.LAST_EXECUTION,
        relationshipType: 'direct',
        relationshipStrength: 0.7
      })
    })

  } catch (error) {
    console.error('获取用户关联数据失败:', error)
  }

  return relations
}

/**
 * 获取文档的关联数据
 */
async function getDocumentRelations(documentId: string) {
  const relations: any[] = []

  try {
    // 1. 访问该文档的用户
    const documentUsers = await executeQuery(`
      SELECT DISTINCT
        u.ID,
        u.USERNAME,
        u.REAL_NAME,
        COUNT(kal.ID) as ACCESS_COUNT,
        MAX(kal.ACCESS_TIME) as LAST_ACCESS
      FROM USER_MANAGEMENT_USER u
      INNER JOIN KNOWLEDGE_ACCESS_LOG kal ON u.ID = kal.USER_ID
      WHERE kal.DOCUMENT_ID = :documentId
      GROUP BY u.ID, u.USERNAME, u.REAL_NAME
      ORDER BY ACCESS_COUNT DESC
      FETCH FIRST 10 ROWS ONLY
    `, { documentId })

    documentUsers.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'user',
        title: `${row.REAL_NAME} (${row.USERNAME})`,
        description: `访问了 ${row.ACCESS_COUNT} 次`,
        metadata: {
          accessCount: row.ACCESS_COUNT
        },
        updatedAt: row.LAST_ACCESS,
        relationshipType: 'direct',
        relationshipStrength: Math.min(row.ACCESS_COUNT / 10, 1)
      })
    })

    // 2. 相关的医疗案例（基于分类）
    const relatedCases = await executeQuery(`
      SELECT 
        mc.ID,
        mc.CASE_NUMBER,
        mc.PATIENT_NAME,
        mc.MEDICAL_CATEGORY,
        mc.CREATED_AT
      FROM MEDICAL_CASE mc
      WHERE mc.MEDICAL_CATEGORY IN (
        SELECT kb.CATEGORY 
        FROM KNOWLEDGE_BASE kb 
        WHERE kb.ID = :documentId
      )
      ORDER BY mc.CREATED_AT DESC
      FETCH FIRST 5 ROWS ONLY
    `, { documentId })

    relatedCases.rows?.forEach((row: any) => {
      relations.push({
        id: row.ID.toString(),
        type: 'medical-case',
        title: `${row.CASE_NUMBER} - ${row.PATIENT_NAME}`,
        description: `相关案例 - ${row.MEDICAL_CATEGORY}`,
        metadata: {
          medicalCategory: row.MEDICAL_CATEGORY
        },
        updatedAt: row.CREATED_AT,
        relationshipType: 'indirect',
        relationshipStrength: 0.5
      })
    })

  } catch (error) {
    console.error('获取文档关联数据失败:', error)
  }

  return relations
}
