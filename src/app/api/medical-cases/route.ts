import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth, withAuthAndValidation } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

/**
 * 医疗案例管理API - 参数说明
 * 
 * @param request HTTP请求对象
 * @param request.url 请求URL，包含查询参数
 * @param request.body 请求体数据（POST/PUT请求）
 * @param request.headers 请求头信息
 * @param context 上下文对象
 * @param context.user 当前用户信息
 * @param context.validatedData 验证后的请求数据
 * 
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - pageSize: 每页数量 (默认: 20)
 * - search: 搜索关键词 (可选)
 * 
 * 请求体参数 (POST/PUT):
 * - 根据具体API而定，详见schema验证
 */


// 简化的静态验证schema，避免动态验证导致的问题
const querySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  caseType: z.enum(['INPATIENT', 'OUTPATIENT']).optional(),
  medicalCategory: z.string().optional(),
  hospitalCode: z.string().optional(),
  admissionDateStart: z.string().optional(),
  admissionDateEnd: z.string().optional(),
  totalCostMin: z.coerce.number().optional(),
  totalCostMax: z.coerce.number().optional(),
  sortBy: z.enum(['ID', 'CREATED_AT', 'CASE_NUMBER', 'PATIENT_NAME', 'ADMISSION_DATE', 'TOTAL_COST']).default('CREATED_AT'),
  sortOrder: z.enum(['ASC', 'DESC']).default('DESC')
})

// 创建医疗案例验证schema（静态）
const createCaseSchema = z.object({
  caseNumber: z.string().min(1, '请输入案例编号'),
  patientName: z.string().min(1, '请输入患者姓名'),
  patientIdCard: z.string().min(18, '身份证号码格式不正确').max(18, '身份证号码格式不正确'),
  patientPhone: z.string().optional(),
  patientAge: z.number().min(0).max(150).optional(),
  patientGender: z.string().optional(),
  caseType: z.string().optional(),
  medicalCategory: z.string().optional(),
  hospitalName: z.string().min(1, '请输入医院名称'),
  hospitalCode: z.string().optional(),
  department: z.string().optional(),
  doctorName: z.string().optional(),
  admissionDate: z.string().optional(),
  dischargeDate: z.string().optional(),
  totalCost: z.number().min(0, '总费用不能为负数')
})

/**
 * GET API - 获取medical-cases数据
 */

/**
 * 医疗案例管理API - 响应示例
 * 
 * @example 成功响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": {
 *     // 具体数据结构根据API而定
 *   },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "performance": {
 *       "executionTime": 150,
 *       "requestId": "req_123456"
 *     }
 *   }
 * }
 * 
 * @example 错误响应示例
 * {
 *   "success": false,
 *   "code": "VALIDATION_ERROR",
 *   "message": "请求参数验证失败",
 *   "details": [
 *     {
 *       "path": ["field"],
 *       "message": "字段验证失败"
 *     }
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * 
 * @example 分页响应示例 (适用于列表API)
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "获取数据成功",
 *   "data": [
 *     // 数据项数组
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "pagination": {
 *       "page": 1,
 *       "pageSize": 20,
 *       "total": 100,
 *       "totalPages": 5,
 *       "hasNext": true,
 *       "hasPrev": false
 *     }
 *   }
 * }
 */

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数（使用静态验证）
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = querySchema.parse(queryParams)

    /**
     * 获取医疗案例列表 - 连接已有的医疗案例服务层
     *
     * @description 调用getMedicalCases服务函数获取分页医疗案例数据
     * @performance 支持分页查询和多种过滤条件，避免大数据集性能问题
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { getMedicalCases } = await import('@/lib/medical-case-service')

    const result = await getMedicalCases({
      page: validatedParams['page'],
      pageSize: validatedParams['pageSize'],
      search: validatedParams['search'],
      caseType: validatedParams['caseType'],
      medicalCategory: validatedParams['medicalCategory'],
      hospitalCode: validatedParams['hospitalCode'],
      admissionDateStart: validatedParams['admissionDateStart'],
      admissionDateEnd: validatedParams['admissionDateEnd'],
      totalCostMin: validatedParams['totalCostMin'],
      totalCostMax: validatedParams['totalCostMax'],
      sortBy: validatedParams['sortBy'],
      sortOrder: validatedParams['sortOrder']
    })

    console.log('🔍 医疗案例API返回数据:', {
      itemsCount: result.items?.length || 0,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      totalPages: result.totalPages,
      firstItem: result.items?.[0] || null,
      itemsIsArray: Array.isArray(result.items),
      itemsType: typeof result.items,
      resultKeys: Object.keys(result)
    })

    console.log('🔍 result.items详细信息:', {
      items: result.items,
      itemsLength: result.items?.length,
      itemsType: typeof result.items,
      itemsIsArray: Array.isArray(result.items),
      firstThreeItems: result.items?.slice(0, 3)
    })

    const responseData = {
      cases: result.items,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        totalCount: result.total,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1
      }
    }

    console.log('🔍 API最终返回数据结构:', {
      casesCount: responseData.cases?.length || 0,
      casesType: typeof responseData.cases,
      casesIsArray: Array.isArray(responseData.cases),
      pagination: responseData.pagination
    })

    return responseData

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取医疗案例列表失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * POST API - 创建医疗案例
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体（使用静态验证）
    const body = await request.json()
    const validatedData = createCaseSchema.parse(body)

    /**
     * 创建医疗案例 - 连接已有的医疗案例服务层
     *
     * @description 调用createMedicalCase服务函数创建新医疗案例
     * @security 已通过权限验证，仅ADMIN和OPERATOR可创建案例
     * @audit 自动记录创建操作日志
     */
    const { createMedicalCase, getMedicalCaseById } = await import('@/lib/medical-case-service')

    // 创建医疗案例（类型断言以解决动态schema的类型问题）
    const caseId = await createMedicalCase(validatedData as any, 1) // TODO: 从认证上下文获取用户ID

    // 获取创建的案例信息
    const newCase = await getMedicalCaseById(caseId)

    return {
      message: '医疗案例创建成功',
      case: newCase
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如案例编号重复）
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '案例编号已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建医疗案例失败')
  }
}, ['ADMIN', 'OPERATOR'])
