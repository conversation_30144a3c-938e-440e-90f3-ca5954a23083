/**
 * 系统API接口
 * 
 * 🔐 认证要求：需要有效的访问令牌
 * 🛡️  权限要求：根据具体API而定
 * 📝 审计日志：自动记录所有操作
 * 
 * @description 系统API接口
 * @param request HTTP请求对象，包含查询参数或请求体
 * @returns 标准API响应格式，包含数据和元信息
 * @example
 * // 请求示例
 * GET /api/endpoint?page=1&pageSize=20
 * 
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { ... },
 *   "meta": { "timestamp": "...", "performance": { ... } }
 * }
 */

/**
 * 医疗案例API
 * 已通过批量重构工具重构以符合API规范
 * TODO: 需要实现具体的业务逻辑
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 更新医疗案例请求体验证
const updateCaseSchema = z.object({
  patientName: z.string().min(1, '请输入患者姓名').optional(),
  patientPhone: z.string().optional(),
  patientAge: z.number().min(0).max(150).optional(),
  patientGender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  caseType: z.enum(['INPATIENT', 'OUTPATIENT']).optional(),
  medicalCategory: z.enum(['异地住院', '急诊住院', '普通住院', '门诊慢特病', '转外诊治住院', '生育住院', '统筹区内转院', '住院前急诊', '特药购药', '急诊转住院', '普通门诊', '药店购慢特病药', '定点药店购药', '村卫门诊', '特药门诊', '外伤住院', '日间手术', '急诊', '急诊2级危重', '急诊3级急症', '急诊1级濒危', '新冠门诊', '4级非急症', '辅助生殖门诊', '急诊(死亡)', '家庭医生签约']).optional(),
  hospitalName: z.string().min(1, '请输入医院名称').optional(),
  hospitalCode: z.string().optional(),
  department: z.string().optional(),
  doctorName: z.string().optional(),
  admissionDate: z.string().optional(),
  dischargeDate: z.string().optional(),
  totalCost: z.number().min(0, '总费用不能为负数').optional()
})

/**
 * GET API
 * @description 获取数据列表
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取案例ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const caseIdStr = pathSegments[pathSegments.length - 1]

    if (!caseIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少案例ID')
    }

    const caseId = parseInt(caseIdStr)

    if (isNaN(caseId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的案例ID')
    }

    /**
     * 获取医疗案例详情 - 连接已有的医疗案例服务层
     *
     * @description 调用getMedicalCaseById服务函数获取案例详细信息
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { getMedicalCaseById } = await import('@/lib/medical-case-service')

    const medicalCase = await getMedicalCaseById(caseId)

    if (!medicalCase) {
      throw new ApiError(ErrorCode.NOT_FOUND, '医疗案例不存在')
    }

    return {
      case: medicalCase
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取医疗案例详情失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * PUT API - 更新医疗案例
 */
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取案例ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const caseIdStr = pathSegments[pathSegments.length - 1]

    if (!caseIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少案例ID')
    }

    const caseId = parseInt(caseIdStr)

    if (isNaN(caseId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的案例ID')
    }

    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = updateCaseSchema.parse(body)

    /**
     * 更新医疗案例 - 连接已有的医疗案例服务层
     *
     * @description 调用updateMedicalCase服务函数更新案例信息
     * @security 已通过权限验证，仅ADMIN和OPERATOR可更新案例
     * @audit 自动记录更新操作日志
     */
    const { updateMedicalCase, getMedicalCaseById } = await import('@/lib/medical-case-service')

    // 检查案例是否存在
    const existingCase = await getMedicalCaseById(caseId)
    if (!existingCase) {
      throw new ApiError(ErrorCode.NOT_FOUND, '医疗案例不存在')
    }

    // 更新案例
    await updateMedicalCase(caseId, validatedData, 1) // TODO: 从认证上下文获取用户ID

    // 获取更新后的案例信息
    const updatedCase = await getMedicalCaseById(caseId)

    return {
      message: '医疗案例更新成功',
      case: updatedCase
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '更新医疗案例失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * DELETE API - 删除医疗案例
 */
export const DELETE = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取案例ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const caseIdStr = pathSegments[pathSegments.length - 1]

    if (!caseIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少案例ID')
    }

    const caseId = parseInt(caseIdStr)

    if (isNaN(caseId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的案例ID')
    }

    /**
     * 删除医疗案例 - 连接已有的医疗案例服务层
     *
     * @description 调用deleteMedicalCase服务函数软删除案例
     * @security 已通过权限验证，仅ADMIN可删除案例
     * @audit 自动记录删除操作日志
     */
    const { deleteMedicalCase, getMedicalCaseById } = await import('@/lib/medical-case-service')

    // 检查案例是否存在
    const existingCase = await getMedicalCaseById(caseId)
    if (!existingCase) {
      throw new ApiError(ErrorCode.NOT_FOUND, '医疗案例不存在')
    }

    // 软删除案例
    await deleteMedicalCase(caseId, 1) // TODO: 从认证上下文获取用户ID

    return {
      message: '医疗案例删除成功'
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '删除医疗案例失败')
  }
}, ['ADMIN'])
