/**
 * 系统配置变更历史API
 * 
 * @description 提供系统配置变更历史的查询功能
 * @route GET /api/system-config/history - 获取配置变更历史
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'
import { executeQuery } from '@/lib/database'

// 查询参数验证
const historyQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  changeType: z.enum(['CREATE', 'UPDATE', 'DELETE', 'ACTIVATE', 'DEACTIVATE']).optional(),
  configItemId: z.coerce.number().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.enum(['CREATED_AT', 'CHANGE_TYPE', 'CONFIG_ITEM_ID']).default('CREATED_AT'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

/**
 * GET API - 获取配置变更历史
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = historyQuerySchema.parse(queryParams)

    const {
      page,
      pageSize,
      search,
      changeType,
      configItemId,
      startDate,
      endDate,
      sortBy,
      sortOrder
    } = validatedParams

    // 构建WHERE条件
    const whereConditions: string[] = []
    const queryParamsObj: any = {}

    if (search) {
      whereConditions.push(`(
        sci.CONFIG_KEY LIKE :search OR
        sci.CONFIG_LABEL LIKE :search OR
        sch.CHANGE_REASON LIKE :search
      )`)
      queryParamsObj.search = `%${search}%`
    }

    if (changeType) {
      whereConditions.push('sch.CHANGE_TYPE = :changeType')
      queryParamsObj.changeType = changeType
    }

    if (configItemId) {
      whereConditions.push('sch.CONFIG_ITEM_ID = :configItemId')
      queryParamsObj.configItemId = configItemId
    }

    if (startDate) {
      whereConditions.push('sch.CREATED_AT >= :startDate')
      queryParamsObj.startDate = startDate
    }

    if (endDate) {
      whereConditions.push('sch.CREATED_AT <= :endDate')
      queryParamsObj.endDate = endDate
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : ''

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM SYSTEM_CONFIG_HISTORY sch
      LEFT JOIN SYSTEM_CONFIG_ITEM sci ON sch.CONFIG_ITEM_ID = sci.ID
      ${whereClause}
    `

    const countResult = await executeQuery(countSql, queryParamsObj)
    const total = countResult.rows[0]?.TOTAL || 0
    const totalPages = Math.ceil(total / pageSize)

    // 构建主查询
    const offset = (page - 1) * pageSize
    const orderClause = `ORDER BY sch.${sortBy} ${sortOrder.toUpperCase()}`

    const mainSql = `
      SELECT
        sch.ID,
        sch.CONFIG_ITEM_ID,
        sch.OLD_VALUE,
        sch.NEW_VALUE,
        sch.CHANGE_REASON,
        sch.CHANGE_TYPE,
        sch.CREATED_AT,
        sch.CREATED_BY,
        sci.CONFIG_KEY,
        sci.CONFIG_LABEL,
        'Admin' as CREATED_BY_NAME
      FROM SYSTEM_CONFIG_HISTORY sch
      LEFT JOIN SYSTEM_CONFIG_ITEM sci ON sch.CONFIG_ITEM_ID = sci.ID
      ${whereClause}
      ${orderClause}
      OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
    `

    const mainResult = await executeQuery(mainSql, {
      ...queryParamsObj,
      offset,
      pageSize
    })

    // 格式化结果
    const history = mainResult.rows.map(row => ({
      id: row.ID,
      configItemId: row.CONFIG_ITEM_ID,
      oldValue: row.OLD_VALUE,
      newValue: row.NEW_VALUE,
      changeReason: row.CHANGE_REASON,
      changeType: row.CHANGE_TYPE,
      createdAt: row.CREATED_AT,
      createdBy: row.CREATED_BY,
      configKey: row.CONFIG_KEY,
      configLabel: row.CONFIG_LABEL,
      createdByName: row.CREATED_BY_NAME
    }))

    return {
      history,
      pagination: {
        page,
        pageSize,
        totalCount: total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    console.error('获取配置变更历史失败:', error)
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取配置变更历史失败')
  }
}, ['ADMIN'])
