/**
 * 系统配置分类管理API
 * 
 * @description 提供系统配置分类的CRUD操作
 * @route GET /api/system-config/categories - 获取配置分类列表
 * @route POST /api/system-config/categories - 创建配置分类
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 查询参数验证
const categoryQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  isActive: z.enum(['true', 'false']).optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
  sortBy: z.enum(['SORT_ORDER', 'CATEGORY_CODE', 'CATEGORY_NAME', 'CREATED_AT']).default('SORT_ORDER'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// 创建配置分类验证
const createCategorySchema = z.object({
  categoryCode: z.string().min(1, '请输入分类编码').max(50, '分类编码不能超过50个字符'),
  categoryName: z.string().min(1, '请输入分类名称').max(100, '分类名称不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  sortOrder: z.number().min(0).default(0)
})

/**
 * GET API - 获取配置分类列表
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = categoryQuerySchema.parse(queryParams)

    /**
     * 获取系统配置分类列表
     * 
     * @description 调用getConfigCategories服务函数获取分页配置分类数据
     * @performance 支持分页查询和搜索过滤，避免大数据集性能问题
     * @security 已通过权限验证，仅ADMIN可访问
     */
    const { getConfigCategories } = await import('@/lib/system-config-service')
    
    // 转换sortOrder为大写（数据服务层期望的格式）
    const sortOrder = validatedParams.sortOrder === 'asc' ? 'ASC' : 'DESC'
    
    const result = await getConfigCategories({
      page: validatedParams.page,
      pageSize: validatedParams.pageSize,
      search: validatedParams.search,
      isActive: validatedParams.isActive,
      sortBy: validatedParams.sortBy,
      sortOrder
    })

    return {
      categories: result.items,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        totalCount: result.total,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1
      }
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取配置分类列表失败')
  }
}, ['ADMIN'])

/**
 * POST API - 创建配置分类
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = createCategorySchema.parse(body)

    /**
     * 创建系统配置分类
     * 
     * @description 调用createConfigCategory服务函数创建新配置分类
     * @security 已通过权限验证，仅ADMIN可创建配置分类
     * @audit 自动记录创建操作日志
     */
    const { createConfigCategory, getConfigCategoryById } = await import('@/lib/system-config-service')
    
    // 创建配置分类
    const categoryId = await createConfigCategory(validatedData, 1) // TODO: 从认证上下文获取用户ID

    // 获取创建的分类信息
    const newCategory = await getConfigCategoryById(categoryId)

    return {
      message: '配置分类创建成功',
      category: newCategory
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如分类编码重复）
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '分类编码已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建配置分类失败')
  }
}, ['ADMIN'])
