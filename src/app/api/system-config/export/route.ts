/**
 * 系统配置导出API
 * 
 * @description 导出系统配置数据为JSON文件
 * @route GET /api/system-config/export - 导出配置数据
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'
import { executeQuery } from '@/lib/database'

/**
 * GET API - 导出系统配置
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    /**
     * 导出完整的系统配置数据
     * 
     * @description 包含配置分类、配置项和元数据信息
     * @security 已通过权限验证，仅ADMIN可导出
     */

    // 获取所有配置分类
    const categoriesResult = await executeQuery(`
      SELECT 
        ID, CATEGORY_CODE, CATEGORY_NAME, DESCRIPTION, 
        SORT_ORDER, IS_ACTIVE, CREATED_AT, UPDATED_AT
      FROM SYSTEM_CONFIG_CATEGORY 
      WHERE IS_DELETED = 0
      ORDER BY SORT_ORDER, CATEGORY_CODE
    `, {})

    // 获取所有配置项
    const itemsResult = await executeQuery(`
      SELECT 
        sci.ID, sci.CATEGORY_ID, sci.CONFIG_KEY, sci.CONFIG_VALUE, 
        sci.CONFIG_LABEL, sci.DESCRIPTION, sci.DATA_TYPE, 
        sci.IS_REQUIRED, sci.SORT_ORDER, sci.IS_ACTIVE,
        sci.CREATED_AT, sci.UPDATED_AT,
        scc.CATEGORY_CODE
      FROM SYSTEM_CONFIG_ITEM sci
      INNER JOIN SYSTEM_CONFIG_CATEGORY scc ON sci.CATEGORY_ID = scc.ID
      WHERE sci.IS_DELETED = 0 AND scc.IS_DELETED = 0
      ORDER BY scc.SORT_ORDER, scc.CATEGORY_CODE, sci.SORT_ORDER, sci.CONFIG_KEY
    `, {})

    // 格式化配置分类
    const categories = categoriesResult.rows.map(row => ({
      id: row.ID,
      categoryCode: row.CATEGORY_CODE,
      categoryName: row.CATEGORY_NAME,
      description: row.DESCRIPTION,
      sortOrder: row.SORT_ORDER,
      isActive: row.IS_ACTIVE === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT
    }))

    // 格式化配置项
    const items = itemsResult.rows.map(row => ({
      id: row.ID,
      categoryId: row.CATEGORY_ID,
      categoryCode: row.CATEGORY_CODE,
      configKey: row.CONFIG_KEY,
      configValue: row.CONFIG_VALUE,
      configLabel: row.CONFIG_LABEL,
      description: row.DESCRIPTION,
      dataType: row.DATA_TYPE,
      isRequired: row.IS_REQUIRED === 1,
      sortOrder: row.SORT_ORDER,
      isActive: row.IS_ACTIVE === 1,
      createdAt: row.CREATED_AT,
      updatedAt: row.UPDATED_AT
    }))

    // 按分类组织配置项
    const itemsByCategory: Record<string, any[]> = {}
    items.forEach(item => {
      if (!itemsByCategory[item.categoryCode]) {
        itemsByCategory[item.categoryCode] = []
      }
      itemsByCategory[item.categoryCode]?.push(item)
    })

    // 构建导出数据
    const exportData = {
      metadata: {
        exportTime: new Date().toISOString(),
        exportBy: 'system', // TODO: 从认证上下文获取用户信息
        version: '1.0',
        description: '医保基金监管平台系统配置导出',
        totalCategories: categories.length,
        totalItems: items.length,
        activeCategories: categories.filter(c => c.isActive).length,
        activeItems: items.filter(i => i.isActive).length
      },
      categories: categories,
      items: items,
      itemsByCategory: itemsByCategory,
      dynamicConfig: {} as Record<string, string[]>
    }

    // 生成动态配置映射（仅包含启用的配置项）
    categories.forEach(category => {
      if (category.isActive) {
        const categoryItems = items.filter(item => 
          item.categoryCode === category.categoryCode && item.isActive
        )
        exportData.dynamicConfig[category.categoryCode] = categoryItems.map(item => item.configValue)
      }
    })

    // 设置响应头为文件下载
    const fileName = `system-config-export-${new Date().toISOString().split('T')[0]}.json`
    
    return new Response(JSON.stringify(exportData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Cache-Control': 'no-cache'
      }
    })

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('导出系统配置失败:', error)
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '导出系统配置失败')
  }
}, ['ADMIN'])
