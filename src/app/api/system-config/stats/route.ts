/**
 * 系统配置统计API
 * 
 * @description 提供系统配置的统计信息
 * @route GET /api/system-config/stats - 获取配置统计信息
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'
import { executeQuery } from '@/lib/database'

/**
 * GET API - 获取系统配置统计信息
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    /**
     * 获取系统配置统计数据
     * 
     * @description 统计配置分类、配置项数量和最后更新时间
     * @security 已通过权限验证，仅ADMIN可访问
     */
    
    // 获取配置分类统计
    const categoryStatsResult = await executeQuery(`
      SELECT 
        COUNT(*) as TOTAL_CATEGORIES,
        COUNT(CASE WHEN IS_ACTIVE = 1 THEN 1 END) as ACTIVE_CATEGORIES
      FROM SYSTEM_CONFIG_CATEGORY 
      WHERE IS_DELETED = 0
    `, {})

    // 获取配置项统计
    const itemStatsResult = await executeQuery(`
      SELECT 
        COUNT(*) as TOTAL_ITEMS,
        COUNT(CASE WHEN IS_ACTIVE = 1 THEN 1 END) as ACTIVE_ITEMS
      FROM SYSTEM_CONFIG_ITEM 
      WHERE IS_DELETED = 0
    `, {})

    // 获取最后更新时间
    const lastUpdatedResult = await executeQuery(`
      SELECT MAX(UPDATED_AT) as LAST_UPDATED
      FROM (
        SELECT UPDATED_AT FROM SYSTEM_CONFIG_CATEGORY WHERE IS_DELETED = 0
        UNION ALL
        SELECT UPDATED_AT FROM SYSTEM_CONFIG_ITEM WHERE IS_DELETED = 0
      )
    `, {})

    const categoryStats = categoryStatsResult.rows[0]
    const itemStats = itemStatsResult.rows[0]
    const lastUpdated = lastUpdatedResult.rows[0]?.LAST_UPDATED

    // 格式化最后更新时间
    let formattedLastUpdated = '未知'
    if (lastUpdated) {
      try {
        formattedLastUpdated = new Date(lastUpdated).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (error) {
        console.error('时间格式化失败:', error)
      }
    }

    return {
      totalCategories: categoryStats?.TOTAL_CATEGORIES || 0,
      totalItems: itemStats?.TOTAL_ITEMS || 0,
      activeItems: itemStats?.ACTIVE_ITEMS || 0,
      lastUpdated: formattedLastUpdated
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('获取配置统计失败:', error)
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取配置统计失败')
  }
}, ['ADMIN'])
