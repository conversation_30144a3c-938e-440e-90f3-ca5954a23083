/**
 * 系统配置项详情管理API
 * 
 * @description 提供单个配置项的详情、更新、删除操作
 * @route GET /api/system-config/items/[id] - 获取配置项详情
 * @route PUT /api/system-config/items/[id] - 更新配置项
 * @route DELETE /api/system-config/items/[id] - 删除配置项
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 更新配置项验证
const updateItemSchema = z.object({
  configValue: z.string().min(1, '请输入配置值').max(500, '配置值不能超过500个字符').optional(),
  configLabel: z.string().min(1, '请输入配置标签').max(200, '配置标签不能超过200个字符').optional(),
  description: z.string().max(1000, '描述不能超过1000个字符').optional(),
  dataType: z.enum(['STRING', 'NUMBER', 'BOOLEAN', 'JSON']).optional(),
  isRequired: z.boolean().optional(),
  sortOrder: z.number().min(0).optional(),
  isActive: z.boolean().optional(),
  changeReason: z.string().max(1000, '变更原因不能超过1000个字符').optional()
})

/**
 * GET API - 获取配置项详情
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取配置项ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const itemIdStr = pathSegments[pathSegments.length - 1]
    
    if (!itemIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少配置项ID')
    }
    
    const itemId = parseInt(itemIdStr)
    
    if (isNaN(itemId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的配置项ID')
    }

    /**
     * 获取系统配置项详情
     * 
     * @description 调用getConfigItemById服务函数获取配置项详细信息
     * @security 已通过权限验证，仅ADMIN可访问
     */
    const { getConfigItemById } = await import('@/lib/system-config-service')
    
    const item = await getConfigItemById(itemId)
    
    if (!item) {
      throw new ApiError(ErrorCode.NOT_FOUND, '配置项不存在')
    }

    return {
      item
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取配置项详情失败')
  }
}, ['ADMIN'])

/**
 * PUT API - 更新配置项
 */
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取配置项ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const itemIdStr = pathSegments[pathSegments.length - 1]
    
    if (!itemIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少配置项ID')
    }
    
    const itemId = parseInt(itemIdStr)
    
    if (isNaN(itemId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的配置项ID')
    }

    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = updateItemSchema.parse(body)

    /**
     * 更新系统配置项
     * 
     * @description 调用updateConfigItem服务函数更新配置项信息
     * @security 已通过权限验证，仅ADMIN可更新配置项
     * @audit 自动记录更新操作日志和变更历史
     */
    const { updateConfigItem, getConfigItemById } = await import('@/lib/system-config-service')
    
    // 检查配置项是否存在
    const existingItem = await getConfigItemById(itemId)
    if (!existingItem) {
      throw new ApiError(ErrorCode.NOT_FOUND, '配置项不存在')
    }

    // 更新配置项
    await updateConfigItem(
      itemId, 
      validatedData, 
      1, // TODO: 从认证上下文获取用户ID
      validatedData.changeReason
    )

    // 获取更新后的配置项信息
    const updatedItem = await getConfigItemById(itemId)

    return {
      message: '配置项更新成功',
      item: updatedItem
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '更新配置项失败')
  }
}, ['ADMIN'])

/**
 * DELETE API - 删除配置项
 */
export const DELETE = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取配置项ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const itemIdStr = pathSegments[pathSegments.length - 1]
    
    if (!itemIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少配置项ID')
    }
    
    const itemId = parseInt(itemIdStr)
    
    if (isNaN(itemId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的配置项ID')
    }

    // 解析请求体获取删除原因
    const body = await request.json().catch(() => ({}))
    const changeReason = body.changeReason || '删除配置项'

    /**
     * 删除系统配置项
     * 
     * @description 调用deleteConfigItem服务函数软删除配置项
     * @security 已通过权限验证，仅ADMIN可删除配置项
     * @audit 自动记录删除操作日志和变更历史
     */
    const { deleteConfigItem, getConfigItemById } = await import('@/lib/system-config-service')
    
    // 检查配置项是否存在
    const existingItem = await getConfigItemById(itemId)
    if (!existingItem) {
      throw new ApiError(ErrorCode.NOT_FOUND, '配置项不存在')
    }

    // 软删除配置项
    await deleteConfigItem(itemId, 1, changeReason) // TODO: 从认证上下文获取用户ID

    return {
      message: '配置项删除成功'
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '删除配置项失败')
  }
}, ['ADMIN'])
