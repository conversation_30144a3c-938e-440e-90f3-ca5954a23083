/**
 * 系统配置项管理API
 * 
 * @description 提供系统配置项的CRUD操作
 * @route GET /api/system-config/items - 获取配置项列表
 * @route POST /api/system-config/items - 创建配置项
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 查询参数验证
const itemQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  categoryCode: z.string().optional(),
  search: z.string().optional(),
  isActive: z.enum(['true', 'false']).optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
  sortBy: z.enum(['SORT_ORDER', 'CONFIG_KEY', 'CONFIG_LABEL', 'CREATED_AT']).default('SORT_ORDER'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// 创建配置项验证
const createItemSchema = z.object({
  categoryId: z.number().min(1, '请选择配置分类'),
  configKey: z.string().min(1, '请输入配置键').max(100, '配置键不能超过100个字符'),
  configValue: z.string().min(1, '请输入配置值').max(500, '配置值不能超过500个字符'),
  configLabel: z.string().min(1, '请输入配置标签').max(200, '配置标签不能超过200个字符'),
  description: z.string().max(1000, '描述不能超过1000个字符').optional(),
  dataType: z.enum(['STRING', 'NUMBER', 'BOOLEAN', 'JSON']).default('STRING'),
  isRequired: z.boolean().default(true),
  sortOrder: z.number().min(0).default(0)
})

/**
 * GET API - 获取配置项列表
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = itemQuerySchema.parse(queryParams)

    /**
     * 获取系统配置项列表
     * 
     * @description 调用getConfigItems服务函数获取分页配置项数据
     * @performance 支持分页查询、分类过滤和搜索，避免大数据集性能问题
     * @security 已通过权限验证，仅ADMIN可访问
     */
    const { getConfigItems } = await import('@/lib/system-config-service')
    
    // 转换sortOrder为大写（数据服务层期望的格式）
    const sortOrder = validatedParams.sortOrder === 'asc' ? 'ASC' : 'DESC'
    
    const result = await getConfigItems({
      page: validatedParams.page,
      pageSize: validatedParams.pageSize,
      categoryCode: validatedParams.categoryCode,
      search: validatedParams.search,
      isActive: validatedParams.isActive,
      sortBy: validatedParams.sortBy,
      sortOrder
    })

    return {
      items: result.items,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        totalCount: result.total,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1
      }
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取配置项列表失败')
  }
}, ['ADMIN'])

/**
 * POST API - 创建配置项
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = createItemSchema.parse(body)

    /**
     * 创建系统配置项
     * 
     * @description 调用createConfigItem服务函数创建新配置项
     * @security 已通过权限验证，仅ADMIN可创建配置项
     * @audit 自动记录创建操作日志和变更历史
     */
    const { createConfigItem, getConfigItemById } = await import('@/lib/system-config-service')
    
    // 创建配置项
    const itemId = await createConfigItem(validatedData, 1) // TODO: 从认证上下文获取用户ID

    // 获取创建的配置项信息
    const newItem = await getConfigItemById(itemId)

    return {
      message: '配置项创建成功',
      item: newItem
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如配置键重复）
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '配置键在该分类下已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建配置项失败')
  }
}, ['ADMIN'])
