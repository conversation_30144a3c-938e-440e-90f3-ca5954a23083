/**
 * 动态配置获取API
 * 
 * @description 为其他API提供动态配置数据，用于替代硬编码枚举值
 * @route GET /api/system-config/dynamic - 获取所有有效配置项
 * @route GET /api/system-config/dynamic?category=MEDICAL_CATEGORY - 获取指定分类的配置项
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 查询参数验证
const dynamicQuerySchema = z.object({
  category: z.string().optional(),
  format: z.enum(['map', 'array', 'values']).default('map')
})

/**
 * GET API - 获取动态配置
 */
export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = dynamicQuerySchema.parse(queryParams)

    /**
     * 获取动态配置数据
     * 
     * @description 根据请求参数返回不同格式的配置数据
     * @performance 支持缓存，避免频繁数据库查询
     * @security 已通过权限验证，支持多种角色访问
     */
    const { getAllActiveConfigItems, getConfigItemsByCategory } = await import('@/lib/system-config-service')
    
    if (validatedParams.category) {
      // 获取指定分类的配置项
      const items = await getConfigItemsByCategory(validatedParams.category)
      
      switch (validatedParams.format) {
        case 'array':
          return {
            category: validatedParams.category,
            items: items.map(item => ({
              key: item.configKey,
              value: item.configValue,
              label: item.configLabel,
              description: item.description
            }))
          }
        
        case 'values':
          return {
            category: validatedParams.category,
            values: items.map(item => item.configValue)
          }
        
        case 'map':
        default:
          const itemMap: Record<string, string> = {}
          items.forEach(item => {
            itemMap[item.configKey] = item.configValue
          })
          return {
            category: validatedParams.category,
            map: itemMap
          }
      }
    } else {
      // 获取所有配置项
      const configMap = await getAllActiveConfigItems()
      
      switch (validatedParams.format) {
        case 'array':
          const arrayResult: Record<string, Array<{key: string, value: string}>> = {}
          Object.entries(configMap).forEach(([category, values]) => {
            arrayResult[category] = values.map((value, index) => ({
              key: `${category}_${index}`,
              value
            }))
          })
          return { configs: arrayResult }
        
        case 'values':
          return { configs: configMap }
        
        case 'map':
        default:
          return { configs: configMap }
      }
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取动态配置失败')
  }
}, ['ADMIN', 'OPERATOR', 'VIEWER'])

/**
 * 配置缓存管理
 * 
 * @description 提供配置缓存的刷新功能
 */
let configCache: Record<string, string[]> | null = null
let cacheTimestamp: number = 0
const CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

/**
 * 获取缓存的配置数据
 */
export async function getCachedConfig(): Promise<Record<string, string[]>> {
  const now = Date.now()
  
  if (!configCache || (now - cacheTimestamp) > CACHE_TTL) {
    const { getAllActiveConfigItems } = await import('@/lib/system-config-service')
    configCache = await getAllActiveConfigItems()
    cacheTimestamp = now
  }
  
  return configCache
}

/**
 * 清除配置缓存
 */
export function clearConfigCache(): void {
  configCache = null
  cacheTimestamp = 0
}

/**
 * POST API - 刷新配置缓存
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    /**
     * 刷新配置缓存
     * 
     * @description 清除当前缓存并重新加载配置数据
     * @security 已通过权限验证，仅ADMIN可刷新缓存
     */
    clearConfigCache()
    const newConfig = await getCachedConfig()
    
    return {
      message: '配置缓存刷新成功',
      timestamp: new Date().toISOString(),
      categoriesCount: Object.keys(newConfig).length,
      totalItemsCount: Object.values(newConfig).reduce((sum, items) => sum + items.length, 0)
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '刷新配置缓存失败')
  }
}, ['ADMIN'])
