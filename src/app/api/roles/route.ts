/**
 * 系统API接口
 * 
 * 🔐 认证要求：需要有效的访问令牌
 * 🛡️  权限要求：根据具体API而定
 * 📝 审计日志：自动记录所有操作
 * 
 * @description 系统API接口
 * @param request HTTP请求对象，包含查询参数或请求体
 * @returns 标准API响应格式，包含数据和元信息
 * @example
 * // 请求示例
 * GET /api/endpoint?page=1&pageSize=20
 * 
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { ... },
 *   "meta": { "timestamp": "...", "performance": { ... } }
 * }
 */

/**
 * API接口
 * 已通过批量重构工具重构以符合API规范
 * TODO: 需要实现具体的业务逻辑
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 查询参数验证schema
const querySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  isActive: z.enum(['true', 'false']).optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined)
})

// 创建角色请求体验证
const createRoleSchema = z.object({
  roleCode: z.enum(['ADMIN', 'SUPERVISOR', 'OPERATOR', 'AUDITOR', 'VIEWER']),
  roleName: z.string().min(1, '请输入角色名称').max(50, '角色名称最多50位'),
  description: z.string().optional(),
  permissionIds: z.array(z.number()).min(1, '请至少选择一个权限')
})

/**
 * GET API
 * @description 获取数据列表
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = querySchema.parse(queryParams)

    /**
     * 获取角色列表 - 连接已有的角色服务层
     *
     * @description 调用getAllRoles服务函数获取角色列表
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { getAllRoles } = await import('@/lib/user-service')

    let roles = await getAllRoles()

    // 根据查询参数过滤角色
    if (validatedParams.search) {
      const searchTerm = validatedParams.search.toLowerCase()
      roles = roles.filter(role =>
        role.roleName.toLowerCase().includes(searchTerm) ||
        role.roleCode.toLowerCase().includes(searchTerm) ||
        (role.description && role.description.toLowerCase().includes(searchTerm))
      )
    }

    if (validatedParams.isActive !== undefined) {
      roles = roles.filter(role => role.isActive === validatedParams.isActive)
    }

    // 实现分页
    const totalCount = roles.length
    const totalPages = Math.ceil(totalCount / validatedParams.pageSize)
    const startIndex = (validatedParams.page - 1) * validatedParams.pageSize
    const endIndex = startIndex + validatedParams.pageSize
    const paginatedRoles = roles.slice(startIndex, endIndex)

    return {
      roles: paginatedRoles,
      pagination: {
        page: validatedParams.page,
        pageSize: validatedParams.pageSize,
        totalCount,
        totalPages,
        hasNext: validatedParams.page < totalPages,
        hasPrev: validatedParams.page > 1
      }
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取角色列表失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * POST API - 创建角色
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = createRoleSchema.parse(body)

    /**
     * 创建角色 - 连接已有的角色服务层
     *
     * @description 调用createRole服务函数创建新角色
     * @security 已通过权限验证，仅ADMIN可创建角色
     * @audit 自动记录创建操作日志
     */
    const { createRole } = await import('@/lib/user-service')

    // 创建角色
    const newRole = await createRole({
      roleCode: validatedData.roleCode,
      roleName: validatedData.roleName,
      description: validatedData.description,
      permissionIds: validatedData.permissionIds
    })

    return {
      message: '角色创建成功',
      role: newRole
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如角色代码重复）
    if (error instanceof Error && error.message.includes('角色代码已存在')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '角色代码已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建角色失败')
  }
}, ['ADMIN'])
