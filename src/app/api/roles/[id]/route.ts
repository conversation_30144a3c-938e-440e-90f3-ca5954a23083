/**
 * 系统API接口
 * 
 * 🔐 认证要求：需要有效的访问令牌
 * 🛡️  权限要求：根据具体API而定
 * 📝 审计日志：自动记录所有操作
 * 
 * @description 系统API接口
 * @param request HTTP请求对象，包含查询参数或请求体
 * @returns 标准API响应格式，包含数据和元信息
 * @example
 * // 请求示例
 * GET /api/endpoint?page=1&pageSize=20
 * 
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { ... },
 *   "meta": { "timestamp": "...", "performance": { ... } }
 * }
 */

/**
 * API接口
 * 已通过批量重构工具重构以符合API规范
 * TODO: 需要实现具体的业务逻辑
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 更新角色请求体验证
const updateRoleSchema = z.object({
  roleName: z.string().min(1, '请输入角色名称').max(50, '角色名称最多50位').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  permissionIds: z.array(z.number()).optional()
})

/**
 * GET API
 * @description 获取数据列表
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取角色ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const roleIdStr = pathSegments[pathSegments.length - 1]

    if (!roleIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少角色ID')
    }

    const roleId = parseInt(roleIdStr)

    if (isNaN(roleId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的角色ID')
    }

    /**
     * 获取角色详情 - 连接已有的角色服务层
     *
     * @description 调用getRoleById服务函数获取角色详细信息
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { getRoleById } = await import('@/lib/user-service')

    const role = await getRoleById(roleId)

    if (!role) {
      throw new ApiError(ErrorCode.NOT_FOUND, '角色不存在')
    }

    return {
      role
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取角色详情失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * PUT API - 更新角色
 */
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取角色ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const roleIdStr = pathSegments[pathSegments.length - 1]

    if (!roleIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少角色ID')
    }

    const roleId = parseInt(roleIdStr)

    if (isNaN(roleId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的角色ID')
    }

    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = updateRoleSchema.parse(body)

    /**
     * 更新角色 - 连接已有的角色服务层
     *
     * @description 调用updateRole服务函数更新角色信息
     * @security 已通过权限验证，仅ADMIN可更新角色
     * @audit 自动记录更新操作日志
     */
    const { updateRole, getRoleById } = await import('@/lib/user-service')

    // 检查角色是否存在
    const existingRole = await getRoleById(roleId)
    if (!existingRole) {
      throw new ApiError(ErrorCode.NOT_FOUND, '角色不存在')
    }

    // 更新角色
    const updatedRole = await updateRole(roleId, validatedData)

    return {
      message: '角色更新成功',
      role: updatedRole
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '更新角色失败')
  }
}, ['ADMIN'])

/**
 * DELETE API - 删除角色
 */
export const DELETE = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取角色ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const roleIdStr = pathSegments[pathSegments.length - 1]

    if (!roleIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少角色ID')
    }

    const roleId = parseInt(roleIdStr)

    if (isNaN(roleId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的角色ID')
    }

    /**
     * 删除角色 - 连接已有的角色服务层
     *
     * @description 调用deleteRole服务函数软删除角色
     * @security 已通过权限验证，仅ADMIN可删除角色
     * @audit 自动记录删除操作日志
     */
    const { deleteRole, getRoleById } = await import('@/lib/user-service')

    // 检查角色是否存在
    const existingRole = await getRoleById(roleId)
    if (!existingRole) {
      throw new ApiError(ErrorCode.NOT_FOUND, '角色不存在')
    }

    // 删除角色
    await deleteRole(roleId)

    return {
      message: '角色删除成功'
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    // 处理业务逻辑错误（如角色正在使用中）
    if (error instanceof Error && error.message.includes('正在被用户使用')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '该角色正在被用户使用，无法删除')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '删除角色失败')
  }
}, ['ADMIN'])
