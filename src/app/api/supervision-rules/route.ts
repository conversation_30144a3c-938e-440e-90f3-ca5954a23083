import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth, withAuthAndValidation } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

/**
 * 监管规则管理API - 参数说明
 * 
 * @param request HTTP请求对象
 * @param request.url 请求URL，包含查询参数
 * @param request.body 请求体数据（POST/PUT请求）
 * @param request.headers 请求头信息
 * @param context 上下文对象
 * @param context.user 当前用户信息
 * @param context.validatedData 验证后的请求数据
 * 
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - pageSize: 每页数量 (默认: 20)
 * - search: 搜索关键词 (可选)
 * 
 * 请求体参数 (POST/PUT):
 * - 根据具体API而定，详见schema验证
 */


// 简化的静态验证schema，避免动态验证导致的问题
const querySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  ruleType: z.string().optional(),
  ruleCategory: z.string().optional(),
  severityLevel: z.string().optional(),
  isActive: z.enum(['true', 'false']).optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
  createdFrom: z.enum(['MANUAL', 'TEMPLATE', 'IMPORT', 'API', 'SYSTEM']).optional(),
  endDate: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'ruleName', 'ruleCode', 'priorityLevel', 'executionCount']).default('createdAt'),
  sortOrder: z.enum(['ASC', 'DESC']).default('DESC')
})

// 创建监管规则验证schema（静态）
const createRuleSchema = z.object({
  ruleCode: z.string().min(1, '请输入规则编码'),
  ruleName: z.string().min(1, '请输入规则名称'),
  ruleType: z.string().min(1, '请选择规则类型'),
  ruleCategory: z.string().min(1, '请选择规则分类'),
  description: z.string().optional(),
  ruleContent: z.string().min(1, '请输入规则内容'),
  ruleDsl: z.string().optional(),
  priorityLevel: z.number().min(1).max(10).default(5),
  severityLevel: z.string().default('MEDIUM'),
  effectiveDate: z.string(),
  expiryDate: z.string().optional(),
  versionNumber: z.string().default('1.0'),
  parentRuleId: z.number().optional(),
  ruleSource: z.string().optional(),
  createdFrom: z.enum(['MANUAL', 'TEMPLATE', 'IMPORT', 'API', 'SYSTEM']).default('MANUAL')
})

/**
 * GET API - 获取supervision-rules数据
 */

/**
 * 监管规则管理API - 响应示例
 * 
 * @example 成功响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": {
 *     // 具体数据结构根据API而定
 *   },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "performance": {
 *       "executionTime": 150,
 *       "requestId": "req_123456"
 *     }
 *   }
 * }
 * 
 * @example 错误响应示例
 * {
 *   "success": false,
 *   "code": "VALIDATION_ERROR",
 *   "message": "请求参数验证失败",
 *   "details": [
 *     {
 *       "path": ["field"],
 *       "message": "字段验证失败"
 *     }
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * 
 * @example 分页响应示例 (适用于列表API)
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "获取数据成功",
 *   "data": [
 *     // 数据项数组
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "pagination": {
 *       "page": 1,
 *       "pageSize": 20,
 *       "total": 100,
 *       "totalPages": 5,
 *       "hasNext": true,
 *       "hasPrev": false
 *     }
 *   }
 * }
 */

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数（使用静态验证）
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = querySchema.parse(queryParams)

    /**
     * 临时使用模拟数据，避免数据库查询问题
     */
    console.log('🔍 API调用参数:', validatedParams)

    // 返回模拟数据进行测试
    const result = {
      items: [
        {
          id: 1,
          ruleCode: 'RULE_001',
          ruleName: '异地住院费用控制规则',
          ruleType: 'SQL',
          ruleCategory: '费用控制',
          description: '检测异地住院费用是否超过合理范围，防止过度医疗',
          priorityLevel: 8,
          severityLevel: 'HIGH',
          isActive: true,
          executionCount: 156,
          lastExecutedAt: '2024-01-15T10:30:00Z',
          createdAt: '2024-01-01T00:00:00Z'
        }
      ],
      pagination: {
        page: validatedParams['page'],
        pageSize: validatedParams['pageSize'],
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }

    return {
      rules: result.items,
      pagination: result.pagination
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取监管规则列表失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * POST API - 创建监管规则
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体（使用静态验证）
    const body = await request.json()
    const validatedData = createRuleSchema.parse(body)

    /**
     * 创建监管规则 - 连接已有的监管规则服务层
     *
     * @description 调用createSupervisionRule服务函数创建新监管规则
     * @security 已通过权限验证，仅ADMIN可创建规则
     * @audit 自动记录创建操作日志
     */
    const { createSupervisionRule, getSupervisionRuleById } = await import('@/lib/supervision-rule-service')

    // 创建监管规则（类型断言以解决动态schema的类型问题）
    const ruleId = await createSupervisionRule(validatedData as any, 1) // TODO: 从认证上下文获取用户ID

    // 获取创建的规则信息
    const newRule = await getSupervisionRuleById(ruleId)

    return {
      message: '监管规则创建成功',
      rule: newRule
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如规则编码重复）
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '规则编码已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建监管规则失败')
  }
}, ['ADMIN'])
