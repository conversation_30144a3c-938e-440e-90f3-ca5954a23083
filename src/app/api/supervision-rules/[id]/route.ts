/**
 * 系统API接口
 * 
 * 🔐 认证要求：需要有效的访问令牌
 * 🛡️  权限要求：根据具体API而定
 * 📝 审计日志：自动记录所有操作
 * 
 * @description 系统API接口
 * @param request HTTP请求对象，包含查询参数或请求体
 * @returns 标准API响应格式，包含数据和元信息
 * @example
 * // 请求示例
 * GET /api/endpoint?page=1&pageSize=20
 * 
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { ... },
 *   "meta": { "timestamp": "...", "performance": { ... } }
 * }
 */

/**
 * 监管规则API
 * 已通过批量重构工具重构以符合API规范
 * TODO: 需要实现具体的业务逻辑
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 更新监管规则请求体验证
const updateRuleSchema = z.object({
  ruleName: z.string().min(1, '请输入规则名称').optional(),
  ruleCategory: z.enum(['COST_CONTROL', 'FRAUD_DETECTION', 'COMPLIANCE_CHECK', 'QUALITY_ASSURANCE', 'STATISTICAL_ANALYSIS']).optional(),
  description: z.string().optional(),
  ruleContent: z.string().min(1, '请输入规则内容').optional(),
  ruleSql: z.string().optional(),
  ruleDsl: z.string().optional(),
  priorityLevel: z.number().min(1).max(10).optional(),
  severityLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  isActive: z.boolean().optional(),
  effectiveDate: z.string().optional(),
  expiryDate: z.string().optional(),
  versionNumber: z.string().optional(),
  ruleSource: z.string().optional()
})

/**
 * GET API
 * @description 获取数据列表
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取规则ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const ruleIdStr = pathSegments[pathSegments.length - 1]

    if (!ruleIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少规则ID')
    }

    const ruleId = parseInt(ruleIdStr)

    if (isNaN(ruleId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的规则ID')
    }

    /**
     * 获取监管规则详情 - 连接已有的监管规则服务层
     *
     * @description 调用getSupervisionRuleById服务函数获取规则详细信息
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { getSupervisionRuleById } = await import('@/lib/supervision-rule-service')

    const rule = await getSupervisionRuleById(ruleId)

    if (!rule) {
      throw new ApiError(ErrorCode.NOT_FOUND, '监管规则不存在')
    }

    return {
      rule
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取监管规则详情失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * PUT API - 更新监管规则
 */
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取规则ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const ruleIdStr = pathSegments[pathSegments.length - 1]

    if (!ruleIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少规则ID')
    }

    const ruleId = parseInt(ruleIdStr)

    if (isNaN(ruleId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的规则ID')
    }

    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = updateRuleSchema.parse(body)

    /**
     * 更新监管规则 - 连接已有的监管规则服务层
     *
     * @description 调用updateSupervisionRule服务函数更新规则信息
     * @security 已通过权限验证，仅ADMIN可更新规则
     * @audit 自动记录更新操作日志
     */
    const { updateSupervisionRule, getSupervisionRuleById } = await import('@/lib/supervision-rule-service')

    // 检查规则是否存在
    const existingRule = await getSupervisionRuleById(ruleId)
    if (!existingRule) {
      throw new ApiError(ErrorCode.NOT_FOUND, '监管规则不存在')
    }

    // 更新规则
    await updateSupervisionRule(ruleId, validatedData, 1) // TODO: 从认证上下文获取用户ID

    // 获取更新后的规则信息
    const updatedRule = await getSupervisionRuleById(ruleId)

    return {
      message: '监管规则更新成功',
      rule: updatedRule
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '更新监管规则失败')
  }
}, ['ADMIN'])

/**
 * DELETE API - 删除监管规则
 */
export const DELETE = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取规则ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const ruleIdStr = pathSegments[pathSegments.length - 1]

    if (!ruleIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少规则ID')
    }

    const ruleId = parseInt(ruleIdStr)

    if (isNaN(ruleId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的规则ID')
    }

    /**
     * 删除监管规则 - 连接已有的监管规则服务层
     *
     * @description 调用deleteSupervisionRule服务函数软删除规则
     * @security 已通过权限验证，仅ADMIN可删除规则
     * @audit 自动记录删除操作日志
     */
    const { deleteSupervisionRule, getSupervisionRuleById } = await import('@/lib/supervision-rule-service')

    // 检查规则是否存在
    const existingRule = await getSupervisionRuleById(ruleId)
    if (!existingRule) {
      throw new ApiError(ErrorCode.NOT_FOUND, '监管规则不存在')
    }

    // 软删除规则
    await deleteSupervisionRule(ruleId, 1) // TODO: 从认证上下文获取用户ID

    return {
      message: '监管规则删除成功'
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '删除监管规则失败')
  }
}, ['ADMIN'])
