/**
 * 系统API接口
 * 
 * 🔐 认证要求：需要有效的访问令牌
 * 🛡️  权限要求：根据具体API而定
 * 📝 审计日志：自动记录所有操作
 * 
 * @description 系统API接口
 * @param request HTTP请求对象，包含查询参数或请求体
 * @returns 标准API响应格式，包含数据和元信息
 * @example
 * // 请求示例
 * GET /api/endpoint?page=1&pageSize=20
 * 
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { ... },
 *   "meta": { "timestamp": "...", "performance": { ... } }
 * }
 */

/**
 * 用户管理API
 * 已通过批量重构工具重构以符合API规范
 * TODO: 需要实现具体的业务逻辑
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 更新用户请求体验证
const updateUserSchema = z.object({
  realName: z.string().min(1, '请输入真实姓名').optional(),
  email: z.string().email('请输入有效的邮箱地址').optional(),
  department: z.string().optional(),
  position: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'LOCKED']).optional(),
  roleIds: z.array(z.number()).optional()
})

/**
 * GET API
 * @description 获取数据列表
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取用户ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const userIdStr = pathSegments[pathSegments.length - 1]

    if (!userIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少用户ID')
    }

    const userId = parseInt(userIdStr)

    if (isNaN(userId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的用户ID')
    }

    /**
     * 获取用户详情 - 连接已有的用户服务层
     *
     * @description 调用findUserById服务函数获取用户详细信息
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { findUserById } = await import('@/lib/user-service')

    const user = await findUserById(userId)

    if (!user) {
      throw new ApiError(ErrorCode.NOT_FOUND, '用户不存在')
    }

    return {
      user
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取用户详情失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * PUT API - 更新用户
 */
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取用户ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const userIdStr = pathSegments[pathSegments.length - 1]

    if (!userIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少用户ID')
    }

    const userId = parseInt(userIdStr)

    if (isNaN(userId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的用户ID')
    }

    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)

    /**
     * 更新用户 - 连接已有的用户服务层
     *
     * @description 调用updateUser和assignUserRoles服务函数更新用户信息
     * @security 已通过权限验证，仅ADMIN可更新用户
     * @audit 自动记录更新操作日志
     */
    const { updateUser, assignUserRoles, findUserById } = await import('@/lib/user-service')

    // 检查用户是否存在
    const existingUser = await findUserById(userId)
    if (!existingUser) {
      throw new ApiError(ErrorCode.NOT_FOUND, '用户不存在')
    }

    // 更新用户基本信息
    await updateUser(userId, {
      realName: validatedData.realName,
      email: validatedData.email,
      department: validatedData.department,
      position: validatedData.position,
      status: validatedData.status
    })

    // 更新用户角色（如果提供了角色ID）
    if (validatedData.roleIds !== undefined) {
      await assignUserRoles(userId, validatedData.roleIds)
    }

    // 获取更新后的用户信息
    const updatedUser = await findUserById(userId)

    return {
      message: '用户更新成功',
      user: updatedUser
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '更新用户失败')
  }
}, ['ADMIN'])

/**
 * DELETE API - 删除用户
 */
export const DELETE = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取用户ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const userIdStr = pathSegments[pathSegments.length - 1]

    if (!userIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少用户ID')
    }

    const userId = parseInt(userIdStr)

    if (isNaN(userId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的用户ID')
    }

    /**
     * 删除用户 - 连接已有的用户服务层
     *
     * @description 调用deleteUser服务函数软删除用户
     * @security 已通过权限验证，仅ADMIN可删除用户
     * @audit 自动记录删除操作日志
     */
    const { deleteUser, findUserById } = await import('@/lib/user-service')

    // 检查用户是否存在
    const existingUser = await findUserById(userId)
    if (!existingUser) {
      throw new ApiError(ErrorCode.NOT_FOUND, '用户不存在')
    }

    // 软删除用户
    await deleteUser(userId)

    return {
      message: '用户删除成功'
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '删除用户失败')
  }
}, ['ADMIN'])
