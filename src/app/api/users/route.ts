import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth, withAuthAndValidation } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

/**
 * 用户管理API - 参数说明
 * 
 * @param request HTTP请求对象
 * @param request.url 请求URL，包含查询参数
 * @param request.body 请求体数据（POST/PUT请求）
 * @param request.headers 请求头信息
 * @param context 上下文对象
 * @param context.user 当前用户信息
 * @param context.validatedData 验证后的请求数据
 * 
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - pageSize: 每页数量 (默认: 20)
 * - search: 搜索关键词 (可选)
 * 
 * 请求体参数 (POST/PUT):
 * - 根据具体API而定，详见schema验证
 */


// 查询参数验证
const querySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  department: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'LOCKED']).optional(),
  sortBy: z.enum(['CREATED_AT', 'USERNAME', 'REAL_NAME', 'LAST_LOGIN_AT']).default('CREATED_AT'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

// 创建用户请求体验证
const createUserSchema = z.object({
  username: z.string().min(3, '用户名至少3位').max(20, '用户名最多20位'),
  password: z.string().min(8, '密码至少8位'),
  realName: z.string().min(1, '请输入真实姓名'),
  email: z.string().email('请输入有效的邮箱地址'),
  department: z.string().optional(),
  position: z.string().optional(),
  roleIds: z.array(z.number()).optional()
})

/**
 * GET API - 获取users数据
 */

/**
 * 用户管理API - 响应示例
 * 
 * @example 成功响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": {
 *     // 具体数据结构根据API而定
 *   },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "performance": {
 *       "executionTime": 150,
 *       "requestId": "req_123456"
 *     }
 *   }
 * }
 * 
 * @example 错误响应示例
 * {
 *   "success": false,
 *   "code": "VALIDATION_ERROR",
 *   "message": "请求参数验证失败",
 *   "details": [
 *     {
 *       "path": ["field"],
 *       "message": "字段验证失败"
 *     }
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * 
 * @example 分页响应示例 (适用于列表API)
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "获取数据成功",
 *   "data": [
 *     // 数据项数组
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "pagination": {
 *       "page": 1,
 *       "pageSize": 20,
 *       "total": 100,
 *       "totalPages": 5,
 *       "hasNext": true,
 *       "hasPrev": false
 *     }
 *   }
 * }
 */

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const validatedParams = querySchema.parse(queryParams)

    /**
     * 获取用户列表 - 连接已有的用户服务层
     *
     * @description 调用getUserList服务函数获取分页用户数据
     * @performance 支持分页查询，避免大数据集性能问题
     * @security 已通过权限验证，仅ADMIN和OPERATOR可访问
     */
    const { getUserList } = await import('@/lib/user-service')

    const result = await getUserList({
      page: validatedParams.page,
      pageSize: validatedParams.pageSize,
      search: validatedParams.search,
      department: validatedParams.department,
      status: validatedParams.status,
      sortBy: validatedParams.sortBy,
      sortOrder: validatedParams.sortOrder
    })

    return {
      users: result.users,
      pagination: result.pagination
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取用户列表失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * POST API - 创建用户
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    /**
     * 创建用户 - 连接已有的用户服务层
     *
     * @description 调用createUser服务函数创建新用户
     * @security 密码自动哈希处理，用户名唯一性验证
     * @audit 自动记录创建操作日志
     */
    const { createUser } = await import('@/lib/user-service')

    // 创建用户 - createUser函数返回完整的User对象，包含角色分配
    const newUser = await createUser({
      username: validatedData.username,
      password: validatedData.password,
      confirmPassword: validatedData.password, // API层不需要确认密码，直接使用相同值
      realName: validatedData.realName,
      email: validatedData.email,
      department: validatedData.department,
      position: validatedData.position,
      roleIds: validatedData.roleIds
    })

    return {
      message: '用户创建成功',
      user: newUser
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如用户名重复）
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '用户名已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建用户失败')
  }
}, ['ADMIN'])
