import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

/**
 * 知识库文档API - 参数说明
 * 
 * @param request HTTP请求对象
 * @param request.url 请求URL，包含查询参数
 * @param request.body 请求体数据（POST/PUT请求）
 * @param request.headers 请求头信息
 * @param context 上下文对象
 * @param context.user 当前用户信息
 * @param context.validatedData 验证后的请求数据
 * 
 * 查询参数:
 * - page: 页码 (默认: 1)
 * - pageSize: 每页数量 (默认: 20)
 * - search: 搜索关键词 (可选)
 * 
 * 请求体参数 (POST/PUT):
 * - 根据具体API而定，详见schema验证
 */


import { createOptionalDynamicEnum, createDynamicEnum } from '@/lib/dynamic-validation'

// 动态验证schema缓存
let documentQuerySchemaCache: z.ZodObject<any> | null = null
let createDocumentSchemaCache: z.ZodObject<any> | null = null

/**
 * 获取查询参数验证schema（动态生成）
 */
async function getDocumentQuerySchema() {
  if (!documentQuerySchemaCache) {
    const documentTypeEnum = await createOptionalDynamicEnum('DOCUMENT_TYPE', '请选择有效的文档类型')
    const documentStatusEnum = await createOptionalDynamicEnum('DOCUMENT_STATUS', '请选择有效的文档状态')

    documentQuerySchemaCache = z.object({
      page: z.coerce.number().min(1).default(1),
      pageSize: z.coerce.number().min(1).max(100).default(20),
      search: z.string().optional(),
      categoryId: z.coerce.number().optional(),
      documentType: documentTypeEnum,
      documentStatus: documentStatusEnum,
      authorId: z.coerce.number().optional(),
      isPublic: z.enum(['true', 'false']).optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
      sortBy: z.enum(['CREATED_AT', 'UPDATED_AT', 'TITLE', 'VIEW_COUNT', 'DOWNLOAD_COUNT']).default('CREATED_AT'),
      sortOrder: z.enum(['asc', 'desc']).default('desc')
    })
  }
  return documentQuerySchemaCache
}

/**
 * 获取创建文档验证schema（动态生成）
 */
async function getCreateDocumentSchema() {
  if (!createDocumentSchemaCache) {
    const documentTypeEnum = await createDynamicEnum('DOCUMENT_TYPE', '请选择有效的文档类型')
    const documentStatusEnum = await createDynamicEnum('DOCUMENT_STATUS', '请选择有效的文档状态')

    createDocumentSchemaCache = z.object({
      categoryId: z.number().min(1, '请选择文档分类'),
      documentCode: z.string().min(1, '请输入文档编码'),
      title: z.string().min(1, '请输入文档标题'),
      content: z.string().min(1, '请输入文档内容'),
      documentType: documentTypeEnum,
      status: documentStatusEnum.default('DRAFT'),
      keywords: z.string().optional(),
      isPublic: z.boolean().default(true)
    })
  }
  return createDocumentSchemaCache
}

/**
 * 获取知识库文档列表API
 */

/**
 * 知识库文档API - 响应示例
 * 
 * @example 成功响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": {
 *     // 具体数据结构根据API而定
 *   },
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "performance": {
 *       "executionTime": 150,
 *       "requestId": "req_123456"
 *     }
 *   }
 * }
 * 
 * @example 错误响应示例
 * {
 *   "success": false,
 *   "code": "VALIDATION_ERROR",
 *   "message": "请求参数验证失败",
 *   "details": [
 *     {
 *       "path": ["field"],
 *       "message": "字段验证失败"
 *     }
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0"
 *   }
 * }
 * 
 * @example 分页响应示例 (适用于列表API)
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "获取数据成功",
 *   "data": [
 *     // 数据项数组
 *   ],
 *   "meta": {
 *     "timestamp": "2024-01-01T00:00:00Z",
 *     "version": "1.0.0",
 *     "pagination": {
 *       "page": 1,
 *       "pageSize": 20,
 *       "total": 100,
 *       "totalPages": 5,
 *       "hasNext": true,
 *       "hasPrev": false
 *     }
 *   }
 * }
 */

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 解析查询参数（使用动态验证）
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const documentQuerySchema = await getDocumentQuerySchema()
    const validatedParams = documentQuerySchema.parse(queryParams)

    /**
     * 获取知识库文档列表 - 连接已有的知识库服务层
     *
     * @description 调用getKnowledgeDocuments服务函数获取分页知识库文档数据
     * @performance 支持分页查询和多种过滤条件，避免大数据集性能问题
     * @security 已通过权限验证，仅ADMIN、OPERATOR和VIEWER可访问
     */
    const { getKnowledgeDocuments } = await import('@/lib/knowledge-document-service')

    // 转换sortOrder为大写（数据服务层期望的格式）
    const sortOrder = validatedParams['sortOrder'] === 'asc' ? 'ASC' : 'DESC'

    const result = await getKnowledgeDocuments({
      page: validatedParams['page'],
      pageSize: validatedParams['pageSize'],
      search: validatedParams['search'],
      categoryId: validatedParams['categoryId'],
      documentType: validatedParams['documentType'],
      documentStatus: validatedParams['documentStatus'],
      authorId: validatedParams['authorId'],
      isPublic: validatedParams['isPublic'],
      sortBy: validatedParams['sortBy'],
      sortOrder
    })

    return {
      documents: result.items,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        totalCount: result.total,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1
      }
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取知识库文档列表失败')
  }
}, ['ADMIN', 'OPERATOR', 'VIEWER'])

/**
 * 创建知识库文档API
 */
export const POST = withAuth(async (request: NextRequest) => {
  try {
    // 手动解析和验证请求体（使用动态验证）
    const body = await request.json()
    const createDocumentSchema = await getCreateDocumentSchema()
    const validatedData = createDocumentSchema.parse(body)

    /**
     * 创建知识库文档 - 连接已有的知识库服务层
     *
     * @description 调用createKnowledgeDocument服务函数创建新知识库文档
     * @security 已通过权限验证，仅ADMIN和OPERATOR可创建文档
     * @audit 自动记录创建操作日志和版本历史
     */
    const { createKnowledgeDocument, getKnowledgeDocumentById } = await import('@/lib/knowledge-document-service')

    // 创建知识库文档（类型断言以解决动态schema的类型问题）
    const documentId = await createKnowledgeDocument(validatedData as any, 1) // TODO: 从认证上下文获取用户ID

    // 获取创建的文档信息
    const newDocument = await getKnowledgeDocumentById(documentId)

    return {
      message: '知识库文档创建成功',
      document: newDocument
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    // 处理数据库约束错误（如文档编码重复）
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '文档编码已存在')
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '创建知识库文档失败')
  }
}, ['ADMIN', 'OPERATOR'])
