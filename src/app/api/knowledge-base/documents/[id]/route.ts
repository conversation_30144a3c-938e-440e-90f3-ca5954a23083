/**
 * 系统API接口
 * 
 * 🔐 认证要求：需要有效的访问令牌
 * 🛡️  权限要求：根据具体API而定
 * 📝 审计日志：自动记录所有操作
 * 
 * @description 系统API接口
 * @param request HTTP请求对象，包含查询参数或请求体
 * @returns 标准API响应格式，包含数据和元信息
 * @example
 * // 请求示例
 * GET /api/endpoint?page=1&pageSize=20
 * 
 * // 响应示例
 * {
 *   "success": true,
 *   "code": "SUCCESS",
 *   "message": "操作成功",
 *   "data": { ... },
 *   "meta": { "timestamp": "...", "performance": { ... } }
 * }
 */

/**
 * 知识库API
 * 已通过批量重构工具重构以符合API规范
 * TODO: 需要实现具体的业务逻辑
 */

import { NextRequest } from 'next/server'
import { z } from 'zod'
import { withAuth } from '@/lib/api/handler'
import { ApiError, ErrorCode } from '@/lib/api/response'

// 更新知识库文档请求体验证
const updateDocumentSchema = z.object({
  categoryId: z.number().min(1, '请选择文档分类').optional(),
  title: z.string().min(1, '请输入文档标题').optional(),
  content: z.string().min(1, '请输入文档内容').optional(),
  documentType: z.enum(['POLICY', 'REGULATION', 'GUIDELINE', 'FAQ', 'MANUAL', 'REPORT', 'OTHER']).optional(),
  status: z.enum(['DRAFT', 'REVIEWING', 'PUBLISHED', 'ARCHIVED']).optional(),
  keywords: z.string().optional(),
  isPublic: z.boolean().optional(),
  changeDescription: z.string().optional()
})

/**
 * GET API
 * @description 获取数据列表
 */
// 响应格式说明：
// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：
// {
//   "success": true,
//   "code": "SUCCESS",
//   "message": "操作成功",
//   "data": <返回的数据>,
//   "meta": {
//     "timestamp": "2024-01-01T00:00:00Z",
//     "version": "1.0.0",
//     "performance": { "executionTime": 100, "requestId": "req_xxx" }
//   }
// }

export const GET = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取文档ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const documentIdStr = pathSegments[pathSegments.length - 1]

    if (!documentIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少文档ID')
    }

    const documentId = parseInt(documentIdStr)

    if (isNaN(documentId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的文档ID')
    }

    /**
     * 获取知识库文档详情 - 连接已有的知识库服务层
     *
     * @description 调用getKnowledgeDocumentById服务函数获取文档详细信息
     * @security 已通过权限验证，仅ADMIN、OPERATOR和VIEWER可访问
     * @audit 自动记录访问日志
     */
    const { getKnowledgeDocumentById } = await import('@/lib/knowledge-document-service')
    const { logKnowledgeAccess } = await import('@/lib/knowledge-version-service')

    const document = await getKnowledgeDocumentById(documentId)

    if (!document) {
      throw new ApiError(ErrorCode.NOT_FOUND, '知识库文档不存在')
    }

    // 记录访问日志
    try {
      await logKnowledgeAccess({
        documentId,
        userId: 1, // TODO: 从认证上下文获取用户ID
        accessType: 'VIEW',
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1'
      })
    } catch (logError) {
      // 访问日志失败不应该影响主要功能
      console.warn('记录访问日志失败:', logError)
    }

    return {
      document
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '获取知识库文档详情失败')
  }
}, ['ADMIN', 'OPERATOR', 'VIEWER'])

/**
 * PUT API - 更新知识库文档
 */
export const PUT = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取文档ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const documentIdStr = pathSegments[pathSegments.length - 1]

    if (!documentIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少文档ID')
    }

    const documentId = parseInt(documentIdStr)

    if (isNaN(documentId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的文档ID')
    }

    // 手动解析和验证请求体
    const body = await request.json()
    const validatedData = updateDocumentSchema.parse(body)

    /**
     * 更新知识库文档 - 连接已有的知识库服务层
     *
     * @description 调用updateKnowledgeDocument服务函数更新文档信息
     * @security 已通过权限验证，仅ADMIN和OPERATOR可更新文档
     * @audit 自动记录更新操作日志和版本历史
     */
    const { updateKnowledgeDocument, getKnowledgeDocumentById } = await import('@/lib/knowledge-document-service')

    // 检查文档是否存在
    const existingDocument = await getKnowledgeDocumentById(documentId)
    if (!existingDocument) {
      throw new ApiError(ErrorCode.NOT_FOUND, '知识库文档不存在')
    }

    // 更新文档
    await updateKnowledgeDocument(documentId, validatedData, 1) // TODO: 从认证上下文获取用户ID

    // 获取更新后的文档信息
    const updatedDocument = await getKnowledgeDocumentById(documentId)

    return {
      message: '知识库文档更新成功',
      document: updatedDocument
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors)
    }
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '更新知识库文档失败')
  }
}, ['ADMIN', 'OPERATOR'])

/**
 * DELETE API - 删除知识库文档
 */
export const DELETE = withAuth(async (request: NextRequest) => {
  try {
    // 从URL路径中提取文档ID
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const documentIdStr = pathSegments[pathSegments.length - 1]

    if (!documentIdStr) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '缺少文档ID')
    }

    const documentId = parseInt(documentIdStr)

    if (isNaN(documentId)) {
      throw new ApiError(ErrorCode.VALIDATION_ERROR, '无效的文档ID')
    }

    /**
     * 删除知识库文档 - 连接已有的知识库服务层
     *
     * @description 调用deleteKnowledgeDocument服务函数软删除文档
     * @security 已通过权限验证，仅ADMIN可删除文档
     * @audit 自动记录删除操作日志
     */
    const { deleteKnowledgeDocument, getKnowledgeDocumentById } = await import('@/lib/knowledge-document-service')

    // 检查文档是否存在
    const existingDocument = await getKnowledgeDocumentById(documentId)
    if (!existingDocument) {
      throw new ApiError(ErrorCode.NOT_FOUND, '知识库文档不存在')
    }

    // 软删除文档
    await deleteKnowledgeDocument(documentId, 1) // TODO: 从认证上下文获取用户ID

    return {
      message: '知识库文档删除成功'
    }

  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError(ErrorCode.UNKNOWN_ERROR, '删除知识库文档失败')
  }
}, ['ADMIN'])
