import type { Metadata } from "next"
import { Inter, Noto_Sans_SC } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { Toaster } from "@/components/ui/sonner"
import { WebVitalsTracker } from "@/components/performance/web-vitals-tracker"
import { LayoutProvider } from "@/components/layout/layout-provider"
import "@/styles/globals.css"

// 优化字体加载 - 支持中英文
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
})

const notoSansSC = Noto_Sans_SC({
  subsets: ["latin"],
  variable: "--font-noto-sans-sc",
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
})

export const metadata: Metadata = {
  title: {
    default: "医保基金监管平台",
    template: "%s | 医保基金监管平台"
  },
  description: "基于Next.js的现代化医疗保险基金监督管理系统，提供智能化的医保基金监管解决方案",
  keywords: ["医保", "基金监管", "医疗保险", "监督管理", "智能监管", "数据分析"],
  authors: [{ name: "医保基金监管平台开发团队" }],
  creator: "医保基金监管平台开发团队",
  publisher: "医保基金监管平台",
  robots: {
    index: false,
    follow: false,
  },
}

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html
      lang="zh-CN"
      suppressHydrationWarning
      className={`${inter.variable} ${notoSansSC.variable}`}
    >
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <meta name="theme-color" content="hsl(var(--primary))" />
        <meta name="color-scheme" content="light dark" />
      </head>
      <body className={`${inter.className} antialiased min-h-screen bg-background font-sans`}>
        <ThemeProvider
          defaultTheme="default"
          defaultMode="light"
          storageKey="mediinspect-theme"
        >
          <AuthProvider>
            <LayoutProvider>
              {children}
            </LayoutProvider>
            <Toaster
              position="top-right"
              expand={false}
              richColors
              closeButton
            />
            <WebVitalsTracker />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
