"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Eye, EyeOff, Loader2, Shield } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/auth-context'
import { businessToast } from '@/lib/toast'
import { LoginRequest } from '@/types/auth'



// 表单验证schema
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名'),
  password: z.string().min(1, '请输入密码'),
  rememberMe: z.boolean().default(false),
})

type LoginFormData = z.infer<typeof loginSchema>



export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { login } = useAuth()

  const searchParams = useSearchParams()
  const message = searchParams.get('message')

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  })

  // 加载记住我数据
  useEffect(() => {
    // 确保在客户端环境中执行
    if (typeof window === 'undefined') return

    try {
      const remembered = localStorage.getItem('rememberMe') === 'true'
      const savedUsername = localStorage.getItem('savedUsername') || ''

      // 始终根据localStorage的状态来设置表单
      form.reset({
        username: remembered ? savedUsername : '',
        password: '',
        rememberMe: remembered
      })
    } catch (error) {
      console.error('加载记住我数据失败:', error)
      // 出错时确保表单状态为默认值
      form.reset({
        username: '',
        password: '',
        rememberMe: false
      })
    }
  }, [form])



  // 处理记住我状态变化
  const handleRememberMeChange = (checked: boolean) => {
    // 确保在客户端环境中执行
    if (typeof window === 'undefined') return

    // 立即更新localStorage
    if (checked) {
      localStorage.setItem('rememberMe', 'true')
      // 如果当前有用户名，也保存
      const currentUsername = form.getValues('username')
      if (currentUsername) {
        localStorage.setItem('savedUsername', currentUsername)
      }
    } else {
      localStorage.removeItem('rememberMe')
      localStorage.removeItem('savedUsername')
    }

    // 更新表单状态
    form.setValue('rememberMe', checked)
  }

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true)

      // 确保记住我功能的最新状态（仅在客户端执行）
      if (typeof window !== 'undefined') {
        if (data.rememberMe) {
          localStorage.setItem('rememberMe', 'true')
          localStorage.setItem('savedUsername', data.username)
        } else {
          localStorage.removeItem('rememberMe')
          localStorage.removeItem('savedUsername')
        }
      }

      // 将记住我状态传递给登录请求
      const loginRequest: LoginRequest = {
        ...data,
        rememberMe: data.rememberMe
      }

      const result = await login(loginRequest)

      if (!result.success) {
        businessToast.loginError(result.message)
      } else {
        businessToast.loginSuccess()
        // 让认证上下文处理重定向，不要手动跳转
        // 认证上下文会在设置完认证状态后自动重定向
      }
    } catch (error) {
      businessToast.networkError()
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      {/* 左侧品牌区域 - 单张高质量背景图片 */}
      <div className="bg-muted relative hidden lg:block">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1576091160550-2173dba999ef?fm=jpg&q=90&w=2000&ixlib=rb-4.0.3')`,
            filter: 'grayscale(100%) contrast(1.1) brightness(0.7)',
          }}
        >
          {/* 优化的黑色滤镜遮罩 */}
          <div className="absolute inset-0 bg-black/50" />
        </div>
      </div>

      {/* 右侧登录表单区域 - 参考shadcn/UI官方设计 */}
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <Link href="#" className="flex items-center gap-2 font-medium">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <Shield className="size-4" />
            </div>
            医保基金监管平台
          </Link>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            {/* 登录表单标题 */}
            <div className="flex flex-col space-y-2 text-center mb-6">
              <h1 className="text-2xl font-semibold tracking-tight">
                登录账户
              </h1>
              <p className="text-sm text-muted-foreground">
                请输入您的凭据以访问系统
              </p>
            </div>

            {/* 消息提示 */}
            {message && (
              <Alert className="mb-4">
                <AlertDescription>{message}</AlertDescription>
              </Alert>
            )}

            {/* 登录表单 */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {/* 用户名输入 */}
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用户名</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请输入用户名"
                          type="text"
                          autoCapitalize="none"
                          autoComplete="username"
                          autoCorrect="off"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 密码输入 */}
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>密码</FormLabel>
                        <Link
                          href="/auth/forgot-password"
                          className="text-sm text-primary hover:text-primary/80"
                        >
                          忘记密码？
                        </Link>
                      </div>
                      <FormControl>
                        <div className="relative">
                          <Input
                            placeholder="请输入密码"
                            type={showPassword ? "text" : "password"}
                            autoComplete="current-password"
                            disabled={isLoading}
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                            <span className="sr-only">
                              {showPassword ? "隐藏密码" : "显示密码"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 记住我选项 */}
                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            handleRememberMeChange(checked as boolean)
                          }}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        记住我的登录状态
                      </FormLabel>
                    </FormItem>
                  )}
                />

                {/* 登录按钮 */}
                <Button disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      登录中...
                    </>
                  ) : (
                    "登录"
                  )}
                </Button>
              </form>
            </Form>

            {/* 底部链接 */}
            <div className="mt-4 text-center text-sm">
              还没有账户？{" "}
              <Link href="/auth/register" className="text-primary hover:text-primary/80">
                立即注册
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


