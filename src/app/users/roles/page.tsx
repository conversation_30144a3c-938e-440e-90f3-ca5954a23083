"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { RoleForm } from '@/components/roles/role-form'
import { useToast } from '@/lib/toast'
import { Role } from '@/types/auth'
import { 
  Shield, 
  Plus, 
  Edit,
  Trash2,
  Users,
  Key,
  Loader2
} from 'lucide-react'

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRoleFormOpen, setIsRoleFormOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const { toast } = useToast()

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const response = await fetch('/api/roles', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setRoles(result.data)
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取角色列表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 删除角色
  const handleDeleteRole = async (roleId: number) => {
    if (!confirm('确定要删除这个角色吗？删除后无法恢复。')) {
      return
    }

    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "成功",
          description: "角色删除成功",
        })
        fetchRoles() // 重新加载角色列表
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "删除角色失败",
      })
    }
  }

  // 编辑角色
  const handleEditRole = (role: Role) => {
    setEditingRole(role)
    setIsRoleFormOpen(true)
  }

  // 新增角色
  const handleAddRole = () => {
    setEditingRole(null)
    setIsRoleFormOpen(true)
  }

  // 表单成功回调
  const handleFormSuccess = () => {
    fetchRoles()
  }

  // 获取角色状态颜色
  const getRoleStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
  }

  // 获取角色代码颜色
  const getRoleCodeColor = (roleCode: string) => {
    const colors: Record<string, string> = {
      'ADMIN': 'bg-red-100 text-red-800',
      'SUPERVISOR': 'bg-blue-100 text-blue-800',
      'OPERATOR': 'bg-green-100 text-green-800',
      'AUDITOR': 'bg-yellow-100 text-yellow-800',
      'VIEWER': 'bg-gray-100 text-gray-800',
    }
    return colors[roleCode] || 'bg-gray-100 text-gray-800'
  }

  // 初始加载
  useEffect(() => {
    fetchRoles()
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">角色管理</h1>
              <p className="text-muted-foreground">管理系统角色和权限配置</p>
            </div>
            <Button onClick={handleAddRole}>
              <Plus className="h-4 w-4 mr-2" />
              新增角色
            </Button>
          </div>

          {/* 角色统计 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总角色数</p>
                    <p className="text-2xl font-bold">{roles.length}</p>
                  </div>
                  <Shield className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">活跃角色</p>
                    <p className="text-2xl font-bold text-green-600">
                      {roles.filter(r => r.isActive).length}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">禁用角色</p>
                    <p className="text-2xl font-bold text-red-600">
                      {roles.filter(r => !r.isActive).length}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">权限总数</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {roles.reduce((total, role) => total + role.permissions.length, 0)}
                    </p>
                  </div>
                  <Key className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 角色列表 */}
          <Card>
            <CardHeader>
              <CardTitle>角色列表</CardTitle>
              <CardDescription>
                系统中所有角色的详细信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>角色信息</TableHead>
                        <TableHead>角色代码</TableHead>
                        <TableHead>权限数量</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>描述</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {roles.map((role) => (
                        <TableRow key={role.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{role.roleName}</p>
                              <p className="text-sm text-muted-foreground">ID: {role.id}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getRoleCodeColor(role.roleCode)}>
                              {role.roleCode}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Key className="h-4 w-4 text-muted-foreground" />
                              <span>{role.permissions.length}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getRoleStatusColor(role.isActive)}>
                              {role.isActive ? '活跃' : '禁用'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <p className="text-sm max-w-xs truncate">
                              {role.description || '-'}
                            </p>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleEditRole(role)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleDeleteRole(role.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 角色表单对话框 */}
          <RoleForm
            open={isRoleFormOpen}
            onOpenChange={setIsRoleFormOpen}
            role={editingRole}
            onSuccess={handleFormSuccess}
          />
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
