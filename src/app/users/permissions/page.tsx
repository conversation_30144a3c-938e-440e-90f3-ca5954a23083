"use client"

import { useState, useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { Permission, PermissionGroup, PermissionModule } from '@/types/auth'
import {
  Shield,
  Plus,
  Search,
  Filter,
  Settings,
  Users,
  FileText,
  BarChart3,
  Database,
  Cog,
  Loader2,
  Edit,
  Trash2,
  Grid3X3
} from 'lucide-react'

export default function PermissionsPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedModule, setSelectedModule] = useState<string>('ALL')
  const { toast } = useToast()

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const response = await fetch('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setPermissions(result.data.permissions || [])
        setPermissionGroups(result.data.groups || [])
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取权限列表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取权限列表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 切换权限状态
  const togglePermissionStatus = async (permissionId: number, isActive: boolean) => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const response = await fetch(`/api/permissions/${permissionId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "成功",
          description: `权限已${isActive ? '启用' : '禁用'}`,
        })
        fetchPermissions() // 重新加载权限列表
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('更新权限状态失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "更新权限状态失败",
      })
    }
  }

  // 获取模块图标
  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'USER_MANAGEMENT':
        return <Users className="h-4 w-4" />
      case 'MEDICAL_CASE':
        return <FileText className="h-4 w-4" />
      case 'SUPERVISION_RULE':
        return <Shield className="h-4 w-4" />
      case 'KNOWLEDGE_BASE':
        return <Database className="h-4 w-4" />
      case 'ANALYTICS':
        return <BarChart3 className="h-4 w-4" />
      case 'SYSTEM_SETTINGS':
        return <Cog className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  // 获取操作类型颜色
  const getActionColor = (action: string) => {
    switch (action) {
      case 'CREATE':
        return 'bg-green-100 text-green-800'
      case 'READ':
        return 'bg-blue-100 text-blue-800'
      case 'UPDATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'DELETE':
        return 'bg-red-100 text-red-800'
      case 'EXPORT':
        return 'bg-purple-100 text-purple-800'
      case 'IMPORT':
        return 'bg-indigo-100 text-indigo-800'
      case 'APPROVE':
        return 'bg-emerald-100 text-emerald-800'
      case 'REJECT':
        return 'bg-orange-100 text-orange-800'
      case 'EXECUTE':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 获取操作类型文本
  const getActionText = (action: string) => {
    const actionMap: Record<string, string> = {
      'CREATE': '创建',
      'READ': '查看',
      'UPDATE': '更新',
      'DELETE': '删除',
      'EXPORT': '导出',
      'IMPORT': '导入',
      'APPROVE': '审批',
      'REJECT': '拒绝',
      'EXECUTE': '执行',
    }
    return actionMap[action] || action
  }

  // 过滤权限
  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = !searchTerm || 
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesModule = selectedModule === 'ALL' || permission.module === selectedModule
    
    return matchesSearch && matchesModule
  })

  // 初始加载
  useEffect(() => {
    fetchPermissions()
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">权限配置</h1>
              <p className="text-muted-foreground">管理系统权限和访问控制</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/users/permissions/matrix')}
              >
                <Grid3X3 className="h-4 w-4 mr-2" />
                权限矩阵
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新增权限
              </Button>
            </div>
          </div>

          {/* 搜索和过滤 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索权限名称、代码或描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select
                  value={selectedModule}
                  onValueChange={setSelectedModule}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="所有模块" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">所有模块</SelectItem>
                    <SelectItem value="USER_MANAGEMENT">用户管理</SelectItem>
                    <SelectItem value="MEDICAL_CASE">医疗案例</SelectItem>
                    <SelectItem value="SUPERVISION_RULE">监管规则</SelectItem>
                    <SelectItem value="KNOWLEDGE_BASE">知识库</SelectItem>
                    <SelectItem value="ANALYTICS">数据分析</SelectItem>
                    <SelectItem value="SYSTEM_SETTINGS">系统设置</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 权限统计 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总权限数</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {permissions.length}
                    </p>
                  </div>
                  <Shield className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">启用权限</p>
                    <p className="text-2xl font-bold text-green-600">
                      {permissions.filter(p => p.isActive).length}
                    </p>
                  </div>
                  <Settings className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">禁用权限</p>
                    <p className="text-2xl font-bold text-red-600">
                      {permissions.filter(p => !p.isActive).length}
                    </p>
                  </div>
                  <Settings className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">权限模块</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {permissionGroups.length}
                    </p>
                  </div>
                  <Database className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 权限列表 */}
          <Card>
            <CardHeader>
              <CardTitle>权限列表</CardTitle>
              <CardDescription>
                共 {filteredPermissions.length} 个权限
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>权限信息</TableHead>
                        <TableHead>模块</TableHead>
                        <TableHead>操作类型</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPermissions.map((permission) => (
                        <TableRow key={permission.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{permission.name}</p>
                              <p className="text-sm text-muted-foreground">{permission.code}</p>
                              {permission.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {permission.description}
                                </p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getModuleIcon(permission.module)}
                              <span className="text-sm">
                                {permissionGroups.find(g => g.module === permission.module)?.moduleName || permission.module}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getActionColor(permission.action)}>
                              {getActionText(permission.action)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={permission.isActive}
                                onCheckedChange={(checked) => togglePermissionStatus(permission.id, checked)}
                              />
                              <span className={`text-sm ${permission.isActive ? 'text-green-600' : 'text-red-600'}`}>
                                {permission.isActive ? '启用' : '禁用'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <p className="text-sm">
                              {new Date(permission.createdAt).toLocaleString('zh-CN')}
                            </p>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  // TODO: 实现编辑权限功能
                                  toast({
                                    title: "提示",
                                    description: "编辑权限功能开发中...",
                                  })
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  // TODO: 实现删除权限功能
                                  toast({
                                    title: "提示",
                                    description: "删除权限功能开发中...",
                                  })
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
