"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { Role, Permission, PermissionGroup } from '@/types/auth'
import {
  Shield,
  Save,
  RotateCcw,
  Users,
  FileText,
  BarChart3,
  Database,
  Cog,
  Settings,
  Loader2,
  ArrowLeft
} from 'lucide-react'

export default function PermissionMatrixPage() {
  const router = useRouter()
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [rolePermissions, setRolePermissions] = useState<Record<number, Set<number>>>({})
  const { toast } = useToast()

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) return

      const response = await fetch('/api/roles', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()
      if (result.success) {
        setRoles(result.data || [])
        
        // 初始化角色权限映射
        const rolePermMap: Record<number, Set<number>> = {}
        result.data.forEach((role: Role) => {
          rolePermMap[role.id] = new Set(role.permissions.map(p => p.id))
        })
        setRolePermissions(rolePermMap)
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
    }
  }

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) return

      const response = await fetch('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()
      if (result.success) {
        setPermissions(result.data.permissions || [])
        setPermissionGroups(result.data.groups || [])
      }
    } catch (error) {
      console.error('获取权限列表失败:', error)
    }
  }

  // 加载数据
  const loadData = async () => {
    setIsLoading(true)
    try {
      await Promise.all([fetchRoles(), fetchPermissions()])
    } catch (error) {
      toast({
        variant: "destructive",
        title: "错误",
        description: "加载数据失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 切换权限
  const togglePermission = (roleId: number, permissionId: number) => {
    setRolePermissions(prev => {
      const newMap = { ...prev }
      if (!newMap[roleId]) {
        newMap[roleId] = new Set()
      }
      
      if (newMap[roleId].has(permissionId)) {
        newMap[roleId].delete(permissionId)
      } else {
        newMap[roleId].add(permissionId)
      }
      
      return newMap
    })
  }

  // 保存权限矩阵
  const savePermissionMatrix = async () => {
    setIsSaving(true)
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      // 为每个角色更新权限
      for (const role of roles) {
        const permissionIds = Array.from(rolePermissions[role.id] || [])
        
        const response = await fetch(`/api/roles/${role.id}/permissions`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ permissionIds }),
        })

        const result = await response.json()
        if (!result.success) {
          throw new Error(`更新角色 ${role.roleName} 权限失败: ${result.message}`)
        }
      }

      toast({
        title: "成功",
        description: "权限矩阵保存成功",
      })

      // 重新加载数据
      await loadData()

    } catch (error) {
      console.error('保存权限矩阵失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: error instanceof Error ? error.message : "保存权限矩阵失败",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 重置权限矩阵
  const resetPermissionMatrix = () => {
    const rolePermMap: Record<number, Set<number>> = {}
    roles.forEach(role => {
      rolePermMap[role.id] = new Set(role.permissions.map(p => p.id))
    })
    setRolePermissions(rolePermMap)
    
    toast({
      title: "提示",
      description: "权限矩阵已重置",
    })
  }

  // 获取模块图标
  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'USER_MANAGEMENT':
        return <Users className="h-4 w-4" />
      case 'MEDICAL_CASE':
        return <FileText className="h-4 w-4" />
      case 'SUPERVISION_RULE':
        return <Shield className="h-4 w-4" />
      case 'KNOWLEDGE_BASE':
        return <Database className="h-4 w-4" />
      case 'ANALYTICS':
        return <BarChart3 className="h-4 w-4" />
      case 'SYSTEM_SETTINGS':
        return <Cog className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  // 获取角色颜色
  const getRoleColor = (roleCode: string) => {
    switch (roleCode) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800'
      case 'SUPERVISOR':
        return 'bg-blue-100 text-blue-800'
      case 'OPERATOR':
        return 'bg-green-100 text-green-800'
      case 'AUDITOR':
        return 'bg-yellow-100 text-yellow-800'
      case 'VIEWER':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 初始加载
  useEffect(() => {
    loadData()
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">权限矩阵</h1>
              <p className="text-muted-foreground">管理角色和权限的对应关系</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/users/permissions')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回权限列表
              </Button>
              <Button variant="outline" onClick={resetPermissionMatrix}>
                <RotateCcw className="h-4 w-4 mr-2" />
                重置
              </Button>
              <Button onClick={savePermissionMatrix} disabled={isSaving}>
                {isSaving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                保存
              </Button>
            </div>
          </div>

          {/* 权限矩阵 */}
          <Card>
            <CardHeader>
              <CardTitle>角色权限矩阵</CardTitle>
              <CardDescription>
                勾选表示该角色拥有对应权限，取消勾选表示移除权限
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-64">权限</TableHead>
                        {roles.map(role => (
                          <TableHead key={role.id} className="text-center min-w-24">
                            <div className="flex flex-col items-center space-y-1">
                              <Badge className={getRoleColor(role.roleCode)}>
                                {role.roleName}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {rolePermissions[role.id]?.size || 0} 权限
                              </span>
                            </div>
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {permissionGroups.map(group => (
                        <React.Fragment key={group.module}>
                          {/* 模块标题行 */}
                          <TableRow className="bg-muted/50">
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                {getModuleIcon(group.module)}
                                <span>{group.moduleName}</span>
                              </div>
                            </TableCell>
                            {roles.map(role => (
                              <TableCell key={role.id} className="text-center">
                                <span className="text-xs text-muted-foreground">
                                  {group.permissions.filter(p => rolePermissions[role.id]?.has(p.id)).length}/{group.permissions.length}
                                </span>
                              </TableCell>
                            ))}
                          </TableRow>
                          {/* 权限行 */}
                          {group.permissions.map(permission => (
                            <TableRow key={permission.id}>
                              <TableCell>
                                <div className="pl-6">
                                  <p className="font-medium">{permission.name}</p>
                                  <p className="text-sm text-muted-foreground">{permission.code}</p>
                                </div>
                              </TableCell>
                              {roles.map(role => (
                                <TableCell key={role.id} className="text-center">
                                  <Checkbox
                                    checked={rolePermissions[role.id]?.has(permission.id) || false}
                                    onCheckedChange={() => togglePermission(role.id, permission.id)}
                                    disabled={!permission.isActive}
                                  />
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </React.Fragment>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
