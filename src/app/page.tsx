'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { LoadingLayout } from '@/components/layout/loading-layout'

export default function HomePage() {
  const router = useRouter()
  const { user, isLoading } = useAuth()

  useEffect(() => {
    if (!isLoading) {
      if (user) {
        // 用户已登录，重定向到仪表板
        router.replace('/dashboard')
      } else {
        // 用户未登录，重定向到登录页
        router.replace('/auth/login')
      }
    }
  }, [user, isLoading, router])

  // 显示加载状态
  return <LoadingLayout />
}
