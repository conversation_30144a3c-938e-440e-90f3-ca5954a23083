"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { businessToast } from '@/lib/toast'
import { BaseStatistics, RealTimeMonitoringData, TimeRange } from '@/types/analytics'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  DollarSign,
  FileText,
  Shield,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw
} from 'lucide-react'
import { ExportButton } from '@/components/analytics/export-button'

// 图表颜色配置 - 使用shadcn/UI设计token
const CHART_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
  'hsl(var(--primary))'
]

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<TimeRange>('LAST_30_DAYS')
  const [baseStats, setBaseStats] = useState<BaseStatistics | null>(null)
  const [realTimeData, setRealTimeData] = useState<RealTimeMonitoringData | null>(null)
  const [caseSummary, setCaseSummary] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>('')


  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        return
      }

      const response = await fetch(`/api/analytics/dashboard?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setBaseStats(result.data.baseStats)
        setRealTimeData(result.data.realTimeData)
        setCaseSummary(result.data.caseSummary)
        setLastUpdated(new Date().toLocaleString())
      } else {
        businessToast.networkError()
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
      businessToast.networkError()
    } finally {
      setIsLoading(false)
    }
  }

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount)
  }

  // 获取时间范围文本
  const getTimeRangeText = (range: TimeRange) => {
    const timeRangeMap: Record<TimeRange, string> = {
      'TODAY': '今天',
      'YESTERDAY': '昨天',
      'LAST_7_DAYS': '最近7天',
      'LAST_30_DAYS': '最近30天',
      'LAST_90_DAYS': '最近90天',
      'THIS_MONTH': '本月',
      'LAST_MONTH': '上月',
      'THIS_QUARTER': '本季度',
      'LAST_QUARTER': '上季度',
      'THIS_YEAR': '今年',
      'LAST_YEAR': '去年',
      'CUSTOM': '自定义',
    }
    return timeRangeMap[range] || range
  }

  // 时间范围变化时重新获取数据
  useEffect(() => {
    fetchDashboardData()
  }, [timeRange])

  // 初始加载
  useEffect(() => {
    fetchDashboardData()
    
    // 设置定时刷新实时数据
    const interval = setInterval(() => {
      fetchDashboardData()
    }, 30000) // 30秒刷新一次

    return () => clearInterval(interval)
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题和控制 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">数据分析</h1>
              <p className="text-muted-foreground">
                医保基金监管数据分析与可视化 {lastUpdated && `• 最后更新: ${lastUpdated}`}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TODAY">今天</SelectItem>
                  <SelectItem value="YESTERDAY">昨天</SelectItem>
                  <SelectItem value="LAST_7_DAYS">最近7天</SelectItem>
                  <SelectItem value="LAST_30_DAYS">最近30天</SelectItem>
                  <SelectItem value="LAST_90_DAYS">最近90天</SelectItem>
                  <SelectItem value="THIS_MONTH">本月</SelectItem>
                  <SelectItem value="LAST_MONTH">上月</SelectItem>
                  <SelectItem value="THIS_QUARTER">本季度</SelectItem>
                  <SelectItem value="THIS_YEAR">今年</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={fetchDashboardData}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                刷新
              </Button>
              {baseStats && (
                <ExportButton
                  data={{
                    baseStats,
                    realTimeData,
                    timeRange,
                    exportTime: new Date().toISOString()
                  }}
                  filename="数据分析中心"
                  dataType="raw"
                />
              )}
            </div>
          </div>

          {isLoading && !baseStats ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : (
            <>
              {/* 实时监控卡片 */}
              {realTimeData && (
                <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">活跃案例</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatNumber(realTimeData.activeCases)}
                          </p>
                        </div>
                        <Activity className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">待执行规则</p>
                          <p className="text-2xl font-bold text-orange-600">
                            {formatNumber(realTimeData.pendingRules)}
                          </p>
                        </div>
                        <Clock className="h-8 w-8 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">最近违规</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatNumber(realTimeData.recentViolations)}
                          </p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-red-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">系统负载</p>
                          <p className="text-2xl font-bold text-green-600">
                            {realTimeData.systemLoad}%
                          </p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">告警数量</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatNumber(realTimeData.alertCount)}
                          </p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">在线用户</p>
                          <p className="text-2xl font-bold text-indigo-600">
                            {formatNumber(realTimeData.onlineUsers)}
                          </p>
                        </div>
                        <Users className="h-8 w-8 text-indigo-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* 基础统计卡片 */}
              {baseStats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总案例数</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatNumber(baseStats.totalCases)}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {getTimeRangeText(timeRange)}
                          </p>
                        </div>
                        <FileText className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总费用</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(baseStats.totalCost)}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            平均: {formatCurrency(baseStats.avgCostPerCase)}
                          </p>
                        </div>
                        <DollarSign className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">违规检出</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatNumber(baseStats.totalViolations)}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            违规率: {baseStats.violationRate.toFixed(2)}%
                          </p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-red-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">规则执行</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatNumber(baseStats.ruleExecutions)}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            成功率: {baseStats.ruleSuccessRate.toFixed(2)}%
                          </p>
                        </div>
                        <Shield className="h-8 w-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* 图表区域 */}
              {caseSummary && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* 案例类型分布 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>案例类型分布</CardTitle>
                      <CardDescription>按案例类型统计分布情况</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={caseSummary.casesByType}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ category, percentage }) => `${category}: ${percentage.toFixed(1)}%`}
                            outerRadius={80}
                            fill="hsl(var(--primary))"
                            dataKey="count"
                          >
                            {caseSummary.casesByType.map((entry: any, index: number) => (
                              <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  {/* 费用分布 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>费用区间分布</CardTitle>
                      <CardDescription>按费用区间统计案例分布</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={caseSummary.costDistribution}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="category" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="count" fill="hsl(var(--primary))" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  {/* 月度趋势 */}
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle>案例月度趋势</CardTitle>
                      <CardDescription>最近12个月的案例数量变化趋势</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={400}>
                        <AreaChart data={caseSummary.monthlyTrend}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Area
                            type="monotone"
                            dataKey="value"
                            stroke="hsl(var(--primary))"
                            fill="hsl(var(--primary))"
                            fillOpacity={0.6}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* 医院排名 */}
              {caseSummary?.topHospitals && (
                <Card>
                  <CardHeader>
                    <CardTitle>医院案例排名</CardTitle>
                    <CardDescription>按案例数量排名的前5名医院</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {caseSummary.topHospitals.map((hospital: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-bold">
                              {hospital.rank}
                            </div>
                            <div>
                              <p className="font-medium">{hospital.name}</p>
                              {hospital.code && (
                                <p className="text-sm text-muted-foreground">{hospital.code}</p>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-lg">{formatNumber(hospital.count)}</p>
                            <p className="text-sm text-muted-foreground">
                              {hospital.percentage.toFixed(1)}%
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 追回和处罚金额 */}
              {baseStats && (baseStats.totalRecoveryAmount > 0 || baseStats.totalPenaltyAmount > 0) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总追回金额</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(baseStats.totalRecoveryAmount)}
                          </p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总处罚金额</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatCurrency(baseStats.totalPenaltyAmount)}
                          </p>
                        </div>
                        <XCircle className="h-8 w-8 text-red-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
