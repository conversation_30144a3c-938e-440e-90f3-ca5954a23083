"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { TrendAnalysisReport, TimeRange } from '@/types/analytics'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ComposedChart,
  Bar
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Calendar,
  BarChart3,
  Loader2,
  RefreshCw
} from 'lucide-react'

export default function TrendsPage() {
  const [timeRange, setTimeRange] = useState<TimeRange>('LAST_90_DAYS')
  const [trendReport, setTrendReport] = useState<TrendAnalysisReport | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>('')
  const { toast } = useToast()

  // 获取趋势分析报表
  const fetchTrendReport = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      // 模拟API调用 - 实际项目中应该调用真实API
      // const response = await fetch(`/api/analytics/reports/trend-analysis?timeRange=${timeRange}`, {
      //   headers: {
      //     'Authorization': `Bearer ${token}`,
      //   },
      // })

      // 模拟数据
      const mockTrendReport: TrendAnalysisReport = {
        caseCountTrend: [
          { date: '2024-01', value: 120 },
          { date: '2024-02', value: 135 },
          { date: '2024-03', value: 142 },
          { date: '2024-04', value: 158 },
          { date: '2024-05', value: 167 },
          { date: '2024-06', value: 145 },
          { date: '2024-07', value: 178 },
          { date: '2024-08', value: 189 },
          { date: '2024-09', value: 195 },
          { date: '2024-10', value: 203 },
          { date: '2024-11', value: 187 },
          { date: '2024-12', value: 210 },
        ],
        costTrend: [
          { date: '2024-01', value: 1250000 },
          { date: '2024-02', value: 1380000 },
          { date: '2024-03', value: 1420000 },
          { date: '2024-04', value: 1580000 },
          { date: '2024-05', value: 1670000 },
          { date: '2024-06', value: 1450000 },
          { date: '2024-07', value: 1780000 },
          { date: '2024-08', value: 1890000 },
          { date: '2024-09', value: 1950000 },
          { date: '2024-10', value: 2030000 },
          { date: '2024-11', value: 1870000 },
          { date: '2024-12', value: 2100000 },
        ],
        violationTrend: [
          { date: '2024-01', value: 12 },
          { date: '2024-02', value: 15 },
          { date: '2024-03', value: 18 },
          { date: '2024-04', value: 22 },
          { date: '2024-05', value: 19 },
          { date: '2024-06', value: 16 },
          { date: '2024-07', value: 25 },
          { date: '2024-08', value: 28 },
          { date: '2024-09', value: 31 },
          { date: '2024-10', value: 27 },
          { date: '2024-11', value: 24 },
          { date: '2024-12', value: 33 },
        ],
        ruleExecutionTrend: [
          { date: '2024-01', value: 1200 },
          { date: '2024-02', value: 1350 },
          { date: '2024-03', value: 1420 },
          { date: '2024-04', value: 1580 },
          { date: '2024-05', value: 1670 },
          { date: '2024-06', value: 1450 },
          { date: '2024-07', value: 1780 },
          { date: '2024-08', value: 1890 },
          { date: '2024-09', value: 1950 },
          { date: '2024-10', value: 2030 },
          { date: '2024-11', value: 1870 },
          { date: '2024-12', value: 2100 },
        ],
        seasonalAnalysis: [
          { season: '春季', avgCases: 145, avgCost: 1416667, avgViolations: 15, trend: 'UP' },
          { season: '夏季', avgCases: 160, avgCost: 1706667, avgViolations: 23, trend: 'UP' },
          { season: '秋季', avgCases: 195, avgCost: 1950000, avgViolations: 27, trend: 'UP' },
          { season: '冬季', avgCases: 199, avgCost: 1985000, avgViolations: 29, trend: 'STABLE' },
        ],
        yearOverYearComparison: [
          { metric: '案例数量', currentYear: 2100, lastYear: 1850, changeRate: 13.5, trend: 'UP' },
          { metric: '总费用', currentYear: 20000000, lastYear: 17500000, changeRate: 14.3, trend: 'UP' },
          { metric: '违规数量', currentYear: 270, lastYear: 220, changeRate: 22.7, trend: 'UP' },
          { metric: '规则执行', currentYear: 19290, lastYear: 16800, changeRate: 14.8, trend: 'UP' },
        ],
        forecastData: [
          { 
            date: '2025-01', 
            predictedCases: 215, 
            predictedCost: 2150000, 
            predictedViolations: 35,
            confidenceInterval: { lower: 200, upper: 230 }
          },
          { 
            date: '2025-02', 
            predictedCases: 220, 
            predictedCost: 2200000, 
            predictedViolations: 36,
            confidenceInterval: { lower: 205, upper: 235 }
          },
          { 
            date: '2025-03', 
            predictedCases: 225, 
            predictedCost: 2250000, 
            predictedViolations: 37,
            confidenceInterval: { lower: 210, upper: 240 }
          },
        ]
      }

      setTrendReport(mockTrendReport)
      setLastUpdated(new Date().toLocaleString())

    } catch (error) {
      console.error('获取趋势分析报表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取趋势分析报表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount)
  }

  // 获取趋势图标
  const getTrendIcon = (trend: 'UP' | 'DOWN' | 'STABLE') => {
    switch (trend) {
      case 'UP':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'DOWN':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'STABLE':
        return <Activity className="h-4 w-4 text-blue-500" />
    }
  }

  // 获取趋势颜色
  const getTrendColor = (trend: 'UP' | 'DOWN' | 'STABLE') => {
    switch (trend) {
      case 'UP':
        return 'text-green-600'
      case 'DOWN':
        return 'text-red-600'
      case 'STABLE':
        return 'text-blue-600'
    }
  }

  // 时间范围变化时重新获取数据
  useEffect(() => {
    fetchTrendReport()
  }, [timeRange])

  // 初始加载
  useEffect(() => {
    fetchTrendReport()
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题和控制 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">趋势分析</h1>
              <p className="text-muted-foreground">
                医保基金监管数据趋势分析与预测 {lastUpdated && `• 最后更新: ${lastUpdated}`}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LAST_30_DAYS">最近30天</SelectItem>
                  <SelectItem value="LAST_90_DAYS">最近90天</SelectItem>
                  <SelectItem value="THIS_QUARTER">本季度</SelectItem>
                  <SelectItem value="THIS_YEAR">今年</SelectItem>
                  <SelectItem value="LAST_YEAR">去年</SelectItem>
                </SelectContent>
              </Select>
              <Button 
                variant="outline" 
                onClick={fetchTrendReport}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                刷新
              </Button>
            </div>
          </div>

          {isLoading && !trendReport ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : trendReport ? (
            <>
              {/* 综合趋势图 */}
              <Card>
                <CardHeader>
                  <CardTitle>综合趋势分析</CardTitle>
                  <CardDescription>案例数量、费用和违规数量的综合趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <ComposedChart data={trendReport.caseCountTrend.map((item, index) => ({
                      ...item,
                      cost: trendReport.costTrend[index]?.value || 0,
                      violations: trendReport.violationTrend[index]?.value || 0,
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip 
                        formatter={(value, name) => {
                          if (name === 'cost') return [formatCurrency(Number(value)), '费用']
                          if (name === 'value') return [formatNumber(Number(value)), '案例数']
                          if (name === 'violations') return [formatNumber(Number(value)), '违规数']
                          return [value, name]
                        }}
                      />
                      <Legend />
                      <Bar yAxisId="left" dataKey="value" fill="hsl(var(--chart-5))" name="案例数" />
                      <Line yAxisId="right" type="monotone" dataKey="cost" stroke="#82ca9d" name="费用" />
                      <Line yAxisId="left" type="monotone" dataKey="violations" stroke="#ff7300" name="违规数" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* 季节性分析 */}
              <Card>
                <CardHeader>
                  <CardTitle>季节性分析</CardTitle>
                  <CardDescription>按季度统计的平均数据和趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {trendReport.seasonalAnalysis.map((season, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium">{season.season}</h3>
                          {getTrendIcon(season.trend)}
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">平均案例</span>
                            <span className="font-medium">{formatNumber(season.avgCases)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">平均费用</span>
                            <span className="font-medium">{formatCurrency(season.avgCost)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">平均违规</span>
                            <span className="font-medium">{formatNumber(season.avgViolations)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 同比分析 */}
              <Card>
                <CardHeader>
                  <CardTitle>同比分析</CardTitle>
                  <CardDescription>与去年同期数据对比分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {trendReport.yearOverYearComparison.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium">{item.metric}</p>
                            <p className="text-sm text-muted-foreground">
                              今年: {item.metric.includes('费用') ? formatCurrency(item.currentYear) : formatNumber(item.currentYear)} |
                              去年: {item.metric.includes('费用') ? formatCurrency(item.lastYear) : formatNumber(item.lastYear)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getTrendIcon(item.trend)}
                          <span className={`font-bold ${getTrendColor(item.trend)}`}>
                            {item.changeRate > 0 ? '+' : ''}{item.changeRate.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 预测数据 */}
              <Card>
                <CardHeader>
                  <CardTitle>预测分析</CardTitle>
                  <CardDescription>基于历史数据的未来趋势预测</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 案例数预测 */}
                    <div>
                      <h4 className="font-medium mb-4">案例数量预测</h4>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={[
                          ...trendReport.caseCountTrend.slice(-6),
                          ...trendReport.forecastData.map(item => ({
                            date: item.date,
                            value: item.predictedCases,
                            predicted: true
                          }))
                        ]}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Area
                            type="monotone"
                            dataKey="value"
                            stroke="hsl(var(--chart-5))"
                            fill="hsl(var(--chart-5))"
                            fillOpacity={0.6}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>

                    {/* 费用预测 */}
                    <div>
                      <h4 className="font-medium mb-4">费用预测</h4>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={[
                          ...trendReport.costTrend.slice(-6),
                          ...trendReport.forecastData.map(item => ({
                            date: item.date,
                            value: item.predictedCost,
                            predicted: true
                          }))
                        ]}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                          <Area
                            type="monotone"
                            dataKey="value"
                            stroke="#82ca9d"
                            fill="#82ca9d"
                            fillOpacity={0.6}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </div>

                  {/* 预测数据表格 */}
                  <div className="mt-6">
                    <h4 className="font-medium mb-4">详细预测数据</h4>
                    <div className="space-y-2">
                      {trendReport.forecastData.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex items-center space-x-4">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{item.date}</span>
                          </div>
                          <div className="flex items-center space-x-6 text-sm">
                            <div className="text-center">
                              <p className="text-muted-foreground">预测案例</p>
                              <p className="font-medium">{formatNumber(item.predictedCases)}</p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">预测费用</p>
                              <p className="font-medium">{formatCurrency(item.predictedCost)}</p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">预测违规</p>
                              <p className="font-medium">{formatNumber(item.predictedViolations)}</p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">置信区间</p>
                              <p className="font-medium">
                                {item.confidenceInterval.lower}-{item.confidenceInterval.upper}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">暂无数据</p>
            </div>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
