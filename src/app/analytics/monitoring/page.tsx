"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { RealTimeMonitoringData, AnomalyMonitoringConfig } from '@/types/analytics'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts'
import {
  AlertTriangle,
  Activity,
  Shield,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  Settings,
  Bell,
  Mail,
  Smartphone,
  Webhook
} from 'lucide-react'

export default function MonitoringPage() {
  const [realTimeData, setRealTimeData] = useState<RealTimeMonitoringData | null>(null)
  const [monitoringConfig, setMonitoringConfig] = useState<AnomalyMonitoringConfig>({
    enabled: true,
    thresholds: {
      costAnomalyThreshold: 50000,
      violationRateThreshold: 15,
      ruleFailureRateThreshold: 10,
      systemLoadThreshold: 80,
    },
    alertChannels: {
      email: true,
      sms: false,
      webhook: false,
    },
    recipients: ['<EMAIL>'],
  })
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<string>('')
  const { toast } = useToast()

  // 获取实时监控数据
  const fetchRealTimeData = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const response = await fetch('/api/analytics/monitoring', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setRealTimeData(result.data)
        setLastUpdated(new Date().toLocaleString())
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取实时监控数据失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取实时监控数据失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  // 获取告警级别颜色
  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'LOW':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取告警图标
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'VIOLATION':
        return <AlertTriangle className="h-4 w-4" />
      case 'SYSTEM':
        return <Activity className="h-4 w-4" />
      case 'RULE_FAILURE':
        return <XCircle className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  // 保存监控配置
  const saveMonitoringConfig = async () => {
    try {
      // 这里应该调用API保存配置
      toast({
        title: "成功",
        description: "监控配置已保存",
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "错误",
        description: "保存配置失败",
      })
    }
  }

  // 初始加载和定时刷新
  useEffect(() => {
    fetchRealTimeData()
    
    // 设置定时刷新
    const interval = setInterval(() => {
      fetchRealTimeData()
    }, 10000) // 10秒刷新一次

    return () => clearInterval(interval)
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题和控制 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">异常监控</h1>
              <p className="text-muted-foreground">
                实时监控系统状态和异常告警 {lastUpdated && `• 最后更新: ${lastUpdated}`}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={fetchRealTimeData}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                刷新
              </Button>
            </div>
          </div>

          {isLoading && !realTimeData ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : (
            <>
              {/* 实时状态卡片 */}
              {realTimeData && (
                <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">活跃案例</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatNumber(realTimeData.activeCases)}
                          </p>
                        </div>
                        <Activity className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">待执行规则</p>
                          <p className="text-2xl font-bold text-orange-600">
                            {formatNumber(realTimeData.pendingRules)}
                          </p>
                        </div>
                        <Clock className="h-8 w-8 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">最近违规</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatNumber(realTimeData.recentViolations)}
                          </p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-red-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">系统负载</p>
                          <p className={`text-2xl font-bold ${realTimeData.systemLoad > 80 ? 'text-red-600' : 'text-green-600'}`}>
                            {realTimeData.systemLoad}%
                          </p>
                        </div>
                        <Activity className={`h-8 w-8 ${realTimeData.systemLoad > 80 ? 'text-red-500' : 'text-green-500'}`} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">告警数量</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatNumber(realTimeData.alertCount)}
                          </p>
                        </div>
                        <Bell className="h-8 w-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">在线用户</p>
                          <p className="text-2xl font-bold text-indigo-600">
                            {formatNumber(realTimeData.onlineUsers)}
                          </p>
                        </div>
                        <Users className="h-8 w-8 text-indigo-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* 最近告警 */}
              {realTimeData?.recentAlerts && realTimeData.recentAlerts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>最近告警</CardTitle>
                    <CardDescription>系统最近产生的告警信息</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {realTimeData.recentAlerts.map((alert, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            {getAlertIcon(alert.type)}
                            <div>
                              <p className="font-medium">{alert.message}</p>
                              <p className="text-sm text-muted-foreground">
                                {new Date(alert.timestamp).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <Badge className={getAlertColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 监控配置 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>监控配置</span>
                  </CardTitle>
                  <CardDescription>配置异常监控阈值和告警通道</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 启用/禁用监控 */}
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="monitoring-enabled">启用异常监控</Label>
                      <p className="text-sm text-muted-foreground">开启或关闭异常监控功能</p>
                    </div>
                    <Switch
                      id="monitoring-enabled"
                      checked={monitoringConfig.enabled}
                      onCheckedChange={(checked) => 
                        setMonitoringConfig(prev => ({ ...prev, enabled: checked }))
                      }
                    />
                  </div>

                  {/* 阈值配置 */}
                  <div className="space-y-4">
                    <h4 className="font-medium">告警阈值</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="cost-threshold">费用异常阈值 (元)</Label>
                        <Input
                          id="cost-threshold"
                          type="number"
                          value={monitoringConfig.thresholds.costAnomalyThreshold}
                          onChange={(e) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              thresholds: {
                                ...prev.thresholds,
                                costAnomalyThreshold: Number(e.target.value)
                              }
                            }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="violation-threshold">违规率阈值 (%)</Label>
                        <Input
                          id="violation-threshold"
                          type="number"
                          value={monitoringConfig.thresholds.violationRateThreshold}
                          onChange={(e) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              thresholds: {
                                ...prev.thresholds,
                                violationRateThreshold: Number(e.target.value)
                              }
                            }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="rule-failure-threshold">规则失败率阈值 (%)</Label>
                        <Input
                          id="rule-failure-threshold"
                          type="number"
                          value={monitoringConfig.thresholds.ruleFailureRateThreshold}
                          onChange={(e) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              thresholds: {
                                ...prev.thresholds,
                                ruleFailureRateThreshold: Number(e.target.value)
                              }
                            }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="system-load-threshold">系统负载阈值 (%)</Label>
                        <Input
                          id="system-load-threshold"
                          type="number"
                          value={monitoringConfig.thresholds.systemLoadThreshold}
                          onChange={(e) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              thresholds: {
                                ...prev.thresholds,
                                systemLoadThreshold: Number(e.target.value)
                              }
                            }))
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* 告警通道 */}
                  <div className="space-y-4">
                    <h4 className="font-medium">告警通道</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4" />
                          <Label htmlFor="email-alerts">邮件告警</Label>
                        </div>
                        <Switch
                          id="email-alerts"
                          checked={monitoringConfig.alertChannels.email}
                          onCheckedChange={(checked) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              alertChannels: { ...prev.alertChannels, email: checked }
                            }))
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Smartphone className="h-4 w-4" />
                          <Label htmlFor="sms-alerts">短信告警</Label>
                        </div>
                        <Switch
                          id="sms-alerts"
                          checked={monitoringConfig.alertChannels.sms}
                          onCheckedChange={(checked) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              alertChannels: { ...prev.alertChannels, sms: checked }
                            }))
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Webhook className="h-4 w-4" />
                          <Label htmlFor="webhook-alerts">Webhook告警</Label>
                        </div>
                        <Switch
                          id="webhook-alerts"
                          checked={monitoringConfig.alertChannels.webhook}
                          onCheckedChange={(checked) => 
                            setMonitoringConfig(prev => ({
                              ...prev,
                              alertChannels: { ...prev.alertChannels, webhook: checked }
                            }))
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* 保存按钮 */}
                  <div className="flex justify-end">
                    <Button onClick={saveMonitoringConfig}>
                      保存配置
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
