"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { CaseSummaryReport, CostAnalysisReport, RulePerformanceReport, TimeRange } from '@/types/analytics'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import {
  FileText,
  DollarSign,
  Shield,
  Download,
  Calendar,
  Filter,
  Loader2,
  RefreshCw
} from 'lucide-react'

// 图表颜色配置
const COLORS = ['hsl(var(--chart-1))', 'hsl(var(--chart-2))', 'hsl(var(--chart-3))', 'hsl(var(--chart-4))', 'hsl(var(--chart-5))', 'hsl(var(--primary))']

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('case-summary')
  const [timeRange, setTimeRange] = useState<TimeRange>('LAST_30_DAYS')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [caseSummaryReport, setCaseSummaryReport] = useState<CaseSummaryReport | null>(null)
  const [costAnalysisReport, setCostAnalysisReport] = useState<CostAnalysisReport | null>(null)
  const [rulePerformanceReport, setRulePerformanceReport] = useState<RulePerformanceReport | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<string>('')
  const { toast } = useToast()

  // 获取案例汇总报表
  const fetchCaseSummaryReport = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const params = new URLSearchParams({
        timeRange,
        ...(timeRange === 'CUSTOM' && startDate && endDate && {
          startDate,
          endDate,
        }),
      })

      const response = await fetch(`/api/analytics/reports/case-summary?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setCaseSummaryReport(result.data)
        setLastUpdated(new Date().toLocaleString())
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取案例汇总报表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取案例汇总报表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取费用分析报表
  const fetchCostAnalysisReport = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const params = new URLSearchParams({
        timeRange,
        ...(timeRange === 'CUSTOM' && startDate && endDate && {
          startDate,
          endDate,
        }),
      })

      const response = await fetch(`/api/analytics/reports/cost-analysis?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setCostAnalysisReport(result.data)
        setLastUpdated(new Date().toLocaleString())
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取费用分析报表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取费用分析报表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取规则执行效果报表
  const fetchRulePerformanceReport = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const params = new URLSearchParams({
        timeRange,
        ...(timeRange === 'CUSTOM' && startDate && endDate && {
          startDate,
          endDate,
        }),
      })

      const response = await fetch(`/api/analytics/reports/rule-performance?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setRulePerformanceReport(result.data)
        setLastUpdated(new Date().toLocaleString())
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取规则执行效果报表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取规则执行效果报表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount)
  }

  // 获取时间范围文本
  const getTimeRangeText = (range: TimeRange) => {
    const timeRangeMap: Record<TimeRange, string> = {
      'TODAY': '今天',
      'YESTERDAY': '昨天',
      'LAST_7_DAYS': '最近7天',
      'LAST_30_DAYS': '最近30天',
      'LAST_90_DAYS': '最近90天',
      'THIS_MONTH': '本月',
      'LAST_MONTH': '上月',
      'THIS_QUARTER': '本季度',
      'LAST_QUARTER': '上季度',
      'THIS_YEAR': '今年',
      'LAST_YEAR': '去年',
      'CUSTOM': '自定义',
    }
    return timeRangeMap[range] || range
  }

  // 刷新当前报表
  const refreshCurrentReport = () => {
    switch (activeTab) {
      case 'case-summary':
        fetchCaseSummaryReport()
        break
      case 'cost-analysis':
        fetchCostAnalysisReport()
        break
      case 'rule-performance':
        fetchRulePerformanceReport()
        break
    }
  }

  // 标签页变化时获取对应报表
  useEffect(() => {
    refreshCurrentReport()
  }, [activeTab, timeRange, startDate, endDate])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题和控制 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">统计报表</h1>
              <p className="text-muted-foreground">
                医保基金监管数据统计分析报表 {lastUpdated && `• 最后更新: ${lastUpdated}`}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TODAY">今天</SelectItem>
                  <SelectItem value="YESTERDAY">昨天</SelectItem>
                  <SelectItem value="LAST_7_DAYS">最近7天</SelectItem>
                  <SelectItem value="LAST_30_DAYS">最近30天</SelectItem>
                  <SelectItem value="LAST_90_DAYS">最近90天</SelectItem>
                  <SelectItem value="THIS_MONTH">本月</SelectItem>
                  <SelectItem value="LAST_MONTH">上月</SelectItem>
                  <SelectItem value="THIS_QUARTER">本季度</SelectItem>
                  <SelectItem value="THIS_YEAR">今年</SelectItem>
                  <SelectItem value="CUSTOM">自定义</SelectItem>
                </SelectContent>
              </Select>
              
              {timeRange === 'CUSTOM' && (
                <>
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-40"
                  />
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-40"
                  />
                </>
              )}
              
              <Button 
                variant="outline" 
                onClick={refreshCurrentReport}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                刷新
              </Button>
              
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>

          {/* 报表标签页 */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="case-summary" className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>案例汇总</span>
              </TabsTrigger>
              <TabsTrigger value="cost-analysis" className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4" />
                <span>费用分析</span>
              </TabsTrigger>
              <TabsTrigger value="rule-performance" className="flex items-center space-x-2">
                <Shield className="h-4 w-4" />
                <span>规则效果</span>
              </TabsTrigger>
            </TabsList>

            {/* 案例汇总报表 */}
            <TabsContent value="case-summary" className="space-y-6">
              {isLoading && !caseSummaryReport ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : caseSummaryReport ? (
                <>
                  {/* 基础统计 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">总案例数</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatNumber(caseSummaryReport.baseStats.totalCases)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">总费用</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(caseSummaryReport.baseStats.totalCost)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">违规检出</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatNumber(caseSummaryReport.baseStats.totalViolations)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">违规率</p>
                          <p className="text-2xl font-bold text-orange-600">
                            {caseSummaryReport.baseStats.violationRate.toFixed(2)}%
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 图表区域 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 案例类型分布 */}
                    <Card>
                      <CardHeader>
                        <CardTitle>案例类型分布</CardTitle>
                        <CardDescription>按案例类型统计分布情况</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={caseSummaryReport.casesByType}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ category, percentage }) => `${category}: ${percentage.toFixed(1)}%`}
                              outerRadius={80}
                              fill="hsl(var(--chart-5))"
                              dataKey="count"
                            >
                              {caseSummaryReport.casesByType.map((entry: any, index: number) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* 医疗类别分布 */}
                    <Card>
                      <CardHeader>
                        <CardTitle>医疗类别分布</CardTitle>
                        <CardDescription>按医疗类别统计分布情况</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={caseSummaryReport.casesByCategory}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="category" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" fill="hsl(var(--chart-5))" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* 月度趋势 */}
                    <Card className="lg:col-span-2">
                      <CardHeader>
                        <CardTitle>案例月度趋势</CardTitle>
                        <CardDescription>案例数量变化趋势</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={400}>
                          <AreaChart data={caseSummaryReport.monthlyTrend}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Area
                              type="monotone"
                              dataKey="value"
                              stroke="hsl(var(--chart-5))"
                              fill="hsl(var(--chart-5))"
                              fillOpacity={0.6}
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 医院排名 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>医院案例排名</CardTitle>
                      <CardDescription>按案例数量排名的医院</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {caseSummaryReport.casesByHospital.map((hospital, index) => (
                          <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-bold">
                                {hospital.rank}
                              </div>
                              <div>
                                <p className="font-medium">{hospital.name}</p>
                                {hospital.code && (
                                  <p className="text-sm text-muted-foreground">{hospital.code}</p>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-lg">{formatNumber(hospital.count)}</p>
                              <p className="text-sm text-muted-foreground">
                                {hospital.percentage.toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">暂无数据</p>
                </div>
              )}
            </TabsContent>

            {/* 费用分析报表 */}
            <TabsContent value="cost-analysis" className="space-y-6">
              {isLoading && !costAnalysisReport ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : costAnalysisReport ? (
                <>
                  {/* 费用统计 */}
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">总费用</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(costAnalysisReport.totalCostStats.totalCost)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">平均费用</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatCurrency(costAnalysisReport.totalCostStats.avgCost)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">中位数费用</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatCurrency(costAnalysisReport.totalCostStats.medianCost)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">最高费用</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatCurrency(costAnalysisReport.totalCostStats.maxCost)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">最低费用</p>
                          <p className="text-2xl font-bold text-orange-600">
                            {formatCurrency(costAnalysisReport.totalCostStats.minCost)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 费用图表 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 按类型费用分布 */}
                    <Card>
                      <CardHeader>
                        <CardTitle>按类型费用分布</CardTitle>
                        <CardDescription>不同案例类型的费用分布</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={costAnalysisReport.costByType}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ category, percentage }) => `${category}: ${percentage.toFixed(1)}%`}
                              outerRadius={80}
                              fill="hsl(var(--chart-5))"
                              dataKey="value"
                            >
                              {costAnalysisReport.costByType.map((entry: any, index: number) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* 费用区间分布 */}
                    <Card>
                      <CardHeader>
                        <CardTitle>费用区间分布</CardTitle>
                        <CardDescription>按费用区间统计案例分布</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={costAnalysisReport.costDistributionByRange}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="category" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" fill="hsl(var(--chart-5))" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* 费用趋势 */}
                    <Card className="lg:col-span-2">
                      <CardHeader>
                        <CardTitle>费用趋势</CardTitle>
                        <CardDescription>费用变化趋势分析</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={400}>
                          <LineChart data={costAnalysisReport.costTrend}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="hsl(var(--chart-5))"
                              strokeWidth={2}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 医院费用排名 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>医院费用排名</CardTitle>
                      <CardDescription>按总费用排名的医院</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {costAnalysisReport.hospitalCostRanking.map((hospital, index) => (
                          <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-full font-bold">
                                {hospital.rank}
                              </div>
                              <div>
                                <p className="font-medium">{hospital.name}</p>
                                <p className="text-sm text-muted-foreground">
                                  案例数: {formatNumber(hospital.count)}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-lg">{formatCurrency(hospital.value)}</p>
                              <p className="text-sm text-muted-foreground">
                                {hospital.percentage.toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">暂无数据</p>
                </div>
              )}
            </TabsContent>

            {/* 规则执行效果报表 */}
            <TabsContent value="rule-performance" className="space-y-6">
              {isLoading && !rulePerformanceReport ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : rulePerformanceReport ? (
                <>
                  {/* 执行统计 */}
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">总执行次数</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatNumber(rulePerformanceReport.executionStats.totalExecutions)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">成功执行</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatNumber(rulePerformanceReport.executionStats.successfulExecutions)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">失败执行</p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatNumber(rulePerformanceReport.executionStats.failedExecutions)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">成功率</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {rulePerformanceReport.executionStats.successRate.toFixed(2)}%
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <p className="text-sm font-medium text-muted-foreground">平均执行时间</p>
                          <p className="text-2xl font-bold text-orange-600">
                            {rulePerformanceReport.executionStats.avgExecutionTime.toFixed(2)}ms
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 规则图表 */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 按分类统计 */}
                    <Card>
                      <CardHeader>
                        <CardTitle>规则分类分布</CardTitle>
                        <CardDescription>按规则分类统计执行情况</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <PieChart>
                            <Pie
                              data={rulePerformanceReport.rulesByCategory}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ category, percentage }) => `${category}: ${percentage.toFixed(1)}%`}
                              outerRadius={80}
                              fill="hsl(var(--chart-5))"
                              dataKey="count"
                            >
                              {rulePerformanceReport.rulesByCategory.map((entry: any, index: number) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* 按严重程度统计 */}
                    <Card>
                      <CardHeader>
                        <CardTitle>严重程度分布</CardTitle>
                        <CardDescription>按严重程度统计规则分布</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={rulePerformanceReport.rulesBySeverity}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="category" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" fill="hsl(var(--chart-5))" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* 执行趋势 */}
                    <Card className="lg:col-span-2">
                      <CardHeader>
                        <CardTitle>执行趋势</CardTitle>
                        <CardDescription>规则执行次数变化趋势</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={400}>
                          <LineChart data={rulePerformanceReport.executionTrend}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="hsl(var(--chart-5))"
                              strokeWidth={2}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 表现最佳规则 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>表现最佳规则</CardTitle>
                      <CardDescription>按执行次数排名的规则</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {rulePerformanceReport.topPerformingRules.map((rule, index) => (
                          <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full font-bold">
                                {rule.rank}
                              </div>
                              <div>
                                <p className="font-medium">{rule.name}</p>
                                {rule.code && (
                                  <p className="text-sm text-muted-foreground">{rule.code}</p>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-lg">{formatNumber(rule.count)}</p>
                              <p className="text-sm text-muted-foreground">
                                {rule.percentage.toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">暂无数据</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
