'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  Activity, 
  Zap, 
  Database, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Server,
  Globe,
  Loader2
} from 'lucide-react'

interface ApiPerformanceStats {
  totalEndpoints: number
  totalCalls: number
  averageResponseTime: number
  slowestEndpoints: Array<{
    endpoint: string
    totalCalls: number
    averageTime: number
    minTime: number
    maxTime: number
    errorCount: number
    errorRate: number
  }>
  mostUsedEndpoints: Array<{
    endpoint: string
    totalCalls: number
    averageTime: number
  }>
  errorProneEndpoints: Array<{
    endpoint: string
    errorRate: number
    errorCount: number
    totalCalls: number
  }>
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  score: number
  metrics: {
    averageResponseTime: number
    errorRate: number
    totalEndpoints: number
    totalCalls: number
    recentCallsCount: number
  }
  recommendations: string[]
}

export default function PerformanceMonitoringPage() {
  const [stats, setStats] = useState<ApiPerformanceStats | null>(null)
  const [health, setHealth] = useState<SystemHealth | null>(null)
  const [recentCalls, setRecentCalls] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { toast } = useToast()

  // 获取性能数据
  const fetchPerformanceData = async () => {
    try {
      setIsRefreshing(true)
      const token = localStorage.getItem('access_token')
      
      if (!token) {
        toast({
          title: '错误',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      // 并行获取多种数据
      const [statsResponse, healthResponse, recentResponse] = await Promise.all([
        fetch('/api/monitoring/performance?type=stats', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/monitoring/performance?type=health', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/monitoring/performance?type=recent&limit=50', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ])

      const [statsResult, healthResult, recentResult] = await Promise.all([
        statsResponse.json(),
        healthResponse.json(),
        recentResponse.json()
      ])

      if (statsResult.success) setStats(statsResult.data)
      if (healthResult.success) setHealth(healthResult.data)
      if (recentResult.success) setRecentCalls(recentResult.data)

    } catch (error) {
      console.error('获取性能数据失败:', error)
      toast({
        title: '获取数据失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  // 格式化时间
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  // 格式化日期
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'critical': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5" />
      case 'warning': return <AlertTriangle className="h-5 w-5" />
      case 'critical': return <TrendingDown className="h-5 w-5" />
      default: return <Activity className="h-5 w-5" />
    }
  }

  // 获取HTTP状态码颜色
  const getHttpStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'text-green-600 bg-green-100'
    if (status >= 300 && status < 400) return 'text-blue-600 bg-blue-100'
    if (status >= 400 && status < 500) return 'text-yellow-600 bg-yellow-100'
    if (status >= 500) return 'text-red-600 bg-red-100'
    return 'text-gray-600 bg-gray-100'
  }

  useEffect(() => {
    fetchPerformanceData()
    
    // 每30秒自动刷新
    const interval = setInterval(fetchPerformanceData, 30000)
    return () => clearInterval(interval)
  }, [])

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN']}>
        <MainLayout>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">加载性能监控数据...</span>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">性能监控</h1>
              <p className="text-muted-foreground">
                实时监控系统API性能和健康状态
              </p>
            </div>
            <Button 
              onClick={fetchPerformanceData} 
              disabled={isRefreshing}
              variant="outline"
            >
              {isRefreshing ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              刷新数据
            </Button>
          </div>

          {/* 系统健康状态 */}
          {health && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {getStatusIcon(health.status)}
                  <span>系统健康状态</span>
                  <Badge className={getStatusColor(health.status)}>
                    {health.status === 'healthy' ? '健康' : 
                     health.status === 'warning' ? '警告' : '严重'}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  健康评分: {health.score}/100
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatTime(health.metrics.averageResponseTime)}
                    </div>
                    <div className="text-sm text-muted-foreground">平均响应时间</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {health.metrics.errorRate.toFixed(1)}%
                    </div>
                    <div className="text-sm text-muted-foreground">错误率</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {health.metrics.totalCalls}
                    </div>
                    <div className="text-sm text-muted-foreground">总调用次数</div>
                  </div>
                </div>
                
                {health.recommendations.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">优化建议:</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {health.recommendations.map((rec, index) => (
                        <li key={index} className="text-muted-foreground">{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 性能统计 */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">API端点数</p>
                      <p className="text-2xl font-bold text-blue-600">{stats.totalEndpoints}</p>
                    </div>
                    <Globe className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">总调用次数</p>
                      <p className="text-2xl font-bold text-green-600">{stats.totalCalls}</p>
                    </div>
                    <Activity className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">平均响应时间</p>
                      <p className="text-2xl font-bold text-purple-600">
                        {formatTime(stats.averageResponseTime)}
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">最慢端点</p>
                      <p className="text-2xl font-bold text-red-600">
                        {stats.slowestEndpoints.length > 0 && stats.slowestEndpoints[0]
                          ? formatTime(stats.slowestEndpoints[0].averageTime)
                          : 'N/A'
                        }
                      </p>
                    </div>
                    <TrendingDown className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 最慢的API端点 */}
          {stats && stats.slowestEndpoints.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>最慢的API端点</CardTitle>
                <CardDescription>
                  需要优化的API端点列表
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>端点</TableHead>
                      <TableHead>调用次数</TableHead>
                      <TableHead>平均时间</TableHead>
                      <TableHead>最小时间</TableHead>
                      <TableHead>最大时间</TableHead>
                      <TableHead>错误率</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {stats.slowestEndpoints.slice(0, 10).map((endpoint, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-sm">
                          {endpoint.endpoint}
                        </TableCell>
                        <TableCell>{endpoint.totalCalls}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-red-600">
                            {formatTime(endpoint.averageTime)}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatTime(endpoint.minTime)}</TableCell>
                        <TableCell>{formatTime(endpoint.maxTime)}</TableCell>
                        <TableCell>
                          <Badge 
                            className={endpoint.errorRate > 5 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}
                          >
                            {endpoint.errorRate.toFixed(1)}%
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* 最近的API调用 */}
          {recentCalls.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>最近的API调用</CardTitle>
                <CardDescription>
                  最近50次API调用记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>时间</TableHead>
                      <TableHead>方法</TableHead>
                      <TableHead>端点</TableHead>
                      <TableHead>状态码</TableHead>
                      <TableHead>响应时间</TableHead>
                      <TableHead>IP地址</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentCalls.slice(0, 20).map((call, index) => (
                      <TableRow key={index}>
                        <TableCell className="text-sm">
                          {formatDate(call.timestamp)}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{call.method}</Badge>
                        </TableCell>
                        <TableCell className="font-mono text-sm max-w-xs truncate">
                          {call.endpoint}
                        </TableCell>
                        <TableCell>
                          <Badge className={getHttpStatusColor(call.statusCode)}>
                            {call.statusCode}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline"
                            className={call.responseTime > 2000 ? 'text-red-600' : 
                                     call.responseTime > 1000 ? 'text-yellow-600' : 'text-green-600'}
                          >
                            {formatTime(call.responseTime)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">{call.ip}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
