"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Activity, Server, Database, Users, AlertTriangle, CheckCircle, Clock, TrendingUp } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

// 模拟监控数据
const systemMetrics = {
  cpu: { usage: 45, status: 'normal' },
  memory: { usage: 67, status: 'warning' },
  disk: { usage: 23, status: 'normal' },
  network: { usage: 34, status: 'normal' }
}

const serviceStatus = [
  { name: 'Web服务', status: 'running', uptime: '99.9%', responseTime: '120ms' },
  { name: '数据库', status: 'running', uptime: '99.8%', responseTime: '45ms' },
  { name: 'API网关', status: 'running', uptime: '99.9%', responseTime: '89ms' },
  { name: '缓存服务', status: 'warning', uptime: '98.5%', responseTime: '234ms' },
  { name: '文件服务', status: 'running', uptime: '99.7%', responseTime: '156ms' }
]

const recentAlerts = [
  {
    id: 1,
    type: 'warning',
    message: '内存使用率超过65%',
    time: '2分钟前',
    service: '应用服务器'
  },
  {
    id: 2,
    type: 'info',
    message: '数据库连接池已优化',
    time: '15分钟前',
    service: '数据库'
  },
  {
    id: 3,
    type: 'error',
    message: '缓存服务响应时间异常',
    time: '1小时前',
    service: '缓存服务'
  }
]

const performanceMetrics = [
  { name: '平均响应时间', value: '145ms', trend: 'up', change: '+5%' },
  { name: '并发用户数', value: '234', trend: 'up', change: '+12%' },
  { name: '错误率', value: '0.02%', trend: 'down', change: '-15%' },
  { name: '吞吐量', value: '1.2K/s', trend: 'up', change: '+8%' }
]

export default function MonitoringPage() {
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-600'
      case 'warning':
        return 'text-yellow-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getAlertBadgeVariant = (type: string) => {
    switch (type) {
      case 'error':
        return 'destructive'
      case 'warning':
        return 'secondary'
      case 'info':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">实时监控</h1>
          <p className="text-muted-foreground">
            系统性能和服务状态实时监控 - 最后更新: {currentTime.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/monitoring/performance">
            <Button variant="outline">
              <TrendingUp className="mr-2 h-4 w-4" />
              性能分析
            </Button>
          </Link>
          <Button>
            <Activity className="mr-2 h-4 w-4" />
            实时日志
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">系统概览</TabsTrigger>
          <TabsTrigger value="services">服务状态</TabsTrigger>
          <TabsTrigger value="performance">性能指标</TabsTrigger>
          <TabsTrigger value="alerts">告警中心</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* 系统资源使用情况 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CPU使用率</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.cpu.usage}%</div>
                <Progress value={systemMetrics.cpu.usage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  状态: <span className="text-green-600">正常</span>
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">内存使用率</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.memory.usage}%</div>
                <Progress value={systemMetrics.memory.usage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  状态: <span className="text-yellow-600">警告</span>
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">磁盘使用率</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.disk.usage}%</div>
                <Progress value={systemMetrics.disk.usage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  状态: <span className="text-green-600">正常</span>
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">网络使用率</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.network.usage}%</div>
                <Progress value={systemMetrics.network.usage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  状态: <span className="text-green-600">正常</span>
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 快速状态概览 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>服务状态概览</CardTitle>
                <CardDescription>核心服务运行状态</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {serviceStatus.slice(0, 3).map((service) => (
                    <div key={service.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(service.status)}
                        <span className="font-medium">{service.name}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {service.responseTime}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>最新告警</CardTitle>
                <CardDescription>系统告警和通知</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentAlerts.slice(0, 3).map((alert) => (
                    <div key={alert.id} className="flex items-start gap-3">
                      <Badge variant={getAlertBadgeVariant(alert.type)} className="mt-0.5">
                        {alert.type}
                      </Badge>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{alert.message}</p>
                        <p className="text-xs text-muted-foreground">
                          {alert.service} • {alert.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          {/* 服务状态详情 */}
          <div className="space-y-4">
            {serviceStatus.map((service) => (
              <Card key={service.name}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(service.status)}
                      <div>
                        <h3 className="font-semibold">{service.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          运行时间: {service.uptime}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">响应时间</div>
                      <div className="text-lg font-bold">{service.responseTime}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* 性能指标 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {performanceMetrics.map((metric) => (
              <Card key={metric.name}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
                  <TrendingUp className={`h-4 w-4 ${
                    metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metric.value}</div>
                  <p className={`text-xs ${
                    metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  } mt-2`}>
                    {metric.change} 相比昨天
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          {/* 告警详情 */}
          <div className="space-y-4">
            {recentAlerts.map((alert) => (
              <Card key={alert.id}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <Badge variant={getAlertBadgeVariant(alert.type)}>
                        {alert.type}
                      </Badge>
                      <div>
                        <h3 className="font-semibold">{alert.message}</h3>
                        <p className="text-sm text-muted-foreground">
                          服务: {alert.service} • 时间: {alert.time}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      处理
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
