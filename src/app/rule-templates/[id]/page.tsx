'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  ArrowLeft,
  Edit,
  Copy,
  Star,
  Award,
  Users,
  TrendingUp,
  Clock,
  Code,
  Database,
  Zap,
  Play,
  Download,
  Share,
  Heart,
  MessageSquare,
  Eye,
  Calendar,
  Tag,
  Settings
} from 'lucide-react'

interface RuleTemplate {
  id: number
  templateCode: string
  templateName: string
  templateDesc?: string
  categoryId: number
  templateType: 'SQL' | 'DSL' | 'JAVASCRIPT'
  ruleCategory: string
  severityLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  priorityLevel: number
  templateContent: string
  templateSql?: string
  templateDsl?: string
  parameterSchema?: any
  usageCount: number
  successRate: number
  lastUsedAt?: string
  tags?: string[]
  previewData?: any
  isOfficial: boolean
  isFeatured: boolean
  versionNumber: string
  createdAt: string
  updatedAt: string
  category?: {
    id: number
    categoryName: string
    iconName?: string
    colorScheme?: string
  }
  averageRating: number
  ratingCount: number
}

export default function TemplateDetailPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const [template, setTemplate] = useState<RuleTemplate | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  const templateId = params['id'] as string

  // 获取模板详情
  const fetchTemplate = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const response = await fetch(`/api/rule-templates/${templateId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setTemplate(result.data)
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取模板详情失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取模板详情失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (templateId) {
      fetchTemplate()
    }
  }, [templateId])

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SQL':
        return <Database className="h-5 w-5" />
      case 'DSL':
        return <Code className="h-5 w-5" />
      case 'JAVASCRIPT':
        return <Zap className="h-5 w-5" />
      default:
        return <Code className="h-5 w-5" />
    }
  }

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 使用模板创建规则
  const handleUseTemplate = () => {
    router.push(`/supervision-rules/create?templateId=${templateId}`)
  }

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR']}>
        <MainLayout>
          <div className="space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
              <div className="h-4 bg-muted rounded w-1/2 mb-8"></div>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                  <Card>
                    <CardHeader>
                      <div className="h-6 bg-muted rounded w-3/4"></div>
                      <div className="h-4 bg-muted rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded"></div>
                        <div className="h-4 bg-muted rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <div className="h-6 bg-muted rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded"></div>
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  if (!template) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR']}>
        <MainLayout>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <h3 className="text-lg font-medium text-foreground mb-2">模板不存在</h3>
              <p className="text-muted-foreground mb-4">
                请检查模板ID是否正确
              </p>
              <Button onClick={() => router.push('/rule-templates')}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回模板库
              </Button>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 面包屑导航 */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/rule-templates')}
              className="p-0 h-auto font-normal"
            >
              模板库
            </Button>
            <span>/</span>
            <span className="text-foreground">{template.templateName}</span>
          </div>

          {/* 页面标题 */}
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                {getTypeIcon(template.templateType)}
                <h1 className="text-3xl font-bold text-foreground">{template.templateName}</h1>
                <div className="flex items-center space-x-2">
                  {template.isOfficial && (
                    <Badge variant="secondary" className="text-xs">
                      <Award className="h-3 w-3 mr-1" />
                      官方
                    </Badge>
                  )}
                  {template.isFeatured && (
                    <Badge variant="default" className="text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      推荐
                    </Badge>
                  )}
                  <Badge variant="outline" className={getSeverityColor(template.severityLevel)}>
                    {template.severityLevel}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <span>编码: {template.templateCode}</span>
                <span>版本: {template.versionNumber}</span>
                <span>分类: {template.category?.categoryName}</span>
              </div>
              {template.templateDesc && (
                <p className="text-muted-foreground max-w-2xl">{template.templateDesc}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Share className="mr-2 h-4 w-4" />
                分享
              </Button>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
              <Button variant="outline" size="sm" onClick={() => router.push(`/rule-templates/${template.id}/edit`)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
              <Button onClick={handleUseTemplate}>
                <Copy className="mr-2 h-4 w-4" />
                使用模板
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 主要内容 */}
            <div className="lg:col-span-2 space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">概览</TabsTrigger>
                  <TabsTrigger value="content">内容</TabsTrigger>
                  <TabsTrigger value="parameters">参数</TabsTrigger>
                  <TabsTrigger value="preview">预览</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>模板信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">模板类型</label>
                          <div className="flex items-center space-x-2 mt-1">
                            {getTypeIcon(template.templateType)}
                            <span>{template.templateType}</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">规则分类</label>
                          <p className="mt-1">{template.ruleCategory}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">优先级</label>
                          <p className="mt-1">{template.priorityLevel}/10</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">创建时间</label>
                          <p className="mt-1">{new Date(template.createdAt).toLocaleString()}</p>
                        </div>
                      </div>

                      {template.tags && template.tags.length > 0 && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">标签</label>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {template.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="content" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>模板内容</CardTitle>
                      <CardDescription>
                        {template.templateType} 类型的规则模板内容
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-muted rounded-lg p-4">
                        <pre className="text-sm overflow-x-auto">
                          <code>{template.templateContent}</code>
                        </pre>
                      </div>
                    </CardContent>
                  </Card>

                  {template.templateSql && (
                    <Card>
                      <CardHeader>
                        <CardTitle>SQL 内容</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="bg-muted rounded-lg p-4">
                          <pre className="text-sm overflow-x-auto">
                            <code>{template.templateSql}</code>
                          </pre>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {template.templateDsl && (
                    <Card>
                      <CardHeader>
                        <CardTitle>DSL 内容</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="bg-muted rounded-lg p-4">
                          <pre className="text-sm overflow-x-auto">
                            <code>{template.templateDsl}</code>
                          </pre>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="parameters" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>参数配置</CardTitle>
                      <CardDescription>
                        使用此模板时需要配置的参数
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {template.parameterSchema && template.parameterSchema.parameters ? (
                        <div className="space-y-4">
                          {template.parameterSchema.parameters.map((param: any, index: number) => (
                            <div key={index} className="border rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium">{param.displayName || param.name}</h4>
                                <div className="flex items-center space-x-2">
                                  <Badge variant="outline" className="text-xs">
                                    {param.type}
                                  </Badge>
                                  {param.required && (
                                    <Badge variant="destructive" className="text-xs">
                                      必填
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              {param.description && (
                                <p className="text-sm text-muted-foreground mb-2">{param.description}</p>
                              )}
                              {param.defaultValue !== undefined && (
                                <p className="text-sm">
                                  <span className="font-medium">默认值:</span> {String(param.defaultValue)}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted-foreground">此模板无需配置参数</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="preview" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>预览效果</CardTitle>
                      <CardDescription>
                        模板执行的预期效果和示例结果
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {template.previewData ? (
                        <div className="space-y-4">
                          {template.previewData.sampleOutput && (
                            <div>
                              <h4 className="font-medium mb-2">预期输出</h4>
                              <div className="bg-muted rounded-lg p-4">
                                <pre className="text-sm">
                                  {JSON.stringify(template.previewData.sampleOutput, null, 2)}
                                </pre>
                              </div>
                            </div>
                          )}
                          {template.previewData.documentation && (
                            <div>
                              <h4 className="font-medium mb-2">使用说明</h4>
                              <div className="prose prose-sm max-w-none">
                                <p>{template.previewData.documentation.usage}</p>
                                {template.previewData.documentation.examples && (
                                  <div>
                                    <h5 className="font-medium">示例:</h5>
                                    <ul>
                                      {template.previewData.documentation.examples.map((example: string, index: number) => (
                                        <li key={index}>{example}</li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-muted-foreground">暂无预览数据</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* 侧边栏 */}
            <div className="space-y-6">
              {/* 统计信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>使用统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">使用次数</span>
                    </div>
                    <span className="font-medium">{template.usageCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">成功率</span>
                    </div>
                    <span className="font-medium">{template.successRate.toFixed(1)}%</span>
                  </div>
                  {template.averageRating > 0 && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Star className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">平均评分</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">{template.averageRating.toFixed(1)}</span>
                        <span className="text-xs text-muted-foreground">({template.ratingCount})</span>
                      </div>
                    </div>
                  )}
                  {template.lastUsedAt && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">最后使用</span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {new Date(template.lastUsedAt).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 快速操作 */}
              <Card>
                <CardHeader>
                  <CardTitle>快速操作</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button className="w-full" onClick={handleUseTemplate}>
                    <Copy className="mr-2 h-4 w-4" />
                    使用此模板
                  </Button>
                  <Button variant="outline" className="w-full" onClick={() => router.push(`/rule-templates/test?templateId=${template.id}`)}>
                    <Play className="mr-2 h-4 w-4" />
                    测试模板
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Heart className="mr-2 h-4 w-4" />
                    收藏模板
                  </Button>
                  <Button variant="outline" className="w-full">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    评价反馈
                  </Button>
                </CardContent>
              </Card>

              {/* 相关信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>相关信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div>
                    <span className="text-muted-foreground">创建者:</span>
                    <span className="ml-2">系统管理员</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">更新时间:</span>
                    <span className="ml-2">{new Date(template.updatedAt).toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">模板ID:</span>
                    <span className="ml-2 font-mono">{template.id}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
