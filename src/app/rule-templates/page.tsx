'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  Search, 
  Plus, 
  Filter, 
  Star,
  Eye,
  Edit,
  Copy,
  Trash2,
  Download,
  Upload,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Award,
  Clock,
  Users,
  TrendingUp,
  Code,
  Database,
  Zap
} from 'lucide-react'

interface RuleTemplate {
  id: number
  templateCode: string
  templateName: string
  templateDesc?: string
  categoryId: number
  templateType: 'SQL' | 'DSL' | 'JAVASCRIPT'
  ruleCategory: string
  severityLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  priorityLevel: number
  usageCount: number
  successRate: number
  lastUsedAt?: string
  tags?: string[]
  isOfficial: boolean
  isFeatured: boolean
  versionNumber: string
  createdAt: string
  category?: {
    id: number
    categoryName: string
    iconName?: string
    colorScheme?: string
  }
  averageRating: number
  ratingCount: number
}

interface TemplateCategory {
  id: number
  categoryCode: string
  categoryName: string
  iconName?: string
  colorScheme?: string
  children?: TemplateCategory[]
}

export default function RuleTemplatesPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [templates, setTemplates] = useState<RuleTemplate[]>([])
  const [categories, setCategories] = useState<TemplateCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('usageCount')
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC')
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0,
  })

  // 获取模板列表
  const fetchTemplates = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          variant: "destructive",
          title: "错误",
          description: "请先登录",
        })
        return
      }

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        search: searchTerm,
        sortBy,
        sortOrder,
        ...(selectedCategory !== 'all' && { categoryId: selectedCategory }),
        ...(selectedType !== 'all' && { templateType: selectedType }),
        ...(selectedSeverity !== 'all' && { severityLevel: selectedSeverity }),
      })

      const response = await fetch(`/api/rule-templates?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setTemplates(result.data.data)
        setPagination(prev => ({
          ...prev,
          total: result.data.pagination.total,
          totalPages: result.data.pagination.totalPages,
        }))
      } else {
        toast({
          variant: "destructive",
          title: "错误",
          description: result.message,
        })
      }
    } catch (error) {
      console.error('获取模板列表失败:', error)
      toast({
        variant: "destructive",
        title: "错误",
        description: "获取模板列表失败",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) return

      const response = await fetch('/api/rule-templates/categories', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setCategories(result.data.flatList)
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  useEffect(() => {
    fetchTemplates()
    fetchCategories()
  }, [pagination.page, searchTerm, selectedCategory, selectedType, selectedSeverity, sortBy, sortOrder])

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SQL':
        return <Database className="h-4 w-4" />
      case 'DSL':
        return <Code className="h-4 w-4" />
      case 'JAVASCRIPT':
        return <Zap className="h-4 w-4" />
      default:
        return <Code className="h-4 w-4" />
    }
  }

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 格式化使用次数
  const formatUsageCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`
    }
    return count.toString()
  }

  // 渲染模板卡片
  const renderTemplateCard = (template: RuleTemplate) => (
    <Card key={template.id} className="group hover:shadow-lg transition-all duration-200 border-border/50 hover:border-border">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            {getTypeIcon(template.templateType)}
            <div>
              <CardTitle className="text-base font-semibold text-foreground group-hover:text-primary transition-colors">
                {template.templateName}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {template.templateCode}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {template.isOfficial && (
              <Badge variant="secondary" className="text-xs">
                <Award className="h-3 w-3 mr-1" />
                官方
              </Badge>
            )}
            {template.isFeatured && (
              <Badge variant="default" className="text-xs">
                <Star className="h-3 w-3 mr-1" />
                推荐
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* 描述 */}
          {template.templateDesc && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {template.templateDesc}
            </p>
          )}

          {/* 标签和分类 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={getSeverityColor(template.severityLevel)}>
                {template.severityLevel}
              </Badge>
              {template.category && (
                <Badge variant="outline" className="text-xs">
                  {template.category.categoryName}
                </Badge>
              )}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Users className="h-3 w-3" />
                <span>{formatUsageCount(template.usageCount)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <TrendingUp className="h-3 w-3" />
                <span>{template.successRate.toFixed(1)}%</span>
              </div>
              {template.averageRating > 0 && (
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3 fill-current text-yellow-400" />
                  <span>{template.averageRating.toFixed(1)}</span>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{new Date(template.createdAt).toLocaleDateString()}</span>
            </div>
          </div>

          {/* 标签 */}
          {template.tags && template.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {template.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-2 border-t border-border/50">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/rule-templates/${template.id}`)}
              >
                <Eye className="h-4 w-4 mr-1" />
                查看
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/rule-templates/${template.id}/edit`)}
              >
                <Edit className="h-4 w-4 mr-1" />
                编辑
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/supervision-rules/create?templateId=${template.id}`)}
              >
                <Copy className="h-4 w-4 mr-1" />
                使用
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">规则模板库</h1>
              <p className="text-muted-foreground">管理和使用预置的监管规则模板</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push('/rule-templates/import')}
              >
                <Upload className="mr-2 h-4 w-4" />
                导入
              </Button>
              <Button
                variant="outline"
                onClick={() => {/* 导出功能 */}}
              >
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
              <Button onClick={() => router.push('/rule-templates/create')}>
                <Plus className="mr-2 h-4 w-4" />
                创建模板
              </Button>
            </div>
          </div>

          {/* 搜索和筛选 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col space-y-4">
                {/* 搜索栏 */}
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="搜索模板名称、编码、描述或标签..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* 筛选器 */}
                <div className="flex items-center space-x-4">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部分类</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="SQL">SQL</SelectItem>
                      <SelectItem value="DSL">DSL</SelectItem>
                      <SelectItem value="JAVASCRIPT">JavaScript</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="严重程度" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部程度</SelectItem>
                      <SelectItem value="LOW">低</SelectItem>
                      <SelectItem value="MEDIUM">中</SelectItem>
                      <SelectItem value="HIGH">高</SelectItem>
                      <SelectItem value="CRITICAL">严重</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="排序" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="usageCount">使用次数</SelectItem>
                      <SelectItem value="successRate">成功率</SelectItem>
                      <SelectItem value="averageRating">评分</SelectItem>
                      <SelectItem value="createdAt">创建时间</SelectItem>
                      <SelectItem value="updatedAt">更新时间</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSortOrder(sortOrder === 'ASC' ? 'DESC' : 'ASC')}
                  >
                    {sortOrder === 'ASC' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 模板列表 */}
          <div className="space-y-4">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, index) => (
                  <Card key={index} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-muted rounded"></div>
                        <div className="h-3 bg-muted rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : templates.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-12">
                    <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
                      <Search className="h-full w-full" />
                    </div>
                    <h3 className="text-lg font-medium text-foreground mb-2">未找到模板</h3>
                    <p className="text-muted-foreground mb-4">
                      尝试调整搜索条件或创建新的模板
                    </p>
                    <Button onClick={() => router.push('/rule-templates/create')}>
                      <Plus className="mr-2 h-4 w-4" />
                      创建模板
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className={viewMode === 'grid' 
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                  : "space-y-4"
                }>
                  {templates.map(renderTemplateCard)}
                </div>

                {/* 分页 */}
                {pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      显示第 {((pagination.page - 1) * pagination.pageSize) + 1} - {Math.min(pagination.page * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                        disabled={pagination.page <= 1}
                      >
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
