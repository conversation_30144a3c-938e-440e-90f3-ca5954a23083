'use client'

/**
 * 系统配置管理页面
 * 
 * @description 提供系统配置的可视化管理界面
 * @features 配置分类管理、配置项管理、变更历史、实时预览
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Settings, 
  Database, 
  History, 
  Download, 
  Upload, 
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

// 组件导入
import { ConfigCategoriesTab } from '@/components/system-config/config-categories-tab'
import { ConfigItemsTab } from '@/components/system-config/config-items-tab'
import { ConfigHistoryTab } from '@/components/system-config/config-history-tab'
import { ConfigPreviewTab } from '@/components/system-config/config-preview-tab'

interface SystemStats {
  totalCategories: number
  totalItems: number
  activeItems: number
  lastUpdated: string
}

export default function SystemConfigPage() {
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('categories')

  /**
   * 获取系统配置统计信息
   */
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/system-config/stats')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setStats(data.data)
        }
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 刷新配置缓存
   */
  const refreshCache = async () => {
    try {
      const response = await fetch('/api/system-config/dynamic', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          toast.success('配置缓存刷新成功')
          await fetchStats()
        } else {
          toast.error('配置缓存刷新失败')
        }
      }
    } catch (error) {
      toast.error('刷新缓存时发生错误')
    }
  }

  /**
   * 导出配置
   */
  const exportConfig = async () => {
    try {
      const response = await fetch('/api/system-config/export')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `system-config-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('配置导出成功')
      } else {
        toast.error('配置导出失败')
      }
    } catch (error) {
      toast.error('导出配置时发生错误')
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题和操作栏 */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Settings className="h-8 w-8" />
            系统配置管理
          </h1>
          <p className="text-muted-foreground">
            管理系统的动态配置项，支持实时更新和历史追踪
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshCache}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            刷新缓存
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={exportConfig}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            导出配置
          </Button>
        </div>
      </div>

      {/* 统计信息卡片 */}
      {!isLoading && stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">配置分类</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCategories}</div>
              <p className="text-xs text-muted-foreground">
                系统配置分类总数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">配置项总数</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalItems}</div>
              <p className="text-xs text-muted-foreground">
                所有配置项数量
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">启用配置项</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.activeItems}</div>
              <p className="text-xs text-muted-foreground">
                当前生效的配置项
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最后更新</CardTitle>
              <History className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium">{stats.lastUpdated}</div>
              <p className="text-xs text-muted-foreground">
                配置最后修改时间
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 重要提示 */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>重要提示：</strong>
          修改系统配置会影响所有API的验证规则。请谨慎操作，建议在非业务高峰期进行配置变更。
          配置变更后会自动刷新缓存，无需重启服务。
        </AlertDescription>
      </Alert>

      {/* 主要内容标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            配置分类
          </TabsTrigger>
          <TabsTrigger value="items" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            配置项管理
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            变更历史
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            配置预览
          </TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="space-y-4">
          <ConfigCategoriesTab onStatsChange={fetchStats} />
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <ConfigItemsTab onStatsChange={fetchStats} />
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <ConfigHistoryTab />
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <ConfigPreviewTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}
