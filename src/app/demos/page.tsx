import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Layout, Palette } from "lucide-react"

export default function DemosPage() {
  const demos = [
    {
      title: "🌟 shadcn/UI Components Showcase",
      description: "🔥 Complete shadcn/UI showcase with sidebar navigation, theme switching, and all components in one page - exactly like the official website!",
      href: "/showcase",
      icon: <Palette className="h-5 w-5" />,
      features: ["Sidebar Navigation", "Theme Switching", "All Components", "Official Design", "Mobile Responsive", "Smooth Scrolling", "Preview/Code Tabs", "Copy Code"],
      isRecommended: true,
      isUltimate: true
    }
  ]

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">shadcn/UI Components Showcase</h1>
          <p className="text-muted-foreground text-xl max-w-3xl mx-auto">
            Complete demonstration of shadcn/UI components following official design guidelines and best practices.
            Experience all components in one beautifully designed showcase.
          </p>
        </div>

        {/* Demo 卡片 */}
        <div className="flex justify-center">
          <div className="w-full max-w-2xl">
            {demos.map((demo, index) => (
              <Card key={index} className="relative ring-2 ring-primary shadow-lg">
                <div className="absolute -top-3 -right-3 flex gap-2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg">
                    🔥 ULTIMATE
                  </Badge>
                  <Badge className="bg-primary text-primary-foreground shadow-lg">
                    ⭐ RECOMMENDED
                  </Badge>
                </div>

                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {demo.icon}
                    </div>
                    <div>
                      <CardTitle className="text-xl text-primary">{demo.title}</CardTitle>
                      <CardDescription className="text-base mt-1">
                        {demo.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* 功能特性 */}
                  <div>
                    <h4 className="text-sm font-semibold mb-3 text-muted-foreground">Features</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {demo.features.map((feature, idx) => (
                        <Badge key={idx} variant="default" className="text-xs justify-start">
                          ✓ {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* 查看按钮 */}
                  <Link href={demo.href}>
                    <Button className="w-full h-12 text-lg" size="lg">
                      🚀 Launch Showcase
                      <ExternalLink className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 设计原则说明 */}
        <Card>
          <CardHeader>
            <CardTitle>设计原则</CardTitle>
            <CardDescription>
              本演示严格遵循 shadcn/UI 官方设计原则和最佳实践
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-semibold">Open Code</h4>
                <p className="text-sm text-muted-foreground">
                  所有组件代码完全可见和可修改，不依赖黑盒组件库
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Composition</h4>
                <p className="text-sm text-muted-foreground">
                  使用统一的可组合接口，保持 API 的一致性和可预测性
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Beautiful Defaults</h4>
                <p className="text-sm text-muted-foreground">
                  使用精心设计的默认样式，组件开箱即用，视觉效果统一
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 技术栈信息 */}
        <Card>
          <CardHeader>
            <CardTitle>技术栈</CardTitle>
            <CardDescription>
              演示使用的核心技术和工具
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                "Next.js 15",
                "React 18", 
                "TypeScript 5",
                "shadcn/UI",
                "Tailwind CSS",
                "Radix UI",
                "React Hook Form",
                "Zod",
                "Lucide Icons"
              ].map((tech, index) => (
                <Badge key={index} variant="outline">
                  {tech}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
