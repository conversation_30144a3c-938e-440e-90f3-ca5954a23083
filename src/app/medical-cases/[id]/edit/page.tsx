"use client"

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { businessToast } from '@/lib/toast'
import { MedicalCase } from '@/types/medical-case'
import { validateFormData, fieldValidators, combineValidators } from '@/lib/form-utils'
import {
  ArrowLeft,
  Save,
  Loader2,
  User,
  Hospital,
  FileText,
  Calendar
} from 'lucide-react'

export default function MedicalCaseEditPage() {
  const router = useRouter()
  const params = useParams()
  const caseId = params['id'] as string
  
  const [medicalCase, setMedicalCase] = useState<MedicalCase | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    caseNumber: '',
    patientName: '',
    patientIdCard: '',
    patientPhone: '',
    patientAge: '',
    patientGender: '',
    caseType: '',
    medicalCategory: '',
    hospitalName: '',
    hospitalCode: '',
    department: '',
    doctorName: '',
    admissionDate: '',
    dischargeDate: '',
    totalCost: '',
    description: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 获取案例详情
  const fetchCaseDetail = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        router.push('/login')
        return
      }

      const response = await fetch(`/api/medical-cases/${caseId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        const caseData = result.data
        setMedicalCase(caseData)
        
        // 填充表单数据
        setFormData({
          caseNumber: caseData.caseNumber || '',
          patientName: caseData.patientName || '',
          patientIdCard: caseData.patientIdCard || '',
          patientPhone: caseData.patientPhone || '',
          patientAge: caseData.patientAge?.toString() || '',
          patientGender: caseData.patientGender || '',
          caseType: caseData.caseType || '',
          medicalCategory: caseData.medicalCategory || '',
          hospitalName: caseData.hospitalName || '',
          hospitalCode: caseData.hospitalCode || '',
          department: caseData.department || '',
          doctorName: caseData.doctorName || '',
          admissionDate: caseData.admissionDate ? new Date(caseData.admissionDate).toISOString().split('T')[0] || '' : '',
          dischargeDate: caseData.dischargeDate ? new Date(caseData.dischargeDate).toISOString().split('T')[0] || '' : '',
          totalCost: caseData.totalCost?.toString() || '',
          description: caseData.description || ''
        })
      } else {
        businessToast.saveError('案例详情', result.message)
        router.push('/medical-cases')
      }
    } catch (error) {
      console.error('获取案例详情失败:', error)
      businessToast.networkError()
      router.push('/medical-cases')
    } finally {
      setIsLoading(false)
    }
  }

  // 表单验证 - 使用统一的验证工具
  const validateForm = () => {
    const validators = {
      caseNumber: fieldValidators.required('案例编号'),
      patientName: fieldValidators.required('患者姓名'),
      patientIdCard: combineValidators(
        fieldValidators.required('身份证号'),
        fieldValidators.idCard
      ),
      patientPhone: fieldValidators.phoneNumber,
      patientAge: fieldValidators.numberRange(0, 150, '年龄'),
      patientGender: () => null, // 可选字段
      caseType: fieldValidators.required('案例类型'),
      medicalCategory: fieldValidators.required('医疗类别'),
      hospitalName: fieldValidators.required('医院名称'),
      hospitalCode: () => null, // 可选字段
      department: () => null, // 可选字段
      doctorName: () => null, // 可选字段
      admissionDate: () => null, // 可选字段
      dischargeDate: () => null, // 可选字段
      totalCost: fieldValidators.positiveNumber('总费用'),
      description: () => null, // 可选字段
    }

    // 直接使用验证器
    const result = validateFormData(formData, validators)

    // 自定义日期验证
    const customErrors = { ...result.errors }
    if (formData.admissionDate && formData.dischargeDate) {
      const admission = new Date(formData.admissionDate)
      const discharge = new Date(formData.dischargeDate)
      if (discharge < admission) {
        customErrors.dischargeDate = '出院日期不能早于入院日期'
      }
    }

    setErrors(customErrors)
    return Object.keys(customErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      businessToast.validationError('请检查并修正表单中的错误')
      return
    }

    setIsSaving(true)
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        return
      }

      const updateData = {
        caseNumber: formData.caseNumber.trim(),
        patientName: formData.patientName.trim(),
        patientIdCard: formData.patientIdCard.trim(),
        patientPhone: formData.patientPhone.trim() || null,
        patientAge: formData.patientAge ? parseInt(formData.patientAge) : null,
        patientGender: formData.patientGender || null,
        caseType: formData.caseType,
        medicalCategory: formData.medicalCategory.trim(),
        hospitalName: formData.hospitalName.trim(),
        hospitalCode: formData.hospitalCode.trim() || null,
        department: formData.department.trim() || null,
        doctorName: formData.doctorName.trim() || null,
        admissionDate: formData.admissionDate || null,
        dischargeDate: formData.dischargeDate || null,
        totalCost: formData.totalCost ? parseFloat(formData.totalCost) : null,
        description: formData.description.trim() || null
      }

      const response = await fetch(`/api/medical-cases/${caseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      })

      const result = await response.json()

      if (result.success) {
        businessToast.updateSuccess('案例')
        router.push(`/medical-cases/${caseId}`)
      } else {
        businessToast.updateError('案例', result.message)
      }
    } catch (error) {
      console.error('更新案例失败:', error)
      businessToast.updateError('案例')
    } finally {
      setIsSaving(false)
    }
  }

  // 处理表单字段变化
  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  useEffect(() => {
    if (caseId) {
      fetchCaseDetail()
    }
  }, [caseId])

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
        <MainLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="text-lg">加载案例信息中...</span>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  if (!medicalCase) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
        <MainLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-muted-foreground mb-2">案例不存在</h2>
              <p className="text-muted-foreground mb-4">请检查案例ID是否正确</p>
              <Button onClick={() => router.push('/medical-cases')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回案例列表
              </Button>
            </div>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'OPERATOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题和操作 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={() => router.push(`/medical-cases/${caseId}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回详情
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-foreground">编辑案例</h1>
                <p className="text-muted-foreground">案例编号：{medicalCase.caseNumber}</p>
              </div>
            </div>
          </div>

          {/* 编辑表单 */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 患者信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  患者信息
                </CardTitle>
                <CardDescription>
                  编辑患者的基本信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="patientName">患者姓名 *</Label>
                    <Input
                      id="patientName"
                      value={formData.patientName}
                      onChange={(e) => handleFieldChange('patientName', e.target.value)}
                      placeholder="请输入患者姓名"
                      className={errors['patientName'] ? 'border-red-500' : ''}
                    />
                    {errors['patientName'] && (
                      <p className="text-sm text-red-500">{errors['patientName']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="patientIdCard">身份证号 *</Label>
                    <Input
                      id="patientIdCard"
                      value={formData.patientIdCard}
                      onChange={(e) => handleFieldChange('patientIdCard', e.target.value)}
                      placeholder="请输入身份证号"
                      className={errors['patientIdCard'] ? 'border-red-500' : ''}
                    />
                    {errors['patientIdCard'] && (
                      <p className="text-sm text-red-500">{errors['patientIdCard']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="patientPhone">联系电话</Label>
                    <Input
                      id="patientPhone"
                      value={formData.patientPhone}
                      onChange={(e) => handleFieldChange('patientPhone', e.target.value)}
                      placeholder="请输入联系电话"
                      className={errors['patientPhone'] ? 'border-red-500' : ''}
                    />
                    {errors['patientPhone'] && (
                      <p className="text-sm text-red-500">{errors['patientPhone']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="patientAge">年龄</Label>
                    <Input
                      id="patientAge"
                      type="number"
                      value={formData.patientAge}
                      onChange={(e) => handleFieldChange('patientAge', e.target.value)}
                      placeholder="请输入年龄"
                      min="0"
                      max="150"
                      className={errors['patientAge'] ? 'border-red-500' : ''}
                    />
                    {errors['patientAge'] && (
                      <p className="text-sm text-red-500">{errors['patientAge']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="patientGender">性别</Label>
                    <Select value={formData.patientGender} onValueChange={(value) => handleFieldChange('patientGender', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择性别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MALE">男</SelectItem>
                        <SelectItem value="FEMALE">女</SelectItem>
                        <SelectItem value="OTHER">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 医院信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Hospital className="h-5 w-5 mr-2" />
                  医院信息
                </CardTitle>
                <CardDescription>
                  编辑医院和医生相关信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="hospitalName">医院名称 *</Label>
                    <Input
                      id="hospitalName"
                      value={formData.hospitalName}
                      onChange={(e) => handleFieldChange('hospitalName', e.target.value)}
                      placeholder="请输入医院名称"
                      className={errors['hospitalName'] ? 'border-red-500' : ''}
                    />
                    {errors['hospitalName'] && (
                      <p className="text-sm text-red-500">{errors['hospitalName']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="hospitalCode">医院编码</Label>
                    <Input
                      id="hospitalCode"
                      value={formData.hospitalCode}
                      onChange={(e) => handleFieldChange('hospitalCode', e.target.value)}
                      placeholder="请输入医院编码"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="department">科室</Label>
                    <Input
                      id="department"
                      value={formData.department}
                      onChange={(e) => handleFieldChange('department', e.target.value)}
                      placeholder="请输入科室"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="doctorName">主治医生</Label>
                    <Input
                      id="doctorName"
                      value={formData.doctorName}
                      onChange={(e) => handleFieldChange('doctorName', e.target.value)}
                      placeholder="请输入主治医生"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 案例信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  案例信息
                </CardTitle>
                <CardDescription>
                  编辑案例的基本信息和分类
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="caseNumber">案例编号 *</Label>
                    <Input
                      id="caseNumber"
                      value={formData.caseNumber}
                      onChange={(e) => handleFieldChange('caseNumber', e.target.value)}
                      placeholder="请输入案例编号"
                      className={errors['caseNumber'] ? 'border-red-500' : ''}
                    />
                    {errors['caseNumber'] && (
                      <p className="text-sm text-red-500">{errors['caseNumber']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="caseType">案例类型 *</Label>
                    <Select value={formData.caseType} onValueChange={(value) => handleFieldChange('caseType', value)}>
                      <SelectTrigger className={errors['caseType'] ? 'border-red-500' : ''}>
                        <SelectValue placeholder="请选择案例类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="INPATIENT">住院</SelectItem>
                        <SelectItem value="OUTPATIENT">门诊</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors['caseType'] && (
                      <p className="text-sm text-red-500">{errors['caseType']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="medicalCategory">医疗类别 *</Label>
                    <Input
                      id="medicalCategory"
                      value={formData.medicalCategory}
                      onChange={(e) => handleFieldChange('medicalCategory', e.target.value)}
                      placeholder="请输入医疗类别"
                      className={errors['medicalCategory'] ? 'border-red-500' : ''}
                    />
                    {errors['medicalCategory'] && (
                      <p className="text-sm text-red-500">{errors['medicalCategory']}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="totalCost">总费用</Label>
                    <Input
                      id="totalCost"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.totalCost}
                      onChange={(e) => handleFieldChange('totalCost', e.target.value)}
                      placeholder="请输入总费用"
                      className={errors['totalCost'] ? 'border-red-500' : ''}
                    />
                    {errors['totalCost'] && (
                      <p className="text-sm text-red-500">{errors['totalCost']}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 时间信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  时间信息
                </CardTitle>
                <CardDescription>
                  编辑入院和出院时间
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="admissionDate">入院日期</Label>
                    <Input
                      id="admissionDate"
                      type="date"
                      value={formData.admissionDate}
                      onChange={(e) => handleFieldChange('admissionDate', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dischargeDate">出院日期</Label>
                    <Input
                      id="dischargeDate"
                      type="date"
                      value={formData.dischargeDate}
                      onChange={(e) => handleFieldChange('dischargeDate', e.target.value)}
                      className={errors['dischargeDate'] ? 'border-red-500' : ''}
                    />
                    {errors['dischargeDate'] && (
                      <p className="text-sm text-red-500">{errors['dischargeDate']}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 描述信息 */}
            <Card>
              <CardHeader>
                <CardTitle>描述信息</CardTitle>
                <CardDescription>
                  添加案例的详细描述或备注
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="description">案例描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleFieldChange('description', e.target.value)}
                    placeholder="请输入案例描述或备注信息"
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <div className="flex items-center justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/medical-cases/${caseId}`)}
                disabled={isSaving}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    保存修改
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
