"use client"

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { TooltipProvider } from '@/components/ui/tooltip'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { businessToast } from '@/lib/toast'
import { formatDate, formatCurrency } from '@/lib/format-utils'
import { MedicalCase } from '@/types/medical-case'
import { usePageState } from '@/hooks/use-page-state'
import { MedicalCaseDetailSkeleton, ErrorState } from '@/components/ui/medical-case-skeleton'

import { PageTransition, CardTransition } from '@/components/ui/page-transition'
import {
  User,
  Hospital,
  Calendar,
  DollarSign,
  FileText,
  Activity,
  Stethoscope,
  Receipt,
  Share,
  Download,
  ArrowLeft,
  Star
} from 'lucide-react'

export default function MedicalCaseDetailPage() {
  const router = useRouter()
  const params = useParams()
  const caseId = params['id'] as string

  const [medicalCase, setMedicalCase] = useState<MedicalCase | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 页面状态管理
  const { smartGoBack, navigateWithContext } = usePageState({
    pageKey: `medical-case-detail-${caseId}`,
    autoSave: false
  })
  const [activeTab, setActiveTab] = useState('diagnoses')

  // 获取案例详情
  const fetchCaseDetail = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const token = localStorage.getItem('access_token')
      if (!token) {
        businessToast.loginRequired()
        router.push('/login')
        return
      }

      const response = await fetch(`/api/medical-cases/${caseId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        if (response.status === 404) {
          setError('案例不存在或已被删除')
        } else if (response.status === 403) {
          setError('您没有权限查看此案例')
        } else {
          setError('获取案例详情失败，请稍后重试')
        }
        return
      }

      const result = await response.json()

      if (result.success) {
        setMedicalCase(result.data)
      } else {
        setError(result.message || '获取案例详情失败')
        businessToast.saveError('案例详情', result.message)
      }
    } catch (error) {
      console.error('获取案例详情失败:', error)
      setError('网络连接失败，请检查网络后重试')
      businessToast.networkError()
    } finally {
      setIsLoading(false)
    }
  }



  // 使用统一的格式化函数
  // formatDate 和 formatCurrency 已从 @/lib/format-utils 导入

  // 获取案例类型标签样式
  const getCaseTypeVariant = (caseType: string) => {
    switch (caseType) {
      case 'INPATIENT':
        return 'default'
      case 'OUTPATIENT':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  // 获取案例类型标签文本
  const getCaseTypeLabel = (caseType: string) => {
    switch (caseType) {
      case 'INPATIENT':
        return '住院'
      case 'OUTPATIENT':
        return '门诊'
      default:
        return '未知'
    }
  }

  // 获取性别显示文本
  const getGenderText = (gender?: string) => {
    switch (gender) {
      case 'MALE':
        return '男'
      case 'FEMALE':
        return '女'
      case 'OTHER':
        return '其他'
      default:
        return '-'
    }
  }



  useEffect(() => {
    if (caseId) {
      fetchCaseDetail()
    }
  }, [caseId])

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
        <MainLayout>
          <MedicalCaseDetailSkeleton />
        </MainLayout>
      </RouteGuard>
    )
  }

  if (error) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
        <MainLayout>
          <div className="container mx-auto p-6">
            <ErrorState
              title="加载失败"
              description={error}
              onRetry={fetchCaseDetail}
            />
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  if (!medicalCase) {
    return (
      <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
        <MainLayout>
          <div className="container mx-auto p-6">
            <ErrorState
              title="案例不存在"
              description="请检查案例ID是否正确"
              onRetry={() => router.push('/medical-cases')}
            />
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
      <MainLayout>
        <TooltipProvider>
          <PageTransition type="fade" duration={300}>
            <div className="container mx-auto p-6 space-y-6">


              {/* 页面头部 - 企业级管理系统设计 */}
              <CardTransition delay={100}>
                <div className="border-b pb-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    onClick={() => smartGoBack('/medical-cases')}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    返回列表
                  </Button>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono">
                      {medicalCase.caseNumber}
                    </Badge>
                    <Badge variant={getCaseTypeVariant(medicalCase.caseType)}>
                      {getCaseTypeLabel(medicalCase.caseType)}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div className="flex-1 min-w-0">
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                    医疗案例详情
                  </h1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>{medicalCase.patientName || '未知患者'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Hospital className="h-4 w-4" />
                      <span>{medicalCase.hospitalName || '未知医院'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(medicalCase.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4" />
                      <span>{formatCurrency(medicalCase.totalCost)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Star className="h-4 w-4 mr-2" />
                    收藏
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share className="h-4 w-4 mr-2" />
                    分享
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </Button>
                </div>
              </div>
            </div>
              </CardTransition>

          {/* 基本信息区域 - 企业级管理系统设计 */}
          <CardTransition delay={300}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* 患者信息 */}
              <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-base">
                  <User className="h-4 w-4 mr-2" />
                  患者信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">姓名</p>
                  <p className="font-medium">{medicalCase.patientName || '-'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">身份证号</p>
                  <p className="font-mono text-sm">
                    {medicalCase.patientIdCard?.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') || '-'}
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-sm text-muted-foreground">年龄</p>
                    <p className="font-medium">{medicalCase.patientAge || '-'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">性别</p>
                    <p className="font-medium">{getGenderText(medicalCase.patientGender)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 医院信息 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-base">
                  <Hospital className="h-4 w-4 mr-2" />
                  医院信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">医院名称</p>
                  <p className="font-medium">{medicalCase.hospitalName || '-'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">科室</p>
                  <p className="font-medium">{medicalCase.department || '-'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">主治医生</p>
                  <p className="font-medium">{medicalCase.doctorName || '-'}</p>
                </div>
              </CardContent>
            </Card>

            {/* 案例信息 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-base">
                  <FileText className="h-4 w-4 mr-2" />
                  案例信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">医疗类别</p>
                  <p className="font-medium">{medicalCase.medicalCategory || '-'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">入院日期</p>
                  <p className="font-medium">{formatDate(medicalCase.admissionDate)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">出院日期</p>
                  <p className="font-medium">{formatDate(medicalCase.dischargeDate)}</p>
                </div>
              </CardContent>
            </Card>

            {/* 费用信息 */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-base">
                  <DollarSign className="h-4 w-4 mr-2" />
                  费用信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">总费用</p>
                  <p className="text-lg font-bold">{formatCurrency(medicalCase.totalCost)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">医保支付</p>
                  <p className="font-medium text-green-600">
                    {formatCurrency(medicalCase.settlements?.[0]?.totalFundPayment || 0)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">个人支付</p>
                  <p className="font-medium text-orange-600">
                    {formatCurrency(medicalCase.settlements?.[0]?.policyRangeSelfPay || 0)}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
          </CardTransition>

          {/* 详细信息标签页 */}
          <CardTransition delay={400}>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="diagnoses">诊断信息</TabsTrigger>
              <TabsTrigger value="surgeries">手术信息</TabsTrigger>
              <TabsTrigger value="groups">入组信息</TabsTrigger>
              <TabsTrigger value="costs">费用明细</TabsTrigger>
              <TabsTrigger value="settlements">结算信息</TabsTrigger>
            </TabsList>

            {/* 诊断信息标签页 */}
            <TabsContent value="diagnoses" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Stethoscope className="h-5 w-5 mr-2" />
                    诊断信息
                  </CardTitle>
                  <CardDescription>
                    患者的诊断记录和相关信息
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {medicalCase.diagnoses && medicalCase.diagnoses.length > 0 ? (
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>诊断编码</TableHead>
                            <TableHead>诊断名称</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>创建时间</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {medicalCase.diagnoses.map((diagnosis, index) => (
                            <TableRow key={diagnosis.id || index}>
                              <TableCell className="font-mono text-sm">
                                {diagnosis.diagnosisCode || '-'}
                              </TableCell>
                              <TableCell className="font-medium">
                                {diagnosis.diagnosisName}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {diagnosis.diagnosisType}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-sm text-muted-foreground">
                                {formatDate(diagnosis.createdAt)}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                        <Stethoscope className="h-12 w-12 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">暂无诊断信息</h3>
                      <p className="text-sm text-muted-foreground">该案例尚未添加诊断记录</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 手术信息标签页 */}
            <TabsContent value="surgeries" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    手术信息
                  </CardTitle>
                  <CardDescription>
                    患者的手术记录和相关信息
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {medicalCase.surgeries && medicalCase.surgeries.length > 0 ? (
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>手术名称</TableHead>
                            <TableHead>手术编码</TableHead>
                            <TableHead>主刀医生</TableHead>
                            <TableHead>手术日期</TableHead>
                            <TableHead>时长</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {medicalCase.surgeries.map((surgery, index) => (
                            <TableRow key={surgery.id || index}>
                              <TableCell className="font-medium">
                                {surgery.surgeryName}
                              </TableCell>
                              <TableCell className="font-mono text-sm">
                                {surgery.surgeryCode || '-'}
                              </TableCell>
                              <TableCell>
                                {surgery.surgeonName || '-'}
                              </TableCell>
                              <TableCell>
                                {formatDate(surgery.surgeryDate)}
                              </TableCell>
                              <TableCell>
                                {surgery.surgeryDuration ? `${surgery.surgeryDuration}分钟` : '-'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                        <Activity className="h-12 w-12 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">暂无手术信息</h3>
                      <p className="text-sm text-muted-foreground">该案例尚未添加手术记录</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 入组信息标签页 */}
            <TabsContent value="groups" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    入组信息
                  </CardTitle>
                  <CardDescription>
                    案例的分组信息和支付标准
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {medicalCase.caseGroups && medicalCase.caseGroups.length > 0 ? (
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>分组名称</TableHead>
                            <TableHead>分组编码</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>权重</TableHead>
                            <TableHead>状态</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {medicalCase.caseGroups.map((group, index) => (
                            <TableRow key={group.id || index}>
                              <TableCell className="font-medium">
                                {group.groupName}
                              </TableCell>
                              <TableCell className="font-mono text-sm">
                                {group.groupCode}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {group.groupType}
                                </Badge>
                              </TableCell>
                              <TableCell className="font-mono">
                                {group.groupWeight.toFixed(4)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={group.isValid ? 'default' : 'secondary'}>
                                  {group.isValid ? '有效' : '无效'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                        <FileText className="h-12 w-12 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">暂无入组信息</h3>
                      <p className="text-sm text-muted-foreground">该案例尚未进行分组</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 费用明细标签页 */}
            <TabsContent value="costs" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Receipt className="h-5 w-5 mr-2" />
                    费用明细
                  </CardTitle>
                  <CardDescription>
                    详细的费用项目和金额信息
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {medicalCase.costDetails && medicalCase.costDetails.length > 0 ? (
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>项目名称</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead className="text-right">数量</TableHead>
                            <TableHead className="text-right">单价</TableHead>
                            <TableHead className="text-right">金额</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {medicalCase.costDetails.map((cost, index) => (
                            <TableRow key={cost.id || index}>
                              <TableCell className="font-medium">
                                {cost.itemName}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {cost.itemType}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right font-mono">
                                {cost.quantity}
                              </TableCell>
                              <TableCell className="text-right font-mono">
                                {formatCurrency(cost.unitPrice)}
                              </TableCell>
                              <TableCell className="text-right font-mono font-semibold">
                                {formatCurrency(cost.totalAmount)}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                        <Receipt className="h-12 w-12 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">暂无费用明细</h3>
                      <p className="text-sm text-muted-foreground">该案例尚未添加费用记录</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* 结算信息标签页 */}
            <TabsContent value="settlements" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    结算信息
                  </CardTitle>
                  <CardDescription>
                    医保结算和支付相关信息
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {medicalCase.settlements && medicalCase.settlements.length > 0 ? (
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>结算编号</TableHead>
                            <TableHead>总费用</TableHead>
                            <TableHead>医保支付</TableHead>
                            <TableHead>个人支付</TableHead>
                            <TableHead>状态</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {medicalCase.settlements.map((settlement, index) => (
                            <TableRow key={settlement.id || index}>
                              <TableCell className="font-mono">
                                #{index + 1}
                              </TableCell>
                              <TableCell className="font-mono">
                                {formatCurrency(settlement.totalMedicalCost)}
                              </TableCell>
                              <TableCell className="font-mono">
                                {formatCurrency(settlement.totalFundPayment)}
                              </TableCell>
                              <TableCell className="font-mono">
                                {formatCurrency(settlement.policyRangeSelfPay)}
                              </TableCell>
                              <TableCell>
                                <Badge variant={settlement.isValid ? 'default' : 'secondary'}>
                                  {settlement.isValid ? '有效' : '无效'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                        <DollarSign className="h-12 w-12 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">暂无结算信息</h3>
                      <p className="text-sm text-muted-foreground">该案例尚未添加结算记录</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          </CardTransition>
            </div>
          </PageTransition>
        </TooltipProvider>
      </MainLayout>
    </RouteGuard>
  )
}
