"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { OptimizedTable } from '@/components/ui/optimized-table'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'

import { RouteGuard } from '@/components/auth/route-guard'
import { businessToast } from '@/lib/toast'

import { EnhancedFilter, FilterParams } from '@/components/medical-cases/enhanced-filter'
import { StatisticsCards } from '@/components/medical-cases/statistics-cards'
import { AdvancedPagination } from '@/components/ui/advanced-pagination'
import { useMedicalCases } from '@/hooks/use-medical-cases'
import { useListState } from '@/hooks/use-list-state'
import { useListPageState, useBatchOperation } from '@/hooks/use-page-state'
import { TableSkeleton } from '@/components/ui/loading-states'
import {
  FileText,
  Search,
  Eye,
  Edit,
  Trash2,
  Users,
  DollarSign,
  Calendar,
  Hospital,
  ChevronUp,
  ChevronDown
} from 'lucide-react'



export default function MedicalCasesPage() {
  const router = useRouter()
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table')
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const { saveState } = useListState()

  // 页面状态管理
  const { navigateWithContext, saveListState } = useListPageState('medical-cases-list')

  // 批量操作
  const { isRunning: isBatchRunning, progress: batchProgress, executeBatch } = useBatchOperation()

  // 使用优化的Hook管理所有状态和操作
  const {
    // 数据状态
    cases,
    statistics,

    // 加载状态
    isLoading,
    isStatisticsLoading,
    error,

    // 分页状态
    pagination,

    // 筛选状态
    searchTerm,
    filters,
    sortBy,
    sortOrder,

    // 操作方法
    setSearchTerm,
    setFilters,
    setSortBy,
    setSortOrder,
    handlePageChange,
    handlePageSizeChange,
    refreshData,
  } = useMedicalCases({
    initialPageSize: 10,
    cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
    enableRealTimeUpdates: true
  })



  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  // 筛选处理
  const handleFiltersChange = (newFilters: FilterParams) => {
    setFilters(newFilters)
  }

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({})
    setSearchTerm('')
  }







  // 操作处理
  const handleView = (id: number) => {
    // 保存当前列表状态
    saveListState({
      page: pagination.page,
      pageSize: pagination.pageSize,
      searchTerm: searchTerm,
      sortBy: sortBy,
      sortOrder: sortOrder,
      filters: filters
    })

    // 使用智能导航跳转到详情页
    navigateWithContext(`/medical-cases/${id}`, {
      returnUrl: '/medical-cases',
      preserveState: true,
      metadata: { caseId: id }
    })
  }

  const handleEdit = (id: number) => {
    // 保存当前列表状态
    saveListState({
      page: pagination.page,
      pageSize: pagination.pageSize,
      searchTerm: searchTerm,
      sortBy: sortBy,
      sortOrder: sortOrder,
      filters: filters
    })

    // 使用智能导航跳转到编辑页
    navigateWithContext(`/medical-cases/${id}/edit`, {
      returnUrl: '/medical-cases',
      preserveState: true,
      metadata: { caseId: id, action: 'edit' }
    })
  }

  // 批量操作处理
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(cases?.map(item => item.id) || [])
    } else {
      setSelectedItems([])
    }
  }

  const handleSelectItem = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id])
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id))
    }
  }

  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) return

    try {
      await executeBatch(
        selectedItems,
        async (id: number) => {
          const response = await fetch(`/api/medical-cases/${id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
          })

          if (!response.ok) {
            throw new Error(`删除案例 ${id} 失败`)
          }
        },
        {
          operationId: `batch-delete-${Date.now()}`,
          operationType: 'batch',
          operationMessage: `批量删除 ${selectedItems.length} 个医疗案例`,
          batchSize: 3
        }
      )

      // 清空选择
      setSelectedItems([])

      // 刷新数据
      refreshData()

      businessToast.deleteSuccess(`${selectedItems.length} 个医疗案例`)
    } catch (error) {
      console.error('批量删除失败:', error)
      businessToast.deleteError('医疗案例', error instanceof Error ? error.message : '未知错误')
    }
  }

  const handleDelete = async (caseId: number) => {
    if (!confirm('确定要删除这个案例吗？')) return

    try {
      // 这里应该调用删除单个案例的API
      // 暂时使用console.log，实际应该实现删除逻辑
      console.log('删除案例:', caseId)
      businessToast.deleteSuccess()
      await refreshData()
    } catch (error) {
      console.error('删除失败:', error)
      businessToast.deleteError()
    }
  }

  const formatCurrency = (amount: number) => {
    if (amount === undefined || amount === null || isNaN(amount)) return '-'
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getCaseTypeLabel = (type: string) => {
    return type === 'INPATIENT' ? '住院' : '门诊'
  }

  const getGenderLabel = (gender?: string) => {
    switch (gender) {
      case 'MALE': return '男'
      case 'FEMALE': return '女'
      case 'OTHER': return '其他'
      default: return '未知'
    }
  }

  const getCaseTypeBadgeVariant = (type: string) => {
    return type === 'INPATIENT' ? 'default' : 'secondary'
  }

  return (
    <RouteGuard>
      <div className="space-y-6">

          {/* 统计卡片 */}
          <StatisticsCards
            statistics={statistics}
            isLoading={isStatisticsLoading}
            error={error}
          />

          {/* 搜索和筛选区 */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {/* 搜索框和控制按钮 */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center">
                  {/* 搜索框 */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="搜索案例编号、患者姓名、医院名称..."
                      value={searchTerm}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* 排序和筛选控制 */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="排序方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="id">ID</SelectItem>
                        <SelectItem value="totalCost">总费用</SelectItem>
                        <SelectItem value="admissionDate">入院时间</SelectItem>
                        <SelectItem value="patientAge">患者年龄</SelectItem>
                      </SelectContent>
                    </Select>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="min-w-[80px]"
                    >
                      {sortOrder === 'asc' ? (
                        <>
                          <ChevronUp className="h-4 w-4 mr-1" />
                          正序
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-4 w-4 mr-1" />
                          倒序
                        </>
                      )}
                    </Button>

                    <EnhancedFilter
                      filters={filters}
                      onFiltersChange={handleFiltersChange}
                      onReset={handleResetFilters}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 工具栏 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                共 {pagination.total} 条记录
              </div>
            </div>

            <div className="flex items-center gap-2">

              <Separator orientation="vertical" className="h-6" />

              <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'table' | 'card')}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="table">表格</TabsTrigger>
                  <TabsTrigger value="card">卡片</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* 数据展示区 */}
          <Card>
            <CardContent className="p-0">
              {isLoading ? (
                <div className="p-8">
                  <TableSkeleton />
                </div>
              ) : error ? (
                <div className="p-8 text-center">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">数据加载失败</h3>
                  <p className="text-muted-foreground mt-2">
                    {error}
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    请刷新页面重试，或联系系统管理员
                  </p>
                </div>
              ) : cases.length === 0 ? (
                <div className="p-8 text-center">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">暂无数据</h3>
                  <p className="text-muted-foreground">
                    {searchTerm || Object.keys(filters).length > 0
                      ? '没有找到符合条件的案例'
                      : '还没有任何医疗案例，请通过API或工具导入数据'}
                  </p>
                </div>
              ) : (
                <Tabs value={viewMode} className="w-full">
                  <TabsContent value="table" className="mt-0">
                    <OptimizedTable
                      data={cases}
                      columns={[
                        {
                          key: 'caseNumber',
                          title: '案例编号',
                          width: 150,
                          sortable: true,
                          render: (item) => (
                            <div className="font-mono text-sm">{item.caseNumber}</div>
                          )
                        },
                        {
                          key: 'patientName',
                          title: '患者信息',
                          width: 180,
                          sortable: true,
                          render: (item) => (
                            <div>
                              <div className="font-medium">{item.patientName}</div>
                              <div className="text-sm text-muted-foreground">
                                {item.patientAge || '未知'}岁 {getGenderLabel(item.patientGender)}
                              </div>
                            </div>
                          )
                        },
                        {
                          key: 'patientIdCard',
                          title: '身份证号',
                          width: 140,
                          render: (item) => (
                            <div className="font-mono text-sm">
                              {item.patientIdCard ?
                                `${item.patientIdCard.slice(0, 6)}****${item.patientIdCard.slice(-4)}` :
                                '未提供'
                              }
                            </div>
                          )
                        },
                        {
                          key: 'caseType',
                          title: '类型',
                          width: 100,
                          sortable: true,
                          filterable: true,
                          render: (item) => (
                            <Badge variant={getCaseTypeBadgeVariant(item.caseType)}>
                              {getCaseTypeLabel(item.caseType)}
                            </Badge>
                          )
                        },
                        {
                          key: 'hospitalName',
                          title: '医院科室',
                          width: 200,
                          sortable: true,
                          render: (item) => (
                            <div>
                              <div className="font-medium">{item.hospitalName}</div>
                              <div className="text-sm text-muted-foreground">
                                {item.department || '未指定科室'}
                              </div>
                            </div>
                          )
                        },
                        {
                          key: 'doctorName',
                          title: '医生',
                          width: 120,
                          render: (item) => (
                            <div className="text-sm">{item.doctorName || '未指定'}</div>
                          )
                        },
                        {
                          key: 'medicalCategory',
                          title: '医疗类别',
                          width: 120,
                          filterable: true,
                          render: (item) => (
                            <Badge variant="outline" className="text-xs">
                              {item.medicalCategory}
                            </Badge>
                          )
                        },
                        {
                          key: 'admissionDate',
                          title: '入出院日期',
                          width: 160,
                          sortable: true,
                          render: (item) => (
                            <div>
                              <div className="text-sm">
                                {item.admissionDate ? formatDate(item.admissionDate) : '未入院'}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {item.dischargeDate ? `至 ${formatDate(item.dischargeDate)}` : '未出院'}
                              </div>
                            </div>
                          )
                        },
                        {
                          key: 'totalCost',
                          title: '总费用',
                          width: 120,
                          sortable: true,
                          render: (item) => (
                            <div className="font-medium">
                              {formatCurrency(item.totalCost)}
                            </div>
                          )
                        },
                        {
                          key: 'actions',
                          title: '操作',
                          width: 120,
                          className: 'text-right',
                          render: (item) => (
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleView(item.id)}
                                title="查看详情"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(item.id)}
                                title="编辑案例"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(item.id)}
                                title="删除案例"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )
                        }
                      ]}
                      rowHeight={80}
                      maxHeight={600}
                      searchable={false} // 已有外部搜索
                      sortable={true}
                      filterable={false} // 已有外部筛选
                      onRowClick={(item) => handleView(item.id)}
                      loading={isLoading}
                      emptyMessage="暂无医疗案例数据"
                      virtualScrollThreshold={50} // 超过50条记录使用虚拟滚动
                      className="w-full"
                    />
                  </TabsContent>

                  <TabsContent value="card" className="mt-0">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 p-6">
                      {cases.map((medicalCase) => (
                        <Card key={medicalCase.id} className="hover:shadow-md transition-shadow">
                          <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                              <div>
                                <CardTitle className="text-base font-mono">
                                  {medicalCase.caseNumber}
                                </CardTitle>
                                <p className="text-sm text-muted-foreground">
                                  ID: {medicalCase.id}
                                </p>
                              </div>
                              <Badge variant={getCaseTypeBadgeVariant(medicalCase.caseType)}>
                                {getCaseTypeLabel(medicalCase.caseType)}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <div className="flex flex-col">
                                <span className="font-medium">{medicalCase.patientName}</span>
                                <span className="text-sm text-muted-foreground">
                                  {medicalCase.patientAge || '未知'}岁 · {getGenderLabel(medicalCase.patientGender)}
                                </span>
                                <span className="text-xs text-muted-foreground font-mono">
                                  {medicalCase.patientIdCard ?
                                    `${medicalCase.patientIdCard.slice(0, 6)}****${medicalCase.patientIdCard.slice(-4)}` :
                                    '未提供身份证'
                                  }
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <Hospital className="h-4 w-4 text-muted-foreground" />
                              <div className="flex flex-col">
                                <span className="text-sm font-medium">{medicalCase.hospitalName}</span>
                                <span className="text-xs text-muted-foreground">
                                  {medicalCase.department || '未指定科室'} · {medicalCase.doctorName || '未指定医生'}
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <Badge variant="outline" className="text-xs">
                                {medicalCase.medicalCategory}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{formatCurrency(medicalCase.totalCost)}</span>
                            </div>

                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <div className="flex flex-col">
                                <span className="text-sm">
                                  {medicalCase.admissionDate ? formatDate(medicalCase.admissionDate) : '未入院'}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {medicalCase.dischargeDate ? `至 ${formatDate(medicalCase.dischargeDate)}` : '未出院'}
                                </span>
                              </div>
                            </div>

                            <Separator />

                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleView(medicalCase.id)}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  查看
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(medicalCase.id)}
                                >
                                  <Edit className="h-4 w-4 mr-1" />
                                  编辑
                                </Button>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(medicalCase.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>

          {/* 高级分页 */}
          <AdvancedPagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            disabled={isLoading}
          />
      </div>
    </RouteGuard>
  )
}
