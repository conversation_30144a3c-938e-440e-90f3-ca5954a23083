'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  FileText, 
  Search, 
  Filter, 
  Download,
  RefreshCw,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Trash2,
  Eye,
  Loader2
} from 'lucide-react'

// 日志类型
type LogLevel = 'INFO' | 'WARN' | 'ERROR' | 'DEBUG'
type LogCategory = 'SYSTEM' | 'USER' | 'SECURITY' | 'OPERATION' | 'DATA'

// 系统日志接口
interface SystemLog {
  id: number
  level: LogLevel
  category: LogCategory
  message: string
  details?: string
  userId?: number
  username?: string
  ipAddress?: string
  userAgent?: string
  module: string
  action: string
  resourceId?: string
  resourceType?: string
  timestamp: string
  duration?: number
}

// 日志级别颜色映射
const levelColors: Record<LogLevel, string> = {
  INFO: 'bg-blue-100 text-blue-800',
  WARN: 'bg-yellow-100 text-yellow-800',
  ERROR: 'bg-red-100 text-red-800',
  DEBUG: 'bg-gray-100 text-gray-800',
}

// 日志级别图标映射
const levelIcons: Record<LogLevel, any> = {
  INFO: Info,
  WARN: AlertTriangle,
  ERROR: XCircle,
  DEBUG: Activity,
}

// 分类颜色映射
const categoryColors: Record<LogCategory, string> = {
  SYSTEM: 'bg-purple-100 text-purple-800',
  USER: 'bg-green-100 text-green-800',
  SECURITY: 'bg-red-100 text-red-800',
  OPERATION: 'bg-blue-100 text-blue-800',
  DATA: 'bg-orange-100 text-orange-800',
}

export default function SystemLogsPage() {
  const [logs, setLogs] = useState<SystemLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('today')
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null)
  const { toast } = useToast()

  // 获取系统日志
  const fetchLogs = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '错误',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      // 构建查询参数
      const params = new URLSearchParams()
      if (levelFilter !== 'all') params.append('level', levelFilter)
      if (categoryFilter !== 'all') params.append('category', categoryFilter)
      if (dateFilter !== 'all') params.append('dateRange', dateFilter)
      if (searchTerm) params.append('search', searchTerm)

      const response = await fetch(`/api/settings/logs?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setLogs(result.data || [])
      } else {
        // 使用模拟数据
        const mockLogs: SystemLog[] = [
          {
            id: 1,
            level: 'INFO',
            category: 'USER',
            message: '用户登录成功',
            details: '用户admin通过Web界面成功登录系统',
            userId: 1,
            username: 'admin',
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            module: 'AUTH',
            action: 'LOGIN',
            timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
            duration: 150,
          },
          {
            id: 2,
            level: 'WARN',
            category: 'SECURITY',
            message: '多次登录失败',
            details: '用户test_user在5分钟内尝试登录失败3次',
            username: 'test_user',
            ipAddress: '*************',
            module: 'AUTH',
            action: 'LOGIN_FAILED',
            timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          },
          {
            id: 3,
            level: 'ERROR',
            category: 'SYSTEM',
            message: '数据库连接失败',
            details: 'ORA-12541: TNS:no listener',
            module: 'DATABASE',
            action: 'CONNECT',
            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          },
          {
            id: 4,
            level: 'INFO',
            category: 'OPERATION',
            message: '创建监管规则',
            details: '用户admin创建了新的监管规则：费用异常检测规则',
            userId: 1,
            username: 'admin',
            ipAddress: '*************',
            module: 'SUPERVISION_RULE',
            action: 'CREATE',
            resourceId: '6',
            resourceType: 'RULE',
            timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
            duration: 320,
          },
          {
            id: 5,
            level: 'INFO',
            category: 'DATA',
            message: '数据导出完成',
            details: '用户admin导出了案例汇总报表，共200条记录',
            userId: 1,
            username: 'admin',
            ipAddress: '*************',
            module: 'ANALYTICS',
            action: 'EXPORT',
            timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
            duration: 2500,
          },
        ]
        
        setLogs(mockLogs)
        
        toast({
          title: '获取日志失败',
          description: result.message + '（已加载模拟数据）',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('获取系统日志失败:', error)
      toast({
        title: '获取日志失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清理日志
  const clearLogs = async () => {
    if (!confirm('确定要清理所有日志吗？此操作不可恢复。')) {
      return
    }

    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '清理失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/settings/logs', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: '清理成功',
          description: '系统日志已清理',
        })
        fetchLogs()
      } else {
        toast({
          title: '清理失败',
          description: result.message,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('清理日志失败:', error)
      toast({
        title: '清理失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 导出日志
  const exportLogs = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '导出失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      // 构建查询参数
      const params = new URLSearchParams()
      if (levelFilter !== 'all') params.append('level', levelFilter)
      if (categoryFilter !== 'all') params.append('category', categoryFilter)
      if (dateFilter !== 'all') params.append('dateRange', dateFilter)
      if (searchTerm) params.append('search', searchTerm)
      params.append('export', 'true')

      const response = await fetch(`/api/settings/logs?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `system_logs_${new Date().toISOString().slice(0, 10)}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: '导出成功',
          description: '日志文件已下载',
        })
      } else {
        toast({
          title: '导出失败',
          description: '无法导出日志文件',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('导出日志失败:', error)
      toast({
        title: '导出失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN')
  }

  // 格式化持续时间
  const formatDuration = (duration?: number) => {
    if (!duration) return '-'
    if (duration < 1000) return `${duration}ms`
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`
    return `${(duration / 60000).toFixed(1)}min`
  }

  // 过滤日志
  const filteredLogs = logs.filter(log => {
    const matchesSearch = !searchTerm || 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.module.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesLevel = levelFilter === 'all' || log.level === levelFilter
    const matchesCategory = categoryFilter === 'all' || log.category === categoryFilter
    
    return matchesSearch && matchesLevel && matchesCategory
  })

  // 统计数据
  const stats = {
    total: logs.length,
    info: logs.filter(l => l.level === 'INFO').length,
    warn: logs.filter(l => l.level === 'WARN').length,
    error: logs.filter(l => l.level === 'ERROR').length,
  }

  useEffect(() => {
    fetchLogs()
  }, [levelFilter, categoryFilter, dateFilter])

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">系统日志</h1>
              <p className="text-muted-foreground">
                查看和管理系统操作日志
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={fetchLogs}>
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button variant="outline" onClick={exportLogs}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
              <Button variant="outline" onClick={clearLogs} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                清理
              </Button>
            </div>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总日志数</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">信息日志</p>
                    <p className="text-2xl font-bold text-green-600">{stats.info}</p>
                  </div>
                  <Info className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">警告日志</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.warn}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">错误日志</p>
                    <p className="text-2xl font-bold text-red-600">{stats.error}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <Card>
            <CardHeader>
              <CardTitle>筛选条件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">搜索日志</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="搜索消息、用户或模块..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="level">日志级别</Label>
                  <Select
                    value={levelFilter}
                    onValueChange={setLevelFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="所有级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有级别</SelectItem>
                      <SelectItem value="INFO">信息</SelectItem>
                      <SelectItem value="WARN">警告</SelectItem>
                      <SelectItem value="ERROR">错误</SelectItem>
                      <SelectItem value="DEBUG">调试</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">日志分类</Label>
                  <Select
                    value={categoryFilter}
                    onValueChange={setCategoryFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="所有分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有分类</SelectItem>
                      <SelectItem value="SYSTEM">系统</SelectItem>
                      <SelectItem value="USER">用户</SelectItem>
                      <SelectItem value="SECURITY">安全</SelectItem>
                      <SelectItem value="OPERATION">操作</SelectItem>
                      <SelectItem value="DATA">数据</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date">时间范围</Label>
                  <Select
                    value={dateFilter}
                    onValueChange={setDateFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="yesterday">昨天</SelectItem>
                      <SelectItem value="last7days">最近7天</SelectItem>
                      <SelectItem value="last30days">最近30天</SelectItem>
                      <SelectItem value="all">所有时间</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 日志列表 */}
          <Card>
            <CardHeader>
              <CardTitle>日志记录</CardTitle>
              <CardDescription>
                共 {filteredLogs.length} 条日志
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>时间</TableHead>
                        <TableHead>级别</TableHead>
                        <TableHead>分类</TableHead>
                        <TableHead>消息</TableHead>
                        <TableHead>用户</TableHead>
                        <TableHead>模块</TableHead>
                        <TableHead>持续时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLogs.map((log) => {
                        const LevelIcon = levelIcons[log.level]
                        
                        return (
                          <TableRow key={log.id}>
                            <TableCell>
                              <div className="text-sm">
                                <div className="flex items-center space-x-1">
                                  <Clock className="h-3 w-3" />
                                  <span>{formatTime(log.timestamp)}</span>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <LevelIcon className="h-4 w-4" />
                                <Badge className={levelColors[log.level]}>
                                  {log.level}
                                </Badge>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={categoryColors[log.category]}>
                                {log.category}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="max-w-xs">
                                <p className="font-medium truncate">{log.message}</p>
                                {log.details && (
                                  <p className="text-sm text-muted-foreground truncate">
                                    {log.details}
                                  </p>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {log.username ? (
                                <div className="flex items-center space-x-1">
                                  <User className="h-3 w-3" />
                                  <span className="text-sm">{log.username}</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {log.module}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {formatDuration(log.duration)}
                            </TableCell>
                            <TableCell>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => setSelectedLog(log)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 日志详情对话框 */}
          {selectedLog && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>日志详情</CardTitle>
                    <Button variant="ghost" size="sm" onClick={() => setSelectedLog(null)}>
                      ×
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>时间</Label>
                      <p className="text-sm">{formatTime(selectedLog.timestamp)}</p>
                    </div>
                    <div>
                      <Label>级别</Label>
                      <Badge className={levelColors[selectedLog.level]}>
                        {selectedLog.level}
                      </Badge>
                    </div>
                    <div>
                      <Label>分类</Label>
                      <Badge className={categoryColors[selectedLog.category]}>
                        {selectedLog.category}
                      </Badge>
                    </div>
                    <div>
                      <Label>模块</Label>
                      <p className="text-sm">{selectedLog.module}</p>
                    </div>
                  </div>
                  
                  <div>
                    <Label>消息</Label>
                    <p className="text-sm">{selectedLog.message}</p>
                  </div>
                  
                  {selectedLog.details && (
                    <div>
                      <Label>详细信息</Label>
                      <p className="text-sm whitespace-pre-wrap">{selectedLog.details}</p>
                    </div>
                  )}
                  
                  {selectedLog.username && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>用户</Label>
                        <p className="text-sm">{selectedLog.username}</p>
                      </div>
                      <div>
                        <Label>IP地址</Label>
                        <p className="text-sm">{selectedLog.ipAddress || '-'}</p>
                      </div>
                    </div>
                  )}
                  
                  {selectedLog.resourceId && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>资源ID</Label>
                        <p className="text-sm">{selectedLog.resourceId}</p>
                      </div>
                      <div>
                        <Label>资源类型</Label>
                        <p className="text-sm">{selectedLog.resourceType}</p>
                      </div>
                    </div>
                  )}
                  
                  {selectedLog.duration && (
                    <div>
                      <Label>执行时长</Label>
                      <p className="text-sm">{formatDuration(selectedLog.duration)}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
