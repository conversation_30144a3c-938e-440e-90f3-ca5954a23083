'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  Settings, 
  Save, 
  RotateCcw,
  Server,
  Globe,
  Mail,
  Phone,
  Building,
  Clock,
  Database,
  Loader2,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

// 系统设置接口
interface SystemSettings {
  // 基本信息
  systemName: string
  systemVersion: string
  systemDescription: string
  organizationName: string
  organizationAddress: string
  contactEmail: string
  contactPhone: string
  
  // 系统配置
  sessionTimeout: number // 分钟
  maxLoginAttempts: number
  passwordMinLength: number
  passwordComplexity: boolean
  enableTwoFactor: boolean
  
  // 业务配置
  defaultPageSize: number
  maxFileUploadSize: number // MB
  enableEmailNotification: boolean
  enableSmsNotification: boolean
  
  // 数据保留
  logRetentionDays: number
  backupRetentionDays: number
  
  // 系统状态
  maintenanceMode: boolean
  maintenanceMessage: string
}

export default function GeneralSettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>({
    systemName: '',
    systemVersion: '',
    systemDescription: '',
    organizationName: '',
    organizationAddress: '',
    contactEmail: '',
    contactPhone: '',
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    passwordComplexity: true,
    enableTwoFactor: false,
    defaultPageSize: 20,
    maxFileUploadSize: 10,
    enableEmailNotification: true,
    enableSmsNotification: false,
    logRetentionDays: 90,
    backupRetentionDays: 30,
    maintenanceMode: false,
    maintenanceMessage: '',
  })
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [originalSettings, setOriginalSettings] = useState<SystemSettings | null>(null)
  const { toast } = useToast()

  // 获取系统设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '错误',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/settings/general', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setSettings(result.data)
        setOriginalSettings(result.data)
      } else {
        // 如果API失败，使用默认设置
        const defaultSettings: SystemSettings = {
          systemName: '医保基金监管平台',
          systemVersion: '2.0.0',
          systemDescription: '智能化医保基金监管与分析系统',
          organizationName: '医保基金监管中心',
          organizationAddress: '北京市朝阳区医保大厦',
          contactEmail: '<EMAIL>',
          contactPhone: '010-12345678',
          sessionTimeout: 30,
          maxLoginAttempts: 5,
          passwordMinLength: 8,
          passwordComplexity: true,
          enableTwoFactor: false,
          defaultPageSize: 20,
          maxFileUploadSize: 10,
          enableEmailNotification: true,
          enableSmsNotification: false,
          logRetentionDays: 90,
          backupRetentionDays: 30,
          maintenanceMode: false,
          maintenanceMessage: '系统维护中，请稍后访问',
        }
        
        setSettings(defaultSettings)
        setOriginalSettings(defaultSettings)
        
        toast({
          title: '获取设置失败',
          description: result.message + '（已加载默认设置）',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('获取系统设置失败:', error)
      toast({
        title: '获取设置失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 更新设置
  const updateSetting = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
    setHasChanges(true)
  }

  // 保存设置
  const saveSettings = async () => {
    try {
      setIsSaving(true)
      const token = localStorage.getItem('access_token')
      
      if (!token) {
        toast({
          title: '保存失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/settings/general', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: '保存成功',
          description: '系统设置已更新',
        })
        setHasChanges(false)
        setOriginalSettings(settings)
      } else {
        toast({
          title: '保存失败',
          description: result.message,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      toast({
        title: '保存失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 重置设置
  const resetSettings = () => {
    if (originalSettings) {
      setSettings(originalSettings)
      setHasChanges(false)
      toast({
        title: '已重置',
        description: '设置已恢复到上次保存的状态',
      })
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN']}>
        <MainLayout>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">加载系统设置...</span>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">基本设置</h1>
              <p className="text-muted-foreground">
                配置系统基本信息和参数
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {hasChanges && (
                <Button variant="outline" onClick={resetSettings}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
              )}
              <Button 
                onClick={saveSettings} 
                disabled={!hasChanges || isSaving}
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                保存设置
              </Button>
            </div>
          </div>

          {/* 更改提示 */}
          {hasChanges && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2 text-yellow-800">
                  <AlertTriangle className="h-5 w-5" />
                  <span>您有未保存的更改，请记得保存设置</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 系统信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>系统信息</span>
              </CardTitle>
              <CardDescription>
                配置系统基本信息和组织信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="systemName">系统名称</Label>
                  <Input
                    id="systemName"
                    value={settings.systemName}
                    onChange={(e) => updateSetting('systemName', e.target.value)}
                    placeholder="输入系统名称"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="systemVersion">系统版本</Label>
                  <Input
                    id="systemVersion"
                    value={settings.systemVersion}
                    onChange={(e) => updateSetting('systemVersion', e.target.value)}
                    placeholder="输入系统版本"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="systemDescription">系统描述</Label>
                <Textarea
                  id="systemDescription"
                  value={settings.systemDescription}
                  onChange={(e) => updateSetting('systemDescription', e.target.value)}
                  placeholder="输入系统描述"
                  rows={3}
                />
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="organizationName">组织名称</Label>
                  <Input
                    id="organizationName"
                    value={settings.organizationName}
                    onChange={(e) => updateSetting('organizationName', e.target.value)}
                    placeholder="输入组织名称"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="organizationAddress">组织地址</Label>
                  <Input
                    id="organizationAddress"
                    value={settings.organizationAddress}
                    onChange={(e) => updateSetting('organizationAddress', e.target.value)}
                    placeholder="输入组织地址"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">联系邮箱</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="contactEmail"
                      type="email"
                      value={settings.contactEmail}
                      onChange={(e) => updateSetting('contactEmail', e.target.value)}
                      placeholder="输入联系邮箱"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">联系电话</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="contactPhone"
                      value={settings.contactPhone}
                      onChange={(e) => updateSetting('contactPhone', e.target.value)}
                      placeholder="输入联系电话"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 安全配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>安全配置</span>
              </CardTitle>
              <CardDescription>
                配置系统安全相关参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时（分钟）</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.sessionTimeout}
                    onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value) || 30)}
                    min="5"
                    max="480"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">最大登录尝试次数</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    value={settings.maxLoginAttempts}
                    onChange={(e) => updateSetting('maxLoginAttempts', parseInt(e.target.value) || 5)}
                    min="3"
                    max="10"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">密码最小长度</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    value={settings.passwordMinLength}
                    onChange={(e) => updateSetting('passwordMinLength', parseInt(e.target.value) || 8)}
                    min="6"
                    max="20"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="passwordComplexity"
                    checked={settings.passwordComplexity}
                    onCheckedChange={(checked) => updateSetting('passwordComplexity', checked)}
                  />
                  <Label htmlFor="passwordComplexity">启用密码复杂度要求</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableTwoFactor"
                    checked={settings.enableTwoFactor}
                    onCheckedChange={(checked) => updateSetting('enableTwoFactor', checked)}
                  />
                  <Label htmlFor="enableTwoFactor">启用双因素认证</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 业务配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>业务配置</span>
              </CardTitle>
              <CardDescription>
                配置系统业务相关参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="defaultPageSize">默认分页大小</Label>
                  <Input
                    id="defaultPageSize"
                    type="number"
                    value={settings.defaultPageSize}
                    onChange={(e) => updateSetting('defaultPageSize', parseInt(e.target.value) || 20)}
                    min="10"
                    max="100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxFileUploadSize">最大文件上传大小（MB）</Label>
                  <Input
                    id="maxFileUploadSize"
                    type="number"
                    value={settings.maxFileUploadSize}
                    onChange={(e) => updateSetting('maxFileUploadSize', parseInt(e.target.value) || 10)}
                    min="1"
                    max="100"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableEmailNotification"
                    checked={settings.enableEmailNotification}
                    onCheckedChange={(checked) => updateSetting('enableEmailNotification', checked)}
                  />
                  <Label htmlFor="enableEmailNotification">启用邮件通知</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableSmsNotification"
                    checked={settings.enableSmsNotification}
                    onCheckedChange={(checked) => updateSetting('enableSmsNotification', checked)}
                  />
                  <Label htmlFor="enableSmsNotification">启用短信通知</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 数据管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>数据管理</span>
              </CardTitle>
              <CardDescription>
                配置数据保留和备份策略
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="logRetentionDays">日志保留天数</Label>
                  <Input
                    id="logRetentionDays"
                    type="number"
                    value={settings.logRetentionDays}
                    onChange={(e) => updateSetting('logRetentionDays', parseInt(e.target.value) || 90)}
                    min="7"
                    max="365"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="backupRetentionDays">备份保留天数</Label>
                  <Input
                    id="backupRetentionDays"
                    type="number"
                    value={settings.backupRetentionDays}
                    onChange={(e) => updateSetting('backupRetentionDays', parseInt(e.target.value) || 30)}
                    min="7"
                    max="180"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 维护模式 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="h-5 w-5" />
                <span>维护模式</span>
              </CardTitle>
              <CardDescription>
                配置系统维护模式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="maintenanceMode"
                  checked={settings.maintenanceMode}
                  onCheckedChange={(checked) => updateSetting('maintenanceMode', checked)}
                />
                <Label htmlFor="maintenanceMode">启用维护模式</Label>
              </div>

              {settings.maintenanceMode && (
                <div className="space-y-2">
                  <Label htmlFor="maintenanceMessage">维护提示信息</Label>
                  <Textarea
                    id="maintenanceMessage"
                    value={settings.maintenanceMessage}
                    onChange={(e) => updateSetting('maintenanceMessage', e.target.value)}
                    placeholder="输入维护提示信息"
                    rows={3}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
