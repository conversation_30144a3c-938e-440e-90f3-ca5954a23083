'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import {
  Shield,
  Save,
  RotateCcw,
  Key,
  Lock,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Globe,
  Loader2,
  RefreshCw,
  Ban
} from 'lucide-react'

// 安全设置接口
interface SecuritySettings {
  // 密码策略
  passwordMinLength: number
  passwordMaxLength: number
  passwordRequireUppercase: boolean
  passwordRequireLowercase: boolean
  passwordRequireNumbers: boolean
  passwordRequireSpecialChars: boolean
  passwordExpiryDays: number
  passwordHistoryCount: number

  // 登录安全
  maxLoginAttempts: number
  lockoutDuration: number // 分钟
  sessionTimeout: number // 分钟
  enableTwoFactor: boolean
  enableCaptcha: boolean

  // IP访问控制
  enableIpWhitelist: boolean
  ipWhitelist: string[]
  enableIpBlacklist: boolean
  ipBlacklist: string[]

  // 审计设置
  enableLoginAudit: boolean
  enableOperationAudit: boolean
  enableDataAccessAudit: boolean
  auditLogRetentionDays: number
}

// 登录尝试记录接口
interface LoginAttempt {
  id: number
  username: string
  ipAddress: string
  userAgent: string
  status: 'SUCCESS' | 'FAILED' | 'BLOCKED'
  failureReason?: string
  attemptTime: string
}

export default function SecuritySettingsPage() {
  const [settings, setSettings] = useState<SecuritySettings>({
    passwordMinLength: 8,
    passwordMaxLength: 20,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSpecialChars: true,
    passwordExpiryDays: 90,
    passwordHistoryCount: 5,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    sessionTimeout: 30,
    enableTwoFactor: false,
    enableCaptcha: true,
    enableIpWhitelist: false,
    ipWhitelist: [],
    enableIpBlacklist: false,
    ipBlacklist: [],
    enableLoginAudit: true,
    enableOperationAudit: true,
    enableDataAccessAudit: true,
    auditLogRetentionDays: 180,
  })

  const [loginAttempts, setLoginAttempts] = useState<LoginAttempt[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [newIpAddress, setNewIpAddress] = useState('')
  const [ipListType, setIpListType] = useState<'whitelist' | 'blacklist'>('whitelist')
  const { toast } = useToast()

  // 获取安全设置
  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '错误',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/settings/security', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setSettings(result.data.settings)
        setLoginAttempts(result.data.loginAttempts || [])
      } else {
        // 使用模拟数据
        const mockLoginAttempts: LoginAttempt[] = [
          {
            id: 1,
            username: 'admin',
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            status: 'SUCCESS',
            attemptTime: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
          },
          {
            id: 2,
            username: 'test_user',
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            status: 'FAILED',
            failureReason: '密码错误',
            attemptTime: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          },
          {
            id: 3,
            username: 'unknown',
            ipAddress: '*********',
            userAgent: 'curl/7.68.0',
            status: 'BLOCKED',
            failureReason: 'IP地址被阻止',
            attemptTime: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          },
        ]

        setLoginAttempts(mockLoginAttempts)

        toast({
          title: '获取安全设置失败',
          description: result.message + '（已加载默认设置）',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('获取安全设置失败:', error)
      toast({
        title: '获取设置失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 更新设置
  const updateSetting = (field: keyof SecuritySettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
    setHasChanges(true)
  }

  // 添加IP地址
  const addIpAddress = () => {
    if (!newIpAddress.trim()) {
      toast({
        title: '添加失败',
        description: 'IP地址不能为空',
        variant: 'destructive',
      })
      return
    }

    // 简单的IP地址格式验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/
    if (!ipRegex.test(newIpAddress.trim())) {
      toast({
        title: '添加失败',
        description: 'IP地址格式不正确',
        variant: 'destructive',
      })
      return
    }

    const field = ipListType === 'whitelist' ? 'ipWhitelist' : 'ipBlacklist'
    const currentList = settings[field]

    if (currentList.includes(newIpAddress.trim())) {
      toast({
        title: '添加失败',
        description: 'IP地址已存在',
        variant: 'destructive',
      })
      return
    }

    updateSetting(field, [...currentList, newIpAddress.trim()])
    setNewIpAddress('')

    toast({
      title: '添加成功',
      description: `IP地址已添加到${ipListType === 'whitelist' ? '白名单' : '黑名单'}`,
    })
  }

  // 移除IP地址
  const removeIpAddress = (ip: string, listType: 'whitelist' | 'blacklist') => {
    const field = listType === 'whitelist' ? 'ipWhitelist' : 'ipBlacklist'
    const currentList = settings[field]
    updateSetting(field, currentList.filter(item => item !== ip))

    toast({
      title: '移除成功',
      description: `IP地址已从${listType === 'whitelist' ? '白名单' : '黑名单'}移除`,
    })
  }

  // 保存设置
  const saveSettings = async () => {
    try {
      setIsSaving(true)
      const token = localStorage.getItem('access_token')

      if (!token) {
        toast({
          title: '保存失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/settings/security', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: '保存成功',
          description: '安全设置已更新',
        })
        setHasChanges(false)
      } else {
        toast({
          title: '保存失败',
          description: result.message,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      toast({
        title: '保存失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN')
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return 'bg-green-100 text-green-800'
      case 'FAILED':
        return 'bg-red-100 text-red-800'
      case 'BLOCKED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4" />
      case 'FAILED':
        return <XCircle className="h-4 w-4" />
      case 'BLOCKED':
        return <Ban className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return '成功'
      case 'FAILED':
        return '失败'
      case 'BLOCKED':
        return '阻止'
      default:
        return status
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN']}>
        <MainLayout>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">加载安全设置...</span>
          </div>
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">安全设置</h1>
              <p className="text-muted-foreground">
                配置系统安全策略和访问控制
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {hasChanges && (
                <Button variant="outline" onClick={() => window.location.reload()}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
              )}
              <Button
                onClick={saveSettings}
                disabled={!hasChanges || isSaving}
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                保存设置
              </Button>
            </div>
          </div>

          {/* 更改提示 */}
          {hasChanges && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2 text-yellow-800">
                  <AlertTriangle className="h-5 w-5" />
                  <span>您有未保存的更改，请记得保存设置</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 密码策略 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>密码策略</span>
              </CardTitle>
              <CardDescription>
                配置用户密码安全要求
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">最小长度</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    value={settings.passwordMinLength}
                    onChange={(e) => updateSetting('passwordMinLength', parseInt(e.target.value) || 8)}
                    min="6"
                    max="20"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordMaxLength">最大长度</Label>
                  <Input
                    id="passwordMaxLength"
                    type="number"
                    value={settings.passwordMaxLength}
                    onChange={(e) => updateSetting('passwordMaxLength', parseInt(e.target.value) || 20)}
                    min="8"
                    max="50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordExpiryDays">过期天数</Label>
                  <Input
                    id="passwordExpiryDays"
                    type="number"
                    value={settings.passwordExpiryDays}
                    onChange={(e) => updateSetting('passwordExpiryDays', parseInt(e.target.value) || 90)}
                    min="30"
                    max="365"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordHistoryCount">历史记录数</Label>
                  <Input
                    id="passwordHistoryCount"
                    type="number"
                    value={settings.passwordHistoryCount}
                    onChange={(e) => updateSetting('passwordHistoryCount', parseInt(e.target.value) || 5)}
                    min="3"
                    max="10"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="passwordRequireUppercase"
                      checked={settings.passwordRequireUppercase}
                      onCheckedChange={(checked) => updateSetting('passwordRequireUppercase', checked)}
                    />
                    <Label htmlFor="passwordRequireUppercase">要求大写字母</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="passwordRequireLowercase"
                      checked={settings.passwordRequireLowercase}
                      onCheckedChange={(checked) => updateSetting('passwordRequireLowercase', checked)}
                    />
                    <Label htmlFor="passwordRequireLowercase">要求小写字母</Label>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="passwordRequireNumbers"
                      checked={settings.passwordRequireNumbers}
                      onCheckedChange={(checked) => updateSetting('passwordRequireNumbers', checked)}
                    />
                    <Label htmlFor="passwordRequireNumbers">要求数字</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="passwordRequireSpecialChars"
                      checked={settings.passwordRequireSpecialChars}
                      onCheckedChange={(checked) => updateSetting('passwordRequireSpecialChars', checked)}
                    />
                    <Label htmlFor="passwordRequireSpecialChars">要求特殊字符</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 登录安全 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lock className="h-5 w-5" />
                <span>登录安全</span>
              </CardTitle>
              <CardDescription>
                配置登录相关安全策略
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">最大登录尝试次数</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    value={settings.maxLoginAttempts}
                    onChange={(e) => updateSetting('maxLoginAttempts', parseInt(e.target.value) || 5)}
                    min="3"
                    max="10"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lockoutDuration">锁定时长（分钟）</Label>
                  <Input
                    id="lockoutDuration"
                    type="number"
                    value={settings.lockoutDuration}
                    onChange={(e) => updateSetting('lockoutDuration', parseInt(e.target.value) || 30)}
                    min="5"
                    max="1440"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时（分钟）</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.sessionTimeout}
                    onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value) || 30)}
                    min="5"
                    max="480"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableTwoFactor"
                    checked={settings.enableTwoFactor}
                    onCheckedChange={(checked) => updateSetting('enableTwoFactor', checked)}
                  />
                  <Label htmlFor="enableTwoFactor">启用双因素认证</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableCaptcha"
                    checked={settings.enableCaptcha}
                    onCheckedChange={(checked) => updateSetting('enableCaptcha', checked)}
                  />
                  <Label htmlFor="enableCaptcha">启用验证码</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}