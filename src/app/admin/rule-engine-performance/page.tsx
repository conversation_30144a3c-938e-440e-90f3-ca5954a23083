'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import {
  Activity,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  TrendingUp,
  Target,
  Cpu,
  BarChart3
} from 'lucide-react'

interface PerformanceTestResult {
  ruleCount: number
  report: string
  timestamp: string
}

export default function RuleEnginePerformancePage() {
  const { toast } = useToast()
  const [isRunning, setIsRunning] = useState(false)
  const [testResult, setTestResult] = useState<PerformanceTestResult | null>(null)
  const [ruleCount, setRuleCount] = useState(10)

  const runPerformanceTest = async () => {
    try {
      setIsRunning(true)
      
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '认证失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch(`/api/supervision-rules/performance-test?ruleCount=${ruleCount}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        setTestResult(result.data)
        toast({
          title: '性能测试完成',
          description: `测试了 ${result.data.ruleCount} 个规则`,
        })
      } else {
        toast({
          title: '性能测试失败',
          description: result.error,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('运行性能测试失败:', error)
      toast({
        title: '性能测试失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsRunning(false)
    }
  }

  const parseReportMetrics = (report: string) => {
    const metrics = {
      executionTimeImprovement: 0,
      throughputImprovement: 0,
      successRate: 0,
      meetsTargets: false,
      bestEngine: 'unknown',
      maxThroughput: 0
    }

    try {
      // 解析报告中的关键指标
      const executionTimeMatch = report.match(/执行时间改进: ([\d.]+)%/)
      if (executionTimeMatch && executionTimeMatch[1]) {
        metrics.executionTimeImprovement = parseFloat(executionTimeMatch[1])
      }

      const throughputMatch = report.match(/吞吐量改进: ([\d.]+)%/)
      if (throughputMatch && throughputMatch[1]) {
        metrics.throughputImprovement = parseFloat(throughputMatch[1])
      }

      const successRateMatch = report.match(/成功率: ([\d.]+)%/)
      if (successRateMatch && successRateMatch[1]) {
        metrics.successRate = parseFloat(successRateMatch[1])
      }

      const meetsTargetsMatch = report.match(/是否达到目标: (.+)/)
      if (meetsTargetsMatch && meetsTargetsMatch[1]) {
        metrics.meetsTargets = meetsTargetsMatch[1].includes('✅')
      }

      const bestEngineMatch = report.match(/最佳引擎: (.+)/)
      if (bestEngineMatch && bestEngineMatch[1]) {
        metrics.bestEngine = bestEngineMatch[1].trim()
      }

      const maxThroughputMatch = report.match(/最大吞吐量: ([\d.]+) 规则\/秒/)
      if (maxThroughputMatch && maxThroughputMatch[1]) {
        metrics.maxThroughput = parseFloat(maxThroughputMatch[1])
      }
    } catch (error) {
      console.error('解析报告指标失败:', error)
    }

    return metrics
  }

  const reportMetrics = testResult ? parseReportMetrics(testResult.report) : null

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">规则引擎性能监控</h1>
              <p className="text-muted-foreground">
                监控和测试规则引擎性能，验证P0-004优化目标
              </p>
            </div>
          </div>

          {/* 测试控制面板 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>性能测试控制台</span>
              </CardTitle>
              <CardDescription>
                运行规则引擎性能测试，比较不同引擎的性能表现
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-end space-x-4">
                <div className="space-y-2">
                  <Label htmlFor="ruleCount">测试规则数量</Label>
                  <Input
                    id="ruleCount"
                    type="number"
                    min="1"
                    max="100"
                    value={ruleCount}
                    onChange={(e) => setRuleCount(parseInt(e.target.value) || 10)}
                    className="w-32"
                  />
                </div>
                <Button onClick={runPerformanceTest} disabled={isRunning}>
                  {isRunning ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Activity className="h-4 w-4 mr-2" />
                  )}
                  {isRunning ? '测试中...' : '运行性能测试'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 性能指标概览 */}
          {reportMetrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4" />
                      <span>执行时间改进</span>
                    </span>
                    {reportMetrics.executionTimeImprovement >= 70 ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {reportMetrics.executionTimeImprovement.toFixed(1)}%
                    </div>
                    <Progress value={Math.min(reportMetrics.executionTimeImprovement, 100)} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      目标: ≥70% {reportMetrics.executionTimeImprovement >= 70 ? '✅' : '❌'}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <BarChart3 className="h-4 w-4" />
                      <span>吞吐量改进</span>
                    </span>
                    {reportMetrics.throughputImprovement > 0 ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {reportMetrics.throughputImprovement.toFixed(1)}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {reportMetrics.maxThroughput.toFixed(2)} 规则/秒
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <Target className="h-4 w-4" />
                      <span>成功率</span>
                    </span>
                    {reportMetrics.successRate >= 99 ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {reportMetrics.successRate.toFixed(1)}%
                    </div>
                    <Progress value={reportMetrics.successRate} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      目标: ≥99% {reportMetrics.successRate >= 99 ? '✅' : '❌'}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <Cpu className="h-4 w-4" />
                      <span>最佳引擎</span>
                    </span>
                    {reportMetrics.meetsTargets ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-lg font-medium">
                      {reportMetrics.bestEngine}
                    </div>
                    <Badge variant={reportMetrics.meetsTargets ? "default" : "destructive"}>
                      {reportMetrics.meetsTargets ? '达到目标' : '未达目标'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* P0-004 目标达成情况 */}
          {reportMetrics && (
            <Card>
              <CardHeader>
                <CardTitle>P0-004 目标达成情况</CardTitle>
                <CardDescription>
                  监管规则引擎性能优化目标验证
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span>规则执行效率提升70%</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{reportMetrics.executionTimeImprovement.toFixed(1)}%</span>
                      {reportMetrics.executionTimeImprovement >= 70 ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">已达成</Badge>
                      ) : (
                        <Badge variant="destructive">未达成</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span>支持并行执行5-10个规则</span>
                    <Badge variant="default" className="bg-green-100 text-green-800">已达成</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span>规则执行错误率 &lt; 1%</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{(100 - reportMetrics.successRate).toFixed(1)}%</span>
                      {reportMetrics.successRate >= 99 ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">已达成</Badge>
                      ) : (
                        <Badge variant="destructive">未达成</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 详细测试报告 */}
          {testResult && (
            <Card>
              <CardHeader>
                <CardTitle>详细测试报告</CardTitle>
                <CardDescription>
                  测试时间: {new Date(testResult.timestamp).toLocaleString()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-lg overflow-auto max-h-96">
                  {testResult.report}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
