'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import {
  Activity,
  Database,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  TrendingUp,
  Target
} from 'lucide-react'

interface PerformanceMetric {
  queryType: string
  executionTime: number
  recordCount: number
  cacheHit: boolean
  timestamp: Date
  status: 'success' | 'error'
  errorMessage?: string
}

interface PerformanceBenchmark {
  medicalCaseQuery: PerformanceMetric
  ruleExecutionLogQuery: PerformanceMetric
  userQuery: PerformanceMetric
  overallScore: number
  recommendations: string[]
}

export default function PerformancePage() {
  const { toast } = useToast()
  const [isRunning, setIsRunning] = useState(false)
  const [benchmark, setBenchmark] = useState<PerformanceBenchmark | null>(null)

  const runBenchmark = async () => {
    try {
      setIsRunning(true)
      
      const response = await fetch('/api/performance/benchmark')
      const result = await response.json()
      
      if (result.success) {
        setBenchmark(result.data)
        toast({
          title: '性能测试完成',
          description: `总体评分: ${result.data.overallScore}/100`,
        })
      } else {
        toast({
          title: '性能测试失败',
          description: result.error,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('运行性能测试失败:', error)
      toast({
        title: '性能测试失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: string, executionTime: number, target: number) => {
    if (status === 'error') {
      return <XCircle className="h-4 w-4 text-red-500" />
    } else if (executionTime <= target) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string, executionTime: number, target: number) => {
    if (status === 'error') {
      return <Badge variant="destructive">错误</Badge>
    } else if (executionTime <= target) {
      return <Badge variant="default" className="bg-green-100 text-green-800">优秀</Badge>
    } else if (executionTime <= target * 1.5) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">一般</Badge>
    } else {
      return <Badge variant="destructive">需优化</Badge>
    }
  }

  const performanceTargets = {
    medical_case_query: 500,
    rule_execution_log_query: 300,
    user_query: 200,
  }

  const getQueryDisplayName = (queryType: string) => {
    const names = {
      medical_case_query: '医疗案例查询',
      rule_execution_log_query: '规则执行日志查询',
      user_query: '用户查询',
    }
    return names[queryType as keyof typeof names] || queryType
  }

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">数据库性能监控</h1>
              <p className="text-muted-foreground">
                监控和优化数据库查询性能，确保系统响应速度
              </p>
            </div>
            <Button onClick={runBenchmark} disabled={isRunning}>
              {isRunning ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Activity className="h-4 w-4 mr-2" />
              )}
              {isRunning ? '测试中...' : '运行性能测试'}
            </Button>
          </div>

          {/* 总体评分卡片 */}
          {benchmark && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>总体性能评分</span>
                </CardTitle>
                <CardDescription>
                  基于查询响应时间的综合评分
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <Progress value={benchmark.overallScore} className="h-3" />
                  </div>
                  <div className="text-2xl font-bold">
                    {benchmark.overallScore}/100
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {benchmark.overallScore >= 80 ? '性能优秀' : 
                   benchmark.overallScore >= 60 ? '性能良好' : '需要优化'}
                </p>
              </CardContent>
            </Card>
          )}

          {/* 详细性能指标 */}
          {benchmark && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                benchmark.medicalCaseQuery,
                benchmark.ruleExecutionLogQuery,
                benchmark.userQuery
              ].map((metric) => {
                const target = performanceTargets[metric.queryType as keyof typeof performanceTargets]
                return (
                  <Card key={metric.queryType}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center justify-between text-base">
                        <span className="flex items-center space-x-2">
                          <Database className="h-4 w-4" />
                          <span>{getQueryDisplayName(metric.queryType)}</span>
                        </span>
                        {getStatusIcon(metric.status, metric.executionTime, target)}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">执行时间</span>
                          <div className="flex items-center space-x-2">
                            <Clock className="h-3 w-3" />
                            <span className="font-medium">{metric.executionTime}ms</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">目标时间</span>
                          <div className="flex items-center space-x-2">
                            <Target className="h-3 w-3" />
                            <span className="text-sm">{target}ms</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">记录数量</span>
                          <span className="font-medium">{metric.recordCount.toLocaleString()}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">状态</span>
                          {getStatusBadge(metric.status, metric.executionTime, target)}
                        </div>
                        
                        {metric.status === 'error' && metric.errorMessage && (
                          <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                            {metric.errorMessage}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}

          {/* 优化建议 */}
          {benchmark && benchmark.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>优化建议</CardTitle>
                <CardDescription>
                  基于性能测试结果的优化建议
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {benchmark.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-2 text-sm">
                      <span className="text-muted-foreground">{index + 1}.</span>
                      <span>{recommendation}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 性能目标说明 */}
          <Card>
            <CardHeader>
              <CardTitle>性能目标</CardTitle>
              <CardDescription>
                各类查询的性能目标和优化标准
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">医疗案例查询</h4>
                  <p className="text-sm text-muted-foreground">目标: &lt; 500ms</p>
                  <p className="text-xs text-muted-foreground">
                    包括分页、筛选、排序等复杂查询
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">规则执行日志查询</h4>
                  <p className="text-sm text-muted-foreground">目标: &lt; 300ms</p>
                  <p className="text-xs text-muted-foreground">
                    监管规则执行历史和状态查询
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">用户查询</h4>
                  <p className="text-sm text-muted-foreground">目标: &lt; 200ms</p>
                  <p className="text-xs text-muted-foreground">
                    用户列表、搜索、权限查询
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
