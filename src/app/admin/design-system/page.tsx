'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  StandardDataCard, 
  StandardStatusBadge, 
  StandardLoadingState 
} from '@/components/ui/standardized-components'
import {
  Palette,
  Component,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Zap,
  Eye,
  Code,
  Layers,
  Sparkles,
  Target,
  TrendingUp,
  Clock
} from 'lucide-react'

interface DesignSystemMetrics {
  overallScore: number
  componentCompliance: number
  tokenUsage: number
  accessibilityScore: number
  issues: {
    total: number
    high: number
    medium: number
    low: number
  }
  components: {
    shadcnComponents: number
    customComponents: number
    totalComponents: number
  }
  tokens: {
    designTokens: number
    hardcodedValues: number
    totalValues: number
  }
}

export default function DesignSystemPage() {
  const { toast } = useToast()
  const [metrics, setMetrics] = useState<DesignSystemMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isValidating, setIsValidating] = useState(false)

  useEffect(() => {
    loadDesignSystemMetrics()
  }, [])

  const loadDesignSystemMetrics = async () => {
    try {
      setIsLoading(true)
      
      // 模拟加载设计系统指标数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockMetrics: DesignSystemMetrics = {
        overallScore: 96,
        componentCompliance: 98,
        tokenUsage: 94,
        accessibilityScore: 92,
        issues: {
          total: 8,
          high: 1,
          medium: 3,
          low: 4
        },
        components: {
          shadcnComponents: 45,
          customComponents: 3,
          totalComponents: 48
        },
        tokens: {
          designTokens: 156,
          hardcodedValues: 12,
          totalValues: 168
        }
      }

      setMetrics(mockMetrics)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载设计系统指标数据',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const runDesignValidation = async () => {
    try {
      setIsValidating(true)
      
      // 模拟运行设计验证
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast({
        title: '验证完成',
        description: '设计系统验证已完成，发现8个问题'
      })
      
      // 重新加载指标
      await loadDesignSystemMetrics()
    } catch (error) {
      toast({
        title: '验证失败',
        description: '设计系统验证失败',
        variant: 'destructive'
      })
    } finally {
      setIsValidating(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600'
    if (score >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadge = (score: number) => {
    if (score >= 95) return <StandardStatusBadge status="success">优秀</StandardStatusBadge>
    if (score >= 85) return <StandardStatusBadge status="warning">良好</StandardStatusBadge>
    return <StandardStatusBadge status="error">需改进</StandardStatusBadge>
  }

  if (isLoading) {
    return (
      <RouteGuard requiredRoles={['ADMIN']}>
        <MainLayout>
          <StandardLoadingState message="加载设计系统数据..." />
        </MainLayout>
      </RouteGuard>
    )
  }

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
                <Palette className="h-8 w-8 text-blue-600" />
                设计系统管理
              </h1>
              <p className="text-muted-foreground">
                shadcn/UI组件标准化监控，验证P1-001设计一致性目标
              </p>
            </div>
            <Button onClick={runDesignValidation} disabled={isValidating}>
              {isValidating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  验证中...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  运行验证
                </>
              )}
            </Button>
          </div>

          {/* 设计系统指标概览 */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StandardDataCard
                title="设计一致性评分"
                value={`${metrics.overallScore}分`}
                icon={Target}
                trend={{ value: 2, isPositive: true }}
                className="border-l-4 border-l-blue-500"
              />

              <StandardDataCard
                title="组件合规率"
                value={`${metrics.componentCompliance}%`}
                icon={Component}
                trend={{ value: 1, isPositive: true }}
                className="border-l-4 border-l-green-500"
              />

              <StandardDataCard
                title="设计Token使用率"
                value={`${metrics.tokenUsage}%`}
                icon={Palette}
                trend={{ value: 3, isPositive: true }}
                className="border-l-4 border-l-purple-500"
              />

              <StandardDataCard
                title="可访问性评分"
                value={`${metrics.accessibilityScore}分`}
                icon={Eye}
                trend={{ value: 1, isPositive: false }}
                className="border-l-4 border-l-orange-500"
              />
            </div>
          )}

          {/* P1-001目标达成情况 */}
          <Card>
            <CardHeader>
              <CardTitle>P1-001 shadcn/UI组件标准化目标达成情况</CardTitle>
              <CardDescription>
                验证设计系统标准化的关键指标
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <span>100%使用shadcn/UI标准组件</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{metrics?.componentCompliance || 0}%</span>
                    {(metrics?.componentCompliance || 0) >= 95 ? (
                      <StandardStatusBadge status="success">已达成</StandardStatusBadge>
                    ) : (
                      <StandardStatusBadge status="warning">接近达成</StandardStatusBadge>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <span>设计一致性评分 &gt; 95%</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{metrics?.overallScore || 0}分</span>
                    {(metrics?.overallScore || 0) > 95 ? (
                      <StandardStatusBadge status="success">已达成</StandardStatusBadge>
                    ) : (
                      <StandardStatusBadge status="warning">接近达成</StandardStatusBadge>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <span>移除所有自定义样式覆盖</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{metrics?.components.customComponents || 0} 个自定义组件</span>
                    {(metrics?.components.customComponents || 0) === 0 ? (
                      <StandardStatusBadge status="success">已达成</StandardStatusBadge>
                    ) : (
                      <StandardStatusBadge status="warning">进行中</StandardStatusBadge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 详细分析 */}
          <Tabs defaultValue="components" className="space-y-4">
            <TabsList>
              <TabsTrigger value="components">组件分析</TabsTrigger>
              <TabsTrigger value="tokens">设计Token</TabsTrigger>
              <TabsTrigger value="issues">问题报告</TabsTrigger>
              <TabsTrigger value="guidelines">设计指南</TabsTrigger>
            </TabsList>

            <TabsContent value="components">
              <Card>
                <CardHeader>
                  <CardTitle>组件使用分析</CardTitle>
                  <CardDescription>
                    shadcn/UI组件与自定义组件的使用情况
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {metrics && (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">
                            {metrics.components.shadcnComponents}
                          </div>
                          <div className="text-sm text-green-700">shadcn/UI组件</div>
                        </div>
                        
                        <div className="text-center p-4 bg-yellow-50 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-600">
                            {metrics.components.customComponents}
                          </div>
                          <div className="text-sm text-yellow-700">自定义组件</div>
                        </div>
                        
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">
                            {metrics.components.totalComponents}
                          </div>
                          <div className="text-sm text-blue-700">总组件数</div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>组件合规率</span>
                          <span>{metrics.componentCompliance}%</span>
                        </div>
                        <Progress value={metrics.componentCompliance} className="h-2" />
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tokens">
              <Card>
                <CardHeader>
                  <CardTitle>设计Token使用分析</CardTitle>
                  <CardDescription>
                    设计token与硬编码值的使用情况
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {metrics && (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">
                            {metrics.tokens.designTokens}
                          </div>
                          <div className="text-sm text-green-700">设计Token</div>
                        </div>
                        
                        <div className="text-center p-4 bg-red-50 rounded-lg">
                          <div className="text-2xl font-bold text-red-600">
                            {metrics.tokens.hardcodedValues}
                          </div>
                          <div className="text-sm text-red-700">硬编码值</div>
                        </div>
                        
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">
                            {metrics.tokens.totalValues}
                          </div>
                          <div className="text-sm text-blue-700">总样式值</div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Token使用率</span>
                          <span>{metrics.tokenUsage}%</span>
                        </div>
                        <Progress value={metrics.tokenUsage} className="h-2" />
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="issues">
              <Card>
                <CardHeader>
                  <CardTitle>设计问题报告</CardTitle>
                  <CardDescription>
                    当前发现的设计一致性问题
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {metrics && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="text-xl font-bold">{metrics.issues.total}</div>
                          <div className="text-sm text-gray-600">总问题</div>
                        </div>
                        <div className="text-center p-3 bg-red-50 rounded-lg">
                          <div className="text-xl font-bold text-red-600">{metrics.issues.high}</div>
                          <div className="text-sm text-red-700">高优先级</div>
                        </div>
                        <div className="text-center p-3 bg-yellow-50 rounded-lg">
                          <div className="text-xl font-bold text-yellow-600">{metrics.issues.medium}</div>
                          <div className="text-sm text-yellow-700">中优先级</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="text-xl font-bold text-green-600">{metrics.issues.low}</div>
                          <div className="text-sm text-green-700">低优先级</div>
                        </div>
                      </div>
                      
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          发现 {metrics.issues.high} 个高优先级问题需要立即处理，
                          {metrics.issues.medium} 个中优先级问题建议尽快修复。
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="guidelines">
              <Card>
                <CardHeader>
                  <CardTitle>设计系统指南</CardTitle>
                  <CardDescription>
                    shadcn/UI组件使用最佳实践
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      推荐做法
                    </h4>
                    <ul className="space-y-2 text-sm text-muted-foreground ml-6">
                      <li>• 优先使用shadcn/UI提供的标准组件</li>
                      <li>• 使用设计token而非硬编码值</li>
                      <li>• 遵循组件的标准变体和尺寸</li>
                      <li>• 保持一致的间距和颜色使用</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <XCircle className="h-4 w-4 text-red-600" />
                      避免做法
                    </h4>
                    <ul className="space-y-2 text-sm text-muted-foreground ml-6">
                      <li>• 避免创建自定义组件替代标准组件</li>
                      <li>• 避免使用内联样式</li>
                      <li>• 避免硬编码颜色和间距值</li>
                      <li>• 避免覆盖组件的默认样式</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
