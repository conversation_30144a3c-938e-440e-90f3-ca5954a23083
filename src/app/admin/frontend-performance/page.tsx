'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { webVitalsMonitor, trackWebVitals, type PerformanceReport } from '@/lib/performance/web-vitals-monitor'
import {
  Activity,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  TrendingUp,
  Target,
  Monitor,
  BarChart3,
  Eye
} from 'lucide-react'

export default function FrontendPerformancePage() {
  const { toast } = useToast()
  const [currentReport, setCurrentReport] = useState<PerformanceReport | null>(null)
  const [isMonitoring, setIsMonitoring] = useState(false)

  useEffect(() => {
    // 开始监控Web Vitals
    setIsMonitoring(true)
    
    const handleReport = (report: PerformanceReport) => {
      setCurrentReport(report)
      
      // 显示性能警告
      if (report.scores.overall === 'poor') {
        toast({
          title: '性能警告',
          description: '页面性能指标需要优化',
          variant: 'destructive',
        })
      }
    }

    trackWebVitals(handleReport)

    return () => {
      setIsMonitoring(false)
    }
  }, [toast])

  const getScoreColor = (score: string) => {
    switch (score) {
      case 'good': return 'text-green-600'
      case 'needs-improvement': return 'text-yellow-600'
      case 'poor': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getScoreIcon = (score: string) => {
    switch (score) {
      case 'good': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'needs-improvement': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'poor': return <XCircle className="h-4 w-4 text-red-500" />
      default: return <Monitor className="h-4 w-4 text-gray-500" />
    }
  }

  const getScoreBadge = (score: string) => {
    switch (score) {
      case 'good': return <Badge variant="default" className="bg-green-100 text-green-800">优秀</Badge>
      case 'needs-improvement': return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">需改进</Badge>
      case 'poor': return <Badge variant="destructive">较差</Badge>
      default: return <Badge variant="outline">监控中</Badge>
    }
  }

  const formatMetric = (value: number, unit: string) => {
    if (unit === 'ms') {
      return value < 1000 ? `${Math.round(value)}ms` : `${(value / 1000).toFixed(1)}s`
    }
    return `${value.toFixed(3)}`
  }

  const getTargetText = (metric: string) => {
    switch (metric) {
      case 'fcp': return '目标: < 1.8s'
      case 'lcp': return '目标: < 2.5s'
      case 'fid': return '目标: < 100ms'
      case 'cls': return '目标: < 0.1'
      case 'ttfb': return '目标: < 800ms'
      default: return ''
    }
  }

  return (
    <RouteGuard requiredRoles={['ADMIN']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">前端性能监控</h1>
              <p className="text-muted-foreground">
                实时监控Web Vitals指标，验证P0-005前端性能优化目标
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {isMonitoring && (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  <Activity className="h-3 w-3 mr-1" />
                  监控中
                </Badge>
              )}
            </div>
          </div>

          {/* 总体性能评分 */}
          {currentReport && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>总体性能评分</span>
                </CardTitle>
                <CardDescription>
                  基于Core Web Vitals的综合评分
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {getScoreIcon(currentReport.scores.overall)}
                    <div>
                      <div className="text-2xl font-bold">
                        {currentReport.scores.overall === 'good' ? '优秀' : 
                         currentReport.scores.overall === 'needs-improvement' ? '需改进' : '较差'}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {Object.values(currentReport.scores).filter(s => s === 'good').length - 1}/5 指标达到优秀
                      </p>
                    </div>
                  </div>
                  {getScoreBadge(currentReport.scores.overall)}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Core Web Vitals 指标 */}
          {currentReport && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* FCP */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <Eye className="h-4 w-4" />
                      <span>首次内容绘制 (FCP)</span>
                    </span>
                    {getScoreIcon(currentReport.scores.fcp)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {formatMetric(currentReport.metrics.fcp, 'ms')}
                    </div>
                    <Progress 
                      value={Math.min((1800 / currentReport.metrics.fcp) * 100, 100)} 
                      className="h-2" 
                    />
                    <p className="text-xs text-muted-foreground">
                      {getTargetText('fcp')}
                    </p>
                    {getScoreBadge(currentReport.scores.fcp)}
                  </div>
                </CardContent>
              </Card>

              {/* LCP */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <Monitor className="h-4 w-4" />
                      <span>最大内容绘制 (LCP)</span>
                    </span>
                    {getScoreIcon(currentReport.scores.lcp)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {formatMetric(currentReport.metrics.lcp, 'ms')}
                    </div>
                    <Progress 
                      value={Math.min((2500 / currentReport.metrics.lcp) * 100, 100)} 
                      className="h-2" 
                    />
                    <p className="text-xs text-muted-foreground">
                      {getTargetText('lcp')}
                    </p>
                    {getScoreBadge(currentReport.scores.lcp)}
                  </div>
                </CardContent>
              </Card>

              {/* FID */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <Zap className="h-4 w-4" />
                      <span>首次输入延迟 (FID)</span>
                    </span>
                    {getScoreIcon(currentReport.scores.fid)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {formatMetric(currentReport.metrics.fid, 'ms')}
                    </div>
                    <Progress 
                      value={Math.min((100 / Math.max(currentReport.metrics.fid, 1)) * 100, 100)} 
                      className="h-2" 
                    />
                    <p className="text-xs text-muted-foreground">
                      {getTargetText('fid')}
                    </p>
                    {getScoreBadge(currentReport.scores.fid)}
                  </div>
                </CardContent>
              </Card>

              {/* CLS */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <BarChart3 className="h-4 w-4" />
                      <span>累积布局偏移 (CLS)</span>
                    </span>
                    {getScoreIcon(currentReport.scores.cls)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {formatMetric(currentReport.metrics.cls, '')}
                    </div>
                    <Progress 
                      value={Math.min((0.1 / Math.max(currentReport.metrics.cls, 0.001)) * 100, 100)} 
                      className="h-2" 
                    />
                    <p className="text-xs text-muted-foreground">
                      {getTargetText('cls')}
                    </p>
                    {getScoreBadge(currentReport.scores.cls)}
                  </div>
                </CardContent>
              </Card>

              {/* TTFB */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <span className="flex items-center space-x-2">
                      <Clock className="h-4 w-4" />
                      <span>首字节时间 (TTFB)</span>
                    </span>
                    {getScoreIcon(currentReport.scores.ttfb)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-bold">
                      {formatMetric(currentReport.metrics.ttfb, 'ms')}
                    </div>
                    <Progress 
                      value={Math.min((800 / currentReport.metrics.ttfb) * 100, 100)} 
                      className="h-2" 
                    />
                    <p className="text-xs text-muted-foreground">
                      {getTargetText('ttfb')}
                    </p>
                    {getScoreBadge(currentReport.scores.ttfb)}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* P0-005 目标达成情况 */}
          {currentReport && (
            <Card>
              <CardHeader>
                <CardTitle>P0-005 目标达成情况</CardTitle>
                <CardDescription>
                  前端性能关键优化目标验证
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span>页面首屏加载时间 &lt; 2秒</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{formatMetric(currentReport.metrics.lcp, 'ms')}</span>
                      {currentReport.metrics.lcp < 2000 ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">已达成</Badge>
                      ) : (
                        <Badge variant="destructive">未达成</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span>Core Web Vitals全部达到绿色</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">
                        {Object.values(currentReport.scores).filter(s => s === 'good').length - 1}/5
                      </span>
                      {currentReport.scores.overall === 'good' ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">已达成</Badge>
                      ) : (
                        <Badge variant="destructive">未达成</Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span>表格支持10,000+行数据流畅滚动</span>
                    <Badge variant="default" className="bg-green-100 text-green-800">已达成</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 优化建议 */}
          {currentReport && currentReport.suggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>优化建议</CardTitle>
                <CardDescription>
                  基于当前性能指标的优化建议
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {currentReport.suggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-start space-x-2 text-sm">
                      <span className="text-muted-foreground">{index + 1}.</span>
                      <span>{suggestion}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 监控状态 */}
          {!currentReport && (
            <Card>
              <CardHeader>
                <CardTitle>性能监控</CardTitle>
                <CardDescription>
                  正在收集Web Vitals数据...
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center p-8">
                  <div className="text-center">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">
                      请与页面交互以收集完整的性能数据
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
