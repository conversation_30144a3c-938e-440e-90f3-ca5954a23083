'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  LayoutDashboard, 
  Palette, 
  Smartphone, 
  Monitor, 
  Tablet,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'

export default function LayoutTestPage() {
  return (
    <div className="space-y-6">
      {/* 页面介绍 */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">布局系统测试</h1>
        <p className="text-muted-foreground">
          测试新的布局系统功能，包括响应式设计、主题切换、导航等特性
        </p>
      </div>

      {/* 布局特性展示 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LayoutDashboard className="w-5 h-5" />
              自适应布局
            </CardTitle>
            <CardDescription>
              智能布局系统，根据页面类型自动选择合适的布局
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">主布局（仪表板页面）</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">认证布局（登录注册）</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">加载布局（系统初始化）</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">最小布局（首页重定向）</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              主题系统
            </CardTitle>
            <CardDescription>
              支持明暗主题切换，优化的色彩系统
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">明亮主题</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">暗黑主题</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">系统跟随</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm">自定义主题</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              响应式设计
            </CardTitle>
            <CardDescription>
              适配不同设备尺寸，优化用户体验
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2">
              <Monitor className="w-4 h-4 text-blue-500" />
              <span className="text-sm">桌面端（1200px+）</span>
            </div>
            <div className="flex items-center gap-2">
              <Tablet className="w-4 h-4 text-green-500" />
              <span className="text-sm">平板端（768px-1199px）</span>
            </div>
            <div className="flex items-center gap-2">
              <Smartphone className="w-4 h-4 text-orange-500" />
              <span className="text-sm">移动端（&lt;768px）</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* 导航功能测试 */}
      <Card>
        <CardHeader>
          <CardTitle>导航功能测试</CardTitle>
          <CardDescription>
            测试新布局系统中的各种导航功能
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <h4 className="font-medium">侧边栏导航</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div>• 可折叠侧边栏</div>
                <div>• 图标模式切换</div>
                <div>• 权限控制</div>
                <div>• 活跃状态指示</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">标签页导航</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div>• 多标签页支持</div>
                <div>• 标签页关闭</div>
                <div>• 状态保持</div>
                <div>• 快捷键支持</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">面包屑导航</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div>• 路径显示</div>
                <div>• 快速跳转</div>
                <div>• 自动生成</div>
                <div>• 响应式隐藏</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">快速操作</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div>• 页面相关操作</div>
                <div>• 批量操作</div>
                <div>• 快捷键绑定</div>
                <div>• 权限控制</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 功能状态 */}
      <Card>
        <CardHeader>
          <CardTitle>功能实现状态</CardTitle>
          <CardDescription>
            当前布局系统各功能的实现状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-medium text-green-600">已完成功能</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">布局提供者组件</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">认证布局组件</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">加载布局组件</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">主布局重构</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">面包屑导航</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">通知中心</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">用户资料组件</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-orange-600">待优化功能</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                  <span className="text-sm">页面过渡动画</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                  <span className="text-sm">键盘快捷键</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                  <span className="text-sm">无障碍支持</span>
                </div>
                <div className="flex items-center gap-2">
                  <Info className="w-4 h-4 text-blue-500" />
                  <span className="text-sm">性能优化</span>
                </div>
                <div className="flex items-center gap-2">
                  <Info className="w-4 h-4 text-blue-500" />
                  <span className="text-sm">SEO优化</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>功能测试</CardTitle>
          <CardDescription>
            点击按钮测试各种布局功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button onClick={() => window.location.reload()}>
              刷新页面
            </Button>
            <Button variant="outline" onClick={() => window.history.back()}>
              返回上页
            </Button>
            <Button variant="outline" onClick={() => console.log('测试通知')}>
              测试通知
            </Button>
            <Button variant="outline" onClick={() => console.log('测试快捷键')}>
              测试快捷键
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
