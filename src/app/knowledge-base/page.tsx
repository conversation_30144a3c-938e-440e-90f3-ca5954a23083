"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { Search, BookOpen, FileText, Tag, Plus, Filter } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// 模拟数据
const knowledgeCategories = [
  {
    id: 1,
    name: '医保政策',
    description: '国家和地方医保政策文件',
    documentCount: 156,
    color: 'bg-blue-500'
  },
  {
    id: 2,
    name: '监管规则',
    description: '医保基金监管相关规则和标准',
    documentCount: 89,
    color: 'bg-green-500'
  },
  {
    id: 3,
    name: '操作指南',
    description: '系统操作和业务流程指南',
    documentCount: 67,
    color: 'bg-purple-500'
  },
  {
    id: 4,
    name: '法律法规',
    description: '相关法律法规和司法解释',
    documentCount: 234,
    color: 'bg-orange-500'
  }
]

const recentDocuments = [
  {
    id: 1,
    title: '医保基金监管办法（2024年修订版）',
    category: '医保政策',
    updateTime: '2024-01-15',
    views: 1234,
    tags: ['政策', '监管', '2024']
  },
  {
    id: 2,
    title: '异地就医结算管理规定',
    category: '医保政策',
    updateTime: '2024-01-10',
    views: 856,
    tags: ['异地就医', '结算']
  },
  {
    id: 3,
    title: '医疗机构违规行为识别指南',
    category: '监管规则',
    updateTime: '2024-01-08',
    views: 2341,
    tags: ['违规', '识别', '指南']
  },
  {
    id: 4,
    title: '系统操作手册v2.0',
    category: '操作指南',
    updateTime: '2024-01-05',
    views: 567,
    tags: ['操作', '手册', 'v2.0']
  }
]

export default function KnowledgeBasePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">知识库</h1>
          <p className="text-muted-foreground">
            医保政策、监管规则和操作指南的集中管理平台
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          新建文档
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索文档、政策、规则..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                {knowledgeCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              高级筛选
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="categories" className="space-y-6">
        <TabsList>
          <TabsTrigger value="categories">分类浏览</TabsTrigger>
          <TabsTrigger value="recent">最近更新</TabsTrigger>
          <TabsTrigger value="popular">热门文档</TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="space-y-6">
          {/* 知识分类 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {knowledgeCategories.map((category) => (
              <Card key={category.id} className="hover:shadow-md transition-shadow cursor-pointer">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${category.color}`} />
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                  </div>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {category.documentCount} 个文档
                    </span>
                    <Link href={`/knowledge-base/categories/${category.id}`}>
                      <Button variant="ghost" size="sm">
                        查看全部
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 快速访问 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                快速访问
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Link href="/knowledge-base/documents">
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="mr-2 h-4 w-4" />
                    文档管理
                  </Button>
                </Link>
                <Link href="/knowledge-base/categories">
                  <Button variant="outline" className="w-full justify-start">
                    <Tag className="mr-2 h-4 w-4" />
                    分类管理
                  </Button>
                </Link>
                <Button variant="outline" className="w-full justify-start">
                  <Search className="mr-2 h-4 w-4" />
                  高级搜索
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-6">
          {/* 最近更新的文档 */}
          <div className="space-y-4">
            {recentDocuments.map((doc) => (
              <Card key={doc.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">{doc.title}</h3>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                        <span>分类: {doc.category}</span>
                        <span>更新: {doc.updateTime}</span>
                        <span>浏览: {doc.views}</span>
                      </div>
                      <div className="flex gap-2">
                        {doc.tags.map((tag) => (
                          <Badge key={tag} variant="secondary">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      查看详情
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="popular" className="space-y-6">
          {/* 热门文档 */}
          <div className="space-y-4">
            {recentDocuments
              .sort((a, b) => b.views - a.views)
              .map((doc, index) => (
                <Card key={doc.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-2">{doc.title}</h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                          <span>分类: {doc.category}</span>
                          <span>浏览: {doc.views}</span>
                        </div>
                        <div className="flex gap-2">
                          {doc.tags.map((tag) => (
                            <Badge key={tag} variant="secondary">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        查看详情
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
