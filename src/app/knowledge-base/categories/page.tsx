'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { 
  Database, 
  Plus,
  Edit,
  Trash2,
  Folder,
  <PERSON><PERSON>er<PERSON><PERSON>,
  FileText,
  Loader2,
  Save,
  X
} from 'lucide-react'

// 分类接口
interface KnowledgeCategory {
  id: number
  name: string
  description?: string
  parentId?: number
  parentName?: string
  documentCount: number
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function KnowledgeCategoriesPage() {
  const [categories, setCategories] = useState<KnowledgeCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<KnowledgeCategory | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parentId: '',
    sortOrder: 1,
    isActive: true,
  })

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '错误',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/knowledge-base/categories', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setCategories(result.data || [])
      } else {
        // 如果API失败，使用模拟数据
        const mockCategories: KnowledgeCategory[] = [
          {
            id: 1,
            name: '政策法规',
            description: '国家和地方医保政策法规文件',
            documentCount: 25,
            sortOrder: 1,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
          {
            id: 2,
            name: '业务指南',
            description: '医保业务操作指南和流程文档',
            documentCount: 18,
            sortOrder: 2,
            isActive: true,
            createdAt: '2024-01-02T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z',
          },
          {
            id: 3,
            name: '技术文档',
            description: '系统技术文档和操作手册',
            documentCount: 12,
            sortOrder: 3,
            isActive: true,
            createdAt: '2024-01-03T00:00:00Z',
            updatedAt: '2024-01-03T00:00:00Z',
          },
          {
            id: 4,
            name: '培训资料',
            description: '培训课件和学习资料',
            documentCount: 8,
            sortOrder: 4,
            isActive: true,
            createdAt: '2024-01-04T00:00:00Z',
            updatedAt: '2024-01-04T00:00:00Z',
          },
          {
            id: 5,
            name: '国家政策',
            description: '国家层面的医保政策文件',
            parentId: 1,
            parentName: '政策法规',
            documentCount: 15,
            sortOrder: 1,
            isActive: true,
            createdAt: '2024-01-05T00:00:00Z',
            updatedAt: '2024-01-05T00:00:00Z',
          },
          {
            id: 6,
            name: '地方政策',
            description: '地方政府的医保政策文件',
            parentId: 1,
            parentName: '政策法规',
            documentCount: 10,
            sortOrder: 2,
            isActive: true,
            createdAt: '2024-01-06T00:00:00Z',
            updatedAt: '2024-01-06T00:00:00Z',
          },
        ]
        
        setCategories(mockCategories)
        
        toast({
          title: '获取分类列表失败',
          description: result.message + '（已加载模拟数据）',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      toast({
        title: '获取分类列表失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      parentId: '',
      sortOrder: 1,
      isActive: true,
    })
    setEditingCategory(null)
  }

  // 打开新增对话框
  const openCreateDialog = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  // 打开编辑对话框
  const openEditDialog = (category: KnowledgeCategory) => {
    setFormData({
      name: category.name,
      description: category.description || '',
      parentId: category.parentId?.toString() || '',
      sortOrder: category.sortOrder,
      isActive: category.isActive,
    })
    setEditingCategory(category)
    setIsDialogOpen(true)
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast({
        title: '提交失败',
        description: '分类名称不能为空',
        variant: 'destructive',
      })
      return
    }

    try {
      setIsSubmitting(true)
      const token = localStorage.getItem('access_token')
      
      if (!token) {
        toast({
          title: '提交失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const url = editingCategory 
        ? `/api/knowledge-base/categories/${editingCategory.id}`
        : '/api/knowledge-base/categories'
      
      const method = editingCategory ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          parentId: formData.parentId ? parseInt(formData.parentId) : null,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: editingCategory ? '更新成功' : '创建成功',
          description: `分类已${editingCategory ? '更新' : '创建'}`,
        })
        setIsDialogOpen(false)
        resetForm()
        fetchCategories()
      } else {
        toast({
          title: editingCategory ? '更新失败' : '创建失败',
          description: result.message,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('提交失败:', error)
      toast({
        title: '提交失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 删除分类
  const handleDelete = async (category: KnowledgeCategory) => {
    if (category.documentCount > 0) {
      toast({
        title: '删除失败',
        description: '该分类下还有文档，无法删除',
        variant: 'destructive',
      })
      return
    }

    if (!confirm(`确定要删除分类"${category.name}"吗？`)) {
      return
    }

    try {
      const token = localStorage.getItem('access_token')
      
      if (!token) {
        toast({
          title: '删除失败',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch(`/api/knowledge-base/categories/${category.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: '删除成功',
          description: '分类已删除',
        })
        fetchCategories()
      } else {
        toast({
          title: '删除失败',
          description: result.message,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('删除失败:', error)
      toast({
        title: '删除失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  // 获取父分类选项
  const getParentOptions = () => {
    return categories.filter(cat => !cat.parentId && (!editingCategory || cat.id !== editingCategory.id))
  }

  // 统计数据
  const stats = {
    total: categories.length,
    active: categories.filter(c => c.isActive).length,
    inactive: categories.filter(c => !c.isActive).length,
    totalDocuments: categories.reduce((sum, cat) => sum + cat.documentCount, 0),
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">分类管理</h1>
              <p className="text-muted-foreground">
                管理知识文档分类体系
              </p>
            </div>
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              新增分类
            </Button>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总分类数</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                  </div>
                  <Database className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">启用分类</p>
                    <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                  </div>
                  <FolderOpen className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">禁用分类</p>
                    <p className="text-2xl font-bold text-red-600">{stats.inactive}</p>
                  </div>
                  <Folder className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总文档数</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.totalDocuments}</p>
                  </div>
                  <FileText className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 分类列表 */}
          <Card>
            <CardHeader>
              <CardTitle>分类列表</CardTitle>
              <CardDescription>
                共 {categories.length} 个分类
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>分类信息</TableHead>
                        <TableHead>父分类</TableHead>
                        <TableHead>文档数量</TableHead>
                        <TableHead>排序</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {categories.map((category) => (
                        <TableRow key={category.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {category.parentId ? (
                                <Folder className="h-4 w-4 text-muted-foreground" />
                              ) : (
                                <FolderOpen className="h-4 w-4 text-blue-500" />
                              )}
                              <div>
                                <p className="font-medium">{category.name}</p>
                                {category.description && (
                                  <p className="text-sm text-muted-foreground">
                                    {category.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {category.parentName ? (
                              <Badge variant="outline">
                                {category.parentName}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <span>{category.documentCount}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {category.sortOrder}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              className={category.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                              }
                            >
                              {category.isActive ? '启用' : '禁用'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDate(category.createdAt)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => openEditDialog(category)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="text-red-600"
                                onClick={() => handleDelete(category)}
                                disabled={category.documentCount > 0}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 新增/编辑对话框 */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? '编辑分类' : '新增分类'}
                </DialogTitle>
                <DialogDescription>
                  {editingCategory ? '修改分类信息' : '创建新的文档分类'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">分类名称 *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="输入分类名称"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">分类描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="输入分类描述"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parentId">父分类</Label>
                  <Select
                    value={formData.parentId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, parentId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="无（顶级分类）" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">无（顶级分类）</SelectItem>
                      {getParentOptions().map(category => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sortOrder">排序</Label>
                    <Input
                      id="sortOrder"
                      type="number"
                      value={formData.sortOrder}
                      onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 1 }))}
                      min="1"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="isActive">状态</Label>
                    <div className="flex items-center space-x-2 pt-2">
                      <Checkbox
                        id="isActive"
                        checked={formData.isActive}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked as boolean }))}
                      />
                      <Label htmlFor="isActive">启用</Label>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    <X className="h-4 w-4 mr-2" />
                    取消
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {editingCategory ? '更新' : '创建'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
