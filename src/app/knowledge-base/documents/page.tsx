'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { MainLayout } from '@/components/layout/main-layout'
import { RouteGuard } from '@/components/auth/route-guard'
import { useToast } from '@/lib/toast'
import { formatFileSize, formatDate } from '@/lib/format-utils'
import { 
  BookOpen, 
  Search, 
  Filter, 
  Plus,
  Upload,
  Download,
  Eye,
  Edit,
  Trash2,
  FileText,
  File,
  Image,
  Video,
  Archive,
  Loader2,
  Calendar,
  User,
  Tag
} from 'lucide-react'

// 文档接口
interface KnowledgeDocument {
  id: number
  title: string
  fileName: string
  fileType: string
  fileSize: number
  categoryId: number
  categoryName: string
  description?: string
  tags: string[]
  authorId: number
  authorName: string
  version: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  downloadCount: number
  viewCount: number
  createdAt: string
  updatedAt: string
}

// 文件类型图标映射
const fileTypeIcons: Record<string, any> = {
  'pdf': FileText,
  'doc': FileText,
  'docx': FileText,
  'txt': FileText,
  'jpg': Image,
  'jpeg': Image,
  'png': Image,
  'gif': Image,
  'mp4': Video,
  'avi': Video,
  'zip': Archive,
  'rar': Archive,
  'default': File,
}

// 状态颜色映射
const statusColors = {
  DRAFT: 'bg-yellow-100 text-yellow-800',
  PUBLISHED: 'bg-green-100 text-green-800',
  ARCHIVED: 'bg-gray-100 text-gray-800',
}

// 状态文本映射
const statusTexts = {
  DRAFT: '草稿',
  PUBLISHED: '已发布',
  ARCHIVED: '已归档',
}

export default function KnowledgeDocumentsPage() {
  const [documents, setDocuments] = useState<KnowledgeDocument[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const { toast } = useToast()

  // 获取文档列表
  const fetchDocuments = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('access_token')
      if (!token) {
        toast({
          title: '错误',
          description: '请先登录',
          variant: 'destructive',
        })
        return
      }

      // 构建查询参数
      const params = new URLSearchParams()
      if (categoryFilter !== 'all') params.append('categoryId', categoryFilter)
      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (searchTerm) params.append('search', searchTerm)

      const response = await fetch(`/api/knowledge-base/documents?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setDocuments(result.data || [])
      } else {
        // 如果API失败，使用模拟数据
        const mockDocuments: KnowledgeDocument[] = [
          {
            id: 1,
            title: '医保基金监管办法',
            fileName: '医保基金监管办法.pdf',
            fileType: 'pdf',
            fileSize: 2048576,
            categoryId: 1,
            categoryName: '政策法规',
            description: '国家医保基金监管相关政策文件',
            tags: ['政策', '监管', '基金'],
            authorId: 1,
            authorName: '系统管理员',
            version: '1.0',
            status: 'PUBLISHED',
            downloadCount: 156,
            viewCount: 892,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T10:00:00Z',
          },
          {
            id: 2,
            title: '医疗服务价格管理规定',
            fileName: '医疗服务价格管理规定.docx',
            fileType: 'docx',
            fileSize: 1024000,
            categoryId: 2,
            categoryName: '业务指南',
            description: '医疗服务价格制定和管理相关规定',
            tags: ['价格', '服务', '管理'],
            authorId: 2,
            authorName: '业务专员',
            version: '2.1',
            status: 'PUBLISHED',
            downloadCount: 89,
            viewCount: 445,
            createdAt: '2024-02-01T14:30:00Z',
            updatedAt: '2024-02-15T09:15:00Z',
          },
          {
            id: 3,
            title: '智能监管系统操作手册',
            fileName: '智能监管系统操作手册.pdf',
            fileType: 'pdf',
            fileSize: 5242880,
            categoryId: 3,
            categoryName: '技术文档',
            description: '系统操作和维护指南',
            tags: ['操作手册', '系统', '技术'],
            authorId: 1,
            authorName: '系统管理员',
            version: '1.5',
            status: 'DRAFT',
            downloadCount: 23,
            viewCount: 156,
            createdAt: '2024-03-01T16:00:00Z',
            updatedAt: '2024-03-10T11:20:00Z',
          },
        ]

        setDocuments(mockDocuments)

        toast({
          title: '获取文档列表失败',
          description: result.message + '（已加载模拟数据）',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('获取文档列表失败:', error)
      toast({
        title: '获取文档列表失败',
        description: '网络错误，请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) return

      const response = await fetch('/api/knowledge-base/categories', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (result.success) {
        setCategories(result.data || [])
      } else {
        // 如果API失败，使用模拟数据
        const mockCategories = [
          { id: 1, name: '政策法规', description: '国家和地方医保政策法规' },
          { id: 2, name: '业务指南', description: '医保业务操作指南' },
          { id: 3, name: '技术文档', description: '系统技术文档和手册' },
          { id: 4, name: '培训资料', description: '培训课件和学习资料' },
        ]
        setCategories(mockCategories)
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  // 获取文件类型图标
  const getFileTypeIcon = (fileType: string) => {
    const IconComponent = fileTypeIcons[fileType.toLowerCase()] || fileTypeIcons['default']
    return <IconComponent className="h-4 w-4" />
  }

  // 使用统一的格式化函数
  // formatFileSize 和 formatDate 已从 @/lib/format-utils 导入

  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = !searchTerm || 
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = categoryFilter === 'all' || doc.categoryId.toString() === categoryFilter
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  // 统计数据
  const stats = {
    total: documents.length,
    published: documents.filter(d => d.status === 'PUBLISHED').length,
    draft: documents.filter(d => d.status === 'DRAFT').length,
    archived: documents.filter(d => d.status === 'ARCHIVED').length,
  }

  useEffect(() => {
    Promise.all([fetchDocuments(), fetchCategories()])
  }, [categoryFilter, statusFilter])

  return (
    <RouteGuard requiredRoles={['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER']}>
      <MainLayout>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">知识文档</h1>
              <p className="text-muted-foreground">
                管理医保政策和知识文档
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                批量上传
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新增文档
              </Button>
            </div>
          </div>

          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总文档数</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">已发布</p>
                    <p className="text-2xl font-bold text-green-600">{stats.published}</p>
                  </div>
                  <FileText className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">草稿</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.draft}</p>
                  </div>
                  <Edit className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">已归档</p>
                    <p className="text-2xl font-bold text-gray-600">{stats.archived}</p>
                  </div>
                  <Archive className="h-8 w-8 text-gray-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 搜索和筛选 */}
          <Card>
            <CardHeader>
              <CardTitle>筛选条件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">搜索文档</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="搜索标题、描述或标签..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">文档分类</Label>
                  <Select
                    value={categoryFilter}
                    onValueChange={setCategoryFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="所有分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有分类</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">文档状态</Label>
                  <Select
                    value={statusFilter}
                    onValueChange={setStatusFilter}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="所有状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有状态</SelectItem>
                      <SelectItem value="PUBLISHED">已发布</SelectItem>
                      <SelectItem value="DRAFT">草稿</SelectItem>
                      <SelectItem value="ARCHIVED">已归档</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 文档列表 */}
          <Card>
            <CardHeader>
              <CardTitle>文档列表</CardTitle>
              <CardDescription>
                共 {filteredDocuments.length} 个文档
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">加载中...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>文档信息</TableHead>
                        <TableHead>分类</TableHead>
                        <TableHead>作者</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>统计</TableHead>
                        <TableHead>更新时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredDocuments.map((doc) => (
                        <TableRow key={doc.id}>
                          <TableCell>
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 mt-1">
                                {getFileTypeIcon(doc.fileType)}
                              </div>
                              <div className="min-w-0 flex-1">
                                <p className="font-medium truncate">{doc.title}</p>
                                <p className="text-sm text-muted-foreground truncate">
                                  {doc.fileName}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {formatFileSize(doc.fileSize)} • v{doc.version}
                                </p>
                                {doc.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {doc.tags.slice(0, 2).map((tag, index) => (
                                      <Badge key={index} variant="outline" className="text-xs">
                                        {tag}
                                      </Badge>
                                    ))}
                                    {doc.tags.length > 2 && (
                                      <Badge variant="outline" className="text-xs">
                                        +{doc.tags.length - 2}
                                      </Badge>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {doc.categoryName}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{doc.authorName}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={statusColors[doc.status]}>
                              {statusTexts[doc.status]}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm space-y-1">
                              <div className="flex items-center space-x-1">
                                <Eye className="h-3 w-3" />
                                <span>{doc.viewCount}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Download className="h-3 w-3" />
                                <span>{doc.downloadCount}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(doc.updatedAt)}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="text-red-600">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
