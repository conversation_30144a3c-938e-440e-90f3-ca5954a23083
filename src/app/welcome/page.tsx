"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { MainLayout } from "@/components/layout/main-layout"
import { RouteGuard } from "@/components/auth/route-guard"
import { Shield, Database, Users, FileText, BarChart3, Settings } from "lucide-react"

export default function WelcomePage() {
  return (
    <RouteGuard>
      <MainLayout>
        <div className="space-y-8">
          {/* Welcome Section */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              欢迎使用医保基金监管平台
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              基于Next.js和shadcn/UI构建的现代化医疗保险基金监督管理系统，
              提供全面的监管功能和直观的用户体验。
            </p>
            <div className="flex justify-center space-x-4">
              <Button size="lg">
                开始使用
              </Button>
              <Button variant="outline" size="lg">
                查看文档
              </Button>
            </div>
          </div>

          <Separator className="my-8" />

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5 text-primary" />
                  <span>数据库管理</span>
                </CardTitle>
                <CardDescription>
                  完整的Oracle数据库设计，包含22个业务表和完善的数据结构
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 用户管理模块</li>
                  <li>• 医疗病例管理</li>
                  <li>• 监管规则引擎</li>
                  <li>• 知识库系统</li>
                  <li>• 系统日志记录</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-primary" />
                  <span>用户权限</span>
                </CardTitle>
                <CardDescription>
                  基于角色的访问控制系统，支持多层级权限管理
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 系统管理员</li>
                  <li>• 监管主管</li>
                  <li>• 业务操作员</li>
                  <li>• 审核员</li>
                  <li>• 查看员</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <span>监管规则</span>
                </CardTitle>
                <CardDescription>
                  智能化监管规则引擎，支持自定义规则配置和执行
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 规则模板管理</li>
                  <li>• 自动化执行</li>
                  <li>• 结果审核</li>
                  <li>• 异常标记</li>
                  <li>• 执行日志</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  <span>数据分析</span>
                </CardTitle>
                <CardDescription>
                  强大的数据可视化和分析功能，支持多维度统计报表
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 实时监控大屏</li>
                  <li>• 统计报表</li>
                  <li>• 趋势分析</li>
                  <li>• 异常检测</li>
                  <li>• 数据导出</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-primary" />
                  <span>安全保障</span>
                </CardTitle>
                <CardDescription>
                  企业级安全机制，确保数据安全和系统稳定性
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• JWT身份认证</li>
                  <li>• 数据加密传输</li>
                  <li>• 操作审计日志</li>
                  <li>• 权限细粒度控制</li>
                  <li>• 安全备份机制</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-primary" />
                  <span>系统配置</span>
                </CardTitle>
                <CardDescription>
                  灵活的系统配置和管理功能，支持个性化定制
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 主题切换</li>
                  <li>• 系统参数配置</li>
                  <li>• 通知设置</li>
                  <li>• 备份恢复</li>
                  <li>• 性能监控</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <Separator className="my-8" />

          {/* Status Section */}
          <div className="text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">系统状态</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-2xl font-bold text-green-600">✓</div>
                  <p className="text-sm font-medium">数据库</p>
                  <p className="text-xs text-muted-foreground">已连接</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-2xl font-bold text-green-600">22</div>
                  <p className="text-sm font-medium">数据表</p>
                  <p className="text-xs text-muted-foreground">已创建</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-2xl font-bold text-green-600">5</div>
                  <p className="text-sm font-medium">用户角色</p>
                  <p className="text-xs text-muted-foreground">已配置</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="text-2xl font-bold text-blue-600">v2.0</div>
                  <p className="text-sm font-medium">系统版本</p>
                  <p className="text-xs text-muted-foreground">最新版本</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </RouteGuard>
  )
}
