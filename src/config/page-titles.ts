/**
 * 统一页面标题配置
 * 确保页面标题与导航标签完全一致
 */

export const PAGE_TITLES = {
  // 主要页面
  '/dashboard': '仪表板',
  '/medical-cases': '医疗案例',
  '/supervision-rules': '监管规则',
  '/users': '用户管理',
  '/analytics': '数据分析',
  '/knowledge-base': '知识库',
  '/settings': '系统设置',

  // 医疗案例子页面
  '/medical-cases/[id]': '案例详情',
  '/medical-cases/[id]/edit': '编辑案例',

  // 监管规则子页面
  '/supervision-rules/new': '新建规则',
  '/supervision-rules/[id]': '规则详情',
  '/supervision-rules/[id]/edit': '编辑规则',
  '/supervision-rules/executions': '执行记录',

  // 用户管理子页面
  '/users/[id]': '用户详情',
  '/users/roles': '角色管理',
  '/users/permissions': '权限配置',

  // 数据分析子页面
  '/analytics/reports': '统计报表',
  '/analytics/trends': '趋势分析',
  '/analytics/monitoring': '异常监控',

  // 知识库子页面
  '/knowledge-base/documents': '文档管理',
  '/knowledge-base/categories': '分类管理',
  '/knowledge-base/logs': '访问日志',

  // 系统设置子页面
  '/settings/general': '基本设置',
  '/settings/security': '安全设置',
  '/settings/logs': '系统日志',
} as const

export type PagePath = keyof typeof PAGE_TITLES

/**
 * 获取页面标题
 * @param pathname 页面路径
 * @param dynamicData 动态数据（如案例编号、用户名等）
 * @returns 页面标题
 */
export function getPageTitle(pathname: string, dynamicData?: any): string {
  // 处理动态路由
  if (pathname.includes('/medical-cases/') && dynamicData?.caseNumber) {
    return `案例 ${dynamicData.caseNumber}`
  }

  if (pathname.includes('/supervision-rules/') && dynamicData?.ruleName) {
    return dynamicData.ruleName
  }

  if (pathname.includes('/users/') && dynamicData?.userName) {
    return `用户 ${dynamicData.userName}`
  }

  // 查找精确匹配
  if (pathname in PAGE_TITLES) {
    return PAGE_TITLES[pathname as PagePath]
  }

  // 查找模式匹配
  for (const [pattern, title] of Object.entries(PAGE_TITLES)) {
    if (pattern.includes('[id]')) {
      const regex = new RegExp(pattern.replace(/\[id\]/g, '[^/]+'))
      if (regex.test(pathname)) {
        return title
      }
    }
  }

  // 默认返回路径的最后一段，首字母大写
  const segments = pathname.split('/').filter(Boolean)
  const lastSegment = segments[segments.length - 1]
  return lastSegment ? lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1) : 'Page'
}
