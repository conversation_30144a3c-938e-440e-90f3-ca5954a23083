import {
  LayoutDashboard,
  Shield,
  FileText,
  BarChart3,
  Settings,
  BookOpen,
  type LucideIcon
} from 'lucide-react'
import { UserRole } from '@/types/auth'

export interface NavigationItem {
  title: string
  href: string
  icon?: LucideIcon // 图标现在是可选的，子菜单不需要图标
  description?: string
  roles?: UserRole[] // 允许访问的角色，空数组表示所有角色都可以访问
  children?: NavigationItem[]
  badge?: string | number
  isNew?: boolean
}

/**
 * 医保基金监管平台导航配置
 * 按照业务功能模块进行分组，图标唯一不重复
 */
export const navigationConfig: NavigationItem[] = [
  // 仪表盘 - 单页面集成（系统概览、待办事项、最近更新）
  {
    title: '仪表盘',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: '系统概览、待办事项和最近更新',
    roles: [], // 所有角色都可以访问
  },

  // 病例管理 - 单页面集成（病例列表、新增/导入、统计）
  {
    title: '病例管理',
    href: '/medical-cases',
    icon: FileText,
    description: '病例信息管理、新增导入和统计分析',
    roles: ['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER'],
  },

  // 基金监管 - 多个业务流程，保留子菜单
  {
    title: '基金监管',
    icon: Shield,
    description: '基金监管规则和执行管理',
    href: '#supervision',
    roles: ['ADMIN', 'SUPERVISOR', 'OPERATOR'],
    children: [
      {
        title: '监管规则',
        href: '/supervision/rules',
        description: '监管规则的增删改查',
        roles: ['ADMIN', 'SUPERVISOR', 'OPERATOR'],
      },
      {
        title: '规则执行',
        href: '/supervision/execution',
        description: '规则执行和调度管理',
        roles: ['ADMIN', 'SUPERVISOR', 'OPERATOR'],
      },
      {
        title: '审核中心',
        href: '/supervision/review',
        description: '规则执行结果审核',
        roles: ['ADMIN', 'SUPERVISOR'],
      },
      {
        title: '监管报告',
        href: '/supervision/reports',
        description: '监管报告生成和查看',
        roles: ['ADMIN', 'SUPERVISOR'],
      },
    ],
  },

  // 知识库 - 单页面集成（文档管理、智能问答、检索）
  {
    title: '知识库',
    href: '/knowledge',
    icon: BookOpen,
    description: '文档管理、智能问答和检索功能',
    roles: ['ADMIN', 'SUPERVISOR', 'OPERATOR', 'VIEWER'],
  },

  // 数据统计 - 多种分析类型，保留子菜单
  {
    title: '数据统计',
    icon: BarChart3,
    description: '数据统计分析和报表',
    href: '#analytics',
    roles: ['ADMIN', 'SUPERVISOR', 'AUDITOR'],
    children: [
      {
        title: '统计报表',
        href: '/analytics/reports',
        description: '各类统计报表生成',
        roles: ['ADMIN', 'SUPERVISOR', 'AUDITOR'],
      },
      {
        title: '趋势分析',
        href: '/analytics/trends',
        description: '数据趋势分析和预测',
        roles: ['ADMIN', 'SUPERVISOR'],
      },
      {
        title: '数据导出',
        href: '/analytics/export',
        description: '数据导出和备份',
        roles: ['ADMIN', 'SUPERVISOR'],
      },
    ],
  },

  // 系统管理 - 多个管理功能，保留子菜单
  {
    title: '系统管理',
    icon: Settings,
    description: '系统配置和管理',
    href: '#system',
    roles: ['ADMIN'],
    children: [
      {
        title: '用户管理',
        href: '/system/users',
        description: '用户账户和权限管理',
        roles: ['ADMIN'],
      },
      {
        title: '角色权限',
        href: '/system/roles',
        description: '角色和权限配置',
        roles: ['ADMIN'],
      },
      {
        title: '系统设置',
        href: '/system/settings',
        description: '系统参数和配置',
        roles: ['ADMIN'],
      },
    ],
  },
]

/**
 * 根据用户角色过滤导航项
 */
export function filterNavigationByRoles(
  navigation: NavigationItem[],
  userRoles: UserRole[]
): NavigationItem[] {
  return navigation
    .filter(item => {
      // 如果没有指定角色限制，所有用户都可以访问
      if (!item.roles || item.roles.length === 0) {
        return true
      }
      // 检查用户是否有任一允许的角色
      return item.roles.some(role => userRoles.includes(role))
    })
    .map(item => ({
      ...item,
      children: item.children 
        ? filterNavigationByRoles(item.children, userRoles)
        : undefined
    }))
    .filter(item => {
      // 如果有子项，但过滤后子项为空，则隐藏父项
      if (item.children) {
        return item.children.length > 0
      }
      return true
    })
}

export interface BreadcrumbItem {
  title: string
  href: string
}

/**
 * 面包屑路由配置
 * 使用配置化方式简化面包屑生成逻辑
 */
interface BreadcrumbRoute {
  pattern: string | RegExp
  title: string
  href?: string
  children?: BreadcrumbRoute[]
  dynamic?: (pathname: string, data?: any) => BreadcrumbItem | null
}

const breadcrumbRoutes: BreadcrumbRoute[] = [
  {
    pattern: '/dashboard',
    title: '工作台',
    href: '/dashboard'
  },
  {
    pattern: /^\/medical-cases/,
    title: '医疗案例',
    href: '/medical-cases',
    children: [
      {
        pattern: '/medical-cases/new',
        title: '新建案例'
      },
      {
        pattern: /^\/medical-cases\/[^\/]+$/,
        title: '案例详情',
        dynamic: (pathname, data) => ({
          title: data?.caseNumber ? `案例 ${data.caseNumber}` : '案例详情',
          href: pathname
        })
      }
    ]
  },
  {
    pattern: /^\/supervision-rules/,
    title: '监管规则',
    href: '/supervision-rules',
    children: [
      {
        pattern: '/supervision-rules/new',
        title: '新建规则'
      },
      {
        pattern: '/supervision-rules/executions',
        title: '执行记录'
      },
      {
        pattern: /^\/supervision-rules\/[^\/]+$/,
        title: '规则详情',
        dynamic: (pathname, data) => ({
          title: data?.ruleName || '规则详情',
          href: pathname
        })
      }
    ]
  },
  {
    pattern: /^\/analytics/,
    title: '数据中心',
    href: '/analytics',
    children: [
      { pattern: '/analytics/reports', title: '统计报表' },
      { pattern: '/analytics/trends', title: '趋势分析' },
      { pattern: '/analytics/monitoring', title: '实时监控' }
    ]
  },
  {
    pattern: /^\/users/,
    title: '用户管理',
    href: '/users',
    children: [
      { pattern: '/users/roles', title: '角色权限' },
      {
        pattern: /^\/users\/[^\/]+$/,
        title: '用户详情',
        dynamic: (pathname, data) => ({
          title: data?.userName ? `用户 ${data.userName}` : '用户详情',
          href: pathname
        })
      }
    ]
  },
  {
    pattern: /^\/knowledge-base/,
    title: '知识库',
    href: '/knowledge-base',
    children: [
      { pattern: '/knowledge-base/documents', title: '文档管理' },
      { pattern: '/knowledge-base/categories', title: '分类管理' }
    ]
  },
  {
    pattern: /^\/rule-templates/,
    title: '规则模板',
    href: '/rule-templates'
  },
  {
    pattern: /^\/settings/,
    title: '系统设置',
    href: '/settings',
    children: [
      { pattern: '/settings/general', title: '基本设置' },
      { pattern: '/settings/security', title: '安全设置' }
    ]
  },
  {
    pattern: /^\/system-config/,
    title: '配置管理',
    href: '/system-config',
    children: [
      { pattern: '/system-config/categories', title: '配置分类' },
      { pattern: '/system-config/items', title: '配置项' },
      { pattern: '/system-config/history', title: '变更历史' }
    ]
  }
]

/**
 * 增强的面包屑导航生成器
 * 支持动态数据、嵌套路由和智能匹配
 */
export function getBreadcrumbs(pathname: string, dynamicData?: any): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: '工作台', href: '/dashboard' }
  ]

  // 如果就是首页，直接返回
  if (pathname === '/dashboard') {
    return breadcrumbs
  }

  // 查找匹配的路由配置
  const findMatchingRoute = (routes: BreadcrumbRoute[], path: string): BreadcrumbRoute | null => {
    for (const route of routes) {
      if (typeof route.pattern === 'string') {
        if (path === route.pattern) return route
      } else {
        if (route.pattern.test(path)) return route
      }
    }
    return null
  }

  // 递归构建面包屑
  const buildBreadcrumbs = (routes: BreadcrumbRoute[], path: string, parentBreadcrumbs: BreadcrumbItem[] = []) => {
    const matchedRoute = findMatchingRoute(routes, path)

    if (!matchedRoute) return parentBreadcrumbs

    // 添加当前级别的面包屑
    const currentBreadcrumb: BreadcrumbItem = {
      title: matchedRoute.title,
      href: matchedRoute.href || path
    }

    const newBreadcrumbs = [...parentBreadcrumbs, currentBreadcrumb]

    // 如果有子路由，继续查找
    if (matchedRoute.children) {
      const childRoute = findMatchingRoute(matchedRoute.children, path)
      if (childRoute) {
        if (childRoute.dynamic) {
          const dynamicBreadcrumb = childRoute.dynamic(path, dynamicData)
          if (dynamicBreadcrumb) {
            newBreadcrumbs.push(dynamicBreadcrumb)
          }
        } else {
          newBreadcrumbs.push({
            title: childRoute.title,
            href: path
          })
        }
      }
    }

    return newBreadcrumbs
  }

  // 构建面包屑
  const result = buildBreadcrumbs(breadcrumbRoutes, pathname, breadcrumbs.slice(1))

  // 如果没有找到匹配的路由，使用智能推断
  if (result.length === 1) {
    const segments = pathname.split('/').filter(Boolean)

    // 根据路径段智能生成面包屑
    segments.forEach((segment, index) => {
      const segmentPath = '/' + segments.slice(0, index + 1).join('/')
      const title = segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

      result.push({
        title,
        href: segmentPath
      })
    })
  }

  return result
}
