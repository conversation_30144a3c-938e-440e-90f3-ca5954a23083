import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { NavigationItem, filterNavigationByRoles } from '@/config/navigation'
import { UserRole } from '@/types/auth'

interface NavigationPermissionState {
  filteredNavigation: NavigationItem[]
  isLoading: boolean
  lastUpdated: Date | null
  permissionVersion: number
}

/**
 * 导航权限管理Hook
 * 提供动态权限更新和实时导航过滤功能
 */
export function useNavigationPermissions(navigationConfig: NavigationItem[]) {
  const { user, isAuthenticated, hasRole } = useAuth()
  const [state, setState] = useState<NavigationPermissionState>({
    filteredNavigation: [],
    isLoading: true,
    lastUpdated: null,
    permissionVersion: 0
  })

  // 权限变更监听器
  const [permissionListeners, setPermissionListeners] = useState<Set<() => void>>(new Set())

  /**
   * 更新导航权限
   */
  const updateNavigationPermissions = useCallback(() => {
    if (!isAuthenticated || !user) {
      setState(prev => ({
        ...prev,
        filteredNavigation: [],
        isLoading: false,
        lastUpdated: new Date(),
        permissionVersion: prev.permissionVersion + 1
      }))
      return
    }

    const userRoles = user.roles.map(role => role.roleCode) as UserRole[]
    const filteredNavigation = filterNavigationByRoles(navigationConfig, userRoles)

    setState(prev => ({
      ...prev,
      filteredNavigation,
      isLoading: false,
      lastUpdated: new Date(),
      permissionVersion: prev.permissionVersion + 1
    }))
  }, [isAuthenticated, user, navigationConfig])

  /**
   * 检查导航项权限
   */
  const checkNavigationPermission = useCallback((item: NavigationItem): boolean => {
    if (!isAuthenticated || !user) {
      return false
    }

    // 如果没有指定角色限制，所有用户都可以访问
    if (!item.roles || item.roles.length === 0) {
      return true
    }

    // 检查用户是否有任一允许的角色
    return item.roles.some(role => hasRole(role))
  }, [isAuthenticated, user, hasRole])

  /**
   * 获取用户可访问的导航项数量
   */
  const getAccessibleNavigationCount = useCallback((): number => {
    const countAccessible = (items: NavigationItem[]): number => {
      return items.reduce((count, item) => {
        if (checkNavigationPermission(item)) {
          count += 1
          if (item.children) {
            count += countAccessible(item.children)
          }
        }
        return count
      }, 0)
    }

    return countAccessible(state.filteredNavigation)
  }, [state.filteredNavigation, checkNavigationPermission])

  /**
   * 添加权限变更监听器
   */
  const addPermissionListener = useCallback((listener: () => void) => {
    setPermissionListeners(prev => new Set([...prev, listener]))
    
    // 返回移除监听器的函数
    return () => {
      setPermissionListeners(prev => {
        const newSet = new Set(prev)
        newSet.delete(listener)
        return newSet
      })
    }
  }, [])

  /**
   * 触发权限变更事件
   */
  const triggerPermissionUpdate = useCallback(() => {
    updateNavigationPermissions()
    
    // 通知所有监听器
    permissionListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('权限监听器执行失败:', error)
      }
    })
  }, [updateNavigationPermissions, permissionListeners])

  /**
   * 验证导航路径权限
   */
  const validateNavigationPath = useCallback((pathname: string): boolean => {
    const findNavigationItem = (items: NavigationItem[], path: string): NavigationItem | null => {
      for (const item of items) {
        if (item.href === path) {
          return item
        }
        if (item.children) {
          const found = findNavigationItem(item.children, path)
          if (found) return found
        }
      }
      return null
    }

    const navigationItem = findNavigationItem(navigationConfig, pathname)
    if (!navigationItem) {
      // 如果找不到导航项，默认允许访问（可能是动态路由）
      return true
    }

    return checkNavigationPermission(navigationItem)
  }, [navigationConfig, checkNavigationPermission])

  /**
   * 获取权限摘要信息
   */
  const getPermissionSummary = useCallback(() => {
    if (!user) {
      return {
        totalNavigationItems: 0,
        accessibleItems: 0,
        userRoles: [],
        permissionLevel: 'none' as const
      }
    }

    const userRoles = user.roles.map(role => role.roleCode)
    const totalItems = navigationConfig.length
    const accessibleItems = getAccessibleNavigationCount()
    
    let permissionLevel: 'none' | 'limited' | 'standard' | 'admin' = 'none'
    
    if (userRoles.includes('ADMIN')) {
      permissionLevel = 'admin'
    } else if (userRoles.includes('SUPERVISOR')) {
      permissionLevel = 'standard'
    } else if (userRoles.length > 0) {
      permissionLevel = 'limited'
    }

    return {
      totalNavigationItems: totalItems,
      accessibleItems,
      userRoles,
      permissionLevel
    }
  }, [user, navigationConfig, getAccessibleNavigationCount])

  // 初始化和权限变更时更新导航
  useEffect(() => {
    updateNavigationPermissions()
  }, [updateNavigationPermissions])

  // 监听用户权限变更
  useEffect(() => {
    return () => {}
    if (user) {
      // 模拟权限变更检查（实际项目中可以通过WebSocket或轮询实现）
      const checkPermissionChanges = () => {
        // 这里可以调用API检查用户权限是否有变更
        // 如果有变更，调用 triggerPermissionUpdate()
      }

      const interval = setInterval(checkPermissionChanges, 30000) // 每30秒检查一次
      return () => clearInterval(interval)
    }
  }, [user, triggerPermissionUpdate])

  return {
    // 状态
    filteredNavigation: state.filteredNavigation,
    isLoading: state.isLoading,
    lastUpdated: state.lastUpdated,
    permissionVersion: state.permissionVersion,
    
    // 方法
    checkNavigationPermission,
    validateNavigationPath,
    getAccessibleNavigationCount,
    getPermissionSummary,
    addPermissionListener,
    triggerPermissionUpdate,
    
    // 便捷属性
    hasNavigationAccess: state.filteredNavigation.length > 0,
    permissionSummary: getPermissionSummary()
  }
}
