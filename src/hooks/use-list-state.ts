'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

// 列表状态接口
export interface ListState {
  page: number
  pageSize: number
  searchTerm: string
  sortBy: string
  sortOrder: 'ASC' | 'DESC'
  filters: Record<string, any>
}

// 默认列表状态
const DEFAULT_LIST_STATE: ListState = {
  page: 1,
  pageSize: 10,
  searchTerm: '',
  sortBy: 'id',
  sortOrder: 'DESC',
  filters: {}
}

// 状态存储键
const STORAGE_KEY = 'medical_cases_list_state'

/**
 * 医疗案例列表状态管理Hook
 * 用于保持用户在列表页面的状态（页码、搜索条件、排序等）
 */
export function useListState() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [listState, setListState] = useState<ListState>(DEFAULT_LIST_STATE)

  // 从localStorage和URL参数恢复状态
  useEffect(() => {
    try {
      // 优先从URL参数读取状态
      const urlState: Partial<ListState> = {}
      
      if (searchParams.get('page')) {
        urlState.page = parseInt(searchParams.get('page') || '1')
      }
      if (searchParams.get('pageSize')) {
        urlState.pageSize = parseInt(searchParams.get('pageSize') || '10')
      }
      if (searchParams.get('search')) {
        urlState.searchTerm = searchParams.get('search') || ''
      }
      if (searchParams.get('sortBy')) {
        urlState.sortBy = searchParams.get('sortBy') || 'id'
      }
      if (searchParams.get('sortOrder')) {
        urlState.sortOrder = (searchParams.get('sortOrder') as 'ASC' | 'DESC') || 'DESC'
      }

      // 如果URL中没有状态参数，尝试从localStorage恢复
      if (Object.keys(urlState).length === 0) {
        const savedState = localStorage.getItem(STORAGE_KEY)
        if (savedState) {
          const parsedState = JSON.parse(savedState)
          setListState({ ...DEFAULT_LIST_STATE, ...parsedState })
          return
        }
      }

      // 使用URL状态或默认状态
      setListState({ ...DEFAULT_LIST_STATE, ...urlState })
    } catch (error) {
      console.error('Failed to restore list state:', error)
      setListState(DEFAULT_LIST_STATE)
    }
  }, [searchParams])

  // 保存状态到localStorage
  const saveState = (newState: Partial<ListState>) => {
    const updatedState = { ...listState, ...newState }
    setListState(updatedState)

    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      console.log('🔍 服务端环境，跳过localStorage保存')
      return
    }

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      console.log('✅ 状态保存成功:', updatedState)
    } catch (error) {
      console.error('Failed to save list state:', error)
    }
  }

  // 构建带状态参数的URL
  const buildUrlWithState = (basePath: string, state?: Partial<ListState>) => {
    const currentState = state ? { ...listState, ...state } : listState
    const params = new URLSearchParams()

    if (currentState.page !== DEFAULT_LIST_STATE.page) {
      params.set('page', currentState.page.toString())
    }
    if (currentState.pageSize !== DEFAULT_LIST_STATE.pageSize) {
      params.set('pageSize', currentState.pageSize.toString())
    }
    if (currentState.searchTerm !== DEFAULT_LIST_STATE.searchTerm) {
      params.set('search', currentState.searchTerm)
    }
    if (currentState.sortBy !== DEFAULT_LIST_STATE.sortBy) {
      params.set('sortBy', currentState.sortBy)
    }
    if (currentState.sortOrder !== DEFAULT_LIST_STATE.sortOrder) {
      params.set('sortOrder', currentState.sortOrder)
    }

    const queryString = params.toString()
    return queryString ? `${basePath}?${queryString}` : basePath
  }

  // 导航到列表页面并保持状态
  const navigateToList = (newState?: Partial<ListState>) => {
    const url = buildUrlWithState('/medical-cases', newState)
    router.push(url)
  }

  // 清除保存的状态
  const clearState = () => {
    setListState(DEFAULT_LIST_STATE)
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.error('Failed to clear list state:', error)
    }
  }

  return {
    listState,
    saveState,
    navigateToList,
    buildUrlWithState,
    clearState
  }
}

/**
 * 为详情页面提供的简化版本
 * 主要用于返回到列表页面时恢复状态
 */
export function useDetailPageNavigation() {
  const router = useRouter()

  // 从localStorage读取保存的状态
  const getSavedState = (): ListState => {
    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      console.log('🔍 服务端环境，使用默认状态:', DEFAULT_LIST_STATE)
      return DEFAULT_LIST_STATE
    }

    try {
      const savedState = localStorage.getItem(STORAGE_KEY)
      console.log('🔍 读取保存的状态:', savedState)
      if (savedState) {
        const parsedState = JSON.parse(savedState)
        const finalState = { ...DEFAULT_LIST_STATE, ...parsedState }
        console.log('🔍 解析后的状态:', finalState)
        return finalState
      }
    } catch (error) {
      console.error('Failed to read saved state:', error)
    }
    console.log('🔍 使用默认状态:', DEFAULT_LIST_STATE)
    return DEFAULT_LIST_STATE
  }

  // 构建带状态参数的URL
  const buildUrlWithSavedState = (basePath: string): string => {
    // 检查是否在客户端环境
    if (typeof window === 'undefined') {
      console.log('🔍 服务端环境，返回基础路径:', basePath)
      return basePath
    }

    const savedState = getSavedState()
    console.log('🔍 客户端环境，使用保存的状态构建URL:', savedState)

    const params = new URLSearchParams()

    if (savedState.page !== DEFAULT_LIST_STATE.page) {
      params.set('page', savedState.page.toString())
    }
    if (savedState.pageSize !== DEFAULT_LIST_STATE.pageSize) {
      params.set('pageSize', savedState.pageSize.toString())
    }
    if (savedState.searchTerm !== DEFAULT_LIST_STATE.searchTerm) {
      params.set('search', savedState.searchTerm)
    }
    if (savedState.sortBy !== DEFAULT_LIST_STATE.sortBy) {
      params.set('sortBy', savedState.sortBy)
    }
    if (savedState.sortOrder !== DEFAULT_LIST_STATE.sortOrder) {
      params.set('sortOrder', savedState.sortOrder)
    }

    const queryString = params.toString()
    const finalUrl = queryString ? `${basePath}?${queryString}` : basePath
    console.log('🔍 构建的最终URL:', finalUrl)
    return finalUrl
  }

  // 返回到列表页面，保持之前的状态
  const returnToList = () => {
    const url = buildUrlWithSavedState('/medical-cases')
    router.push(url)
  }

  // 获取返回列表的URL（用于面包屑导航）
  const getListUrl = () => {
    const url = buildUrlWithSavedState('/medical-cases')
    console.log('🔍 构建的返回URL:', url)
    return url
  }

  return {
    returnToList,
    getListUrl
  }
}
