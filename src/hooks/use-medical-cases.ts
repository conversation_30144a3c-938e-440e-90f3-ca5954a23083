// 优化的医疗案例数据管理Hook
import { useState, useEffect, useCallback, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { useDebounce } from '@/hooks/use-debounce'
import { MedicalCase, CaseListParams, CaseStatistics } from '@/types/medical-case'
import { businessToast } from '@/lib/toast'

interface UseMedicalCasesOptions {
  initialPageSize?: number
  cacheTimeout?: number
  enableRealTimeUpdates?: boolean
}

interface UseMedicalCasesReturn {
  // 数据状态
  cases: MedicalCase[]
  statistics: CaseStatistics | null
  
  // 加载状态
  isLoading: boolean
  isStatisticsLoading: boolean
  error: string | null
  
  // 分页状态
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  
  // 筛选状态
  searchTerm: string
  filters: Record<string, any>
  sortBy: string
  sortOrder: 'asc' | 'desc'
  
  // 选择状态
  selectedIds: string[]
  
  // 操作方法
  setSearchTerm: (term: string) => void
  setFilters: (filters: Record<string, any>) => void
  setSortBy: (field: string) => void
  setSortOrder: (order: 'asc' | 'desc') => void
  setSelectedIds: (ids: string[]) => void
  handlePageChange: (page: number) => void
  handlePageSizeChange: (size: number) => void
  refreshData: () => Promise<void>
  
  // 批量操作
  selectAll: () => void
  clearSelection: () => void
  deleteSelected: () => Promise<void>
  exportSelected: () => Promise<void>
}

export function useMedicalCases(options: UseMedicalCasesOptions = {}): UseMedicalCasesReturn {
  const { token } = useAuth()
  const searchParams = useSearchParams()
  const {
    initialPageSize = 10,
    cacheTimeout = 5 * 60 * 1000, // 5分钟缓存
    enableRealTimeUpdates = false
  } = options

  // 从URL参数读取初始状态
  const getInitialState = useCallback(() => {
    const urlPage = searchParams.get('page')
    const urlPageSize = searchParams.get('pageSize')
    const urlSearch = searchParams.get('search')
    const urlSortBy = searchParams.get('sortBy')
    const urlSortOrder = searchParams.get('sortOrder')

    return {
      page: urlPage ? parseInt(urlPage) : 1,
      pageSize: urlPageSize ? parseInt(urlPageSize) : initialPageSize,
      searchTerm: urlSearch || '',
      sortBy: urlSortBy || 'id',
      sortOrder: (urlSortOrder as 'asc' | 'desc') || 'desc'
    }
  }, [searchParams, initialPageSize])

  const initialState = getInitialState()

  // 数据状态
  const [cases, setCases] = useState<MedicalCase[]>([])
  const [statistics, setStatistics] = useState<CaseStatistics | null>(null)

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  const [isStatisticsLoading, setIsStatisticsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 分页状态
  const [pagination, setPagination] = useState({
    page: initialState.page,
    pageSize: initialState.pageSize,
    total: 0,
    totalPages: 0,
  })

  // 筛选状态
  const [searchTerm, setSearchTerm] = useState(initialState.searchTerm)
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [sortBy, setSortBy] = useState(initialState.sortBy)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(initialState.sortOrder)
  
  // 选择状态
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  
  // 防抖搜索
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  
  // 缓存管理
  const cacheKey = useMemo(() => {
    return `medical-cases-${JSON.stringify({
      page: pagination.page,
      pageSize: pagination.pageSize,
      search: debouncedSearchTerm,
      filters,
      sortBy,
      sortOrder
    })}`
  }, [pagination.page, pagination.pageSize, debouncedSearchTerm, filters, sortBy, sortOrder])

  // 创建认证请求函数
  const authenticatedFetch = useCallback((url: string, options: RequestInit = {}) => {
    if (!token) {
      throw new Error('用户未认证')
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...(options.headers as Record<string, string> || {}),
    }

    return fetch(url, {
      ...options,
      headers,
    })
  }, [token])

  // 获取案例列表
  const fetchCases = useCallback(async () => {
    if (!token) {
      setError('用户未认证，请重新登录')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // 检查缓存
      const cached = sessionStorage.getItem(cacheKey)
      if (cached) {
        const { data, timestamp } = JSON.parse(cached)
        if (Date.now() - timestamp < cacheTimeout) {
          setCases(data.items || [])
          setPagination(prev => ({
            ...prev,
            total: data.total || 0,
            totalPages: data.totalPages || 0
          }))
          return
        }
      }

      // 映射sortBy字段到API期望的格式
      const sortByMapping: Record<string, string> = {
        'id': 'ID',
        'totalCost': 'TOTAL_COST',
        'admissionDate': 'ADMISSION_DATE',
        'patientAge': 'PATIENT_NAME', // 暂时映射到患者姓名
        'caseNumber': 'CASE_NUMBER',
        'patientName': 'PATIENT_NAME'
      }

      const params: CaseListParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        search: debouncedSearchTerm || undefined,
        sortBy: sortByMapping[sortBy] || 'CREATED_AT',
        sortOrder: sortOrder.toUpperCase() as 'ASC' | 'DESC',
        ...filters
      }

      console.log('🔍 API调用参数:', params)

      // 构建查询字符串
      const queryParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value))
        }
      })

      // 使用GET请求获取数据
      const response = await authenticatedFetch(`/api/medical-cases?${queryParams.toString()}`)
      
      if (response.status === 401) {
        setError('认证已过期，请重新登录')
        return
      }
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `请求失败 (${response.status})`)
      }

      const data = await response.json()

      console.log('📊 API响应数据:', data)
      console.log('📊 API响应数据结构分析:', {
        hasSuccess: 'success' in data,
        hasData: 'data' in data,
        hasCases: 'cases' in data,
        hasItems: 'items' in data,
        hasPagination: 'pagination' in data,
        hasTotal: 'total' in data,
        hasPage: 'page' in data,
        dataKeys: Object.keys(data),
        dataType: typeof data,
        isArray: Array.isArray(data)
      })

      // 处理API响应格式差异
      let responseData
      if (data.success && data.data) {
        // 标准API响应格式: { success: true, data: { cases: [...], pagination: {...} } }
        responseData = data.data
      } else if (data.cases) {
        // 直接返回格式: { cases: [...], pagination: {...} }
        responseData = data
      } else if (data.items) {
        // 备选格式: { items: [...], pagination: {...} }
        responseData = data
      } else {
        // 兜底处理
        responseData = data
      }

      console.log('🔍 处理后的响应数据:', responseData)

      // 设置案例数据 - 从API返回的数据中提取items字段
      const cases = responseData.cases || responseData.items || []
      console.log('📋 解析的案例数据:', { casesLength: cases.length, firstCase: cases[0] })
      setCases(cases)

      // 设置分页数据 - 直接从API响应中获取分页信息
      const paginationData = responseData.pagination || {}
      const totalCount = paginationData.totalCount || responseData.total || 0
      const totalPages = paginationData.totalPages || responseData.totalPages || 0

      console.log('📊 解析的分页数据:', {
        totalCount,
        totalPages,
        paginationData,
        rawTotal: responseData.total,
        rawTotalPages: responseData.totalPages
      })

      setPagination(prev => ({
        ...prev,
        total: totalCount,
        totalPages: totalPages
      }))

      // 缓存结果
      sessionStorage.setItem(cacheKey, JSON.stringify({
        data: responseData,
        timestamp: Date.now()
      }))

    } catch (error) {
      console.error('获取案例列表失败:', error)
      setError(error instanceof Error ? error.message : '获取案例列表失败')
      businessToast.networkError()
    } finally {
      setIsLoading(false)
    }
  }, [
    token, pagination.page, pagination.pageSize, debouncedSearchTerm, 
    filters, sortBy, sortOrder, authenticatedFetch, cacheKey, cacheTimeout
  ])

  // 获取统计数据
  const fetchStatistics = useCallback(async () => {
    if (!token) return

    try {
      setIsStatisticsLoading(true)
      
      const response = await authenticatedFetch('/api/analytics/dashboard')
      if (response.status === 401) {
        console.warn('⚠️ 统计数据请求认证失败')
        return
      }
      if (!response.ok) {
        console.warn('统计数据获取失败，状态码:', response.status)
        return
      }
      
      const data = await response.json()
      setStatistics(data.data || data)
    } catch (error) {
      console.warn('获取统计数据失败:', error)
    } finally {
      setIsStatisticsLoading(false)
    }
  }, [authenticatedFetch, token])

  // 页面变化处理
  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }))
    setSelectedIds([]) // 清除选择
  }, [])

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setPagination(prev => ({ ...prev, page: 1, pageSize }))
    setSelectedIds([]) // 清除选择
  }, [])

  // 刷新数据
  const refreshData = useCallback(async () => {
    // 清除缓存
    sessionStorage.removeItem(cacheKey)
    await Promise.all([fetchCases(), fetchStatistics()])
  }, [fetchCases, fetchStatistics, cacheKey])

  // 批量操作
  const selectAll = useCallback(() => {
    setSelectedIds(cases.map(c => String(c.id)))
  }, [cases])

  const clearSelection = useCallback(() => {
    setSelectedIds([])
  }, [])

  const deleteSelected = useCallback(async () => {
    if (selectedIds.length === 0) return
    
    try {
      const response = await authenticatedFetch('/api/medical-cases/batch', {
        method: 'DELETE',
        body: JSON.stringify({ ids: selectedIds })
      })
      
      if (!response.ok) {
        throw new Error('批量删除失败')
      }
      
      businessToast.deleteSuccess()
      setSelectedIds([])
      await refreshData()
    } catch (error) {
      console.error('批量删除失败:', error)
      businessToast.deleteError()
    }
  }, [selectedIds, authenticatedFetch, refreshData])

  const exportSelected = useCallback(async () => {
    if (selectedIds.length === 0) return
    
    try {
      const response = await authenticatedFetch('/api/medical-cases/export', {
        method: 'POST',
        body: JSON.stringify({ ids: selectedIds })
      })
      
      if (!response.ok) {
        throw new Error('导出失败')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `medical-cases-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      businessToast.exportSuccess('医疗案例')
    } catch (error) {
      console.error('导出失败:', error)
      businessToast.exportError('医疗案例')
    }
  }, [selectedIds, authenticatedFetch])

  // 监听认证状态变化
  useEffect(() => {
    if (token) {
      fetchCases()
      fetchStatistics()
    }
  }, [token])

  // 监听URL参数变化并更新状态
  useEffect(() => {
    const urlPage = searchParams.get('page')
    const urlPageSize = searchParams.get('pageSize')
    const urlSearch = searchParams.get('search')
    const urlSortBy = searchParams.get('sortBy')
    const urlSortOrder = searchParams.get('sortOrder')

    const newState = {
      page: urlPage ? parseInt(urlPage) : 1,
      pageSize: urlPageSize ? parseInt(urlPageSize) : initialPageSize,
      searchTerm: urlSearch || '',
      sortBy: urlSortBy || 'id',
      sortOrder: (urlSortOrder as 'asc' | 'desc') || 'desc'
    }

    console.log('🔍 URL参数变化，更新状态:', newState)

    if (pagination.page !== newState.page) {
      console.log('📄 更新页码:', pagination.page, '->', newState.page)
      setPagination(prev => ({ ...prev, page: newState.page }))
    }
    if (pagination.pageSize !== newState.pageSize) {
      console.log('📏 更新页面大小:', pagination.pageSize, '->', newState.pageSize)
      setPagination(prev => ({ ...prev, pageSize: newState.pageSize }))
    }
    if (searchTerm !== newState.searchTerm) {
      console.log('🔍 更新搜索词:', searchTerm, '->', newState.searchTerm)
      setSearchTerm(newState.searchTerm)
    }
    if (sortBy !== newState.sortBy) {
      console.log('📊 更新排序字段:', sortBy, '->', newState.sortBy)
      setSortBy(newState.sortBy)
    }
    if (sortOrder !== newState.sortOrder) {
      console.log('🔄 更新排序顺序:', sortOrder, '->', newState.sortOrder)
      setSortOrder(newState.sortOrder)
    }
  }, [searchParams, initialPageSize, pagination.page, pagination.pageSize, searchTerm, sortBy, sortOrder])

  // 监听筛选条件变化
  useEffect(() => {
    fetchCases()
  }, [fetchCases])

  // 实时更新（可选）
  useEffect(() => {
    if (!enableRealTimeUpdates) return

    const interval = setInterval(() => {
      fetchStatistics() // 只更新统计数据，避免影响用户操作
    }, 30000) // 30秒更新一次

    return () => clearInterval(interval)
  }, [enableRealTimeUpdates, fetchStatistics])

  return {
    // 数据状态
    cases,
    statistics,
    
    // 加载状态
    isLoading,
    isStatisticsLoading,
    error,
    
    // 分页状态
    pagination,
    
    // 筛选状态
    searchTerm,
    filters,
    sortBy,
    sortOrder,
    
    // 选择状态
    selectedIds,
    
    // 操作方法
    setSearchTerm,
    setFilters,
    setSortBy,
    setSortOrder,
    setSelectedIds,
    handlePageChange,
    handlePageSizeChange,
    refreshData,
    
    // 批量操作
    selectAll,
    clearSelection,
    deleteSelected,
    exportSelected,
  }
}
