import { useEffect, useRef, useState } from 'react'

interface TouchPoint {
  x: number
  y: number
  timestamp: number
}

interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down'
  distance: number
  velocity: number
  duration: number
}

interface GestureOptions {
  minSwipeDistance?: number
  maxSwipeTime?: number
  minSwipeVelocity?: number
  preventScroll?: boolean
}

interface GestureCallbacks {
  onSwipe?: (gesture: SwipeGesture) => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onTap?: (point: TouchPoint) => void
  onDoubleTap?: (point: TouchPoint) => void
  onLongPress?: (point: TouchPoint) => void
  onPinch?: (scale: number) => void
}

/**
 * 移动端手势识别Hook
 * 支持滑动、点击、长按、双击、缩放等手势
 */
export function useMobileGestures(
  callbacks: GestureCallbacks = {},
  options: GestureOptions = {}
) {
  const {
    minSwipeDistance = 50,
    maxSwipeTime = 300,
    minSwipeVelocity = 0.3,
    preventScroll = false
  } = options

  const elementRef = useRef<HTMLElement>(null)
  const [isGestureActive, setIsGestureActive] = useState(false)
  
  // 触摸状态
  const touchStartRef = useRef<TouchPoint | null>(null)
  const touchEndRef = useRef<TouchPoint | null>(null)
  const lastTapRef = useRef<TouchPoint | null>(null)
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null)
  const pinchStartDistanceRef = useRef<number | null>(null)

  /**
   * 计算两点之间的距离
   */
  const getDistance = (point1: TouchPoint, point2: TouchPoint): number => {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算两个触摸点之间的距离（用于缩放手势）
   */
  const getTouchDistance = (touch1: Touch, touch2: Touch): number => {
    const dx = touch2.clientX - touch1.clientX
    const dy = touch2.clientY - touch1.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 获取滑动方向
   */
  const getSwipeDirection = (start: TouchPoint, end: TouchPoint): SwipeGesture['direction'] => {
    const dx = end.x - start.x
    const dy = end.y - start.y
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left'
    } else {
      return dy > 0 ? 'down' : 'up'
    }
  }

  /**
   * 处理触摸开始
   */
  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0]
    if (!touch) return
    const touchPoint: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    }

    touchStartRef.current = touchPoint
    setIsGestureActive(true)

    // 处理多点触控（缩放手势）
    if (e.touches.length === 2) {
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      if (!touch1 || !touch2) return
      const distance = getTouchDistance(touch1, touch2)
      pinchStartDistanceRef.current = distance
    }

    // 长按检测
    if (callbacks.onLongPress) {
      longPressTimerRef.current = setTimeout(() => {
        if (touchStartRef.current) {
          callbacks.onLongPress!(touchStartRef.current)
        }
      }, 500) // 500ms 长按阈值
    }

    // 阻止滚动（如果需要）
    if (preventScroll) {
      e.preventDefault()
    }
  }

  /**
   * 处理触摸移动
   */
  const handleTouchMove = (e: TouchEvent) => {
    // 处理缩放手势
    if (e.touches.length === 2 && pinchStartDistanceRef.current && callbacks.onPinch) {
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      if (!touch1 || !touch2) return
      const currentDistance = getTouchDistance(touch1, touch2)
      const scale = currentDistance / pinchStartDistanceRef.current
      callbacks.onPinch(scale)
    }

    // 清除长按定时器（因为手指移动了）
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }

    // 阻止滚动（如果需要）
    if (preventScroll) {
      e.preventDefault()
    }
  }

  /**
   * 处理触摸结束
   */
  const handleTouchEnd = (e: TouchEvent) => {
    const touch = e.changedTouches[0]
    if (!touch) return
    const touchPoint: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    }

    touchEndRef.current = touchPoint
    setIsGestureActive(false)

    // 清除长按定时器
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }

    // 重置缩放状态
    pinchStartDistanceRef.current = null

    if (!touchStartRef.current) return

    const distance = getDistance(touchStartRef.current, touchPoint)
    const duration = touchPoint.timestamp - touchStartRef.current.timestamp
    const velocity = distance / duration

    // 检测滑动手势
    if (
      distance >= minSwipeDistance &&
      duration <= maxSwipeTime &&
      velocity >= minSwipeVelocity
    ) {
      const direction = getSwipeDirection(touchStartRef.current, touchPoint)
      const gesture: SwipeGesture = {
        direction,
        distance,
        velocity,
        duration
      }

      // 调用滑动回调
      callbacks.onSwipe?.(gesture)
      
      switch (direction) {
        case 'left':
          callbacks.onSwipeLeft?.()
          break
        case 'right':
          callbacks.onSwipeRight?.()
          break
        case 'up':
          callbacks.onSwipeUp?.()
          break
        case 'down':
          callbacks.onSwipeDown?.()
          break
      }
    } else if (distance < 10 && duration < 300) {
      // 检测点击手势
      const now = Date.now()
      
      if (lastTapRef.current && 
          now - lastTapRef.current.timestamp < 300 &&
          getDistance(lastTapRef.current, touchPoint) < 20) {
        // 双击
        callbacks.onDoubleTap?.(touchPoint)
        lastTapRef.current = null
      } else {
        // 单击
        callbacks.onTap?.(touchPoint)
        lastTapRef.current = touchPoint
      }
    }

    touchStartRef.current = null
  }

  /**
   * 绑定事件监听器
   */
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // 添加触摸事件监听器
    element.addEventListener('touchstart', handleTouchStart, { passive: !preventScroll })
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventScroll })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })

    // 清理函数
    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
      
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current)
      }
    }
  }, [callbacks, options])

  return {
    elementRef,
    isGestureActive
  }
}

/**
 * 移动端滑动导航Hook
 * 专门用于页面间的滑动导航
 */
export function useSwipeNavigation(
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  options: GestureOptions = {}
) {
  return useMobileGestures({
    onSwipeLeft,
    onSwipeRight
  }, {
    minSwipeDistance: 100,
    maxSwipeTime: 400,
    minSwipeVelocity: 0.5,
    ...options
  })
}

/**
 * 移动端下拉刷新Hook
 */
export function usePullToRefresh(
  onRefresh: () => void | Promise<void>,
  options: { threshold?: number; enabled?: boolean } = {}
) {
  const { threshold = 80, enabled = true } = options
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)

  const { elementRef } = useMobileGestures({
    onSwipeDown: async () => {
      if (!enabled || isRefreshing) return
      
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
        setPullDistance(0)
      }
    }
  }, {
    minSwipeDistance: threshold,
    preventScroll: false
  })

  return {
    elementRef,
    isRefreshing,
    pullDistance
  }
}
