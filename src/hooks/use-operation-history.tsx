'use client'

import { useState, useEffect, useCallback, useContext, createContext } from 'react'
import { useRouter } from 'next/navigation'

/**
 * 操作历史记录接口
 */
export interface OperationHistory {
  id: string
  timestamp: Date
  module: string
  action: string
  target: string
  targetId?: string
  context: any
  userId?: string
  duration?: number
  success: boolean
  metadata?: Record<string, any>
}

/**
 * 操作历史上下文
 */
interface OperationHistoryContextType {
  history: OperationHistory[]
  addOperation: (operation: Omit<OperationHistory, 'id' | 'timestamp'>) => void
  getRecentOperations: (module?: string, limit?: number) => OperationHistory[]
  getOperationsByTarget: (targetId: string) => OperationHistory[]
  clearHistory: () => void
  exportHistory: () => void
}

const OperationHistoryContext = createContext<OperationHistoryContextType | null>(null)

/**
 * 生成唯一ID
 */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 操作历史Provider组件
 */
export function OperationHistoryProvider({ children }: { children: React.ReactNode }) {
  const [history, setHistory] = useState<OperationHistory[]>([])

  /**
   * 从localStorage加载历史记录
   */
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem('operation-history')
      if (savedHistory) {
        const parsed = JSON.parse(savedHistory).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
        setHistory(parsed)
      }
    } catch (error) {
      console.error('加载操作历史失败:', error)
    }
  }, [])

  /**
   * 保存历史记录到localStorage
   */
  useEffect(() => {
    if (history.length > 0) {
      try {
        localStorage.setItem('operation-history', JSON.stringify(history))
      } catch (error) {
        console.error('保存操作历史失败:', error)
      }
    }
  }, [history])

  /**
   * 添加操作记录
   */
  const addOperation = useCallback((operation: Omit<OperationHistory, 'id' | 'timestamp'>) => {
    const newOperation: OperationHistory = {
      ...operation,
      id: generateId(),
      timestamp: new Date()
    }

    setHistory(prev => {
      // 保留最近100条记录
      const updated = [newOperation, ...prev].slice(0, 100)
      return updated
    })
  }, [])

  /**
   * 获取最近的操作记录
   */
  const getRecentOperations = useCallback((module?: string, limit = 10) => {
    let filtered = history
    
    if (module) {
      filtered = history.filter(op => op.module === module)
    }
    
    return filtered.slice(0, limit)
  }, [history])

  /**
   * 根据目标ID获取操作记录
   */
  const getOperationsByTarget = useCallback((targetId: string) => {
    return history.filter(op => op.targetId === targetId)
  }, [history])

  /**
   * 清除历史记录
   */
  const clearHistory = useCallback(() => {
    setHistory([])
    localStorage.removeItem('operation-history')
  }, [])

  /**
   * 导出历史记录
   */
  const exportHistory = useCallback(() => {
    try {
      const dataStr = JSON.stringify(history, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `operation-history-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('导出操作历史失败:', error)
    }
  }, [history])

  const value: OperationHistoryContextType = {
    history,
    addOperation,
    getRecentOperations,
    getOperationsByTarget,
    clearHistory,
    exportHistory
  }

  return (
    <OperationHistoryContext.Provider value={value}>
      {children}
    </OperationHistoryContext.Provider>
  )
}

/**
 * 使用操作历史的Hook
 */
export function useOperationHistory() {
  const context = useContext(OperationHistoryContext)
  if (!context) {
    throw new Error('useOperationHistory must be used within OperationHistoryProvider')
  }
  return context
}

/**
 * 操作跟踪Hook
 * 自动记录页面操作
 */
export function useOperationTracker(module: string) {
  const { addOperation } = useOperationHistory()
  const router = useRouter()

  /**
   * 跟踪操作
   */
  const trackOperation = useCallback(async (
    action: string,
    target: string,
    targetId?: string,
    context?: any,
    metadata?: Record<string, any>
  ) => {
    const startTime = Date.now()
    let success = true
    let error: any = null

    try {
      // 如果context是一个Promise或函数，执行它
      if (typeof context === 'function') {
        await context()
      } else if (context && typeof context.then === 'function') {
        await context
      }
    } catch (err) {
      success = false
      error = err
      console.error(`操作失败 [${module}/${action}]:`, err)
    } finally {
      const duration = Date.now() - startTime

      addOperation({
        module,
        action,
        target,
        targetId,
        context: error ? { error: error.message } : context,
        duration,
        success,
        metadata
      })
    }

    return success
  }, [addOperation, module])

  /**
   * 跟踪导航操作
   */
  const trackNavigation = useCallback((
    target: string,
    targetId?: string,
    context?: any
  ) => {
    return trackOperation('navigate', target, targetId, context)
  }, [trackOperation])

  /**
   * 跟踪CRUD操作
   */
  const trackCRUD = useCallback((
    action: 'create' | 'read' | 'update' | 'delete',
    target: string,
    targetId?: string,
    context?: any
  ) => {
    return trackOperation(action, target, targetId, context)
  }, [trackOperation])

  /**
   * 跟踪批量操作
   */
  const trackBatchOperation = useCallback((
    action: string,
    target: string,
    itemCount: number,
    context?: any
  ) => {
    return trackOperation(`batch_${action}`, target, undefined, context, {
      itemCount,
      batchOperation: true
    })
  }, [trackOperation])

  return {
    trackOperation,
    trackNavigation,
    trackCRUD,
    trackBatchOperation
  }
}

/**
 * 跨模块上下文管理Hook
 */
export function useCrossModuleContext() {
  const { addOperation, getRecentOperations } = useOperationHistory()
  const router = useRouter()

  /**
   * 保存当前上下文并导航
   */
  const navigateWithContext = useCallback((
    targetPath: string,
    context: {
      returnUrl?: string
      preserveState?: boolean
      metadata?: Record<string, any>
    }
  ) => {
    // 记录导航操作
    addOperation({
      module: 'navigation',
      action: 'cross_module_navigate',
      target: targetPath,
      context: {
        returnUrl: context.returnUrl || window.location.pathname,
        preserveState: context.preserveState,
        metadata: context.metadata
      },
      success: true
    })

    // 执行导航
    router.push(targetPath)
  }, [addOperation, router])

  /**
   * 获取返回上下文
   */
  const getReturnContext = useCallback(() => {
    const recentNavigation = getRecentOperations('navigation', 5)
      .find(op => op.action === 'cross_module_navigate')

    return recentNavigation?.context || null
  }, [getRecentOperations])

  /**
   * 智能返回
   */
  const smartReturn = useCallback(() => {
    const context = getReturnContext()
    if (context?.returnUrl) {
      router.push(context.returnUrl)
    } else {
      router.back()
    }
  }, [getReturnContext, router])

  return {
    navigateWithContext,
    getReturnContext,
    smartReturn
  }
}
