'use client'

import { useState, useEffect } from 'react'

/**
 * 本地存储钩子
 * 
 * 提供类型安全的本地存储操作，支持：
 * - 自动序列化/反序列化
 * - SSR安全
 * - 错误处理
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  // 状态初始化
  const [storedValue, setStoredValue] = useState<T>(() => {
    // 在服务端渲染时返回初始值
    if (typeof window === 'undefined') {
      return initialValue
    }

    try {
      // 从本地存储获取值
      const item = window.localStorage.getItem(key)
      // 如果存在则解析，否则返回初始值
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      // 如果解析失败，返回初始值
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  // 设置值的函数
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // 允许值是函数，以便可以使用函数式更新
      const valueToStore = value instanceof Function ? value(storedValue) : value
      
      // 保存状态
      setStoredValue(valueToStore)
      
      // 保存到本地存储
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      // 更详细的错误处理
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }

  return [storedValue, setValue]
}
