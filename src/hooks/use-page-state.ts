/**
 * 页面状态管理Hook
 * 提供统一的页面状态保持和恢复功能
 */

'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { pageStateManager, type PageState, type NavigationContext, type OperationProgress } from '@/lib/state/page-state-manager'

export interface UsePageStateOptions {
  pageKey: string
  autoSave?: boolean
  saveInterval?: number
  restoreOnMount?: boolean
}

export interface UsePageStateReturn {
  state: PageState | null
  saveState: (state: Partial<PageState>) => void
  clearState: () => void
  isLoading: boolean
  
  // 导航相关
  navigateWithContext: (url: string, context?: NavigationContext) => void
  smartGoBack: (defaultUrl?: string) => void
  
  // 操作进度相关
  startOperation: (operation: Omit<OperationProgress, 'startTime'>) => string
  updateOperation: (id: string, updates: Partial<OperationProgress>) => void
  completeOperation: (id: string, success: boolean, message?: string) => void
  operations: OperationProgress[]
}

export function usePageState(options: UsePageStateOptions): UsePageStateReturn {
  const { pageKey, autoSave = true, saveInterval = 1000, restoreOnMount = true } = options
  const router = useRouter()
  const pathname = usePathname()
  
  const [state, setState] = useState<PageState | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [operations, setOperations] = useState<OperationProgress[]>([])
  
  const saveTimeoutRef = useRef<NodeJS.Timeout>()
  const pendingStateRef = useRef<Partial<PageState> | null>(null)

  // 恢复状态
  useEffect(() => {
    if (restoreOnMount) {
      const restoredState = pageStateManager.restoreState(pageKey)
      setState(restoredState)
    }
    setIsLoading(false)
  }, [pageKey, restoreOnMount])

  // 监听状态变化
  useEffect(() => {
    const unsubscribe = pageStateManager.onStateChange(pageKey, (newState) => {
      setState(newState)
    })

    return unsubscribe
  }, [pageKey])

  // 监听操作变化
  useEffect(() => {
    const unsubscribe = pageStateManager.onOperationChange((operation) => {
      setOperations(prev => {
        const index = prev.findIndex(op => op.id === operation.id)
        if (index >= 0) {
          const newOps = [...prev]
          newOps[index] = operation
          return newOps
        } else {
          return [...prev, operation]
        }
      })
    })

    // 初始化当前操作列表
    setOperations(pageStateManager.getAllOperations())

    return unsubscribe
  }, [])

  // 自动保存机制
  const debouncedSave = useCallback(() => {
    if (!autoSave || !pendingStateRef.current) return

    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }

    saveTimeoutRef.current = setTimeout(() => {
      if (pendingStateRef.current) {
        pageStateManager.saveState(pageKey, pendingStateRef.current)
        pendingStateRef.current = null
      }
    }, saveInterval)
  }, [pageKey, autoSave, saveInterval])

  // 保存状态
  const saveState = useCallback((newState: Partial<PageState>) => {
    if (autoSave) {
      // 累积待保存的状态
      pendingStateRef.current = {
        ...pendingStateRef.current,
        ...newState
      }
      debouncedSave()
    } else {
      // 立即保存
      pageStateManager.saveState(pageKey, newState)
    }
  }, [pageKey, autoSave, debouncedSave])

  // 清除状态
  const clearState = useCallback(() => {
    pageStateManager.clearState(pageKey)
    setState(null)
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }
    pendingStateRef.current = null
  }, [pageKey])

  // 带上下文的导航
  const navigateWithContext = useCallback((url: string, context?: NavigationContext) => {
    const navigationContext: NavigationContext = {
      from: pathname,
      preserveState: true,
      ...context
    }
    
    pageStateManager.pushNavigationContext(navigationContext)
    router.push(url)
  }, [router, pathname])

  // 智能返回
  const smartGoBack = useCallback((defaultUrl: string = '/') => {
    const returnUrl = pageStateManager.smartGoBack(defaultUrl)
    if (returnUrl) {
      router.push(returnUrl)
    }
  }, [router])

  // 操作管理
  const startOperation = useCallback((operation: Omit<OperationProgress, 'startTime'>) => {
    return pageStateManager.startOperation(operation)
  }, [])

  const updateOperation = useCallback((id: string, updates: Partial<OperationProgress>) => {
    pageStateManager.updateOperation(id, updates)
  }, [])

  const completeOperation = useCallback((id: string, success: boolean, message?: string) => {
    pageStateManager.completeOperation(id, success, message)
  }, [])

  // 清理
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
      // 如果有待保存的状态，立即保存
      if (pendingStateRef.current) {
        pageStateManager.saveState(pageKey, pendingStateRef.current)
      }
    }
  }, [pageKey])

  return {
    state,
    saveState,
    clearState,
    isLoading,
    navigateWithContext,
    smartGoBack,
    startOperation,
    updateOperation,
    completeOperation,
    operations
  }
}

/**
 * 列表页面专用Hook
 */
export function useListPageState(pageKey: string) {
  const pageState = usePageState({ pageKey })
  
  const saveListState = useCallback((listState: Partial<PageState['listState']>) => {
    const currentListState = pageState.state?.listState
    pageState.saveState({
      listState: {
        page: currentListState?.page || 1,
        pageSize: currentListState?.pageSize || 20,
        filters: currentListState?.filters || {},
        sortBy: currentListState?.sortBy || '',
        sortOrder: currentListState?.sortOrder || 'asc',
        ...listState
      }
    })
  }, [pageState])

  const getListState = useCallback(() => {
    return pageState.state?.listState || null
  }, [pageState.state])

  return {
    ...pageState,
    saveListState,
    getListState
  }
}

/**
 * 表单页面专用Hook
 */
export function useFormPageState(pageKey: string) {
  const pageState = usePageState({ pageKey })
  
  const saveFormState = useCallback((formState: Partial<PageState['formState']>) => {
    const currentFormState = pageState.state?.formState
    pageState.saveState({
      formState: {
        data: currentFormState?.data || {},
        isDirty: currentFormState?.isDirty || false,
        ...formState
      }
    })
  }, [pageState])

  const getFormState = useCallback(() => {
    return pageState.state?.formState || null
  }, [pageState.state])

  const markFormDirty = useCallback((isDirty: boolean = true) => {
    saveFormState({ isDirty })
  }, [saveFormState])

  return {
    ...pageState,
    saveFormState,
    getFormState,
    markFormDirty
  }
}

/**
 * 批量操作Hook
 */
export function useBatchOperation() {
  const [isRunning, setIsRunning] = useState(false)
  const [progress, setProgress] = useState(0)
  const [errors, setErrors] = useState<string[]>([])

  const executeBatch = useCallback(async <T>(
    items: T[],
    operation: (item: T, index: number) => Promise<void>,
    options: {
      operationId: string
      operationType: OperationProgress['type']
      operationMessage: string
      batchSize?: number
    }
  ) => {
    setIsRunning(true)
    setProgress(0)
    setErrors([])

    try {
      const result = await pageStateManager.executeBatchOperation(
        items,
        operation,
        {
          ...options,
          onProgress: (completed, total) => {
            setProgress(Math.round((completed / total) * 100))
          }
        }
      )

      setErrors(result.errors)
      return result
    } finally {
      setIsRunning(false)
    }
  }, [])

  return {
    isRunning,
    progress,
    errors,
    executeBatch
  }
}

/**
 * 操作反馈Hook
 */
export function useOperationFeedback() {
  const [operations, setOperations] = useState<OperationProgress[]>([])

  useEffect(() => {
    const unsubscribe = pageStateManager.onOperationChange((operation) => {
      setOperations(prev => {
        const index = prev.findIndex(op => op.id === operation.id)
        if (index >= 0) {
          const newOps = [...prev]
          newOps[index] = operation
          return newOps
        } else {
          return [...prev, operation]
        }
      })
    })

    // 初始化
    setOperations(pageStateManager.getAllOperations())

    return unsubscribe
  }, [])

  const getRunningOperations = useCallback(() => {
    return operations.filter(op => op.status === 'running')
  }, [operations])

  const getCompletedOperations = useCallback(() => {
    return operations.filter(op => op.status === 'success' || op.status === 'error')
  }, [operations])

  return {
    operations,
    runningOperations: getRunningOperations(),
    completedOperations: getCompletedOperations()
  }
}
