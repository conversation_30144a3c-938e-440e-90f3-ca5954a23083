'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { navigationConfig, filterNavigationByRoles, type NavigationItem } from '@/config/navigation'
import { UserRole } from '@/types/auth'

interface NavigationState {
  navigation: NavigationItem[]
  isLoading: boolean
  lastUpdated: Date
  permissionVersion: number
}

interface NavigationPermissionChange {
  type: 'added' | 'removed' | 'modified'
  item: NavigationItem
  timestamp: Date
}

/**
 * 动态导航权限管理Hook
 * 实现实时权限检查和导航更新
 */
export function useDynamicNavigation() {
  const { user, isAuthenticated } = useAuth()
  
  const [state, setState] = useState<NavigationState>({
    navigation: [],
    isLoading: true,
    lastUpdated: new Date(),
    permissionVersion: 0
  })
  
  const [permissionChanges, setPermissionChanges] = useState<NavigationPermissionChange[]>([])
  const [permissionListeners, setPermissionListeners] = useState<Set<() => void>>(new Set())

  /**
   * 获取用户角色
   */
  const userRoles = useMemo(() => {
    if (!isAuthenticated || !user) return []
    return user.roles.map(role => role.roleCode) as UserRole[]
  }, [isAuthenticated, user])

  /**
   * 更新导航权限
   */
  const updateNavigationPermissions = useCallback(() => {
    if (!isAuthenticated || !user) {
      setState(prev => ({
        ...prev,
        navigation: [],
        isLoading: false,
        lastUpdated: new Date(),
        permissionVersion: prev.permissionVersion + 1
      }))
      return
    }

    const previousNavigation = state.navigation
    const filteredNavigation = filterNavigationByRoles(navigationConfig, userRoles)

    // 检测权限变更
    const changes: NavigationPermissionChange[] = []
    
    // 检查新增的导航项
    const flattenNavigation = (items: NavigationItem[]): NavigationItem[] => {
      const result: NavigationItem[] = []
      items.forEach(item => {
        result.push(item)
        if (item.children) {
          result.push(...flattenNavigation(item.children))
        }
      })
      return result
    }

    const previousFlat = flattenNavigation(previousNavigation)
    const currentFlat = flattenNavigation(filteredNavigation)

    // 检查新增项
    currentFlat.forEach(item => {
      if (!previousFlat.find(prev => prev.href === item.href)) {
        changes.push({
          type: 'added',
          item,
          timestamp: new Date()
        })
      }
    })

    // 检查移除项
    previousFlat.forEach(item => {
      if (!currentFlat.find(curr => curr.href === item.href)) {
        changes.push({
          type: 'removed',
          item,
          timestamp: new Date()
        })
      }
    })

    if (changes.length > 0) {
      setPermissionChanges(prev => [...changes, ...prev.slice(0, 19)]) // 保留最近20条变更
    }

    setState(prev => ({
      ...prev,
      navigation: filteredNavigation,
      isLoading: false,
      lastUpdated: new Date(),
      permissionVersion: prev.permissionVersion + 1
    }))

    // 通知监听器
    permissionListeners.forEach(listener => listener())
  }, [isAuthenticated, user, userRoles, state.navigation, permissionListeners])

  /**
   * 检查导航项权限
   */
  const checkNavigationPermission = useCallback((item: NavigationItem): boolean => {
    if (!isAuthenticated || !user) {
      return false
    }

    // 如果没有指定角色限制，所有用户都可以访问
    if (!item.roles || item.roles.length === 0) {
      return true
    }

    // 检查用户是否有任一允许的角色
    return item.roles.some(role => userRoles.includes(role))
  }, [isAuthenticated, user, userRoles])

  /**
   * 验证导航路径权限
   */
  const validateNavigationPath = useCallback((pathname: string): boolean => {
    const findNavigationItem = (items: NavigationItem[], path: string): NavigationItem | null => {
      for (const item of items) {
        if (item.href === path) {
          return item
        }
        if (item.children) {
          const found = findNavigationItem(item.children, path)
          if (found) return found
        }
      }
      return null
    }

    const navigationItem = findNavigationItem(state.navigation, pathname)
    if (!navigationItem) {
      // 如果找不到导航项，检查是否是动态路由
      const segments = pathname.split('/').filter(Boolean)
      if (segments.length > 1) {
        // 尝试匹配父路径
        const parentPath = '/' + segments.slice(0, -1).join('/')
        const parentItem = findNavigationItem(state.navigation, parentPath)
        return parentItem ? checkNavigationPermission(parentItem) : false
      }
      return false
    }

    return checkNavigationPermission(navigationItem)
  }, [state.navigation, checkNavigationPermission])

  /**
   * 添加权限变更监听器
   */
  const addPermissionListener = useCallback((listener: () => void) => {
    setPermissionListeners(prev => new Set([...prev, listener]))
    
    // 返回移除监听器的函数
    return () => {
      setPermissionListeners(prev => {
        const newSet = new Set(prev)
        newSet.delete(listener)
        return newSet
      })
    }
  }, [])

  /**
   * 获取导航项的完整路径
   */
  const getNavigationPath = useCallback((href: string): string[] => {
    const findPath = (items: NavigationItem[], targetHref: string, path: string[] = []): string[] | null => {
      for (const item of items) {
        const currentPath = [...path, item.title]
        
        if (item.href === targetHref) {
          return currentPath
        }
        
        if (item.children) {
          const found = findPath(item.children, targetHref, currentPath)
          if (found) return found
        }
      }
      return null
    }

    return findPath(state.navigation, href) || []
  }, [state.navigation])

  /**
   * 获取用户可访问的所有导航项（扁平化）
   */
  const getAllAccessibleItems = useCallback((): NavigationItem[] => {
    const flatten = (items: NavigationItem[]): NavigationItem[] => {
      const result: NavigationItem[] = []
      items.forEach(item => {
        if (item.href && item.href !== '#') {
          result.push(item)
        }
        if (item.children) {
          result.push(...flatten(item.children))
        }
      })
      return result
    }

    return flatten(state.navigation)
  }, [state.navigation])

  /**
   * 强制刷新权限
   */
  const refreshPermissions = useCallback(() => {
    setState(prev => ({ ...prev, isLoading: true }))
    updateNavigationPermissions()
  }, [updateNavigationPermissions])

  // 初始化和权限变更监听
  useEffect(() => {
    updateNavigationPermissions()
  }, [isAuthenticated, user])

  // 定期检查权限变更（可选，用于处理服务端权限变更）
  useEffect(() => {
    const interval = setInterval(() => {
      // 这里可以添加服务端权限检查逻辑
      // 例如：检查用户权限是否在服务端发生变更
    }, 30000) // 每30秒检查一次

    return () => clearInterval(interval)
  }, [])

  return {
    // 状态
    navigation: state.navigation,
    isLoading: state.isLoading,
    lastUpdated: state.lastUpdated,
    permissionVersion: state.permissionVersion,
    permissionChanges,
    userRoles,

    // 方法
    checkNavigationPermission,
    validateNavigationPath,
    addPermissionListener,
    getNavigationPath,
    getAllAccessibleItems,
    refreshPermissions,
    updateNavigationPermissions
  }
}
