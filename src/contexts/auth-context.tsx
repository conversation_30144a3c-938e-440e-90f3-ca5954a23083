"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  AuthContextType,
  User,
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  UserRole
} from '@/types/auth'
import {
  isTokenValid,
  isTokenExpiringSoon,
  clearAuthStorage,
  saveAuthToStorage,
  loadAuthFromStorage
} from '@/lib/client-auth'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  // 为了避免水合错误，初始状态始终为空，在客户端挂载后再加载
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true) // 初始为加载状态
  const [isInitialized, setIsInitialized] = useState(false)
  const router = useRouter()

  // 检查用户是否已认证
  const isAuthenticated = !!user && !!token

  // 在客户端挂载后初始化认证状态
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const { token: storedToken, user: storedUser } = loadAuthFromStorage()

        if (storedToken && storedUser) {
          // 检查token是否有效
          if (isTokenValid(storedToken)) {
            setUser(storedUser)
            setToken(storedToken)
          } else {
            // Token无效，清除存储
            console.warn('⚠️ Token已失效，清除本地存储')
            clearAuthStorage()
          }
        }
      } catch (error) {
        console.error('❌ 加载认证状态失败:', error)
        clearAuthStorage()
      } finally {
        setIsLoading(false)
        setIsInitialized(true)
      }
    }

    // 确保在客户端执行初始化
    initializeAuth()
  }, [])

  // 在客户端挂载后检查token是否即将过期
  useEffect(() => {
    if (token && isTokenExpiringSoon(token)) {
      console.warn('⚠️ Token即将过期，需要刷新')
      // TODO: 实现token刷新
    }
  }, [token])

  // 登录函数
  const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const result: LoginResponse = await response.json()

      if (result.success && result.data) {
        // 保存认证信息
        setUser(result.data.user)
        setToken(result.data.token)

        // 存储到localStorage和Cookie
        saveAuthToStorage(result.data.token, result.data.user, result.data.refreshToken)

        // 给浏览器一些时间来设置Cookie，然后跳转
        setTimeout(() => {
          // 检查是否有重定向参数
          const urlParams = new URLSearchParams(window.location.search)
          const redirectUrl = urlParams.get('redirect')

          if (redirectUrl) {
            // 解码重定向URL并跳转
            try {
              const decodedUrl = decodeURIComponent(redirectUrl)
              // 确保重定向URL是相对路径，防止开放重定向攻击
              if (decodedUrl.startsWith('/') && !decodedUrl.startsWith('//')) {
                router.push(decodedUrl)
                return
              }
            } catch (error) {
              console.warn('重定向URL解码失败:', error)
            }
          }

          // 默认跳转到仪表板
          router.push('/dashboard')
        }, 100)
      }

      return result
    } catch (error) {
      console.error('❌ 登录请求失败:', error)
      return {
        success: false,
        message: '网络错误，请检查网络连接',
      }
    } finally {
      setIsLoading(false)
    }
  }

  // 注册函数
  const register = async (data: RegisterRequest): Promise<LoginResponse> => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result: LoginResponse = await response.json()

      // 注册成功后跳转到登录页
      if (result.success) {
        router.push('/auth/login?message=注册成功，请等待管理员审核')
      }

      return result
    } catch (error) {
      console.error('❌ 注册请求失败:', error)
      return {
        success: false,
        message: '网络错误，请检查网络连接',
      }
    } finally {
      setIsLoading(false)
    }
  }

  // 登出函数
  const logout = () => {
    setUser(null)
    setToken(null)

    // 清除存储
    clearAuthStorage()

    // 跳转到登录页
    router.push('/auth/login')
  }

  // 刷新token函数
  const refreshToken = async (): Promise<boolean> => {
    try {
      const storedRefreshToken = localStorage.getItem('refresh_token')
      if (!storedRefreshToken) {
        return false
      }

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: storedRefreshToken }),
      })

      const result = await response.json()

      if (result.success && result.data) {
        setToken(result.data.token)
        localStorage.setItem('access_token', result.data.token)
        return true
      }

      return false
    } catch (error) {
      console.error('❌ 刷新token失败:', error)
      return false
    }
  }

  // 检查权限函数
  const hasPermission = (permission: string): boolean => {
    if (!user || !user.roles) return false
    
    // 管理员拥有所有权限
    if (user.roles.some(role => role.roleCode === 'ADMIN')) {
      return true
    }
    
    // TODO: 实现具体的权限检查逻辑
    return false
  }

  // 检查角色函数
  const hasRole = (role: UserRole): boolean => {
    if (!user || !user.roles) return false
    return user.roles.some(userRole => userRole.roleCode === role)
  }

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    logout,
    register,
    refreshToken,
    hasPermission,
    hasRole,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
