'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { v4 as uuidv4 } from 'uuid'
import { useLocalStorage } from '@/hooks/use-local-storage'

// 标签页状态类型
export interface TabState {
  id: string
  title: string
  path: string
  icon?: string
  isDirty?: boolean
  isActive: boolean
  params?: Record<string, any>
  closable?: boolean
}

// 标签页上下文类型
interface TabContextType {
  tabs: TabState[]
  activeTabId: string | null
  openTab: (path: string, title: string, options?: { icon?: string, params?: any, closable?: boolean }) => void
  closeTab: (id: string) => void
  activateTab: (id: string) => void
  refreshTab: (id: string) => void
  closeOtherTabs: (id: string) => void
  closeAllTabs: () => void
  closeTabsToRight: (id: string) => void
  markTabDirty: (id: string, isDirty: boolean) => void
  updateTabTitle: (id: string, title: string) => void
}

// 创建上下文
const TabContext = createContext<TabContextType | undefined>(undefined)

// 标签页提供者组件
export function TabProvider({ children }: { children: React.ReactNode }) {
  // 使用本地存储持久化标签页状态
  const [tabs, setTabs] = useLocalStorage<TabState[]>('mediinspect-tabs', [])
  const [activeTabId, setActiveTabId] = useState<string | null>(null)
  
  const router = useRouter()
  const pathname = usePathname()
  
  // 初始化 - 如果没有标签页，添加当前页面作为标签页
  useEffect(() => {
    if (tabs.length === 0 && pathname !== '/auth/login') {
      const newTab = createTab(pathname, getDefaultTitle(pathname))
      setTabs([newTab])
      setActiveTabId(newTab.id)
    }
  }, [pathname, tabs.length])
  
  // 路径变化时同步标签页
  useEffect(() => {
    if (pathname === '/auth/login') return
    
    // 查找匹配当前路径的标签页
    const existingTab = tabs.find(tab => tab.path === pathname)
    
    if (existingTab) {
      // 如果已存在，激活它
      if (!existingTab.isActive) {
        activateTab(existingTab.id)
      }
    } else {
      // 如果不存在，创建新标签页
      const newTab = createTab(pathname, getDefaultTitle(pathname))
      setTabs(prev => [...prev, newTab])
      setActiveTabId(newTab.id)
    }
  }, [pathname])
  
  // 更新标签页活动状态
  useEffect(() => {
    if (activeTabId) {
      setTabs(prev => 
        prev.map(tab => ({
          ...tab,
          isActive: tab.id === activeTabId
        }))
      )
      
      // 同步路由
      const activeTab = tabs.find(tab => tab.id === activeTabId)
      if (activeTab && activeTab.path !== pathname) {
        router.push(activeTab.path)
      }
    }
  }, [activeTabId])
  
  // 创建新标签页
  const createTab = (path: string, title: string, options?: { icon?: string, params?: any, closable?: boolean }): TabState => {
    return {
      id: uuidv4(),
      title,
      path,
      icon: options?.icon,
      params: options?.params,
      isActive: true,
      isDirty: false,
      closable: options?.closable ?? true
    }
  }
  
  // 获取默认标题
  const getDefaultTitle = (path: string): string => {
    // 简单的路径到标题映射
    const pathSegments = path.split('/').filter(Boolean)
    if (pathSegments.length === 0) return '工作台'

    // 将路径转换为标题 (例如: medical-cases -> 医疗案例)
    const pathToTitle: Record<string, string> = {
      'dashboard': '工作台',
      'medical-cases': '医疗案例',
      'supervision-rules': '监管规则',
      'users': '用户管理',
      'settings': '系统设置',
      'knowledge-base': '知识库',
      'analytics': '数据分析'
    }

    const firstSegment = pathSegments[0]
    return firstSegment ? (pathToTitle[firstSegment] || firstSegment.replace(/-/g, ' ')) : '工作台'
  }
  
  // 打开新标签页
  const openTab = (path: string, title: string, options?: { icon?: string, params?: any, closable?: boolean }) => {
    // 检查是否已存在相同路径的标签页
    const existingTab = tabs.find(tab => tab.path === path)
    
    if (existingTab) {
      // 如果已存在，激活它
      activateTab(existingTab.id)
    } else {
      // 如果不存在，创建新标签页
      const newTab = createTab(path, title, options)
      setTabs(prev => [...prev, newTab])
      setActiveTabId(newTab.id)
    }
  }
  
  // 关闭标签页
  const closeTab = (id: string) => {
    // 获取要关闭的标签页
    const tabToClose = tabs.find(tab => tab.id === id)
    if (!tabToClose) return
    
    // 如果标签页不可关闭，直接返回
    if (tabToClose.closable === false) return
    
    // 如果关闭的是当前活动标签页，需要激活另一个标签页
    if (tabToClose.isActive) {
      const tabIndex = tabs.findIndex(tab => tab.id === id)

      // 优先激活右侧标签页，如果没有则激活左侧标签页
      if (tabIndex < tabs.length - 1) {
        const nextTab = tabs[tabIndex + 1]
        if (nextTab) {
          setActiveTabId(nextTab.id)
        }
      } else if (tabIndex > 0) {
        const prevTab = tabs[tabIndex - 1]
        if (prevTab) {
          setActiveTabId(prevTab.id)
        }
      }
    }
    
    // 移除标签页
    setTabs(prev => prev.filter(tab => tab.id !== id))
  }
  
  // 激活标签页
  const activateTab = (id: string) => {
    setActiveTabId(id)
  }
  
  // 刷新标签页
  const refreshTab = (id: string) => {
    const tab = tabs.find(tab => tab.id === id)
    if (!tab) return
    
    // 重新加载当前路径
    if (tab.isActive) {
      router.refresh()
    }
  }
  
  // 关闭其他标签页
  const closeOtherTabs = (id: string) => {
    const tab = tabs.find(tab => tab.id === id)
    if (!tab) return
    
    // 保留不可关闭的标签页和当前标签页
    setTabs(prev => prev.filter(tab => tab.id === id || tab.closable === false))
    setActiveTabId(id)
  }
  
  // 关闭所有标签页
  const closeAllTabs = () => {
    // 只保留不可关闭的标签页
    const nonClosableTabs = tabs.filter(tab => tab.closable === false)
    setTabs(nonClosableTabs)
    
    // 如果有不可关闭的标签页，激活第一个
    if (nonClosableTabs.length > 0) {
      const firstTab = nonClosableTabs[0]
      if (firstTab) {
        setActiveTabId(firstTab.id)
      }
    } else {
      // 如果没有不可关闭的标签页，创建一个工作台标签页
      const newTab = createTab('/dashboard', '工作台', { closable: false })
      setTabs([newTab])
      setActiveTabId(newTab.id)
    }
  }
  
  // 关闭右侧标签页
  const closeTabsToRight = (id: string) => {
    const tabIndex = tabs.findIndex(tab => tab.id === id)
    if (tabIndex === -1) return
    
    // 保留左侧标签页和不可关闭的右侧标签页
    setTabs(prev => [
      ...prev.slice(0, tabIndex + 1),
      ...prev.slice(tabIndex + 1).filter(tab => tab.closable === false)
    ])
  }
  
  // 标记标签页为脏状态
  const markTabDirty = (id: string, isDirty: boolean) => {
    setTabs(prev => 
      prev.map(tab => 
        tab.id === id ? { ...tab, isDirty } : tab
      )
    )
  }
  
  // 更新标签页标题
  const updateTabTitle = (id: string, title: string) => {
    setTabs(prev => 
      prev.map(tab => 
        tab.id === id ? { ...tab, title } : tab
      )
    )
  }
  
  // 上下文值
  const contextValue: TabContextType = {
    tabs,
    activeTabId,
    openTab,
    closeTab,
    activateTab,
    refreshTab,
    closeOtherTabs,
    closeAllTabs,
    closeTabsToRight,
    markTabDirty,
    updateTabTitle
  }
  
  return (
    <TabContext.Provider value={contextValue}>
      {children}
    </TabContext.Provider>
  )
}

// 使用标签页上下文的钩子
export function useTabs() {
  const context = useContext(TabContext)
  if (context === undefined) {
    throw new Error('useTabs must be used within a TabProvider')
  }
  return context
}

// 导出类型
export type { TabContextType }
