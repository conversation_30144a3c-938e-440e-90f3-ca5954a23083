# 医保基金监管平台 - 数据库设计文档

## 📋 目录

- [1. 设计概述](#1-设计概述)
- [2. 设计原则](#2-设计原则)
- [3. 核心数据模型](#3-核心数据模型)
- [4. 索引设计策略](#4-索引设计策略)
- [5. 性能优化](#5-性能优化)
- [6. 数据安全](#6-数据安全)
- [7. 扩展性设计](#7-扩展性设计)

## 1. 设计概述

### 1.1 设计目标
构建高性能、高可用、易维护的数据存储和管理系统，支持医疗保险基金的智能化监督管理。

### 1.2 技术选型
- **主数据库**: Oracle Database 12c+ (业务数据)
- **分析数据库**: ClickHouse (大数据分析)
- **搜索引擎**: Elasticsearch (知识库搜索)
- **缓存**: Redis (高性能缓存)
- **字符集**: UTF-8 (支持多语言)

### 1.3 架构特点
- **读写分离**: Oracle主从架构，提升查询性能
- **专业分析**: ClickHouse处理复杂统计分析
- **全文搜索**: Elasticsearch支持知识库检索
- **多层缓存**: Redis + 应用缓存提升响应速度

## 2. 设计原则

### 2.1 命名规范

#### 2.1.1 表命名规范
- **格式**: `模块前缀_业务名称`
- **模块前缀**:
  - `USER_`: 用户管理模块
  - `MEDICAL_`: 医疗病例模块
  - `RULE_`: 监管规则模块
  - `KNOWLEDGE_`: 知识库模块
  - `SYSTEM_`: 系统日志模块

#### 2.1.2 字段命名规范
- **主键字段**: 统一使用 `ID`
- **外键字段**: 使用 `表名_ID` 格式
- **时间字段**: 统一使用 `_AT` 后缀
- **标识字段**: 统一使用 `IS_` 前缀
- **状态字段**: 使用 `STATUS` 或具体状态名

#### 2.1.3 约束命名规范
- **主键约束**: `PK_表名`
- **外键约束**: `FK_表名_引用字段`
- **唯一约束**: `UK_表名_字段名`
- **检查约束**: `CK_表名_字段名`

### 2.2 数据类型标准

| 用途分类 | 数据类型 | 设计考虑 |
|----------|----------|----------|
| 主键ID | `NUMBER(19,0)` | 支持大数据量，避免溢出 |
| 外键ID | `NUMBER(19,0)` | 与主键保持一致 |
| 短文本 | `VARCHAR2(长度)` | 根据业务需求设置合适长度 |
| 长文本 | `CLOB` | 存储大量文本内容 |
| 精确数值 | `NUMBER(精度,小数位)` | 金额等精确计算场景 |
| 日期时间 | `DATE/TIMESTAMP` | DATE用于日期，TIMESTAMP用于精确时间 |
| 布尔标识 | `NUMBER(1,0)` | 0=false, 1=true，便于查询 |
| 枚举状态 | `VARCHAR2(20)` | 使用英文常量，便于国际化 |

### 2.3 审计设计规范

#### 2.3.1 标准审计字段
所有业务表必须包含以下审计字段：
```sql
CREATED_AT    DATE DEFAULT SYSDATE NOT NULL,  -- 创建时间
UPDATED_AT    DATE DEFAULT SYSDATE NOT NULL,  -- 更新时间
CREATED_BY    NUMBER(19,0),                   -- 创建人ID
UPDATED_BY    NUMBER(19,0)                    -- 更新人ID
```

#### 2.3.2 软删除设计
```sql
IS_DELETED    NUMBER(1,0) DEFAULT 0 NOT NULL  -- 软删除标识
```

## 3. 核心数据模型

### 3.1 用户管理模块

```sql
-- 用户信息表
CREATE TABLE USER_INFO (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 用户ID，主键
  USERNAME VARCHAR2(50) UNIQUE NOT NULL,          -- 用户名，唯一
  EMAIL VARCHAR2(100) UNIQUE NOT NULL,            -- 邮箱地址，唯一
  PASSWORD_HASH VARCHAR2(255) NOT NULL,           -- 密码哈希值
  REAL_NAME VARCHAR2(100) NOT NULL,               -- 真实姓名
  PHONE VARCHAR2(20),                             -- 手机号码
  DEPARTMENT_ID NUMBER(19,0),                     -- 所属部门ID
  STATUS VARCHAR2(20) DEFAULT 'ACTIVE',           -- 用户状态：ACTIVE-激活，INACTIVE-停用，LOCKED-锁定
  LAST_LOGIN_AT DATE,                             -- 最后登录时间
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE,                -- 更新时间
  CREATED_BY NUMBER(19,0),                        -- 创建人ID
  UPDATED_BY NUMBER(19,0)                         -- 更新人ID
);

-- 添加表注释
COMMENT ON TABLE USER_INFO IS '用户信息表，存储系统用户的基本信息';
COMMENT ON COLUMN USER_INFO.ID IS '用户ID，主键，自增';
COMMENT ON COLUMN USER_INFO.USERNAME IS '用户名，系统登录标识，唯一';
COMMENT ON COLUMN USER_INFO.EMAIL IS '邮箱地址，用于找回密码等功能，唯一';
COMMENT ON COLUMN USER_INFO.PASSWORD_HASH IS '密码哈希值，使用bcrypt加密';
COMMENT ON COLUMN USER_INFO.REAL_NAME IS '用户真实姓名';
COMMENT ON COLUMN USER_INFO.PHONE IS '手机号码，可选';
COMMENT ON COLUMN USER_INFO.DEPARTMENT_ID IS '所属部门ID，关联部门表';
COMMENT ON COLUMN USER_INFO.STATUS IS '用户状态：ACTIVE-激活，INACTIVE-停用，LOCKED-锁定';
COMMENT ON COLUMN USER_INFO.LAST_LOGIN_AT IS '最后登录时间';
COMMENT ON COLUMN USER_INFO.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN USER_INFO.UPDATED_AT IS '记录更新时间';
COMMENT ON COLUMN USER_INFO.CREATED_BY IS '创建人用户ID';
COMMENT ON COLUMN USER_INFO.UPDATED_BY IS '更新人用户ID';

-- 角色信息表
CREATE TABLE USER_ROLE_INFO (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 角色ID，主键
  ROLE_NAME VARCHAR2(50) UNIQUE NOT NULL,         -- 角色名称，唯一
  ROLE_CODE VARCHAR2(20) UNIQUE NOT NULL,         -- 角色编码，唯一
  DESCRIPTION VARCHAR2(500),                      -- 角色描述
  IS_SYSTEM NUMBER(1,0) DEFAULT 0,                -- 是否系统角色：0-否，1-是
  STATUS VARCHAR2(20) DEFAULT 'ACTIVE',           -- 角色状态：ACTIVE-激活，INACTIVE-停用
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE                 -- 更新时间
);

-- 添加表注释
COMMENT ON TABLE USER_ROLE_INFO IS '角色信息表，定义系统中的各种角色';
COMMENT ON COLUMN USER_ROLE_INFO.ID IS '角色ID，主键，自增';
COMMENT ON COLUMN USER_ROLE_INFO.ROLE_NAME IS '角色名称，如：系统管理员、业务操作员等';
COMMENT ON COLUMN USER_ROLE_INFO.ROLE_CODE IS '角色编码，如：ADMIN、OPERATOR等';
COMMENT ON COLUMN USER_ROLE_INFO.DESCRIPTION IS '角色功能描述';
COMMENT ON COLUMN USER_ROLE_INFO.IS_SYSTEM IS '是否系统内置角色：0-否，1-是（系统角色不可删除）';
COMMENT ON COLUMN USER_ROLE_INFO.STATUS IS '角色状态：ACTIVE-激活，INACTIVE-停用';
COMMENT ON COLUMN USER_ROLE_INFO.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN USER_ROLE_INFO.UPDATED_AT IS '记录更新时间';

-- 用户角色关联表
CREATE TABLE USER_ROLE_MAPPING (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 关联ID，主键
  USER_ID NUMBER(19,0) NOT NULL,                  -- 用户ID
  ROLE_ID NUMBER(19,0) NOT NULL,                  -- 角色ID
  ASSIGNED_AT DATE DEFAULT SYSDATE,               -- 分配时间
  ASSIGNED_BY NUMBER(19,0),                       -- 分配人ID
  CONSTRAINT FK_URM_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
  CONSTRAINT FK_URM_ROLE FOREIGN KEY (ROLE_ID) REFERENCES USER_ROLE_INFO(ID)
);

-- 添加表注释
COMMENT ON TABLE USER_ROLE_MAPPING IS '用户角色关联表，建立用户与角色的多对多关系';
COMMENT ON COLUMN USER_ROLE_MAPPING.ID IS '关联记录ID，主键，自增';
COMMENT ON COLUMN USER_ROLE_MAPPING.USER_ID IS '用户ID，外键关联USER_INFO表';
COMMENT ON COLUMN USER_ROLE_MAPPING.ROLE_ID IS '角色ID，外键关联USER_ROLE_INFO表';
COMMENT ON COLUMN USER_ROLE_MAPPING.ASSIGNED_AT IS '角色分配时间';
COMMENT ON COLUMN USER_ROLE_MAPPING.ASSIGNED_BY IS '执行角色分配操作的用户ID';
```

### 3.2 医疗病例模块

```sql
-- 医疗病例主表
CREATE TABLE MEDICAL_CASE (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 病例ID，主键
  CASE_NUMBER VARCHAR2(50) UNIQUE NOT NULL,       -- 病例编号，唯一
  -- 患者信息
  PATIENT_NAME VARCHAR2(100) NOT NULL,            -- 患者姓名
  PATIENT_ID_CARD VARCHAR2(18) NOT NULL,          -- 患者身份证号
  PATIENT_GENDER VARCHAR2(10),                    -- 患者性别：MALE-男，FEMALE-女
  PATIENT_AGE NUMBER(3,0),                        -- 患者年龄
  PATIENT_PHONE VARCHAR2(20),                     -- 患者联系电话
  -- 医疗机构信息
  HOSPITAL_CODE VARCHAR2(20) NOT NULL,            -- 医院编码
  HOSPITAL_NAME VARCHAR2(200) NOT NULL,           -- 医院名称
  DEPARTMENT_NAME VARCHAR2(100),                  -- 科室名称
  DOCTOR_NAME VARCHAR2(100),                      -- 主治医生姓名
  -- 病例分类
  CASE_TYPE VARCHAR2(20) NOT NULL,                -- 病例类型：OUTPATIENT-门诊，INPATIENT-住院
  MEDICAL_TYPE VARCHAR2(20) NOT NULL,             -- 医疗类型：GENERAL-普通，EMERGENCY-急诊，ICU-重症等
  -- 时间信息
  ADMISSION_DATE DATE NOT NULL,                   -- 入院/就诊日期
  DISCHARGE_DATE DATE,                            -- 出院日期（门诊可为空）
  -- 费用信息
  TOTAL_COST NUMBER(12,2) NOT NULL,               -- 总费用金额
  INSURANCE_COST NUMBER(12,2),                    -- 医保支付金额
  PERSONAL_COST NUMBER(12,2),                     -- 个人支付金额
  -- 入组信息
  DRG_CODE VARCHAR2(20),                          -- DRG分组编码
  DRG_WEIGHT NUMBER(8,4),                         -- DRG权重系数
  CASE_GROUP VARCHAR2(50),                        -- 病例分组名称
  -- 状态和审计
  STATUS VARCHAR2(20) DEFAULT 'ACTIVE',           -- 记录状态：ACTIVE-有效，DELETED-已删除
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE,                -- 更新时间
  CREATED_BY NUMBER(19,0),                        -- 创建人ID
  UPDATED_BY NUMBER(19,0)                         -- 更新人ID
);

-- 添加表注释
COMMENT ON TABLE MEDICAL_CASE IS '医疗病例主表，存储使用医保基金结算的病例基本信息';
COMMENT ON COLUMN MEDICAL_CASE.ID IS '病例ID，主键，自增';
COMMENT ON COLUMN MEDICAL_CASE.CASE_NUMBER IS '病例编号，系统自动生成，格式：MC+年月+6位序号';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_NAME IS '患者真实姓名';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_ID_CARD IS '患者身份证号码，18位';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_GENDER IS '患者性别：MALE-男性，FEMALE-女性';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_AGE IS '患者年龄，单位：岁';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_PHONE IS '患者联系电话';
COMMENT ON COLUMN MEDICAL_CASE.HOSPITAL_CODE IS '医院统一编码';
COMMENT ON COLUMN MEDICAL_CASE.HOSPITAL_NAME IS '医院全称';
COMMENT ON COLUMN MEDICAL_CASE.DEPARTMENT_NAME IS '就诊科室名称';
COMMENT ON COLUMN MEDICAL_CASE.DOCTOR_NAME IS '主治医生姓名';
COMMENT ON COLUMN MEDICAL_CASE.CASE_TYPE IS '病例类型：OUTPATIENT-门诊，INPATIENT-住院';
COMMENT ON COLUMN MEDICAL_CASE.MEDICAL_TYPE IS '医疗类型：GENERAL-普通，EMERGENCY-急诊，ICU-重症监护等';
COMMENT ON COLUMN MEDICAL_CASE.ADMISSION_DATE IS '入院日期或门诊就诊日期';
COMMENT ON COLUMN MEDICAL_CASE.DISCHARGE_DATE IS '出院日期，门诊病例可为空';
COMMENT ON COLUMN MEDICAL_CASE.TOTAL_COST IS '总医疗费用，单位：元';
COMMENT ON COLUMN MEDICAL_CASE.INSURANCE_COST IS '医保基金支付金额，单位：元';
COMMENT ON COLUMN MEDICAL_CASE.PERSONAL_COST IS '个人自付金额，单位：元';
COMMENT ON COLUMN MEDICAL_CASE.DRG_CODE IS 'DRG分组编码，用于医保结算';
COMMENT ON COLUMN MEDICAL_CASE.DRG_WEIGHT IS 'DRG权重系数，用于费用计算';
COMMENT ON COLUMN MEDICAL_CASE.CASE_GROUP IS '病例分组名称';
COMMENT ON COLUMN MEDICAL_CASE.STATUS IS '记录状态：ACTIVE-有效，DELETED-已删除';
COMMENT ON COLUMN MEDICAL_CASE.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN MEDICAL_CASE.UPDATED_AT IS '记录更新时间';
COMMENT ON COLUMN MEDICAL_CASE.CREATED_BY IS '创建人用户ID';
COMMENT ON COLUMN MEDICAL_CASE.UPDATED_BY IS '更新人用户ID';

-- 诊断信息表
CREATE TABLE MEDICAL_DIAGNOSIS (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 诊断ID，主键
  CASE_ID NUMBER(19,0) NOT NULL,                  -- 病例ID，外键
  DIAGNOSIS_TYPE VARCHAR2(20) NOT NULL,           -- 诊断类型：PRIMARY-主要诊断，SECONDARY-次要诊断
  DIAGNOSIS_CODE VARCHAR2(20) NOT NULL,           -- ICD-10诊断代码
  DIAGNOSIS_NAME VARCHAR2(200) NOT NULL,          -- 诊断名称
  DIAGNOSIS_DATE DATE,                            -- 诊断日期
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  CONSTRAINT FK_MD_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID)
);

-- 添加表注释
COMMENT ON TABLE MEDICAL_DIAGNOSIS IS '医疗诊断信息表，存储病例的诊断信息';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.ID IS '诊断记录ID，主键，自增';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.CASE_ID IS '关联的病例ID，外键';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_TYPE IS '诊断类型：PRIMARY-主要诊断，SECONDARY-次要诊断';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_CODE IS 'ICD-10国际疾病分类代码';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_NAME IS '诊断疾病名称';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_DATE IS '确诊日期';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.CREATED_AT IS '记录创建时间';

-- 手术信息表
CREATE TABLE MEDICAL_SURGERY (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 手术ID，主键
  CASE_ID NUMBER(19,0) NOT NULL,                  -- 病例ID，外键
  SURGERY_CODE VARCHAR2(20) NOT NULL,             -- 手术操作编码
  SURGERY_NAME VARCHAR2(200) NOT NULL,            -- 手术名称
  SURGERY_DATE DATE NOT NULL,                     -- 手术日期
  SURGEON_NAME VARCHAR2(100),                     -- 主刀医生姓名
  ANESTHESIA_TYPE VARCHAR2(50),                   -- 麻醉方式
  SURGERY_LEVEL VARCHAR2(20),                     -- 手术级别：1-4级
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  CONSTRAINT FK_MS_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID)
);

-- 添加表注释
COMMENT ON TABLE MEDICAL_SURGERY IS '手术信息表，存储病例的手术操作信息';
COMMENT ON COLUMN MEDICAL_SURGERY.ID IS '手术记录ID，主键，自增';
COMMENT ON COLUMN MEDICAL_SURGERY.CASE_ID IS '关联的病例ID，外键';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_CODE IS '手术操作编码，按国家标准';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_NAME IS '手术操作名称';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_DATE IS '手术实施日期';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGEON_NAME IS '主刀医生姓名';
COMMENT ON COLUMN MEDICAL_SURGERY.ANESTHESIA_TYPE IS '麻醉方式：全麻、局麻、椎管内麻醉等';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_LEVEL IS '手术级别：1级-小手术，2级-中等手术，3级-大手术，4级-特大手术';
COMMENT ON COLUMN MEDICAL_SURGERY.CREATED_AT IS '记录创建时间';

-- 费用明细表
CREATE TABLE MEDICAL_COST_DETAIL (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 费用明细ID，主键
  CASE_ID NUMBER(19,0) NOT NULL,                  -- 病例ID，外键
  ITEM_CODE VARCHAR2(50) NOT NULL,                -- 收费项目代码
  ITEM_NAME VARCHAR2(200) NOT NULL,               -- 收费项目名称
  ITEM_TYPE VARCHAR2(50) NOT NULL,                -- 费用类型
  UNIT_PRICE NUMBER(10,2) NOT NULL,               -- 单价
  QUANTITY NUMBER(10,2) NOT NULL,                 -- 数量
  TOTAL_AMOUNT NUMBER(12,2) NOT NULL,             -- 总金额
  INSURANCE_AMOUNT NUMBER(12,2),                  -- 医保支付金额
  PERSONAL_AMOUNT NUMBER(12,2),                   -- 个人支付金额
  CHARGED_AT DATE NOT NULL,                       -- 收费时间
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  CONSTRAINT FK_MCD_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID)
);

-- 添加表注释
COMMENT ON TABLE MEDICAL_COST_DETAIL IS '费用明细表，存储病例的详细费用信息';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ID IS '费用明细ID，主键，自增';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.CASE_ID IS '关联的病例ID，外键';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ITEM_CODE IS '收费项目代码，按国家标准';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ITEM_NAME IS '收费项目名称';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ITEM_TYPE IS '费用类型：药品费、检查费、治疗费、手术费、材料费等';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.UNIT_PRICE IS '项目单价，单位：元';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.QUANTITY IS '使用数量或次数';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.TOTAL_AMOUNT IS '该项目总金额，单位：元';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.INSURANCE_AMOUNT IS '医保基金支付金额，单位：元';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.PERSONAL_AMOUNT IS '个人自付金额，单位：元';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.CHARGED_AT IS '收费发生时间';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.CREATED_AT IS '记录创建时间';

-- 入组信息表
CREATE TABLE MEDICAL_CASE_GROUP (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 入组信息ID，主键
  CASE_ID NUMBER(19,0) NOT NULL,                  -- 病例ID，外键
  GROUP_TYPE VARCHAR2(20) NOT NULL,               -- 分组类型
  GROUP_CODE VARCHAR2(20) NOT NULL,               -- 分组代码
  GROUP_NAME VARCHAR2(200),                       -- 分组名称
  WEIGHT_COEFFICIENT NUMBER(8,4),                 -- 权重系数
  BASE_RATE NUMBER(10,2),                         -- 基础费率
  SETTLEMENT_AMOUNT NUMBER(12,2),                 -- 结算金额
  GROUP_DATE DATE DEFAULT SYSDATE,                -- 分组时间
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  CONSTRAINT FK_MCG_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID)
);

-- 添加表注释
COMMENT ON TABLE MEDICAL_CASE_GROUP IS '病例入组信息表，存储病例的DRG/DIP分组信息';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.ID IS '入组信息ID，主键，自增';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.CASE_ID IS '关联的病例ID，外键';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_TYPE IS '分组类型：DRG-诊断相关分组，DIP-区域点数法等';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_CODE IS '分组代码，如DRG编码';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_NAME IS '分组名称描述';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.WEIGHT_COEFFICIENT IS '权重系数，用于费用计算';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.BASE_RATE IS '基础费率，单位：元';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.SETTLEMENT_AMOUNT IS '按分组计算的结算金额，单位：元';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_DATE IS '分组计算日期';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.CREATED_AT IS '记录创建时间';
```

### 3.3 监管规则模块

```sql
-- 规则模板表
CREATE TABLE RULE_TEMPLATE (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 模板ID，主键
  TEMPLATE_NAME VARCHAR2(200) NOT NULL,           -- 模板名称
  TEMPLATE_CODE VARCHAR2(50) UNIQUE NOT NULL,     -- 模板编码，唯一
  CATEGORY VARCHAR2(50) NOT NULL,                 -- 模板分类
  DESCRIPTION CLOB,                               -- 模板描述
  RULE_LOGIC CLOB NOT NULL,                       -- 规则逻辑代码
  PARAMETERS CLOB,                                -- 参数配置JSON
  IS_ACTIVE NUMBER(1,0) DEFAULT 1,                -- 是否启用：0-否，1-是
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE,                -- 更新时间
  CREATED_BY NUMBER(19,0),                        -- 创建人ID
  UPDATED_BY NUMBER(19,0)                         -- 更新人ID
);

-- 添加表注释
COMMENT ON TABLE RULE_TEMPLATE IS '监管规则模板表，存储可复用的规则模板';
COMMENT ON COLUMN RULE_TEMPLATE.ID IS '模板ID，主键，自增';
COMMENT ON COLUMN RULE_TEMPLATE.TEMPLATE_NAME IS '规则模板名称';
COMMENT ON COLUMN RULE_TEMPLATE.TEMPLATE_CODE IS '模板编码，系统内唯一标识';
COMMENT ON COLUMN RULE_TEMPLATE.CATEGORY IS '模板分类：费用监管、行为监管、合规检查等';
COMMENT ON COLUMN RULE_TEMPLATE.DESCRIPTION IS '模板功能描述和使用说明';
COMMENT ON COLUMN RULE_TEMPLATE.RULE_LOGIC IS '规则逻辑代码，支持SQL或DSL';
COMMENT ON COLUMN RULE_TEMPLATE.PARAMETERS IS '参数配置，JSON格式存储';
COMMENT ON COLUMN RULE_TEMPLATE.IS_ACTIVE IS '是否启用：0-禁用，1-启用';
COMMENT ON COLUMN RULE_TEMPLATE.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN RULE_TEMPLATE.UPDATED_AT IS '记录更新时间';
COMMENT ON COLUMN RULE_TEMPLATE.CREATED_BY IS '创建人用户ID';
COMMENT ON COLUMN RULE_TEMPLATE.UPDATED_BY IS '更新人用户ID';

-- 监管规则表
CREATE TABLE RULE_SUPERVISION (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 规则ID，主键
  RULE_NAME VARCHAR2(200) NOT NULL,               -- 规则名称
  RULE_CODE VARCHAR2(50) UNIQUE NOT NULL,         -- 规则编码，唯一
  TEMPLATE_ID NUMBER(19,0),                       -- 关联模板ID
  RULE_TYPE VARCHAR2(50) NOT NULL,                -- 规则类型
  PRIORITY NUMBER(2,0) DEFAULT 5,                 -- 执行优先级：1-10
  RULE_LOGIC CLOB NOT NULL,                       -- 规则逻辑代码
  PARAMETERS CLOB,                                -- 参数配置JSON
  SCHEDULE_CONFIG CLOB,                           -- 调度配置JSON
  IS_ACTIVE NUMBER(1,0) DEFAULT 1,                -- 是否启用：0-否，1-是
  LAST_EXECUTED_AT DATE,                          -- 最后执行时间
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE,                -- 更新时间
  CREATED_BY NUMBER(19,0),                        -- 创建人ID
  UPDATED_BY NUMBER(19,0),                        -- 更新人ID
  CONSTRAINT FK_RS_TEMPLATE FOREIGN KEY (TEMPLATE_ID) REFERENCES RULE_TEMPLATE(ID)
);

-- 添加表注释
COMMENT ON TABLE RULE_SUPERVISION IS '监管规则表，存储具体的监管规则实例';
COMMENT ON COLUMN RULE_SUPERVISION.ID IS '规则ID，主键，自增';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_NAME IS '规则名称';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_CODE IS '规则编码，系统内唯一标识';
COMMENT ON COLUMN RULE_SUPERVISION.TEMPLATE_ID IS '关联的规则模板ID，可为空';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_TYPE IS '规则类型：COST-费用监管，BEHAVIOR-行为监管，COMPLIANCE-合规检查等';
COMMENT ON COLUMN RULE_SUPERVISION.PRIORITY IS '执行优先级，1-10，数字越小优先级越高';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_LOGIC IS '规则逻辑代码，支持SQL或DSL';
COMMENT ON COLUMN RULE_SUPERVISION.PARAMETERS IS '规则参数配置，JSON格式';
COMMENT ON COLUMN RULE_SUPERVISION.SCHEDULE_CONFIG IS '调度配置：执行频率、时间等，JSON格式';
COMMENT ON COLUMN RULE_SUPERVISION.IS_ACTIVE IS '是否启用：0-禁用，1-启用';
COMMENT ON COLUMN RULE_SUPERVISION.LAST_EXECUTED_AT IS '最后一次执行时间';
COMMENT ON COLUMN RULE_SUPERVISION.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN RULE_SUPERVISION.UPDATED_AT IS '记录更新时间';
COMMENT ON COLUMN RULE_SUPERVISION.CREATED_BY IS '创建人用户ID';
COMMENT ON COLUMN RULE_SUPERVISION.UPDATED_BY IS '更新人用户ID';

-- 规则执行记录表
CREATE TABLE RULE_EXECUTION_LOG (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 执行记录ID，主键
  RULE_ID NUMBER(19,0) NOT NULL,                  -- 规则ID，外键
  EXECUTION_ID VARCHAR2(50) NOT NULL,             -- 执行批次ID
  STATUS VARCHAR2(20) NOT NULL,                   -- 执行状态
  START_TIME DATE NOT NULL,                       -- 开始时间
  END_TIME DATE,                                  -- 结束时间
  EXECUTION_DURATION NUMBER(10,0),                -- 执行耗时（秒）
  PROCESSED_COUNT NUMBER(10,0),                   -- 处理记录数
  SUCCESS_COUNT NUMBER(10,0),                     -- 成功处理数
  ERROR_COUNT NUMBER(10,0),                       -- 错误处理数
  ERROR_MESSAGE CLOB,                             -- 错误信息
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  CONSTRAINT FK_REL_RULE FOREIGN KEY (RULE_ID) REFERENCES RULE_SUPERVISION(ID)
);

-- 添加表注释
COMMENT ON TABLE RULE_EXECUTION_LOG IS '规则执行记录表，记录每次规则执行的详细信息';
COMMENT ON COLUMN RULE_EXECUTION_LOG.ID IS '执行记录ID，主键，自增';
COMMENT ON COLUMN RULE_EXECUTION_LOG.RULE_ID IS '关联的规则ID，外键';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_ID IS '执行批次唯一标识，UUID格式';
COMMENT ON COLUMN RULE_EXECUTION_LOG.STATUS IS '执行状态：PENDING-等待，RUNNING-执行中，COMPLETED-完成，FAILED-失败';
COMMENT ON COLUMN RULE_EXECUTION_LOG.START_TIME IS '规则执行开始时间';
COMMENT ON COLUMN RULE_EXECUTION_LOG.END_TIME IS '规则执行结束时间';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_DURATION IS '执行耗时，单位：秒';
COMMENT ON COLUMN RULE_EXECUTION_LOG.PROCESSED_COUNT IS '本次执行处理的记录总数';
COMMENT ON COLUMN RULE_EXECUTION_LOG.SUCCESS_COUNT IS '成功处理的记录数';
COMMENT ON COLUMN RULE_EXECUTION_LOG.ERROR_COUNT IS '处理失败的记录数';
COMMENT ON COLUMN RULE_EXECUTION_LOG.ERROR_MESSAGE IS '执行过程中的错误信息';
COMMENT ON COLUMN RULE_EXECUTION_LOG.CREATED_AT IS '记录创建时间';

-- 规则执行结果表
CREATE TABLE RULE_EXECUTION_RESULT (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 结果ID，主键
  EXECUTION_LOG_ID NUMBER(19,0) NOT NULL,         -- 执行记录ID，外键
  CASE_ID NUMBER(19,0) NOT NULL,                  -- 病例ID，外键
  RESULT_TYPE VARCHAR2(20) NOT NULL,              -- 结果类型
  RESULT_DESCRIPTION VARCHAR2(1000),              -- 结果描述
  RISK_LEVEL NUMBER(2,0),                         -- 风险等级：1-5
  AMOUNT_INVOLVED NUMBER(12,2),                   -- 涉及金额
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  CONSTRAINT FK_RER_LOG FOREIGN KEY (EXECUTION_LOG_ID) REFERENCES RULE_EXECUTION_LOG(ID),
  CONSTRAINT FK_RER_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID)
);

-- 添加表注释
COMMENT ON TABLE RULE_EXECUTION_RESULT IS '规则执行结果表，存储规则执行后的具体结果';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.ID IS '结果记录ID，主键，自增';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.EXECUTION_LOG_ID IS '关联的执行记录ID，外键';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.CASE_ID IS '涉及的病例ID，外键';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RESULT_TYPE IS '结果类型：NORMAL-正常，WARNING-预警，VIOLATION-违规';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RESULT_DESCRIPTION IS '结果详细描述';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RISK_LEVEL IS '风险等级：1-低风险，2-较低风险，3-中等风险，4-较高风险，5-高风险';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.AMOUNT_INVOLVED IS '涉及的金额，单位：元';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.CREATED_AT IS '记录创建时间';
```

### 3.4 知识库模块

```sql
-- 知识分类表
CREATE TABLE KNOWLEDGE_CATEGORY (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 分类ID，主键
  CATEGORY_NAME VARCHAR2(100) NOT NULL,           -- 分类名称
  CATEGORY_CODE VARCHAR2(50) UNIQUE NOT NULL,     -- 分类编码，唯一
  PARENT_ID NUMBER(19,0),                         -- 父分类ID
  SORT_ORDER NUMBER(3,0) DEFAULT 0,               -- 排序序号
  DESCRIPTION VARCHAR2(500),                      -- 分类描述
  IS_ACTIVE NUMBER(1,0) DEFAULT 1,                -- 是否启用：0-否，1-是
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE,                -- 更新时间
  CONSTRAINT FK_KC_PARENT FOREIGN KEY (PARENT_ID) REFERENCES KNOWLEDGE_CATEGORY(ID)
);

-- 添加表注释
COMMENT ON TABLE KNOWLEDGE_CATEGORY IS '知识分类表，支持树形结构的知识分类管理';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.ID IS '分类ID，主键，自增';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_NAME IS '分类名称';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_CODE IS '分类编码，系统内唯一标识';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.PARENT_ID IS '父分类ID，支持多级分类，根分类为NULL';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.SORT_ORDER IS '同级分类的排序序号，数字越小排序越靠前';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.DESCRIPTION IS '分类描述说明';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.IS_ACTIVE IS '是否启用：0-禁用，1-启用';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.UPDATED_AT IS '记录更新时间';

-- 知识文档表
CREATE TABLE KNOWLEDGE_DOCUMENT (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 文档ID，主键
  TITLE VARCHAR2(200) NOT NULL,                   -- 文档标题
  CONTENT CLOB NOT NULL,                          -- 文档内容
  CATEGORY_ID NUMBER(19,0) NOT NULL,              -- 分类ID，外键
  DOCUMENT_TYPE VARCHAR2(20) NOT NULL,            -- 文档类型
  VERSION VARCHAR2(20) DEFAULT '1.0',             -- 文档版本
  STATUS VARCHAR2(20) DEFAULT 'DRAFT',            -- 文档状态
  TAGS VARCHAR2(500),                             -- 标签，逗号分隔
  AUTHOR_ID NUMBER(19,0),                         -- 作者ID，外键
  PUBLISH_DATE DATE,                              -- 发布日期
  VIEW_COUNT NUMBER(10,0) DEFAULT 0,              -- 浏览次数
  CREATED_AT DATE DEFAULT SYSDATE,                -- 创建时间
  UPDATED_AT DATE DEFAULT SYSDATE,                -- 更新时间
  CREATED_BY NUMBER(19,0),                        -- 创建人ID
  UPDATED_BY NUMBER(19,0),                        -- 更新人ID
  CONSTRAINT FK_KD_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
  CONSTRAINT FK_KD_AUTHOR FOREIGN KEY (AUTHOR_ID) REFERENCES USER_INFO(ID)
);

-- 添加表注释
COMMENT ON TABLE KNOWLEDGE_DOCUMENT IS '知识文档表，存储医保政策、指南等知识文档';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ID IS '文档ID，主键，自增';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.TITLE IS '文档标题';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CONTENT IS '文档正文内容，支持富文本';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CATEGORY_ID IS '所属分类ID，外键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOCUMENT_TYPE IS '文档类型：POLICY-政策，GUIDE-指南，FAQ-常见问题，NOTICE-通知';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VERSION IS '文档版本号，如：1.0、1.1、2.0等';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.STATUS IS '文档状态：DRAFT-草稿，PUBLISHED-已发布，ARCHIVED-已归档';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.TAGS IS '文档标签，多个标签用逗号分隔，便于搜索';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.AUTHOR_ID IS '文档作者用户ID，外键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PUBLISH_DATE IS '文档发布日期';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VIEW_COUNT IS '文档浏览次数统计';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CREATED_AT IS '记录创建时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.UPDATED_AT IS '记录更新时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CREATED_BY IS '创建人用户ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.UPDATED_BY IS '更新人用户ID';

-- 知识文档访问记录表
CREATE TABLE KNOWLEDGE_ACCESS_LOG (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 访问记录ID，主键
  DOCUMENT_ID NUMBER(19,0) NOT NULL,              -- 文档ID，外键
  USER_ID NUMBER(19,0),                           -- 用户ID，外键
  ACCESS_TYPE VARCHAR2(20) NOT NULL,              -- 访问类型
  IP_ADDRESS VARCHAR2(45),                        -- 访问IP地址
  USER_AGENT VARCHAR2(1000),                      -- 用户代理信息
  ACCESS_TIME DATE DEFAULT SYSDATE,               -- 访问时间
  CONSTRAINT FK_KAL_DOCUMENT FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
  CONSTRAINT FK_KAL_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID)
);

-- 添加表注释
COMMENT ON TABLE KNOWLEDGE_ACCESS_LOG IS '知识文档访问记录表，记录用户对文档的访问行为';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ID IS '访问记录ID，主键，自增';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.DOCUMENT_ID IS '被访问的文档ID，外键';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.USER_ID IS '访问用户ID，外键，匿名访问时可为NULL';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_TYPE IS '访问类型：VIEW-查看，DOWNLOAD-下载，SEARCH-搜索';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.IP_ADDRESS IS '访问者IP地址';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.USER_AGENT IS '浏览器用户代理信息';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_TIME IS '访问时间';
```

### 3.5 系统日志模块

```sql
-- 系统操作日志表
CREATE TABLE SYSTEM_OPERATION_LOG (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 日志ID，主键
  USER_ID NUMBER(19,0),                           -- 用户ID，外键
  USERNAME VARCHAR2(50),                          -- 用户名
  OPERATION_TYPE VARCHAR2(50) NOT NULL,           -- 操作类型
  OPERATION_MODULE VARCHAR2(50) NOT NULL,         -- 操作模块
  OPERATION_FUNCTION VARCHAR2(100),               -- 操作功能
  OPERATION_DESCRIPTION VARCHAR2(500),            -- 操作描述
  REQUEST_METHOD VARCHAR2(10),                    -- 请求方法
  REQUEST_URL VARCHAR2(500),                      -- 请求URL
  REQUEST_PARAMS CLOB,                            -- 请求参数
  RESPONSE_STATUS NUMBER(3,0),                    -- 响应状态码
  RESPONSE_MESSAGE VARCHAR2(1000),                -- 响应消息
  EXECUTION_TIME NUMBER(10,0),                    -- 执行耗时（毫秒）
  IP_ADDRESS VARCHAR2(45),                        -- 客户端IP地址
  USER_AGENT VARCHAR2(1000),                      -- 用户代理信息
  CREATED_AT DATE DEFAULT SYSDATE                 -- 创建时间
);

-- 添加表注释
COMMENT ON TABLE SYSTEM_OPERATION_LOG IS '系统操作日志表，记录用户在系统中的所有操作行为';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ID IS '日志记录ID，主键，自增';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_ID IS '操作用户ID，外键，系统操作时可为NULL';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USERNAME IS '操作用户名，冗余字段便于查询';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_TYPE IS '操作类型：CREATE-创建，UPDATE-更新，DELETE-删除，QUERY-查询，LOGIN-登录等';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_MODULE IS '操作模块：USER-用户管理，CASE-病例管理，RULE-规则管理等';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_FUNCTION IS '具体操作功能描述';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_DESCRIPTION IS '操作详细描述';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_METHOD IS 'HTTP请求方法：GET、POST、PUT、DELETE等';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_URL IS '请求的URL路径';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_PARAMS IS '请求参数，JSON格式存储';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_STATUS IS 'HTTP响应状态码';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_MESSAGE IS '响应消息或错误信息';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.EXECUTION_TIME IS '操作执行耗时，单位：毫秒';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.IP_ADDRESS IS '客户端IP地址';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_AGENT IS '浏览器用户代理信息';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CREATED_AT IS '日志记录创建时间';

-- 数据变更审计表
CREATE TABLE SYSTEM_CHANGE_AUDIT (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 审计ID，主键
  TABLE_NAME VARCHAR2(100) NOT NULL,              -- 表名
  RECORD_ID VARCHAR2(100) NOT NULL,               -- 记录ID
  OPERATION_TYPE VARCHAR2(20) NOT NULL,           -- 操作类型
  OLD_VALUES CLOB,                                -- 变更前数据
  NEW_VALUES CLOB,                                -- 变更后数据
  CHANGED_FIELDS VARCHAR2(1000),                  -- 变更字段列表
  USER_ID NUMBER(19,0),                           -- 操作用户ID
  USERNAME VARCHAR2(50),                          -- 操作用户名
  IP_ADDRESS VARCHAR2(45),                        -- 客户端IP地址
  CHANGED_AT DATE DEFAULT SYSDATE,                -- 变更时间
  BUSINESS_KEY VARCHAR2(200)                      -- 业务关键字
);

-- 添加表注释
COMMENT ON TABLE SYSTEM_CHANGE_AUDIT IS '数据变更审计表，记录重要数据的变更历史';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.ID IS '审计记录ID，主键，自增';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.TABLE_NAME IS '发生变更的数据表名';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.RECORD_ID IS '变更记录的主键ID';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.OPERATION_TYPE IS '操作类型：INSERT-新增，UPDATE-更新，DELETE-删除';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.OLD_VALUES IS '变更前的数据值，JSON格式';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.NEW_VALUES IS '变更后的数据值，JSON格式';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.CHANGED_FIELDS IS '发生变更的字段名列表，逗号分隔';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.USER_ID IS '执行变更操作的用户ID';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.USERNAME IS '执行变更操作的用户名';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.IP_ADDRESS IS '操作来源IP地址';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.CHANGED_AT IS '数据变更时间';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.BUSINESS_KEY IS '业务关键字，如病例编号、规则编码等';

-- 登录日志表
CREATE TABLE SYSTEM_LOGIN_LOG (
  ID NUMBER(19,0) PRIMARY KEY,                    -- 登录日志ID，主键
  USER_ID NUMBER(19,0),                           -- 用户ID，外键
  USERNAME VARCHAR2(50),                          -- 用户名
  LOGIN_TYPE VARCHAR2(20) NOT NULL,               -- 登录类型
  LOGIN_TIME DATE DEFAULT SYSDATE,                -- 登录时间
  LOGOUT_TIME DATE,                               -- 登出时间
  SESSION_DURATION NUMBER(10,0),                  -- 会话持续时间（分钟）
  IP_ADDRESS VARCHAR2(45),                        -- 登录IP地址
  USER_AGENT VARCHAR2(1000),                      -- 用户代理信息
  DEVICE_INFO VARCHAR2(500),                      -- 设备信息
  LOCATION VARCHAR2(200),                         -- 登录地理位置
  FAIL_REASON VARCHAR2(200),                      -- 登录失败原因
  CONSTRAINT FK_SLL_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID)
);

-- 添加表注释
COMMENT ON TABLE SYSTEM_LOGIN_LOG IS '系统登录日志表，记录用户登录、登出行为';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.ID IS '登录日志ID，主键，自增';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.USER_ID IS '用户ID，外键，登录失败时可为NULL';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.USERNAME IS '登录用户名';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGIN_TYPE IS '登录类型：SUCCESS-成功，FAILED-失败，LOGOUT-登出';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGIN_TIME IS '登录时间';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGOUT_TIME IS '登出时间，主动登出或会话过期时记录';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.SESSION_DURATION IS '会话持续时间，单位：分钟';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.IP_ADDRESS IS '登录来源IP地址';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.USER_AGENT IS '浏览器用户代理信息';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.DEVICE_INFO IS '设备信息：操作系统、浏览器等';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOCATION IS '登录地理位置，根据IP解析';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.FAIL_REASON IS '登录失败原因：密码错误、账户锁定等';
```

## 4. 索引设计策略

### 4.1 主要索引

```sql
-- 用户管理模块索引
CREATE INDEX IDX_USER_INFO_USERNAME ON USER_INFO(USERNAME);
CREATE INDEX IDX_USER_INFO_EMAIL ON USER_INFO(EMAIL);
CREATE INDEX IDX_USER_INFO_STATUS ON USER_INFO(STATUS);
CREATE INDEX IDX_USER_ROLE_MAPPING_USER ON USER_ROLE_MAPPING(USER_ID);
CREATE INDEX IDX_USER_ROLE_MAPPING_ROLE ON USER_ROLE_MAPPING(ROLE_ID);

-- 医疗病例模块索引
CREATE INDEX IDX_MEDICAL_CASE_PATIENT ON MEDICAL_CASE(PATIENT_ID_CARD);
CREATE INDEX IDX_MEDICAL_CASE_HOSPITAL ON MEDICAL_CASE(HOSPITAL_CODE);
CREATE INDEX IDX_MEDICAL_CASE_DATE ON MEDICAL_CASE(ADMISSION_DATE, DISCHARGE_DATE);
CREATE INDEX IDX_MEDICAL_CASE_TYPE ON MEDICAL_CASE(CASE_TYPE, MEDICAL_TYPE);
CREATE INDEX IDX_MEDICAL_CASE_STATUS ON MEDICAL_CASE(STATUS);
CREATE INDEX IDX_MEDICAL_COST_CASE ON MEDICAL_COST_DETAIL(CASE_ID);
CREATE INDEX IDX_MEDICAL_COST_DATE ON MEDICAL_COST_DETAIL(CHARGED_AT);
CREATE INDEX IDX_MEDICAL_COST_TYPE ON MEDICAL_COST_DETAIL(ITEM_TYPE);

-- 监管规则模块索引
CREATE INDEX IDX_RULE_SUPERVISION_TYPE ON RULE_SUPERVISION(RULE_TYPE);
CREATE INDEX IDX_RULE_SUPERVISION_ACTIVE ON RULE_SUPERVISION(IS_ACTIVE);
CREATE INDEX IDX_RULE_EXECUTION_RULE ON RULE_EXECUTION_LOG(RULE_ID);
CREATE INDEX IDX_RULE_EXECUTION_TIME ON RULE_EXECUTION_LOG(START_TIME);
CREATE INDEX IDX_RULE_RESULT_LOG ON RULE_EXECUTION_RESULT(EXECUTION_LOG_ID);
CREATE INDEX IDX_RULE_RESULT_CASE ON RULE_EXECUTION_RESULT(CASE_ID);

-- 知识库模块索引
CREATE INDEX IDX_KNOWLEDGE_DOC_CATEGORY ON KNOWLEDGE_DOCUMENT(CATEGORY_ID);
CREATE INDEX IDX_KNOWLEDGE_DOC_STATUS ON KNOWLEDGE_DOCUMENT(STATUS);
CREATE INDEX IDX_KNOWLEDGE_DOC_TYPE ON KNOWLEDGE_DOCUMENT(DOCUMENT_TYPE);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_DOC ON KNOWLEDGE_ACCESS_LOG(DOCUMENT_ID);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_USER ON KNOWLEDGE_ACCESS_LOG(USER_ID);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_TIME ON KNOWLEDGE_ACCESS_LOG(ACCESS_TIME);

-- 系统日志模块索引
CREATE INDEX IDX_SYSTEM_OP_LOG_USER ON SYSTEM_OPERATION_LOG(USER_ID);
CREATE INDEX IDX_SYSTEM_OP_LOG_TIME ON SYSTEM_OPERATION_LOG(CREATED_AT);
CREATE INDEX IDX_SYSTEM_OP_LOG_MODULE ON SYSTEM_OPERATION_LOG(OPERATION_MODULE);
CREATE INDEX IDX_SYSTEM_CHANGE_TABLE ON SYSTEM_CHANGE_AUDIT(TABLE_NAME);
CREATE INDEX IDX_SYSTEM_CHANGE_TIME ON SYSTEM_CHANGE_AUDIT(CHANGED_AT);
CREATE INDEX IDX_SYSTEM_LOGIN_USER ON SYSTEM_LOGIN_LOG(USER_ID);
CREATE INDEX IDX_SYSTEM_LOGIN_TIME ON SYSTEM_LOGIN_LOG(LOGIN_TIME);
```

### 4.2 分区索引策略

```sql
-- 大表分区索引示例
-- 按时间分区的操作日志表
CREATE INDEX IDX_SYSTEM_OP_LOG_TIME_PART ON SYSTEM_OPERATION_LOG(CREATED_AT) LOCAL;

-- 按哈希分区的费用明细表
CREATE INDEX IDX_MEDICAL_COST_CASE_PART ON MEDICAL_COST_DETAIL(CASE_ID) LOCAL;
```

## 5. 性能优化

### 5.1 查询优化策略

#### 5.1.1 分页查询优化
```sql
-- 使用ROWNUM优化分页查询
SELECT * FROM (
  SELECT ROWNUM rn, t.* FROM (
    SELECT ID, PATIENT_NAME, TOTAL_COST
    FROM MEDICAL_CASE
    WHERE STATUS = 'ACTIVE'
    ORDER BY ADMISSION_DATE DESC
  ) t
  WHERE ROWNUM <= 20
) WHERE rn > 10;
```

#### 5.1.2 统计查询优化
```sql
-- 使用物化视图优化复杂统计
CREATE MATERIALIZED VIEW MV_MEDICAL_CASE_STATS
BUILD IMMEDIATE
REFRESH FAST ON COMMIT
AS
SELECT
  HOSPITAL_CODE,
  CASE_TYPE,
  COUNT(*) AS CASE_COUNT,
  SUM(TOTAL_COST) AS TOTAL_AMOUNT,
  AVG(TOTAL_COST) AS AVG_AMOUNT,
  TRUNC(ADMISSION_DATE, 'MM') AS MONTH_DATE
FROM MEDICAL_CASE
WHERE STATUS = 'ACTIVE'
GROUP BY HOSPITAL_CODE, CASE_TYPE, TRUNC(ADMISSION_DATE, 'MM');
```

### 5.2 存储优化

#### 5.2.1 表分区策略
```sql
-- 按时间分区大表
CREATE TABLE SYSTEM_OPERATION_LOG_PART (
  -- 字段定义同原表
) PARTITION BY RANGE (CREATED_AT) (
  PARTITION P2024Q1 VALUES LESS THAN (DATE '2024-04-01'),
  PARTITION P2024Q2 VALUES LESS THAN (DATE '2024-07-01'),
  PARTITION P2024Q3 VALUES LESS THAN (DATE '2024-10-01'),
  PARTITION P2024Q4 VALUES LESS THAN (DATE '2025-01-01')
);

-- 按哈希分区
CREATE TABLE MEDICAL_COST_DETAIL_PART (
  -- 字段定义同原表
) PARTITION BY HASH (CASE_ID) PARTITIONS 8;
```

#### 5.2.2 压缩策略
```sql
-- 启用表压缩
ALTER TABLE SYSTEM_OPERATION_LOG COMPRESS FOR OLTP;
ALTER TABLE MEDICAL_COST_DETAIL COMPRESS FOR OLTP;
```

## 6. 数据安全

### 6.1 敏感数据保护

#### 6.1.1 数据脱敏
```sql
-- 创建脱敏视图
CREATE VIEW V_MEDICAL_CASE_MASKED AS
SELECT
  ID,
  CASE_NUMBER,
  SUBSTR(PATIENT_NAME, 1, 1) || '**' AS PATIENT_NAME, -- 姓名脱敏
  SUBSTR(PATIENT_ID_CARD, 1, 6) || '********' || SUBSTR(PATIENT_ID_CARD, 15, 4) AS PATIENT_ID_CARD, -- 身份证脱敏
  PATIENT_GENDER,
  PATIENT_AGE,
  HOSPITAL_CODE,
  HOSPITAL_NAME,
  CASE_TYPE,
  ADMISSION_DATE,
  DISCHARGE_DATE,
  TOTAL_COST,
  STATUS,
  CREATED_AT
FROM MEDICAL_CASE;
```

#### 6.1.2 访问控制
```sql
-- 创建角色和权限
CREATE ROLE MEDICAL_VIEWER;
CREATE ROLE MEDICAL_MANAGER;
CREATE ROLE SYSTEM_ADMIN;

-- 分配权限
GRANT SELECT ON MEDICAL_CASE TO MEDICAL_VIEWER;
GRANT SELECT, INSERT, UPDATE ON MEDICAL_CASE TO MEDICAL_MANAGER;
GRANT ALL ON MEDICAL_CASE TO SYSTEM_ADMIN;
```

### 6.2 审计触发器

```sql
-- 数据变更审计触发器示例
CREATE OR REPLACE TRIGGER TRG_MEDICAL_CASE_AUDIT
  AFTER INSERT OR UPDATE OR DELETE ON MEDICAL_CASE
  FOR EACH ROW
DECLARE
  v_operation VARCHAR2(10);
  v_user_id NUMBER;
BEGIN
  -- 获取当前用户ID
  SELECT USER_ID INTO v_user_id FROM USER_SESSION WHERE SESSION_ID = SYS_CONTEXT('USERENV', 'SESSIONID');

  -- 确定操作类型
  IF INSERTING THEN
    v_operation := 'INSERT';
  ELSIF UPDATING THEN
    v_operation := 'UPDATE';
  ELSIF DELETING THEN
    v_operation := 'DELETE';
  END IF;

  -- 插入审计记录
  INSERT INTO SYSTEM_CHANGE_AUDIT (
    TABLE_NAME, RECORD_ID, OPERATION_TYPE,
    OLD_VALUES, NEW_VALUES, USER_ID, USERNAME,
    IP_ADDRESS, CHANGED_AT, BUSINESS_KEY
  ) VALUES (
    'MEDICAL_CASE',
    COALESCE(:NEW.ID, :OLD.ID),
    v_operation,
    CASE WHEN v_operation != 'INSERT' THEN JSON_OBJECT(:OLD) END,
    CASE WHEN v_operation != 'DELETE' THEN JSON_OBJECT(:NEW) END,
    v_user_id,
    USER,
    SYS_CONTEXT('USERENV', 'IP_ADDRESS'),
    SYSDATE,
    COALESCE(:NEW.CASE_NUMBER, :OLD.CASE_NUMBER)
  );
END;
```

## 7. 扩展性设计

### 7.1 水平扩展

#### 7.1.1 读写分离配置
```sql
-- 主库配置（写操作）
-- 从库配置（读操作）
-- 通过应用层路由实现读写分离
```

#### 7.1.2 分库分表策略
```sql
-- 按医院编码分库
-- 按时间维度分表
-- 使用中间件实现透明分库分表
```

### 7.2 垂直扩展

#### 7.2.1 预留扩展字段
```sql
-- 为主要业务表添加扩展字段
ALTER TABLE MEDICAL_CASE ADD (
  EXT_FIELD1 VARCHAR2(100),  -- 扩展字段1
  EXT_FIELD2 VARCHAR2(100),  -- 扩展字段2
  EXT_JSON   CLOB            -- JSON扩展字段
);

-- 添加JSON格式约束
ALTER TABLE MEDICAL_CASE ADD CONSTRAINT CK_MEDICAL_CASE_EXT_JSON
CHECK (EXT_JSON IS JSON);
```

#### 7.2.2 版本管理
```sql
-- 数据库版本管理表
CREATE TABLE SYSTEM_DB_VERSION (
  ID NUMBER(19,0) PRIMARY KEY,
  VERSION_NUMBER VARCHAR2(20) NOT NULL,
  DESCRIPTION VARCHAR2(500),
  SCRIPT_NAME VARCHAR2(200),
  APPLIED_AT DATE DEFAULT SYSDATE,
  APPLIED_BY VARCHAR2(100)
);
```

---

**文档版本**: v2.0
**最后更新**: 2025-01-15
**文档状态**: 正式版
**维护团队**: 医保基金监管平台开发团队
