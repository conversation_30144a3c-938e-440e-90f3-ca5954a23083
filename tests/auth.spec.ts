import { test, expect } from '@playwright/test';

test.describe('认证系统测试', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前访问登录页面
    await page.goto('/auth/login');
  });

  test('应该显示登录页面', async ({ page }) => {
    // 检查页面标题 - 暂时使用实际的标题
    await expect(page).toHaveTitle(/DRG数据分析系统|医保基金监管平台/);

    // 检查登录表单元素 - 使用更准确的选择器
    await expect(page.locator('input[placeholder="请输入用户名"]')).toBeVisible();
    await expect(page.locator('input[placeholder="请输入密码"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    // 检查页面内容
    await expect(page.locator('text=用户登录')).toBeVisible();
    await expect(page.locator('text=输入您的用户名和密码以访问系统')).toBeVisible();
  });

  test('应该能够成功登录', async ({ page }) => {
    // 填写登录表单 - 使用正确的选择器和凭据
    await page.fill('input[placeholder="请输入用户名"]', 'admin');
    await page.fill('input[placeholder="请输入密码"]', 'Admin123!');

    // 点击登录按钮
    await page.click('button[type="submit"]');

    // 等待网络请求完成
    await page.waitForResponse(response =>
      response.url().includes('/api/auth/login') && response.status() === 200
    );

    // 等待跳转到仪表板
    await expect(page).toHaveURL(/\/dashboard/, { timeout: 10000 });

    // 检查仪表板页面元素 - 使用更精确的选择器
    await expect(page.locator('h2:has-text("欢迎回来，系统管理员！")')).toBeVisible();
  });

  test('应该显示错误的登录凭据错误', async ({ page }) => {
    // 填写错误的登录信息
    await page.fill('input[placeholder="请输入用户名"]', 'wronguser');
    await page.fill('input[placeholder="请输入密码"]', 'wrongpass');

    // 点击登录按钮
    await page.click('button[type="submit"]');

    // 等待网络请求完成 - 登录失败通常返回200但包含错误信息
    await page.waitForResponse(response =>
      response.url().includes('/api/auth/login')
    );

    // 检查错误消息 - 等待通知显示
    await expect(page.locator('text=用户名或密码错误')).toBeVisible({ timeout: 10000 });
  });

  test('应该验证必填字段', async ({ page }) => {
    // 尝试提交空表单
    await page.click('button[type="submit"]');

    // 检查验证错误
    await expect(page.locator('text=请输入用户名')).toBeVisible();
    await expect(page.locator('text=请输入密码')).toBeVisible();
  });

  test('应该能够注销', async ({ page }) => {
    // 先登录
    await page.fill('input[placeholder="请输入用户名"]', 'admin');
    await page.fill('input[placeholder="请输入密码"]', 'Admin123!');
    await page.click('button[type="submit"]');

    // 等待网络请求完成
    await page.waitForResponse(response =>
      response.url().includes('/api/auth/login') && response.status() === 200
    );

    // 等待跳转到仪表板
    await expect(page).toHaveURL(/\/dashboard/, { timeout: 10000 });

    // 点击注销按钮 - 使用图标按钮的选择器
    await page.click('button[title="退出登录"], button:has([data-testid="logout-icon"]), button:has(.lucide-log-out)');

    // 检查是否跳转回登录页面
    await expect(page).toHaveURL(/\/auth\/login/);
  });

  test('密码显示/隐藏功能应该正常工作', async ({ page }) => {
    // 定位密码输入框和眼睛按钮
    const passwordInput = page.locator('input[placeholder="请输入密码"]');
    const toggleButton = page.locator('button[aria-label*="密码"]');

    // 初始状态：密码应该是隐藏的
    await expect(passwordInput).toHaveAttribute('type', 'password');

    // 输入一些密码文本
    await passwordInput.fill('testpassword123');

    // 点击眼睛按钮显示密码
    await toggleButton.click();

    // 验证密码变为可见
    await expect(passwordInput).toHaveAttribute('type', 'text');

    // 再次点击眼睛按钮隐藏密码
    await toggleButton.click();

    // 验证密码重新隐藏
    await expect(passwordInput).toHaveAttribute('type', 'password');

    // 验证密码值没有丢失
    await expect(passwordInput).toHaveValue('testpassword123');
  });

  test('记住我功能应该正常工作', async ({ page }) => {
    // 清除localStorage
    await page.evaluate(() => {
      localStorage.removeItem('rememberMe');
      localStorage.removeItem('savedUsername');
    });

    // 刷新页面确保状态重置
    await page.reload();

    // 定位记住我复选框 - 使用更精确的选择器
    const rememberMeLabel = page.locator('text=记住我');
    const usernameInput = page.locator('input[placeholder="请输入用户名"]');

    // 初始状态：用户名应该为空
    await expect(usernameInput).toHaveValue('');

    // 填写用户名和密码
    await usernameInput.fill('testuser');
    await page.fill('input[placeholder="请输入密码"]', 'testpass');

    // 点击记住我标签来勾选复选框
    await rememberMeLabel.click();

    // 模拟表单提交（不实际提交，只是触发记住我逻辑）
    await page.evaluate(() => {
      // 模拟记住我功能的localStorage存储
      localStorage.setItem('rememberMe', 'true');
      localStorage.setItem('savedUsername', 'testuser');
    });

    // 刷新页面
    await page.reload();

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 验证记住我功能：用户名应该被恢复
    await expect(usernameInput).toHaveValue('testuser');

    // 测试清除记住我
    await page.evaluate(() => {
      localStorage.removeItem('rememberMe');
      localStorage.removeItem('savedUsername');
    });

    // 刷新页面
    await page.reload();

    // 验证记住我被清除
    await expect(usernameInput).toHaveValue('');
  });

  test('记住我功能与实际登录集成测试', async ({ page }) => {
    // 清除localStorage
    await page.evaluate(() => {
      localStorage.removeItem('rememberMe');
      localStorage.removeItem('savedUsername');
    });

    await page.reload();

    // 使用正确的凭据登录并勾选记住我
    await page.fill('input[placeholder="请输入用户名"]', 'admin');
    await page.fill('input[placeholder="请输入密码"]', 'Admin123!');

    // 点击记住我标签
    const rememberMeLabel = page.locator('text=记住我');
    await rememberMeLabel.click();

    // 提交登录表单
    await page.click('button[type="submit"]');

    // 等待登录成功
    await page.waitForResponse(response =>
      response.url().includes('/api/auth/login') && response.status() === 200
    );

    // 验证localStorage中保存了记住我数据
    const savedData = await page.evaluate(() => ({
      rememberMe: localStorage.getItem('rememberMe'),
      savedUsername: localStorage.getItem('savedUsername')
    }));

    expect(savedData.rememberMe).toBe('true');
    expect(savedData.savedUsername).toBe('admin');

    // 注销并返回登录页面
    await expect(page).toHaveURL(/\/dashboard/, { timeout: 10000 });
    await page.goto('/auth/login');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 验证用户名被恢复
    await expect(page.locator('input[placeholder="请输入用户名"]')).toHaveValue('admin');
  });
});
