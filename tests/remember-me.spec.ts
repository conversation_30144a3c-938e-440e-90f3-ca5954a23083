import { test, expect } from '@playwright/test'

test.describe('记住我功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 清除localStorage
    await page.goto('http://localhost:3000/auth/login')
    await page.evaluate(() => localStorage.clear())
    await page.reload()
  })

  test('记住我功能应该正常工作', async ({ page }) => {
    // 1. 访问登录页面
    await page.goto('http://localhost:3000/auth/login')
    
    // 2. 等待页面加载完成
    await page.waitForSelector('[data-testid="login-form"]', { timeout: 10000 })
    
    // 3. 验证初始状态
    const initialRememberMe = await page.isChecked('[data-testid="remember-me-checkbox"]')
    expect(initialRememberMe).toBe(false)
    
    // 4. 填写登录信息
    await page.fill('[data-testid="username-input"]', 'admin')
    await page.fill('[data-testid="password-input"]', 'Admin123!')
    
    // 5. 勾选记住我
    await page.check('[data-testid="remember-me-checkbox"]')
    
    // 6. 验证复选框已勾选
    const checkedRememberMe = await page.isChecked('[data-testid="remember-me-checkbox"]')
    expect(checkedRememberMe).toBe(true)
    
    // 7. 点击登录按钮
    await page.click('[data-testid="login-button"]')
    
    // 8. 等待登录成功（可能会跳转到主页面）
    await page.waitForTimeout(2000)
    
    // 9. 返回登录页面测试持久化
    await page.goto('http://localhost:3000/auth/login')

    // 10. 等待页面加载和数据恢复
    await page.waitForTimeout(1000)
    
    // 11. 验证用户名是否自动填充
    const usernameValue = await page.inputValue('[data-testid="username-input"]')
    expect(usernameValue).toBe('admin')
    
    // 12. 验证记住我复选框是否被勾选
    const persistedRememberMe = await page.isChecked('[data-testid="remember-me-checkbox"]')
    expect(persistedRememberMe).toBe(true)
    
    console.log('✅ 记住我功能测试通过')
  })

  test('取消记住我应该清除保存的数据', async ({ page }) => {
    // 1. 先设置记住我
    await page.goto('http://localhost:3000/auth/login')
    await page.waitForSelector('[data-testid="login-form"]')
    
    await page.fill('[data-testid="username-input"]', 'admin')
    await page.fill('[data-testid="password-input"]', 'Admin123!')
    await page.check('[data-testid="remember-me-checkbox"]')
    await page.click('[data-testid="login-button"]')
    
    await page.waitForTimeout(2000)
    
    // 2. 返回登录页面
    await page.goto('http://localhost:3000/auth/login')
    await page.waitForTimeout(1000)
    
    // 3. 验证数据已保存
    expect(await page.inputValue('[data-testid="username-input"]')).toBe('admin')
    expect(await page.isChecked('[data-testid="remember-me-checkbox"]')).toBe(true)
    
    // 4. 取消勾选记住我
    await page.uncheck('[data-testid="remember-me-checkbox"]')
    await page.fill('[data-testid="password-input"]', 'Admin123!')
    await page.click('[data-testid="login-button"]')
    
    await page.waitForTimeout(2000)
    
    // 5. 再次返回登录页面
    await page.goto('http://localhost:3000/auth/login')
    await page.waitForTimeout(1000)
    
    // 6. 验证数据已清除
    expect(await page.inputValue('[data-testid="username-input"]')).toBe('')
    expect(await page.isChecked('[data-testid="remember-me-checkbox"]')).toBe(false)
    
    console.log('✅ 取消记住我功能测试通过')
  })
})
