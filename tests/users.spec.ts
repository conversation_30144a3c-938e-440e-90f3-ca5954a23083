import { test, expect } from '@playwright/test';

test.describe('用户管理测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/auth/login');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // 等待跳转到仪表板
    await expect(page).toHaveURL('/dashboard');
    
    // 导航到用户管理页面
    await page.goto('/users');
  });

  test('应该显示用户列表页面', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h1')).toContainText('用户管理');
    
    // 检查用户列表表格
    await expect(page.locator('table')).toBeVisible();
    
    // 检查新增用户按钮
    await expect(page.locator('text=新增用户')).toBeVisible();
  });

  test('应该能够搜索用户', async ({ page }) => {
    // 等待用户列表加载
    await page.waitForSelector('table tbody tr');
    
    // 输入搜索关键词
    await page.fill('input[placeholder*="搜索"]', 'admin');
    
    // 等待搜索结果
    await page.waitForTimeout(1000);
    
    // 检查搜索结果
    const rows = page.locator('table tbody tr');
    await expect(rows).toHaveCount(1);
    await expect(rows.first()).toContainText('admin');
  });

  test('应该能够打开新增用户对话框', async ({ page }) => {
    // 点击新增用户按钮
    await page.click('text=新增用户');
    
    // 检查对话框是否打开
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=新增用户')).toBeVisible();
    
    // 检查表单字段
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('input[name="realName"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
  });

  test('应该能够创建新用户', async ({ page }) => {
    // 点击新增用户按钮
    await page.click('text=新增用户');
    
    // 填写用户信息
    await page.fill('input[name="username"]', 'testuser');
    await page.fill('input[name="realName"]', '测试用户');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'Test123!');
    await page.fill('input[name="department"]', '测试部门');
    await page.fill('input[name="position"]', '测试职位');
    
    // 选择角色
    await page.click('[data-testid="role-select"]');
    await page.click('text=查看员');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 检查成功消息
    await expect(page.locator('text=用户创建成功')).toBeVisible();
    
    // 检查用户是否出现在列表中
    await expect(page.locator('text=testuser')).toBeVisible();
  });

  test('应该能够编辑用户', async ({ page }) => {
    // 等待用户列表加载
    await page.waitForSelector('table tbody tr');
    
    // 点击第一个用户的编辑按钮
    await page.click('table tbody tr:first-child button[title="编辑"]');
    
    // 检查编辑对话框
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=编辑用户')).toBeVisible();
    
    // 修改用户信息
    await page.fill('input[name="realName"]', '修改后的姓名');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 检查成功消息
    await expect(page.locator('text=用户信息更新成功')).toBeVisible();
  });

  test('应该能够删除用户', async ({ page }) => {
    // 等待用户列表加载
    await page.waitForSelector('table tbody tr');
    
    // 获取初始用户数量
    const initialCount = await page.locator('table tbody tr').count();
    
    // 点击最后一个用户的删除按钮（避免删除admin用户）
    await page.click('table tbody tr:last-child button[title="删除"]');
    
    // 确认删除
    await page.click('text=确定');
    
    // 检查成功消息
    await expect(page.locator('text=用户删除成功')).toBeVisible();
    
    // 检查用户数量是否减少
    await expect(page.locator('table tbody tr')).toHaveCount(initialCount - 1);
  });

  test('应该能够分页浏览用户', async ({ page }) => {
    // 等待用户列表加载
    await page.waitForSelector('table tbody tr');
    
    // 检查分页控件
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
    
    // 如果有多页，测试分页功能
    const nextButton = page.locator('button:has-text("下一页")');
    if (await nextButton.isEnabled()) {
      await nextButton.click();
      
      // 检查URL是否包含页码参数
      await expect(page).toHaveURL(/page=2/);
    }
  });
});
