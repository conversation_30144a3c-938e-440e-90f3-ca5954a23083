import { test, expect } from '@playwright/test';

test.describe('角色权限管理测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/auth/login');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // 等待跳转到仪表板
    await expect(page).toHaveURL('/dashboard');
    
    // 导航到角色管理页面
    await page.goto('/users/roles');
  });

  test('应该显示角色管理页面', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h1')).toContainText('角色管理');
    
    // 检查角色统计卡片
    await expect(page.locator('text=总角色数')).toBeVisible();
    await expect(page.locator('text=活跃角色')).toBeVisible();
    
    // 检查角色列表表格
    await expect(page.locator('table')).toBeVisible();
    
    // 检查新增角色按钮
    await expect(page.locator('text=新增角色')).toBeVisible();
  });

  test('应该显示所有默认角色', async ({ page }) => {
    // 等待角色列表加载
    await page.waitForSelector('table tbody tr');
    
    // 检查默认角色是否存在
    await expect(page.locator('text=ADMIN')).toBeVisible();
    await expect(page.locator('text=SUPERVISOR')).toBeVisible();
    await expect(page.locator('text=OPERATOR')).toBeVisible();
    await expect(page.locator('text=AUDITOR')).toBeVisible();
    await expect(page.locator('text=VIEWER')).toBeVisible();
  });

  test('应该能够打开新增角色对话框', async ({ page }) => {
    // 点击新增角色按钮
    await page.click('text=新增角色');
    
    // 检查对话框是否打开
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=新增角色')).toBeVisible();
    
    // 检查表单字段
    await expect(page.locator('select[name="roleCode"]')).toBeVisible();
    await expect(page.locator('input[name="roleName"]')).toBeVisible();
    await expect(page.locator('textarea[name="description"]')).toBeVisible();
  });

  test('应该显示权限配置界面', async ({ page }) => {
    // 点击新增角色按钮
    await page.click('text=新增角色');
    
    // 检查权限配置部分
    await expect(page.locator('text=权限配置')).toBeVisible();
    
    // 检查权限模块
    await expect(page.locator('text=用户管理')).toBeVisible();
    await expect(page.locator('text=医疗案例')).toBeVisible();
    await expect(page.locator('text=监管规则')).toBeVisible();
    await expect(page.locator('text=知识库')).toBeVisible();
    await expect(page.locator('text=数据分析')).toBeVisible();
    await expect(page.locator('text=系统设置')).toBeVisible();
  });

  test('应该能够选择权限', async ({ page }) => {
    // 点击新增角色按钮
    await page.click('text=新增角色');
    
    // 选择用户管理模块的所有权限
    const userManagementCheckbox = page.locator('text=用户管理').locator('..').locator('input[type="checkbox"]').first();
    await userManagementCheckbox.check();
    
    // 检查模块下的权限是否被选中
    const userPermissions = page.locator('text=用户管理').locator('..').locator('..').locator('input[type="checkbox"]');
    const count = await userPermissions.count();
    
    for (let i = 1; i < count; i++) {
      await expect(userPermissions.nth(i)).toBeChecked();
    }
  });

  test('应该能够创建新角色', async ({ page }) => {
    // 点击新增角色按钮
    await page.click('text=新增角色');
    
    // 填写角色信息
    await page.selectOption('select[name="roleCode"]', 'VIEWER');
    await page.fill('input[name="roleName"]', '测试角色');
    await page.fill('textarea[name="description"]', '这是一个测试角色');
    
    // 选择一些权限
    const firstPermission = page.locator('input[type="checkbox"]').nth(1);
    await firstPermission.check();
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 检查成功消息
    await expect(page.locator('text=角色创建成功')).toBeVisible();
  });

  test('应该能够编辑角色', async ({ page }) => {
    // 等待角色列表加载
    await page.waitForSelector('table tbody tr');
    
    // 点击第一个角色的编辑按钮
    await page.click('table tbody tr:first-child button[title*="编辑"]');
    
    // 检查编辑对话框
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=编辑角色')).toBeVisible();
    
    // 修改角色描述
    await page.fill('textarea[name="description"]', '修改后的描述');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 检查成功消息
    await expect(page.locator('text=角色信息更新成功')).toBeVisible();
  });

  test('应该显示角色权限统计', async ({ page }) => {
    // 等待角色列表加载
    await page.waitForSelector('table tbody tr');
    
    // 检查权限数量列
    const permissionCells = page.locator('table tbody tr td:has(svg)');
    await expect(permissionCells.first()).toBeVisible();
    
    // 检查权限数量是否为数字
    const permissionText = await permissionCells.first().textContent();
    expect(permissionText).toMatch(/\d+/);
  });

  test('应该能够删除角色', async ({ page }) => {
    // 先创建一个测试角色
    await page.click('text=新增角色');
    await page.selectOption('select[name="roleCode"]', 'VIEWER');
    await page.fill('input[name="roleName"]', '待删除角色');
    
    // 选择一个权限
    const firstPermission = page.locator('input[type="checkbox"]').nth(1);
    await firstPermission.check();
    
    await page.click('button[type="submit"]');
    await expect(page.locator('text=角色创建成功')).toBeVisible();
    
    // 等待页面刷新
    await page.waitForTimeout(1000);
    
    // 找到刚创建的角色并删除
    const roleRow = page.locator('table tbody tr:has-text("待删除角色")');
    await roleRow.locator('button[title*="删除"]').click();
    
    // 确认删除
    await page.click('text=确定');
    
    // 检查成功消息
    await expect(page.locator('text=角色删除成功')).toBeVisible();
    
    // 检查角色是否从列表中消失
    await expect(page.locator('text=待删除角色')).not.toBeVisible();
  });
});
