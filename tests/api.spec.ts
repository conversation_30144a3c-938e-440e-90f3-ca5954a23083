import { test, expect } from '@playwright/test';

test.describe('API接口测试', () => {
  let authToken: string;

  test.beforeAll(async ({ request }) => {
    // 获取认证令牌
    const loginResponse = await request.post('/api/auth/login', {
      data: {
        username: 'admin',
        password: 'Admin123!'
      }
    });

    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    authToken = loginData.data.token;
  });

  test.describe('认证API', () => {
    test('POST /api/auth/login - 成功登录', async ({ request }) => {
      const response = await request.post('/api/auth/login', {
        data: {
          username: 'admin',
          password: 'Admin123!'
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.token).toBeDefined();
      expect(data.data.user.username).toBe('admin');
    });

    test('POST /api/auth/login - 错误凭据', async ({ request }) => {
      const response = await request.post('/api/auth/login', {
        data: {
          username: 'wronguser',
          password: 'wrongpass'
        }
      });

      expect(response.status()).toBe(401);
      const data = await response.json();
      expect(data.success).toBe(false);
    });

    test('POST /api/auth/logout - 成功注销', async ({ request }) => {
      const response = await request.post('/api/auth/logout', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
    });
  });

  test.describe('用户管理API', () => {
    test('GET /api/users - 获取用户列表', async ({ request }) => {
      const response = await request.get('/api/users', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.users).toBeDefined();
      expect(Array.isArray(data.data.users)).toBe(true);
    });

    test('GET /api/users - 分页参数', async ({ request }) => {
      const response = await request.get('/api/users?page=1&pageSize=5', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.pagination.page).toBe(1);
      expect(data.data.pagination.pageSize).toBe(5);
    });

    test('GET /api/users - 搜索参数', async ({ request }) => {
      const response = await request.get('/api/users?search=admin', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.users.length).toBeGreaterThan(0);
      expect(data.data.users[0].username).toContain('admin');
    });

    test('POST /api/users - 创建用户', async ({ request }) => {
      const response = await request.post('/api/users', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          username: `testuser_api_${Date.now()}`,
          realName: 'API测试用户',
          email: `apitest_${Date.now()}@example.com`,
          password: 'Test123!',
          department: 'API测试部门',
          position: 'API测试职位',
          roleIds: [5] // VIEWER角色
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.username).toMatch(/^testuser_api_\d+$/);
    });

    test('GET /api/users/[id] - 获取单个用户', async ({ request }) => {
      // 先创建一个用户
      const createResponse = await request.post('/api/users', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          username: `testuser_get_${Date.now()}`,
          realName: 'GET测试用户',
          email: `gettest_${Date.now()}@example.com`,
          password: 'Test123!',
          department: 'GET测试部门',
          position: 'GET测试职位',
          roleIds: [5]
        }
      });

      const createData = await createResponse.json();
      const userId = createData.data.id;

      // 获取用户信息
      const response = await request.get(`/api/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.id).toBe(userId);
      expect(data.data.username).toMatch(/^testuser_get_\d+$/);
    });

    test('PUT /api/users/[id] - 更新用户', async ({ request }) => {
      // 先创建一个用户
      const createResponse = await request.post('/api/users', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          username: `testuser_update_${Date.now()}`,
          realName: 'UPDATE测试用户',
          email: `updatetest_${Date.now()}@example.com`,
          password: 'Test123!',
          department: 'UPDATE测试部门',
          position: 'UPDATE测试职位',
          roleIds: [5]
        }
      });

      const createData = await createResponse.json();
      const userId = createData.data.id;

      // 更新用户信息
      const response = await request.put(`/api/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          realName: '更新后的姓名',
          department: '更新后的部门'
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.realName).toBe('更新后的姓名');
      expect(data.data.department).toBe('更新后的部门');
    });

    test('DELETE /api/users/[id] - 删除用户', async ({ request }) => {
      // 先创建一个用户
      const createResponse = await request.post('/api/users', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          username: `testuser_delete_${Date.now()}`,
          realName: 'DELETE测试用户',
          email: `deletetest_${Date.now()}@example.com`,
          password: 'Test123!',
          department: 'DELETE测试部门',
          position: 'DELETE测试职位',
          roleIds: [5]
        }
      });

      const createData = await createResponse.json();
      const userId = createData.data.id;

      // 删除用户
      const response = await request.delete(`/api/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);

      // 验证用户已被删除
      const getResponse = await request.get(`/api/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(getResponse.status()).toBe(404);
    });
  });

  test.describe('角色权限API', () => {
    test('GET /api/roles - 获取角色列表', async ({ request }) => {
      const response = await request.get('/api/roles', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
      expect(data.data.length).toBeGreaterThan(0);
    });

    test('GET /api/permissions - 获取权限列表', async ({ request }) => {
      const response = await request.get('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.permissions).toBeDefined();
      expect(data.data.groups).toBeDefined();
      expect(Array.isArray(data.data.permissions)).toBe(true);
      expect(Array.isArray(data.data.groups)).toBe(true);
    });

    test('POST /api/roles - 创建角色', async ({ request }) => {
      const response = await request.post('/api/roles', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        data: {
          roleCode: `TEST_ROLE_${Date.now()}`,
          roleName: 'API测试角色',
          description: '通过API创建的测试角色',
          permissionIds: [20] // 使用单个权限避免冲突
        }
      });

      expect(response.ok()).toBeTruthy();
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.roleName).toBe('API测试角色');
    });
  });

  test.describe('权限验证', () => {
    test('未认证请求应该返回401', async ({ request }) => {
      const response = await request.get('/api/users');
      expect(response.status()).toBe(401);
    });

    test('无效令牌应该返回401', async ({ request }) => {
      const response = await request.get('/api/users', {
        headers: {
          'Authorization': 'Bearer invalid_token'
        }
      });
      expect(response.status()).toBe(401);
    });
  });
});
