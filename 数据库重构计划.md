# 医保基金监管平台数据库重构计划

## 1. 项目概述

### 1.1 重构目标
基于个人项目的实际需求，对医保基金监管平台进行数据库架构重构，在保证功能完整性的前提下，简化部署复杂度，提升系统的安全性、性能和可维护性。

### 1.2 核心模块分析
- **医疗记录管理模块**：核心业务数据，包含患者信息、诊疗记录、费用明细等敏感医疗数据
- **监管规则管理模块**：业务规则引擎，包含规则定义、执行记录、结果分析等
- **知识库管理模块**：知识管理系统，包含文档、分类、访问控制等
- **用户角色模块**：身份认证授权，包含用户、角色、权限管理
- **系统管理模块**：系统配置和日志，包含配置管理、操作审计、性能监控

### 1.3 个人项目架构要求
- **数据安全性**：基础数据加密、访问控制、操作审计
- **稳定性**：单机高可用、定期备份、故障恢复
- **可维护性**：简化架构、标准化设计、易于部署
- **合规性**：基础数据保护、隐私安全
- **性能优化**：本地缓存、索引优化、查询优化

## 2. 重构策略

### 2.1 阶段一：基础架构重构（1-2周）

#### 2.1.1 统一数据模型标准
```sql
-- 标准表设计模板
CREATE TABLE TEMPLATE_TABLE (
    ID              NUMBER(19,0) NOT NULL,           -- 主键
    TENANT_ID       NUMBER(19,0),                     -- 租户ID（多租户支持）
    CREATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP, -- 创建时间
    UPDATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP, -- 更新时间
    CREATED_BY      NUMBER(19,0),                     -- 创建人
    UPDATED_BY      NUMBER(19,0),                     -- 更新人
    VERSION         NUMBER(10,0) DEFAULT 1,           -- 版本号（乐观锁）
    IS_DELETED      NUMBER(1,0) DEFAULT 0,            -- 逻辑删除
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL', -- 数据分类
    -- 业务字段...
    CONSTRAINT PK_TEMPLATE_TABLE PRIMARY KEY (ID)
);
```

#### 2.1.2 安全架构增强
- **多租户架构**：所有表添加TENANT_ID字段，实现数据隔离
- **数据分类**：PUBLIC/INTERNAL/CONFIDENTIAL/RESTRICTED四级分类
- **字段级加密**：敏感字段使用Oracle TDE加密
- **RBAC权限模型**：基于角色的细粒度访问控制

### 2.2 阶段二：核心模块重构（2-3周）

#### 2.2.1 医疗记录管理模块重构

**患者信息表优化**：
```sql
CREATE TABLE PATIENT_INFO (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    PATIENT_NO          VARCHAR2(50) NOT NULL,        -- 患者编号
    NAME_ENCRYPTED      RAW(256),                     -- 加密姓名
    ID_CARD_ENCRYPTED   RAW(256),                     -- 加密身份证
    PHONE_ENCRYPTED     RAW(256),                     -- 加密电话
    GENDER              NUMBER(1,0),                  -- 性别
    BIRTH_DATE          DATE,                         -- 出生日期
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL',
    -- 标准审计字段
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1,
    IS_DELETED          NUMBER(1,0) DEFAULT 0,
    CONSTRAINT PK_PATIENT_INFO PRIMARY KEY (ID)
)
PARTITION BY RANGE (CREATED_AT) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'));
```

**诊疗记录表优化**：
```sql
CREATE TABLE MEDICAL_RECORD (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    PATIENT_ID          NUMBER(19,0) NOT NULL,
    VISIT_NO            VARCHAR2(50) NOT NULL,
    DIAGNOSIS_CODE      VARCHAR2(20),                 -- ICD-10编码
    TREATMENT_CODE      VARCHAR2(20),                 -- 治疗编码
    RECORD_CONTENT      CLOB,                         -- 诊疗内容
    RECORD_STATUS       VARCHAR2(20) DEFAULT 'DRAFT', -- 记录状态
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL',
    -- 标准审计字段
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1,
    IS_DELETED          NUMBER(1,0) DEFAULT 0,
    CONSTRAINT PK_MEDICAL_RECORD PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_RECORD_PATIENT FOREIGN KEY (PATIENT_ID) REFERENCES PATIENT_INFO(ID)
)
PARTITION BY RANGE (CREATED_AT) INTERVAL (NUMTODSINTERVAL(1, 'DAY'));
```

#### 2.2.2 监管规则管理模块重构

**规则定义表优化**：
```sql
CREATE TABLE SUPERVISION_RULE (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    RULE_CODE           VARCHAR2(50) NOT NULL,
    RULE_NAME           VARCHAR2(200) NOT NULL,
    RULE_TYPE           VARCHAR2(50) NOT NULL,
    RULE_CATEGORY       VARCHAR2(50),
    RULE_CONTENT        CLOB,                         -- 规则内容（JSON格式）
    RULE_EXPRESSION     CLOB,                         -- 规则表达式
    SEVERITY_LEVEL      VARCHAR2(20) DEFAULT 'MEDIUM',
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1,
    EFFECTIVE_DATE      TIMESTAMP(6),
    EXPIRY_DATE         TIMESTAMP(6),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL',
    -- 标准审计字段
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1,
    IS_DELETED          NUMBER(1,0) DEFAULT 0,
    CONSTRAINT PK_SUPERVISION_RULE PRIMARY KEY (ID)
);
```

#### 2.2.3 知识库管理模块重构

**文档表优化**：
```sql
CREATE TABLE KNOWLEDGE_DOCUMENT (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    CATEGORY_ID         NUMBER(19,0),
    TITLE               VARCHAR2(500) NOT NULL,
    CONTENT             CLOB,
    CONTENT_TYPE        VARCHAR2(50),
    FILE_PATH           VARCHAR2(1000),
    FILE_SIZE           NUMBER(19,0),
    TAGS                VARCHAR2(1000),               -- 标签（JSON数组）
    VIEW_COUNT          NUMBER(10,0) DEFAULT 0,
    DOWNLOAD_COUNT      NUMBER(10,0) DEFAULT 0,
    IS_PUBLIC           NUMBER(1,0) DEFAULT 0,
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL',
    -- 标准审计字段
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1,
    IS_DELETED          NUMBER(1,0) DEFAULT 0,
    CONSTRAINT PK_KNOWLEDGE_DOCUMENT PRIMARY KEY (ID)
)
PARTITION BY RANGE (CREATED_AT) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'));

-- 全文索引
CREATE INDEX IDX_KNOWLEDGE_DOC_FULLTEXT ON KNOWLEDGE_DOCUMENT(CONTENT) 
INDEXTYPE IS CTXSYS.CONTEXT;
```

### 2.3 阶段三：性能与扩展性优化（1-2周）

#### 2.3.1 分库分表策略
- **按租户分库**：多租户数据物理隔离
- **按时间分表**：历史数据自动归档
- **按业务分表**：热点数据分离

#### 2.3.2 缓存架构设计
```yaml
# Redis单机配置
redis:
  host: localhost
  port: 6379
  database: 0
  cache:
    session: 30m      # 会话缓存
    config: 1h        # 配置缓存
    permission: 15m   # 权限缓存
    hotdata: 5m       # 热点数据缓存
```

### 2.4 阶段四：企业级特性实现（2-3周）

#### 2.4.1 单机架构设计
- **数据库统一管理**：
  - 单一数据库实例
  - 按模块分Schema
  - 简化权限管理
  - 统一备份策略

#### 2.4.2 容器化支持（可选）
```yaml
# Docker单容器部署
version: '3.8'
services:
  mediinspect-db:
    image: postgres:14
    environment:
      POSTGRES_DB: mediinspect
      POSTGRES_USER: mediuser
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - ./data:/var/lib/postgresql/data
      - ./backup:/backup
    ports:
      - "5432:5432"
```

#### 2.4.3 合规性增强
- **医疗数据标准**：支持HL7 FHIR、ICD-10标准
- **隐私保护**：GDPR、个人信息保护法合规
- **审计要求**：完整的操作日志和数据变更追踪

### 2.5 阶段五：运维与监控（1周）

#### 2.5.1 监控体系
```sql
-- 性能监控视图
CREATE OR REPLACE VIEW V_DB_PERFORMANCE AS
SELECT 
    'CPU_USAGE' as METRIC_NAME,
    ROUND(VALUE, 2) as METRIC_VALUE,
    SYSTIMESTAMP as COLLECT_TIME
FROM V$OSSTAT 
WHERE STAT_NAME = 'IDLE_TIME'
UNION ALL
SELECT 
    'MEMORY_USAGE' as METRIC_NAME,
    ROUND(VALUE/1024/1024, 2) as METRIC_VALUE,
    SYSTIMESTAMP as COLLECT_TIME
FROM V$SGASTAT 
WHERE NAME = 'free memory';
```

#### 2.5.2 备份恢复策略
```bash
#!/bin/bash
# 自动备份脚本
RMAN_SCRIPT="
BACKUP DATABASE PLUS ARCHIVELOG;
DELETE OBSOLETE;
EXIT;
"

echo "$RMAN_SCRIPT" | rman target / catalog rman/rman@rmancat
```

## 3. 技术选型

### 3.1 数据库架构（简化版）
- **主库**：Oracle 19c 或 PostgreSQL 14+（单机部署）
- **缓存**：Redis 单机版（本地缓存）
- **搜索**：数据库内置全文检索
- **备份**：本地备份 + 云存储（可选）

### 3.2 中间件选型（轻量化）
- **连接池**：HikariCP（高性能连接池）
- **配置管理**：配置文件管理
- **监控**：简单健康检查
- **日志**：文件日志系统

## 4. 实施计划

### 4.1 重构优先级（个人项目版）
1. **高优先级**：用户角色模块、医疗记录管理模块（核心功能）
2. **中优先级**：监管规则模块、基础安全配置（重要功能）
3. **低优先级**：知识库模块、高级监控（可选功能）

### 4.2 时间安排（简化版）
| 阶段 | 内容 | 时间 | 说明 |
|------|------|------|--------|
| 阶段一 | 基础架构重构 | 3-5天 | 个人开发，分步实施 |
| 阶段二 | 核心模块重构 | 1-2周 | 重点关注核心业务 |
| 阶段三 | 性能优化 | 2-3天 | 基础优化即可 |
| 阶段四 | 安全配置 | 2-3天 | 必要的安全措施 |
| 阶段五 | 测试验证 | 1-2天 | 功能验证为主 |

## 5. 风险控制（个人项目版）

### 5.1 数据安全风险
- **完整备份**：重构前完整数据备份（本地+云端）
- **分步实施**：小步快跑，每个模块独立验证
- **回滚准备**：保留原始数据和配置副本

### 5.2 开发风险
- **时间控制**：合理安排开发时间，避免过度设计
- **功能优先级**：优先实现核心功能，可选功能后续迭代
- **简化设计**：保持架构简单，便于个人维护

### 5.3 技术风险
- **技术选型**：选择熟悉的技术栈，降低学习成本
- **性能验证**：关键功能性能对比测试
- **文档记录**：充分的实施文档，便于后续维护

## 6. 交付成果（简化版）

### 6.1 设计文档
- [ ] 数据库重构设计方案（简化版）
- [ ] 表结构设计规范
- [ ] 数据迁移方案
- [ ] 基础性能优化方案

### 6.2 实施脚本
- [ ] DDL脚本（建表、索引）
- [ ] 数据迁移脚本
- [ ] 基础配置脚本
- [ ] 简单备份脚本

### 6.3 测试验证
- [ ] 基础功能测试
- [ ] 关键性能验证
- [ ] 数据完整性检查

## 7. 验收标准（个人项目版）

### 7.1 功能性验收
- 核心业务功能正常运行
- 数据完整性保证
- 用户体验保持稳定

### 7.2 性能验收
- 常用查询响应时间提升20-30%
- 系统整体运行流畅
- 数据库连接稳定

### 7.3 安全性验收
- 敏感数据基础加密
- 用户权限控制有效
- 基础审计日志记录

### 7.4 可维护性验收
- 代码结构清晰规范
- 文档完整易懂
- 部署和维护简单便捷

## 8. 总结

本重构计划基于企业级成熟方案，全面提升医保基金监管平台的数据库架构。通过统一的设计规范、增强的安全机制、优化的性能策略和完善的运维体系，重构后的系统将具备更好的安全性、性能、扩展性和可维护性，满足医疗行业的企业级应用需求。

重构过程中将严格控制风险，确保业务连续性，并通过分阶段实施、灰度发布等方式降低实施风险。最终交付的系统将为医保基金监管提供稳定、高效、安全的数据支撑平台。