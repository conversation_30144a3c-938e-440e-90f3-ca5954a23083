# 医保基金监管平台v2.0 环境变量配置

# 应用配置
NODE_ENV=development
NEXT_PUBLIC_APP_NAME="医保基金监管平台"
NEXT_PUBLIC_APP_VERSION="2.0.0"

# 数据库配置 (Oracle)
DB_HOST=37.tcp.cpolar.top
DB_PORT=14488
DB_SERVICE_NAME=ORCL
DB_USERNAME=Mediinspect
DB_PASSWORD=Alt654301
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_INCREMENT=1

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-mediinspect-v2-2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12

# 注册配置
ALLOW_REGISTRATION=true
REQUIRE_INVITE_CODE=false
INVITE_CODE=MEDIINSPECT2024
DEFAULT_USER_ROLE=USER

# API配置
API_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 开发模式配置
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_SHOW_DEBUG=true

# 限流配置
RATE_LIMIT_ENABLED=true
# 开发环境使用更宽松的限制
RATE_LIMIT_DEV_MULTIPLIER=10
# API缓存时间（秒）
API_CACHE_TTL=300
# 防抖延迟（毫秒）
DEBOUNCE_DELAY=500
