// Service Worker for Medical Insurance Platform
// 版本号 - 更新时需要修改
const CACHE_VERSION = 'mediinspect-v2.0.0'
const STATIC_CACHE = `${CACHE_VERSION}-static`
const DYNAMIC_CACHE = `${CACHE_VERSION}-dynamic`
const API_CACHE = `${CACHE_VERSION}-api`

// 需要预缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/auth/login',
  '/manifest.json',
  '/_next/static/css/',
  '/_next/static/js/',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// API缓存策略配置
const API_CACHE_CONFIG = {
  // 长期缓存的API（很少变化的数据）
  longTerm: {
    patterns: [
      /\/api\/settings\//,
      /\/api\/permissions/,
      /\/api\/roles/
    ],
    maxAge: 60 * 60 * 1000, // 1小时
    maxEntries: 50
  },
  
  // 中期缓存的API（定期更新的数据）
  mediumTerm: {
    patterns: [
      /\/api\/medical-cases\/stats/,
      /\/api\/analytics\//,
      /\/api\/knowledge-base\//
    ],
    maxAge: 5 * 60 * 1000, // 5分钟
    maxEntries: 100
  },
  
  // 短期缓存的API（频繁变化的数据）
  shortTerm: {
    patterns: [
      /\/api\/medical-cases(?!\/(stats|[0-9]+))/,
      /\/api\/supervision-rules\/executions/,
      /\/api\/monitoring\//
    ],
    maxAge: 1 * 60 * 1000, // 1分钟
    maxEntries: 200
  }
}

// 安装事件 - 预缓存静态资源
self.addEventListener('install', event => {
  console.log('🔧 Service Worker 安装中...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('📦 预缓存静态资源')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('✅ Service Worker 安装完成')
        return self.skipWaiting()
      })
      .catch(error => {
        console.error('❌ Service Worker 安装失败:', error)
      })
  )
})

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('🚀 Service Worker 激活中...')
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName.indexOf(CACHE_VERSION) === -1) {
              console.log('🗑️ 删除旧缓存:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('✅ Service Worker 激活完成')
        return self.clients.claim()
      })
  )
})

// 获取事件 - 缓存策略
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return
  }
  
  // API请求处理
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request))
    return
  }
  
  // 静态资源处理
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request))
    return
  }
  
  // 页面请求处理
  if (request.mode === 'navigate') {
    event.respondWith(handlePageRequest(request))
    return
  }
  
  // 其他请求直接通过网络
  event.respondWith(fetch(request))
})

// 处理API请求
async function handleApiRequest(request) {
  const url = new URL(request.url)
  const cacheStrategy = getApiCacheStrategy(url.pathname)
  
  if (!cacheStrategy || request.method !== 'GET') {
    // 不缓存或非GET请求，直接从网络获取
    return fetch(request)
  }
  
  try {
    const cache = await caches.open(API_CACHE)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      const cacheTime = new Date(cachedResponse.headers.get('sw-cache-time'))
      const now = new Date()
      
      // 检查缓存是否过期
      if (now - cacheTime < cacheStrategy.maxAge) {
        console.log('🎯 API缓存命中:', url.pathname)
        return cachedResponse
      }
    }
    
    // 从网络获取新数据
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // 克隆响应用于缓存
      const responseToCache = networkResponse.clone()
      
      // 添加缓存时间戳
      const headers = new Headers(responseToCache.headers)
      headers.set('sw-cache-time', new Date().toISOString())
      
      const cachedResponse = new Response(await responseToCache.blob(), {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      })
      
      // 存入缓存
      await cache.put(request, cachedResponse)
      console.log('💾 API缓存存储:', url.pathname)
      
      // 清理过期缓存
      await cleanupApiCache(cache, cacheStrategy)
    }
    
    return networkResponse
    
  } catch (error) {
    console.error('❌ API请求失败:', error)
    
    // 网络失败时尝试返回缓存
    const cache = await caches.open(API_CACHE)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      console.log('🔄 网络失败，返回缓存:', url.pathname)
      return cachedResponse
    }
    
    // 返回离线页面或错误响应
    return new Response(JSON.stringify({
      success: false,
      message: '网络连接失败，请检查网络设置'
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// 处理静态资源
async function handleStaticAsset(request) {
  try {
    const cache = await caches.open(STATIC_CACHE)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
    
  } catch (error) {
    console.error('❌ 静态资源请求失败:', error)
    return fetch(request)
  }
}

// 处理页面请求
async function handlePageRequest(request) {
  try {
    const cache = await caches.open(DYNAMIC_CACHE)
    
    // 网络优先策略
    try {
      const networkResponse = await fetch(request)
      
      if (networkResponse.ok) {
        await cache.put(request, networkResponse.clone())
      }
      
      return networkResponse
      
    } catch (networkError) {
      // 网络失败时返回缓存
      const cachedResponse = await cache.match(request)
      
      if (cachedResponse) {
        return cachedResponse
      }
      
      // 返回离线页面
      return cache.match('/') || fetch(request)
    }
    
  } catch (error) {
    console.error('❌ 页面请求失败:', error)
    return fetch(request)
  }
}

// 获取API缓存策略
function getApiCacheStrategy(pathname) {
  for (const [key, config] of Object.entries(API_CACHE_CONFIG)) {
    if (config.patterns.some(pattern => pattern.test(pathname))) {
      return config
    }
  }
  return null
}

// 判断是否为静态资源
function isStaticAsset(request) {
  const url = new URL(request.url)
  return (
    url.pathname.startsWith('/_next/static/') ||
    url.pathname.startsWith('/static/') ||
    url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/)
  )
}

// 清理API缓存
async function cleanupApiCache(cache, strategy) {
  const requests = await cache.keys()
  const apiRequests = requests.filter(req => 
    strategy.patterns.some(pattern => pattern.test(new URL(req.url).pathname))
  )
  
  if (apiRequests.length > strategy.maxEntries) {
    // 删除最旧的缓存项
    const sortedRequests = apiRequests.sort((a, b) => {
      const aTime = new Date(a.headers.get('sw-cache-time') || 0)
      const bTime = new Date(b.headers.get('sw-cache-time') || 0)
      return aTime - bTime
    })
    
    const toDelete = sortedRequests.slice(0, apiRequests.length - strategy.maxEntries)
    await Promise.all(toDelete.map(req => cache.delete(req)))
  }
}

// 消息处理
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
    }).then(() => {
      event.ports[0].postMessage({ success: true })
    })
  }
  
  if (event.data && event.data.type === 'GET_CACHE_STATS') {
    getCacheStats().then(stats => {
      event.ports[0].postMessage(stats)
    })
  }
})

// 获取缓存统计信息
async function getCacheStats() {
  const cacheNames = await caches.keys()
  const stats = {}
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName)
    const requests = await cache.keys()
    stats[cacheName] = requests.length
  }
  
  return stats
}
