# 医保基金监管平台数据库重构部署指南

## 1. 部署概述

### 1.1 部署目标
- 完成医保基金监管平台数据库架构重构
- 实现企业级安全、性能和扩展性要求
- 确保数据完整性和业务连续性
- 建立完善的监控和运维体系

### 1.2 部署环境要求

#### 硬件要求
- **个人开发环境（推荐配置）**：
  - CPU: 4核心以上，主频2.0GHz+
  - 内存: 16GB以上
  - 存储: SSD 256GB以上
  - 网络: 普通宽带即可

- **最低配置**：
  - CPU: 2核心，主频1.8GHz+
  - 内存: 8GB以上
  - 存储: 128GB可用空间
  - 网络: 稳定网络连接

#### 软件要求
- **数据库**: Oracle Database 19c Standard Edition 或 Express Edition
- **操作系统**: macOS 10.15+ / Windows 10+ / Ubuntu 20.04+
- **Java**: OpenJDK 11 或 Oracle JDK 11
- **可选中间件**: Redis 6.x（单机版）, Node.js 16+（如需前端开发）

### 1.3 部署架构

#### 单机部署架构
```
┌─────────────────────────────────────────┐
│           个人开发环境                    │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   应用服务   │───▶│   数据库     │    │
│  │             │    │  (Oracle/    │    │
│  │             │    │ PostgreSQL)  │    │
│  └─────────────┘    └─────────────┘    │
│         │                   │          │
│         ▼                   ▼          │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   缓存       │    │   文件存储   │    │
│  │  (Redis)    │    │  (本地磁盘)  │    │
│  └─────────────┘    └─────────────┘    │
└─────────────────────────────────────────┘
```

#### 简化监控
```
┌─────────────┐    ┌─────────────┐
│   应用日志   │───▶│   文件日志   │
└─────────────┘    └─────────────┘
       │                   │
       ▼                   ▼
┌─────────────┐    ┌─────────────┐
│  数据库日志  │    │  健康检查    │
└─────────────┘    └─────────────┘
```

## 2. 部署前准备

### 2.1 环境检查清单

#### 简单检查清单
- [ ] 确认操作系统版本兼容
- [ ] 检查可用磁盘空间（至少10GB）
- [ ] 确认内存足够（推荐16GB+）
- [ ] 检查网络连接正常
- [ ] 确认数据库软件已安装

#### 快速检查命令
```bash
# macOS/Linux 检查
echo "系统信息: $(uname -a)"
echo "可用空间: $(df -h . | tail -1)"
echo "内存信息: $(free -h 2>/dev/null || vm_stat | head -5)"
echo "数据库版本: $(sqlplus -v 2>/dev/null || psql --version 2>/dev/null)"
```

### 2.2 备份策略

#### 简化数据备份
```sql
-- Oracle 备份
CREATE DIRECTORY backup_dir AS '/Users/<USER>/Documents/backup';
EXPORT FULL=Y DIRECTORY=backup_dir DUMPFILE=mediinspect_backup_%U.dmp LOGFILE=export.log;

-- PostgreSQL 备份（如果使用）
-- pg_dump mediinspect > /path/to/backup/mediinspect_backup.sql
```

#### 配置备份
```bash
#!/bin/bash
# 简单备份脚本

BACKUP_DIR="$HOME/Documents/mediinspect_backup/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# 备份项目文件
cp -r ./mediinspect-v2 "$BACKUP_DIR/"

# 备份数据库配置
cp ~/.oracle/* "$BACKUP_DIR/" 2>/dev/null || true

echo "备份完成: $BACKUP_DIR"
```

### 2.3 权限准备

#### 数据库用户权限（简化版）
```sql
-- Oracle 用户创建
CREATE USER mediinspect IDENTIFIED BY "your_password";
GRANT CONNECT, RESOURCE TO mediinspect;
GRANT CREATE SESSION, CREATE TABLE, CREATE VIEW TO mediinspect;
GRANT CREATE PROCEDURE, CREATE SEQUENCE, CREATE TRIGGER TO mediinspect;
ALTER USER mediinspect QUOTA UNLIMITED ON users;

-- PostgreSQL 用户创建（如果使用）
-- CREATE USER mediinspect WITH PASSWORD 'your_password';
-- CREATE DATABASE mediinspect OWNER mediinspect;
-- GRANT ALL PRIVILEGES ON DATABASE mediinspect TO mediinspect;
```

#### 文件权限（个人环境）
```bash
#!/bin/bash
# 个人环境权限设置

# 创建项目目录
mkdir -p ~/Documents/mediinspect-v2/{data,logs,backup}

# 设置执行权限
chmod +x ~/Documents/mediinspect-v2/schema/*.sql

# 创建日志目录
mkdir -p ~/Documents/mediinspect-v2/logs

echo "权限设置完成"
```

## 3. 部署步骤

### 3.1 阶段一：基础环境部署（预计2小时）

#### 步骤1：创建表空间
```sql
-- 执行表空间创建脚本
@/path/to/schema/重构实施脚本.sql
```

#### 步骤2：验证表空间
```sql
-- 检查表空间状态
SELECT tablespace_name, status, contents FROM dba_tablespaces 
WHERE tablespace_name LIKE 'MEDIINSPECT%';

-- 检查数据文件
SELECT file_name, tablespace_name, bytes/1024/1024 as size_mb 
FROM dba_data_files 
WHERE tablespace_name LIKE 'MEDIINSPECT%';
```

### 3.2 阶段二：数据库对象部署（预计4小时）

#### 步骤1：创建序列和表
```bash
# 执行DDL脚本
sqlplus deploy_user/password @/path/to/schema/重构实施脚本.sql

# 检查执行结果
tail -f /path/to/logs/ddl_execution.log
```

#### 步骤2：创建索引和约束
```sql
-- 验证表创建
SELECT table_name, status FROM user_tables ORDER BY table_name;

-- 验证索引创建
SELECT index_name, table_name, status FROM user_indexes ORDER BY table_name;

-- 验证约束
SELECT constraint_name, table_name, constraint_type, status 
FROM user_constraints ORDER BY table_name;
```

#### 步骤3：创建触发器和视图
```sql
-- 验证触发器
SELECT trigger_name, table_name, status FROM user_triggers;

-- 验证视图
SELECT view_name, status FROM user_views;
```

### 3.3 阶段三：数据迁移（预计6-8小时）

#### 步骤1：数据迁移准备
```sql
-- 禁用外键约束（迁移期间）
BEGIN
    FOR c IN (SELECT constraint_name, table_name FROM user_constraints WHERE constraint_type = 'R') LOOP
        EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' DISABLE CONSTRAINT ' || c.constraint_name;
    END LOOP;
END;
/
```

#### 步骤2：执行数据迁移
```bash
# 执行数据迁移脚本
sqlplus deploy_user/password @/path/to/schema/数据迁移脚本.sql

# 监控迁移进度
tail -f /path/to/logs/migration.log
```

#### 步骤3：启用约束和验证
```sql
-- 启用外键约束
BEGIN
    FOR c IN (SELECT constraint_name, table_name FROM user_constraints WHERE constraint_type = 'R') LOOP
        EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' ENABLE CONSTRAINT ' || c.constraint_name;
    END LOOP;
END;
/

-- 验证数据完整性
@/path/to/schema/测试验证脚本.sql
```

### 3.4 阶段四：性能优化（预计2小时）

#### 步骤1：执行性能优化
```bash
# 执行性能优化脚本
sqlplus deploy_user/password @/path/to/schema/性能优化脚本.sql
```

#### 步骤2：收集统计信息
```sql
-- 收集表统计信息
BEGIN
    DBMS_STATS.GATHER_SCHEMA_STATS(
        ownname => 'DEPLOY_USER',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        method_opt => 'FOR ALL COLUMNS SIZE AUTO',
        degree => 4
    );
END;
/
```

### 3.5 阶段五：安全配置（预计2小时）

#### 步骤1：部署安全配置
```bash
# 执行安全配置脚本
sqlplus deploy_user/password @/path/to/schema/安全配置脚本.sql
```

#### 步骤2：配置访问控制
```sql
-- 创建应用用户
CREATE USER app_user IDENTIFIED BY "AppPass123!";

-- 授予应用权限
GRANT CONNECT TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON deploy_user.* TO app_user;

-- 创建只读用户
CREATE USER readonly_user IDENTIFIED BY "ReadPass123!";
GRANT CONNECT TO readonly_user;
GRANT SELECT ON deploy_user.V_* TO readonly_user;
```

## 4. 部署验证

### 4.1 功能验证

#### 基础功能测试
```sql
-- 执行完整验证
@/path/to/schema/测试验证脚本.sql

-- 检查测试结果
SELECT * FROM test_results WHERE test_status IN ('FAIL', 'ERROR');
```

#### 性能验证
```sql
-- 查询性能测试
SET TIMING ON;
SELECT COUNT(*) FROM PATIENT_INFO WHERE TENANT_ID = 1;
SELECT COUNT(*) FROM MEDICAL_RECORD WHERE VISIT_DATE >= SYSDATE - 30;

-- 插入性能测试
INSERT INTO PATIENT_INFO (TENANT_ID, PATIENT_CODE, PATIENT_NAME_ENCRYPTED, GENDER, BIRTH_DATE, PATIENT_STATUS, CREATED_BY, UPDATED_BY)
SELECT 1, 'TEST_' || LEVEL, 'TEST_PATIENT_' || LEVEL, 'M', SYSDATE - 365*30, 'ACTIVE', 1, 1
FROM DUAL CONNECT BY LEVEL <= 1000;
COMMIT;
```

### 4.2 安全验证

#### 加密功能测试
```sql
-- 测试数据加密
SELECT PKG_ENCRYPTION_MANAGER.ENCRYPT_SENSITIVE_DATA('测试数据', 'GENERAL') FROM DUAL;

-- 测试数据脱敏
SELECT PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA('13812345678', 'PHONE') FROM DUAL;

-- 测试权限控制
SELECT PKG_ACCESS_CONTROL.CHECK_USER_PERMISSION(1, 'USER_MANAGEMENT', 'read', 1) FROM DUAL;
```

#### 审计功能测试
```sql
-- 检查审计日志
SELECT * FROM AUDIT_LOG WHERE CREATED_TIME >= SYSDATE - 1;

-- 检查操作日志
SELECT * FROM SYSTEM_OPERATION_LOG WHERE CREATED_TIME >= SYSDATE - 1;
```

## 5. 部署后配置

### 5.1 监控配置

#### 数据库监控
```sql
-- 创建监控用户
CREATE USER monitor_user IDENTIFIED BY "MonitorPass123!";
GRANT CONNECT TO monitor_user;
GRANT SELECT ON V_$SESSION TO monitor_user;
GRANT SELECT ON V_$PROCESS TO monitor_user;
GRANT SELECT ON V_$SQL TO monitor_user;
GRANT SELECT ON DBA_TABLESPACES TO monitor_user;
GRANT SELECT ON DBA_DATA_FILES TO monitor_user;

-- 创建监控视图
CREATE OR REPLACE VIEW V_DB_PERFORMANCE AS
SELECT 
    'DATABASE' as metric_type,
    'SESSIONS' as metric_name,
    COUNT(*) as metric_value,
    SYSDATE as collect_time
FROM V$SESSION
WHERE TYPE = 'USER'
UNION ALL
SELECT 
    'DATABASE' as metric_type,
    'ACTIVE_SESSIONS' as metric_name,
    COUNT(*) as metric_value,
    SYSDATE as collect_time
FROM V$SESSION
WHERE STATUS = 'ACTIVE' AND TYPE = 'USER';
```

#### 应用监控配置
```bash
# 配置应用监控
cat > /app/config/monitor.properties << EOF
# 数据库连接监控
db.monitor.enabled=true
db.monitor.interval=30
db.monitor.threshold.connection=80
db.monitor.threshold.response=1000

# 缓存监控
cache.monitor.enabled=true
cache.monitor.interval=60
cache.monitor.threshold.hit_rate=0.8

# 业务监控
business.monitor.enabled=true
business.monitor.interval=300
EOF
```

### 5.2 备份配置

#### 自动备份脚本
```bash
#!/bin/bash
# /app/scripts/auto_backup.sh

BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 数据库备份
expdp system/password SCHEMAS=mediinspect DIRECTORY=backup_dir \
    DUMPFILE=mediinspect_$(date +%Y%m%d_%H%M%S).dmp \
    LOGFILE=backup_$(date +%Y%m%d_%H%M%S).log \
    PARALLEL=4

# 压缩备份文件
gzip $BACKUP_DIR/*.dmp

# 清理7天前的备份
find /backup -name "*.gz" -mtime +7 -delete

# 发送备份报告
echo "数据库备份完成: $(date)" | mail -s "数据库备份报告" <EMAIL>
```

#### 定时任务配置
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点执行全量备份
0 2 * * * /app/scripts/auto_backup.sh

# 每小时执行增量备份
0 * * * * /app/scripts/incremental_backup.sh

# 每天凌晨3点收集统计信息
0 3 * * * /app/scripts/gather_stats.sh
```

### 5.3 运维脚本

#### 健康检查脚本
```bash
#!/bin/bash
# /app/scripts/health_check.sh

echo "=== 数据库健康检查 ==="
echo "检查时间: $(date)"

# 检查数据库连接
sqlplus -s system/password << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SELECT 'DB_STATUS: ' || status FROM v\$instance;
SELECT 'DB_OPEN_MODE: ' || open_mode FROM v\$database;
EXIT;
EOF

# 检查表空间使用率
sqlplus -s system/password << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SELECT 'TABLESPACE: ' || tablespace_name || ' USAGE: ' || 
       ROUND((used_space/total_space)*100, 2) || '%'
FROM (
    SELECT 
        tablespace_name,
        SUM(bytes) as total_space,
        SUM(bytes) - SUM(NVL(free_space, 0)) as used_space
    FROM (
        SELECT tablespace_name, bytes, 0 as free_space
        FROM dba_data_files
        UNION ALL
        SELECT tablespace_name, 0 as bytes, bytes as free_space
        FROM dba_free_space
    )
    WHERE tablespace_name LIKE 'MEDIINSPECT%'
    GROUP BY tablespace_name
);
EXIT;
EOF

# 检查应用连接
echo "=== 应用连接检查 ==="
netstat -an | grep :1521 | wc -l

echo "=== 检查完成 ==="
```

## 6. 故障处理

### 6.1 常见问题及解决方案

#### 问题1：表空间不足
```sql
-- 检查表空间使用情况
SELECT 
    tablespace_name,
    ROUND(used_percent, 2) as used_percent,
    ROUND(used_space/1024/1024, 2) as used_mb,
    ROUND(free_space/1024/1024, 2) as free_mb
FROM (
    SELECT 
        tablespace_name,
        (used_space/total_space)*100 as used_percent,
        used_space,
        total_space - used_space as free_space
    FROM (
        SELECT 
            tablespace_name,
            SUM(bytes) as total_space,
            SUM(bytes) - SUM(NVL(free_space, 0)) as used_space
        FROM (
            SELECT tablespace_name, bytes, 0 as free_space FROM dba_data_files
            UNION ALL
            SELECT tablespace_name, 0 as bytes, bytes as free_space FROM dba_free_space
        )
        GROUP BY tablespace_name
    )
);

-- 扩展表空间
ALTER TABLESPACE MEDIINSPECT_DATA ADD DATAFILE 
'/oracle/oradata/mediinspect_data02.dbf' SIZE 1G AUTOEXTEND ON NEXT 100M MAXSIZE 10G;
```

#### 问题2：性能问题
```sql
-- 查找慢查询
SELECT 
    sql_id,
    sql_text,
    executions,
    elapsed_time/1000000 as elapsed_seconds,
    cpu_time/1000000 as cpu_seconds
FROM v$sql
WHERE elapsed_time > 5000000  -- 超过5秒的查询
ORDER BY elapsed_time DESC;

-- 检查锁等待
SELECT 
    s1.username as blocking_user,
    s1.sid as blocking_sid,
    s2.username as waiting_user,
    s2.sid as waiting_sid,
    lo.object_id,
    do.object_name
FROM v$lock l1, v$session s1, v$lock l2, v$session s2, v$locked_object lo, dba_objects do
WHERE s1.sid = l1.sid
AND s2.sid = l2.sid
AND l1.id1 = l2.id1
AND l1.id2 = l2.id2
AND l1.request = 0
AND l2.lmode = 0
AND lo.session_id = s2.sid
AND do.object_id = lo.object_id;
```

#### 问题3：数据不一致
```sql
-- 检查外键完整性
SELECT 
    'Table: ' || table_name || ', Missing references: ' || COUNT(*) as issue
FROM (
    SELECT 'MEDICAL_RECORD' as table_name FROM DUAL
    WHERE EXISTS (
        SELECT 1 FROM MEDICAL_RECORD mr
        LEFT JOIN PATIENT_INFO pi ON mr.PATIENT_ID = pi.ID
        WHERE pi.ID IS NULL AND mr.IS_DELETED = 0
    )
);

-- 修复数据不一致
UPDATE MEDICAL_RECORD 
SET IS_DELETED = 1, UPDATED_TIME = SYSDATE
WHERE PATIENT_ID NOT IN (SELECT ID FROM PATIENT_INFO WHERE IS_DELETED = 0)
AND IS_DELETED = 0;
```

### 6.2 回滚方案

#### 数据回滚
```bash
# 1. 停止应用服务
systemctl stop mediinspect-app

# 2. 恢复数据库
impdp system/password DIRECTORY=backup_dir DUMPFILE=backup_before_migration.dmp \
    SCHEMAS=mediinspect REMAP_SCHEMA=mediinspect:mediinspect_old

# 3. 切换数据源
# 修改应用配置文件中的数据库连接

# 4. 重启应用
systemctl start mediinspect-app
```

#### 配置回滚
```bash
# 恢复配置文件
cp /backup/config/* $ORACLE_HOME/dbs/
cp /backup/app_config/* /app/config/

# 重启数据库
sqlplus / as sysdba
SHUTDOWN IMMEDIATE;
STARTUP;
```

## 7. 部署检查清单

### 7.1 部署前检查
- [ ] 硬件资源满足要求
- [ ] 软件版本符合要求
- [ ] 网络连接正常
- [ ] 备份策略已制定
- [ ] 回滚方案已准备
- [ ] 部署权限已获取
- [ ] 维护窗口已确认

### 7.2 部署中检查
- [ ] 表空间创建成功
- [ ] 数据库对象创建完整
- [ ] 数据迁移无错误
- [ ] 约束启用成功
- [ ] 性能优化完成
- [ ] 安全配置生效

### 7.3 部署后检查
- [ ] 功能验证通过
- [ ] 性能测试达标
- [ ] 安全测试通过
- [ ] 监控配置正常
- [ ] 备份策略生效
- [ ] 文档更新完成
- [ ] 团队培训完成

## 8. 联系信息

### 8.1 技术支持
- **数据库团队**: <EMAIL>
- **应用团队**: <EMAIL>
- **运维团队**: <EMAIL>

### 8.2 紧急联系
- **值班电话**: 400-xxx-xxxx
- **紧急邮箱**: <EMAIL>

---

**部署指南版本**: 1.0  
**最后更新**: 2024-12-19  
**文档维护**: 数据库架构团队