/** @type {import('next').NextConfig} */
const nextConfig = {
  // 服务器外部包配置
  serverExternalPackages: ['oracledb'],

  // API路由重写配置
  async rewrites() {
    return [
      {
        source: '/api/v1/:path*',
        destination: '/api/:path*',
      },
    ];
  },

  // API CORS 头部配置
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ];
  },

  // Webpack配置 - Oracle数据库支持
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.externals.push('oracledb');
    }

    // 路径别名配置
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src')
    }

    return config;
  },

  // 输出配置
  output: 'standalone',

  // 关闭 Next.js 品牌标识
  poweredByHeader: false,
};

module.exports = nextConfig;
