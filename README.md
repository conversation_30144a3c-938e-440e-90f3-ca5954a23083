# 医保基金监管平台 v2.0 - 数据库初始化

## 📋 概述

这是医保基金监管平台v2.0的数据库初始化工具包，提供完整的数据库设置和管理功能。

## 🏗️ 项目结构

```
mediinspect-v2/
├── schema/                          # 数据库设计和SQL脚本
│   ├── DATABASE_DESIGN.md           # 数据库设计文档
│   ├── 00_design_principles.md      # 设计原则说明
│   ├── 01_user_management.sql       # 用户管理模块
│   ├── 02_medical_case.sql          # 医疗病例模块
│   ├── 03_supervision_rules.sql     # 监管规则模块
│   ├── 04_knowledge_base.sql        # 知识库模块
│   ├── 05_system_logs.sql           # 系统日志模块
│   └── check_completeness.sql       # 完整性检查脚本
├── scripts/                         # 初始化脚本
│   ├── test-db-connection.js        # 数据库连接测试
│   ├── init-database.js             # 数据库结构初始化
│   ├── init-default-data.js         # 默认数据创建
│   ├── verify-database.js           # 数据库验证
│   └── setup-database.js            # 完整设置脚本
├── .env.local                       # 环境配置文件
├── package.json                     # 项目配置
└── README.md                        # 本文件
```

## ⚙️ 环境要求

- **Node.js**: >= 18.0.0
- **Oracle Database**: >= 12c
- **操作系统**: Windows/macOS/Linux

## 🔧 安装依赖

```bash
npm install
```

## 📝 环境配置

在项目根目录创建 `.env.local` 文件：

```env
# 数据库连接配置
DB_HOST=localhost
DB_PORT=1521
DB_SERVICE_NAME=ORCL
DB_USERNAME=mediinspect
DB_PASSWORD=your_password

# 连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_INCREMENT=1
```

## 🚀 快速开始

### 1. 完整数据库设置（推荐）

```bash
npm run db:setup
```

这个命令会自动执行：
- ✅ 测试数据库连接
- ✅ 初始化数据库结构（22个表）
- ✅ 创建默认数据（角色、管理员用户等）
- ✅ 验证设置结果

### 2. 强制重新设置

```bash
npm run db:setup-force
```

⚠️ **警告**: 这会清除现有数据，请谨慎使用！

## 📋 分步执行

如果需要分步执行，可以使用以下命令：

### 测试数据库连接
```bash
npm run db:test
```

### 初始化数据库结构
```bash
npm run db:init
```

### 创建默认数据
```bash
npm run db:data
```

### 验证数据库
```bash
npm run db:verify
```

## 🔐 默认账户信息

设置完成后，系统会创建以下默认账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | Admin123! | 系统管理员 | 拥有所有权限 |

⚠️ **重要**: 请在首次登录后立即修改默认密码！

## 👥 默认角色

系统会自动创建以下角色：

| 角色代码 | 角色名称 | 说明 |
|----------|----------|------|
| ADMIN | 系统管理员 | 拥有所有权限 |
| SUPERVISOR | 监管主管 | 负责监管规则制定和审核 |
| OPERATOR | 业务操作员 | 负责日常业务操作 |
| AUDITOR | 审核员 | 负责规则执行结果审核 |
| VIEWER | 查看员 | 只能查看数据 |

## 📚 知识库分类

系统会自动创建以下知识库分类：

- 📋 政策法规
- 📋 管理制度  
- 📋 操作手册
- 📋 常见问题
- 📋 培训资料

## 🔍 故障排除

### 连接失败

**错误**: `ORA-12541: TNS:no listener`
**解决**: 检查数据库服务器地址和端口

**错误**: `ORA-01017: invalid username/password`
**解决**: 检查用户名和密码

**错误**: `ORA-12514: TNS:listener does not currently know of service`
**解决**: 检查服务名是否正确

### 权限不足

**错误**: `ORA-00942: table or view does not exist`
**解决**: 确保用户有足够的权限创建表

### 依赖问题

**错误**: `Cannot find module 'oracledb'`
**解决**: 运行 `npm install` 安装依赖

## 📖 数据库设计

详细的数据库设计文档请查看：
- [数据库设计文档](./schema/DATABASE_DESIGN.md)
- [设计原则说明](./schema/00_design_principles.md)

## 🛠️ 高级用法

### 自定义设置

```bash
# 跳过连接测试
node scripts/setup-database.js --skip-test

# 只初始化结构，不创建默认数据
node scripts/setup-database.js --skip-data

# 查看所有选项
npm run db:help
```

### 手动执行SQL

```bash
# 连接到数据库
sqlplus mediinspect/password@localhost:1521/ORCL

# 执行完整性检查
@schema/check_completeness.sql
```

## 📊 验证检查

数据库设置完成后，系统会自动进行以下验证：

- ✅ 表结构完整性（22个表）
- ✅ 序列完整性（22个序列）
- ✅ 触发器完整性（22个触发器）
- ✅ 索引完整性（50+个索引）
- ✅ 约束完整性（100+个约束）
- ✅ 默认数据完整性

## 🔄 更新和维护

### 备份数据库

```bash
# 导出数据
expdp mediinspect/password@localhost:1521/ORCL \
  directory=BACKUP_DIR \
  dumpfile=mediinspect_backup.dmp \
  full=y
```

### 更新统计信息

```sql
-- 更新所有表的统计信息
EXEC DBMS_STATS.GATHER_SCHEMA_STATS('MEDIINSPECT');
```

## 📞 技术支持

如有问题，请联系：
- **技术支持**: <EMAIL>
- **项目文档**: [GitHub Wiki](https://github.com/your-org/mediinspect-v2/wiki)

## 📄 许可证

本项目为内部使用，未经授权不得外传。

---

*最后更新: 2025-01-05*
