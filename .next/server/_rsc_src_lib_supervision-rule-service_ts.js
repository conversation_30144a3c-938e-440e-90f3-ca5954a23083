"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supervision-rule-service_ts";
exports.ids = ["_rsc_src_lib_supervision-rule-service_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildCountSQL: () => (/* binding */ buildCountSQL),\n/* harmony export */   buildPaginationSQL: () => (/* binding */ buildPaginationSQL),\n/* harmony export */   checkConnection: () => (/* binding */ checkConnection),\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   formatBinds: () => (/* binding */ formatBinds),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   getPoolStats: () => (/* binding */ getPoolStats),\n/* harmony export */   initializePool: () => (/* binding */ initializePool),\n/* harmony export */   monitorPoolHealth: () => (/* binding */ monitorPoolHealth)\n/* harmony export */ });\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! oracledb */ \"oracledb\");\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(oracledb__WEBPACK_IMPORTED_MODULE_0__);\n\n// Oracle数据库连接配置 - 性能优化版本\nconst dbConfig = {\n    user: process.env['DB_USERNAME'],\n    password: process.env['DB_PASSWORD'],\n    connectString: `${process.env['DB_HOST']}:${process.env['DB_PORT']}/${process.env['DB_SERVICE_NAME']}`,\n    poolMin: parseInt(process.env['DB_POOL_MIN'] || '5'),\n    poolMax: parseInt(process.env['DB_POOL_MAX'] || '20'),\n    poolIncrement: parseInt(process.env['DB_POOL_INCREMENT'] || '2'),\n    poolTimeout: parseInt(process.env['DB_POOL_TIMEOUT'] || '30'),\n    stmtCacheSize: parseInt(process.env['DB_STMT_CACHE_SIZE'] || '50'),\n    queueMax: parseInt(process.env['DB_QUEUE_MAX'] || '100'),\n    queueTimeout: parseInt(process.env['DB_QUEUE_TIMEOUT'] || '10000'),\n    enableStatistics: true\n};\n// 配置Oracle客户端\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().outFormat) = (oracledb__WEBPACK_IMPORTED_MODULE_0___default().OUT_FORMAT_OBJECT);\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().autoCommit) = true;\n// 连接池\nlet pool = null;\n/**\n * 初始化数据库连接池\n */ async function initializePool() {\n    try {\n        if (!pool) {\n            pool = await oracledb__WEBPACK_IMPORTED_MODULE_0___default().createPool(dbConfig);\n            console.log('✅ 数据库连接池初始化成功');\n            // 启动健康检查\n            startHealthCheck();\n        }\n    } catch (error) {\n        console.error('❌ 数据库连接池初始化失败:', error);\n        throw error;\n    }\n}\n/**\n * 获取数据库连接\n */ async function getConnection() {\n    try {\n        if (!pool) {\n            await initializePool();\n        }\n        return await pool.getConnection();\n    } catch (error) {\n        console.error('❌ 获取数据库连接失败:', error);\n        throw error;\n    }\n}\n// 清理Oracle查询结果，移除循环引用\nfunction cleanOracleResult(obj) {\n    if (obj === null || obj === undefined) {\n        return obj;\n    }\n    if (typeof obj !== 'object') {\n        return obj;\n    }\n    if (Array.isArray(obj)) {\n        return obj.map((item)=>cleanOracleResult(item));\n    }\n    if (obj instanceof Date) {\n        return obj;\n    }\n    if (Buffer.isBuffer(obj)) {\n        return obj;\n    }\n    // 创建新对象，只复制基本属性\n    const cleanObj = {};\n    for (const [key, value] of Object.entries(obj)){\n        // 跳过Oracle内部属性和可能的循环引用\n        if (typeof key === 'string' && (key.startsWith('_') || key.toLowerCase().includes('connection') || key.toLowerCase().includes('pool') || key.toLowerCase().includes('client') || key.toLowerCase().includes('socket') || key.toLowerCase().includes('stream') || key.toLowerCase().includes('cursor') || key.toLowerCase().includes('resultset') || key === 'domain' || key === 'constructor' || key === 'prototype')) {\n            continue;\n        }\n        try {\n            cleanObj[key] = cleanOracleResult(value);\n        } catch (error) {\n            continue;\n        }\n    }\n    return cleanObj;\n}\n/**\n * 执行SQL查询\n */ async function executeQuery(sql, binds = {}, options = {}) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        const result = await connection.execute(sql, binds, options);\n        // 使用经过验证的清理函数，移除循环引用\n        const cleanRows = result.rows ? cleanOracleResult(result.rows) : [];\n        return {\n            rows: cleanRows,\n            rowsAffected: result.rowsAffected,\n            metaData: result.metaData ? cleanOracleResult(result.metaData) : undefined\n        };\n    } catch (error) {\n        console.error('❌ SQL查询执行失败:', error);\n        console.error('SQL:', sql);\n        console.error('Binds:', binds);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 执行事务\n */ async function executeTransaction(operations) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        // 关闭自动提交\n        // connection.autoCommit = false // Oracle连接不支持直接设置autoCommit属性\n        // 执行操作\n        const result = await operations(connection);\n        // 提交事务\n        await connection.commit();\n        return result;\n    } catch (error) {\n        // 回滚事务\n        if (connection) {\n            try {\n                await connection.rollback();\n            } catch (rollbackError) {\n                console.error('⚠️ 事务回滚失败:', rollbackError);\n            }\n        }\n        console.error('❌ 事务执行失败:', error);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                // 恢复自动提交\n                // connection.autoCommit = true // Oracle连接不支持直接设置autoCommit属性\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 关闭数据库连接池\n */ async function closePool() {\n    try {\n        if (pool) {\n            // 停止健康检查\n            stopHealthCheck();\n            await pool.close(10); // 等待10秒关闭\n            pool = null;\n            console.log('✅ 数据库连接池已关闭');\n        }\n    } catch (error) {\n        console.error('❌ 关闭数据库连接池失败:', error);\n        throw error;\n    }\n}\n/**\n * 检查数据库连接状态\n */ async function checkConnection() {\n    try {\n        const result = await executeQuery('SELECT SYSDATE FROM DUAL');\n        return !!(result.rows && result.rows.length > 0);\n    } catch (error) {\n        console.error('❌ 数据库连接检查失败:', error);\n        return false;\n    }\n}\n/**\n * 格式化SQL绑定参数\n */ function formatBinds(params) {\n    const formatted = {};\n    for (const [key, value] of Object.entries(params)){\n        if (value === null || value === undefined) {\n            formatted[key] = {\n                val: null,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'string') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'number') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().NUMBER)\n            };\n        } else if (value instanceof Date) {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().DATE)\n            };\n        } else {\n            formatted[key] = value;\n        }\n    }\n    return formatted;\n}\n/**\n * 构建分页SQL\n */ function buildPaginationSQL(baseSql, page = 1, pageSize = 10, orderBy = 'ID') {\n    const offset = (page - 1) * pageSize;\n    // 检查baseSql是否已经包含ORDER BY子句\n    const hasOrderBy = /ORDER\\s+BY/i.test(baseSql);\n    return `\n    SELECT * FROM (\n      SELECT a.*, ROWNUM rnum FROM (\n        ${baseSql}\n        ${hasOrderBy ? '' : `ORDER BY ${orderBy}`}\n      ) a\n      WHERE ROWNUM <= ${offset + pageSize}\n    )\n    WHERE rnum > ${offset}\n  `;\n}\n/**\n * 构建计数SQL\n */ function buildCountSQL(baseSql) {\n    // 移除ORDER BY子句\n    const cleanSql = baseSql.replace(/ORDER\\s+BY\\s+[^)]*$/i, '');\n    return `SELECT COUNT(*) as TOTAL FROM (${cleanSql})`;\n}\n// 进程退出时关闭连接池\nprocess.on('SIGINT', async ()=>{\n    await closePool();\n    process.exit(0);\n});\nprocess.on('SIGTERM', async ()=>{\n    await closePool();\n    process.exit(0);\n});\n/**\n * 获取连接池统计信息\n */ function getPoolStats() {\n    if (!pool) {\n        return {\n            connectionsOpen: 0,\n            connectionsInUse: 0,\n            connectionsAvailable: 0,\n            queueLength: 0,\n            queueTimeout: 0,\n            poolMin: 0,\n            poolMax: 0,\n            poolIncrement: 0\n        };\n    }\n    return {\n        connectionsOpen: pool.connectionsOpen,\n        connectionsInUse: pool.connectionsInUse,\n        connectionsAvailable: pool.connectionsOpen - pool.connectionsInUse,\n        queueLength: pool.queueLength || 0,\n        queueTimeout: pool.queueTimeout || 0,\n        poolMin: pool.poolMin,\n        poolMax: pool.poolMax,\n        poolIncrement: pool.poolIncrement\n    };\n}\n/**\n * 监控连接池健康状态\n */ function monitorPoolHealth() {\n    const stats = getPoolStats();\n    if (!pool) {\n        return {\n            status: 'critical',\n            message: '连接池未初始化',\n            stats\n        };\n    }\n    const utilizationRate = stats.connectionsOpen > 0 ? stats.connectionsInUse / stats.connectionsOpen * 100 : 0;\n    const queueUtilization = stats.queueLength > 0 ? stats.queueLength / (dbConfig.queueMax || 100) * 100 : 0;\n    if (utilizationRate > 90 || queueUtilization > 80) {\n        return {\n            status: 'critical',\n            message: `连接池使用率过高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    if (utilizationRate > 70 || queueUtilization > 50) {\n        return {\n            status: 'warning',\n            message: `连接池使用率偏高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    return {\n        status: 'healthy',\n        message: `连接池运行正常: ${utilizationRate.toFixed(1)}% 使用率`,\n        stats\n    };\n}\n/**\n * 启动连接池健康检查\n */ function startHealthCheck() {\n    // 如果已经有定时器在运行，先清除它\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n    }\n    global.__healthCheckInterval = setInterval(()=>{\n        const health = monitorPoolHealth();\n        if (health.status === 'critical') {\n            console.error('🚨 连接池健康检查:', health.message);\n        } else if (health.status === 'warning') {\n            console.warn('⚠️ 连接池健康检查:', health.message);\n        }\n    }, 30000); // 每30秒检查一次\n}\n/**\n * 停止连接池健康检查\n */ function stopHealthCheck() {\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n        global.__healthCheckInterval = undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supervision-rule-service.ts":
/*!*********************************************!*\
  !*** ./src/lib/supervision-rule-service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupervisionRule: () => (/* binding */ createSupervisionRule),\n/* harmony export */   deleteSupervisionRule: () => (/* binding */ deleteSupervisionRule),\n/* harmony export */   executeSupervisionRules: () => (/* binding */ executeSupervisionRules),\n/* harmony export */   getChildRules: () => (/* binding */ getChildRules),\n/* harmony export */   getRuleExecutionLogs: () => (/* binding */ getRuleExecutionLogs),\n/* harmony export */   getRuleExecutionResults: () => (/* binding */ getRuleExecutionResults),\n/* harmony export */   getSupervisionRuleById: () => (/* binding */ getSupervisionRuleById),\n/* harmony export */   getSupervisionRuleStatistics: () => (/* binding */ getSupervisionRuleStatistics),\n/* harmony export */   getSupervisionRules: () => (/* binding */ getSupervisionRules),\n/* harmony export */   toggleSupervisionRule: () => (/* binding */ toggleSupervisionRule),\n/* harmony export */   updateSupervisionRule: () => (/* binding */ updateSupervisionRule)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n/**\n * 获取监管规则列表（分页）\n */ async function getSupervisionRules(params) {\n    try {\n        const { page = 1, pageSize = 10, search = '', ruleType, ruleCategory, severityLevel, isActive, createdFrom, startDate, endDate, sortBy = 'CREATED_AT', sortOrder = 'DESC' } = params;\n        // 字段名映射：前端字段名 -> 数据库字段名\n        const fieldMapping = {\n            'createdAt': 'CREATED_AT',\n            'updatedAt': 'UPDATED_AT',\n            'ruleName': 'RULE_NAME',\n            'ruleCode': 'RULE_CODE',\n            'ruleType': 'RULE_TYPE',\n            'ruleCategory': 'RULE_CATEGORY',\n            'severityLevel': 'SEVERITY_LEVEL',\n            'isActive': 'IS_ACTIVE'\n        };\n        // 转换排序字段名\n        const dbSortBy = fieldMapping[sortBy] || 'CREATED_AT';\n        console.log(`🔍 字段映射调试: sortBy=${sortBy}, dbSortBy=${dbSortBy}`);\n        // 构建WHERE条件\n        const conditions = [\n            'IS_DELETED = 0'\n        ];\n        const queryParams = {};\n        if (search) {\n            conditions.push(`(\n        UPPER(RULE_NAME) LIKE UPPER(:search) OR \n        UPPER(RULE_CODE) LIKE UPPER(:search) OR \n        UPPER(DESCRIPTION) LIKE UPPER(:search)\n      )`);\n            queryParams.search = `%${search}%`;\n        }\n        if (ruleType) {\n            conditions.push('RULE_TYPE = :ruleType');\n            queryParams.ruleType = ruleType;\n        }\n        if (ruleCategory) {\n            conditions.push('RULE_CATEGORY = :ruleCategory');\n            queryParams.ruleCategory = ruleCategory;\n        }\n        if (severityLevel) {\n            conditions.push('SEVERITY_LEVEL = :severityLevel');\n            queryParams.severityLevel = severityLevel;\n        }\n        if (isActive !== undefined) {\n            conditions.push('IS_ACTIVE = :isActive');\n            queryParams.isActive = isActive ? 1 : 0;\n        }\n        if (createdFrom) {\n            conditions.push('CREATED_FROM = :createdFrom');\n            queryParams.createdFrom = createdFrom;\n        }\n        if (startDate) {\n            conditions.push('CREATED_AT >= TO_DATE(:startDate, \\'YYYY-MM-DD\\')');\n            queryParams.startDate = startDate;\n        }\n        if (endDate) {\n            conditions.push('CREATED_AT <= TO_DATE(:endDate, \\'YYYY-MM-DD\\')');\n            queryParams.endDate = endDate;\n        }\n        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';\n        // 获取总数\n        const countSql = `\n      SELECT COUNT(*) as TOTAL_COUNT\n      FROM RULE_SUPERVISION\n      ${whereClause}\n    `;\n        const countResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSql, queryParams);\n        const totalCount = countResult.rows?.[0]?.TOTAL_COUNT || 0;\n        // 计算分页\n        const offset = (page - 1) * pageSize;\n        const totalPages = Math.ceil(totalCount / pageSize);\n        // 获取数据\n        const dataSql = `\n      SELECT * FROM (\n        SELECT \n          ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,\n          RULE_CONTENT, RULE_SQL, RULE_DSL, PRIORITY_LEVEL, SEVERITY_LEVEL,\n          IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE, VERSION_NUMBER, PARENT_RULE_ID,\n          RULE_SOURCE, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT, LAST_EXECUTED_AT,\n          IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY,\n          ROW_NUMBER() OVER (ORDER BY ${dbSortBy} ${sortOrder}) as RN\n        FROM RULE_SUPERVISION\n        ${whereClause}\n      ) WHERE RN > :offset AND RN <= :limit\n    `;\n        queryParams.offset = offset;\n        queryParams.limit = offset + pageSize;\n        const dataResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(dataSql, queryParams);\n        const rules = (dataResult.rows || []).map((row)=>{\n            // 确保所有字段都是基本类型，避免Oracle对象引用\n            const cleanRow = {\n                id: Number(row.ID) || 0,\n                ruleCode: String(row.RULE_CODE || ''),\n                ruleName: String(row.RULE_NAME || ''),\n                ruleType: String(row.RULE_TYPE || 'BASIC'),\n                ruleCategory: String(row.RULE_CATEGORY || 'COMPLIANCE_CHECK'),\n                description: String(row.DESCRIPTION || ''),\n                ruleContent: String(row.RULE_CONTENT || ''),\n                ruleSql: String(row.RULE_SQL || ''),\n                ruleDsl: String(row.RULE_DSL || ''),\n                priorityLevel: Number(row.PRIORITY_LEVEL || 1),\n                severityLevel: String(row.SEVERITY_LEVEL || 'MEDIUM'),\n                isActive: Boolean(row.IS_ACTIVE === 1),\n                effectiveDate: row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] || '' : new Date().toISOString().split('T')[0] || '',\n                expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,\n                versionNumber: String(row.VERSION_NUMBER || '1.0'),\n                parentRuleId: row.PARENT_RULE_ID ? Number(row.PARENT_RULE_ID) : undefined,\n                ruleSource: String(row.RULE_SOURCE || ''),\n                createdFrom: String(row.CREATED_FROM || 'MANUAL'),\n                executionCount: Number(row.EXECUTION_COUNT || 0),\n                successCount: Number(row.SUCCESS_COUNT || 0),\n                lastExecutedAt: row.LAST_EXECUTED_AT ? new Date(row.LAST_EXECUTED_AT).toISOString() : undefined,\n                isDeleted: Boolean(row.IS_DELETED === 1),\n                createdAt: row.CREATED_AT ? String(row.CREATED_AT) : new Date().toISOString(),\n                updatedAt: row.UPDATED_AT ? String(row.UPDATED_AT) : new Date().toISOString(),\n                createdBy: Number(row.CREATED_BY) || undefined,\n                updatedBy: Number(row.UPDATED_BY) || undefined\n            };\n            return cleanRow;\n        });\n        return {\n            items: rules,\n            page,\n            pageSize,\n            total: totalCount,\n            totalPages\n        };\n    } catch (error) {\n        console.error('❌ 获取监管规则列表失败:', error);\n        throw new Error('获取监管规则列表失败');\n    }\n}\n/**\n * 根据ID获取监管规则详情\n */ async function getSupervisionRuleById(ruleId) {\n    try {\n        const sql = `\n      SELECT \n        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,\n        RULE_CONTENT, RULE_SQL, RULE_DSL, PRIORITY_LEVEL, SEVERITY_LEVEL,\n        IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE, VERSION_NUMBER, PARENT_RULE_ID,\n        RULE_SOURCE, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT, LAST_EXECUTED_AT,\n        IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY\n      FROM RULE_SUPERVISION\n      WHERE ID = :ruleId AND IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        const rule = {\n            id: row.ID,\n            ruleCode: row.RULE_CODE,\n            ruleName: row.RULE_NAME,\n            ruleType: row.RULE_TYPE,\n            ruleCategory: row.RULE_CATEGORY,\n            description: row.DESCRIPTION,\n            ruleContent: row.RULE_CONTENT,\n            ruleSql: row.RULE_SQL,\n            ruleDsl: row.RULE_DSL,\n            priorityLevel: row.PRIORITY_LEVEL,\n            severityLevel: row.SEVERITY_LEVEL,\n            isActive: row.IS_ACTIVE === 1,\n            effectiveDate: row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n            expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,\n            versionNumber: row.VERSION_NUMBER,\n            parentRuleId: row.PARENT_RULE_ID,\n            ruleSource: row.RULE_SOURCE,\n            createdFrom: row.CREATED_FROM,\n            executionCount: row.EXECUTION_COUNT,\n            successCount: row.SUCCESS_COUNT,\n            lastExecutedAt: row.LAST_EXECUTED_AT ? new Date(row.LAST_EXECUTED_AT).toISOString() : undefined,\n            isDeleted: row.IS_DELETED === 1,\n            createdAt: row.CREATED_AT,\n            updatedAt: row.UPDATED_AT,\n            createdBy: row.CREATED_BY,\n            updatedBy: row.UPDATED_BY\n        };\n        // 获取关联数据\n        rule.executionLogs = await getRuleExecutionLogs(ruleId);\n        rule.executionResults = await getRuleExecutionResults(ruleId);\n        // 获取父规则和子规则（避免循环引用）\n        if (rule.parentRuleId) {\n            const parentRule = await getBasicRuleInfo(rule.parentRuleId);\n            rule.parentRule = parentRule || undefined;\n        }\n        rule.childRules = await getChildRules(ruleId);\n        return rule;\n    } catch (error) {\n        console.error('❌ 获取监管规则详情失败:', error);\n        throw new Error('获取监管规则详情失败');\n    }\n}\n/**\n * 获取规则的执行记录\n */ async function getRuleExecutionLogs(ruleId) {\n    try {\n        const sql = `\n      SELECT \n        ID, RULE_ID, EXECUTION_ID, EXECUTION_STATUS, STARTED_AT, ENDED_AT,\n        EXECUTION_DURATION, PROCESSED_RECORD_COUNT, MATCHED_RECORD_COUNT,\n        ERROR_MESSAGE, EXECUTION_PARAMS, EXECUTION_RESULT, EXECUTED_BY, CREATED_AT\n      FROM RULE_EXECUTION_LOG\n      WHERE RULE_ID = :ruleId\n      ORDER BY STARTED_AT DESC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId\n        });\n        return (result.rows || []).map((row)=>({\n                id: row.ID,\n                ruleId: row.RULE_ID,\n                executionId: row.EXECUTION_ID,\n                executionStatus: row.EXECUTION_STATUS,\n                startedAt: row.STARTED_AT,\n                endedAt: row.ENDED_AT,\n                executionDuration: row.EXECUTION_DURATION,\n                processedRecordCount: row.PROCESSED_RECORD_COUNT,\n                matchedRecordCount: row.MATCHED_RECORD_COUNT,\n                errorMessage: row.ERROR_MESSAGE,\n                executionParams: row.EXECUTION_PARAMS,\n                executionResult: row.EXECUTION_RESULT,\n                executedBy: row.EXECUTED_BY,\n                createdAt: row.CREATED_AT\n            }));\n    } catch (error) {\n        console.error('❌ 获取规则执行记录失败:', error);\n        return [];\n    }\n}\n/**\n * 获取规则的执行结果\n */ async function getRuleExecutionResults(ruleId) {\n    try {\n        const sql = `\n      SELECT \n        ID, EXECUTION_LOG_ID, RULE_ID, CASE_ID, RESULT_TYPE, RISK_LEVEL,\n        VIOLATION_DESCRIPTION, VIOLATION_AMOUNT, EVIDENCE_DATA, RULE_MATCHED_FIELDS,\n        CONFIDENCE_SCORE, RESULT_STATUS, IS_AUTO_PROCESSED, IS_FOLLOW_UP_REQUIRED,\n        RELATED_CASE_COUNT, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY\n      FROM RULE_EXECUTION_RESULT\n      WHERE RULE_ID = :ruleId\n      ORDER BY CREATED_AT DESC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId\n        });\n        return (result.rows || []).map((row)=>({\n                id: row.ID,\n                executionLogId: row.EXECUTION_LOG_ID,\n                ruleId: row.RULE_ID,\n                caseId: row.CASE_ID,\n                resultType: row.RESULT_TYPE,\n                riskLevel: row.RISK_LEVEL,\n                violationDescription: row.VIOLATION_DESCRIPTION,\n                violationAmount: row.VIOLATION_AMOUNT,\n                evidenceData: row.EVIDENCE_DATA,\n                ruleMatchedFields: row.RULE_MATCHED_FIELDS,\n                confidenceScore: row.CONFIDENCE_SCORE,\n                resultStatus: row.RESULT_STATUS,\n                isAutoProcessed: row.IS_AUTO_PROCESSED === 1,\n                isFollowUpRequired: row.IS_FOLLOW_UP_REQUIRED === 1,\n                relatedCaseCount: row.RELATED_CASE_COUNT,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT,\n                createdBy: row.CREATED_BY,\n                updatedBy: row.UPDATED_BY\n            }));\n    } catch (error) {\n        console.error('❌ 获取规则执行结果失败:', error);\n        return [];\n    }\n}\n/**\n * 获取基本规则信息（避免循环引用）\n */ async function getBasicRuleInfo(ruleId) {\n    try {\n        const sql = `\n      SELECT\n        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,\n        PRIORITY_LEVEL, SEVERITY_LEVEL, IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE,\n        VERSION_NUMBER, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT,\n        CREATED_AT, UPDATED_AT\n      FROM RULE_SUPERVISION\n      WHERE ID = :ruleId AND IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            id: row.ID,\n            ruleCode: row.RULE_CODE,\n            ruleName: row.RULE_NAME,\n            ruleType: row.RULE_TYPE,\n            ruleCategory: row.RULE_CATEGORY,\n            description: row.DESCRIPTION,\n            ruleContent: '',\n            priorityLevel: row.PRIORITY_LEVEL,\n            severityLevel: row.SEVERITY_LEVEL,\n            isActive: row.IS_ACTIVE === 1,\n            effectiveDate: row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n            expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,\n            versionNumber: row.VERSION_NUMBER,\n            createdFrom: row.CREATED_FROM,\n            executionCount: row.EXECUTION_COUNT,\n            successCount: row.SUCCESS_COUNT,\n            isDeleted: false,\n            createdAt: row.CREATED_AT,\n            updatedAt: row.UPDATED_AT\n        };\n    } catch (error) {\n        console.error('❌ 获取基本规则信息失败:', error);\n        return null;\n    }\n}\n/**\n * 获取子规则\n */ async function getChildRules(parentRuleId) {\n    try {\n        const sql = `\n      SELECT \n        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,\n        PRIORITY_LEVEL, SEVERITY_LEVEL, IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE,\n        VERSION_NUMBER, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT,\n        CREATED_AT, UPDATED_AT\n      FROM RULE_SUPERVISION\n      WHERE PARENT_RULE_ID = :parentRuleId AND IS_DELETED = 0\n      ORDER BY PRIORITY_LEVEL DESC, CREATED_AT ASC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            parentRuleId\n        });\n        return (result.rows || []).map((row)=>({\n                id: row.ID,\n                ruleCode: row.RULE_CODE,\n                ruleName: row.RULE_NAME,\n                ruleType: row.RULE_TYPE,\n                ruleCategory: row.RULE_CATEGORY,\n                description: row.DESCRIPTION,\n                ruleContent: '',\n                priorityLevel: row.PRIORITY_LEVEL,\n                severityLevel: row.SEVERITY_LEVEL,\n                isActive: row.IS_ACTIVE === 1,\n                effectiveDate: row.EFFECTIVE_DATE ? new Date(row.EFFECTIVE_DATE).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n                expiryDate: row.EXPIRY_DATE ? new Date(row.EXPIRY_DATE).toISOString().split('T')[0] : undefined,\n                versionNumber: row.VERSION_NUMBER,\n                parentRuleId,\n                createdFrom: row.CREATED_FROM,\n                executionCount: row.EXECUTION_COUNT,\n                successCount: row.SUCCESS_COUNT,\n                isDeleted: false,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT\n            }));\n    } catch (error) {\n        console.error('❌ 获取子规则失败:', error);\n        return [];\n    }\n}\n/**\n * 创建监管规则\n */ async function createSupervisionRule(ruleData, createdBy) {\n    try {\n        // 先获取下一个ID\n        const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM RULE_SUPERVISION`;\n        const nextIdResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(getNextIdSql);\n        const ruleId = nextIdResult.rows?.[0]?.NEXT_ID || 1;\n        const sql = `\n      INSERT INTO RULE_SUPERVISION (\n        ID, RULE_CODE, RULE_NAME, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,\n        RULE_CONTENT, RULE_SQL, RULE_DSL, PRIORITY_LEVEL, SEVERITY_LEVEL,\n        IS_ACTIVE, EFFECTIVE_DATE, EXPIRY_DATE, VERSION_NUMBER, PARENT_RULE_ID,\n        RULE_SOURCE, CREATED_FROM, EXECUTION_COUNT, SUCCESS_COUNT,\n        IS_DELETED, CREATED_BY, UPDATED_BY\n      ) VALUES (\n        :ruleId, :ruleCode, :ruleName, :ruleType, :ruleCategory, :description,\n        :ruleContent, :ruleSql, :ruleDsl, :priorityLevel, :severityLevel,\n        1, TO_DATE(:effectiveDate, 'YYYY-MM-DD'),\n        CASE WHEN :expiryDate IS NOT NULL THEN TO_DATE(:expiryDate, 'YYYY-MM-DD') ELSE NULL END,\n        :versionNumber, :parentRuleId, :ruleSource, :createdFrom, 0, 0,\n        0, :createdBy, :createdBy\n      )\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId,\n            ruleCode: ruleData.ruleCode,\n            ruleName: ruleData.ruleName,\n            ruleType: ruleData.ruleType,\n            ruleCategory: ruleData.ruleCategory,\n            description: ruleData.description,\n            ruleContent: ruleData.ruleContent,\n            ruleSql: ruleData.ruleSql,\n            ruleDsl: ruleData.ruleDsl,\n            priorityLevel: ruleData.priorityLevel,\n            severityLevel: ruleData.severityLevel,\n            effectiveDate: ruleData.effectiveDate,\n            expiryDate: ruleData.expiryDate,\n            versionNumber: ruleData.versionNumber,\n            parentRuleId: ruleData.parentRuleId,\n            ruleSource: ruleData.ruleSource,\n            createdFrom: ruleData.createdFrom,\n            createdBy\n        });\n        return ruleId;\n    } catch (error) {\n        console.error('❌ 创建监管规则失败:', error);\n        throw new Error('创建监管规则失败');\n    }\n}\n/**\n * 更新监管规则\n */ async function updateSupervisionRule(ruleId, updateData, updatedBy) {\n    try {\n        const updateFields = [];\n        const params = {\n            ruleId,\n            updatedBy\n        };\n        if (updateData.ruleName !== undefined) {\n            updateFields.push('RULE_NAME = :ruleName');\n            params.ruleName = updateData.ruleName;\n        }\n        if (updateData.ruleCategory !== undefined) {\n            updateFields.push('RULE_CATEGORY = :ruleCategory');\n            params.ruleCategory = updateData.ruleCategory;\n        }\n        if (updateData.description !== undefined) {\n            updateFields.push('DESCRIPTION = :description');\n            params.description = updateData.description;\n        }\n        if (updateData.ruleContent !== undefined) {\n            updateFields.push('RULE_CONTENT = :ruleContent');\n            params.ruleContent = updateData.ruleContent;\n        }\n        if (updateData.ruleSql !== undefined) {\n            updateFields.push('RULE_SQL = :ruleSql');\n            params.ruleSql = updateData.ruleSql;\n        }\n        if (updateData.ruleDsl !== undefined) {\n            updateFields.push('RULE_DSL = :ruleDsl');\n            params.ruleDsl = updateData.ruleDsl;\n        }\n        if (updateData.priorityLevel !== undefined) {\n            updateFields.push('PRIORITY_LEVEL = :priorityLevel');\n            params.priorityLevel = updateData.priorityLevel;\n        }\n        if (updateData.severityLevel !== undefined) {\n            updateFields.push('SEVERITY_LEVEL = :severityLevel');\n            params.severityLevel = updateData.severityLevel;\n        }\n        if (updateData.isActive !== undefined) {\n            updateFields.push('IS_ACTIVE = :isActive');\n            params.isActive = updateData.isActive ? 1 : 0;\n        }\n        if (updateData.effectiveDate !== undefined) {\n            updateFields.push('EFFECTIVE_DATE = TO_DATE(:effectiveDate, \\'YYYY-MM-DD\\')');\n            params.effectiveDate = updateData.effectiveDate;\n        }\n        if (updateData.expiryDate !== undefined) {\n            updateFields.push('EXPIRY_DATE = CASE WHEN :expiryDate IS NOT NULL THEN TO_DATE(:expiryDate, \\'YYYY-MM-DD\\') ELSE NULL END');\n            params.expiryDate = updateData.expiryDate;\n        }\n        if (updateData.ruleSource !== undefined) {\n            updateFields.push('RULE_SOURCE = :ruleSource');\n            params.ruleSource = updateData.ruleSource;\n        }\n        if (updateFields.length === 0) {\n            throw new Error('没有提供要更新的字段');\n        }\n        updateFields.push('UPDATED_BY = :updatedBy');\n        updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP');\n        const sql = `\n      UPDATE RULE_SUPERVISION\n      SET ${updateFields.join(', ')}\n      WHERE ID = :ruleId AND IS_DELETED = 0\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, params);\n    } catch (error) {\n        console.error('❌ 更新监管规则失败:', error);\n        throw new Error('更新监管规则失败');\n    }\n}\n/**\n * 删除监管规则（软删除）\n */ async function deleteSupervisionRule(ruleId, deletedBy) {\n    try {\n        const sql = `\n      UPDATE RULE_SUPERVISION\n      SET IS_DELETED = 1, UPDATED_BY = :deletedBy, UPDATED_AT = CURRENT_TIMESTAMP\n      WHERE ID = :ruleId AND IS_DELETED = 0\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId,\n            deletedBy\n        });\n    } catch (error) {\n        console.error('❌ 删除监管规则失败:', error);\n        throw new Error('删除监管规则失败');\n    }\n}\n/**\n * 启用/禁用监管规则\n */ async function toggleSupervisionRule(ruleId, isActive, updatedBy) {\n    try {\n        const sql = `\n      UPDATE RULE_SUPERVISION\n      SET IS_ACTIVE = :isActive, UPDATED_BY = :updatedBy, UPDATED_AT = CURRENT_TIMESTAMP\n      WHERE ID = :ruleId AND IS_DELETED = 0\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            ruleId,\n            isActive: isActive ? 1 : 0,\n            updatedBy\n        });\n    } catch (error) {\n        console.error('❌ 切换规则状态失败:', error);\n        throw new Error('切换规则状态失败');\n    }\n}\n/**\n * 执行监管规则\n */ async function executeSupervisionRules(executionRequest, executedBy) {\n    try {\n        const executionId = `EXEC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        for (const ruleId of executionRequest.ruleIds){\n            // 获取规则信息\n            const rule = await getSupervisionRuleById(ruleId);\n            if (!rule || !rule.isActive) {\n                console.warn(`规则 ${ruleId} 不存在或未启用，跳过执行`);\n                continue;\n            }\n            // 创建执行记录\n            const logId = await createExecutionLog(ruleId, executionId, executedBy, executionRequest.executionParams);\n            try {\n                // 更新执行记录为运行中\n                await updateExecutionLogStatus(logId, 'RUNNING');\n                // 执行规则逻辑\n                const executionResult = await executeRule(rule, executionRequest);\n                // 更新执行记录为成功\n                await updateExecutionLogStatus(logId, 'SUCCESS', executionResult);\n                // 更新规则执行统计\n                await updateRuleExecutionStats(ruleId, true);\n            } catch (error) {\n                console.error(`规则 ${ruleId} 执行失败:`, error);\n                // 更新执行记录为失败\n                await updateExecutionLogStatus(logId, 'FAILED', undefined, error instanceof Error ? error.message : String(error));\n                // 更新规则执行统计\n                await updateRuleExecutionStats(ruleId, false);\n            }\n        }\n        return executionId;\n    } catch (error) {\n        console.error('❌ 执行监管规则失败:', error);\n        throw new Error('执行监管规则失败');\n    }\n}\n/**\n * 创建执行记录\n */ async function createExecutionLog(ruleId, executionId, executedBy, executionParams) {\n    const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM RULE_EXECUTION_LOG`;\n    const nextIdResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(getNextIdSql);\n    const logId = nextIdResult.rows?.[0]?.NEXT_ID || 1;\n    const sql = `\n    INSERT INTO RULE_EXECUTION_LOG (\n      ID, RULE_ID, EXECUTION_ID, EXECUTION_STATUS, STARTED_AT,\n      PROCESSED_RECORD_COUNT, MATCHED_RECORD_COUNT, EXECUTION_PARAMS, EXECUTED_BY\n    ) VALUES (\n      :logId, :ruleId, :executionId, 'RUNNING', CURRENT_TIMESTAMP,\n      0, 0, :executionParams, :executedBy\n    )\n  `;\n    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n        logId,\n        ruleId,\n        executionId,\n        executionParams: executionParams ? JSON.stringify(executionParams) : null,\n        executedBy\n    });\n    return logId;\n}\n/**\n * 更新执行记录状态\n */ async function updateExecutionLogStatus(logId, status, result, errorMessage) {\n    const sql = `\n    UPDATE RULE_EXECUTION_LOG\n    SET EXECUTION_STATUS = :status,\n        ENDED_AT = CURRENT_TIMESTAMP,\n        EXECUTION_DURATION = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - STARTED_AT)) * 1000,\n        EXECUTION_RESULT = :result,\n        ERROR_MESSAGE = :errorMessage\n    WHERE ID = :logId\n  `;\n    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n        logId,\n        status,\n        result: result ? JSON.stringify(result) : null,\n        errorMessage\n    });\n}\n/**\n * 更新规则执行统计\n */ async function updateRuleExecutionStats(ruleId, isSuccess) {\n    const sql = `\n    UPDATE RULE_SUPERVISION\n    SET EXECUTION_COUNT = EXECUTION_COUNT + 1,\n        SUCCESS_COUNT = SUCCESS_COUNT + ${isSuccess ? 1 : 0},\n        LAST_EXECUTED_AT = CURRENT_TIMESTAMP\n    WHERE ID = :ruleId\n  `;\n    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n        ruleId\n    });\n}\n/**\n * 执行单个规则的核心逻辑\n */ async function executeRule(rule, executionRequest) {\n    // 这里是规则执行的核心逻辑\n    // 根据规则类型和内容执行不同的检查逻辑\n    const results = [];\n    if (rule.ruleSql) {\n        // 执行SQL规则\n        try {\n            const sqlResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(rule.ruleSql, executionRequest.executionParams || {});\n            // 处理SQL执行结果\n            if (sqlResult.rows && sqlResult.rows.length > 0) {\n                for (const row of sqlResult.rows){\n                    const result = {\n                        caseId: row.CASE_ID || null,\n                        resultType: determineResultType(row, rule),\n                        riskLevel: determineRiskLevel(row, rule),\n                        violationDescription: generateViolationDescription(row, rule),\n                        violationAmount: row.VIOLATION_AMOUNT || 0,\n                        evidenceData: JSON.stringify(row),\n                        confidenceScore: calculateConfidenceScore(row, rule)\n                    };\n                    results.push(result);\n                }\n            }\n        } catch (error) {\n            console.error('SQL规则执行失败:', error);\n            throw error;\n        }\n    } else if (rule.ruleDsl) {\n        // 执行DSL规则\n        // 这里可以实现DSL规则引擎\n        console.log('DSL规则执行暂未实现');\n    } else {\n        // 基于规则内容的简单规则执行\n        console.log('基于内容的规则执行');\n    }\n    return {\n        processedCount: results.length,\n        matchedCount: results.filter((r)=>r.resultType === 'VIOLATION' || r.resultType === 'SUSPICIOUS').length,\n        results\n    };\n}\n/**\n * 确定结果类型\n */ function determineResultType(row, rule) {\n    // 根据规则和数据确定结果类型\n    if (row.IS_VIOLATION === 1) return 'VIOLATION';\n    if (row.IS_SUSPICIOUS === 1) return 'SUSPICIOUS';\n    if (row.HAS_ERROR === 1) return 'ERROR';\n    return 'NORMAL';\n}\n/**\n * 确定风险等级\n */ function determineRiskLevel(row, rule) {\n    // 根据规则严重程度和数据确定风险等级\n    if (rule.severityLevel === 'CRITICAL') return 'CRITICAL';\n    if (rule.severityLevel === 'HIGH') return 'HIGH';\n    if (rule.severityLevel === 'MEDIUM') return 'MEDIUM';\n    return 'LOW';\n}\n/**\n * 生成违规描述\n */ function generateViolationDescription(row, rule) {\n    return `规则 ${rule.ruleName} 检测到异常：${row.VIOLATION_REASON || '未知原因'}`;\n}\n/**\n * 计算置信度分数\n */ function calculateConfidenceScore(row, rule) {\n    // 根据数据质量和规则匹配度计算置信度\n    return row.CONFIDENCE_SCORE || 85;\n}\n/**\n * 获取监管规则统计信息\n */ async function getSupervisionRuleStatistics() {\n    try {\n        // 基础统计\n        const basicStatsSql = `\n      SELECT\n        COUNT(*) as TOTAL_RULES,\n        SUM(CASE WHEN IS_ACTIVE = 1 THEN 1 ELSE 0 END) as ACTIVE_RULES,\n        SUM(CASE WHEN IS_ACTIVE = 0 THEN 1 ELSE 0 END) as INACTIVE_RULES\n      FROM RULE_SUPERVISION\n      WHERE IS_DELETED = 0\n    `;\n        const basicStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(basicStatsSql);\n        const basicStats = basicStatsResult.rows?.[0] || {};\n        // 按类型统计\n        const typeStatsSql = `\n      SELECT RULE_TYPE, COUNT(*) as COUNT\n      FROM RULE_SUPERVISION\n      WHERE IS_DELETED = 0\n      GROUP BY RULE_TYPE\n    `;\n        const typeStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(typeStatsSql);\n        const rulesByType = {\n            'SQL': 0,\n            'DSL': 0,\n            'JAVASCRIPT': 0\n        };\n        typeStatsResult.rows?.forEach((row)=>{\n            rulesByType[row.RULE_TYPE] = row.COUNT;\n        });\n        // 按分类统计\n        const categoryStatsSql = `\n      SELECT RULE_CATEGORY, COUNT(*) as COUNT\n      FROM RULE_SUPERVISION\n      WHERE IS_DELETED = 0\n      GROUP BY RULE_CATEGORY\n    `;\n        const categoryStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(categoryStatsSql);\n        const rulesByCategory = {\n            'COST_CONTROL': 0,\n            'FRAUD_DETECTION': 0,\n            'COMPLIANCE_CHECK': 0,\n            'QUALITY_ASSURANCE': 0,\n            'STATISTICAL_ANALYSIS': 0\n        };\n        categoryStatsResult.rows?.forEach((row)=>{\n            rulesByCategory[row.RULE_CATEGORY] = row.COUNT;\n        });\n        // 按严重程度统计\n        const severityStatsSql = `\n      SELECT SEVERITY_LEVEL, COUNT(*) as COUNT\n      FROM RULE_SUPERVISION\n      WHERE IS_DELETED = 0\n      GROUP BY SEVERITY_LEVEL\n    `;\n        const severityStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(severityStatsSql);\n        const rulesBySeverity = {\n            'LOW': 0,\n            'MEDIUM': 0,\n            'HIGH': 0,\n            'CRITICAL': 0\n        };\n        severityStatsResult.rows?.forEach((row)=>{\n            rulesBySeverity[row.SEVERITY_LEVEL] = row.COUNT;\n        });\n        // 执行统计\n        const executionStatsSql = `\n      SELECT\n        COUNT(*) as TOTAL_EXECUTIONS,\n        SUM(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 ELSE 0 END) as SUCCESSFUL_EXECUTIONS,\n        SUM(CASE WHEN EXECUTION_STATUS = 'FAILED' THEN 1 ELSE 0 END) as FAILED_EXECUTIONS,\n        AVG(EXECUTION_DURATION) as AVG_EXECUTION_TIME\n      FROM RULE_EXECUTION_LOG\n      WHERE CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -3)\n    `;\n        const executionStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(executionStatsSql);\n        const executionStats = executionStatsResult.rows?.[0] || {};\n        // 结果统计\n        const resultStatsSql = `\n      SELECT\n        COUNT(*) as TOTAL_RESULTS,\n        SUM(CASE WHEN RESULT_TYPE = 'VIOLATION' THEN 1 ELSE 0 END) as VIOLATION_RESULTS,\n        SUM(CASE WHEN RESULT_TYPE = 'SUSPICIOUS' THEN 1 ELSE 0 END) as SUSPICIOUS_RESULTS,\n        SUM(CASE WHEN RESULT_TYPE = 'NORMAL' THEN 1 ELSE 0 END) as NORMAL_RESULTS,\n        AVG(CONFIDENCE_SCORE) as AVG_CONFIDENCE_SCORE\n      FROM RULE_EXECUTION_RESULT\n      WHERE CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -3)\n    `;\n        const resultStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(resultStatsSql);\n        const resultStats = resultStatsResult.rows?.[0] || {};\n        // 月度趋势\n        const monthlyTrendSql = `\n      SELECT\n        TO_CHAR(rel.CREATED_AT, 'YYYY-MM') as MONTH,\n        COUNT(DISTINCT rel.ID) as EXECUTION_COUNT,\n        COUNT(DISTINCT rer.ID) as VIOLATION_COUNT,\n        AVG(rer.CONFIDENCE_SCORE) as AVG_CONFIDENCE_SCORE\n      FROM RULE_EXECUTION_LOG rel\n      LEFT JOIN RULE_EXECUTION_RESULT rer ON rel.ID = rer.EXECUTION_LOG_ID AND rer.RESULT_TYPE = 'VIOLATION'\n      WHERE rel.CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -12)\n      GROUP BY TO_CHAR(rel.CREATED_AT, 'YYYY-MM')\n      ORDER BY MONTH\n    `;\n        const monthlyTrendResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(monthlyTrendSql);\n        const monthlyTrend = (monthlyTrendResult.rows || []).map((row)=>({\n                month: row.MONTH,\n                executionCount: row.EXECUTION_COUNT,\n                violationCount: row.VIOLATION_COUNT,\n                avgConfidenceScore: row.AVG_CONFIDENCE_SCORE || 0\n            }));\n        return {\n            totalRules: basicStats.TOTAL_RULES || 0,\n            activeRules: basicStats.ACTIVE_RULES || 0,\n            inactiveRules: basicStats.INACTIVE_RULES || 0,\n            rulesByType,\n            rulesByCategory,\n            rulesBySeverity,\n            executionStats: {\n                totalExecutions: executionStats.TOTAL_EXECUTIONS || 0,\n                successfulExecutions: executionStats.SUCCESSFUL_EXECUTIONS || 0,\n                failedExecutions: executionStats.FAILED_EXECUTIONS || 0,\n                avgExecutionTime: executionStats.AVG_EXECUTION_TIME || 0\n            },\n            resultStats: {\n                totalResults: resultStats.TOTAL_RESULTS || 0,\n                violationResults: resultStats.VIOLATION_RESULTS || 0,\n                suspiciousResults: resultStats.SUSPICIOUS_RESULTS || 0,\n                normalResults: resultStats.NORMAL_RESULTS || 0,\n                avgConfidenceScore: resultStats.AVG_CONFIDENCE_SCORE || 0\n            },\n            monthlyTrend\n        };\n    } catch (error) {\n        console.error('❌ 获取监管规则统计失败:', error);\n        throw new Error('获取监管规则统计失败');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supervision-rule-service.ts\n");

/***/ })

};
;