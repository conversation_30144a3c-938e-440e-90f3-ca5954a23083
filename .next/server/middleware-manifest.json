{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fGup0PO52abzRKVcxxQws3K8c4KSlHrEa8BBPj8sR84=", "__NEXT_PREVIEW_MODE_ID": "9426dde46679d5e5accc9fa9e4d61426", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "30b26b1683d6e9a5207b1193c4c814b62930af851a4212b8cc44e056d576d671", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f6a6a035f58272b0b3b3055ec72d8de2fb4bb8c40188c56cdc27ca8f4b6c64a0"}}}, "functions": {}, "sortedMiddleware": ["/"]}