{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fGup0PO52abzRKVcxxQws3K8c4KSlHrEa8BBPj8sR84=", "__NEXT_PREVIEW_MODE_ID": "da12bd1396ccb2159c5182b627b958e9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6be284315c9e11b030835ce1098411a0c965a886758237aa008007841050b87f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c3b3b3ddad327fa7a911128278b533510a7b915162427027fea14badefddbf36"}}}, "functions": {}, "sortedMiddleware": ["/"]}