{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fGup0PO52abzRKVcxxQws3K8c4KSlHrEa8BBPj8sR84=", "__NEXT_PREVIEW_MODE_ID": "12033b99d3db2c40e446972f43573714", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c7ef7079c42879b5d70f571dc037c4d44a7e7c7638677da51e024905ad756cbf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0e06e756281b40e5c4a860bbcb0a30e1e344f15cbdbd789dceacaa96202d2465"}}}, "functions": {}, "sortedMiddleware": ["/"]}