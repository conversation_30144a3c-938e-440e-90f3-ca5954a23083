{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fGup0PO52abzRKVcxxQws3K8c4KSlHrEa8BBPj8sR84=", "__NEXT_PREVIEW_MODE_ID": "aa65378a661085dccaab90eb6f9653ad", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "34cb2b9b59a2fd8093b8ab2f94af0e49e79272db6d1fbff17b1d1665e706ec48", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5c43ee9a1b11645d0fa7523e2721b592b9a80faaf6fcac0c18d02b76865cd58d"}}}, "functions": {}, "sortedMiddleware": ["/"]}