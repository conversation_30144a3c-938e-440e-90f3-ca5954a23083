/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/dist/bcrypt.js":
/*!**********************************************!*\
  !*** ./node_modules/bcryptjs/dist/bcrypt.js ***!
  \**********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\nvar __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/*\r\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\r\n Copyright (c) 2012 Shane Girish <<EMAIL>>\r\n Copyright (c) 2014 Daniel Wirtz <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\n/**\r\n * @license bcrypt.js (c) 2013 Daniel Wirtz <<EMAIL>>\r\n * Released under the Apache License, Version 2.0\r\n * see: https://github.com/dcodeIO/bcrypt.js for details\r\n */\r\n(function(global, factory) {\r\n\r\n    /* AMD */ if (true)\r\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    /* CommonJS */ else {}\r\n\r\n}(this, function() {\r\n    \"use strict\";\r\n\r\n    /**\r\n     * bcrypt namespace.\r\n     * @type {Object.<string,*>}\r\n     */\r\n    var bcrypt = {};\r\n\r\n    /**\r\n     * The random implementation to use as a fallback.\r\n     * @type {?function(number):!Array.<number>}\r\n     * @inner\r\n     */\r\n    var randomFallback = null;\r\n\r\n    /**\r\n     * Generates cryptographically secure random bytes.\r\n     * @function\r\n     * @param {number} len Bytes length\r\n     * @returns {!Array.<number>} Random bytes\r\n     * @throws {Error} If no random implementation is available\r\n     * @inner\r\n     */\r\n    function random(len) {\r\n        /* node */ if ( true && module && module['exports'])\r\n            try {\r\n                return (__webpack_require__(/*! crypto */ \"crypto\").randomBytes)(len);\r\n            } catch (e) {}\r\n        /* WCA */ try {\r\n            var a; (self['crypto']||self['msCrypto'])['getRandomValues'](a = new Uint32Array(len));\r\n            return Array.prototype.slice.call(a);\r\n        } catch (e) {}\r\n        /* fallback */ if (!randomFallback)\r\n            throw Error(\"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\");\r\n        return randomFallback(len);\r\n    }\r\n\r\n    // Test if any secure randomness source is available\r\n    var randomAvailable = false;\r\n    try {\r\n        random(1);\r\n        randomAvailable = true;\r\n    } catch (e) {}\r\n\r\n    // Default fallback, if any\r\n    randomFallback = null;\r\n    /**\r\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\r\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\r\n     *  is seeded properly!\r\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\r\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\r\n     * @see http://nodejs.org/api/crypto.html\r\n     * @see http://www.w3.org/TR/WebCryptoAPI/\r\n     */\r\n    bcrypt.setRandomFallback = function(random) {\r\n        randomFallback = random;\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a salt.\r\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {number=} seed_length Not supported.\r\n     * @returns {string} Resulting salt\r\n     * @throws {Error} If a random fallback is required but not set\r\n     * @expose\r\n     */\r\n    bcrypt.genSaltSync = function(rounds, seed_length) {\r\n        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof rounds !== 'number')\r\n            throw Error(\"Illegal arguments: \"+(typeof rounds)+\", \"+(typeof seed_length));\r\n        if (rounds < 4)\r\n            rounds = 4;\r\n        else if (rounds > 31)\r\n            rounds = 31;\r\n        var salt = [];\r\n        salt.push(\"$2a$\");\r\n        if (rounds < 10)\r\n            salt.push(\"0\");\r\n        salt.push(rounds.toString());\r\n        salt.push('$');\r\n        salt.push(base64_encode(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\r\n        return salt.join('');\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a salt.\r\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\r\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.genSalt = function(rounds, seed_length, callback) {\r\n        if (typeof seed_length === 'function')\r\n            callback = seed_length,\r\n            seed_length = undefined; // Not supported.\r\n        if (typeof rounds === 'function')\r\n            callback = rounds,\r\n            rounds = undefined;\r\n        if (typeof rounds === 'undefined')\r\n            rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        else if (typeof rounds !== 'number')\r\n            throw Error(\"illegal arguments: \"+(typeof rounds));\r\n\r\n        function _async(callback) {\r\n            nextTick(function() { // Pretty thin, but salting is fast enough\r\n                try {\r\n                    callback(null, bcrypt.genSaltSync(rounds));\r\n                } catch (err) {\r\n                    callback(err);\r\n                }\r\n            });\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Synchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\r\n     * @returns {string} Resulting hash\r\n     * @expose\r\n     */\r\n    bcrypt.hashSync = function(s, salt) {\r\n        if (typeof salt === 'undefined')\r\n            salt = GENSALT_DEFAULT_LOG2_ROUNDS;\r\n        if (typeof salt === 'number')\r\n            salt = bcrypt.genSaltSync(salt);\r\n        if (typeof s !== 'string' || typeof salt !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt));\r\n        return _hash(s, salt);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously generates a hash for the given string.\r\n     * @param {string} s String to hash\r\n     * @param {number|string} salt Salt length to generate or salt to use\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.hash = function(s, salt, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s === 'string' && typeof salt === 'number')\r\n                bcrypt.genSalt(salt, function(err, salt) {\r\n                    _hash(s, salt, callback, progressCallback);\r\n                });\r\n            else if (typeof s === 'string' && typeof salt === 'string')\r\n                _hash(s, salt, callback, progressCallback);\r\n            else\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof salt))));\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Compares two strings of the same length in constant time.\r\n     * @param {string} known Must be of the correct length\r\n     * @param {string} unknown Must be the same length as `known`\r\n     * @returns {boolean}\r\n     * @inner\r\n     */\r\n    function safeStringCompare(known, unknown) {\r\n        var right = 0,\r\n            wrong = 0;\r\n        for (var i=0, k=known.length; i<k; ++i) {\r\n            if (known.charCodeAt(i) === unknown.charCodeAt(i))\r\n                ++right;\r\n            else\r\n                ++wrong;\r\n        }\r\n        // Prevent removal of unused variables (never true, actually)\r\n        if (right < 0)\r\n            return false;\r\n        return wrong === 0;\r\n    }\r\n\r\n    /**\r\n     * Synchronously tests a string against a hash.\r\n     * @param {string} s String to compare\r\n     * @param {string} hash Hash to test against\r\n     * @returns {boolean} true if matching, otherwise false\r\n     * @throws {Error} If an argument is illegal\r\n     * @expose\r\n     */\r\n    bcrypt.compareSync = function(s, hash) {\r\n        if (typeof s !== \"string\" || typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash));\r\n        if (hash.length !== 60)\r\n            return false;\r\n        return safeStringCompare(bcrypt.hashSync(s, hash.substr(0, hash.length-31)), hash);\r\n    };\r\n\r\n    /**\r\n     * Asynchronously compares the given data against the given hash.\r\n     * @param {string} s Data to compare\r\n     * @param {string} hash Data to be compared to\r\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\r\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\r\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\r\n     * @returns {!Promise} If `callback` has been omitted\r\n     * @throws {Error} If `callback` is present but not a function\r\n     * @expose\r\n     */\r\n    bcrypt.compare = function(s, hash, callback, progressCallback) {\r\n\r\n        function _async(callback) {\r\n            if (typeof s !== \"string\" || typeof hash !== \"string\") {\r\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \"+(typeof s)+', '+(typeof hash))));\r\n                return;\r\n            }\r\n            if (hash.length !== 60) {\r\n                nextTick(callback.bind(this, null, false));\r\n                return;\r\n            }\r\n            bcrypt.hash(s, hash.substr(0, 29), function(err, comp) {\r\n                if (err)\r\n                    callback(err);\r\n                else\r\n                    callback(null, safeStringCompare(comp, hash));\r\n            }, progressCallback);\r\n        }\r\n\r\n        if (callback) {\r\n            if (typeof callback !== 'function')\r\n                throw Error(\"Illegal callback: \"+typeof(callback));\r\n            _async(callback);\r\n        } else\r\n            return new Promise(function(resolve, reject) {\r\n                _async(function(err, res) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        return;\r\n                    }\r\n                    resolve(res);\r\n                });\r\n            });\r\n    };\r\n\r\n    /**\r\n     * Gets the number of rounds used to encrypt the specified hash.\r\n     * @param {string} hash Hash to extract the used number of rounds from\r\n     * @returns {number} Number of rounds used\r\n     * @throws {Error} If `hash` is not a string\r\n     * @expose\r\n     */\r\n    bcrypt.getRounds = function(hash) {\r\n        if (typeof hash !== \"string\")\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        return parseInt(hash.split(\"$\")[2], 10);\r\n    };\r\n\r\n    /**\r\n     * Gets the salt portion from a hash. Does not validate the hash.\r\n     * @param {string} hash Hash to extract the salt from\r\n     * @returns {string} Extracted salt part\r\n     * @throws {Error} If `hash` is not a string or otherwise invalid\r\n     * @expose\r\n     */\r\n    bcrypt.getSalt = function(hash) {\r\n        if (typeof hash !== 'string')\r\n            throw Error(\"Illegal arguments: \"+(typeof hash));\r\n        if (hash.length !== 60)\r\n            throw Error(\"Illegal hash length: \"+hash.length+\" != 60\");\r\n        return hash.substring(0, 29);\r\n    };\r\n\r\n    /**\r\n     * Continues with the callback on the next tick.\r\n     * @function\r\n     * @param {function(...[*])} callback Callback to execute\r\n     * @inner\r\n     */\r\n    var nextTick = typeof process !== 'undefined' && process && typeof process.nextTick === 'function'\r\n        ? (typeof setImmediate === 'function' ? setImmediate : process.nextTick)\r\n        : setTimeout;\r\n\r\n    /**\r\n     * Converts a JavaScript string to UTF8 bytes.\r\n     * @param {string} str String\r\n     * @returns {!Array.<number>} UTF8 bytes\r\n     * @inner\r\n     */\r\n    function stringToBytes(str) {\r\n        var out = [],\r\n            i = 0;\r\n        utfx.encodeUTF16toUTF8(function() {\r\n            if (i >= str.length) return null;\r\n            return str.charCodeAt(i++);\r\n        }, function(b) {\r\n            out.push(b);\r\n        });\r\n        return out;\r\n    }\r\n\r\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\r\n\r\n    /**\r\n     * bcrypt's own non-standard base64 dictionary.\r\n     * @type {!Array.<string>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_CODE = \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split('');\r\n\r\n    /**\r\n     * @type {!Array.<number>}\r\n     * @const\r\n     * @inner\r\n     **/\r\n    var BASE64_INDEX = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\r\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0,\r\n        1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, -1, -1, -1, -1, -1, -1,\r\n        -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,\r\n        20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28, 29, 30,\r\n        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\r\n        48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1];\r\n\r\n    /**\r\n     * @type {!function(...number):string}\r\n     * @inner\r\n     */\r\n    var stringFromCharCode = String.fromCharCode;\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input.\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @inner\r\n     */\r\n    function base64_encode(b, len) {\r\n        var off = 0,\r\n            rs = [],\r\n            c1, c2;\r\n        if (len <= 0 || len > b.length)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < len) {\r\n            c1 = b[off++] & 0xff;\r\n            rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\r\n            c1 = (c1 & 0x03) << 4;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 4) & 0x0f;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            c1 = (c2 & 0x0f) << 2;\r\n            if (off >= len) {\r\n                rs.push(BASE64_CODE[c1 & 0x3f]);\r\n                break;\r\n            }\r\n            c2 = b[off++] & 0xff;\r\n            c1 |= (c2 >> 6) & 0x03;\r\n            rs.push(BASE64_CODE[c1 & 0x3f]);\r\n            rs.push(BASE64_CODE[c2 & 0x3f]);\r\n        }\r\n        return rs.join('');\r\n    }\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output.\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @inner\r\n     */\r\n    function base64_decode(s, len) {\r\n        var off = 0,\r\n            slen = s.length,\r\n            olen = 0,\r\n            rs = [],\r\n            c1, c2, c3, c4, o, code;\r\n        if (len <= 0)\r\n            throw Error(\"Illegal len: \"+len);\r\n        while (off < slen - 1 && olen < len) {\r\n            code = s.charCodeAt(off++);\r\n            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            code = s.charCodeAt(off++);\r\n            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c1 == -1 || c2 == -1)\r\n                break;\r\n            o = (c1 << 2) >>> 0;\r\n            o |= (c2 & 0x30) >> 4;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            if (c3 == -1)\r\n                break;\r\n            o = ((c2 & 0x0f) << 4) >>> 0;\r\n            o |= (c3 & 0x3c) >> 2;\r\n            rs.push(stringFromCharCode(o));\r\n            if (++olen >= len || off >= slen)\r\n                break;\r\n            code = s.charCodeAt(off++);\r\n            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\r\n            o = ((c3 & 0x03) << 6) >>> 0;\r\n            o |= c4;\r\n            rs.push(stringFromCharCode(o));\r\n            ++olen;\r\n        }\r\n        var res = [];\r\n        for (off = 0; off<olen; off++)\r\n            res.push(rs[off].charCodeAt(0));\r\n        return res;\r\n    }\r\n\r\n    /**\r\n     * utfx-embeddable (c) 2014 Daniel Wirtz <<EMAIL>>\r\n     * Released under the Apache License, Version 2.0\r\n     * see: https://github.com/dcodeIO/utfx for details\r\n     */\r\n    var utfx = function() {\r\n        \"use strict\";\r\n\r\n        /**\r\n         * utfx namespace.\r\n         * @inner\r\n         * @type {!Object.<string,*>}\r\n         */\r\n        var utfx = {};\r\n\r\n        /**\r\n         * Maximum valid code point.\r\n         * @type {number}\r\n         * @const\r\n         */\r\n        utfx.MAX_CODEPOINT = 0x10FFFF;\r\n\r\n        /**\r\n         * Encodes UTF8 code points to UTF8 bytes.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte\r\n         */\r\n        utfx.encodeUTF8 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src,\r\n                src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp < 0x80)\r\n                    dst(cp&0x7F);\r\n                else if (cp < 0x800)\r\n                    dst(((cp>>6)&0x1F)|0xC0),\r\n                    dst((cp&0x3F)|0x80);\r\n                else if (cp < 0x10000)\r\n                    dst(((cp>>12)&0x0F)|0xE0),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                else\r\n                    dst(((cp>>18)&0x07)|0xF0),\r\n                    dst(((cp>>12)&0x3F)|0x80),\r\n                    dst(((cp>>6)&0x3F)|0x80),\r\n                    dst((cp&0x3F)|0x80);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Decodes UTF8 bytes to UTF8 code points.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each decoded code point.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\r\n         *  remaining bytes.\r\n         */\r\n        utfx.decodeUTF8 = function(src, dst) {\r\n            var a, b, c, d, fail = function(b) {\r\n                b = b.slice(0, b.indexOf(null));\r\n                var err = Error(b.toString());\r\n                err.name = \"TruncatedError\";\r\n                err['bytes'] = b;\r\n                throw err;\r\n            };\r\n            while ((a = src()) !== null) {\r\n                if ((a&0x80) === 0)\r\n                    dst(a);\r\n                else if ((a&0xE0) === 0xC0)\r\n                    ((b = src()) === null) && fail([a, b]),\r\n                    dst(((a&0x1F)<<6) | (b&0x3F));\r\n                else if ((a&0xF0) === 0xE0)\r\n                    ((b=src()) === null || (c=src()) === null) && fail([a, b, c]),\r\n                    dst(((a&0x0F)<<12) | ((b&0x3F)<<6) | (c&0x3F));\r\n                else if ((a&0xF8) === 0xF0)\r\n                    ((b=src()) === null || (c=src()) === null || (d=src()) === null) && fail([a, b, c ,d]),\r\n                    dst(((a&0x07)<<18) | ((b&0x3F)<<12) | ((c&0x3F)<<6) | (d&0x3F));\r\n                else throw RangeError(\"Illegal starting byte: \"+a);\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts UTF16 characters to UTF8 code points.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @param {!function(number)} dst Code points destination as a function successively called with each converted code\r\n         *  point.\r\n         */\r\n        utfx.UTF16toUTF8 = function(src, dst) {\r\n            var c1, c2 = null;\r\n            while (true) {\r\n                if ((c1 = c2 !== null ? c2 : src()) === null)\r\n                    break;\r\n                if (c1 >= 0xD800 && c1 <= 0xDFFF) {\r\n                    if ((c2 = src()) !== null) {\r\n                        if (c2 >= 0xDC00 && c2 <= 0xDFFF) {\r\n                            dst((c1-0xD800)*0x400+c2-0xDC00+0x10000);\r\n                            c2 = null; continue;\r\n                        }\r\n                    }\r\n                }\r\n                dst(c1);\r\n            }\r\n            if (c2 !== null) dst(c2);\r\n        };\r\n\r\n        /**\r\n         * Converts UTF8 code points to UTF16 characters.\r\n         * @param {(!function():number|null) | number} src Code points source, either as a function returning the next code point\r\n         *  respectively `null` if there are no more code points left or a single numeric code point.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a code point is out of range\r\n         */\r\n        utfx.UTF8toUTF16 = function(src, dst) {\r\n            var cp = null;\r\n            if (typeof src === 'number')\r\n                cp = src, src = function() { return null; };\r\n            while (cp !== null || (cp = src()) !== null) {\r\n                if (cp <= 0xFFFF)\r\n                    dst(cp);\r\n                else\r\n                    cp -= 0x10000,\r\n                    dst((cp>>10)+0xD800),\r\n                    dst((cp%0x400)+0xDC00);\r\n                cp = null;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Converts and encodes UTF16 characters to UTF8 bytes.\r\n         * @param {!function():number|null} src Characters source as a function returning the next char code respectively `null`\r\n         *  if there are no more characters left.\r\n         * @param {!function(number)} dst Bytes destination as a function successively called with the next byte.\r\n         */\r\n        utfx.encodeUTF16toUTF8 = function(src, dst) {\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                utfx.encodeUTF8(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Decodes and converts UTF8 bytes to UTF16 characters.\r\n         * @param {!function():number|null} src Bytes source as a function returning the next byte respectively `null` if there\r\n         *  are no more bytes left.\r\n         * @param {!function(number)} dst Characters destination as a function successively called with each converted char code.\r\n         * @throws {RangeError} If a starting byte is invalid in UTF8\r\n         * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\r\n         */\r\n        utfx.decodeUTF8toUTF16 = function(src, dst) {\r\n            utfx.decodeUTF8(src, function(cp) {\r\n                utfx.UTF8toUTF16(cp, dst);\r\n            });\r\n        };\r\n\r\n        /**\r\n         * Calculates the byte length of an UTF8 code point.\r\n         * @param {number} cp UTF8 code point\r\n         * @returns {number} Byte length\r\n         */\r\n        utfx.calculateCodePoint = function(cp) {\r\n            return (cp < 0x80) ? 1 : (cp < 0x800) ? 2 : (cp < 0x10000) ? 3 : 4;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 bytes required to store UTF8 code points.\r\n         * @param {(!function():number|null)} src Code points source as a function returning the next code point respectively\r\n         *  `null` if there are no more code points left.\r\n         * @returns {number} The number of UTF8 bytes required\r\n         */\r\n        utfx.calculateUTF8 = function(src) {\r\n            var cp, l=0;\r\n            while ((cp = src()) !== null)\r\n                l += utfx.calculateCodePoint(cp);\r\n            return l;\r\n        };\r\n\r\n        /**\r\n         * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\r\n         * @param {(!function():number|null)} src Characters source as a function returning the next char code respectively\r\n         *  `null` if there are no more characters left.\r\n         * @returns {!Array.<number>} The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\r\n         */\r\n        utfx.calculateUTF16asUTF8 = function(src) {\r\n            var n=0, l=0;\r\n            utfx.UTF16toUTF8(src, function(cp) {\r\n                ++n; l += utfx.calculateCodePoint(cp);\r\n            });\r\n            return [n,l];\r\n        };\r\n\r\n        return utfx;\r\n    }();\r\n\r\n    Date.now = Date.now || function() { return +new Date; };\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BCRYPT_SALT_LEN = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var BLOWFISH_NUM_ROUNDS = 16;\r\n\r\n    /**\r\n     * @type {number}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var MAX_EXECUTION_TIME = 100;\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var P_ORIG = [\r\n        0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822,\r\n        0x299f31d0, 0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377,\r\n        0xbe5466cf, 0x34e90c6c, 0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5,\r\n        0xb5470917, 0x9216d5d9, 0x8979fb1b\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var S_ORIG = [\r\n        0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed,\r\n        0x6a267e96, 0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7,\r\n        0x0801f2e2, 0x858efc16, 0x636920d8, 0x71574e69, 0xa458fea3,\r\n        0xf4933d7e, 0x0d95748f, 0x728eb658, 0x718bcd58, 0x82154aee,\r\n        0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013, 0xc5d1b023,\r\n        0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\r\n        0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda,\r\n        0x55605c60, 0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440,\r\n        0x55ca396a, 0x2aab10b6, 0xb4cc5c34, 0x1141e8ce, 0xa15486af,\r\n        0x7c72e993, 0xb3ee1411, 0x636fbc2a, 0x2ba9c55d, 0x741831f6,\r\n        0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c, 0x7a325381,\r\n        0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\r\n        0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d,\r\n        0xe98575b1, 0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5,\r\n        0x0f6d6ff3, 0x83f44239, 0x2e0b4482, 0xa4842004, 0x69c8f04a,\r\n        0x9e1f9b5e, 0x21c66842, 0xf6e96c9a, 0x670c9c61, 0xabd388f0,\r\n        0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3, 0x6eef0b6c,\r\n        0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\r\n        0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3,\r\n        0x3b8b5ebe, 0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6,\r\n        0x4ed3aa62, 0x363f7706, 0x1bfedf72, 0x429b023d, 0x37d0d724,\r\n        0xd00a1248, 0xdb0fead3, 0x49f1c09b, 0x075372c9, 0x80991b7b,\r\n        0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b, 0x976ce0bd,\r\n        0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\r\n        0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f,\r\n        0x9b30952c, 0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd,\r\n        0x660f2807, 0x192e4bb3, 0xc0cba857, 0x45c8740f, 0xd20b5f39,\r\n        0xb9d3fbdb, 0x5579c0bd, 0x1a60320a, 0xd6a100c6, 0x402c7279,\r\n        0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8, 0x3c7516df,\r\n        0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\r\n        0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e,\r\n        0xdf1769db, 0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573,\r\n        0x695b27b0, 0xbbca58c8, 0xe1ffa35d, 0xb8f011a0, 0x10fa3d98,\r\n        0xfd2183b8, 0x4afcb56c, 0x2dd1d35b, 0x9a53e479, 0xb6f84565,\r\n        0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33, 0x62fb1341,\r\n        0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\r\n        0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0,\r\n        0xafc725e0, 0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64,\r\n        0x8888b812, 0x900df01c, 0x4fad5ea0, 0x688fc31c, 0xd1cff191,\r\n        0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777, 0xea752dfe, 0x8b021fa1,\r\n        0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299, 0xb4a84fe0,\r\n        0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\r\n        0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5,\r\n        0xfb9d35cf, 0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49,\r\n        0x00250e2d, 0x2071b35e, 0x226800bb, 0x57b8e0af, 0x2464369b,\r\n        0xf009b91e, 0x5563911d, 0x59dfa6aa, 0x78c14389, 0xd95a537f,\r\n        0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9, 0x11c81968,\r\n        0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\r\n        0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5,\r\n        0x571be91f, 0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6,\r\n        0xff34052e, 0xc5855664, 0x53b02d5d, 0xa99f8fa1, 0x08ba4799,\r\n        0x6e85076a, 0x4b7a70e9, 0xb5b32944, 0xdb75092e, 0xc4192623,\r\n        0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266, 0xecaa8c71,\r\n        0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\r\n        0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6,\r\n        0x99f73fd6, 0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1,\r\n        0x4cdd2086, 0x8470eb26, 0x6382e9c6, 0x021ecc5e, 0x09686b3f,\r\n        0x3ebaefc9, 0x3c971814, 0x6b6a70a1, 0x687f3584, 0x52a0e286,\r\n        0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c, 0x8e7d44ec,\r\n        0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\r\n        0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9,\r\n        0x7ca92ff6, 0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc,\r\n        0xc8b57634, 0x9af3dda7, 0xa9446146, 0x0fd0030e, 0xecc8c73e,\r\n        0xa4751e41, 0xe238cd99, 0x3bea0e2f, 0x3280bba1, 0x183eb331,\r\n        0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf, 0x2cb81290,\r\n        0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\r\n        0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6,\r\n        0x9f84cd87, 0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c,\r\n        0xec7aec3a, 0xdb851dfa, 0x63094366, 0xc464c3d2, 0xef1c1847,\r\n        0x3215d908, 0xdd433b37, 0x24c2ba16, 0x12a14d43, 0x2a65c451,\r\n        0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55, 0x81ac77d6,\r\n        0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\r\n        0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570,\r\n        0xeae96fb1, 0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa,\r\n        0x2965dcb9, 0x99e71d0f, 0x803e89d6, 0x5266c825, 0x2e4cc978,\r\n        0x9c10b36a, 0xc6150eba, 0x94e2ea78, 0xa5fc3c53, 0x1e0a2df4,\r\n        0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960, 0x5223a708,\r\n        0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\r\n        0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185,\r\n        0x68ab9802, 0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84,\r\n        0x1521b628, 0x29076170, 0xecdd4775, 0x619f1510, 0x13cca830,\r\n        0xeb61bd96, 0x0334fe1e, 0xaa0363cf, 0xb5735c90, 0x4c70a239,\r\n        0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7, 0x9cab5cab,\r\n        0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\r\n        0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19,\r\n        0x875fa099, 0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77,\r\n        0x11ed935f, 0x16681281, 0x0e358829, 0xc7e61fd6, 0x96dedfa1,\r\n        0x7858ba99, 0x57f584a5, 0x1b227263, 0x9b83c3ff, 0x1ac24696,\r\n        0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128, 0x58ebf2ef,\r\n        0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\r\n        0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15,\r\n        0xfacb4fd0, 0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105,\r\n        0xd81e799e, 0x86854dc7, 0xe44b476a, 0x3d816250, 0xcf62a1f2,\r\n        0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3, 0x7f1524c3, 0x69cb7492,\r\n        0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d, 0x1462b174,\r\n        0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\r\n        0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759,\r\n        0xcbee7460, 0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e,\r\n        0xe8efd855, 0x61d99735, 0xa969a7aa, 0xc50c06c2, 0x5a04abfc,\r\n        0x800bcadc, 0x9e447a2e, 0xc3453484, 0xfdd56705, 0x0e1e9ec9,\r\n        0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340, 0xc5c43465,\r\n        0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\r\n        0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c,\r\n        0x94692934, 0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068,\r\n        0xd4082471, 0x3320f46a, 0x43b7d4b7, 0x500061af, 0x1e39f62e,\r\n        0x97244546, 0x14214f74, 0xbf8b8840, 0x4d95fc1d, 0x96b591af,\r\n        0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785, 0x7fac6dd0,\r\n        0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\r\n        0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462,\r\n        0xd7486900, 0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c,\r\n        0xb58ce006, 0x7af4d6b6, 0xaace1e7c, 0xd3375fec, 0xce78a399,\r\n        0x406b2a42, 0x20fe9e35, 0xd9f385b9, 0xee39d7ab, 0x3b124e8b,\r\n        0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2, 0x3a6efa74,\r\n        0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\r\n        0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7,\r\n        0xd096954b, 0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33,\r\n        0xa62a4a56, 0x3f3125f9, 0x5ef47e1c, 0x9029317c, 0xfdf8e802,\r\n        0x04272f70, 0x80bb155c, 0x05282ce3, 0x95c11548, 0xe4c66d22,\r\n        0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f, 0x404779a4,\r\n        0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\r\n        0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2,\r\n        0x02e1329e, 0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1,\r\n        0x3b240b62, 0xeebeb922, 0x85b2a20e, 0xe6ba0d99, 0xde720c8c,\r\n        0x2da2f728, 0xd0127845, 0x95b794fd, 0x647d0862, 0xe7ccf5f0,\r\n        0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e, 0x0a476341,\r\n        0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\r\n        0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b,\r\n        0xdcd0e804, 0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b,\r\n        0x667b9ffb, 0xcedb7d9c, 0xa091cf0b, 0xd9155ea3, 0xbb132f88,\r\n        0x515bad24, 0x7b9479bf, 0x763bd6eb, 0x37392eb3, 0xcc115979,\r\n        0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b, 0x12754ccc,\r\n        0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\r\n        0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659,\r\n        0x0a121386, 0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f,\r\n        0xbebfe988, 0x64e4c3fe, 0x9dbc8057, 0xf0f7c086, 0x60787bf8,\r\n        0x6003604d, 0xd1fd8346, 0xf6381fb0, 0x7745ae04, 0xd736fccc,\r\n        0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f, 0x77a057be,\r\n        0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\r\n        0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255,\r\n        0x46fcd9b9, 0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2,\r\n        0x466e598e, 0x20b45770, 0x8cd55591, 0xc902de4c, 0xb90bace1,\r\n        0xbb8205d0, 0x11a86248, 0x7574a99e, 0xb77f19b6, 0xe0a9dc09,\r\n        0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c, 0x4a99a025,\r\n        0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\r\n        0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01,\r\n        0xa70683fa, 0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641,\r\n        0xc3604c06, 0x61a806b5, 0xf0177a28, 0xc0f586e0, 0x006058aa,\r\n        0x30dc7d62, 0x11e69ed7, 0x2338ea63, 0x53c2dd94, 0xc2c21634,\r\n        0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76, 0x6f05e409,\r\n        0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\r\n        0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3,\r\n        0x4dad0fc4, 0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c,\r\n        0x6fd5c7e7, 0x56e14ec4, 0x362abfce, 0xddc6c837, 0xd79a3234,\r\n        0x92638212, 0x670efa8e, 0x406000e0, 0x3a39ce37, 0xd3faf5cf,\r\n        0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742, 0xd3822740,\r\n        0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\r\n        0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f,\r\n        0xbc946e79, 0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d,\r\n        0xd5730a1d, 0x4cd04dc6, 0x2939bbdb, 0xa9ba4650, 0xac9526e8,\r\n        0xbe5ee304, 0xa1fad5f0, 0x6a2d519a, 0x63ef8ce2, 0x9a86ee22,\r\n        0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4, 0x83c061ba,\r\n        0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\r\n        0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69,\r\n        0x77fa0a59, 0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593,\r\n        0xe990fd5a, 0x9e34d797, 0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a,\r\n        0x017da67d, 0xd1cf3ed6, 0x7c7d2d28, 0x1f9f25cf, 0xadf2b89b,\r\n        0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6, 0x47b0acfd,\r\n        0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\r\n        0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4,\r\n        0x88f46dba, 0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2,\r\n        0x97271aec, 0xa93a072a, 0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb,\r\n        0x26dcf319, 0x7533d928, 0xb155fdf5, 0x03563482, 0x8aba3cbb,\r\n        0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f, 0x4de81751,\r\n        0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\r\n        0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369,\r\n        0x6413e680, 0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166,\r\n        0xb39a460a, 0x6445c0dd, 0x586cdecf, 0x1c20c8ae, 0x5bbef7dd,\r\n        0x1b588d40, 0xccd2017f, 0x6bb4e3bb, 0xdda26a7e, 0x3a59ff45,\r\n        0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb, 0x8d6612ae,\r\n        0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\r\n        0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08,\r\n        0x4eb4e2cc, 0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d,\r\n        0x06b89fb4, 0xce6ea048, 0x6f3f3b82, 0x3520ab82, 0x011a1d4b,\r\n        0x277227f8, 0x611560b1, 0xe7933fdc, 0xbb3a792b, 0x344525bd,\r\n        0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9, 0xe01cc87e,\r\n        0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\r\n        0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c,\r\n        0xe0b12b4f, 0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c,\r\n        0xbf97222c, 0x15e6fc2a, 0x0f91fc71, 0x9b941525, 0xfae59361,\r\n        0xceb69ceb, 0xc2a86459, 0x12baa8d1, 0xb6c1075e, 0xe3056a0c,\r\n        0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b, 0x4c98a0be,\r\n        0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\r\n        0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d,\r\n        0x9b992f2e, 0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891,\r\n        0xce6279cf, 0xcd3e7e6f, 0x1618b166, 0xfd2c1d05, 0x848fd2c5,\r\n        0xf6fb2299, 0xf523f357, 0xa6327623, 0x93a83531, 0x56cccd02,\r\n        0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc, 0xde966292,\r\n        0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\r\n        0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2,\r\n        0x35bdd2f6, 0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b,\r\n        0x53113ec0, 0x1640e3d3, 0x38abbd60, 0x2547adf0, 0xba38209c,\r\n        0xf746ce76, 0x77afa1c5, 0x20756060, 0x85cbfe4e, 0x8ae88dd8,\r\n        0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c, 0x01c36ae4,\r\n        0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\r\n        0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6\r\n    ];\r\n\r\n    /**\r\n     * @type {Array.<number>}\r\n     * @const\r\n     * @inner\r\n     */\r\n    var C_ORIG = [\r\n        0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944,\r\n        0x6f756274\r\n    ];\r\n\r\n    /**\r\n     * @param {Array.<number>} lr\r\n     * @param {number} off\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @returns {Array.<number>}\r\n     * @inner\r\n     */\r\n    function _encipher(lr, off, P, S) { // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\r\n        var n,\r\n            l = lr[off],\r\n            r = lr[off + 1];\r\n\r\n        l ^= P[0];\r\n\r\n        /*\r\n        for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\r\n            // Feistel substitution on left word\r\n            n  = S[l >>> 24],\r\n            n += S[0x100 | ((l >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((l >> 8) & 0xff)],\r\n            n += S[0x300 | (l & 0xff)],\r\n            r ^= n ^ P[++i],\r\n            // Feistel substitution on right word\r\n            n  = S[r >>> 24],\r\n            n += S[0x100 | ((r >> 16) & 0xff)],\r\n            n ^= S[0x200 | ((r >> 8) & 0xff)],\r\n            n += S[0x300 | (r & 0xff)],\r\n            l ^= n ^ P[++i];\r\n        */\r\n\r\n        //The following is an unrolled version of the above loop.\r\n        //Iteration 0\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[1];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[2];\r\n        //Iteration 1\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[3];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[4];\r\n        //Iteration 2\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[5];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[6];\r\n        //Iteration 3\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[7];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[8];\r\n        //Iteration 4\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[9];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[10];\r\n        //Iteration 5\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[11];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[12];\r\n        //Iteration 6\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[13];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[14];\r\n        //Iteration 7\r\n        n  = S[l >>> 24];\r\n        n += S[0x100 | ((l >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((l >> 8) & 0xff)];\r\n        n += S[0x300 | (l & 0xff)];\r\n        r ^= n ^ P[15];\r\n        n  = S[r >>> 24];\r\n        n += S[0x100 | ((r >> 16) & 0xff)];\r\n        n ^= S[0x200 | ((r >> 8) & 0xff)];\r\n        n += S[0x300 | (r & 0xff)];\r\n        l ^= n ^ P[16];\r\n\r\n        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\r\n        lr[off + 1] = l;\r\n        return lr;\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} data\r\n     * @param {number} offp\r\n     * @returns {{key: number, offp: number}}\r\n     * @inner\r\n     */\r\n    function _streamtoword(data, offp) {\r\n        for (var i = 0, word = 0; i < 4; ++i)\r\n            word = (word << 8) | (data[offp] & 0xff),\r\n            offp = (offp + 1) % data.length;\r\n        return { key: word, offp: offp };\r\n    }\r\n\r\n    /**\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _key(key, P, S) {\r\n        var offset = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offset),\r\n            offset = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        for (i = 0; i < plen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Expensive key schedule Blowfish.\r\n     * @param {Array.<number>} data\r\n     * @param {Array.<number>} key\r\n     * @param {Array.<number>} P\r\n     * @param {Array.<number>} S\r\n     * @inner\r\n     */\r\n    function _ekskey(data, key, P, S) {\r\n        var offp = 0,\r\n            lr = [0, 0],\r\n            plen = P.length,\r\n            slen = S.length,\r\n            sw;\r\n        for (var i = 0; i < plen; i++)\r\n            sw = _streamtoword(key, offp),\r\n            offp = sw.offp,\r\n            P[i] = P[i] ^ sw.key;\r\n        offp = 0;\r\n        for (i = 0; i < plen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            P[i] = lr[0],\r\n            P[i + 1] = lr[1];\r\n        for (i = 0; i < slen; i += 2)\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[0] ^= sw.key,\r\n            sw = _streamtoword(data, offp),\r\n            offp = sw.offp,\r\n            lr[1] ^= sw.key,\r\n            lr = _encipher(lr, 0, P, S),\r\n            S[i] = lr[0],\r\n            S[i + 1] = lr[1];\r\n    }\r\n\r\n    /**\r\n     * Internaly crypts a string.\r\n     * @param {Array.<number>} b Bytes to crypt\r\n     * @param {Array.<number>} salt Salt bytes to use\r\n     * @param {number} rounds Number of rounds\r\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\r\n     *  omitted, the operation will be performed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _crypt(b, salt, rounds, callback, progressCallback) {\r\n        var cdata = C_ORIG.slice(),\r\n            clen = cdata.length,\r\n            err;\r\n\r\n        // Validate\r\n        if (rounds < 4 || rounds > 31) {\r\n            err = Error(\"Illegal number of rounds (4-31): \"+rounds);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        if (salt.length !== BCRYPT_SALT_LEN) {\r\n            err =Error(\"Illegal salt length: \"+salt.length+\" != \"+BCRYPT_SALT_LEN);\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        rounds = (1 << rounds) >>> 0;\r\n\r\n        var P, S, i = 0, j;\r\n\r\n        //Use typed arrays when available - huge speedup!\r\n        if (Int32Array) {\r\n            P = new Int32Array(P_ORIG);\r\n            S = new Int32Array(S_ORIG);\r\n        } else {\r\n            P = P_ORIG.slice();\r\n            S = S_ORIG.slice();\r\n        }\r\n\r\n        _ekskey(salt, b, P, S);\r\n\r\n        /**\r\n         * Calcualtes the next round.\r\n         * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\r\n         * @inner\r\n         */\r\n        function next() {\r\n            if (progressCallback)\r\n                progressCallback(i / rounds);\r\n            if (i < rounds) {\r\n                var start = Date.now();\r\n                for (; i < rounds;) {\r\n                    i = i + 1;\r\n                    _key(b, P, S);\r\n                    _key(salt, P, S);\r\n                    if (Date.now() - start > MAX_EXECUTION_TIME)\r\n                        break;\r\n                }\r\n            } else {\r\n                for (i = 0; i < 64; i++)\r\n                    for (j = 0; j < (clen >> 1); j++)\r\n                        _encipher(cdata, j << 1, P, S);\r\n                var ret = [];\r\n                for (i = 0; i < clen; i++)\r\n                    ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\r\n                    ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\r\n                    ret.push((cdata[i] & 0xff) >>> 0);\r\n                if (callback) {\r\n                    callback(null, ret);\r\n                    return;\r\n                } else\r\n                    return ret;\r\n            }\r\n            if (callback)\r\n                nextTick(next);\r\n        }\r\n\r\n        // Async\r\n        if (typeof callback !== 'undefined') {\r\n            next();\r\n\r\n            // Sync\r\n        } else {\r\n            var res;\r\n            while (true)\r\n                if (typeof(res = next()) !== 'undefined')\r\n                    return res || [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Internally hashes a string.\r\n     * @param {string} s String to hash\r\n     * @param {?string} salt Salt to use, actually never null\r\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\r\n     *  hashing is perormed synchronously.\r\n     *  @param {function(number)=} progressCallback Callback called with the current progress\r\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\r\n     * @inner\r\n     */\r\n    function _hash(s, salt, callback, progressCallback) {\r\n        var err;\r\n        if (typeof s !== 'string' || typeof salt !== 'string') {\r\n            err = Error(\"Invalid string / salt: Not a string\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n\r\n        // Validate the salt\r\n        var minor, offset;\r\n        if (salt.charAt(0) !== '$' || salt.charAt(1) !== '2') {\r\n            err = Error(\"Invalid salt version: \"+salt.substring(0,2));\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            }\r\n            else\r\n                throw err;\r\n        }\r\n        if (salt.charAt(2) === '$')\r\n            minor = String.fromCharCode(0),\r\n            offset = 3;\r\n        else {\r\n            minor = salt.charAt(2);\r\n            if ((minor !== 'a' && minor !== 'b' && minor !== 'y') || salt.charAt(3) !== '$') {\r\n                err = Error(\"Invalid salt revision: \"+salt.substring(2,4));\r\n                if (callback) {\r\n                    nextTick(callback.bind(this, err));\r\n                    return;\r\n                } else\r\n                    throw err;\r\n            }\r\n            offset = 4;\r\n        }\r\n\r\n        // Extract number of rounds\r\n        if (salt.charAt(offset + 2) > '$') {\r\n            err = Error(\"Missing salt rounds\");\r\n            if (callback) {\r\n                nextTick(callback.bind(this, err));\r\n                return;\r\n            } else\r\n                throw err;\r\n        }\r\n        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\r\n            r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\r\n            rounds = r1 + r2,\r\n            real_salt = salt.substring(offset + 3, offset + 25);\r\n        s += minor >= 'a' ? \"\\x00\" : \"\";\r\n\r\n        var passwordb = stringToBytes(s),\r\n            saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\r\n\r\n        /**\r\n         * Finishes hashing.\r\n         * @param {Array.<number>} bytes Byte array\r\n         * @returns {string}\r\n         * @inner\r\n         */\r\n        function finish(bytes) {\r\n            var res = [];\r\n            res.push(\"$2\");\r\n            if (minor >= 'a')\r\n                res.push(minor);\r\n            res.push(\"$\");\r\n            if (rounds < 10)\r\n                res.push(\"0\");\r\n            res.push(rounds.toString());\r\n            res.push(\"$\");\r\n            res.push(base64_encode(saltb, saltb.length));\r\n            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\r\n            return res.join('');\r\n        }\r\n\r\n        // Sync\r\n        if (typeof callback == 'undefined')\r\n            return finish(_crypt(passwordb, saltb, rounds));\r\n\r\n        // Async\r\n        else {\r\n            _crypt(passwordb, saltb, rounds, function(err, bytes) {\r\n                if (err)\r\n                    callback(err, null);\r\n                else\r\n                    callback(null, finish(bytes));\r\n            }, progressCallback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {!Array.<number>} b Byte array\r\n     * @param {number} len Maximum input length\r\n     * @returns {string}\r\n     * @expose\r\n     */\r\n    bcrypt.encodeBase64 = base64_encode;\r\n\r\n    /**\r\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\r\n     * @function\r\n     * @param {string} s String to decode\r\n     * @param {number} len Maximum output length\r\n     * @returns {!Array.<number>}\r\n     * @expose\r\n     */\r\n    bcrypt.decodeBase64 = base64_decode;\r\n\r\n    return bcrypt;\r\n}));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/dist/bcrypt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/bcryptjs/index.js":
/*!****************************************!*\
  !*** ./node_modules/bcryptjs/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*\r\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\r\n Copyright (c) 2012 Shane Girish <<EMAIL>>\r\n Copyright (c) 2013 Daniel Wirtz <<EMAIL>>\r\n\r\n Redistribution and use in source and binary forms, with or without\r\n modification, are permitted provided that the following conditions\r\n are met:\r\n 1. Redistributions of source code must retain the above copyright\r\n notice, this list of conditions and the following disclaimer.\r\n 2. Redistributions in binary form must reproduce the above copyright\r\n notice, this list of conditions and the following disclaimer in the\r\n documentation and/or other materials provided with the distribution.\r\n 3. The name of the author may not be used to endorse or promote products\r\n derived from this software without specific prior written permission.\r\n\r\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\r\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\r\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\r\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\r\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\r\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\r\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\r\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\r\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\r\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n */\r\n\r\nmodule.exports = __webpack_require__(/*! ./dist/bcrypt.js */ \"(rsc)/./node_modules/bcryptjs/dist/bcrypt.js\");\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/index.js\n");

/***/ })

};
;