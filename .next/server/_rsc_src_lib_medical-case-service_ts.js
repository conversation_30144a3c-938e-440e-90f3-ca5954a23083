"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_medical-case-service_ts";
exports.ids = ["_rsc_src_lib_medical-case-service_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/cache-manager.ts":
/*!**********************************!*\
  !*** ./src/lib/cache-manager.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CACHE_CONFIGS: () => (/* binding */ CACHE_CONFIGS),\n/* harmony export */   cacheManager: () => (/* binding */ cacheManager),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _monitoring__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./monitoring */ \"(rsc)/./src/lib/monitoring.ts\");\n/**\n * 企业级缓存管理系统\n */ \n/**\n * 多层缓存管理器\n */ class CacheManager {\n    /**\n   * 获取缓存值\n   */ async get(key, config) {\n        const timer = _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.startTimer('cache_get', {\n            key\n        });\n        try {\n            // 1. 检查内存缓存\n            const memoryResult = this.getFromMemory(key);\n            if (memoryResult !== null) {\n                this.stats.hits++;\n                timer.end(true);\n                this.updateHitRate();\n                _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n                    name: 'cache_hit',\n                    value: 1,\n                    tags: {\n                        layer: 'memory',\n                        key: this.sanitizeKey(key)\n                    }\n                });\n                return memoryResult;\n            }\n            // 2. 检查Redis缓存（生产环境）\n            if (false) {}\n            // 缓存未命中\n            this.stats.misses++;\n            timer.end(true);\n            this.updateHitRate();\n            _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n                name: 'cache_miss',\n                value: 1,\n                tags: {\n                    key: this.sanitizeKey(key)\n                }\n            });\n            return null;\n        } catch (error) {\n            timer.end(false, error.message);\n            (0,_monitoring__WEBPACK_IMPORTED_MODULE_0__.log)({\n                level: 'error',\n                message: 'Cache get operation failed',\n                context: {\n                    key,\n                    error: error.message\n                }\n            });\n            return null;\n        }\n    }\n    /**\n   * 设置缓存值\n   */ async set(key, value, config) {\n        const timer = _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.startTimer('cache_set', {\n            key\n        });\n        try {\n            // 1. 设置内存缓存\n            this.setInMemory(key, value, config);\n            // 2. 设置Redis缓存（生产环境）\n            if (false) {}\n            this.stats.sets++;\n            timer.end(true);\n            _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n                name: 'cache_set',\n                value: 1,\n                tags: {\n                    key: this.sanitizeKey(key)\n                }\n            });\n        } catch (error) {\n            timer.end(false, error.message);\n            (0,_monitoring__WEBPACK_IMPORTED_MODULE_0__.log)({\n                level: 'error',\n                message: 'Cache set operation failed',\n                context: {\n                    key,\n                    error: error.message\n                }\n            });\n            throw error;\n        }\n    }\n    /**\n   * 删除缓存\n   */ async delete(key) {\n        const timer = _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.startTimer('cache_delete', {\n            key\n        });\n        try {\n            // 1. 删除内存缓存\n            this.memoryCache.delete(key);\n            // 2. 删除Redis缓存（生产环境）\n            if (false) {}\n            this.stats.deletes++;\n            this.stats.size = this.memoryCache.size;\n            timer.end(true);\n            _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n                name: 'cache_delete',\n                value: 1,\n                tags: {\n                    key: this.sanitizeKey(key)\n                }\n            });\n        } catch (error) {\n            timer.end(false, error.message);\n            (0,_monitoring__WEBPACK_IMPORTED_MODULE_0__.log)({\n                level: 'error',\n                message: 'Cache delete operation failed',\n                context: {\n                    key,\n                    error: error.message\n                }\n            });\n        }\n    }\n    /**\n   * 缓存穿透保护：获取或设置\n   */ async getOrSet(key, fetcher, config) {\n        // 先尝试获取缓存\n        const cached = await this.get(key, config);\n        if (cached !== null) {\n            return cached;\n        }\n        // 防止缓存击穿：使用分布式锁\n        const lockKey = `lock:${key}`;\n        const acquired = await this.acquireLock(lockKey, 30000) // 30秒锁\n        ;\n        if (!acquired) {\n            // 等待一段时间后重试\n            await this.sleep(100);\n            const retryResult = await this.get(key, config);\n            if (retryResult !== null) {\n                return retryResult;\n            }\n        }\n        try {\n            // 执行数据获取\n            const timer = _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.startTimer('cache_fetch', {\n                key\n            });\n            const value = await fetcher();\n            timer.end(true);\n            // 设置缓存\n            await this.set(key, value, config);\n            _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n                name: 'cache_fetch_success',\n                value: 1,\n                tags: {\n                    key: this.sanitizeKey(key)\n                }\n            });\n            return value;\n        } catch (error) {\n            _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n                name: 'cache_fetch_error',\n                value: 1,\n                tags: {\n                    key: this.sanitizeKey(key)\n                }\n            });\n            throw error;\n        } finally{\n            if (acquired) {\n                await this.releaseLock(lockKey);\n            }\n        }\n    }\n    /**\n   * 批量操作\n   */ async mget(keys) {\n        const results = new Map();\n        await Promise.all(keys.map(async (key)=>{\n            const value = await this.get(key);\n            results.set(key, value);\n        }));\n        return results;\n    }\n    async mset(entries) {\n        await Promise.all(Array.from(entries.entries()).map(async ([key, { value, config }])=>{\n            await this.set(key, value, config);\n        }));\n    }\n    /**\n   * 清空缓存\n   */ async clear(pattern) {\n        if (pattern) {\n            // 清空匹配模式的缓存\n            const regex = new RegExp(pattern);\n            for (const key of this.memoryCache.keys()){\n                if (regex.test(key)) {\n                    await this.delete(key);\n                }\n            }\n        } else {\n            // 清空所有缓存\n            this.memoryCache.clear();\n            this.stats.size = 0;\n        }\n        _monitoring__WEBPACK_IMPORTED_MODULE_0__.monitoring.recordMetric({\n            name: 'cache_clear',\n            value: 1,\n            tags: {\n                pattern: pattern || 'all'\n            }\n        });\n    }\n    /**\n   * 获取统计信息\n   */ getStats() {\n        return {\n            ...this.stats,\n            size: this.memoryCache.size\n        };\n    }\n    /**\n   * 内存缓存操作\n   */ getFromMemory(key) {\n        const entry = this.memoryCache.get(key);\n        if (!entry) return null;\n        const now = Date.now();\n        // 检查是否过期\n        if (now > entry.timestamp + entry.ttl * 1000) {\n            this.memoryCache.delete(key);\n            return null;\n        }\n        // 更新访问信息\n        entry.accessCount++;\n        entry.lastAccessed = now;\n        return entry.value;\n    }\n    setInMemory(key, value, config) {\n        const now = Date.now();\n        const entry = {\n            value,\n            timestamp: now,\n            ttl: config.ttl,\n            accessCount: 1,\n            lastAccessed: now\n        };\n        this.memoryCache.set(key, entry);\n        this.stats.size = this.memoryCache.size;\n        // 检查是否需要清理\n        if (config.maxSize && this.memoryCache.size > config.maxSize) {\n            this.evictEntries(config);\n        }\n    }\n    /**\n   * Redis缓存操作（生产环境）\n   */ async getFromRedis(key) {\n        // 这里应该实现Redis连接和操作\n        // 为了示例，返回null\n        return null;\n    }\n    async setInRedis(key, value, config) {\n    // 这里应该实现Redis设置操作\n    }\n    async deleteFromRedis(key) {\n    // 这里应该实现Redis删除操作\n    }\n    /**\n   * 分布式锁\n   */ async acquireLock(key, ttl) {\n        // 简化实现，生产环境应使用Redis分布式锁\n        return true;\n    }\n    async releaseLock(key) {\n    // 释放锁的实现\n    }\n    /**\n   * 缓存淘汰策略\n   */ evictEntries(config) {\n        const strategy = config.strategy || 'LRU';\n        const maxSize = config.maxSize || 1000;\n        if (this.memoryCache.size <= maxSize) return;\n        const entries = Array.from(this.memoryCache.entries());\n        let toEvict = [];\n        switch(strategy){\n            case 'LRU':\n                toEvict = entries.sort(([, a], [, b])=>a.lastAccessed - b.lastAccessed).slice(0, this.memoryCache.size - maxSize).map(([key])=>key);\n                break;\n            case 'LFU':\n                toEvict = entries.sort(([, a], [, b])=>a.accessCount - b.accessCount).slice(0, this.memoryCache.size - maxSize).map(([key])=>key);\n                break;\n            case 'FIFO':\n                toEvict = entries.sort(([, a], [, b])=>a.timestamp - b.timestamp).slice(0, this.memoryCache.size - maxSize).map(([key])=>key);\n                break;\n        }\n        toEvict.forEach((key)=>this.memoryCache.delete(key));\n        this.stats.size = this.memoryCache.size;\n    }\n    updateHitRate() {\n        const total = this.stats.hits + this.stats.misses;\n        this.stats.hitRate = total > 0 ? this.stats.hits / total * 100 : 0;\n    }\n    sanitizeKey(key) {\n        // 清理敏感信息用于指标记录\n        return key.replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, 'UUID');\n    }\n    async sleep(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    constructor(){\n        this.memoryCache = new Map();\n        this.stats = {\n            hits: 0,\n            misses: 0,\n            sets: 0,\n            deletes: 0,\n            size: 0,\n            hitRate: 0\n        };\n    }\n}\n// 单例实例\nconst cacheManager = new CacheManager();\n// 预定义的缓存配置\nconst CACHE_CONFIGS = {\n    // 短期缓存：用户会话、临时数据\n    SHORT: {\n        ttl: 300,\n        maxSize: 1000,\n        strategy: 'LRU'\n    },\n    // 中期缓存：API响应、计算结果\n    MEDIUM: {\n        ttl: 1800,\n        maxSize: 500,\n        strategy: 'LRU'\n    },\n    // 长期缓存：配置数据、静态内容\n    LONG: {\n        ttl: 3600,\n        maxSize: 200,\n        strategy: 'LFU'\n    },\n    // 用户数据缓存\n    USER_DATA: {\n        ttl: 900,\n        maxSize: 1000,\n        strategy: 'LRU'\n    },\n    // 查询结果缓存\n    QUERY_RESULT: {\n        ttl: 600,\n        maxSize: 500,\n        strategy: 'LRU'\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (cacheManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cache-manager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database-optimization.ts":
/*!******************************************!*\
  !*** ./src/lib/database-optimization.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeQueryPerformance: () => (/* binding */ analyzeQueryPerformance),\n/* harmony export */   executeBatchQueries: () => (/* binding */ executeBatchQueries),\n/* harmony export */   getOptimizedMedicalCases: () => (/* binding */ getOptimizedMedicalCases),\n/* harmony export */   getOptimizedRuleExecutionLogs: () => (/* binding */ getOptimizedRuleExecutionLogs),\n/* harmony export */   getOptimizedStatistics: () => (/* binding */ getOptimizedStatistics)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _cache_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cache-manager */ \"(rsc)/./src/lib/cache-manager.ts\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _monitoring__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./monitoring */ \"(rsc)/./src/lib/monitoring.ts\");\n/**\n * 数据库查询优化工具库\n * 提供高性能的查询构建和执行方法\n */ \n\n\n\n/**\n * 优化的医疗案例查询\n */ async function getOptimizedMedicalCases(params = {}) {\n    const timer = _monitoring__WEBPACK_IMPORTED_MODULE_3__.monitoring.startTimer('db_query_medical_cases');\n    try {\n        const { page = 1, pageSize = 10, sortBy = 'CREATED_AT', sortOrder = 'DESC', filters = {}, useCache = true, cacheTTL = 300000 // 5分钟\n         } = params;\n        // 生成缓存键\n        const cacheKey = `medical_cases:${JSON.stringify({\n            page,\n            pageSize,\n            sortBy,\n            sortOrder,\n            filters\n        })}`;\n        // 尝试从缓存获取\n        if (useCache) {\n            const cached = await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.get(cacheKey);\n            if (cached) {\n                timer.end(true);\n                return cached;\n            }\n        }\n        // 构建优化的查询SQL\n        let baseSQL = `\n      SELECT \n        mc.ID,\n        mc.CASE_NUMBER,\n        mc.PATIENT_NAME,\n        mc.PATIENT_ID_CARD,\n        mc.CASE_TYPE,\n        mc.MEDICAL_CATEGORY,\n        mc.HOSPITAL_NAME,\n        mc.HOSPITAL_CODE,\n        mc.TOTAL_COST,\n        mc.ADMISSION_DATE,\n        mc.DISCHARGE_DATE,\n        mc.CREATED_AT\n      FROM MEDICAL_CASE mc\n      WHERE mc.IS_DELETED = 0\n    `;\n        const binds = {};\n        // 添加筛选条件\n        if (filters['caseType']) {\n            baseSQL += ` AND mc.CASE_TYPE = :caseType`;\n            binds['caseType'] = filters['caseType'];\n        }\n        if (filters['medicalCategory']) {\n            baseSQL += ` AND mc.MEDICAL_CATEGORY = :medicalCategory`;\n            binds['medicalCategory'] = filters['medicalCategory'];\n        }\n        if (filters['hospitalCode']) {\n            baseSQL += ` AND mc.HOSPITAL_CODE = :hospitalCode`;\n            binds['hospitalCode'] = filters['hospitalCode'];\n        }\n        if (filters['patientName']) {\n            baseSQL += ` AND UPPER(mc.PATIENT_NAME) LIKE UPPER(:patientName)`;\n            binds['patientName'] = `%${filters['patientName']}%`;\n        }\n        if (filters['patientIdCard']) {\n            baseSQL += ` AND mc.PATIENT_ID_CARD = :patientIdCard`;\n            binds['patientIdCard'] = filters['patientIdCard'];\n        }\n        if (filters['startDate']) {\n            baseSQL += ` AND mc.CREATED_AT >= :startDate`;\n            binds['startDate'] = new Date(filters['startDate']);\n        }\n        if (filters['endDate']) {\n            baseSQL += ` AND mc.CREATED_AT <= :endDate`;\n            binds['endDate'] = new Date(filters['endDate']);\n        }\n        if (filters['minCost']) {\n            baseSQL += ` AND mc.TOTAL_COST >= :minCost`;\n            binds['minCost'] = filters['minCost'];\n        }\n        if (filters['maxCost']) {\n            baseSQL += ` AND mc.TOTAL_COST <= :maxCost`;\n            binds['maxCost'] = filters['maxCost'];\n        }\n        // 获取总数\n        const countSQL = (0,_database__WEBPACK_IMPORTED_MODULE_0__.buildCountSQL)(baseSQL);\n        const countResult = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSQL, binds);\n        const total = countResult.rows?.[0]?.TOTAL || 0;\n        // 构建分页查询\n        const paginatedSQL = (0,_database__WEBPACK_IMPORTED_MODULE_0__.buildPaginationSQL)(baseSQL, page, pageSize, `${sortBy} ${sortOrder}`);\n        const dataResult = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(paginatedSQL, binds);\n        const result = {\n            data: dataResult.rows || [],\n            total,\n            page,\n            pageSize,\n            totalPages: Math.ceil(total / pageSize),\n            hasNext: page < Math.ceil(total / pageSize),\n            hasPrev: page > 1\n        };\n        // 缓存结果\n        if (useCache) {\n            await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.set(cacheKey, result, {\n                ttl: cacheTTL\n            });\n        }\n        timer.end(true);\n        return result;\n    } catch (error) {\n        timer.end(false, error.message);\n        _logger__WEBPACK_IMPORTED_MODULE_2__.log.error('Optimized medical cases query failed', {\n            params,\n            error: error.message\n        });\n        throw error;\n    }\n}\n/**\n * 优化的规则执行日志查询\n */ async function getOptimizedRuleExecutionLogs(params = {}) {\n    const timer = _monitoring__WEBPACK_IMPORTED_MODULE_3__.monitoring.startTimer('db_query_rule_execution_logs');\n    try {\n        const { page = 1, pageSize = 10, sortBy = 'STARTED_AT', sortOrder = 'DESC', filters = {}, useCache = true, cacheTTL = 180000 // 3分钟\n         } = params;\n        const cacheKey = `rule_execution_logs:${JSON.stringify({\n            page,\n            pageSize,\n            sortBy,\n            sortOrder,\n            filters\n        })}`;\n        if (useCache) {\n            const cached = await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.get(cacheKey);\n            if (cached) {\n                timer.end(true);\n                return cached;\n            }\n        }\n        let baseSQL = `\n      SELECT \n        rel.ID,\n        rel.RULE_ID,\n        rel.EXECUTION_ID,\n        rel.EXECUTION_STATUS,\n        rel.STARTED_AT,\n        rel.ENDED_AT,\n        rel.EXECUTION_DURATION,\n        rel.PROCESSED_RECORD_COUNT,\n        rel.MATCHED_RECORD_COUNT,\n        rel.ERROR_MESSAGE,\n        rs.RULE_NAME,\n        rs.RULE_TYPE,\n        rs.RULE_CATEGORY\n      FROM RULE_EXECUTION_LOG rel\n      LEFT JOIN RULE_SUPERVISION rs ON rel.RULE_ID = rs.ID\n      WHERE 1=1\n    `;\n        const binds = {};\n        // 添加筛选条件\n        if (filters['ruleId']) {\n            baseSQL += ` AND rel.RULE_ID = :ruleId`;\n            binds['ruleId'] = filters['ruleId'];\n        }\n        if (filters['executionStatus']) {\n            baseSQL += ` AND rel.EXECUTION_STATUS = :executionStatus`;\n            binds['executionStatus'] = filters['executionStatus'];\n        }\n        if (filters['ruleType']) {\n            baseSQL += ` AND rs.RULE_TYPE = :ruleType`;\n            binds['ruleType'] = filters['ruleType'];\n        }\n        if (filters['startDate']) {\n            baseSQL += ` AND rel.STARTED_AT >= :startDate`;\n            binds['startDate'] = new Date(filters['startDate']);\n        }\n        if (filters['endDate']) {\n            baseSQL += ` AND rel.STARTED_AT <= :endDate`;\n            binds['endDate'] = new Date(filters['endDate']);\n        }\n        if (filters['executedBy']) {\n            baseSQL += ` AND rel.EXECUTED_BY = :executedBy`;\n            binds['executedBy'] = filters['executedBy'];\n        }\n        // 获取总数和分页数据\n        const countSQL = (0,_database__WEBPACK_IMPORTED_MODULE_0__.buildCountSQL)(baseSQL);\n        const countResult = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSQL, binds);\n        const total = countResult.rows?.[0]?.TOTAL || 0;\n        const paginatedSQL = (0,_database__WEBPACK_IMPORTED_MODULE_0__.buildPaginationSQL)(baseSQL, page, pageSize, `${sortBy} ${sortOrder}`);\n        const dataResult = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(paginatedSQL, binds);\n        const result = {\n            data: dataResult.rows || [],\n            total,\n            page,\n            pageSize,\n            totalPages: Math.ceil(total / pageSize),\n            hasNext: page < Math.ceil(total / pageSize),\n            hasPrev: page > 1\n        };\n        if (useCache) {\n            await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.set(cacheKey, result, {\n                ttl: cacheTTL\n            });\n        }\n        timer.end(true);\n        return result;\n    } catch (error) {\n        timer.end(false, error.message);\n        _logger__WEBPACK_IMPORTED_MODULE_2__.log.error('Optimized rule execution logs query failed', {\n            params,\n            error: error.message\n        });\n        throw error;\n    }\n}\n/**\n * 优化的统计查询\n */ async function getOptimizedStatistics(type, params = {}) {\n    const timer = _monitoring__WEBPACK_IMPORTED_MODULE_3__.monitoring.startTimer('db_query_statistics');\n    try {\n        const cacheKey = `statistics:${type}:${JSON.stringify(params)}`;\n        // 尝试从缓存获取\n        const cached = await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.get(cacheKey);\n        if (cached) {\n            timer.end(true);\n            return cached;\n        }\n        let sql = '';\n        const binds = {};\n        switch(type){\n            case 'medical_cases_overview':\n                sql = `\n          SELECT \n            COUNT(*) as total_cases,\n            COUNT(CASE WHEN CASE_TYPE = 'INPATIENT' THEN 1 END) as inpatient_cases,\n            COUNT(CASE WHEN CASE_TYPE = 'OUTPATIENT' THEN 1 END) as outpatient_cases,\n            ROUND(AVG(TOTAL_COST), 2) as avg_cost,\n            SUM(TOTAL_COST) as total_cost,\n            COUNT(DISTINCT HOSPITAL_CODE) as hospital_count,\n            COUNT(DISTINCT PATIENT_ID_CARD) as patient_count\n          FROM MEDICAL_CASE \n          WHERE IS_DELETED = 0\n            AND CREATED_AT >= SYSDATE - 30\n        `;\n                break;\n            case 'rule_execution_overview':\n                sql = `\n          SELECT \n            COUNT(*) as total_executions,\n            COUNT(CASE WHEN EXECUTION_STATUS = 'SUCCESS' THEN 1 END) as success_count,\n            COUNT(CASE WHEN EXECUTION_STATUS = 'FAILED' THEN 1 END) as failed_count,\n            ROUND(AVG(EXECUTION_DURATION), 2) as avg_duration,\n            SUM(PROCESSED_RECORD_COUNT) as total_processed,\n            SUM(MATCHED_RECORD_COUNT) as total_matched\n          FROM RULE_EXECUTION_LOG \n          WHERE STARTED_AT >= SYSDATE - 7\n        `;\n                break;\n            case 'system_activity_overview':\n                sql = `\n          SELECT \n            COUNT(*) as total_operations,\n            COUNT(CASE WHEN IS_SUCCESS = 1 THEN 1 END) as success_operations,\n            COUNT(CASE WHEN IS_SUCCESS = 0 THEN 1 END) as failed_operations,\n            COUNT(DISTINCT USER_ID) as active_users,\n            ROUND(AVG(EXECUTION_TIME), 2) as avg_response_time\n          FROM SYSTEM_OPERATION_LOG \n          WHERE CREATED_AT >= SYSDATE - 1\n        `;\n                break;\n            default:\n                throw new Error(`Unknown statistics type: ${type}`);\n        }\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, binds);\n        const data = result.rows?.[0] || {};\n        // 缓存结果（统计数据缓存时间较长）\n        await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.set(cacheKey, data, _cache_manager__WEBPACK_IMPORTED_MODULE_1__.CACHE_CONFIGS.MEDIUM);\n        timer.end(true);\n        return data;\n    } catch (error) {\n        timer.end(false, error.message);\n        _logger__WEBPACK_IMPORTED_MODULE_2__.log.error('Optimized statistics query failed', {\n            type,\n            params,\n            error: error.message\n        });\n        throw error;\n    }\n}\n/**\n * 批量查询优化\n */ async function executeBatchQueries(queries) {\n    const timer = _monitoring__WEBPACK_IMPORTED_MODULE_3__.monitoring.startTimer('db_batch_queries');\n    try {\n        const results = await Promise.allSettled(queries.map(async (query)=>{\n            // 尝试从缓存获取\n            if (query.cacheKey) {\n                const cached = await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.get(query.cacheKey);\n                if (cached) return cached;\n            }\n            // 执行查询\n            const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(query.sql, query.binds || {});\n            const data = result.rows;\n            // 缓存结果\n            if (query.cacheKey && query.cacheTTL) {\n                await _cache_manager__WEBPACK_IMPORTED_MODULE_1__.cacheManager.set(query.cacheKey, data, {\n                    ttl: query.cacheTTL\n                });\n            }\n            return data;\n        }));\n        timer.end(true);\n        return results.map((result)=>result.status === 'fulfilled' ? result.value : null).filter(Boolean);\n    } catch (error) {\n        timer.end(false, error.message);\n        throw error;\n    }\n}\n/**\n * 查询性能分析\n */ async function analyzeQueryPerformance(sql, binds = {}) {\n    const startTime = Date.now();\n    try {\n        // 执行查询\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, binds);\n        const executionTime = Date.now() - startTime;\n        // 获取执行计划信息\n        const planResult = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(`\n      SELECT plan_hash_value, rows_processed \n      FROM v$sql \n      WHERE sql_text LIKE :sqlPattern \n      AND rownum = 1\n    `, {\n            sqlPattern: `${sql.substring(0, 50)}%`\n        });\n        return {\n            executionTime,\n            rowsProcessed: result.rows?.length || 0,\n            planHash: planResult.rows?.[0]?.PLAN_HASH_VALUE || 'unknown'\n        };\n    } catch (error) {\n        _logger__WEBPACK_IMPORTED_MODULE_2__.log.error('Query performance analysis failed', {\n            sql,\n            error: error.message\n        });\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database-optimization.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database-optimizer.ts":
/*!***************************************!*\
  !*** ./src/lib/database-optimizer.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   QueryProfiler: () => (/* binding */ QueryProfiler),\n/* harmony export */   clearCache: () => (/* binding */ clearCache),\n/* harmony export */   executeBatchQuery: () => (/* binding */ executeBatchQuery),\n/* harmony export */   executePagedQuery: () => (/* binding */ executePagedQuery),\n/* harmony export */   executeQueryWithCache: () => (/* binding */ executeQueryWithCache),\n/* harmony export */   getCacheStats: () => (/* binding */ getCacheStats),\n/* harmony export */   getConnectionPoolStats: () => (/* binding */ getConnectionPoolStats),\n/* harmony export */   optimizedExecuteQuery: () => (/* binding */ optimizedExecuteQuery)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/**\n * 数据库查询优化工具\n */ \n// 查询缓存\nconst queryCache = new Map();\n// 缓存配置\nconst CACHE_CONFIG = {\n    DEFAULT_TTL: 5 * 60 * 1000,\n    MAX_CACHE_SIZE: 100,\n    CACHE_TTL: {\n        USER_INFO: 10 * 60 * 1000,\n        MEDICAL_CASE: 2 * 60 * 1000,\n        ANALYTICS: 1 * 60 * 1000,\n        RULES: 15 * 60 * 1000,\n        SETTINGS: 30 * 60 * 1000\n    }\n};\n/**\n * 带缓存的查询执行\n */ async function executeQueryWithCache(sql, params = {}, cacheKey, ttl) {\n    // 生成缓存键\n    const key = cacheKey || generateCacheKey(sql, params);\n    // 检查缓存\n    const cached = getFromCache(key);\n    if (cached) {\n        console.log(`🎯 缓存命中: ${key}`);\n        return cached;\n    }\n    // 执行查询\n    console.log(`🔍 执行查询: ${key}`);\n    const startTime = Date.now();\n    const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, params);\n    const duration = Date.now() - startTime;\n    // 记录慢查询\n    if (duration > 1000) {\n        console.warn(`🐌 慢查询检测 (${duration}ms):`, sql.substring(0, 100) + '...');\n    }\n    // 存入缓存\n    const cacheTTL = ttl || CACHE_CONFIG.DEFAULT_TTL;\n    setToCache(key, result, cacheTTL);\n    return result;\n}\n/**\n * 生成缓存键\n */ function generateCacheKey(sql, params) {\n    const sqlHash = btoa(sql).substring(0, 20);\n    const paramsHash = btoa(JSON.stringify(params)).substring(0, 10);\n    return `query_${sqlHash}_${paramsHash}`;\n}\n/**\n * 从缓存获取数据\n */ function getFromCache(key) {\n    const cached = queryCache.get(key);\n    if (!cached) return null;\n    // 检查是否过期\n    if (Date.now() > cached.timestamp + cached.ttl) {\n        queryCache.delete(key);\n        return null;\n    }\n    return cached.data;\n}\n/**\n * 存入缓存\n */ function setToCache(key, data, ttl) {\n    // 检查缓存大小限制\n    if (queryCache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {\n        // 删除最旧的缓存项\n        const oldestKey = queryCache.keys().next().value;\n        if (oldestKey) {\n            queryCache.delete(oldestKey);\n        }\n    }\n    queryCache.set(key, {\n        data,\n        timestamp: Date.now(),\n        ttl\n    });\n}\n/**\n * 清除缓存\n */ function clearCache(pattern) {\n    if (pattern) {\n        // 清除匹配模式的缓存\n        for (const key of queryCache.keys()){\n            if (key.includes(pattern)) {\n                queryCache.delete(key);\n            }\n        }\n    } else {\n        // 清除所有缓存\n        queryCache.clear();\n    }\n    console.log(`🧹 缓存已清除${pattern ? ` (模式: ${pattern})` : ''}`);\n}\n/**\n * 获取缓存统计信息\n */ function getCacheStats() {\n    return {\n        size: queryCache.size,\n        maxSize: CACHE_CONFIG.MAX_CACHE_SIZE,\n        hitRate: 0,\n        keys: Array.from(queryCache.keys())\n    };\n}\n/**\n * 优化的分页查询\n */ async function executePagedQuery(sql, params = {}, page = 1, limit = 20, cacheKey) {\n    const offset = (page - 1) * limit;\n    // 构建分页SQL\n    const pagedSql = `\n    SELECT * FROM (\n      SELECT a.*, ROWNUM rnum FROM (\n        ${sql}\n      ) a WHERE ROWNUM <= :maxRow\n    ) WHERE rnum > :minRow\n  `;\n    // 构建计数SQL\n    const countSql = `SELECT COUNT(*) as TOTAL_COUNT FROM (${sql})`;\n    const pagedParams = {\n        ...params,\n        maxRow: offset + limit,\n        minRow: offset\n    };\n    // 并行执行数据查询和计数查询\n    const [dataResult, countResult] = await Promise.all([\n        executeQueryWithCache(pagedSql, pagedParams, cacheKey ? `${cacheKey}_page_${page}_${limit}` : undefined),\n        executeQueryWithCache(countSql, params, cacheKey ? `${cacheKey}_count` : undefined)\n    ]);\n    const total = countResult.rows?.[0]?.TOTAL_COUNT || 0;\n    const totalPages = Math.ceil(total / limit);\n    return {\n        data: dataResult.rows || [],\n        total,\n        page,\n        limit,\n        totalPages\n    };\n}\n/**\n * 批量查询优化\n */ async function executeBatchQuery(queries) {\n    console.log(`🔄 执行批量查询: ${queries.length} 个查询`);\n    const promises = queries.map((query)=>executeQueryWithCache(query.sql, query.params, query.cacheKey, query.ttl));\n    return Promise.all(promises);\n}\n/**\n * 数据库连接池状态监控\n */ async function getConnectionPoolStats() {\n    // TODO: 实现连接池状态监控\n    return {\n        connectionsOpen: 0,\n        connectionsInUse: 0,\n        connectionsAvailable: 0,\n        queueLength: 0\n    };\n}\n/**\n * 查询性能分析\n */ class QueryProfiler {\n    static{\n        this.queries = [];\n    }\n    static record(sql, params, duration) {\n        this.queries.push({\n            sql,\n            params,\n            duration,\n            timestamp: Date.now()\n        });\n        // 保持最近1000条记录\n        if (this.queries.length > 1000) {\n            this.queries.shift();\n        }\n    }\n    static getSlowQueries(threshold = 1000) {\n        return this.queries.filter((q)=>q.duration > threshold);\n    }\n    static getAverageQueryTime() {\n        if (this.queries.length === 0) return 0;\n        const total = this.queries.reduce((sum, q)=>sum + q.duration, 0);\n        return total / this.queries.length;\n    }\n    static getQueryStats() {\n        if (this.queries.length === 0) {\n            return {\n                totalQueries: 0,\n                averageTime: 0,\n                slowQueries: 0,\n                fastestQuery: 0,\n                slowestQuery: 0\n            };\n        }\n        const durations = this.queries.map((q)=>q.duration);\n        return {\n            totalQueries: this.queries.length,\n            averageTime: this.getAverageQueryTime(),\n            slowQueries: this.getSlowQueries().length,\n            fastestQuery: Math.min(...durations),\n            slowestQuery: Math.max(...durations)\n        };\n    }\n    static clear() {\n        this.queries = [];\n    }\n}\n/**\n * 优化的executeQuery包装器\n */ async function optimizedExecuteQuery(sql, params = {}, options = {}) {\n    const startTime = Date.now();\n    try {\n        let result;\n        if (options.cache) {\n            result = await executeQueryWithCache(sql, params, options.cacheKey, options.ttl);\n        } else {\n            result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, params);\n        }\n        const duration = Date.now() - startTime;\n        // 记录性能数据\n        if (options.profile) {\n            QueryProfiler.record(sql, params, duration);\n        }\n        return result;\n    } catch (error) {\n        const duration = Date.now() - startTime;\n        console.error(`❌ 查询执行失败 (${duration}ms):`, error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database-optimizer.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildCountSQL: () => (/* binding */ buildCountSQL),\n/* harmony export */   buildPaginationSQL: () => (/* binding */ buildPaginationSQL),\n/* harmony export */   checkConnection: () => (/* binding */ checkConnection),\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   formatBinds: () => (/* binding */ formatBinds),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   getPoolStats: () => (/* binding */ getPoolStats),\n/* harmony export */   initializePool: () => (/* binding */ initializePool),\n/* harmony export */   monitorPoolHealth: () => (/* binding */ monitorPoolHealth)\n/* harmony export */ });\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! oracledb */ \"oracledb\");\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(oracledb__WEBPACK_IMPORTED_MODULE_0__);\n\n// Oracle数据库连接配置 - 性能优化版本\nconst dbConfig = {\n    user: process.env['DB_USERNAME'],\n    password: process.env['DB_PASSWORD'],\n    connectString: `${process.env['DB_HOST']}:${process.env['DB_PORT']}/${process.env['DB_SERVICE_NAME']}`,\n    poolMin: parseInt(process.env['DB_POOL_MIN'] || '5'),\n    poolMax: parseInt(process.env['DB_POOL_MAX'] || '20'),\n    poolIncrement: parseInt(process.env['DB_POOL_INCREMENT'] || '2'),\n    poolTimeout: parseInt(process.env['DB_POOL_TIMEOUT'] || '30'),\n    stmtCacheSize: parseInt(process.env['DB_STMT_CACHE_SIZE'] || '50'),\n    queueMax: parseInt(process.env['DB_QUEUE_MAX'] || '100'),\n    queueTimeout: parseInt(process.env['DB_QUEUE_TIMEOUT'] || '10000'),\n    enableStatistics: true\n};\n// 配置Oracle客户端\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().outFormat) = (oracledb__WEBPACK_IMPORTED_MODULE_0___default().OUT_FORMAT_OBJECT);\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().autoCommit) = true;\n// 连接池\nlet pool = null;\n/**\n * 初始化数据库连接池\n */ async function initializePool() {\n    try {\n        if (!pool) {\n            pool = await oracledb__WEBPACK_IMPORTED_MODULE_0___default().createPool(dbConfig);\n            console.log('✅ 数据库连接池初始化成功');\n            // 启动健康检查\n            startHealthCheck();\n        }\n    } catch (error) {\n        console.error('❌ 数据库连接池初始化失败:', error);\n        throw error;\n    }\n}\n/**\n * 获取数据库连接\n */ async function getConnection() {\n    try {\n        if (!pool) {\n            await initializePool();\n        }\n        return await pool.getConnection();\n    } catch (error) {\n        console.error('❌ 获取数据库连接失败:', error);\n        throw error;\n    }\n}\n// 清理Oracle查询结果，移除循环引用\nfunction cleanOracleResult(obj) {\n    if (obj === null || obj === undefined) {\n        return obj;\n    }\n    if (typeof obj !== 'object') {\n        return obj;\n    }\n    if (Array.isArray(obj)) {\n        return obj.map((item)=>cleanOracleResult(item));\n    }\n    if (obj instanceof Date) {\n        return obj;\n    }\n    if (Buffer.isBuffer(obj)) {\n        return obj;\n    }\n    // 创建新对象，只复制基本属性\n    const cleanObj = {};\n    for (const [key, value] of Object.entries(obj)){\n        // 跳过Oracle内部属性和可能的循环引用\n        if (typeof key === 'string' && (key.startsWith('_') || key.toLowerCase().includes('connection') || key.toLowerCase().includes('pool') || key.toLowerCase().includes('client') || key.toLowerCase().includes('socket') || key.toLowerCase().includes('stream') || key.toLowerCase().includes('cursor') || key.toLowerCase().includes('resultset') || key === 'domain' || key === 'constructor' || key === 'prototype')) {\n            continue;\n        }\n        try {\n            cleanObj[key] = cleanOracleResult(value);\n        } catch (error) {\n            continue;\n        }\n    }\n    return cleanObj;\n}\n/**\n * 执行SQL查询\n */ async function executeQuery(sql, binds = {}, options = {}) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        const result = await connection.execute(sql, binds, options);\n        // 使用经过验证的清理函数，移除循环引用\n        const cleanRows = result.rows ? cleanOracleResult(result.rows) : [];\n        return {\n            rows: cleanRows,\n            rowsAffected: result.rowsAffected,\n            metaData: result.metaData ? cleanOracleResult(result.metaData) : undefined\n        };\n    } catch (error) {\n        console.error('❌ SQL查询执行失败:', error);\n        console.error('SQL:', sql);\n        console.error('Binds:', binds);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 执行事务\n */ async function executeTransaction(operations) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        // 关闭自动提交\n        // connection.autoCommit = false // Oracle连接不支持直接设置autoCommit属性\n        // 执行操作\n        const result = await operations(connection);\n        // 提交事务\n        await connection.commit();\n        return result;\n    } catch (error) {\n        // 回滚事务\n        if (connection) {\n            try {\n                await connection.rollback();\n            } catch (rollbackError) {\n                console.error('⚠️ 事务回滚失败:', rollbackError);\n            }\n        }\n        console.error('❌ 事务执行失败:', error);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                // 恢复自动提交\n                // connection.autoCommit = true // Oracle连接不支持直接设置autoCommit属性\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 关闭数据库连接池\n */ async function closePool() {\n    try {\n        if (pool) {\n            // 停止健康检查\n            stopHealthCheck();\n            await pool.close(10); // 等待10秒关闭\n            pool = null;\n            console.log('✅ 数据库连接池已关闭');\n        }\n    } catch (error) {\n        console.error('❌ 关闭数据库连接池失败:', error);\n        throw error;\n    }\n}\n/**\n * 检查数据库连接状态\n */ async function checkConnection() {\n    try {\n        const result = await executeQuery('SELECT SYSDATE FROM DUAL');\n        return !!(result.rows && result.rows.length > 0);\n    } catch (error) {\n        console.error('❌ 数据库连接检查失败:', error);\n        return false;\n    }\n}\n/**\n * 格式化SQL绑定参数\n */ function formatBinds(params) {\n    const formatted = {};\n    for (const [key, value] of Object.entries(params)){\n        if (value === null || value === undefined) {\n            formatted[key] = {\n                val: null,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'string') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'number') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().NUMBER)\n            };\n        } else if (value instanceof Date) {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().DATE)\n            };\n        } else {\n            formatted[key] = value;\n        }\n    }\n    return formatted;\n}\n/**\n * 构建分页SQL\n */ function buildPaginationSQL(baseSql, page = 1, pageSize = 10, orderBy = 'ID') {\n    const offset = (page - 1) * pageSize;\n    // 检查baseSql是否已经包含ORDER BY子句\n    const hasOrderBy = /ORDER\\s+BY/i.test(baseSql);\n    return `\n    SELECT * FROM (\n      SELECT a.*, ROWNUM rnum FROM (\n        ${baseSql}\n        ${hasOrderBy ? '' : `ORDER BY ${orderBy}`}\n      ) a\n      WHERE ROWNUM <= ${offset + pageSize}\n    )\n    WHERE rnum > ${offset}\n  `;\n}\n/**\n * 构建计数SQL\n */ function buildCountSQL(baseSql) {\n    // 移除ORDER BY子句\n    const cleanSql = baseSql.replace(/ORDER\\s+BY\\s+[^)]*$/i, '');\n    return `SELECT COUNT(*) as TOTAL FROM (${cleanSql})`;\n}\n// 进程退出时关闭连接池\nprocess.on('SIGINT', async ()=>{\n    await closePool();\n    process.exit(0);\n});\nprocess.on('SIGTERM', async ()=>{\n    await closePool();\n    process.exit(0);\n});\n/**\n * 获取连接池统计信息\n */ function getPoolStats() {\n    if (!pool) {\n        return {\n            connectionsOpen: 0,\n            connectionsInUse: 0,\n            connectionsAvailable: 0,\n            queueLength: 0,\n            queueTimeout: 0,\n            poolMin: 0,\n            poolMax: 0,\n            poolIncrement: 0\n        };\n    }\n    return {\n        connectionsOpen: pool.connectionsOpen,\n        connectionsInUse: pool.connectionsInUse,\n        connectionsAvailable: pool.connectionsOpen - pool.connectionsInUse,\n        queueLength: pool.queueLength || 0,\n        queueTimeout: pool.queueTimeout || 0,\n        poolMin: pool.poolMin,\n        poolMax: pool.poolMax,\n        poolIncrement: pool.poolIncrement\n    };\n}\n/**\n * 监控连接池健康状态\n */ function monitorPoolHealth() {\n    const stats = getPoolStats();\n    if (!pool) {\n        return {\n            status: 'critical',\n            message: '连接池未初始化',\n            stats\n        };\n    }\n    const utilizationRate = stats.connectionsOpen > 0 ? stats.connectionsInUse / stats.connectionsOpen * 100 : 0;\n    const queueUtilization = stats.queueLength > 0 ? stats.queueLength / (dbConfig.queueMax || 100) * 100 : 0;\n    if (utilizationRate > 90 || queueUtilization > 80) {\n        return {\n            status: 'critical',\n            message: `连接池使用率过高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    if (utilizationRate > 70 || queueUtilization > 50) {\n        return {\n            status: 'warning',\n            message: `连接池使用率偏高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    return {\n        status: 'healthy',\n        message: `连接池运行正常: ${utilizationRate.toFixed(1)}% 使用率`,\n        stats\n    };\n}\n/**\n * 启动连接池健康检查\n */ function startHealthCheck() {\n    // 如果已经有定时器在运行，先清除它\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n    }\n    global.__healthCheckInterval = setInterval(()=>{\n        const health = monitorPoolHealth();\n        if (health.status === 'critical') {\n            console.error('🚨 连接池健康检查:', health.message);\n        } else if (health.status === 'warning') {\n            console.warn('⚠️ 连接池健康检查:', health.message);\n        }\n    }, 30000); // 每30秒检查一次\n}\n/**\n * 停止连接池健康检查\n */ function stopHealthCheck() {\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n        global.__healthCheckInterval = undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LOG_LEVELS: () => (/* binding */ LOG_LEVELS),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   log: () => (/* binding */ log)\n/* harmony export */ });\n/**\n * 简单的日志记录器\n */ const LOG_LEVELS = {\n    ERROR: 'error',\n    WARN: 'warn',\n    INFO: 'info',\n    DEBUG: 'debug'\n};\nclass Logger {\n    error(message, ...args) {\n        console.error(`[ERROR] ${message}`, ...args);\n    }\n    warn(message, ...args) {\n        console.warn(`[WARN] ${message}`, ...args);\n    }\n    info(message, ...args) {\n        if (this.isDevelopment) {\n            console.info(`[INFO] ${message}`, ...args);\n        }\n    }\n    debug(message, ...args) {\n        if (this.isDevelopment) {\n            console.debug(`[DEBUG] ${message}`, ...args);\n        }\n    }\n    log(data) {\n        if (typeof data === 'object' && data.level) {\n            const { level, message, ...rest } = data;\n            switch(level){\n                case 'ERROR':\n                    this.error(message, rest);\n                    break;\n                case 'WARN':\n                    this.warn(message, rest);\n                    break;\n                case 'INFO':\n                    this.info(message, rest);\n                    break;\n                case 'DEBUG':\n                    this.debug(message, rest);\n                    break;\n                default:\n                    console.log(message, rest);\n            }\n        } else {\n            console.log(data);\n        }\n    }\n    constructor(){\n        this.isDevelopment = \"development\" === 'development';\n    }\n}\nconst log = new Logger();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (log);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/medical-case-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/medical-case-service.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   batchArchiveMedicalCases: () => (/* binding */ batchArchiveMedicalCases),\n/* harmony export */   batchDeleteMedicalCases: () => (/* binding */ batchDeleteMedicalCases),\n/* harmony export */   createMedicalCase: () => (/* binding */ createMedicalCase),\n/* harmony export */   deleteMedicalCase: () => (/* binding */ deleteMedicalCase),\n/* harmony export */   exportMedicalCases: () => (/* binding */ exportMedicalCases),\n/* harmony export */   getMedicalCaseById: () => (/* binding */ getMedicalCaseById),\n/* harmony export */   getMedicalCaseGroups: () => (/* binding */ getMedicalCaseGroups),\n/* harmony export */   getMedicalCaseStatistics: () => (/* binding */ getMedicalCaseStatistics),\n/* harmony export */   getMedicalCases: () => (/* binding */ getMedicalCases),\n/* harmony export */   getMedicalCostDetails: () => (/* binding */ getMedicalCostDetails),\n/* harmony export */   getMedicalDiagnoses: () => (/* binding */ getMedicalDiagnoses),\n/* harmony export */   getMedicalSettlements: () => (/* binding */ getMedicalSettlements),\n/* harmony export */   getMedicalSurgeries: () => (/* binding */ getMedicalSurgeries),\n/* harmony export */   updateMedicalCase: () => (/* binding */ updateMedicalCase)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_database_optimizer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-optimizer */ \"(rsc)/./src/lib/database-optimizer.ts\");\n/* harmony import */ var _lib_database_optimization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database-optimization */ \"(rsc)/./src/lib/database-optimization.ts\");\n\n\n\n// 清理Oracle查询结果，移除循环引用\nfunction cleanOracleResult(obj) {\n    if (obj === null || obj === undefined) {\n        return obj;\n    }\n    if (typeof obj !== 'object') {\n        return obj;\n    }\n    if (Array.isArray(obj)) {\n        return obj.map((item)=>cleanOracleResult(item));\n    }\n    if (obj instanceof Date) {\n        return obj;\n    }\n    if (Buffer.isBuffer(obj)) {\n        return obj;\n    }\n    // 创建新对象，只复制基本属性\n    const cleanObj = {};\n    for (const [key, value] of Object.entries(obj)){\n        // 跳过Oracle内部属性和可能的循环引用\n        if (typeof key === 'string' && (key.startsWith('_') || key.toLowerCase().includes('connection') || key.toLowerCase().includes('pool') || key.toLowerCase().includes('client') || key.toLowerCase().includes('socket') || key.toLowerCase().includes('stream') || key.toLowerCase().includes('cursor') || key.toLowerCase().includes('resultset') || key === 'domain' || key === 'constructor' || key === 'prototype')) {\n            continue;\n        }\n        try {\n            cleanObj[key] = cleanOracleResult(value);\n        } catch (error) {\n            continue;\n        }\n    }\n    return cleanObj;\n}\n/**\n * 获取医疗案例列表（分页）- 优化版本\n */ async function getMedicalCases(params) {\n    try {\n        // 使用优化的查询方法\n        const optimizedParams = {\n            page: params.page || 1,\n            pageSize: params.pageSize || 10,\n            sortBy: params.sortBy || 'CREATED_AT',\n            sortOrder: params.sortOrder || 'DESC',\n            useCache: true,\n            cacheTTL: 300000,\n            filters: {\n                caseType: params.caseType,\n                medicalCategory: params.medicalCategory,\n                hospitalCode: params.hospitalCode,\n                patientName: params.search,\n                patientIdCard: params.search?.length === 18 ? params.search : undefined,\n                startDate: params.admissionDateStart,\n                endDate: params.admissionDateEnd,\n                minCost: params.totalCostMin,\n                maxCost: params.totalCostMax\n            }\n        };\n        // 过滤掉undefined值\n        Object.keys(optimizedParams.filters).forEach((key)=>{\n            if (optimizedParams.filters[key] === undefined) {\n                delete optimizedParams.filters[key];\n            }\n        });\n        const optimizedResult = await (0,_lib_database_optimization__WEBPACK_IMPORTED_MODULE_2__.getOptimizedMedicalCases)(optimizedParams);\n        return {\n            items: optimizedResult.data.map(cleanOracleResult),\n            total: optimizedResult.total,\n            page: optimizedResult.page,\n            pageSize: optimizedResult.pageSize,\n            totalPages: optimizedResult.totalPages\n        };\n    } catch (error) {\n        console.error('获取医疗案例失败:', error);\n        throw new Error('获取医疗案例失败');\n    }\n}\n/**\n * 根据ID获取医疗案例详情（完整版本，包含所有关联数据）\n */ async function getMedicalCaseById(caseId) {\n    try {\n        const sql = `\n      SELECT\n        ID,\n        CASE_NUMBER,\n        PATIENT_NAME,\n        PATIENT_ID_CARD,\n        PATIENT_PHONE,\n        PATIENT_AGE,\n        PATIENT_GENDER,\n        CASE_TYPE,\n        MEDICAL_CATEGORY,\n        HOSPITAL_NAME,\n        HOSPITAL_CODE,\n        DEPARTMENT,\n        DOCTOR_NAME,\n        ADMISSION_DATE,\n        DISCHARGE_DATE,\n        TOTAL_COST,\n        IS_DELETED,\n        CREATED_AT,\n        UPDATED_AT,\n        CREATED_BY,\n        UPDATED_BY\n      FROM MEDICAL_CASE\n      WHERE ID = :caseId AND IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database_optimizer__WEBPACK_IMPORTED_MODULE_1__.executeQueryWithCache)(sql, {\n            caseId\n        }, `medical_case_${caseId}`, _lib_database_optimizer__WEBPACK_IMPORTED_MODULE_1__.CACHE_CONFIG.CACHE_TTL.MEDICAL_CASE);\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        // 确保数据是纯净的JSON对象，避免循环引用\n        const row = result.rows[0];\n        const medicalCase = {\n            id: row.ID,\n            caseNumber: row.CASE_NUMBER,\n            patientName: row.PATIENT_NAME || '未知患者',\n            patientIdCard: row.PATIENT_ID_CARD || '未提供',\n            patientPhone: row.PATIENT_PHONE || '未提供',\n            patientAge: row.PATIENT_AGE || 0,\n            patientGender: row.PATIENT_GENDER || 'UNKNOWN',\n            caseType: row.CASE_TYPE,\n            medicalCategory: row.MEDICAL_CATEGORY,\n            hospitalName: row.HOSPITAL_NAME || '未指定医院',\n            hospitalCode: row.HOSPITAL_CODE || '',\n            department: row.DEPARTMENT || '未指定科室',\n            doctorName: row.DOCTOR_NAME || '未指定',\n            admissionDate: row.ADMISSION_DATE ? new Date(row.ADMISSION_DATE).toISOString().split('T')[0] : undefined,\n            dischargeDate: row.DISCHARGE_DATE ? new Date(row.DISCHARGE_DATE).toISOString().split('T')[0] : undefined,\n            totalCost: row.TOTAL_COST && !isNaN(row.TOTAL_COST) ? row.TOTAL_COST : 0,\n            isDeleted: row.IS_DELETED === 1,\n            createdAt: row.CREATED_AT,\n            updatedAt: row.UPDATED_AT,\n            createdBy: row.CREATED_BY,\n            updatedBy: row.UPDATED_BY\n        };\n        // 获取关联数据\n        medicalCase.diagnoses = await getMedicalDiagnoses(caseId);\n        medicalCase.surgeries = await getMedicalSurgeries(caseId);\n        medicalCase.costDetails = await getMedicalCostDetails(caseId);\n        medicalCase.settlements = await getMedicalSettlements(caseId);\n        medicalCase.caseGroups = await getMedicalCaseGroups(caseId);\n        // 清理数据，移除可能的循环引用\n        return cleanOracleResult(medicalCase);\n    } catch (error) {\n        console.error('❌ 获取医疗案例详情失败:', error instanceof Error ? error.message : String(error));\n        throw new Error('获取医疗案例详情失败');\n    }\n}\n/**\n * 获取案例的诊断信息\n */ async function getMedicalDiagnoses(caseId) {\n    try {\n        const sql = `\n      SELECT \n        ID,\n        CASE_ID,\n        DIAGNOSIS_TYPE,\n        DIAGNOSIS_CODE,\n        DIAGNOSIS_NAME,\n        DIAGNOSIS_DESC,\n        IS_PRIMARY,\n        CREATED_AT,\n        UPDATED_AT,\n        CREATED_BY,\n        UPDATED_BY\n      FROM MEDICAL_DIAGNOSIS\n      WHERE CASE_ID = :caseId\n      ORDER BY IS_PRIMARY DESC, CREATED_AT ASC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId\n        });\n        // 清理数据并映射到标准格式\n        const cleanRows = cleanOracleResult(result.rows || []);\n        return cleanRows.map((row)=>({\n                id: row.ID,\n                caseId: row.CASE_ID,\n                diagnosisType: row.DIAGNOSIS_TYPE,\n                diagnosisCode: row.DIAGNOSIS_CODE,\n                diagnosisName: row.DIAGNOSIS_NAME,\n                diagnosisDesc: row.DIAGNOSIS_DESC,\n                isPrimary: row.IS_PRIMARY === 1,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT,\n                createdBy: row.CREATED_BY,\n                updatedBy: row.UPDATED_BY\n            }));\n    } catch (error) {\n        console.error('❌ 获取诊断信息失败:', error instanceof Error ? error.message : String(error));\n        return [];\n    }\n}\n/**\n * 获取案例的手术信息\n */ async function getMedicalSurgeries(caseId) {\n    try {\n        const sql = `\n      SELECT\n        ID,\n        CASE_ID,\n        SURGERY_CODE,\n        SURGERY_NAME,\n        SURGERY_DATE,\n        SURGEON_NAME,\n        ANESTHESIA_TYPE,\n        SURGERY_LEVEL,\n        SURGERY_DURATION,\n        SURGERY_NOTES,\n        CREATED_AT,\n        UPDATED_AT,\n        CREATED_BY,\n        UPDATED_BY\n      FROM MEDICAL_SURGERY\n      WHERE CASE_ID = :caseId\n      ORDER BY SURGERY_DATE ASC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId\n        });\n        // 清理数据并映射到标准格式\n        const cleanRows = cleanOracleResult(result.rows || []);\n        return cleanRows.map((row)=>({\n                id: row.ID,\n                caseId: row.CASE_ID,\n                surgeryCode: row.SURGERY_CODE,\n                surgeryName: row.SURGERY_NAME,\n                surgeryDate: row.SURGERY_DATE ? new Date(row.SURGERY_DATE).toISOString().split('T')[0] : '',\n                surgeonName: row.SURGEON_NAME,\n                anesthesiaType: row.ANESTHESIA_TYPE,\n                surgeryLevel: row.SURGERY_LEVEL,\n                surgeryDuration: row.SURGERY_DURATION,\n                surgeryNotes: row.SURGERY_NOTES,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT,\n                createdBy: row.CREATED_BY,\n                updatedBy: row.UPDATED_BY\n            }));\n    } catch (error) {\n        console.error('❌ 获取手术信息失败:', error instanceof Error ? error.message : String(error));\n        return [];\n    }\n}\n/**\n * 获取案例的费用明细\n */ async function getMedicalCostDetails(caseId) {\n    try {\n        const sql = `\n      SELECT\n        ID,\n        CASE_ID,\n        ITEM_CODE,\n        ITEM_NAME,\n        INSURANCE_ITEM_CODE,\n        INSURANCE_ITEM_NAME,\n        ITEM_TYPE,\n        UNIT_PRICE,\n        QUANTITY,\n        TOTAL_AMOUNT,\n        COMPLIANT_AMOUNT,\n        CHARGED_AT,\n        DEPARTMENT,\n        DOCTOR_NAME,\n        CREATED_AT,\n        UPDATED_AT,\n        CREATED_BY,\n        UPDATED_BY\n      FROM MEDICAL_COST_DETAIL\n      WHERE CASE_ID = :caseId\n      ORDER BY CHARGED_AT DESC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId\n        });\n        // 清理数据并映射到标准格式\n        const cleanRows = cleanOracleResult(result.rows || []);\n        return cleanRows.map((row)=>({\n                id: row.ID,\n                caseId: row.CASE_ID,\n                itemCode: row.ITEM_CODE,\n                itemName: row.ITEM_NAME,\n                insuranceItemCode: row.INSURANCE_ITEM_CODE,\n                insuranceItemName: row.INSURANCE_ITEM_NAME,\n                itemType: row.ITEM_TYPE,\n                unitPrice: row.UNIT_PRICE,\n                quantity: row.QUANTITY,\n                totalAmount: row.TOTAL_AMOUNT,\n                compliantAmount: row.COMPLIANT_AMOUNT,\n                chargedAt: row.CHARGED_AT,\n                department: row.DEPARTMENT,\n                doctorName: row.DOCTOR_NAME,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT,\n                createdBy: row.CREATED_BY,\n                updatedBy: row.UPDATED_BY\n            }));\n    } catch (error) {\n        console.error('❌ 获取费用明细失败:', error instanceof Error ? error.message : String(error));\n        return [];\n    }\n}\n/**\n * 获取案例的结算信息\n */ async function getMedicalSettlements(caseId) {\n    try {\n        const sql = `\n      SELECT\n        ID,\n        CASE_ID,\n        SETTLEMENT_NUMBER,\n        TOTAL_MEDICAL_COST,\n        FULL_SELF_PAY_AMOUNT,\n        OVER_LIMIT_SELF_PAY_AMOUNT,\n        ADVANCE_SELF_PAY_AMOUNT,\n        ELIGIBLE_AMOUNT,\n        ACTUAL_DEDUCTIBLE,\n        BASIC_MEDICAL_PAY_RATIO,\n        TOTAL_FUND_PAYMENT,\n        POOLING_FUND_PAYMENT,\n        POLICY_RANGE_SELF_PAY,\n        OUT_OF_POLICY_RANGE_AMOUNT,\n        IS_REFUND,\n        IS_VALID,\n        MEDIUM_TYPE,\n        SETTLEMENT_STAFF_CODE,\n        SETTLEMENT_STAFF_NAME,\n        SETTLED_AT,\n        IS_DELETED,\n        CREATED_AT,\n        UPDATED_AT,\n        CREATED_BY,\n        UPDATED_BY\n      FROM MEDICAL_SETTLEMENT\n      WHERE CASE_ID = :caseId AND IS_DELETED = 0\n      ORDER BY SETTLED_AT DESC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId\n        });\n        // 清理数据并映射到标准格式\n        const cleanRows = cleanOracleResult(result.rows || []);\n        return cleanRows.map((row)=>({\n                id: row.ID,\n                caseId: row.CASE_ID,\n                settlementNumber: row.SETTLEMENT_NUMBER,\n                totalMedicalCost: row.TOTAL_MEDICAL_COST,\n                fullSelfPayAmount: row.FULL_SELF_PAY_AMOUNT,\n                overLimitSelfPayAmount: row.OVER_LIMIT_SELF_PAY_AMOUNT,\n                advanceSelfPayAmount: row.ADVANCE_SELF_PAY_AMOUNT,\n                eligibleAmount: row.ELIGIBLE_AMOUNT,\n                actualDeductible: row.ACTUAL_DEDUCTIBLE,\n                basicMedicalPayRatio: row.BASIC_MEDICAL_PAY_RATIO,\n                totalFundPayment: row.TOTAL_FUND_PAYMENT,\n                poolingFundPayment: row.POOLING_FUND_PAYMENT,\n                policyRangeSelfPay: row.POLICY_RANGE_SELF_PAY,\n                outOfPolicyRangeAmount: row.OUT_OF_POLICY_RANGE_AMOUNT,\n                isRefund: row.IS_REFUND === 1,\n                isValid: row.IS_VALID === 1,\n                mediumType: row.MEDIUM_TYPE,\n                settlementStaffCode: row.SETTLEMENT_STAFF_CODE,\n                settlementStaffName: row.SETTLEMENT_STAFF_NAME,\n                settledAt: row.SETTLED_AT,\n                isDeleted: row.IS_DELETED === 1,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT,\n                createdBy: row.CREATED_BY,\n                updatedBy: row.UPDATED_BY\n            }));\n    } catch (error) {\n        console.error('❌ 获取结算信息失败:', error instanceof Error ? error.message : String(error));\n        return [];\n    }\n}\n/**\n * 获取案例的分组信息\n */ async function getMedicalCaseGroups(caseId) {\n    try {\n        const sql = `\n      SELECT\n        ID, CASE_ID, GROUP_CODE, GROUP_NAME, GROUP_WEIGHT, GROUP_RATE,\n        MONTHLY_PAYMENT_STANDARD, SETTLEMENT_PAYMENT_STANDARD, GROUP_TYPE,\n        IS_VALID, GROUPED_AT, IS_DELETED, CREATED_AT, UPDATED_AT, CREATED_BY, UPDATED_BY\n      FROM MEDICAL_CASE_GROUP\n      WHERE CASE_ID = :caseId AND IS_DELETED = 0\n      ORDER BY GROUPED_AT DESC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId\n        });\n        // 清理数据并映射到标准格式\n        const cleanRows = cleanOracleResult(result.rows || []);\n        return cleanRows.map((row)=>({\n                id: row.ID,\n                caseId: row.CASE_ID,\n                groupCode: row.GROUP_CODE,\n                groupName: row.GROUP_NAME,\n                groupWeight: row.GROUP_WEIGHT,\n                groupRate: row.GROUP_RATE,\n                monthlyPaymentStandard: row.MONTHLY_PAYMENT_STANDARD,\n                settlementPaymentStandard: row.SETTLEMENT_PAYMENT_STANDARD,\n                groupType: row.GROUP_TYPE,\n                isValid: row.IS_VALID === 1,\n                groupedAt: row.GROUPED_AT,\n                isDeleted: row.IS_DELETED === 1,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT,\n                createdBy: row.CREATED_BY,\n                updatedBy: row.UPDATED_BY\n            }));\n    } catch (error) {\n        console.error('❌ 获取分组信息失败:', error instanceof Error ? error.message : String(error));\n        return [];\n    }\n}\n/**\n * 创建医疗案例\n */ async function createMedicalCase(caseData, createdBy) {\n    try {\n        // 先获取下一个ID\n        const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM MEDICAL_CASE`;\n        const nextIdResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(getNextIdSql);\n        const caseId = nextIdResult.rows?.[0]?.NEXT_ID || 1;\n        const sql = `\n      INSERT INTO MEDICAL_CASE (\n        ID, CASE_NUMBER, PATIENT_NAME, PATIENT_ID_CARD, PATIENT_PHONE, PATIENT_AGE,\n        PATIENT_GENDER, CASE_TYPE, MEDICAL_CATEGORY, HOSPITAL_NAME, HOSPITAL_CODE,\n        DEPARTMENT, DOCTOR_NAME, ADMISSION_DATE, DISCHARGE_DATE, TOTAL_COST,\n        IS_DELETED, CREATED_BY, UPDATED_BY\n      ) VALUES (\n        :caseId, :caseNumber, :patientName, :patientIdCard, :patientPhone, :patientAge,\n        :patientGender, :caseType, :medicalCategory, :hospitalName, :hospitalCode,\n        :department, :doctorName,\n        CASE WHEN :admissionDate IS NOT NULL THEN TO_DATE(:admissionDate, 'YYYY-MM-DD') ELSE NULL END,\n        CASE WHEN :dischargeDate IS NOT NULL THEN TO_DATE(:dischargeDate, 'YYYY-MM-DD') ELSE NULL END,\n        :totalCost, 0, :createdBy, :createdBy\n      )\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId,\n            caseNumber: caseData.caseNumber,\n            patientName: caseData.patientName,\n            patientIdCard: caseData.patientIdCard,\n            patientPhone: caseData.patientPhone,\n            patientAge: caseData.patientAge,\n            patientGender: caseData.patientGender,\n            caseType: caseData.caseType,\n            medicalCategory: caseData.medicalCategory,\n            hospitalName: caseData.hospitalName,\n            hospitalCode: caseData.hospitalCode,\n            department: caseData.department,\n            doctorName: caseData.doctorName,\n            admissionDate: caseData.admissionDate,\n            dischargeDate: caseData.dischargeDate,\n            totalCost: caseData.totalCost,\n            createdBy\n        });\n        return caseId;\n    } catch (error) {\n        console.error('❌ 创建医疗案例失败:', error instanceof Error ? error.message : String(error));\n        throw new Error('创建医疗案例失败');\n    }\n}\n/**\n * 更新医疗案例\n */ async function updateMedicalCase(caseId, updateData, updatedBy) {\n    try {\n        const updateFields = [];\n        const params = {\n            caseId,\n            updatedBy\n        };\n        if (updateData.patientName !== undefined) {\n            updateFields.push('PATIENT_NAME = :patientName');\n            params.patientName = updateData.patientName;\n        }\n        if (updateData.patientPhone !== undefined) {\n            updateFields.push('PATIENT_PHONE = :patientPhone');\n            params.patientPhone = updateData.patientPhone;\n        }\n        if (updateData.patientAge !== undefined) {\n            updateFields.push('PATIENT_AGE = :patientAge');\n            params.patientAge = updateData.patientAge;\n        }\n        if (updateData.patientGender !== undefined) {\n            updateFields.push('PATIENT_GENDER = :patientGender');\n            params.patientGender = updateData.patientGender;\n        }\n        if (updateData.medicalCategory !== undefined) {\n            updateFields.push('MEDICAL_CATEGORY = :medicalCategory');\n            params.medicalCategory = updateData.medicalCategory;\n        }\n        if (updateData.hospitalName !== undefined) {\n            updateFields.push('HOSPITAL_NAME = :hospitalName');\n            params.hospitalName = updateData.hospitalName;\n        }\n        if (updateData.hospitalCode !== undefined) {\n            updateFields.push('HOSPITAL_CODE = :hospitalCode');\n            params.hospitalCode = updateData.hospitalCode;\n        }\n        if (updateData.department !== undefined) {\n            updateFields.push('DEPARTMENT = :department');\n            params.department = updateData.department;\n        }\n        if (updateData.doctorName !== undefined) {\n            updateFields.push('DOCTOR_NAME = :doctorName');\n            params.doctorName = updateData.doctorName;\n        }\n        if (updateData.admissionDate !== undefined) {\n            updateFields.push('ADMISSION_DATE = TO_DATE(:admissionDate, \\'YYYY-MM-DD\\')');\n            params.admissionDate = updateData.admissionDate;\n        }\n        if (updateData.dischargeDate !== undefined) {\n            updateFields.push('DISCHARGE_DATE = TO_DATE(:dischargeDate, \\'YYYY-MM-DD\\')');\n            params.dischargeDate = updateData.dischargeDate;\n        }\n        if (updateData.totalCost !== undefined) {\n            updateFields.push('TOTAL_COST = :totalCost');\n            params.totalCost = updateData.totalCost;\n        }\n        if (updateFields.length === 0) {\n            throw new Error('没有提供要更新的字段');\n        }\n        updateFields.push('UPDATED_BY = :updatedBy');\n        updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP');\n        const sql = `\n      UPDATE MEDICAL_CASE\n      SET ${updateFields.join(', ')}\n      WHERE ID = :caseId AND IS_DELETED = 0\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, params);\n    } catch (error) {\n        console.error('❌ 更新医疗案例失败:', error instanceof Error ? error.message : String(error));\n        throw new Error('更新医疗案例失败');\n    }\n}\n/**\n * 删除医疗案例（软删除）\n */ async function deleteMedicalCase(caseId, deletedBy) {\n    try {\n        const sql = `\n      UPDATE MEDICAL_CASE\n      SET IS_DELETED = 1, UPDATED_BY = :deletedBy, UPDATED_AT = CURRENT_TIMESTAMP\n      WHERE ID = :caseId AND IS_DELETED = 0\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            caseId,\n            deletedBy\n        });\n    } catch (error) {\n        console.error('❌ 删除医疗案例失败:', error);\n        throw new Error('删除医疗案例失败');\n    }\n}\n/**\n * 获取医疗案例统计信息 - 优化版本\n */ async function getMedicalCaseStatistics() {\n    try {\n        // 使用优化的统计查询\n        const basicStats = await (0,_lib_database_optimization__WEBPACK_IMPORTED_MODULE_2__.getOptimizedStatistics)('medical_cases_overview');\n        // 按类型统计\n        const typeStatsSql = `\n      SELECT CASE_TYPE, COUNT(*) as COUNT\n      FROM MEDICAL_CASE\n      WHERE IS_DELETED = 0\n      GROUP BY CASE_TYPE\n    `;\n        const typeStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(typeStatsSql);\n        const casesByType = {\n            'INPATIENT': 0,\n            'OUTPATIENT': 0\n        };\n        typeStatsResult.rows?.forEach((row)=>{\n            casesByType[row.CASE_TYPE] = row.COUNT;\n        });\n        // 按医疗类别统计\n        const categoryStatsSql = `\n      SELECT MEDICAL_CATEGORY, COUNT(*) as COUNT\n      FROM MEDICAL_CASE\n      WHERE IS_DELETED = 0\n      GROUP BY MEDICAL_CATEGORY\n    `;\n        const categoryStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(categoryStatsSql);\n        const casesByCategory = {};\n        categoryStatsResult.rows?.forEach((row)=>{\n            casesByCategory[row.MEDICAL_CATEGORY] = row.COUNT;\n        });\n        // 按性别统计\n        const genderStatsSql = `\n      SELECT PATIENT_GENDER, COUNT(*) as COUNT\n      FROM MEDICAL_CASE\n      WHERE IS_DELETED = 0 AND PATIENT_GENDER IS NOT NULL\n      GROUP BY PATIENT_GENDER\n    `;\n        const genderStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(genderStatsSql);\n        const casesByGender = {\n            'MALE': 0,\n            'FEMALE': 0,\n            'OTHER': 0\n        };\n        genderStatsResult.rows?.forEach((row)=>{\n            casesByGender[row.PATIENT_GENDER] = row.COUNT;\n        });\n        // 月度趋势\n        const monthlyTrendSql = `\n      SELECT\n        TO_CHAR(CREATED_AT, 'YYYY-MM') as MONTH,\n        COUNT(*) as CASE_COUNT,\n        SUM(TOTAL_COST) as TOTAL_COST,\n        AVG(TOTAL_COST) as AVG_COST\n      FROM MEDICAL_CASE\n      WHERE IS_DELETED = 0\n        AND CREATED_AT >= ADD_MONTHS(CURRENT_TIMESTAMP, -12)\n      GROUP BY TO_CHAR(CREATED_AT, 'YYYY-MM')\n      ORDER BY MONTH\n    `;\n        const monthlyTrendResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(monthlyTrendSql);\n        const monthlyTrend = (monthlyTrendResult.rows || []).map((row)=>({\n                month: row.MONTH,\n                caseCount: row.CASE_COUNT,\n                totalCost: row.TOTAL_COST,\n                avgCost: row.AVG_COST\n            }));\n        // 医院统计\n        const hospitalStatsSql = `\n      SELECT\n        HOSPITAL_NAME,\n        HOSPITAL_CODE,\n        COUNT(*) as CASE_COUNT,\n        SUM(TOTAL_COST) as TOTAL_COST\n      FROM MEDICAL_CASE\n      WHERE IS_DELETED = 0\n      GROUP BY HOSPITAL_NAME, HOSPITAL_CODE\n      ORDER BY CASE_COUNT DESC\n      FETCH FIRST 10 ROWS ONLY\n    `;\n        const hospitalStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(hospitalStatsSql);\n        const hospitalStats = (hospitalStatsResult.rows || []).map((row)=>({\n                hospitalName: row.HOSPITAL_NAME,\n                hospitalCode: row.HOSPITAL_CODE,\n                caseCount: row.CASE_COUNT,\n                totalCost: row.TOTAL_COST\n            }));\n        return {\n            totalCases: basicStats.TOTAL_CASES || 0,\n            inpatientCases: basicStats.INPATIENT_CASES || 0,\n            outpatientCases: basicStats.OUTPATIENT_CASES || 0,\n            totalCost: basicStats.TOTAL_COST || 0,\n            avgCostPerCase: basicStats.AVG_COST || 0,\n            casesByType,\n            casesByCategory,\n            casesByGender,\n            monthlyTrend,\n            hospitalStats\n        };\n    } catch (error) {\n        console.error('❌ 获取医疗案例统计失败:', error);\n        throw new Error('获取医疗案例统计失败');\n    }\n}\n/**\n * 批量删除医疗案例（软删除）\n */ async function batchDeleteMedicalCases(ids, userId) {\n    try {\n        // 将字符串 ID 转换为数字\n        const numericIds = ids.map((id)=>parseInt(id, 10)).filter((id)=>!isNaN(id));\n        if (numericIds.length === 0) {\n            return {\n                success: false,\n                error: '无效的案例ID列表'\n            };\n        }\n        // 构建 IN 子句的占位符\n        const placeholders = numericIds.map((_, index)=>`:id${index}`).join(', ');\n        const queryParams = {\n            userId: parseInt(userId, 10)\n        };\n        // 添加 ID 参数\n        numericIds.forEach((id, index)=>{\n            queryParams[`id${index}`] = id;\n        });\n        const deleteQuery = `\n      UPDATE MEDICAL_CASE_MEDICAL_CASE\n      SET IS_DELETED = 1,\n          UPDATED_BY = :userId,\n          UPDATED_AT = CURRENT_TIMESTAMP\n      WHERE ID IN (${placeholders}) AND IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(deleteQuery, queryParams);\n        return {\n            success: true,\n            data: {\n                deletedCount: result.rowsAffected || 0\n            }\n        };\n    } catch (error) {\n        console.error('❌ 批量删除案例失败:', error);\n        return {\n            success: false,\n            error: '批量删除案例失败'\n        };\n    }\n}\n/**\n * 批量归档医疗案例\n */ async function batchArchiveMedicalCases(ids, userId) {\n    try {\n        // 将字符串 ID 转换为数字\n        const numericIds = ids.map((id)=>parseInt(id, 10)).filter((id)=>!isNaN(id));\n        if (numericIds.length === 0) {\n            return {\n                success: false,\n                error: '无效的案例ID列表'\n            };\n        }\n        // 构建 IN 子句的占位符\n        const placeholders = numericIds.map((_, index)=>`:id${index}`).join(', ');\n        const queryParams = {\n            userId: parseInt(userId, 10)\n        };\n        // 添加 ID 参数\n        numericIds.forEach((id, index)=>{\n            queryParams[`id${index}`] = id;\n        });\n        const archiveQuery = `\n      UPDATE MEDICAL_CASE_MEDICAL_CASE\n      SET STATUS = 'ARCHIVED',\n          UPDATED_BY = :userId,\n          UPDATED_AT = CURRENT_TIMESTAMP\n      WHERE ID IN (${placeholders}) AND IS_DELETED = 0 AND STATUS != 'ARCHIVED'\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(archiveQuery, queryParams);\n        return {\n            success: true,\n            data: {\n                archivedCount: result.rowsAffected || 0\n            }\n        };\n    } catch (error) {\n        console.error('❌ 批量归档案例失败:', error);\n        return {\n            success: false,\n            error: '批量归档案例失败'\n        };\n    }\n}\n/**\n * 导出医疗案例\n */ async function exportMedicalCases(ids, format) {\n    try {\n        // 将字符串 ID 转换为数字\n        const numericIds = ids.map((id)=>parseInt(id, 10)).filter((id)=>!isNaN(id));\n        if (numericIds.length === 0) {\n            return {\n                success: false,\n                error: '无效的案例ID列表'\n            };\n        }\n        // 获取案例数据\n        const placeholders = numericIds.map((_, index)=>`:id${index}`).join(', ');\n        const queryParams = {};\n        // 添加 ID 参数\n        numericIds.forEach((id, index)=>{\n            queryParams[`id${index}`] = id;\n        });\n        const exportQuery = `\n      SELECT\n        ID,\n        CASE_NUMBER,\n        PATIENT_NAME,\n        PATIENT_ID_CARD,\n        PATIENT_PHONE,\n        PATIENT_AGE,\n        PATIENT_GENDER,\n        CASE_TYPE,\n        MEDICAL_CATEGORY,\n        HOSPITAL_NAME,\n        HOSPITAL_CODE,\n        DEPARTMENT,\n        DOCTOR_NAME,\n        ADMISSION_DATE,\n        DISCHARGE_DATE,\n        TOTAL_COST,\n        DESCRIPTION,\n        STATUS,\n        CREATED_AT\n      FROM MEDICAL_CASE\n      WHERE ID IN (${placeholders}) AND IS_DELETED = 0\n      ORDER BY CREATED_AT DESC\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(exportQuery, queryParams);\n        const cases = result.rows || [];\n        if (cases.length === 0) {\n            return {\n                success: false,\n                error: '没有找到要导出的案例'\n            };\n        }\n        // 根据格式生成文件\n        const timestamp = new Date().toISOString().split('T')[0];\n        switch(format){\n            case 'csv':\n                return generateCSV(cases, timestamp || new Date().toISOString());\n            case 'excel':\n                return generateExcel(cases, timestamp || new Date().toISOString());\n            case 'pdf':\n                return generatePDF(cases, timestamp || new Date().toISOString());\n            default:\n                return {\n                    success: false,\n                    error: '不支持的导出格式'\n                };\n        }\n    } catch (error) {\n        console.error('❌ 导出案例失败:', error);\n        return {\n            success: false,\n            error: '导出案例失败'\n        };\n    }\n}\n// CSV 导出\nfunction generateCSV(cases, timestamp) {\n    try {\n        const headers = [\n            '案例编号',\n            '患者姓名',\n            '身份证号',\n            '联系电话',\n            '年龄',\n            '性别',\n            '案例类型',\n            '医疗类别',\n            '医院名称',\n            '医院编码',\n            '科室',\n            '主治医生',\n            '入院日期',\n            '出院日期',\n            '总费用',\n            '描述',\n            '状态',\n            '创建时间'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...cases.map((c)=>[\n                    c.CASE_NUMBER || '',\n                    c.PATIENT_NAME || '',\n                    c.PATIENT_ID_CARD || '',\n                    c.PATIENT_PHONE || '',\n                    c.PATIENT_AGE || '',\n                    c.PATIENT_GENDER === 'MALE' ? '男' : c.PATIENT_GENDER === 'FEMALE' ? '女' : '其他',\n                    c.CASE_TYPE === 'INPATIENT' ? '住院' : '门诊',\n                    c.MEDICAL_CATEGORY || '',\n                    c.HOSPITAL_NAME || '',\n                    c.HOSPITAL_CODE || '',\n                    c.DEPARTMENT || '',\n                    c.DOCTOR_NAME || '',\n                    c.ADMISSION_DATE ? new Date(c.ADMISSION_DATE).toLocaleDateString() : '',\n                    c.DISCHARGE_DATE ? new Date(c.DISCHARGE_DATE).toLocaleDateString() : '',\n                    c.TOTAL_COST || '',\n                    c.DESCRIPTION || '',\n                    c.STATUS || '',\n                    c.CREATED_AT ? new Date(c.CREATED_AT).toLocaleString() : ''\n                ].map((field)=>`\"${String(field).replace(/\"/g, '\"\"')}\"`).join(','))\n        ].join('\\n');\n        const buffer = Buffer.from('\\uFEFF' + csvContent, 'utf8') // 添加 BOM 以支持中文\n        ;\n        return {\n            success: true,\n            data: {\n                buffer,\n                contentType: 'text/csv; charset=utf-8',\n                filename: `medical-cases-${timestamp}.csv`\n            }\n        };\n    } catch (error) {\n        console.error('❌ 生成CSV失败:', error);\n        return {\n            success: false,\n            error: '生成CSV文件失败'\n        };\n    }\n}\n// Excel 导出（简化版，实际项目中可以使用 xlsx 库）\nfunction generateExcel(cases, timestamp) {\n    try {\n        // 这里使用简化的 Excel 格式（实际上是 CSV 格式，但浏览器会用 Excel 打开）\n        const headers = [\n            '案例编号',\n            '患者姓名',\n            '身份证号',\n            '联系电话',\n            '年龄',\n            '性别',\n            '案例类型',\n            '医疗类别',\n            '医院名称',\n            '医院编码',\n            '科室',\n            '主治医生',\n            '入院日期',\n            '出院日期',\n            '总费用',\n            '描述',\n            '状态',\n            '创建时间'\n        ];\n        const csvContent = [\n            headers.join('\\t'),\n            ...cases.map((c)=>[\n                    c.CASE_NUMBER || '',\n                    c.PATIENT_NAME || '',\n                    c.PATIENT_ID_CARD || '',\n                    c.PATIENT_PHONE || '',\n                    c.PATIENT_AGE || '',\n                    c.PATIENT_GENDER === 'MALE' ? '男' : c.PATIENT_GENDER === 'FEMALE' ? '女' : '其他',\n                    c.CASE_TYPE === 'INPATIENT' ? '住院' : '门诊',\n                    c.MEDICAL_CATEGORY || '',\n                    c.HOSPITAL_NAME || '',\n                    c.HOSPITAL_CODE || '',\n                    c.DEPARTMENT || '',\n                    c.DOCTOR_NAME || '',\n                    c.ADMISSION_DATE ? new Date(c.ADMISSION_DATE).toLocaleDateString() : '',\n                    c.DISCHARGE_DATE ? new Date(c.DISCHARGE_DATE).toLocaleDateString() : '',\n                    c.TOTAL_COST || '',\n                    c.DESCRIPTION || '',\n                    c.STATUS || '',\n                    c.CREATED_AT ? new Date(c.CREATED_AT).toLocaleString() : ''\n                ].join('\\t'))\n        ].join('\\n');\n        const buffer = Buffer.from('\\uFEFF' + csvContent, 'utf8');\n        return {\n            success: true,\n            data: {\n                buffer,\n                contentType: 'application/vnd.ms-excel',\n                filename: `medical-cases-${timestamp}.xls`\n            }\n        };\n    } catch (error) {\n        console.error('❌ 生成Excel失败:', error);\n        return {\n            success: false,\n            error: '生成Excel文件失败'\n        };\n    }\n}\n// PDF 导出（简化版）\nfunction generatePDF(cases, timestamp) {\n    try {\n        // 简化的 PDF 内容（实际项目中可以使用 pdfkit 或其他 PDF 库）\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>医疗案例导出</title>\n        <style>\n          body { font-family: Arial, sans-serif; margin: 20px; }\n          h1 { color: #333; text-align: center; }\n          table { width: 100%; border-collapse: collapse; margin-top: 20px; }\n          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n          th { background-color: #f2f2f2; font-weight: bold; }\n          tr:nth-child(even) { background-color: #f9f9f9; }\n        </style>\n      </head>\n      <body>\n        <h1>医疗案例导出报告</h1>\n        <p>导出时间: ${new Date().toLocaleString()}</p>\n        <p>案例数量: ${cases.length}</p>\n        <table>\n          <thead>\n            <tr>\n              <th>案例编号</th>\n              <th>患者姓名</th>\n              <th>性别</th>\n              <th>年龄</th>\n              <th>案例类型</th>\n              <th>医院名称</th>\n              <th>总费用</th>\n              <th>状态</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${cases.map((c)=>`\n              <tr>\n                <td>${c.CASE_NUMBER || ''}</td>\n                <td>${c.PATIENT_NAME || ''}</td>\n                <td>${c.PATIENT_GENDER === 'MALE' ? '男' : c.PATIENT_GENDER === 'FEMALE' ? '女' : '其他'}</td>\n                <td>${c.PATIENT_AGE || ''}</td>\n                <td>${c.CASE_TYPE === 'INPATIENT' ? '住院' : '门诊'}</td>\n                <td>${c.HOSPITAL_NAME || ''}</td>\n                <td>${c.TOTAL_COST || ''}</td>\n                <td>${c.STATUS || ''}</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n      </body>\n      </html>\n    `;\n        const buffer = Buffer.from(htmlContent, 'utf8');\n        return {\n            success: true,\n            data: {\n                buffer,\n                contentType: 'text/html',\n                filename: `medical-cases-${timestamp}.html`\n            }\n        };\n    } catch (error) {\n        console.error('❌ 生成PDF失败:', error);\n        return {\n            success: false,\n            error: '生成PDF文件失败'\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/medical-case-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/monitoring.ts":
/*!*******************************!*\
  !*** ./src/lib/monitoring.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   monitoring: () => (/* binding */ monitoring),\n/* harmony export */   recordMetric: () => (/* binding */ recordMetric),\n/* harmony export */   startTimer: () => (/* binding */ startTimer),\n/* harmony export */   withPerformanceMonitoring: () => (/* binding */ withPerformanceMonitoring)\n/* harmony export */ });\n/**\n * 企业级监控和可观测性服务\n */ class MonitoringService {\n    /**\n   * 记录业务指标\n   */ recordMetric(metric) {\n        this.metrics.push({\n            ...metric,\n            timestamp: Date.now()\n        });\n        // 在生产环境中，这里应该发送到监控系统（如 Prometheus、DataDog）\n        if (false) {}\n    }\n    /**\n   * 记录结构化日志\n   */ log(entry) {\n        const logEntry = {\n            ...entry,\n            timestamp: Date.now()\n        };\n        this.logs.push(logEntry);\n        // 控制台输出（开发环境）\n        if (true) {\n            const emoji = this.getLogEmoji(entry.level);\n            console.log(`${emoji} [${entry.level.toUpperCase()}] ${entry.message}`, entry.context || '');\n        }\n        // 生产环境发送到日志聚合系统（如 ELK、Splunk）\n        if (false) {}\n    }\n    /**\n   * 记录性能指标\n   */ recordPerformance(metric) {\n        const perfMetric = {\n            ...metric,\n            timestamp: Date.now()\n        };\n        this.performances.push(perfMetric);\n        // 性能告警\n        if (metric.duration > 5000) {\n            this.log({\n                level: 'warn',\n                message: `Slow operation detected: ${metric.operation}`,\n                context: {\n                    duration: metric.duration,\n                    ...metric.metadata\n                }\n            });\n        }\n        // 发送到APM系统\n        if (false) {}\n    }\n    /**\n   * 创建性能计时器\n   */ startTimer(operation, metadata) {\n        const startTime = Date.now();\n        return {\n            end: (success = true, errorMessage)=>{\n                const duration = Date.now() - startTime;\n                this.recordPerformance({\n                    operation,\n                    duration,\n                    success,\n                    errorMessage,\n                    metadata\n                });\n                return duration;\n            }\n        };\n    }\n    /**\n   * 健康检查\n   */ async healthCheck() {\n        const checks = {};\n        // 数据库连接检查\n        try {\n            const dbTimer = this.startTimer('health_check_database');\n            // 这里应该实际检查数据库连接\n            await new Promise((resolve)=>setTimeout(resolve, 10)); // 模拟检查\n            const latency = dbTimer.end(true);\n            checks['database'] = {\n                status: 'healthy',\n                latency\n            };\n        } catch (error) {\n            checks['database'] = {\n                status: 'unhealthy',\n                error: error.message\n            };\n        }\n        // 内存使用检查（仅在 Node.js 环境中可用）\n        try {\n            // 使用动态访问避免 webpack 静态分析\n            const processObj = typeof process !== 'undefined' ? process : null;\n            const memoryUsageFn = processObj && processObj['memoryUsage'];\n            if (memoryUsageFn && typeof memoryUsageFn === 'function') {\n                const memUsage = memoryUsageFn();\n                const memUsagePercent = memUsage.heapUsed / memUsage.heapTotal * 100;\n                checks['memory'] = {\n                    status: memUsagePercent > 90 ? 'unhealthy' : memUsagePercent > 70 ? 'degraded' : 'healthy',\n                    usage: `${memUsagePercent.toFixed(2)}%`\n                };\n            } else {\n                checks['memory'] = {\n                    status: 'unknown',\n                    error: 'Memory usage check not available in this environment'\n                };\n            }\n        } catch (error) {\n            checks['memory'] = {\n                status: 'unknown',\n                error: 'Memory usage check failed'\n            };\n        }\n        // 确定整体状态\n        const hasUnhealthy = Object.values(checks).some((check)=>check.status === 'unhealthy');\n        const hasDegraded = Object.values(checks).some((check)=>check.status === 'degraded');\n        const status = hasUnhealthy ? 'unhealthy' : hasDegraded ? 'degraded' : 'healthy';\n        return {\n            status,\n            checks\n        };\n    }\n    /**\n   * 获取指标摘要\n   */ getMetricsSummary(timeRange = 3600000) {\n        const now = Date.now();\n        const cutoff = now - timeRange;\n        const recentMetrics = this.metrics.filter((m)=>m.timestamp > cutoff);\n        const recentLogs = this.logs.filter((l)=>l.timestamp > cutoff);\n        const recentPerformances = this.performances.filter((p)=>p.timestamp > cutoff);\n        return {\n            metrics: {\n                total: recentMetrics.length,\n                byName: this.groupBy(recentMetrics, 'name')\n            },\n            logs: {\n                total: recentLogs.length,\n                byLevel: this.groupBy(recentLogs, 'level')\n            },\n            performance: {\n                total: recentPerformances.length,\n                avgDuration: recentPerformances.reduce((sum, p)=>sum + p.duration, 0) / recentPerformances.length || 0,\n                successRate: recentPerformances.filter((p)=>p.success).length / recentPerformances.length * 100 || 0\n            }\n        };\n    }\n    getLogEmoji(level) {\n        const emojis = {\n            debug: '🐛',\n            info: 'ℹ️',\n            warn: '⚠️',\n            error: '❌',\n            fatal: '💀'\n        };\n        return emojis[level] || 'ℹ️';\n    }\n    groupBy(array, key) {\n        return array.reduce((groups, item)=>{\n            const group = String(item[key]);\n            groups[group] = (groups[group] || 0) + 1;\n            return groups;\n        }, {});\n    }\n    async sendToMonitoringSystem(type, data) {\n    // 实现发送到监控系统的逻辑\n    // 例如：Prometheus、DataDog、CloudWatch\n    }\n    async sendToLoggingSystem(logEntry) {\n    // 实现发送到日志系统的逻辑\n    // 例如：ELK Stack、Splunk、Fluentd\n    }\n    async sendToAPMSystem(perfMetric) {\n    // 实现发送到APM系统的逻辑\n    // 例如：New Relic、Datadog APM、Elastic APM\n    }\n    constructor(){\n        this.metrics = [];\n        this.logs = [];\n        this.performances = [];\n    }\n}\n// 单例实例\nconst monitoring = new MonitoringService();\n// 便捷方法\nconst recordMetric = (metric)=>monitoring.recordMetric(metric);\nconst log = (entry)=>monitoring.log(entry);\nconst startTimer = (operation, metadata)=>monitoring.startTimer(operation, metadata);\n// 装饰器：自动性能监控\nfunction withPerformanceMonitoring(operation) {\n    return function(target, propertyName, descriptor) {\n        const method = descriptor.value;\n        descriptor.value = async function(...args) {\n            const timer = startTimer(`${target.constructor.name}.${operation}`, {\n                method: propertyName,\n                args: args.length\n            });\n            try {\n                const result = await method.apply(this, args);\n                timer.end(true);\n                return result;\n            } catch (error) {\n                timer.end(false, error.message);\n                throw error;\n            }\n        };\n        return descriptor;\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (monitoring);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/monitoring.ts\n");

/***/ })

};
;