/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Users_wangfeng_Documents_mediinspect_v2_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Users/<USER>/Documents/mediinspect-v2/src/app/api/auth/login/route.ts\",\n    nextConfigOutput,\n    userland: _Users_wangfeng_Documents_mediinspect_v2_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/auth/login/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var _lib_user_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/user-service */ \"(rsc)/./src/lib/user-service.ts\");\n/* harmony import */ var _lib_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/jwt */ \"(rsc)/./src/lib/jwt.ts\");\n/* harmony import */ var _lib_api_handler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/handler */ \"(rsc)/./src/lib/api/handler.ts\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.ts\");\n// 注意：此API使用withAuth处理器，自动处理：\n// - 身份验证 (verifyAccessToken)\n// - 权限检查 (hasRole)\n// - 统一错误处理\n// - 审计日志记录\n\n\n\n\n\n// 登录请求验证schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    username: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, '用户名不能为空'),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(6, '密码至少6位')\n});\n/**\n * 用户登录API\n * @description 用户通过用户名和密码进行登录认证\n * @param request 登录请求，包含用户名和密码\n * @returns 登录成功返回访问令牌和用户信息\n * @example\n * POST /api/auth/login\n * {\n *   \"username\": \"admin\",\n *   \"password\": \"password123\"\n * }\n *\n * Response:\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"登录成功\",\n *   \"data\": {\n *     \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n *     \"user\": {\n *       \"id\": 1,\n *       \"username\": \"admin\",\n *       \"roles\": [\"ADMIN\"]\n *     }\n *   }\n * }\n */ // 响应格式说明：\n// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：\n// {\n//   \"success\": true,\n//   \"code\": \"SUCCESS\",\n//   \"message\": \"操作成功\",\n//   \"data\": <返回的数据>,\n//   \"meta\": {\n//     \"timestamp\": \"2024-01-01T00:00:00Z\",\n//     \"version\": \"1.0.0\",\n//     \"performance\": { \"executionTime\": 100, \"requestId\": \"req_xxx\" }\n//   }\n// }\nconst POST = (0,_lib_api_handler__WEBPACK_IMPORTED_MODULE_2__.publicApi)(async (request)=>{\n    // 解析和验证请求数据\n    const body = await request.json();\n    const validatedData = loginSchema.parse(body);\n    // 验证用户凭据\n    const user = await (0,_lib_user_service__WEBPACK_IMPORTED_MODULE_0__.authenticateUser)(validatedData);\n    if (!user) {\n        throw new _lib_api_response__WEBPACK_IMPORTED_MODULE_3__.ApiError(_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.ErrorCode.UNAUTHORIZED, '用户名或密码错误');\n    }\n    // 生成真正的JWT token\n    const { accessToken, refreshToken } = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.generateTokenPair)(user);\n    // 返回登录成功响应\n    return {\n        user: {\n            id: user.id,\n            username: user.username,\n            realName: user.realName,\n            email: user.email,\n            department: user.department,\n            position: user.position,\n            status: user.status,\n            roles: user.roles\n        },\n        token: accessToken,\n        refreshToken: refreshToken,\n        expiresIn: 24 * 60 * 60 // 24小时，以秒为单位\n    };\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/handler.ts":
/*!********************************!*\
  !*** ./src/lib/api/handler.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiHandler: () => (/* binding */ createApiHandler),\n/* harmony export */   publicApi: () => (/* binding */ publicApi),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthAndValidation: () => (/* binding */ withAuthAndValidation),\n/* harmony export */   withValidation: () => (/* binding */ withValidation)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./response */ \"(rsc)/./src/lib/api/response.ts\");\n/* harmony import */ var _lib_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/jwt */ \"(rsc)/./src/lib/jwt.ts\");\n/* harmony import */ var _lib_security_audit_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/security/audit-middleware */ \"(rsc)/./src/lib/security/audit-middleware.ts\");\n// 注意：此文件为API处理器工具库，不是API路由文件\n// 提供统一的API处理器，包含认证、验证、错误处理\n// 本身不需要认证检查，为其他API提供认证功能\n/**\n * 统一API处理器\n * 实现P1-002 API接口规范化目标：\n * - 统一错误处理\n * - 请求验证\n * - 性能监控\n * - 日志记录\n *\n * @example 基本使用示例\n * ```typescript\n * // 带认证的API处理器\n * export const GET = withAuth(async (request, context) => {\n *   const data = await getUserData(context.user.id)\n *   return data\n * })\n *\n * // 响应示例\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"操作成功\",\n *   \"data\": { \"userId\": 123, \"name\": \"张三\" },\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\",\n *     \"performance\": { \"executionTime\": 150, \"requestId\": \"req_123\" }\n *   }\n * }\n * ```\n *\n * @example 带验证的API处理器\n * ```typescript\n * const schema = z.object({\n *   name: z.string(),\n *   email: z.string().email()\n * })\n *\n * export const POST = withAuthAndValidation(schema, async (request, context) => {\n *   const { name, email } = context.validatedData\n *   const user = await createUser({ name, email })\n *   return user\n * })\n *\n * // 错误响应示例\n * {\n *   \"success\": false,\n *   \"code\": \"VALIDATION_ERROR\",\n *   \"message\": \"请求参数验证失败\",\n *   \"details\": [\n *     { \"path\": [\"email\"], \"message\": \"Invalid email format\" }\n *   ],\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\"\n *   }\n * }\n * ```\n */ \n\n\n\n/**\n * 统一API处理器\n */ function createApiHandler(handler, options = {}) {\n    return async (request)=>{\n        const startTime = Date.now();\n        const requestId = generateRequestId();\n        // 创建请求上下文\n        const context = {\n            requestId,\n            startTime,\n            ip: getClientIP(request),\n            userAgent: request.headers.get('user-agent') || 'Unknown'\n        };\n        try {\n            // 1. 身份验证\n            if (options.requireAuth) {\n                context.user = await authenticateRequest(request);\n            }\n            // 2. 权限检查\n            if (options.requiredRoles && context.user) {\n                checkUserRoles(context.user.roles, options.requiredRoles);\n            }\n            // 3. 请求验证\n            if (options.validateSchema) {\n                await validateRequest(request, options.validateSchema);\n            }\n            // 4. 速率限制\n            if (options.rateLimit) {\n                await checkRateLimit(context.ip, options.rateLimit);\n            }\n            // 5. 执行处理器\n            const result = await executeWithTimeout(()=>handler(request, context), options.timeout || 30000);\n            // 6. 记录审计日志\n            if (context.user) {\n                await (0,_lib_security_audit_middleware__WEBPACK_IMPORTED_MODULE_2__.recordAuditLog)({\n                    userId: context.user.id,\n                    userName: context.user.username,\n                    action: request.method,\n                    resource: request.nextUrl.pathname,\n                    operationResult: 'SUCCESS',\n                    operationDescription: `API调用成功: ${request.method} ${request.nextUrl.pathname}`,\n                    ipAddress: context.ip,\n                    userAgent: context.userAgent,\n                    timestamp: new Date(),\n                    executionTime: Date.now() - startTime\n                });\n            }\n            // 7. 构建性能元数据\n            const performance = {\n                executionTime: Date.now() - startTime,\n                requestId: context.requestId\n            };\n            // 8. 返回成功响应\n            return (0,_response__WEBPACK_IMPORTED_MODULE_0__.successResponse)(result, undefined, {\n                performance\n            });\n        } catch (error) {\n            // 错误处理\n            return handleApiError(error, context, startTime);\n        }\n    };\n}\n/**\n * 身份验证\n */ async function authenticateRequest(request) {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.UNAUTHORIZED, '缺少访问令牌');\n    }\n    const token = authHeader.substring(7);\n    const payload = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.verifyAccessToken)(token);\n    if (!payload) {\n        throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.TOKEN_EXPIRED, '访问令牌已过期');\n    }\n    return {\n        id: payload.userId,\n        username: payload.username,\n        roles: payload.roles || []\n    };\n}\n/**\n * 权限检查\n */ function checkUserRoles(userRoles, requiredRoles) {\n    const hasRequiredRole = requiredRoles.some((role)=>userRoles.includes(role));\n    if (!hasRequiredRole) {\n        throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.INSUFFICIENT_PERMISSIONS, `需要以下权限之一: ${requiredRoles.join(', ')}`);\n    }\n}\n/**\n * 请求验证\n */ async function validateRequest(request, schema) {\n    try {\n        let data;\n        if (request.method === 'GET') {\n            // 验证查询参数\n            const url = new URL(request.url);\n            data = Object.fromEntries(url.searchParams.entries());\n        } else {\n            // 验证请求体\n            const contentType = request.headers.get('content-type');\n            if (contentType?.includes('application/json')) {\n                data = await request.json();\n            } else if (contentType?.includes('application/x-www-form-urlencoded')) {\n                const formData = await request.formData();\n                data = Object.fromEntries(formData.entries());\n            } else {\n                throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.INVALID_REQUEST, '不支持的内容类型');\n            }\n        }\n        schema.parse(data);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n            throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors);\n        }\n        throw error;\n    }\n}\n/**\n * 速率限制检查\n */ async function checkRateLimit(ip, rateLimit) {\n// 这里应该实现实际的速率限制逻辑\n// 可以使用Redis或内存存储\n// 暂时跳过实现\n}\n/**\n * 超时执行\n */ async function executeWithTimeout(fn, timeoutMs) {\n    return Promise.race([\n        fn(),\n        new Promise((_, reject)=>setTimeout(()=>reject(new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.TIMEOUT_ERROR)), timeoutMs))\n    ]);\n}\n/**\n * 错误处理\n */ async function handleApiError(error, context, startTime) {\n    let apiError;\n    if (error instanceof _response__WEBPACK_IMPORTED_MODULE_0__.ApiError) {\n        apiError = error;\n    } else if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n        apiError = new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors);\n    } else {\n        // 记录未知错误\n        console.error('Unhandled API error:', error);\n        apiError = new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.UNKNOWN_ERROR, '服务器内部错误');\n    }\n    // 记录错误审计日志\n    if (context.user) {\n        await (0,_lib_security_audit_middleware__WEBPACK_IMPORTED_MODULE_2__.recordAuditLog)({\n            userId: context.user.id,\n            userName: context.user.username,\n            action: 'ERROR',\n            resource: 'API',\n            operationResult: 'FAILED',\n            operationDescription: `API调用失败: ${apiError.message}`,\n            ipAddress: context.ip,\n            userAgent: context.userAgent,\n            timestamp: new Date(),\n            executionTime: Date.now() - startTime\n        }).catch(console.error);\n    }\n    return (0,_response__WEBPACK_IMPORTED_MODULE_0__.errorResponse)(apiError.code, apiError.message, apiError.details);\n}\n/**\n * 生成请求ID\n */ function generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n/**\n * 获取客户端IP\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    if (forwarded) {\n        return forwarded.split(',')[0]?.trim() || 'unknown';\n    }\n    return realIP || 'unknown';\n}\n/**\n * 便捷的API处理器创建函数\n */ const withAuth = (handler, requiredRoles)=>createApiHandler(handler, {\n        requireAuth: true,\n        requiredRoles\n    });\nconst withValidation = (handler, schema)=>createApiHandler(handler, {\n        validateSchema: schema\n    });\nconst withAuthAndValidation = (handler, schema, requiredRoles)=>createApiHandler(handler, {\n        requireAuth: true,\n        requiredRoles,\n        validateSchema: schema\n    });\nconst publicApi = (handler)=>createApiHandler(handler, {\n        requireAuth: false\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/handler.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.ts":
/*!*********************************!*\
  !*** ./src/lib/api/response.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   ErrorCodeToHttpStatus: () => (/* binding */ ErrorCodeToHttpStatus),\n/* harmony export */   ErrorCodeToMessage: () => (/* binding */ ErrorCodeToMessage),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createNextResponse: () => (/* binding */ createNextResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// 注意：此文件为响应格式工具库，不是API路由文件\n// 提供统一的响应格式和错误处理工具\n// 不需要认证检查，由API路由文件使用\n/**\n * 统一API响应格式\n * 实现P1-002 API接口规范化目标：\n * - 统一响应格式\n * - 完整错误处理\n * - 标准化接口规范\n *\n * @param data 响应数据，可以是任意类型\n * @param message 响应消息，默认为\"操作成功\"\n * @param meta 元数据信息，包含时间戳、版本等\n * @returns 标准化的API响应对象\n *\n * @example 成功响应示例\n * ```typescript\n * // 使用createSuccessResponse\n * const response = createSuccessResponse(\n *   { userId: 123, name: \"张三\" },\n *   \"获取用户信息成功\"\n * )\n *\n * // 响应格式\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"获取用户信息成功\",\n *   \"data\": { \"userId\": 123, \"name\": \"张三\" },\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\"\n *   }\n * }\n * ```\n *\n * @example 错误响应示例\n * ```typescript\n * // 使用createErrorResponse\n * const errorResponse = createErrorResponse(\n *   ErrorCode.VALIDATION_ERROR,\n *   \"请求参数验证失败\",\n *   { field: \"email\", message: \"邮箱格式不正确\" }\n * )\n *\n * // 错误响应格式\n * {\n *   \"success\": false,\n *   \"code\": \"VALIDATION_ERROR\",\n *   \"message\": \"请求参数验证失败\",\n *   \"details\": { \"field\": \"email\", \"message\": \"邮箱格式不正确\" },\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\"\n *   }\n * }\n * ```\n *\n * @example 分页响应示例\n * ```typescript\n * // 使用createPaginatedResponse\n * const paginatedResponse = createPaginatedResponse(\n *   [{ id: 1, name: \"用户1\" }, { id: 2, name: \"用户2\" }],\n *   { page: 1, pageSize: 20, total: 100, totalPages: 5, hasNext: true, hasPrev: false },\n *   \"获取用户列表成功\"\n * )\n *\n * // 分页响应格式\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"获取用户列表成功\",\n *   \"data\": [{ \"id\": 1, \"name\": \"用户1\" }, { \"id\": 2, \"name\": \"用户2\" }],\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\",\n *     \"pagination\": {\n *       \"page\": 1,\n *       \"pageSize\": 20,\n *       \"total\": 100,\n *       \"totalPages\": 5,\n *       \"hasNext\": true,\n *       \"hasPrev\": false\n *     }\n *   }\n * }\n * ```\n */ \n// 错误码枚举\nvar ErrorCode = /*#__PURE__*/ function(ErrorCode) {\n    // 通用错误\n    ErrorCode[\"SUCCESS\"] = \"SUCCESS\";\n    ErrorCode[\"UNKNOWN_ERROR\"] = \"UNKNOWN_ERROR\";\n    ErrorCode[\"INVALID_REQUEST\"] = \"INVALID_REQUEST\";\n    ErrorCode[\"VALIDATION_ERROR\"] = \"VALIDATION_ERROR\";\n    // 认证授权错误\n    ErrorCode[\"UNAUTHORIZED\"] = \"UNAUTHORIZED\";\n    ErrorCode[\"FORBIDDEN\"] = \"FORBIDDEN\";\n    ErrorCode[\"TOKEN_EXPIRED\"] = \"TOKEN_EXPIRED\";\n    ErrorCode[\"INVALID_TOKEN\"] = \"INVALID_TOKEN\";\n    // 资源错误\n    ErrorCode[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorCode[\"RESOURCE_EXISTS\"] = \"RESOURCE_EXISTS\";\n    ErrorCode[\"RESOURCE_CONFLICT\"] = \"RESOURCE_CONFLICT\";\n    // 业务错误\n    ErrorCode[\"BUSINESS_ERROR\"] = \"BUSINESS_ERROR\";\n    ErrorCode[\"OPERATION_FAILED\"] = \"OPERATION_FAILED\";\n    ErrorCode[\"INSUFFICIENT_PERMISSIONS\"] = \"INSUFFICIENT_PERMISSIONS\";\n    // 数据库错误\n    ErrorCode[\"DATABASE_ERROR\"] = \"DATABASE_ERROR\";\n    ErrorCode[\"CONNECTION_ERROR\"] = \"CONNECTION_ERROR\";\n    ErrorCode[\"QUERY_ERROR\"] = \"QUERY_ERROR\";\n    // 外部服务错误\n    ErrorCode[\"EXTERNAL_SERVICE_ERROR\"] = \"EXTERNAL_SERVICE_ERROR\";\n    ErrorCode[\"NETWORK_ERROR\"] = \"NETWORK_ERROR\";\n    ErrorCode[\"TIMEOUT_ERROR\"] = \"TIMEOUT_ERROR\";\n    // 限流错误\n    ErrorCode[\"RATE_LIMIT_EXCEEDED\"] = \"RATE_LIMIT_EXCEEDED\";\n    ErrorCode[\"QUOTA_EXCEEDED\"] = \"QUOTA_EXCEEDED\";\n    // 文件错误\n    ErrorCode[\"FILE_NOT_FOUND\"] = \"FILE_NOT_FOUND\";\n    ErrorCode[\"FILE_TOO_LARGE\"] = \"FILE_TOO_LARGE\";\n    ErrorCode[\"INVALID_FILE_TYPE\"] = \"INVALID_FILE_TYPE\";\n    // 医保业务特定错误\n    ErrorCode[\"MEDICAL_CASE_NOT_FOUND\"] = \"MEDICAL_CASE_NOT_FOUND\";\n    ErrorCode[\"INVALID_MEDICAL_DATA\"] = \"INVALID_MEDICAL_DATA\";\n    ErrorCode[\"SUPERVISION_RULE_ERROR\"] = \"SUPERVISION_RULE_ERROR\";\n    ErrorCode[\"KNOWLEDGE_BASE_ERROR\"] = \"KNOWLEDGE_BASE_ERROR\";\n    return ErrorCode;\n}({});\n// 错误码对应的HTTP状态码\nconst ErrorCodeToHttpStatus = {\n    [\"SUCCESS\"]: 200,\n    [\"UNKNOWN_ERROR\"]: 500,\n    [\"INVALID_REQUEST\"]: 400,\n    [\"VALIDATION_ERROR\"]: 400,\n    [\"UNAUTHORIZED\"]: 401,\n    [\"FORBIDDEN\"]: 403,\n    [\"TOKEN_EXPIRED\"]: 401,\n    [\"INVALID_TOKEN\"]: 401,\n    [\"NOT_FOUND\"]: 404,\n    [\"RESOURCE_EXISTS\"]: 409,\n    [\"RESOURCE_CONFLICT\"]: 409,\n    [\"BUSINESS_ERROR\"]: 400,\n    [\"OPERATION_FAILED\"]: 400,\n    [\"INSUFFICIENT_PERMISSIONS\"]: 403,\n    [\"DATABASE_ERROR\"]: 500,\n    [\"CONNECTION_ERROR\"]: 500,\n    [\"QUERY_ERROR\"]: 500,\n    [\"EXTERNAL_SERVICE_ERROR\"]: 502,\n    [\"NETWORK_ERROR\"]: 502,\n    [\"TIMEOUT_ERROR\"]: 504,\n    [\"RATE_LIMIT_EXCEEDED\"]: 429,\n    [\"QUOTA_EXCEEDED\"]: 429,\n    [\"FILE_NOT_FOUND\"]: 404,\n    [\"FILE_TOO_LARGE\"]: 413,\n    [\"INVALID_FILE_TYPE\"]: 400,\n    [\"MEDICAL_CASE_NOT_FOUND\"]: 404,\n    [\"INVALID_MEDICAL_DATA\"]: 400,\n    [\"SUPERVISION_RULE_ERROR\"]: 400,\n    [\"KNOWLEDGE_BASE_ERROR\"]: 400\n};\n// 错误码对应的中文消息\nconst ErrorCodeToMessage = {\n    [\"SUCCESS\"]: '操作成功',\n    [\"UNKNOWN_ERROR\"]: '未知错误',\n    [\"INVALID_REQUEST\"]: '请求参数无效',\n    [\"VALIDATION_ERROR\"]: '数据验证失败',\n    [\"UNAUTHORIZED\"]: '未授权访问',\n    [\"FORBIDDEN\"]: '访问被禁止',\n    [\"TOKEN_EXPIRED\"]: '登录已过期',\n    [\"INVALID_TOKEN\"]: '无效的访问令牌',\n    [\"NOT_FOUND\"]: '资源不存在',\n    [\"RESOURCE_EXISTS\"]: '资源已存在',\n    [\"RESOURCE_CONFLICT\"]: '资源冲突',\n    [\"BUSINESS_ERROR\"]: '业务处理失败',\n    [\"OPERATION_FAILED\"]: '操作失败',\n    [\"INSUFFICIENT_PERMISSIONS\"]: '权限不足',\n    [\"DATABASE_ERROR\"]: '数据库错误',\n    [\"CONNECTION_ERROR\"]: '连接失败',\n    [\"QUERY_ERROR\"]: '查询失败',\n    [\"EXTERNAL_SERVICE_ERROR\"]: '外部服务错误',\n    [\"NETWORK_ERROR\"]: '网络错误',\n    [\"TIMEOUT_ERROR\"]: '请求超时',\n    [\"RATE_LIMIT_EXCEEDED\"]: '请求频率超限',\n    [\"QUOTA_EXCEEDED\"]: '配额已用完',\n    [\"FILE_NOT_FOUND\"]: '文件不存在',\n    [\"FILE_TOO_LARGE\"]: '文件过大',\n    [\"INVALID_FILE_TYPE\"]: '文件类型不支持',\n    [\"MEDICAL_CASE_NOT_FOUND\"]: '医疗案例不存在',\n    [\"INVALID_MEDICAL_DATA\"]: '医疗数据格式错误',\n    [\"SUPERVISION_RULE_ERROR\"]: '监管规则处理失败',\n    [\"KNOWLEDGE_BASE_ERROR\"]: '知识库操作失败'\n};\n// API错误类\nclass ApiError extends Error {\n    constructor(code, message, details){\n        super(message || ErrorCodeToMessage[code]);\n        this.name = 'ApiError';\n        this.code = code;\n        this.httpStatus = ErrorCodeToHttpStatus[code];\n        this.details = details;\n    }\n}\n// 成功响应构建器\nfunction createSuccessResponse(data, message, meta) {\n    try {\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: message || ErrorCodeToMessage[\"SUCCESS\"],\n            data,\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0',\n                ...meta\n            }\n        };\n    } catch (error) {\n        console.error('创建成功响应失败:', error);\n        // 返回基本的成功响应\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: '操作成功',\n            data,\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            }\n        };\n    }\n}\n// 错误响应构建器\nfunction createErrorResponse(code, message, details) {\n    try {\n        return {\n            success: false,\n            code,\n            message: message || ErrorCodeToMessage[code] || '未知错误',\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            },\n            ...details && {\n                details\n            }\n        };\n    } catch (error) {\n        console.error('创建错误响应失败:', error);\n        // 返回基本的错误响应\n        return {\n            success: false,\n            code: \"UNKNOWN_ERROR\",\n            message: '系统错误',\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            }\n        };\n    }\n}\n// 分页响应构建器\nfunction createPaginatedResponse(data, pagination, message, performance) {\n    try {\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: message || ErrorCodeToMessage[\"SUCCESS\"],\n            data,\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0',\n                pagination,\n                ...performance && {\n                    performance\n                }\n            }\n        };\n    } catch (error) {\n        console.error('创建分页响应失败:', error);\n        // 返回基本的分页响应\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: '获取数据成功',\n            data: data || [],\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0',\n                pagination: pagination || {\n                    page: 1,\n                    pageSize: 20,\n                    total: 0,\n                    totalPages: 0,\n                    hasNext: false,\n                    hasPrev: false\n                }\n            }\n        };\n    }\n}\n// NextResponse包装器\nfunction createNextResponse(response) {\n    try {\n        const httpStatus = response.success ? 200 : ErrorCodeToHttpStatus[response.code] || 500;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: httpStatus\n        });\n    } catch (error) {\n        console.error('创建NextResponse失败:', error);\n        // 返回基本的错误响应\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            code: \"UNKNOWN_ERROR\",\n            message: '系统错误',\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n// 成功响应快捷方法\nfunction successResponse(data, message, meta) {\n    try {\n        return createNextResponse(createSuccessResponse(data, message, meta));\n    } catch (error) {\n        console.error('创建成功响应失败:', error);\n        return createNextResponse(createErrorResponse(\"UNKNOWN_ERROR\", '创建响应失败'));\n    }\n}\n// 错误响应快捷方法\nfunction errorResponse(code, message, details) {\n    try {\n        return createNextResponse(createErrorResponse(code, message, details));\n    } catch (error) {\n        console.error('创建错误响应失败:', error);\n        return createNextResponse(createErrorResponse(\"UNKNOWN_ERROR\", '创建错误响应失败'));\n    }\n}\n// 分页响应快捷方法\nfunction paginatedResponse(data, pagination, message, performance) {\n    try {\n        return createNextResponse(createPaginatedResponse(data, pagination, message, performance));\n    } catch (error) {\n        console.error('创建分页响应失败:', error);\n        return createNextResponse(createErrorResponse(\"UNKNOWN_ERROR\", '创建分页响应失败'));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildCountSQL: () => (/* binding */ buildCountSQL),\n/* harmony export */   buildPaginationSQL: () => (/* binding */ buildPaginationSQL),\n/* harmony export */   checkConnection: () => (/* binding */ checkConnection),\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   formatBinds: () => (/* binding */ formatBinds),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   getPoolStats: () => (/* binding */ getPoolStats),\n/* harmony export */   initializePool: () => (/* binding */ initializePool),\n/* harmony export */   monitorPoolHealth: () => (/* binding */ monitorPoolHealth)\n/* harmony export */ });\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! oracledb */ \"oracledb\");\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(oracledb__WEBPACK_IMPORTED_MODULE_0__);\n\n// Oracle数据库连接配置 - 性能优化版本\nconst dbConfig = {\n    user: process.env['DB_USERNAME'],\n    password: process.env['DB_PASSWORD'],\n    connectString: `${process.env['DB_HOST']}:${process.env['DB_PORT']}/${process.env['DB_SERVICE_NAME']}`,\n    poolMin: parseInt(process.env['DB_POOL_MIN'] || '5'),\n    poolMax: parseInt(process.env['DB_POOL_MAX'] || '20'),\n    poolIncrement: parseInt(process.env['DB_POOL_INCREMENT'] || '2'),\n    poolTimeout: parseInt(process.env['DB_POOL_TIMEOUT'] || '30'),\n    stmtCacheSize: parseInt(process.env['DB_STMT_CACHE_SIZE'] || '50'),\n    queueMax: parseInt(process.env['DB_QUEUE_MAX'] || '100'),\n    queueTimeout: parseInt(process.env['DB_QUEUE_TIMEOUT'] || '10000'),\n    enableStatistics: true\n};\n// 配置Oracle客户端\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().outFormat) = (oracledb__WEBPACK_IMPORTED_MODULE_0___default().OUT_FORMAT_OBJECT);\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().autoCommit) = true;\n// 连接池\nlet pool = null;\n/**\n * 初始化数据库连接池\n */ async function initializePool() {\n    try {\n        if (!pool) {\n            pool = await oracledb__WEBPACK_IMPORTED_MODULE_0___default().createPool(dbConfig);\n            console.log('✅ 数据库连接池初始化成功');\n            // 启动健康检查\n            startHealthCheck();\n        }\n    } catch (error) {\n        console.error('❌ 数据库连接池初始化失败:', error);\n        throw error;\n    }\n}\n/**\n * 获取数据库连接\n */ async function getConnection() {\n    try {\n        if (!pool) {\n            await initializePool();\n        }\n        return await pool.getConnection();\n    } catch (error) {\n        console.error('❌ 获取数据库连接失败:', error);\n        throw error;\n    }\n}\n// 清理Oracle查询结果，移除循环引用\nfunction cleanOracleResult(obj) {\n    if (obj === null || obj === undefined) {\n        return obj;\n    }\n    if (typeof obj !== 'object') {\n        return obj;\n    }\n    if (Array.isArray(obj)) {\n        return obj.map((item)=>cleanOracleResult(item));\n    }\n    if (obj instanceof Date) {\n        return obj;\n    }\n    if (Buffer.isBuffer(obj)) {\n        return obj;\n    }\n    // 创建新对象，只复制基本属性\n    const cleanObj = {};\n    for (const [key, value] of Object.entries(obj)){\n        // 跳过Oracle内部属性和可能的循环引用\n        if (typeof key === 'string' && (key.startsWith('_') || key.toLowerCase().includes('connection') || key.toLowerCase().includes('pool') || key.toLowerCase().includes('client') || key.toLowerCase().includes('socket') || key.toLowerCase().includes('stream') || key.toLowerCase().includes('cursor') || key.toLowerCase().includes('resultset') || key === 'domain' || key === 'constructor' || key === 'prototype')) {\n            continue;\n        }\n        try {\n            cleanObj[key] = cleanOracleResult(value);\n        } catch (error) {\n            continue;\n        }\n    }\n    return cleanObj;\n}\n/**\n * 执行SQL查询\n */ async function executeQuery(sql, binds = {}, options = {}) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        const result = await connection.execute(sql, binds, options);\n        // 使用经过验证的清理函数，移除循环引用\n        const cleanRows = result.rows ? cleanOracleResult(result.rows) : [];\n        return {\n            rows: cleanRows,\n            rowsAffected: result.rowsAffected,\n            metaData: result.metaData ? cleanOracleResult(result.metaData) : undefined\n        };\n    } catch (error) {\n        console.error('❌ SQL查询执行失败:', error);\n        console.error('SQL:', sql);\n        console.error('Binds:', binds);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 执行事务\n */ async function executeTransaction(operations) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        // 关闭自动提交\n        // connection.autoCommit = false // Oracle连接不支持直接设置autoCommit属性\n        // 执行操作\n        const result = await operations(connection);\n        // 提交事务\n        await connection.commit();\n        return result;\n    } catch (error) {\n        // 回滚事务\n        if (connection) {\n            try {\n                await connection.rollback();\n            } catch (rollbackError) {\n                console.error('⚠️ 事务回滚失败:', rollbackError);\n            }\n        }\n        console.error('❌ 事务执行失败:', error);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                // 恢复自动提交\n                // connection.autoCommit = true // Oracle连接不支持直接设置autoCommit属性\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 关闭数据库连接池\n */ async function closePool() {\n    try {\n        if (pool) {\n            // 停止健康检查\n            stopHealthCheck();\n            await pool.close(10); // 等待10秒关闭\n            pool = null;\n            console.log('✅ 数据库连接池已关闭');\n        }\n    } catch (error) {\n        console.error('❌ 关闭数据库连接池失败:', error);\n        throw error;\n    }\n}\n/**\n * 检查数据库连接状态\n */ async function checkConnection() {\n    try {\n        const result = await executeQuery('SELECT SYSDATE FROM DUAL');\n        return !!(result.rows && result.rows.length > 0);\n    } catch (error) {\n        console.error('❌ 数据库连接检查失败:', error);\n        return false;\n    }\n}\n/**\n * 格式化SQL绑定参数\n */ function formatBinds(params) {\n    const formatted = {};\n    for (const [key, value] of Object.entries(params)){\n        if (value === null || value === undefined) {\n            formatted[key] = {\n                val: null,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'string') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'number') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().NUMBER)\n            };\n        } else if (value instanceof Date) {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().DATE)\n            };\n        } else {\n            formatted[key] = value;\n        }\n    }\n    return formatted;\n}\n/**\n * 构建分页SQL\n */ function buildPaginationSQL(baseSql, page = 1, pageSize = 10, orderBy = 'ID') {\n    const offset = (page - 1) * pageSize;\n    // 检查baseSql是否已经包含ORDER BY子句\n    const hasOrderBy = /ORDER\\s+BY/i.test(baseSql);\n    return `\n    SELECT * FROM (\n      SELECT a.*, ROWNUM rnum FROM (\n        ${baseSql}\n        ${hasOrderBy ? '' : `ORDER BY ${orderBy}`}\n      ) a\n      WHERE ROWNUM <= ${offset + pageSize}\n    )\n    WHERE rnum > ${offset}\n  `;\n}\n/**\n * 构建计数SQL\n */ function buildCountSQL(baseSql) {\n    // 移除ORDER BY子句\n    const cleanSql = baseSql.replace(/ORDER\\s+BY\\s+[^)]*$/i, '');\n    return `SELECT COUNT(*) as TOTAL FROM (${cleanSql})`;\n}\n// 进程退出时关闭连接池\nprocess.on('SIGINT', async ()=>{\n    await closePool();\n    process.exit(0);\n});\nprocess.on('SIGTERM', async ()=>{\n    await closePool();\n    process.exit(0);\n});\n/**\n * 获取连接池统计信息\n */ function getPoolStats() {\n    if (!pool) {\n        return {\n            connectionsOpen: 0,\n            connectionsInUse: 0,\n            connectionsAvailable: 0,\n            queueLength: 0,\n            queueTimeout: 0,\n            poolMin: 0,\n            poolMax: 0,\n            poolIncrement: 0\n        };\n    }\n    return {\n        connectionsOpen: pool.connectionsOpen,\n        connectionsInUse: pool.connectionsInUse,\n        connectionsAvailable: pool.connectionsOpen - pool.connectionsInUse,\n        queueLength: pool.queueLength || 0,\n        queueTimeout: pool.queueTimeout || 0,\n        poolMin: pool.poolMin,\n        poolMax: pool.poolMax,\n        poolIncrement: pool.poolIncrement\n    };\n}\n/**\n * 监控连接池健康状态\n */ function monitorPoolHealth() {\n    const stats = getPoolStats();\n    if (!pool) {\n        return {\n            status: 'critical',\n            message: '连接池未初始化',\n            stats\n        };\n    }\n    const utilizationRate = stats.connectionsOpen > 0 ? stats.connectionsInUse / stats.connectionsOpen * 100 : 0;\n    const queueUtilization = stats.queueLength > 0 ? stats.queueLength / (dbConfig.queueMax || 100) * 100 : 0;\n    if (utilizationRate > 90 || queueUtilization > 80) {\n        return {\n            status: 'critical',\n            message: `连接池使用率过高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    if (utilizationRate > 70 || queueUtilization > 50) {\n        return {\n            status: 'warning',\n            message: `连接池使用率偏高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    return {\n        status: 'healthy',\n        message: `连接池运行正常: ${utilizationRate.toFixed(1)}% 使用率`,\n        stats\n    };\n}\n/**\n * 启动连接池健康检查\n */ function startHealthCheck() {\n    // 如果已经有定时器在运行，先清除它\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n    }\n    global.__healthCheckInterval = setInterval(()=>{\n        const health = monitorPoolHealth();\n        if (health.status === 'critical') {\n            console.error('🚨 连接池健康检查:', health.message);\n        } else if (health.status === 'warning') {\n            console.warn('⚠️ 连接池健康检查:', health.message);\n        }\n    }, 30000); // 每30秒检查一次\n}\n/**\n * 停止连接池健康检查\n */ function stopHealthCheck() {\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n        global.__healthCheckInterval = undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/jwt.ts":
/*!************************!*\
  !*** ./src/lib/jwt.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractTokenFromHeader: () => (/* binding */ extractTokenFromHeader),\n/* harmony export */   generateAccessToken: () => (/* binding */ generateAccessToken),\n/* harmony export */   generateRefreshToken: () => (/* binding */ generateRefreshToken),\n/* harmony export */   generateTokenPair: () => (/* binding */ generateTokenPair),\n/* harmony export */   getTokenRemainingTime: () => (/* binding */ getTokenRemainingTime),\n/* harmony export */   getUserPermissionLevel: () => (/* binding */ getUserPermissionLevel),\n/* harmony export */   hasAllRoles: () => (/* binding */ hasAllRoles),\n/* harmony export */   hasAnyRole: () => (/* binding */ hasAnyRole),\n/* harmony export */   hasPermissionLevel: () => (/* binding */ hasPermissionLevel),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isTokenExpiringSoon: () => (/* binding */ isTokenExpiringSoon),\n/* harmony export */   verifyAccessToken: () => (/* binding */ verifyAccessToken),\n/* harmony export */   verifyRefreshToken: () => (/* binding */ verifyRefreshToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\n// JWT配置\nconst JWT_SECRET = process.env['JWT_SECRET'] || 'mediinspect-v2-secret-key';\nconst JWT_EXPIRES_IN = process.env['JWT_EXPIRES_IN'] || '24h';\nconst JWT_REFRESH_SECRET = process.env['JWT_REFRESH_SECRET'] || 'mediinspect-v2-refresh-secret';\nconst JWT_REFRESH_EXPIRES_IN = process.env['JWT_REFRESH_EXPIRES_IN'] || '7d';\n/**\n * 生成访问令牌\n */ function generateAccessToken(user) {\n    const payload = {\n        userId: user.id,\n        username: user.username,\n        roles: user.roles.map((role)=>role.roleCode)\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN,\n        issuer: 'mediinspect-v2',\n        audience: 'mediinspect-users'\n    });\n}\n/**\n * 生成刷新令牌\n */ function generateRefreshToken(userId) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n        userId,\n        type: 'refresh'\n    }, JWT_REFRESH_SECRET, {\n        expiresIn: JWT_REFRESH_EXPIRES_IN,\n        issuer: 'mediinspect-v2',\n        audience: 'mediinspect-users'\n    });\n}\n/**\n * 验证访问令牌\n */ function verifyAccessToken(token) {\n    // 检查是否在服务端环境\n    if (false) {}\n    try {\n        if (!JWT_SECRET) {\n            console.error('❌ JWT_SECRET未配置');\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: 'mediinspect-v2',\n            audience: 'mediinspect-users'\n        });\n        return decoded;\n    } catch (error) {\n        console.error('❌ 访问令牌验证失败:', error);\n        return null;\n    }\n}\n/**\n * 验证刷新令牌\n */ function verifyRefreshToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_REFRESH_SECRET, {\n            issuer: 'mediinspect-v2',\n            audience: 'mediinspect-users'\n        });\n        if (decoded.type !== 'refresh') {\n            throw new Error('Invalid token type');\n        }\n        return {\n            userId: decoded.userId\n        };\n    } catch (error) {\n        console.error('❌ 刷新令牌验证失败:', error);\n        return null;\n    }\n}\n/**\n * 从请求头中提取令牌\n */ function extractTokenFromHeader(authHeader) {\n    if (!authHeader) return null;\n    const parts = authHeader.split(' ');\n    if (parts.length !== 2 || parts[0] !== 'Bearer') {\n        return null;\n    }\n    return parts[1] || null;\n}\n/**\n * 检查令牌是否即将过期\n */ function isTokenExpiringSoon(token, thresholdMinutes = 30) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(token);\n        if (!decoded || !decoded.exp) return true;\n        const now = Math.floor(Date.now() / 1000);\n        const threshold = thresholdMinutes * 60;\n        return decoded.exp - now < threshold;\n    } catch (error) {\n        return true;\n    }\n}\n/**\n * 获取令牌剩余时间（秒）\n */ function getTokenRemainingTime(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(token);\n        if (!decoded || !decoded.exp) return 0;\n        const now = Math.floor(Date.now() / 1000);\n        return Math.max(0, decoded.exp - now);\n    } catch (error) {\n        return 0;\n    }\n}\n/**\n * 检查用户是否有指定角色\n */ function hasRole(userRoles, requiredRole) {\n    if (false) {}\n    return userRoles.includes(requiredRole);\n}\n/**\n * 检查用户是否有任一指定角色\n */ function hasAnyRole(userRoles, requiredRoles) {\n    return requiredRoles.some((role)=>userRoles.includes(role));\n}\n/**\n * 检查用户是否有所有指定角色\n */ function hasAllRoles(userRoles, requiredRoles) {\n    return requiredRoles.every((role)=>userRoles.includes(role));\n}\n/**\n * 角色权限级别映射\n */ const ROLE_LEVELS = {\n    'ADMIN': 5,\n    'SUPERVISOR': 4,\n    'AUDITOR': 3,\n    'OPERATOR': 2,\n    'VIEWER': 1\n};\n/**\n * 检查用户是否有足够的权限级别\n */ function hasPermissionLevel(userRoles, requiredLevel) {\n    const userLevel = Math.max(...userRoles.map((role)=>ROLE_LEVELS[role] || 0));\n    return userLevel >= requiredLevel;\n}\n/**\n * 获取用户最高权限级别\n */ function getUserPermissionLevel(userRoles) {\n    return Math.max(...userRoles.map((role)=>ROLE_LEVELS[role] || 0));\n}\n/**\n * 生成令牌对\n */ function generateTokenPair(user) {\n    const accessToken = generateAccessToken(user);\n    const refreshToken = generateRefreshToken(user.id);\n    const expiresIn = getTokenRemainingTime(accessToken);\n    return {\n        accessToken,\n        refreshToken,\n        expiresIn\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/jwt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/password.ts":
/*!*****************************!*\
  !*** ./src/lib/password.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPasswordStrength: () => (/* binding */ checkPasswordStrength),\n/* harmony export */   generateRandomPassword: () => (/* binding */ generateRandomPassword),\n/* harmony export */   getPasswordStrengthColor: () => (/* binding */ getPasswordStrengthColor),\n/* harmony export */   getPasswordStrengthText: () => (/* binding */ getPasswordStrengthText),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   validatePasswordConfirmation: () => (/* binding */ validatePasswordConfirmation),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n\n// 密码加密配置\nconst SALT_ROUNDS = 12;\n/**\n * 加密密码\n */ async function hashPassword(password) {\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().genSalt(SALT_ROUNDS);\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, salt);\n        return hashedPassword;\n    } catch (error) {\n        console.error('❌ 密码加密失败:', error);\n        throw new Error('密码加密失败');\n    }\n}\n/**\n * 验证密码\n */ async function verifyPassword(password, hashedPassword) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hashedPassword);\n    } catch (error) {\n        console.error('❌ 密码验证失败:', error);\n        return false;\n    }\n}\n/**\n * 检查密码强度\n */ function checkPasswordStrength(password) {\n    const feedback = [];\n    const requirements = {\n        minLength: password.length >= 8,\n        hasUppercase: /[A-Z]/.test(password),\n        hasLowercase: /[a-z]/.test(password),\n        hasNumbers: /\\d/.test(password),\n        hasSpecialChars: /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)\n    };\n    // 检查最小长度\n    if (!requirements.minLength) {\n        feedback.push('密码长度至少8位');\n    }\n    // 检查大写字母\n    if (!requirements.hasUppercase) {\n        feedback.push('至少包含一个大写字母');\n    }\n    // 检查小写字母\n    if (!requirements.hasLowercase) {\n        feedback.push('至少包含一个小写字母');\n    }\n    // 检查数字\n    if (!requirements.hasNumbers) {\n        feedback.push('至少包含一个数字');\n    }\n    // 检查特殊字符\n    if (!requirements.hasSpecialChars) {\n        feedback.push('至少包含一个特殊字符');\n    }\n    // 计算强度分数\n    let score = 0;\n    if (requirements.minLength) score++;\n    if (requirements.hasUppercase) score++;\n    if (requirements.hasLowercase) score++;\n    if (requirements.hasNumbers) score++;\n    if (requirements.hasSpecialChars) score++;\n    // 额外检查\n    if (password.length >= 12) score += 0.5;\n    if (password.length >= 16) score += 0.5;\n    // 检查常见弱密码模式\n    const weakPatterns = [\n        /^123456/,\n        /^password/i,\n        /^admin/i,\n        /^qwerty/i,\n        /^abc123/i,\n        /(.)\\1{2,}/\n    ];\n    const hasWeakPattern = weakPatterns.some((pattern)=>pattern.test(password));\n    if (hasWeakPattern) {\n        score = Math.max(0, score - 1);\n        feedback.push('避免使用常见的弱密码模式');\n    }\n    const isValid = Object.values(requirements).every((req)=>req) && !hasWeakPattern;\n    return {\n        isValid,\n        score: Math.min(4, Math.floor(score)),\n        feedback,\n        requirements\n    };\n}\n/**\n * 获取密码强度描述\n */ function getPasswordStrengthText(score) {\n    switch(score){\n        case 0:\n        case 1:\n            return '弱';\n        case 2:\n            return '一般';\n        case 3:\n            return '强';\n        case 4:\n            return '很强';\n        default:\n            return '未知';\n    }\n}\n/**\n * 获取密码强度颜色\n */ function getPasswordStrengthColor(score) {\n    switch(score){\n        case 0:\n        case 1:\n            return 'text-red-500';\n        case 2:\n            return 'text-yellow-500';\n        case 3:\n            return 'text-blue-500';\n        case 4:\n            return 'text-green-500';\n        default:\n            return 'text-gray-500';\n    }\n}\n/**\n * 生成随机密码\n */ function generateRandomPassword(length = 12) {\n    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    const lowercase = 'abcdefghijklmnopqrstuvwxyz';\n    const numbers = '0123456789';\n    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';\n    const allChars = uppercase + lowercase + numbers + specialChars;\n    let password = '';\n    // 确保至少包含每种类型的字符\n    password += uppercase[Math.floor(Math.random() * uppercase.length)];\n    password += lowercase[Math.floor(Math.random() * lowercase.length)];\n    password += numbers[Math.floor(Math.random() * numbers.length)];\n    password += specialChars[Math.floor(Math.random() * specialChars.length)];\n    // 填充剩余长度\n    for(let i = password.length; i < length; i++){\n        password += allChars[Math.floor(Math.random() * allChars.length)];\n    }\n    // 打乱字符顺序\n    return password.split('').sort(()=>Math.random() - 0.5).join('');\n}\n/**\n * 验证密码确认\n */ function validatePasswordConfirmation(password, confirmPassword) {\n    return password === confirmPassword;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/password.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security/audit-middleware.ts":
/*!**********************************************!*\
  !*** ./src/lib/security/audit-middleware.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSecurityMiddleware: () => (/* binding */ createSecurityMiddleware),\n/* harmony export */   recordAuditLog: () => (/* binding */ recordAuditLog),\n/* harmony export */   recordSecurityEvent: () => (/* binding */ recordSecurityEvent),\n/* harmony export */   withAuditLog: () => (/* binding */ withAuditLog)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/jwt */ \"(rsc)/./src/lib/jwt.ts\");\n/**\n * 审计日志中间件\n * 实现P0-009安全机制强化目标：\n * - 记录所有用户操作的审计日志\n * - 实现完整的操作追踪\n * - 支持安全事件监控\n */ \n\n/**\n * 审计日志装饰器\n */ function withAuditLog(operationType, operationModule, getDescription) {\n    return function(target, propertyKey, descriptor) {\n        const originalMethod = descriptor.value;\n        descriptor.value = async function(request, ...args) {\n            const startTime = Date.now();\n            let auditEntry = {\n                action: operationType,\n                resource: operationModule,\n                timestamp: new Date(),\n                ipAddress: getClientIP(request),\n                userAgent: request.headers.get('user-agent') || 'Unknown'\n            };\n            try {\n                // 验证用户身份\n                const authHeader = request.headers.get('authorization');\n                if (authHeader && authHeader.startsWith('Bearer ')) {\n                    const token = authHeader.substring(7);\n                    const payload = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.verifyAccessToken)(token);\n                    if (payload) {\n                        auditEntry.userId = payload.userId;\n                        auditEntry.userName = payload.username;\n                    }\n                }\n                // 记录请求数据（敏感数据脱敏）\n                if (request.method !== 'GET') {\n                    try {\n                        const requestBody = await request.clone().json();\n                        auditEntry.requestData = maskSensitiveData(requestBody);\n                    } catch  {\n                    // 忽略非JSON请求\n                    }\n                }\n                // 执行原始方法\n                const result = await originalMethod.call(this, request, ...args);\n                // 记录执行结果\n                auditEntry.operationResult = 'SUCCESS';\n                auditEntry.executionTime = Date.now() - startTime;\n                if (getDescription) {\n                    auditEntry.operationDescription = getDescription(request, result);\n                } else {\n                    auditEntry.operationDescription = `${operationType} ${operationModule}`;\n                }\n                // 记录响应数据（敏感数据脱敏）\n                if (result instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n                    try {\n                        const responseData = await result.clone().json();\n                        auditEntry.responseData = maskSensitiveData(responseData);\n                    } catch  {\n                    // 忽略非JSON响应\n                    }\n                }\n                // 记录审计日志\n                await recordAuditLog(auditEntry);\n                return result;\n            } catch (error) {\n                // 记录失败的操作\n                auditEntry.operationResult = 'FAILED';\n                auditEntry.executionTime = Date.now() - startTime;\n                auditEntry.errorMessage = error instanceof Error ? error.message : String(error);\n                auditEntry.operationDescription = `${operationType} ${operationModule} - FAILED`;\n                await recordAuditLog(auditEntry);\n                throw error;\n            }\n        };\n        return descriptor;\n    };\n}\n/**\n * 记录审计日志\n */ async function recordAuditLog(entry) {\n    try {\n        // 在API路由中使用时，动态导入数据库服务\n        if (true) {\n            const { logSystemOperation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_system-log-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/system-log-service */ \"(rsc)/./src/lib/system-log-service.ts\"));\n            // 映射操作模块到数据库约束允许的值\n            const moduleMapping = {\n                'users': 'USER',\n                'roles': 'ROLE',\n                'medical-cases': 'MEDICAL_CASE',\n                'supervision-rules': 'SUPERVISION_RULE',\n                'knowledge-base': 'KNOWLEDGE',\n                'system': 'SYSTEM',\n                'auth': 'AUTH'\n            };\n            // 映射操作类型到数据库约束允许的值\n            const actionMapping = {\n                'GET': 'QUERY',\n                'POST': 'CREATE',\n                'PUT': 'UPDATE',\n                'DELETE': 'DELETE',\n                'PATCH': 'UPDATE',\n                'create': 'CREATE',\n                'update': 'UPDATE',\n                'delete': 'DELETE',\n                'query': 'QUERY',\n                'login': 'LOGIN',\n                'logout': 'LOGOUT',\n                'export': 'EXPORT',\n                'import': 'IMPORT',\n                'execute': 'EXECUTE'\n            };\n            const mappedModule = moduleMapping[entry.resource] || 'SYSTEM';\n            const mappedAction = actionMapping[entry.action] || 'QUERY';\n            await logSystemOperation(entry.userId || 0, mappedAction, mappedModule, entry.operationDescription, entry.operationResult, entry.ipAddress, entry.userAgent, entry.requestData ? JSON.stringify(entry.requestData) : undefined, entry.responseData ? JSON.stringify(entry.responseData) : undefined, entry.executionTime);\n        } else {}\n    } catch (error) {\n        console.error('Failed to record audit log:', error);\n    // 不抛出错误，避免影响主要业务流程\n    }\n}\n/**\n * 记录安全事件\n */ async function recordSecurityEvent(event) {\n    try {\n        // 在API路由中使用时，动态导入数据库服务\n        if (true) {\n            const { logSystemError } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_system-log-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/system-log-service */ \"(rsc)/./src/lib/system-log-service.ts\"));\n            await logSystemError(event.eventType, event.severity, event.description, JSON.stringify(event.metadata || {}), undefined, undefined, event.userId, event.ipAddress, event.userAgent);\n        } else {}\n        // 如果是高危事件，发送告警\n        if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {\n            await sendSecurityAlert(event);\n        }\n    } catch (error) {\n        console.error('Failed to record security event:', error);\n    }\n}\n/**\n * 发送安全告警\n */ async function sendSecurityAlert(event) {\n    // 这里可以集成邮件、短信、钉钉等告警渠道\n    console.warn('🚨 Security Alert:', {\n        type: event.eventType,\n        severity: event.severity,\n        description: event.description,\n        userId: event.userId,\n        ipAddress: event.ipAddress,\n        timestamp: event.timestamp\n    });\n}\n/**\n * 获取客户端IP地址\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.headers.get('remote-addr');\n    if (forwarded) {\n        return forwarded.split(',')[0]?.trim() || 'unknown';\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * 敏感数据脱敏\n */ function maskSensitiveData(data) {\n    if (!data || typeof data !== 'object') {\n        return data;\n    }\n    const sensitiveFields = [\n        'password',\n        'token',\n        'secret',\n        'key',\n        'idCard',\n        'phone',\n        'email',\n        'patientIdCard',\n        'patientPhone',\n        'doctorPhone',\n        'bankAccount'\n    ];\n    const masked = {\n        ...data\n    };\n    for (const field of sensitiveFields){\n        if (masked[field]) {\n            if (field.includes('phone') || field.includes('Phone')) {\n                masked[field] = maskPhone(masked[field]);\n            } else if (field.includes('idCard') || field.includes('IdCard')) {\n                masked[field] = maskIdCard(masked[field]);\n            } else if (field.includes('email') || field.includes('Email')) {\n                masked[field] = maskEmail(masked[field]);\n            } else {\n                masked[field] = '***';\n            }\n        }\n    }\n    // 递归处理嵌套对象\n    for(const key in masked){\n        if (typeof masked[key] === 'object' && masked[key] !== null) {\n            masked[key] = maskSensitiveData(masked[key]);\n        }\n    }\n    return masked;\n}\n/**\n * 手机号脱敏\n */ function maskPhone(phone) {\n    if (!phone || phone.length < 7) return phone;\n    return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n}\n/**\n * 身份证号脱敏\n */ function maskIdCard(idCard) {\n    if (!idCard || idCard.length < 10) return idCard;\n    return idCard.replace(/(\\d{6})\\d{8}(\\d{4})/, '$1********$2');\n}\n/**\n * 邮箱脱敏\n */ function maskEmail(email) {\n    if (!email || !email.includes('@')) return email;\n    const [username, domain] = email.split('@');\n    if (!username || username.length <= 2) return email;\n    return `${username.substring(0, 2)}***@${domain}`;\n}\n/**\n * API安全中间件\n */ function createSecurityMiddleware() {\n    return async (request)=>{\n        const startTime = Date.now();\n        try {\n            // 检查请求频率限制\n            await checkRateLimit(request);\n            // 检查IP白名单/黑名单\n            await checkIPRestrictions(request);\n            // 检查用户权限\n            await checkUserPermissions(request);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        } catch (error) {\n            // 记录安全事件\n            await recordSecurityEvent({\n                eventType: 'UNAUTHORIZED_ACCESS',\n                severity: 'HIGH',\n                ipAddress: getClientIP(request),\n                userAgent: request.headers.get('user-agent') || 'Unknown',\n                description: `Security check failed: ${error instanceof Error ? error.message : String(error)}`,\n                timestamp: new Date()\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Access denied'\n            }, {\n                status: 403\n            });\n        }\n    };\n}\n/**\n * 检查请求频率限制\n */ async function checkRateLimit(request) {\n    // 简单的内存限流实现，生产环境建议使用Redis\n    const ip = getClientIP(request);\n    const key = `rate_limit_${ip}`;\n// 这里可以实现更复杂的限流逻辑\n// 暂时跳过实现\n}\n/**\n * 检查IP限制\n */ async function checkIPRestrictions(request) {\n    const ip = getClientIP(request);\n    // 检查IP黑名单\n    const blacklistedIPs = [\n        '*************'\n    ] // 示例黑名单\n    ;\n    if (blacklistedIPs.includes(ip)) {\n        throw new Error(`IP ${ip} is blacklisted`);\n    }\n}\n/**\n * 检查用户权限\n */ async function checkUserPermissions(request) {\n    // 对于需要认证的API，检查token有效性\n    if (request.nextUrl.pathname.startsWith('/api/') && !request.nextUrl.pathname.startsWith('/api/auth/')) {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            throw new Error('Missing or invalid authorization header');\n        }\n        const token = authHeader.substring(7);\n        const payload = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.verifyAccessToken)(token);\n        if (!payload) {\n            throw new Error('Invalid or expired token');\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security/audit-middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/user-service.ts":
/*!*********************************!*\
  !*** ./src/lib/user-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRolePermissions: () => (/* binding */ assignRolePermissions),\n/* harmony export */   assignUserRoles: () => (/* binding */ assignUserRoles),\n/* harmony export */   authenticateUser: () => (/* binding */ authenticateUser),\n/* harmony export */   batchDeleteUsers: () => (/* binding */ batchDeleteUsers),\n/* harmony export */   createRole: () => (/* binding */ createRole),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteRole: () => (/* binding */ deleteRole),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   findUserById: () => (/* binding */ findUserById),\n/* harmony export */   findUserByUsername: () => (/* binding */ findUserByUsername),\n/* harmony export */   getAllPermissions: () => (/* binding */ getAllPermissions),\n/* harmony export */   getAllRoles: () => (/* binding */ getAllRoles),\n/* harmony export */   getPermissionById: () => (/* binding */ getPermissionById),\n/* harmony export */   getRoleById: () => (/* binding */ getRoleById),\n/* harmony export */   getRolePermissions: () => (/* binding */ getRolePermissions),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserList: () => (/* binding */ getUserList),\n/* harmony export */   getUserRoles: () => (/* binding */ getUserRoles),\n/* harmony export */   updateLastLoginTime: () => (/* binding */ updateLastLoginTime),\n/* harmony export */   updatePermission: () => (/* binding */ updatePermission),\n/* harmony export */   updateRole: () => (/* binding */ updateRole),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_password__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/password */ \"(rsc)/./src/lib/password.ts\");\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! oracledb */ \"oracledb\");\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(oracledb__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\n * 根据用户ID查找用户\n */ async function getUserById(userId) {\n    try {\n        const sql = `\n      SELECT\n        u.ID,\n        u.USERNAME,\n        u.PASSWORD_HASH,\n        u.REAL_NAME,\n        u.EMAIL,\n        u.DEPARTMENT,\n        u.POSITION,\n        u.STATUS,\n        u.CREATED_AT,\n        u.UPDATED_AT,\n        u.LAST_LOGIN_AT\n      FROM USER_INFO u\n      WHERE u.ID = :userId\n        AND u.IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            userId\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const userRow = result.rows[0];\n        // 获取用户角色\n        const roles = await getUserRoles(userId);\n        return {\n            id: userRow.ID,\n            username: userRow.USERNAME,\n            realName: userRow.REAL_NAME,\n            email: userRow.EMAIL,\n            department: userRow.DEPARTMENT,\n            position: userRow.POSITION,\n            status: userRow.STATUS,\n            createdAt: userRow.CREATED_AT,\n            updatedAt: userRow.UPDATED_AT,\n            lastLoginAt: userRow.LAST_LOGIN_AT,\n            roles\n        };\n    } catch (error) {\n        console.error('获取用户失败:', error);\n        throw new Error('获取用户失败');\n    }\n}\n/**\n * 根据用户名查找用户\n */ async function findUserByUsername(username) {\n    try {\n        const sql = `\n      SELECT \n        u.ID,\n        u.USERNAME,\n        u.PASSWORD_HASH,\n        u.REAL_NAME,\n        u.EMAIL,\n        u.DEPARTMENT,\n        u.POSITION,\n        u.STATUS,\n        u.CREATED_AT,\n        u.UPDATED_AT,\n        u.LAST_LOGIN_AT\n      FROM USER_INFO u\n      WHERE u.USERNAME = :username \n        AND u.IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            username\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const userRow = result.rows[0];\n        // 获取用户角色\n        const roles = await getUserRoles(userRow.ID);\n        return {\n            id: userRow.ID,\n            username: userRow.USERNAME,\n            realName: userRow.REAL_NAME,\n            email: userRow.EMAIL,\n            department: userRow.DEPARTMENT,\n            position: userRow.POSITION,\n            status: userRow.STATUS,\n            createdAt: userRow.CREATED_AT,\n            updatedAt: userRow.UPDATED_AT,\n            lastLoginAt: userRow.LAST_LOGIN_AT,\n            roles\n        };\n    } catch (error) {\n        console.error('❌ 查找用户失败:', error);\n        throw new Error('查找用户失败');\n    }\n}\n/**\n * 根据用户ID查找用户\n */ async function findUserById(userId) {\n    try {\n        const sql = `\n      SELECT \n        u.ID,\n        u.USERNAME,\n        u.REAL_NAME,\n        u.EMAIL,\n        u.DEPARTMENT,\n        u.POSITION,\n        u.STATUS,\n        u.CREATED_AT,\n        u.UPDATED_AT,\n        u.LAST_LOGIN_AT\n      FROM USER_INFO u\n      WHERE u.ID = :userId \n        AND u.IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            userId\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const userRow = result.rows[0];\n        // 获取用户角色\n        const roles = await getUserRoles(userRow.ID);\n        return {\n            id: userRow.ID,\n            username: userRow.USERNAME,\n            realName: userRow.REAL_NAME,\n            email: userRow.EMAIL,\n            department: userRow.DEPARTMENT,\n            position: userRow.POSITION,\n            status: userRow.STATUS,\n            createdAt: userRow.CREATED_AT,\n            updatedAt: userRow.UPDATED_AT,\n            lastLoginAt: userRow.LAST_LOGIN_AT,\n            roles\n        };\n    } catch (error) {\n        console.error('❌ 查找用户失败:', error);\n        throw new Error('查找用户失败');\n    }\n}\n/**\n * 获取用户角色\n */ async function getUserRoles(userId) {\n    try {\n        const sql = `\n      SELECT \n        r.ID,\n        r.ROLE_CODE,\n        r.ROLE_NAME,\n        r.DESCRIPTION,\n        r.IS_ACTIVE\n      FROM USER_ROLE_INFO r\n      INNER JOIN USER_ROLE_MAPPING m ON r.ID = m.ROLE_ID\n      WHERE m.USER_ID = :userId \n        AND m.IS_DELETED = 0 \n        AND r.IS_DELETED = 0\n        AND r.IS_ACTIVE = 1\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            userId\n        });\n        if (!result.rows) {\n            return [];\n        }\n        const roles = [];\n        for (const row of result.rows){\n            const permissions = await getRolePermissions(row.ID);\n            roles.push({\n                id: row.ID,\n                roleCode: row.ROLE_CODE,\n                roleName: row.ROLE_NAME,\n                description: row.DESCRIPTION,\n                isActive: row.IS_ACTIVE === 1,\n                permissions\n            });\n        }\n        return roles;\n    } catch (error) {\n        console.error('❌ 获取用户角色失败:', error);\n        return [];\n    }\n}\n/**\n * 获取角色权限\n */ async function getRolePermissions(roleId) {\n    try {\n        const sql = `\n      SELECT \n        p.ID,\n        p.PERMISSION_CODE,\n        p.PERMISSION_NAME,\n        p.DESCRIPTION,\n        p.MODULE,\n        p.ACTION,\n        p.IS_ACTIVE,\n        p.CREATED_AT,\n        p.UPDATED_AT\n      FROM USER_PERMISSION_INFO p\n      INNER JOIN USER_ROLE_PERMISSION_MAPPING m ON p.ID = m.PERMISSION_ID\n      WHERE m.ROLE_ID = :roleId \n        AND m.IS_DELETED = 0 \n        AND p.IS_DELETED = 0\n        AND p.IS_ACTIVE = 1\n      ORDER BY p.MODULE, p.ACTION\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            roleId\n        });\n        if (!result.rows) {\n            return [];\n        }\n        return result.rows.map((row)=>({\n                id: row.ID,\n                code: row.PERMISSION_CODE,\n                name: row.PERMISSION_NAME,\n                description: row.DESCRIPTION,\n                module: row.MODULE,\n                action: row.ACTION,\n                isActive: row.IS_ACTIVE === 1,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT\n            }));\n    } catch (error) {\n        console.error('❌ 获取角色权限失败:', error);\n        return [];\n    }\n}\n/**\n * 获取所有权限列表\n */ async function getAllPermissions() {\n    try {\n        const sql = `\n      SELECT \n        ID,\n        PERMISSION_CODE,\n        PERMISSION_NAME,\n        DESCRIPTION,\n        MODULE,\n        ACTION,\n        IS_ACTIVE,\n        CREATED_AT,\n        UPDATED_AT\n      FROM USER_PERMISSION_INFO\n      WHERE IS_DELETED = 0\n      ORDER BY MODULE, ACTION\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql);\n        if (!result.rows) {\n            return [];\n        }\n        return result.rows.map((row)=>({\n                id: row.ID,\n                code: row.PERMISSION_CODE,\n                name: row.PERMISSION_NAME,\n                description: row.DESCRIPTION,\n                module: row.MODULE,\n                action: row.ACTION,\n                isActive: row.IS_ACTIVE === 1,\n                createdAt: row.CREATED_AT,\n                updatedAt: row.UPDATED_AT\n            }));\n    } catch (error) {\n        console.error('❌ 获取权限列表失败:', error);\n        throw new Error('获取权限列表失败');\n    }\n}\n/**\n * 根据ID获取权限信息\n */ async function getPermissionById(permissionId) {\n    try {\n        const sql = `\n      SELECT\n        ID,\n        PERMISSION_CODE,\n        PERMISSION_NAME,\n        DESCRIPTION,\n        MODULE,\n        ACTION,\n        IS_ACTIVE,\n        CREATED_AT,\n        UPDATED_AT\n      FROM USER_PERMISSION_INFO\n      WHERE ID = :permissionId AND IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            permissionId\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            id: row.ID,\n            code: row.PERMISSION_CODE,\n            name: row.PERMISSION_NAME,\n            description: row.DESCRIPTION,\n            module: row.MODULE,\n            action: row.ACTION,\n            isActive: row.IS_ACTIVE === 1,\n            createdAt: row.CREATED_AT,\n            updatedAt: row.UPDATED_AT\n        };\n    } catch (error) {\n        console.error('❌ 获取权限信息失败:', error);\n        throw new Error('获取权限信息失败');\n    }\n}\n/**\n * 更新权限信息\n */ async function updatePermission(permissionId, updateData) {\n    try {\n        const updateFields = [];\n        const params = {\n            permissionId\n        };\n        if (updateData.name !== undefined) {\n            updateFields.push('PERMISSION_NAME = :name');\n            params.name = updateData.name;\n        }\n        if (updateData.description !== undefined) {\n            updateFields.push('DESCRIPTION = :description');\n            params.description = updateData.description;\n        }\n        if (updateData.isActive !== undefined) {\n            updateFields.push('IS_ACTIVE = :isActive');\n            params.isActive = updateData.isActive ? 1 : 0;\n        }\n        if (updateFields.length === 0) {\n            throw new Error('没有提供要更新的字段');\n        }\n        updateFields.push('UPDATED_AT = CURRENT_TIMESTAMP');\n        const sql = `\n      UPDATE USER_PERMISSION_INFO\n      SET ${updateFields.join(', ')}\n      WHERE ID = :permissionId AND IS_DELETED = 0\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, params);\n    } catch (error) {\n        console.error('❌ 更新权限失败:', error);\n        throw new Error('更新权限失败');\n    }\n}\n/**\n * 分配角色权限\n */ async function assignRolePermissions(roleId, permissionIds) {\n    try {\n        // 先删除现有的角色权限关联（物理删除）\n        const deleteSql = `\n      DELETE FROM USER_ROLE_PERMISSION_MAPPING\n      WHERE ROLE_ID = :roleId\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(deleteSql, {\n            roleId\n        });\n        // 如果有新的权限ID，则插入新的关联\n        if (permissionIds.length > 0) {\n            // 获取下一个ID\n            const getNextIdSql = `SELECT NVL(MAX(ID), 0) + 1 as NEXT_ID FROM USER_ROLE_PERMISSION_MAPPING`;\n            const nextIdResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(getNextIdSql);\n            let nextId = nextIdResult.rows?.[0]?.NEXT_ID || 1;\n            const insertSql = `\n        INSERT INTO USER_ROLE_PERMISSION_MAPPING (ID, ROLE_ID, PERMISSION_ID, IS_DELETED, CREATED_AT, UPDATED_AT)\n        VALUES (:id, :roleId, :permissionId, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)\n      `;\n            for (const permissionId of permissionIds){\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(insertSql, {\n                    id: nextId++,\n                    roleId,\n                    permissionId\n                });\n            }\n        }\n    } catch (error) {\n        console.error('❌ 分配角色权限失败:', error);\n        throw new Error('分配角色权限失败');\n    }\n}\n/**\n * 获取所有角色列表\n */ async function getAllRoles() {\n    try {\n        const sql = `\n      SELECT \n        ID,\n        ROLE_CODE,\n        ROLE_NAME,\n        DESCRIPTION,\n        IS_ACTIVE,\n        CREATED_AT,\n        UPDATED_AT\n      FROM USER_ROLE_INFO\n      WHERE IS_DELETED = 0\n      ORDER BY ROLE_CODE\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql);\n        if (!result.rows) {\n            return [];\n        }\n        const roles = [];\n        for (const row of result.rows){\n            const permissions = await getRolePermissions(row.ID);\n            roles.push({\n                id: row.ID,\n                roleCode: row.ROLE_CODE,\n                roleName: row.ROLE_NAME,\n                description: row.DESCRIPTION,\n                isActive: row.IS_ACTIVE === 1,\n                permissions\n            });\n        }\n        return roles;\n    } catch (error) {\n        console.error('❌ 获取角色列表失败:', error);\n        throw new Error('获取角色列表失败');\n    }\n}\n/**\n * 验证用户登录\n */ async function authenticateUser(credentials) {\n    try {\n        const user = await findUserByUsername(credentials.username);\n        if (!user) {\n            return null;\n        }\n        // 检查用户状态\n        if (user.status !== 'ACTIVE') {\n            throw new Error('用户账户已被禁用');\n        }\n        // 获取密码哈希\n        const passwordSql = `\n      SELECT PASSWORD_HASH \n      FROM USER_INFO \n      WHERE USERNAME = :username AND IS_DELETED = 0\n    `;\n        const passwordResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(passwordSql, {\n            username: credentials.username\n        });\n        if (!passwordResult.rows || passwordResult.rows.length === 0) {\n            return null;\n        }\n        const passwordHash = passwordResult.rows[0].PASSWORD_HASH;\n        // 验证密码\n        const isPasswordValid = await (0,_lib_password__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(credentials.password, passwordHash);\n        if (!isPasswordValid) {\n            return null;\n        }\n        // 更新最后登录时间\n        await updateLastLoginTime(user.id);\n        return user;\n    } catch (error) {\n        console.error('❌ 用户认证失败:', error);\n        throw error;\n    }\n}\n/**\n * 更新最后登录时间\n */ async function updateLastLoginTime(userId) {\n    try {\n        const sql = `\n      UPDATE USER_INFO\n      SET LAST_LOGIN_AT = SYSDATE,\n          UPDATED_AT = SYSDATE\n      WHERE ID = :userId\n    `;\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            userId\n        });\n    } catch (error) {\n        console.error('❌ 更新登录时间失败:', error);\n    // 不抛出错误，因为这不是关键操作\n    }\n}\n/**\n * 创建用户\n */ async function createUser(userData) {\n    try {\n        return await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            // 检查用户名是否已存在\n            const existingUserSql = `\n        SELECT COUNT(*) as COUNT\n        FROM USER_INFO\n        WHERE USERNAME = :username AND IS_DELETED = 0\n      `;\n            const existingResult = await connection.execute(existingUserSql, {\n                username: userData.username\n            });\n            const existingCount = existingResult.rows[0].COUNT;\n            if (existingCount > 0) {\n                throw new Error('用户名已存在');\n            }\n            // 哈希密码\n            const passwordHash = await (0,_lib_password__WEBPACK_IMPORTED_MODULE_1__.hashPassword)(userData.password);\n            // 插入用户\n            const insertUserSql = `\n        INSERT INTO USER_INFO (\n          USERNAME, PASSWORD_HASH, REAL_NAME, EMAIL, DEPARTMENT, POSITION, STATUS, IS_DELETED, CREATED_BY\n        ) VALUES (\n          :username, :passwordHash, :realName, :email, :department, :position, 'ACTIVE', 0, 1\n        ) RETURNING ID INTO :userId\n      `;\n            const userResult = await connection.execute(insertUserSql, {\n                username: userData.username,\n                passwordHash,\n                realName: userData.realName,\n                email: userData.email,\n                department: userData.department,\n                position: userData.position,\n                userId: {\n                    type: (oracledb__WEBPACK_IMPORTED_MODULE_2___default().NUMBER),\n                    dir: (oracledb__WEBPACK_IMPORTED_MODULE_2___default().BIND_OUT)\n                }\n            });\n            const userId = userResult.outBinds.userId[0];\n            // 分配角色\n            if (userData.roleIds && userData.roleIds.length > 0) {\n                for (const roleId of userData.roleIds){\n                    const assignRoleSql = `\n            INSERT INTO USER_ROLE_MAPPING (\n              USER_ID, ROLE_ID, IS_DELETED, CREATED_BY\n            ) VALUES (\n              :userId, :roleId, 0, 1\n            )\n          `;\n                    await connection.execute(assignRoleSql, {\n                        userId,\n                        roleId\n                    });\n                }\n            }\n            // 返回创建的用户\n            const newUser = await findUserById(userId);\n            if (!newUser) {\n                throw new Error('创建用户后查询失败');\n            }\n            return newUser;\n        });\n    } catch (error) {\n        console.error('❌ 创建用户失败:', error);\n        throw error;\n    }\n}\n/**\n * 获取用户列表\n */ async function getUserList(params) {\n    try {\n        const { page = 1, pageSize = 10, search, status, department } = params;\n        // 构建查询条件\n        const conditions = [\n            'u.IS_DELETED = 0'\n        ];\n        const binds = {};\n        if (search) {\n            conditions.push('(UPPER(u.USERNAME) LIKE UPPER(:search) OR UPPER(u.REAL_NAME) LIKE UPPER(:search) OR UPPER(u.EMAIL) LIKE UPPER(:search))');\n            binds.search = `%${search}%`;\n        }\n        if (status) {\n            conditions.push('u.STATUS = :status');\n            binds.status = status;\n        }\n        if (department) {\n            conditions.push('UPPER(u.DEPARTMENT) LIKE UPPER(:department)');\n            binds.department = `%${department}%`;\n        }\n        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';\n        // 查询总数\n        const countSql = `\n      SELECT COUNT(*) as TOTAL\n      FROM USER_INFO u\n      ${whereClause}\n    `;\n        const countResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSql, binds);\n        const totalCount = countResult.rows[0].TOTAL;\n        // 查询数据\n        const dataSql = (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.buildPaginationSQL)(`\n      SELECT\n        u.ID,\n        u.USERNAME,\n        u.REAL_NAME,\n        u.EMAIL,\n        u.DEPARTMENT,\n        u.POSITION,\n        u.STATUS,\n        u.CREATED_AT,\n        u.UPDATED_AT,\n        u.LAST_LOGIN_AT\n      FROM USER_INFO u\n      ${whereClause}\n      ORDER BY u.CREATED_AT DESC\n    `, page, pageSize);\n        const dataResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(dataSql, binds);\n        const users = [];\n        if (dataResult.rows) {\n            for (const row of dataResult.rows){\n                const roles = await getUserRoles(row.ID);\n                users.push({\n                    id: row.ID,\n                    username: row.USERNAME,\n                    realName: row.REAL_NAME,\n                    email: row.EMAIL,\n                    department: row.DEPARTMENT,\n                    position: row.POSITION,\n                    status: row.STATUS,\n                    createdAt: row.CREATED_AT,\n                    updatedAt: row.UPDATED_AT,\n                    lastLoginAt: row.LAST_LOGIN_AT,\n                    roles\n                });\n            }\n        }\n        const totalPages = Math.ceil(totalCount / pageSize);\n        return {\n            users,\n            pagination: {\n                page,\n                pageSize,\n                totalCount,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        };\n    } catch (error) {\n        console.error('❌ 获取用户列表失败:', error);\n        throw new Error('获取用户列表失败');\n    }\n}\n/**\n * 更新用户信息\n */ async function updateUser(userId, userData) {\n    try {\n        return await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            const updateFields = [];\n            const binds = {\n                userId\n            };\n            if (userData.realName !== undefined) {\n                updateFields.push('REAL_NAME = :realName');\n                binds.realName = userData.realName;\n            }\n            if (userData.email !== undefined) {\n                updateFields.push('EMAIL = :email');\n                binds.email = userData.email;\n            }\n            if (userData.department !== undefined) {\n                updateFields.push('DEPARTMENT = :department');\n                binds.department = userData.department;\n            }\n            if (userData.position !== undefined) {\n                updateFields.push('POSITION = :position');\n                binds.position = userData.position;\n            }\n            if (userData.status !== undefined) {\n                updateFields.push('STATUS = :status');\n                binds.status = userData.status;\n            }\n            if (updateFields.length > 0) {\n                updateFields.push('UPDATED_AT = SYSDATE');\n                const updateSql = `\n          UPDATE USER_INFO\n          SET ${updateFields.join(', ')}\n          WHERE ID = :userId AND IS_DELETED = 0\n        `;\n                await connection.execute(updateSql, binds);\n            }\n            // 更新角色\n            if (userData.roleIds !== undefined) {\n                // 删除现有角色关联\n                const deleteRolesSql = `\n          UPDATE USER_ROLE_MAPPING\n          SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n          WHERE USER_ID = :userId AND IS_DELETED = 0\n        `;\n                await connection.execute(deleteRolesSql, {\n                    userId\n                });\n                // 添加新的角色关联\n                for (const roleId of userData.roleIds){\n                    const assignRoleSql = `\n            INSERT INTO USER_ROLE_MAPPING (\n              USER_ID, ROLE_ID, IS_DELETED, CREATED_BY\n            ) VALUES (\n              :userId, :roleId, 0, 1\n            )\n          `;\n                    await connection.execute(assignRoleSql, {\n                        userId,\n                        roleId\n                    });\n                }\n            }\n            // 返回更新后的用户\n            const updatedUser = await findUserById(userId);\n            if (!updatedUser) {\n                throw new Error('更新后查询用户失败');\n            }\n            return updatedUser;\n        });\n    } catch (error) {\n        console.error('❌ 更新用户失败:', error);\n        throw error;\n    }\n}\n/**\n * 删除用户\n */ async function deleteUser(userId) {\n    try {\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            // 软删除用户\n            const deleteUserSql = `\n        UPDATE USER_INFO\n        SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n        WHERE ID = :userId AND IS_DELETED = 0\n      `;\n            await connection.execute(deleteUserSql, {\n                userId\n            });\n            // 软删除用户角色关联\n            const deleteRolesSql = `\n        UPDATE USER_ROLE_MAPPING\n        SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n        WHERE USER_ID = :userId AND IS_DELETED = 0\n      `;\n            await connection.execute(deleteRolesSql, {\n                userId\n            });\n        });\n    } catch (error) {\n        console.error('❌ 删除用户失败:', error);\n        throw error;\n    }\n}\n/**\n * 批量删除用户\n */ async function batchDeleteUsers(userIds) {\n    try {\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            for (const userId of userIds){\n                // 软删除用户\n                const deleteUserSql = `\n          UPDATE USER_INFO\n          SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n          WHERE ID = :userId AND IS_DELETED = 0\n        `;\n                await connection.execute(deleteUserSql, {\n                    userId\n                });\n                // 软删除用户角色关联\n                const deleteRolesSql = `\n          UPDATE USER_ROLE_MAPPING\n          SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n          WHERE USER_ID = :userId AND IS_DELETED = 0\n        `;\n                await connection.execute(deleteRolesSql, {\n                    userId\n                });\n            }\n        });\n    } catch (error) {\n        console.error('❌ 批量删除用户失败:', error);\n        throw error;\n    }\n}\n/**\n * 分配用户角色\n */ async function assignUserRoles(userId, roleIds) {\n    try {\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            // 删除现有角色关联\n            const deleteRolesSql = `\n        UPDATE USER_ROLE_MAPPING\n        SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n        WHERE USER_ID = :userId AND IS_DELETED = 0\n      `;\n            await connection.execute(deleteRolesSql, {\n                userId\n            });\n            // 添加新的角色关联\n            for (const roleId of roleIds){\n                const assignRoleSql = `\n          INSERT INTO USER_ROLE_MAPPING (\n            USER_ID, ROLE_ID, IS_DELETED, CREATED_BY\n          ) VALUES (\n            :userId, :roleId, 0, 1\n          )\n        `;\n                await connection.execute(assignRoleSql, {\n                    userId,\n                    roleId\n                });\n            }\n        });\n    } catch (error) {\n        console.error('❌ 分配用户角色失败:', error);\n        throw error;\n    }\n}\n/**\n * 创建角色\n */ async function createRole(roleData) {\n    try {\n        return await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            // 检查角色代码是否已存在\n            const existingRoleSql = `\n        SELECT COUNT(*) as COUNT\n        FROM USER_ROLE_INFO\n        WHERE ROLE_CODE = :roleCode AND IS_DELETED = 0\n      `;\n            const existingResult = await connection.execute(existingRoleSql, {\n                roleCode: roleData.roleCode\n            });\n            const existingCount = existingResult.rows[0].COUNT;\n            if (existingCount > 0) {\n                throw new Error('角色代码已存在');\n            }\n            // 插入角色\n            const insertRoleSql = `\n        INSERT INTO USER_ROLE_INFO (\n          ROLE_CODE, ROLE_NAME, DESCRIPTION, IS_ACTIVE, IS_DELETED, CREATED_BY\n        ) VALUES (\n          :roleCode, :roleName, :description, 1, 0, 1\n        ) RETURNING ID INTO :roleId\n      `;\n            const roleResult = await connection.execute(insertRoleSql, {\n                roleCode: roleData.roleCode,\n                roleName: roleData.roleName,\n                description: roleData.description || null,\n                roleId: {\n                    type: (oracledb__WEBPACK_IMPORTED_MODULE_2___default().NUMBER),\n                    dir: (oracledb__WEBPACK_IMPORTED_MODULE_2___default().BIND_OUT)\n                }\n            });\n            const roleId = roleResult.outBinds.roleId[0];\n            // 分配权限\n            if (roleData.permissionIds && roleData.permissionIds.length > 0) {\n                for (const permissionId of roleData.permissionIds){\n                    // 生成一个简单的ID（使用时间戳的后6位 + 随机数）\n                    const mappingId = Date.now() % 1000000 * 1000 + Math.floor(Math.random() * 1000);\n                    const assignPermissionSql = `\n            INSERT INTO USER_ROLE_PERMISSION_MAPPING (\n              ID, ROLE_ID, PERMISSION_ID, IS_DELETED, CREATED_BY\n            ) VALUES (\n              :mappingId, :roleId, :permissionId, 0, 1\n            )\n          `;\n                    await connection.execute(assignPermissionSql, {\n                        mappingId,\n                        roleId,\n                        permissionId\n                    });\n                }\n            }\n            // 返回创建的角色\n            const newRole = await getRoleById(roleId);\n            if (!newRole) {\n                throw new Error('创建角色后查询失败');\n            }\n            return newRole;\n        });\n    } catch (error) {\n        console.error('❌ 创建角色失败:', error);\n        throw error;\n    }\n}\n/**\n * 根据ID获取角色\n */ async function getRoleById(roleId) {\n    try {\n        const sql = `\n      SELECT\n        ID,\n        ROLE_CODE,\n        ROLE_NAME,\n        DESCRIPTION,\n        IS_ACTIVE,\n        CREATED_AT,\n        UPDATED_AT\n      FROM USER_ROLE_INFO\n      WHERE ID = :roleId AND IS_DELETED = 0\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n            roleId\n        });\n        if (!result.rows || result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        const permissions = await getRolePermissions(row.ID);\n        return {\n            id: row.ID,\n            roleCode: row.ROLE_CODE,\n            roleName: row.ROLE_NAME,\n            description: row.DESCRIPTION,\n            isActive: row.IS_ACTIVE === 1,\n            permissions\n        };\n    } catch (error) {\n        console.error('❌ 获取角色失败:', error);\n        return null;\n    }\n}\n/**\n * 更新角色\n */ async function updateRole(roleId, roleData) {\n    try {\n        return await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            const updateFields = [];\n            const binds = {\n                roleId\n            };\n            if (roleData.roleName !== undefined) {\n                updateFields.push('ROLE_NAME = :roleName');\n                binds.roleName = roleData.roleName;\n            }\n            if (roleData.description !== undefined) {\n                updateFields.push('DESCRIPTION = :description');\n                binds.description = roleData.description;\n            }\n            if (roleData.isActive !== undefined) {\n                updateFields.push('IS_ACTIVE = :isActive');\n                binds.isActive = roleData.isActive ? 1 : 0;\n            }\n            if (updateFields.length > 0) {\n                updateFields.push('UPDATED_AT = SYSDATE');\n                const updateSql = `\n          UPDATE USER_ROLE_INFO\n          SET ${updateFields.join(', ')}\n          WHERE ID = :roleId AND IS_DELETED = 0\n        `;\n                await connection.execute(updateSql, binds);\n            }\n            // 更新权限\n            if (roleData.permissionIds !== undefined) {\n                // 删除现有权限关联\n                const deletePermissionsSql = `\n          UPDATE USER_ROLE_PERMISSION_MAPPING\n          SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n          WHERE ROLE_ID = :roleId AND IS_DELETED = 0\n        `;\n                await connection.execute(deletePermissionsSql, {\n                    roleId\n                });\n                // 添加新的权限关联\n                for (const permissionId of roleData.permissionIds){\n                    // 生成一个简单的ID（使用时间戳的后6位 + 随机数）\n                    const mappingId = Date.now() % 1000000 * 1000 + Math.floor(Math.random() * 1000);\n                    const assignPermissionSql = `\n            INSERT INTO USER_ROLE_PERMISSION_MAPPING (\n              ID, ROLE_ID, PERMISSION_ID, IS_DELETED, CREATED_BY\n            ) VALUES (\n              :mappingId, :roleId, :permissionId, 0, 1\n            )\n          `;\n                    await connection.execute(assignPermissionSql, {\n                        mappingId,\n                        roleId,\n                        permissionId\n                    });\n                }\n            }\n            // 返回更新后的角色\n            const updatedRole = await getRoleById(roleId);\n            if (!updatedRole) {\n                throw new Error('更新后查询角色失败');\n            }\n            return updatedRole;\n        });\n    } catch (error) {\n        console.error('❌ 更新角色失败:', error);\n        throw error;\n    }\n}\n/**\n * 删除角色\n */ async function deleteRole(roleId) {\n    try {\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeTransaction)(async (connection)=>{\n            // 检查是否有用户使用此角色\n            const userCountSql = `\n        SELECT COUNT(*) as COUNT\n        FROM USER_ROLE_MAPPING\n        WHERE ROLE_ID = :roleId AND IS_DELETED = 0\n      `;\n            const userCountResult = await connection.execute(userCountSql, {\n                roleId\n            });\n            const userCount = userCountResult.rows[0].COUNT;\n            if (userCount > 0) {\n                throw new Error('该角色正在被用户使用，无法删除');\n            }\n            // 软删除角色\n            const deleteRoleSql = `\n        UPDATE USER_ROLE_INFO\n        SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n        WHERE ID = :roleId AND IS_DELETED = 0\n      `;\n            await connection.execute(deleteRoleSql, {\n                roleId\n            });\n            // 软删除角色权限关联\n            const deletePermissionsSql = `\n        UPDATE USER_ROLE_PERMISSION_MAPPING\n        SET IS_DELETED = 1, UPDATED_AT = SYSDATE\n        WHERE ROLE_ID = :roleId AND IS_DELETED = 0\n      `;\n            await connection.execute(deletePermissionsSql, {\n                roleId\n            });\n        });\n    } catch (error) {\n        console.error('❌ 删除角色失败:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/user-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "oracledb":
/*!***************************!*\
  !*** external "oracledb" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("oracledb");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();