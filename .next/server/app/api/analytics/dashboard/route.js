/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/dashboard/route";
exports.ids = ["app/api/analytics/dashboard/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdashboard%2Froute&page=%2Fapi%2Fanalytics%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdashboard%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdashboard%2Froute&page=%2Fapi%2Fanalytics%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdashboard%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Users_wangfeng_Documents_mediinspect_v2_src_app_api_analytics_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/analytics/dashboard/route.ts */ \"(rsc)/./src/app/api/analytics/dashboard/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/dashboard/route\",\n        pathname: \"/api/analytics/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/dashboard/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Users/<USER>/Documents/mediinspect-v2/src/app/api/analytics/dashboard/route.ts\",\n    nextConfigOutput,\n    userland: _Users_wangfeng_Documents_mediinspect_v2_src_app_api_analytics_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/analytics/dashboard/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdashboard%2Froute&page=%2Fapi%2Fanalytics%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdashboard%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/dashboard/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/analytics/dashboard/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _lib_api_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/handler */ \"(rsc)/./src/lib/api/handler.ts\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.ts\");\n/**\n * 系统API接口\n * \n * 🔐 认证要求：需要有效的访问令牌\n * 🛡️  权限要求：根据具体API而定\n * 📝 审计日志：自动记录所有操作\n * \n * @description 系统API接口\n * @param request HTTP请求对象，包含查询参数或请求体\n * @returns 标准API响应格式，包含数据和元信息\n * @example\n * // 请求示例\n * GET /api/endpoint?page=1&pageSize=20\n * \n * // 响应示例\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"操作成功\",\n *   \"data\": { ... },\n *   \"meta\": { \"timestamp\": \"...\", \"performance\": { ... } }\n * }\n */ /**\n * 数据分析API\n * 已通过批量重构工具重构以符合API规范\n * TODO: 需要实现具体的业务逻辑\n */ \n\n\n// 查询参数验证schema\nconst querySchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional().transform((val)=>val ? parseInt(val) : 1),\n    pageSize: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional().transform((val)=>val ? parseInt(val) : 20),\n    search: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n});\n/**\n * GET API\n * @description 获取数据列表\n */ // 响应格式说明：\n// withAuth/withAuthAndValidation 处理器自动包装返回值为标准格式：\n// {\n//   \"success\": true,\n//   \"code\": \"SUCCESS\",\n//   \"message\": \"操作成功\",\n//   \"data\": <返回的数据>,\n//   \"meta\": {\n//     \"timestamp\": \"2024-01-01T00:00:00Z\",\n//     \"version\": \"1.0.0\",\n//     \"performance\": { \"executionTime\": 100, \"requestId\": \"req_xxx\" }\n//   }\n// }\nconst GET = (0,_lib_api_handler__WEBPACK_IMPORTED_MODULE_0__.withAuth)(async (request)=>{\n    try {\n        const url = new URL(request.url);\n        const queryParams = Object.fromEntries(url.searchParams.entries());\n        const validatedParams = querySchema.parse(queryParams);\n        // TODO: 实现具体的业务逻辑\n        return {\n            message: 'API已重构，待实现具体逻辑',\n            params: validatedParams\n        };\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n            throw new _lib_api_response__WEBPACK_IMPORTED_MODULE_1__.ApiError(_lib_api_response__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors);\n        }\n        if (error instanceof _lib_api_response__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n            throw error;\n        }\n        throw new _lib_api_response__WEBPACK_IMPORTED_MODULE_1__.ApiError(_lib_api_response__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.UNKNOWN_ERROR, '操作失败');\n    }\n}, [\n    'ADMIN',\n    'OPERATOR'\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/handler.ts":
/*!********************************!*\
  !*** ./src/lib/api/handler.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiHandler: () => (/* binding */ createApiHandler),\n/* harmony export */   publicApi: () => (/* binding */ publicApi),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthAndValidation: () => (/* binding */ withAuthAndValidation),\n/* harmony export */   withValidation: () => (/* binding */ withValidation)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./response */ \"(rsc)/./src/lib/api/response.ts\");\n/* harmony import */ var _lib_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/jwt */ \"(rsc)/./src/lib/jwt.ts\");\n/* harmony import */ var _lib_security_audit_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/security/audit-middleware */ \"(rsc)/./src/lib/security/audit-middleware.ts\");\n// 注意：此文件为API处理器工具库，不是API路由文件\n// 提供统一的API处理器，包含认证、验证、错误处理\n// 本身不需要认证检查，为其他API提供认证功能\n/**\n * 统一API处理器\n * 实现P1-002 API接口规范化目标：\n * - 统一错误处理\n * - 请求验证\n * - 性能监控\n * - 日志记录\n *\n * @example 基本使用示例\n * ```typescript\n * // 带认证的API处理器\n * export const GET = withAuth(async (request, context) => {\n *   const data = await getUserData(context.user.id)\n *   return data\n * })\n *\n * // 响应示例\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"操作成功\",\n *   \"data\": { \"userId\": 123, \"name\": \"张三\" },\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\",\n *     \"performance\": { \"executionTime\": 150, \"requestId\": \"req_123\" }\n *   }\n * }\n * ```\n *\n * @example 带验证的API处理器\n * ```typescript\n * const schema = z.object({\n *   name: z.string(),\n *   email: z.string().email()\n * })\n *\n * export const POST = withAuthAndValidation(schema, async (request, context) => {\n *   const { name, email } = context.validatedData\n *   const user = await createUser({ name, email })\n *   return user\n * })\n *\n * // 错误响应示例\n * {\n *   \"success\": false,\n *   \"code\": \"VALIDATION_ERROR\",\n *   \"message\": \"请求参数验证失败\",\n *   \"details\": [\n *     { \"path\": [\"email\"], \"message\": \"Invalid email format\" }\n *   ],\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\"\n *   }\n * }\n * ```\n */ \n\n\n\n/**\n * 统一API处理器\n */ function createApiHandler(handler, options = {}) {\n    return async (request)=>{\n        const startTime = Date.now();\n        const requestId = generateRequestId();\n        // 创建请求上下文\n        const context = {\n            requestId,\n            startTime,\n            ip: getClientIP(request),\n            userAgent: request.headers.get('user-agent') || 'Unknown'\n        };\n        try {\n            // 1. 身份验证\n            if (options.requireAuth) {\n                context.user = await authenticateRequest(request);\n            }\n            // 2. 权限检查\n            if (options.requiredRoles && context.user) {\n                checkUserRoles(context.user.roles, options.requiredRoles);\n            }\n            // 3. 请求验证\n            if (options.validateSchema) {\n                await validateRequest(request, options.validateSchema);\n            }\n            // 4. 速率限制\n            if (options.rateLimit) {\n                await checkRateLimit(context.ip, options.rateLimit);\n            }\n            // 5. 执行处理器\n            const result = await executeWithTimeout(()=>handler(request, context), options.timeout || 30000);\n            // 6. 记录审计日志\n            if (context.user) {\n                await (0,_lib_security_audit_middleware__WEBPACK_IMPORTED_MODULE_2__.recordAuditLog)({\n                    userId: context.user.id,\n                    userName: context.user.username,\n                    action: request.method,\n                    resource: request.nextUrl.pathname,\n                    operationResult: 'SUCCESS',\n                    operationDescription: `API调用成功: ${request.method} ${request.nextUrl.pathname}`,\n                    ipAddress: context.ip,\n                    userAgent: context.userAgent,\n                    timestamp: new Date(),\n                    executionTime: Date.now() - startTime\n                });\n            }\n            // 7. 构建性能元数据\n            const performance = {\n                executionTime: Date.now() - startTime,\n                requestId: context.requestId\n            };\n            // 8. 返回成功响应\n            return (0,_response__WEBPACK_IMPORTED_MODULE_0__.successResponse)(result, undefined, {\n                performance\n            });\n        } catch (error) {\n            // 错误处理\n            return handleApiError(error, context, startTime);\n        }\n    };\n}\n/**\n * 身份验证\n */ async function authenticateRequest(request) {\n    const authHeader = request.headers.get('authorization');\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n        throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.UNAUTHORIZED, '缺少访问令牌');\n    }\n    const token = authHeader.substring(7);\n    const payload = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.verifyAccessToken)(token);\n    if (!payload) {\n        throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.TOKEN_EXPIRED, '访问令牌已过期');\n    }\n    return {\n        id: payload.userId,\n        username: payload.username,\n        roles: payload.roles || []\n    };\n}\n/**\n * 权限检查\n */ function checkUserRoles(userRoles, requiredRoles) {\n    const hasRequiredRole = requiredRoles.some((role)=>userRoles.includes(role));\n    if (!hasRequiredRole) {\n        throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.INSUFFICIENT_PERMISSIONS, `需要以下权限之一: ${requiredRoles.join(', ')}`);\n    }\n}\n/**\n * 请求验证\n */ async function validateRequest(request, schema) {\n    try {\n        let data;\n        if (request.method === 'GET') {\n            // 验证查询参数\n            const url = new URL(request.url);\n            data = Object.fromEntries(url.searchParams.entries());\n        } else {\n            // 验证请求体\n            const contentType = request.headers.get('content-type');\n            if (contentType?.includes('application/json')) {\n                data = await request.json();\n            } else if (contentType?.includes('application/x-www-form-urlencoded')) {\n                const formData = await request.formData();\n                data = Object.fromEntries(formData.entries());\n            } else {\n                throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.INVALID_REQUEST, '不支持的内容类型');\n            }\n        }\n        schema.parse(data);\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n            throw new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors);\n        }\n        throw error;\n    }\n}\n/**\n * 速率限制检查\n */ async function checkRateLimit(ip, rateLimit) {\n// 这里应该实现实际的速率限制逻辑\n// 可以使用Redis或内存存储\n// 暂时跳过实现\n}\n/**\n * 超时执行\n */ async function executeWithTimeout(fn, timeoutMs) {\n    return Promise.race([\n        fn(),\n        new Promise((_, reject)=>setTimeout(()=>reject(new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.TIMEOUT_ERROR)), timeoutMs))\n    ]);\n}\n/**\n * 错误处理\n */ async function handleApiError(error, context, startTime) {\n    let apiError;\n    if (error instanceof _response__WEBPACK_IMPORTED_MODULE_0__.ApiError) {\n        apiError = error;\n    } else if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n        apiError = new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.VALIDATION_ERROR, '请求参数验证失败', error.errors);\n    } else {\n        // 记录未知错误\n        console.error('Unhandled API error:', error);\n        apiError = new _response__WEBPACK_IMPORTED_MODULE_0__.ApiError(_response__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.UNKNOWN_ERROR, '服务器内部错误');\n    }\n    // 记录错误审计日志\n    if (context.user) {\n        await (0,_lib_security_audit_middleware__WEBPACK_IMPORTED_MODULE_2__.recordAuditLog)({\n            userId: context.user.id,\n            userName: context.user.username,\n            action: 'ERROR',\n            resource: 'API',\n            operationResult: 'FAILED',\n            operationDescription: `API调用失败: ${apiError.message}`,\n            ipAddress: context.ip,\n            userAgent: context.userAgent,\n            timestamp: new Date(),\n            executionTime: Date.now() - startTime\n        }).catch(console.error);\n    }\n    return (0,_response__WEBPACK_IMPORTED_MODULE_0__.errorResponse)(apiError.code, apiError.message, apiError.details);\n}\n/**\n * 生成请求ID\n */ function generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n/**\n * 获取客户端IP\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    if (forwarded) {\n        return forwarded.split(',')[0]?.trim() || 'unknown';\n    }\n    return realIP || 'unknown';\n}\n/**\n * 便捷的API处理器创建函数\n */ const withAuth = (handler, requiredRoles)=>createApiHandler(handler, {\n        requireAuth: true,\n        requiredRoles\n    });\nconst withValidation = (handler, schema)=>createApiHandler(handler, {\n        validateSchema: schema\n    });\nconst withAuthAndValidation = (handler, schema, requiredRoles)=>createApiHandler(handler, {\n        requireAuth: true,\n        requiredRoles,\n        validateSchema: schema\n    });\nconst publicApi = (handler)=>createApiHandler(handler, {\n        requireAuth: false\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/handler.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.ts":
/*!*********************************!*\
  !*** ./src/lib/api/response.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   ErrorCodeToHttpStatus: () => (/* binding */ ErrorCodeToHttpStatus),\n/* harmony export */   ErrorCodeToMessage: () => (/* binding */ ErrorCodeToMessage),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createNextResponse: () => (/* binding */ createNextResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// 注意：此文件为响应格式工具库，不是API路由文件\n// 提供统一的响应格式和错误处理工具\n// 不需要认证检查，由API路由文件使用\n/**\n * 统一API响应格式\n * 实现P1-002 API接口规范化目标：\n * - 统一响应格式\n * - 完整错误处理\n * - 标准化接口规范\n *\n * @param data 响应数据，可以是任意类型\n * @param message 响应消息，默认为\"操作成功\"\n * @param meta 元数据信息，包含时间戳、版本等\n * @returns 标准化的API响应对象\n *\n * @example 成功响应示例\n * ```typescript\n * // 使用createSuccessResponse\n * const response = createSuccessResponse(\n *   { userId: 123, name: \"张三\" },\n *   \"获取用户信息成功\"\n * )\n *\n * // 响应格式\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"获取用户信息成功\",\n *   \"data\": { \"userId\": 123, \"name\": \"张三\" },\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\"\n *   }\n * }\n * ```\n *\n * @example 错误响应示例\n * ```typescript\n * // 使用createErrorResponse\n * const errorResponse = createErrorResponse(\n *   ErrorCode.VALIDATION_ERROR,\n *   \"请求参数验证失败\",\n *   { field: \"email\", message: \"邮箱格式不正确\" }\n * )\n *\n * // 错误响应格式\n * {\n *   \"success\": false,\n *   \"code\": \"VALIDATION_ERROR\",\n *   \"message\": \"请求参数验证失败\",\n *   \"details\": { \"field\": \"email\", \"message\": \"邮箱格式不正确\" },\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\"\n *   }\n * }\n * ```\n *\n * @example 分页响应示例\n * ```typescript\n * // 使用createPaginatedResponse\n * const paginatedResponse = createPaginatedResponse(\n *   [{ id: 1, name: \"用户1\" }, { id: 2, name: \"用户2\" }],\n *   { page: 1, pageSize: 20, total: 100, totalPages: 5, hasNext: true, hasPrev: false },\n *   \"获取用户列表成功\"\n * )\n *\n * // 分页响应格式\n * {\n *   \"success\": true,\n *   \"code\": \"SUCCESS\",\n *   \"message\": \"获取用户列表成功\",\n *   \"data\": [{ \"id\": 1, \"name\": \"用户1\" }, { \"id\": 2, \"name\": \"用户2\" }],\n *   \"meta\": {\n *     \"timestamp\": \"2024-01-01T00:00:00Z\",\n *     \"version\": \"1.0.0\",\n *     \"pagination\": {\n *       \"page\": 1,\n *       \"pageSize\": 20,\n *       \"total\": 100,\n *       \"totalPages\": 5,\n *       \"hasNext\": true,\n *       \"hasPrev\": false\n *     }\n *   }\n * }\n * ```\n */ \n// 错误码枚举\nvar ErrorCode = /*#__PURE__*/ function(ErrorCode) {\n    // 通用错误\n    ErrorCode[\"SUCCESS\"] = \"SUCCESS\";\n    ErrorCode[\"UNKNOWN_ERROR\"] = \"UNKNOWN_ERROR\";\n    ErrorCode[\"INVALID_REQUEST\"] = \"INVALID_REQUEST\";\n    ErrorCode[\"VALIDATION_ERROR\"] = \"VALIDATION_ERROR\";\n    // 认证授权错误\n    ErrorCode[\"UNAUTHORIZED\"] = \"UNAUTHORIZED\";\n    ErrorCode[\"FORBIDDEN\"] = \"FORBIDDEN\";\n    ErrorCode[\"TOKEN_EXPIRED\"] = \"TOKEN_EXPIRED\";\n    ErrorCode[\"INVALID_TOKEN\"] = \"INVALID_TOKEN\";\n    // 资源错误\n    ErrorCode[\"NOT_FOUND\"] = \"NOT_FOUND\";\n    ErrorCode[\"RESOURCE_EXISTS\"] = \"RESOURCE_EXISTS\";\n    ErrorCode[\"RESOURCE_CONFLICT\"] = \"RESOURCE_CONFLICT\";\n    // 业务错误\n    ErrorCode[\"BUSINESS_ERROR\"] = \"BUSINESS_ERROR\";\n    ErrorCode[\"OPERATION_FAILED\"] = \"OPERATION_FAILED\";\n    ErrorCode[\"INSUFFICIENT_PERMISSIONS\"] = \"INSUFFICIENT_PERMISSIONS\";\n    // 数据库错误\n    ErrorCode[\"DATABASE_ERROR\"] = \"DATABASE_ERROR\";\n    ErrorCode[\"CONNECTION_ERROR\"] = \"CONNECTION_ERROR\";\n    ErrorCode[\"QUERY_ERROR\"] = \"QUERY_ERROR\";\n    // 外部服务错误\n    ErrorCode[\"EXTERNAL_SERVICE_ERROR\"] = \"EXTERNAL_SERVICE_ERROR\";\n    ErrorCode[\"NETWORK_ERROR\"] = \"NETWORK_ERROR\";\n    ErrorCode[\"TIMEOUT_ERROR\"] = \"TIMEOUT_ERROR\";\n    // 限流错误\n    ErrorCode[\"RATE_LIMIT_EXCEEDED\"] = \"RATE_LIMIT_EXCEEDED\";\n    ErrorCode[\"QUOTA_EXCEEDED\"] = \"QUOTA_EXCEEDED\";\n    // 文件错误\n    ErrorCode[\"FILE_NOT_FOUND\"] = \"FILE_NOT_FOUND\";\n    ErrorCode[\"FILE_TOO_LARGE\"] = \"FILE_TOO_LARGE\";\n    ErrorCode[\"INVALID_FILE_TYPE\"] = \"INVALID_FILE_TYPE\";\n    // 医保业务特定错误\n    ErrorCode[\"MEDICAL_CASE_NOT_FOUND\"] = \"MEDICAL_CASE_NOT_FOUND\";\n    ErrorCode[\"INVALID_MEDICAL_DATA\"] = \"INVALID_MEDICAL_DATA\";\n    ErrorCode[\"SUPERVISION_RULE_ERROR\"] = \"SUPERVISION_RULE_ERROR\";\n    ErrorCode[\"KNOWLEDGE_BASE_ERROR\"] = \"KNOWLEDGE_BASE_ERROR\";\n    return ErrorCode;\n}({});\n// 错误码对应的HTTP状态码\nconst ErrorCodeToHttpStatus = {\n    [\"SUCCESS\"]: 200,\n    [\"UNKNOWN_ERROR\"]: 500,\n    [\"INVALID_REQUEST\"]: 400,\n    [\"VALIDATION_ERROR\"]: 400,\n    [\"UNAUTHORIZED\"]: 401,\n    [\"FORBIDDEN\"]: 403,\n    [\"TOKEN_EXPIRED\"]: 401,\n    [\"INVALID_TOKEN\"]: 401,\n    [\"NOT_FOUND\"]: 404,\n    [\"RESOURCE_EXISTS\"]: 409,\n    [\"RESOURCE_CONFLICT\"]: 409,\n    [\"BUSINESS_ERROR\"]: 400,\n    [\"OPERATION_FAILED\"]: 400,\n    [\"INSUFFICIENT_PERMISSIONS\"]: 403,\n    [\"DATABASE_ERROR\"]: 500,\n    [\"CONNECTION_ERROR\"]: 500,\n    [\"QUERY_ERROR\"]: 500,\n    [\"EXTERNAL_SERVICE_ERROR\"]: 502,\n    [\"NETWORK_ERROR\"]: 502,\n    [\"TIMEOUT_ERROR\"]: 504,\n    [\"RATE_LIMIT_EXCEEDED\"]: 429,\n    [\"QUOTA_EXCEEDED\"]: 429,\n    [\"FILE_NOT_FOUND\"]: 404,\n    [\"FILE_TOO_LARGE\"]: 413,\n    [\"INVALID_FILE_TYPE\"]: 400,\n    [\"MEDICAL_CASE_NOT_FOUND\"]: 404,\n    [\"INVALID_MEDICAL_DATA\"]: 400,\n    [\"SUPERVISION_RULE_ERROR\"]: 400,\n    [\"KNOWLEDGE_BASE_ERROR\"]: 400\n};\n// 错误码对应的中文消息\nconst ErrorCodeToMessage = {\n    [\"SUCCESS\"]: '操作成功',\n    [\"UNKNOWN_ERROR\"]: '未知错误',\n    [\"INVALID_REQUEST\"]: '请求参数无效',\n    [\"VALIDATION_ERROR\"]: '数据验证失败',\n    [\"UNAUTHORIZED\"]: '未授权访问',\n    [\"FORBIDDEN\"]: '访问被禁止',\n    [\"TOKEN_EXPIRED\"]: '登录已过期',\n    [\"INVALID_TOKEN\"]: '无效的访问令牌',\n    [\"NOT_FOUND\"]: '资源不存在',\n    [\"RESOURCE_EXISTS\"]: '资源已存在',\n    [\"RESOURCE_CONFLICT\"]: '资源冲突',\n    [\"BUSINESS_ERROR\"]: '业务处理失败',\n    [\"OPERATION_FAILED\"]: '操作失败',\n    [\"INSUFFICIENT_PERMISSIONS\"]: '权限不足',\n    [\"DATABASE_ERROR\"]: '数据库错误',\n    [\"CONNECTION_ERROR\"]: '连接失败',\n    [\"QUERY_ERROR\"]: '查询失败',\n    [\"EXTERNAL_SERVICE_ERROR\"]: '外部服务错误',\n    [\"NETWORK_ERROR\"]: '网络错误',\n    [\"TIMEOUT_ERROR\"]: '请求超时',\n    [\"RATE_LIMIT_EXCEEDED\"]: '请求频率超限',\n    [\"QUOTA_EXCEEDED\"]: '配额已用完',\n    [\"FILE_NOT_FOUND\"]: '文件不存在',\n    [\"FILE_TOO_LARGE\"]: '文件过大',\n    [\"INVALID_FILE_TYPE\"]: '文件类型不支持',\n    [\"MEDICAL_CASE_NOT_FOUND\"]: '医疗案例不存在',\n    [\"INVALID_MEDICAL_DATA\"]: '医疗数据格式错误',\n    [\"SUPERVISION_RULE_ERROR\"]: '监管规则处理失败',\n    [\"KNOWLEDGE_BASE_ERROR\"]: '知识库操作失败'\n};\n// API错误类\nclass ApiError extends Error {\n    constructor(code, message, details){\n        super(message || ErrorCodeToMessage[code]);\n        this.name = 'ApiError';\n        this.code = code;\n        this.httpStatus = ErrorCodeToHttpStatus[code];\n        this.details = details;\n    }\n}\n// 成功响应构建器\nfunction createSuccessResponse(data, message, meta) {\n    try {\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: message || ErrorCodeToMessage[\"SUCCESS\"],\n            data,\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0',\n                ...meta\n            }\n        };\n    } catch (error) {\n        console.error('创建成功响应失败:', error);\n        // 返回基本的成功响应\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: '操作成功',\n            data,\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            }\n        };\n    }\n}\n// 错误响应构建器\nfunction createErrorResponse(code, message, details) {\n    try {\n        return {\n            success: false,\n            code,\n            message: message || ErrorCodeToMessage[code] || '未知错误',\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            },\n            ...details && {\n                details\n            }\n        };\n    } catch (error) {\n        console.error('创建错误响应失败:', error);\n        // 返回基本的错误响应\n        return {\n            success: false,\n            code: \"UNKNOWN_ERROR\",\n            message: '系统错误',\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            }\n        };\n    }\n}\n// 分页响应构建器\nfunction createPaginatedResponse(data, pagination, message, performance) {\n    try {\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: message || ErrorCodeToMessage[\"SUCCESS\"],\n            data,\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0',\n                pagination,\n                ...performance && {\n                    performance\n                }\n            }\n        };\n    } catch (error) {\n        console.error('创建分页响应失败:', error);\n        // 返回基本的分页响应\n        return {\n            success: true,\n            code: \"SUCCESS\",\n            message: '获取数据成功',\n            data: data || [],\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0',\n                pagination: pagination || {\n                    page: 1,\n                    pageSize: 20,\n                    total: 0,\n                    totalPages: 0,\n                    hasNext: false,\n                    hasPrev: false\n                }\n            }\n        };\n    }\n}\n// NextResponse包装器\nfunction createNextResponse(response) {\n    try {\n        const httpStatus = response.success ? 200 : ErrorCodeToHttpStatus[response.code] || 500;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: httpStatus\n        });\n    } catch (error) {\n        console.error('创建NextResponse失败:', error);\n        // 返回基本的错误响应\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            code: \"UNKNOWN_ERROR\",\n            message: '系统错误',\n            meta: {\n                timestamp: new Date().toISOString(),\n                version: '1.0.0'\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n// 成功响应快捷方法\nfunction successResponse(data, message, meta) {\n    try {\n        return createNextResponse(createSuccessResponse(data, message, meta));\n    } catch (error) {\n        console.error('创建成功响应失败:', error);\n        return createNextResponse(createErrorResponse(\"UNKNOWN_ERROR\", '创建响应失败'));\n    }\n}\n// 错误响应快捷方法\nfunction errorResponse(code, message, details) {\n    try {\n        return createNextResponse(createErrorResponse(code, message, details));\n    } catch (error) {\n        console.error('创建错误响应失败:', error);\n        return createNextResponse(createErrorResponse(\"UNKNOWN_ERROR\", '创建错误响应失败'));\n    }\n}\n// 分页响应快捷方法\nfunction paginatedResponse(data, pagination, message, performance) {\n    try {\n        return createNextResponse(createPaginatedResponse(data, pagination, message, performance));\n    } catch (error) {\n        console.error('创建分页响应失败:', error);\n        return createNextResponse(createErrorResponse(\"UNKNOWN_ERROR\", '创建分页响应失败'));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/jwt.ts":
/*!************************!*\
  !*** ./src/lib/jwt.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractTokenFromHeader: () => (/* binding */ extractTokenFromHeader),\n/* harmony export */   generateAccessToken: () => (/* binding */ generateAccessToken),\n/* harmony export */   generateRefreshToken: () => (/* binding */ generateRefreshToken),\n/* harmony export */   generateTokenPair: () => (/* binding */ generateTokenPair),\n/* harmony export */   getTokenRemainingTime: () => (/* binding */ getTokenRemainingTime),\n/* harmony export */   getUserPermissionLevel: () => (/* binding */ getUserPermissionLevel),\n/* harmony export */   hasAllRoles: () => (/* binding */ hasAllRoles),\n/* harmony export */   hasAnyRole: () => (/* binding */ hasAnyRole),\n/* harmony export */   hasPermissionLevel: () => (/* binding */ hasPermissionLevel),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isTokenExpiringSoon: () => (/* binding */ isTokenExpiringSoon),\n/* harmony export */   verifyAccessToken: () => (/* binding */ verifyAccessToken),\n/* harmony export */   verifyRefreshToken: () => (/* binding */ verifyRefreshToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\n// JWT配置\nconst JWT_SECRET = process.env['JWT_SECRET'] || 'mediinspect-v2-secret-key';\nconst JWT_EXPIRES_IN = process.env['JWT_EXPIRES_IN'] || '24h';\nconst JWT_REFRESH_SECRET = process.env['JWT_REFRESH_SECRET'] || 'mediinspect-v2-refresh-secret';\nconst JWT_REFRESH_EXPIRES_IN = process.env['JWT_REFRESH_EXPIRES_IN'] || '7d';\n/**\n * 生成访问令牌\n */ function generateAccessToken(user) {\n    const payload = {\n        userId: user.id,\n        username: user.username,\n        roles: user.roles.map((role)=>role.roleCode)\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN,\n        issuer: 'mediinspect-v2',\n        audience: 'mediinspect-users'\n    });\n}\n/**\n * 生成刷新令牌\n */ function generateRefreshToken(userId) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n        userId,\n        type: 'refresh'\n    }, JWT_REFRESH_SECRET, {\n        expiresIn: JWT_REFRESH_EXPIRES_IN,\n        issuer: 'mediinspect-v2',\n        audience: 'mediinspect-users'\n    });\n}\n/**\n * 验证访问令牌\n */ function verifyAccessToken(token) {\n    // 检查是否在服务端环境\n    if (false) {}\n    try {\n        if (!JWT_SECRET) {\n            console.error('❌ JWT_SECRET未配置');\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: 'mediinspect-v2',\n            audience: 'mediinspect-users'\n        });\n        return decoded;\n    } catch (error) {\n        console.error('❌ 访问令牌验证失败:', error);\n        return null;\n    }\n}\n/**\n * 验证刷新令牌\n */ function verifyRefreshToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_REFRESH_SECRET, {\n            issuer: 'mediinspect-v2',\n            audience: 'mediinspect-users'\n        });\n        if (decoded.type !== 'refresh') {\n            throw new Error('Invalid token type');\n        }\n        return {\n            userId: decoded.userId\n        };\n    } catch (error) {\n        console.error('❌ 刷新令牌验证失败:', error);\n        return null;\n    }\n}\n/**\n * 从请求头中提取令牌\n */ function extractTokenFromHeader(authHeader) {\n    if (!authHeader) return null;\n    const parts = authHeader.split(' ');\n    if (parts.length !== 2 || parts[0] !== 'Bearer') {\n        return null;\n    }\n    return parts[1] || null;\n}\n/**\n * 检查令牌是否即将过期\n */ function isTokenExpiringSoon(token, thresholdMinutes = 30) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(token);\n        if (!decoded || !decoded.exp) return true;\n        const now = Math.floor(Date.now() / 1000);\n        const threshold = thresholdMinutes * 60;\n        return decoded.exp - now < threshold;\n    } catch (error) {\n        return true;\n    }\n}\n/**\n * 获取令牌剩余时间（秒）\n */ function getTokenRemainingTime(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(token);\n        if (!decoded || !decoded.exp) return 0;\n        const now = Math.floor(Date.now() / 1000);\n        return Math.max(0, decoded.exp - now);\n    } catch (error) {\n        return 0;\n    }\n}\n/**\n * 检查用户是否有指定角色\n */ function hasRole(userRoles, requiredRole) {\n    if (false) {}\n    return userRoles.includes(requiredRole);\n}\n/**\n * 检查用户是否有任一指定角色\n */ function hasAnyRole(userRoles, requiredRoles) {\n    return requiredRoles.some((role)=>userRoles.includes(role));\n}\n/**\n * 检查用户是否有所有指定角色\n */ function hasAllRoles(userRoles, requiredRoles) {\n    return requiredRoles.every((role)=>userRoles.includes(role));\n}\n/**\n * 角色权限级别映射\n */ const ROLE_LEVELS = {\n    'ADMIN': 5,\n    'SUPERVISOR': 4,\n    'AUDITOR': 3,\n    'OPERATOR': 2,\n    'VIEWER': 1\n};\n/**\n * 检查用户是否有足够的权限级别\n */ function hasPermissionLevel(userRoles, requiredLevel) {\n    const userLevel = Math.max(...userRoles.map((role)=>ROLE_LEVELS[role] || 0));\n    return userLevel >= requiredLevel;\n}\n/**\n * 获取用户最高权限级别\n */ function getUserPermissionLevel(userRoles) {\n    return Math.max(...userRoles.map((role)=>ROLE_LEVELS[role] || 0));\n}\n/**\n * 生成令牌对\n */ function generateTokenPair(user) {\n    const accessToken = generateAccessToken(user);\n    const refreshToken = generateRefreshToken(user.id);\n    const expiresIn = getTokenRemainingTime(accessToken);\n    return {\n        accessToken,\n        refreshToken,\n        expiresIn\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/jwt.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security/audit-middleware.ts":
/*!**********************************************!*\
  !*** ./src/lib/security/audit-middleware.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSecurityMiddleware: () => (/* binding */ createSecurityMiddleware),\n/* harmony export */   recordAuditLog: () => (/* binding */ recordAuditLog),\n/* harmony export */   recordSecurityEvent: () => (/* binding */ recordSecurityEvent),\n/* harmony export */   withAuditLog: () => (/* binding */ withAuditLog)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/jwt */ \"(rsc)/./src/lib/jwt.ts\");\n/**\n * 审计日志中间件\n * 实现P0-009安全机制强化目标：\n * - 记录所有用户操作的审计日志\n * - 实现完整的操作追踪\n * - 支持安全事件监控\n */ \n\n/**\n * 审计日志装饰器\n */ function withAuditLog(operationType, operationModule, getDescription) {\n    return function(target, propertyKey, descriptor) {\n        const originalMethod = descriptor.value;\n        descriptor.value = async function(request, ...args) {\n            const startTime = Date.now();\n            let auditEntry = {\n                action: operationType,\n                resource: operationModule,\n                timestamp: new Date(),\n                ipAddress: getClientIP(request),\n                userAgent: request.headers.get('user-agent') || 'Unknown'\n            };\n            try {\n                // 验证用户身份\n                const authHeader = request.headers.get('authorization');\n                if (authHeader && authHeader.startsWith('Bearer ')) {\n                    const token = authHeader.substring(7);\n                    const payload = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.verifyAccessToken)(token);\n                    if (payload) {\n                        auditEntry.userId = payload.userId;\n                        auditEntry.userName = payload.username;\n                    }\n                }\n                // 记录请求数据（敏感数据脱敏）\n                if (request.method !== 'GET') {\n                    try {\n                        const requestBody = await request.clone().json();\n                        auditEntry.requestData = maskSensitiveData(requestBody);\n                    } catch  {\n                    // 忽略非JSON请求\n                    }\n                }\n                // 执行原始方法\n                const result = await originalMethod.call(this, request, ...args);\n                // 记录执行结果\n                auditEntry.operationResult = 'SUCCESS';\n                auditEntry.executionTime = Date.now() - startTime;\n                if (getDescription) {\n                    auditEntry.operationDescription = getDescription(request, result);\n                } else {\n                    auditEntry.operationDescription = `${operationType} ${operationModule}`;\n                }\n                // 记录响应数据（敏感数据脱敏）\n                if (result instanceof next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse) {\n                    try {\n                        const responseData = await result.clone().json();\n                        auditEntry.responseData = maskSensitiveData(responseData);\n                    } catch  {\n                    // 忽略非JSON响应\n                    }\n                }\n                // 记录审计日志\n                await recordAuditLog(auditEntry);\n                return result;\n            } catch (error) {\n                // 记录失败的操作\n                auditEntry.operationResult = 'FAILED';\n                auditEntry.executionTime = Date.now() - startTime;\n                auditEntry.errorMessage = error instanceof Error ? error.message : String(error);\n                auditEntry.operationDescription = `${operationType} ${operationModule} - FAILED`;\n                await recordAuditLog(auditEntry);\n                throw error;\n            }\n        };\n        return descriptor;\n    };\n}\n/**\n * 记录审计日志\n */ async function recordAuditLog(entry) {\n    try {\n        // 在API路由中使用时，动态导入数据库服务\n        if (true) {\n            const { logSystemOperation } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_system-log-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/system-log-service */ \"(rsc)/./src/lib/system-log-service.ts\"));\n            // 映射操作模块到数据库约束允许的值\n            const moduleMapping = {\n                'users': 'USER',\n                'roles': 'ROLE',\n                'medical-cases': 'MEDICAL_CASE',\n                'supervision-rules': 'SUPERVISION_RULE',\n                'knowledge-base': 'KNOWLEDGE',\n                'system': 'SYSTEM',\n                'auth': 'AUTH'\n            };\n            // 映射操作类型到数据库约束允许的值\n            const actionMapping = {\n                'GET': 'QUERY',\n                'POST': 'CREATE',\n                'PUT': 'UPDATE',\n                'DELETE': 'DELETE',\n                'PATCH': 'UPDATE',\n                'create': 'CREATE',\n                'update': 'UPDATE',\n                'delete': 'DELETE',\n                'query': 'QUERY',\n                'login': 'LOGIN',\n                'logout': 'LOGOUT',\n                'export': 'EXPORT',\n                'import': 'IMPORT',\n                'execute': 'EXECUTE'\n            };\n            const mappedModule = moduleMapping[entry.resource] || 'SYSTEM';\n            const mappedAction = actionMapping[entry.action] || 'QUERY';\n            await logSystemOperation(entry.userId || 0, mappedAction, mappedModule, entry.operationDescription, entry.operationResult, entry.ipAddress, entry.userAgent, entry.requestData ? JSON.stringify(entry.requestData) : undefined, entry.responseData ? JSON.stringify(entry.responseData) : undefined, entry.executionTime);\n        } else {}\n    } catch (error) {\n        console.error('Failed to record audit log:', error);\n    // 不抛出错误，避免影响主要业务流程\n    }\n}\n/**\n * 记录安全事件\n */ async function recordSecurityEvent(event) {\n    try {\n        // 在API路由中使用时，动态导入数据库服务\n        if (true) {\n            const { logSystemError } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_system-log-service_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/system-log-service */ \"(rsc)/./src/lib/system-log-service.ts\"));\n            await logSystemError(event.eventType, event.severity, event.description, JSON.stringify(event.metadata || {}), undefined, undefined, event.userId, event.ipAddress, event.userAgent);\n        } else {}\n        // 如果是高危事件，发送告警\n        if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {\n            await sendSecurityAlert(event);\n        }\n    } catch (error) {\n        console.error('Failed to record security event:', error);\n    }\n}\n/**\n * 发送安全告警\n */ async function sendSecurityAlert(event) {\n    // 这里可以集成邮件、短信、钉钉等告警渠道\n    console.warn('🚨 Security Alert:', {\n        type: event.eventType,\n        severity: event.severity,\n        description: event.description,\n        userId: event.userId,\n        ipAddress: event.ipAddress,\n        timestamp: event.timestamp\n    });\n}\n/**\n * 获取客户端IP地址\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.headers.get('remote-addr');\n    if (forwarded) {\n        return forwarded.split(',')[0]?.trim() || 'unknown';\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * 敏感数据脱敏\n */ function maskSensitiveData(data) {\n    if (!data || typeof data !== 'object') {\n        return data;\n    }\n    const sensitiveFields = [\n        'password',\n        'token',\n        'secret',\n        'key',\n        'idCard',\n        'phone',\n        'email',\n        'patientIdCard',\n        'patientPhone',\n        'doctorPhone',\n        'bankAccount'\n    ];\n    const masked = {\n        ...data\n    };\n    for (const field of sensitiveFields){\n        if (masked[field]) {\n            if (field.includes('phone') || field.includes('Phone')) {\n                masked[field] = maskPhone(masked[field]);\n            } else if (field.includes('idCard') || field.includes('IdCard')) {\n                masked[field] = maskIdCard(masked[field]);\n            } else if (field.includes('email') || field.includes('Email')) {\n                masked[field] = maskEmail(masked[field]);\n            } else {\n                masked[field] = '***';\n            }\n        }\n    }\n    // 递归处理嵌套对象\n    for(const key in masked){\n        if (typeof masked[key] === 'object' && masked[key] !== null) {\n            masked[key] = maskSensitiveData(masked[key]);\n        }\n    }\n    return masked;\n}\n/**\n * 手机号脱敏\n */ function maskPhone(phone) {\n    if (!phone || phone.length < 7) return phone;\n    return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n}\n/**\n * 身份证号脱敏\n */ function maskIdCard(idCard) {\n    if (!idCard || idCard.length < 10) return idCard;\n    return idCard.replace(/(\\d{6})\\d{8}(\\d{4})/, '$1********$2');\n}\n/**\n * 邮箱脱敏\n */ function maskEmail(email) {\n    if (!email || !email.includes('@')) return email;\n    const [username, domain] = email.split('@');\n    if (!username || username.length <= 2) return email;\n    return `${username.substring(0, 2)}***@${domain}`;\n}\n/**\n * API安全中间件\n */ function createSecurityMiddleware() {\n    return async (request)=>{\n        const startTime = Date.now();\n        try {\n            // 检查请求频率限制\n            await checkRateLimit(request);\n            // 检查IP白名单/黑名单\n            await checkIPRestrictions(request);\n            // 检查用户权限\n            await checkUserPermissions(request);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        } catch (error) {\n            // 记录安全事件\n            await recordSecurityEvent({\n                eventType: 'UNAUTHORIZED_ACCESS',\n                severity: 'HIGH',\n                ipAddress: getClientIP(request),\n                userAgent: request.headers.get('user-agent') || 'Unknown',\n                description: `Security check failed: ${error instanceof Error ? error.message : String(error)}`,\n                timestamp: new Date()\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Access denied'\n            }, {\n                status: 403\n            });\n        }\n    };\n}\n/**\n * 检查请求频率限制\n */ async function checkRateLimit(request) {\n    // 简单的内存限流实现，生产环境建议使用Redis\n    const ip = getClientIP(request);\n    const key = `rate_limit_${ip}`;\n// 这里可以实现更复杂的限流逻辑\n// 暂时跳过实现\n}\n/**\n * 检查IP限制\n */ async function checkIPRestrictions(request) {\n    const ip = getClientIP(request);\n    // 检查IP黑名单\n    const blacklistedIPs = [\n        '*************'\n    ] // 示例黑名单\n    ;\n    if (blacklistedIPs.includes(ip)) {\n        throw new Error(`IP ${ip} is blacklisted`);\n    }\n}\n/**\n * 检查用户权限\n */ async function checkUserPermissions(request) {\n    // 对于需要认证的API，检查token有效性\n    if (request.nextUrl.pathname.startsWith('/api/') && !request.nextUrl.pathname.startsWith('/api/auth/')) {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            throw new Error('Missing or invalid authorization header');\n        }\n        const token = authHeader.substring(7);\n        const payload = (0,_lib_jwt__WEBPACK_IMPORTED_MODULE_1__.verifyAccessToken)(token);\n        if (!payload) {\n            throw new Error('Invalid or expired token');\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security/audit-middleware.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "oracledb":
/*!***************************!*\
  !*** external "oracledb" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("oracledb");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fdashboard%2Froute&page=%2Fapi%2Fanalytics%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fdashboard%2Froute.ts&appDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&rootDir=%2FUsers%2Fwangfeng%2FDocuments%2Fmediinspect-v2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();