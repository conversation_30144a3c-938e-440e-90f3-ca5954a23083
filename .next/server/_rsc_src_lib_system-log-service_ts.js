"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_system-log-service_ts";
exports.ids = ["_rsc_src_lib_system-log-service_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildCountSQL: () => (/* binding */ buildCountSQL),\n/* harmony export */   buildPaginationSQL: () => (/* binding */ buildPaginationSQL),\n/* harmony export */   checkConnection: () => (/* binding */ checkConnection),\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   formatBinds: () => (/* binding */ formatBinds),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   getPoolStats: () => (/* binding */ getPoolStats),\n/* harmony export */   initializePool: () => (/* binding */ initializePool),\n/* harmony export */   monitorPoolHealth: () => (/* binding */ monitorPoolHealth)\n/* harmony export */ });\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! oracledb */ \"oracledb\");\n/* harmony import */ var oracledb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(oracledb__WEBPACK_IMPORTED_MODULE_0__);\n\n// Oracle数据库连接配置 - 性能优化版本\nconst dbConfig = {\n    user: process.env['DB_USERNAME'],\n    password: process.env['DB_PASSWORD'],\n    connectString: `${process.env['DB_HOST']}:${process.env['DB_PORT']}/${process.env['DB_SERVICE_NAME']}`,\n    poolMin: parseInt(process.env['DB_POOL_MIN'] || '5'),\n    poolMax: parseInt(process.env['DB_POOL_MAX'] || '20'),\n    poolIncrement: parseInt(process.env['DB_POOL_INCREMENT'] || '2'),\n    poolTimeout: parseInt(process.env['DB_POOL_TIMEOUT'] || '30'),\n    stmtCacheSize: parseInt(process.env['DB_STMT_CACHE_SIZE'] || '50'),\n    queueMax: parseInt(process.env['DB_QUEUE_MAX'] || '100'),\n    queueTimeout: parseInt(process.env['DB_QUEUE_TIMEOUT'] || '10000'),\n    enableStatistics: true\n};\n// 配置Oracle客户端\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().outFormat) = (oracledb__WEBPACK_IMPORTED_MODULE_0___default().OUT_FORMAT_OBJECT);\n(oracledb__WEBPACK_IMPORTED_MODULE_0___default().autoCommit) = true;\n// 连接池\nlet pool = null;\n/**\n * 初始化数据库连接池\n */ async function initializePool() {\n    try {\n        if (!pool) {\n            pool = await oracledb__WEBPACK_IMPORTED_MODULE_0___default().createPool(dbConfig);\n            console.log('✅ 数据库连接池初始化成功');\n            // 启动健康检查\n            startHealthCheck();\n        }\n    } catch (error) {\n        console.error('❌ 数据库连接池初始化失败:', error);\n        throw error;\n    }\n}\n/**\n * 获取数据库连接\n */ async function getConnection() {\n    try {\n        if (!pool) {\n            await initializePool();\n        }\n        return await pool.getConnection();\n    } catch (error) {\n        console.error('❌ 获取数据库连接失败:', error);\n        throw error;\n    }\n}\n// 清理Oracle查询结果，移除循环引用\nfunction cleanOracleResult(obj) {\n    if (obj === null || obj === undefined) {\n        return obj;\n    }\n    if (typeof obj !== 'object') {\n        return obj;\n    }\n    if (Array.isArray(obj)) {\n        return obj.map((item)=>cleanOracleResult(item));\n    }\n    if (obj instanceof Date) {\n        return obj;\n    }\n    if (Buffer.isBuffer(obj)) {\n        return obj;\n    }\n    // 创建新对象，只复制基本属性\n    const cleanObj = {};\n    for (const [key, value] of Object.entries(obj)){\n        // 跳过Oracle内部属性和可能的循环引用\n        if (typeof key === 'string' && (key.startsWith('_') || key.toLowerCase().includes('connection') || key.toLowerCase().includes('pool') || key.toLowerCase().includes('client') || key.toLowerCase().includes('socket') || key.toLowerCase().includes('stream') || key.toLowerCase().includes('cursor') || key.toLowerCase().includes('resultset') || key === 'domain' || key === 'constructor' || key === 'prototype')) {\n            continue;\n        }\n        try {\n            cleanObj[key] = cleanOracleResult(value);\n        } catch (error) {\n            continue;\n        }\n    }\n    return cleanObj;\n}\n/**\n * 执行SQL查询\n */ async function executeQuery(sql, binds = {}, options = {}) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        const result = await connection.execute(sql, binds, options);\n        // 使用经过验证的清理函数，移除循环引用\n        const cleanRows = result.rows ? cleanOracleResult(result.rows) : [];\n        return {\n            rows: cleanRows,\n            rowsAffected: result.rowsAffected,\n            metaData: result.metaData ? cleanOracleResult(result.metaData) : undefined\n        };\n    } catch (error) {\n        console.error('❌ SQL查询执行失败:', error);\n        console.error('SQL:', sql);\n        console.error('Binds:', binds);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 执行事务\n */ async function executeTransaction(operations) {\n    let connection = null;\n    try {\n        connection = await getConnection();\n        // 关闭自动提交\n        // connection.autoCommit = false // Oracle连接不支持直接设置autoCommit属性\n        // 执行操作\n        const result = await operations(connection);\n        // 提交事务\n        await connection.commit();\n        return result;\n    } catch (error) {\n        // 回滚事务\n        if (connection) {\n            try {\n                await connection.rollback();\n            } catch (rollbackError) {\n                console.error('⚠️ 事务回滚失败:', rollbackError);\n            }\n        }\n        console.error('❌ 事务执行失败:', error);\n        throw error;\n    } finally{\n        if (connection) {\n            try {\n                // 恢复自动提交\n                // connection.autoCommit = true // Oracle连接不支持直接设置autoCommit属性\n                await connection.close();\n            } catch (error) {\n                console.error('⚠️ 关闭数据库连接失败:', error);\n            }\n        }\n    }\n}\n/**\n * 关闭数据库连接池\n */ async function closePool() {\n    try {\n        if (pool) {\n            // 停止健康检查\n            stopHealthCheck();\n            await pool.close(10); // 等待10秒关闭\n            pool = null;\n            console.log('✅ 数据库连接池已关闭');\n        }\n    } catch (error) {\n        console.error('❌ 关闭数据库连接池失败:', error);\n        throw error;\n    }\n}\n/**\n * 检查数据库连接状态\n */ async function checkConnection() {\n    try {\n        const result = await executeQuery('SELECT SYSDATE FROM DUAL');\n        return !!(result.rows && result.rows.length > 0);\n    } catch (error) {\n        console.error('❌ 数据库连接检查失败:', error);\n        return false;\n    }\n}\n/**\n * 格式化SQL绑定参数\n */ function formatBinds(params) {\n    const formatted = {};\n    for (const [key, value] of Object.entries(params)){\n        if (value === null || value === undefined) {\n            formatted[key] = {\n                val: null,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'string') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().STRING)\n            };\n        } else if (typeof value === 'number') {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().NUMBER)\n            };\n        } else if (value instanceof Date) {\n            formatted[key] = {\n                val: value,\n                type: (oracledb__WEBPACK_IMPORTED_MODULE_0___default().DATE)\n            };\n        } else {\n            formatted[key] = value;\n        }\n    }\n    return formatted;\n}\n/**\n * 构建分页SQL\n */ function buildPaginationSQL(baseSql, page = 1, pageSize = 10, orderBy = 'ID') {\n    const offset = (page - 1) * pageSize;\n    // 检查baseSql是否已经包含ORDER BY子句\n    const hasOrderBy = /ORDER\\s+BY/i.test(baseSql);\n    return `\n    SELECT * FROM (\n      SELECT a.*, ROWNUM rnum FROM (\n        ${baseSql}\n        ${hasOrderBy ? '' : `ORDER BY ${orderBy}`}\n      ) a\n      WHERE ROWNUM <= ${offset + pageSize}\n    )\n    WHERE rnum > ${offset}\n  `;\n}\n/**\n * 构建计数SQL\n */ function buildCountSQL(baseSql) {\n    // 移除ORDER BY子句\n    const cleanSql = baseSql.replace(/ORDER\\s+BY\\s+[^)]*$/i, '');\n    return `SELECT COUNT(*) as TOTAL FROM (${cleanSql})`;\n}\n// 进程退出时关闭连接池\nprocess.on('SIGINT', async ()=>{\n    await closePool();\n    process.exit(0);\n});\nprocess.on('SIGTERM', async ()=>{\n    await closePool();\n    process.exit(0);\n});\n/**\n * 获取连接池统计信息\n */ function getPoolStats() {\n    if (!pool) {\n        return {\n            connectionsOpen: 0,\n            connectionsInUse: 0,\n            connectionsAvailable: 0,\n            queueLength: 0,\n            queueTimeout: 0,\n            poolMin: 0,\n            poolMax: 0,\n            poolIncrement: 0\n        };\n    }\n    return {\n        connectionsOpen: pool.connectionsOpen,\n        connectionsInUse: pool.connectionsInUse,\n        connectionsAvailable: pool.connectionsOpen - pool.connectionsInUse,\n        queueLength: pool.queueLength || 0,\n        queueTimeout: pool.queueTimeout || 0,\n        poolMin: pool.poolMin,\n        poolMax: pool.poolMax,\n        poolIncrement: pool.poolIncrement\n    };\n}\n/**\n * 监控连接池健康状态\n */ function monitorPoolHealth() {\n    const stats = getPoolStats();\n    if (!pool) {\n        return {\n            status: 'critical',\n            message: '连接池未初始化',\n            stats\n        };\n    }\n    const utilizationRate = stats.connectionsOpen > 0 ? stats.connectionsInUse / stats.connectionsOpen * 100 : 0;\n    const queueUtilization = stats.queueLength > 0 ? stats.queueLength / (dbConfig.queueMax || 100) * 100 : 0;\n    if (utilizationRate > 90 || queueUtilization > 80) {\n        return {\n            status: 'critical',\n            message: `连接池使用率过高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    if (utilizationRate > 70 || queueUtilization > 50) {\n        return {\n            status: 'warning',\n            message: `连接池使用率偏高: ${utilizationRate.toFixed(1)}%, 队列使用率: ${queueUtilization.toFixed(1)}%`,\n            stats\n        };\n    }\n    return {\n        status: 'healthy',\n        message: `连接池运行正常: ${utilizationRate.toFixed(1)}% 使用率`,\n        stats\n    };\n}\n/**\n * 启动连接池健康检查\n */ function startHealthCheck() {\n    // 如果已经有定时器在运行，先清除它\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n    }\n    global.__healthCheckInterval = setInterval(()=>{\n        const health = monitorPoolHealth();\n        if (health.status === 'critical') {\n            console.error('🚨 连接池健康检查:', health.message);\n        } else if (health.status === 'warning') {\n            console.warn('⚠️ 连接池健康检查:', health.message);\n        }\n    }, 30000); // 每30秒检查一次\n}\n/**\n * 停止连接池健康检查\n */ function stopHealthCheck() {\n    if (global.__healthCheckInterval) {\n        clearInterval(global.__healthCheckInterval);\n        global.__healthCheckInterval = undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/system-log-service.ts":
/*!***************************************!*\
  !*** ./src/lib/system-log-service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSystemErrorLogs: () => (/* binding */ getSystemErrorLogs),\n/* harmony export */   getSystemLoginLogs: () => (/* binding */ getSystemLoginLogs),\n/* harmony export */   getSystemOperationLogs: () => (/* binding */ getSystemOperationLogs),\n/* harmony export */   logSystemError: () => (/* binding */ logSystemError),\n/* harmony export */   logSystemLogin: () => (/* binding */ logSystemLogin),\n/* harmony export */   logSystemOperation: () => (/* binding */ logSystemOperation)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/**\n * 系统日志服务\n * 实现P0-008数据库设计与API实现一致性修复目标：\n * - 替换模拟数据，实现真实的数据库查询\n * - 确保SYSTEM_OPERATION_LOG等表与API接口完全匹配\n */ \n/**\n * 获取系统操作日志\n */ async function getSystemOperationLogs(params = {}) {\n    const { page = 1, pageSize = 20, startDate, endDate, userId, operationType, operationModule, search, sortBy = 'CREATED_AT', sortOrder = 'DESC' } = params;\n    const offset = (page - 1) * pageSize;\n    const conditions = [];\n    const queryParams = {\n        offset,\n        pageSize\n    };\n    // 构建查询条件\n    if (startDate) {\n        conditions.push('sol.CREATED_AT >= :startDate');\n        queryParams.startDate = startDate;\n    }\n    if (endDate) {\n        conditions.push('sol.CREATED_AT <= :endDate');\n        queryParams.endDate = endDate;\n    }\n    if (userId) {\n        conditions.push('sol.USER_ID = :userId');\n        queryParams.userId = userId;\n    }\n    if (operationType) {\n        conditions.push('sol.OPERATION_TYPE = :operationType');\n        queryParams.operationType = operationType;\n    }\n    if (operationModule) {\n        conditions.push('sol.OPERATION_MODULE = :operationModule');\n        queryParams.operationModule = operationModule;\n    }\n    if (search) {\n        conditions.push('(UPPER(sol.OPERATION_DESCRIPTION) LIKE UPPER(:search) OR UPPER(u.REAL_NAME) LIKE UPPER(:searchUser))');\n        queryParams.search = `%${search}%`;\n        queryParams.searchUser = `%${search}%`;\n    }\n    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';\n    // 查询总数\n    const countSql = `\n    SELECT COUNT(*) as total\n    FROM SYSTEM_OPERATION_LOG sol\n    LEFT JOIN USER_INFO u ON sol.USER_ID = u.ID\n    ${whereClause}\n  `;\n    const countResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSql, queryParams);\n    const total = countResult.rows[0]?.TOTAL || 0;\n    // 查询数据\n    const dataSql = `\n    SELECT \n      sol.ID, sol.USER_ID, sol.OPERATION_TYPE, sol.OPERATION_MODULE,\n      sol.OPERATION_DESCRIPTION, sol.OPERATION_RESULT, sol.IP_ADDRESS,\n      sol.USER_AGENT, sol.REQUEST_DATA, sol.RESPONSE_DATA,\n      sol.EXECUTION_TIME, sol.CREATED_AT,\n      u.REAL_NAME as USER_NAME\n    FROM SYSTEM_OPERATION_LOG sol\n    LEFT JOIN USER_INFO u ON sol.USER_ID = u.ID\n    ${whereClause}\n    ORDER BY sol.${sortBy} ${sortOrder}\n    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY\n  `;\n    const dataResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(dataSql, queryParams);\n    const items = dataResult.rows.map((row)=>({\n            id: row.ID,\n            userId: row.USER_ID,\n            userName: row.USER_NAME,\n            operationType: row.OPERATION_TYPE,\n            operationModule: row.OPERATION_MODULE,\n            operationDescription: row.OPERATION_DESCRIPTION,\n            operationResult: row.OPERATION_RESULT,\n            ipAddress: row.IP_ADDRESS,\n            userAgent: row.USER_AGENT,\n            requestData: row.REQUEST_DATA,\n            responseData: row.RESPONSE_DATA,\n            executionTime: row.EXECUTION_TIME || 0,\n            createdAt: row.CREATED_AT\n        }));\n    return {\n        items,\n        total,\n        page,\n        pageSize,\n        totalPages: Math.ceil(total / pageSize)\n    };\n}\n/**\n * 获取系统登录日志\n */ async function getSystemLoginLogs(params = {}) {\n    const { page = 1, pageSize = 20, startDate, endDate, userId, search, sortBy = 'LOGIN_TIME', sortOrder = 'DESC' } = params;\n    const offset = (page - 1) * pageSize;\n    const conditions = [];\n    const queryParams = {\n        offset,\n        pageSize\n    };\n    // 构建查询条件\n    if (startDate) {\n        conditions.push('sll.LOGIN_TIME >= :startDate');\n        queryParams.startDate = startDate;\n    }\n    if (endDate) {\n        conditions.push('sll.LOGIN_TIME <= :endDate');\n        queryParams.endDate = endDate;\n    }\n    if (userId) {\n        conditions.push('sll.USER_ID = :userId');\n        queryParams.userId = userId;\n    }\n    if (search) {\n        conditions.push('(UPPER(u.REAL_NAME) LIKE UPPER(:search) OR UPPER(sll.IP_ADDRESS) LIKE UPPER(:searchIP))');\n        queryParams.search = `%${search}%`;\n        queryParams.searchIP = `%${search}%`;\n    }\n    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';\n    // 查询总数\n    const countSql = `\n    SELECT COUNT(*) as total\n    FROM SYSTEM_LOGIN_LOG sll\n    LEFT JOIN USER_INFO u ON sll.USER_ID = u.ID\n    ${whereClause}\n  `;\n    const countResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSql, queryParams);\n    const total = countResult.rows[0]?.TOTAL || 0;\n    // 查询数据\n    const dataSql = `\n    SELECT \n      sll.ID, sll.USER_ID, sll.LOGIN_TYPE, sll.LOGIN_RESULT,\n      sll.IP_ADDRESS, sll.USER_AGENT, sll.LOGIN_TIME, sll.LOGOUT_TIME,\n      sll.SESSION_DURATION,\n      u.REAL_NAME as USER_NAME\n    FROM SYSTEM_LOGIN_LOG sll\n    LEFT JOIN USER_INFO u ON sll.USER_ID = u.ID\n    ${whereClause}\n    ORDER BY sll.${sortBy} ${sortOrder}\n    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY\n  `;\n    const dataResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(dataSql, queryParams);\n    const items = dataResult.rows.map((row)=>({\n            id: row.ID,\n            userId: row.USER_ID,\n            userName: row.USER_NAME,\n            loginType: row.LOGIN_TYPE,\n            loginResult: row.LOGIN_RESULT,\n            ipAddress: row.IP_ADDRESS,\n            userAgent: row.USER_AGENT,\n            loginTime: row.LOGIN_TIME,\n            logoutTime: row.LOGOUT_TIME,\n            sessionDuration: row.SESSION_DURATION\n        }));\n    return {\n        items,\n        total,\n        page,\n        pageSize,\n        totalPages: Math.ceil(total / pageSize)\n    };\n}\n/**\n * 获取系统错误日志\n */ async function getSystemErrorLogs(params = {}) {\n    const { page = 1, pageSize = 20, startDate, endDate, logLevel, search, sortBy = 'CREATED_AT', sortOrder = 'DESC' } = params;\n    const offset = (page - 1) * pageSize;\n    const conditions = [];\n    const queryParams = {\n        offset,\n        pageSize\n    };\n    // 构建查询条件\n    if (startDate) {\n        conditions.push('sel.CREATED_AT >= :startDate');\n        queryParams.startDate = startDate;\n    }\n    if (endDate) {\n        conditions.push('sel.CREATED_AT <= :endDate');\n        queryParams.endDate = endDate;\n    }\n    if (logLevel) {\n        conditions.push('sel.ERROR_LEVEL = :logLevel');\n        queryParams.logLevel = logLevel;\n    }\n    if (search) {\n        conditions.push('(UPPER(sel.ERROR_MESSAGE) LIKE UPPER(:search) OR UPPER(sel.REQUEST_URL) LIKE UPPER(:searchUrl))');\n        queryParams.search = `%${search}%`;\n        queryParams.searchUrl = `%${search}%`;\n    }\n    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';\n    // 查询总数\n    const countSql = `\n    SELECT COUNT(*) as total\n    FROM SYSTEM_ERROR_LOG sel\n    ${whereClause}\n  `;\n    const countResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(countSql, queryParams);\n    const total = countResult.rows[0]?.TOTAL || 0;\n    // 查询数据\n    const dataSql = `\n    SELECT \n      sel.ID, sel.ERROR_TYPE, sel.ERROR_LEVEL, sel.ERROR_MESSAGE,\n      sel.ERROR_STACK, sel.REQUEST_URL, sel.REQUEST_METHOD,\n      sel.USER_ID, sel.IP_ADDRESS, sel.USER_AGENT, sel.CREATED_AT,\n      u.REAL_NAME as USER_NAME\n    FROM SYSTEM_ERROR_LOG sel\n    LEFT JOIN USER_INFO u ON sel.USER_ID = u.ID\n    ${whereClause}\n    ORDER BY sel.${sortBy} ${sortOrder}\n    OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY\n  `;\n    const dataResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(dataSql, queryParams);\n    const items = dataResult.rows.map((row)=>({\n            id: row.ID,\n            errorType: row.ERROR_TYPE,\n            errorLevel: row.ERROR_LEVEL,\n            errorMessage: row.ERROR_MESSAGE,\n            errorStack: row.ERROR_STACK,\n            requestUrl: row.REQUEST_URL,\n            requestMethod: row.REQUEST_METHOD,\n            userId: row.USER_ID,\n            userName: row.USER_NAME,\n            ipAddress: row.IP_ADDRESS,\n            userAgent: row.USER_AGENT,\n            createdAt: row.CREATED_AT\n        }));\n    return {\n        items,\n        total,\n        page,\n        pageSize,\n        totalPages: Math.ceil(total / pageSize)\n    };\n}\n/**\n * 记录系统操作日志\n */ async function logSystemOperation(userId, operationType, operationModule, operationDescription, operationResult, ipAddress, userAgent, requestData, responseData, executionTime) {\n    const sql = `\n    INSERT INTO SYSTEM_OPERATION_LOG (\n      ID, USER_ID, OPERATION_TYPE, OPERATION_MODULE, OPERATION_DESC,\n      REQUEST_METHOD, REQUEST_URL, REQUEST_PARAMS, RESPONSE_STATUS, RESPONSE_DATA,\n      IP_ADDRESS, USER_AGENT, EXECUTION_TIME, IS_SUCCESS, CREATED_AT\n    ) VALUES (\n      SEQ_SYSTEM_OPERATION_LOG.NEXTVAL,\n      :userId, :operationType, :operationModule, :operationDescription,\n      :requestMethod, :requestUrl, :requestData, :responseStatus, :responseData,\n      :ipAddress, :userAgent, :executionTime, :isSuccess, CURRENT_TIMESTAMP\n    )\n  `;\n    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n        userId,\n        operationType,\n        operationModule,\n        operationDescription,\n        requestMethod: 'POST',\n        requestUrl: '',\n        requestData: requestData || '',\n        responseStatus: 200,\n        responseData: responseData || '',\n        ipAddress,\n        userAgent,\n        executionTime: executionTime || 0,\n        isSuccess: 1\n    });\n}\n/**\n * 记录系统登录日志\n */ async function logSystemLogin(userId, loginType, loginResult, ipAddress, userAgent) {\n    const sql = `\n    INSERT INTO SYSTEM_LOGIN_LOG (\n      ID, USER_ID, LOGIN_TYPE, LOGIN_RESULT, IP_ADDRESS,\n      USER_AGENT, LOGIN_TIME, CREATED_AT\n    ) VALUES (\n      SEQ_SYSTEM_LOGIN_LOG.NEXTVAL,\n      :userId, :loginType, :loginResult, :ipAddress,\n      :userAgent, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP\n    )\n  `;\n    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n        userId,\n        loginType,\n        loginResult,\n        ipAddress,\n        userAgent\n    });\n}\n/**\n * 记录系统错误日志\n */ async function logSystemError(errorType, errorLevel, errorMessage, errorStack, requestUrl, requestMethod, userId, ipAddress, userAgent) {\n    const sql = `\n    INSERT INTO SYSTEM_ERROR_LOG (\n      ID, ERROR_TYPE, ERROR_LEVEL, ERROR_MESSAGE, ERROR_STACK,\n      REQUEST_URL, REQUEST_METHOD, USER_ID, IP_ADDRESS,\n      USER_AGENT, CREATED_AT\n    ) VALUES (\n      SEQ_SYSTEM_ERROR_LOG.NEXTVAL,\n      :errorType, :errorLevel, :errorMessage, :errorStack,\n      :requestUrl, :requestMethod, :userId, :ipAddress,\n      :userAgent, CURRENT_TIMESTAMP\n    )\n  `;\n    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.executeQuery)(sql, {\n        errorType,\n        errorLevel,\n        errorMessage,\n        errorStack: errorStack || '',\n        requestUrl: requestUrl || '',\n        requestMethod: requestMethod || '',\n        userId: userId || null,\n        ipAddress: ipAddress || '',\n        userAgent: userAgent || ''\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N5c3RlbS1sb2ctc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFNEM7QUE0RDdDOztDQUVDLEdBQ00sZUFBZUMsdUJBQXVCQyxTQUF5QixDQUFDLENBQUM7SUFDdEUsTUFBTSxFQUNKQyxPQUFPLENBQUMsRUFDUkMsV0FBVyxFQUFFLEVBQ2JDLFNBQVMsRUFDVEMsT0FBTyxFQUNQQyxNQUFNLEVBQ05DLGFBQWEsRUFDYkMsZUFBZSxFQUNmQyxNQUFNLEVBQ05DLFNBQVMsWUFBWSxFQUNyQkMsWUFBWSxNQUFNLEVBQ25CLEdBQUdWO0lBRUosTUFBTVcsU0FBUyxDQUFDVixPQUFPLEtBQUtDO0lBQzVCLE1BQU1VLGFBQXVCLEVBQUU7SUFDL0IsTUFBTUMsY0FBbUI7UUFBRUY7UUFBUVQ7SUFBUztJQUU1QyxTQUFTO0lBQ1QsSUFBSUMsV0FBVztRQUNiUyxXQUFXRSxJQUFJLENBQUM7UUFDaEJELFlBQVlWLFNBQVMsR0FBR0E7SUFDMUI7SUFFQSxJQUFJQyxTQUFTO1FBQ1hRLFdBQVdFLElBQUksQ0FBQztRQUNoQkQsWUFBWVQsT0FBTyxHQUFHQTtJQUN4QjtJQUVBLElBQUlDLFFBQVE7UUFDVk8sV0FBV0UsSUFBSSxDQUFDO1FBQ2hCRCxZQUFZUixNQUFNLEdBQUdBO0lBQ3ZCO0lBRUEsSUFBSUMsZUFBZTtRQUNqQk0sV0FBV0UsSUFBSSxDQUFDO1FBQ2hCRCxZQUFZUCxhQUFhLEdBQUdBO0lBQzlCO0lBRUEsSUFBSUMsaUJBQWlCO1FBQ25CSyxXQUFXRSxJQUFJLENBQUM7UUFDaEJELFlBQVlOLGVBQWUsR0FBR0E7SUFDaEM7SUFFQSxJQUFJQyxRQUFRO1FBQ1ZJLFdBQVdFLElBQUksQ0FBQztRQUNoQkQsWUFBWUwsTUFBTSxHQUFHLENBQUMsQ0FBQyxFQUFFQSxPQUFPLENBQUMsQ0FBQztRQUNsQ0ssWUFBWUUsVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFUCxPQUFPLENBQUMsQ0FBQztJQUN4QztJQUVBLE1BQU1RLGNBQWNKLFdBQVdLLE1BQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxFQUFFTCxXQUFXTSxJQUFJLENBQUMsVUFBVSxHQUFHO0lBRWxGLE9BQU87SUFDUCxNQUFNQyxXQUFXLENBQUM7Ozs7SUFJaEIsRUFBRUgsWUFBWTtFQUNoQixDQUFDO0lBRUQsTUFBTUksY0FBYyxNQUFNdEIsMkRBQVlBLENBQUNxQixVQUFVTjtJQUNqRCxNQUFNUSxRQUFRRCxZQUFZRSxJQUFJLENBQUMsRUFBRSxFQUFFQyxTQUFTO0lBRTVDLE9BQU87SUFDUCxNQUFNQyxVQUFVLENBQUM7Ozs7Ozs7OztJQVNmLEVBQUVSLFlBQVk7aUJBQ0QsRUFBRVAsT0FBTyxDQUFDLEVBQUVDLFVBQVU7O0VBRXJDLENBQUM7SUFFRCxNQUFNZSxhQUFhLE1BQU0zQiwyREFBWUEsQ0FBQzBCLFNBQVNYO0lBRS9DLE1BQU1hLFFBQVFELFdBQVdILElBQUksQ0FBQ0ssR0FBRyxDQUFDQyxDQUFBQSxNQUFRO1lBQ3hDQyxJQUFJRCxJQUFJRSxFQUFFO1lBQ1Z6QixRQUFRdUIsSUFBSUcsT0FBTztZQUNuQkMsVUFBVUosSUFBSUssU0FBUztZQUN2QjNCLGVBQWVzQixJQUFJTSxjQUFjO1lBQ2pDM0IsaUJBQWlCcUIsSUFBSU8sZ0JBQWdCO1lBQ3JDQyxzQkFBc0JSLElBQUlTLHFCQUFxQjtZQUMvQ0MsaUJBQWlCVixJQUFJVyxnQkFBZ0I7WUFDckNDLFdBQVdaLElBQUlhLFVBQVU7WUFDekJDLFdBQVdkLElBQUllLFVBQVU7WUFDekJDLGFBQWFoQixJQUFJaUIsWUFBWTtZQUM3QkMsY0FBY2xCLElBQUltQixhQUFhO1lBQy9CQyxlQUFlcEIsSUFBSXFCLGNBQWMsSUFBSTtZQUNyQ0MsV0FBV3RCLElBQUl1QixVQUFVO1FBQzNCO0lBRUEsT0FBTztRQUNMekI7UUFDQUw7UUFDQXBCO1FBQ0FDO1FBQ0FrRCxZQUFZQyxLQUFLQyxJQUFJLENBQUNqQyxRQUFRbkI7SUFDaEM7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZXFELG1CQUFtQnZELFNBQXlCLENBQUMsQ0FBQztJQUNsRSxNQUFNLEVBQ0pDLE9BQU8sQ0FBQyxFQUNSQyxXQUFXLEVBQUUsRUFDYkMsU0FBUyxFQUNUQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkcsTUFBTSxFQUNOQyxTQUFTLFlBQVksRUFDckJDLFlBQVksTUFBTSxFQUNuQixHQUFHVjtJQUVKLE1BQU1XLFNBQVMsQ0FBQ1YsT0FBTyxLQUFLQztJQUM1QixNQUFNVSxhQUF1QixFQUFFO0lBQy9CLE1BQU1DLGNBQW1CO1FBQUVGO1FBQVFUO0lBQVM7SUFFNUMsU0FBUztJQUNULElBQUlDLFdBQVc7UUFDYlMsV0FBV0UsSUFBSSxDQUFDO1FBQ2hCRCxZQUFZVixTQUFTLEdBQUdBO0lBQzFCO0lBRUEsSUFBSUMsU0FBUztRQUNYUSxXQUFXRSxJQUFJLENBQUM7UUFDaEJELFlBQVlULE9BQU8sR0FBR0E7SUFDeEI7SUFFQSxJQUFJQyxRQUFRO1FBQ1ZPLFdBQVdFLElBQUksQ0FBQztRQUNoQkQsWUFBWVIsTUFBTSxHQUFHQTtJQUN2QjtJQUVBLElBQUlHLFFBQVE7UUFDVkksV0FBV0UsSUFBSSxDQUFDO1FBQ2hCRCxZQUFZTCxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUVBLE9BQU8sQ0FBQyxDQUFDO1FBQ2xDSyxZQUFZMkMsUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFaEQsT0FBTyxDQUFDLENBQUM7SUFDdEM7SUFFQSxNQUFNUSxjQUFjSixXQUFXSyxNQUFNLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRUwsV0FBV00sSUFBSSxDQUFDLFVBQVUsR0FBRztJQUVsRixPQUFPO0lBQ1AsTUFBTUMsV0FBVyxDQUFDOzs7O0lBSWhCLEVBQUVILFlBQVk7RUFDaEIsQ0FBQztJQUVELE1BQU1JLGNBQWMsTUFBTXRCLDJEQUFZQSxDQUFDcUIsVUFBVU47SUFDakQsTUFBTVEsUUFBUUQsWUFBWUUsSUFBSSxDQUFDLEVBQUUsRUFBRUMsU0FBUztJQUU1QyxPQUFPO0lBQ1AsTUFBTUMsVUFBVSxDQUFDOzs7Ozs7OztJQVFmLEVBQUVSLFlBQVk7aUJBQ0QsRUFBRVAsT0FBTyxDQUFDLEVBQUVDLFVBQVU7O0VBRXJDLENBQUM7SUFFRCxNQUFNZSxhQUFhLE1BQU0zQiwyREFBWUEsQ0FBQzBCLFNBQVNYO0lBRS9DLE1BQU1hLFFBQVFELFdBQVdILElBQUksQ0FBQ0ssR0FBRyxDQUFDQyxDQUFBQSxNQUFRO1lBQ3hDQyxJQUFJRCxJQUFJRSxFQUFFO1lBQ1Z6QixRQUFRdUIsSUFBSUcsT0FBTztZQUNuQkMsVUFBVUosSUFBSUssU0FBUztZQUN2QndCLFdBQVc3QixJQUFJOEIsVUFBVTtZQUN6QkMsYUFBYS9CLElBQUlnQyxZQUFZO1lBQzdCcEIsV0FBV1osSUFBSWEsVUFBVTtZQUN6QkMsV0FBV2QsSUFBSWUsVUFBVTtZQUN6QmtCLFdBQVdqQyxJQUFJa0MsVUFBVTtZQUN6QkMsWUFBWW5DLElBQUlvQyxXQUFXO1lBQzNCQyxpQkFBaUJyQyxJQUFJc0MsZ0JBQWdCO1FBQ3ZDO0lBRUEsT0FBTztRQUNMeEM7UUFDQUw7UUFDQXBCO1FBQ0FDO1FBQ0FrRCxZQUFZQyxLQUFLQyxJQUFJLENBQUNqQyxRQUFRbkI7SUFDaEM7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZWlFLG1CQUFtQm5FLFNBQXlCLENBQUMsQ0FBQztJQUNsRSxNQUFNLEVBQ0pDLE9BQU8sQ0FBQyxFQUNSQyxXQUFXLEVBQUUsRUFDYkMsU0FBUyxFQUNUQyxPQUFPLEVBQ1BnRSxRQUFRLEVBQ1I1RCxNQUFNLEVBQ05DLFNBQVMsWUFBWSxFQUNyQkMsWUFBWSxNQUFNLEVBQ25CLEdBQUdWO0lBRUosTUFBTVcsU0FBUyxDQUFDVixPQUFPLEtBQUtDO0lBQzVCLE1BQU1VLGFBQXVCLEVBQUU7SUFDL0IsTUFBTUMsY0FBbUI7UUFBRUY7UUFBUVQ7SUFBUztJQUU1QyxTQUFTO0lBQ1QsSUFBSUMsV0FBVztRQUNiUyxXQUFXRSxJQUFJLENBQUM7UUFDaEJELFlBQVlWLFNBQVMsR0FBR0E7SUFDMUI7SUFFQSxJQUFJQyxTQUFTO1FBQ1hRLFdBQVdFLElBQUksQ0FBQztRQUNoQkQsWUFBWVQsT0FBTyxHQUFHQTtJQUN4QjtJQUVBLElBQUlnRSxVQUFVO1FBQ1p4RCxXQUFXRSxJQUFJLENBQUM7UUFDaEJELFlBQVl1RCxRQUFRLEdBQUdBO0lBQ3pCO0lBRUEsSUFBSTVELFFBQVE7UUFDVkksV0FBV0UsSUFBSSxDQUFDO1FBQ2hCRCxZQUFZTCxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUVBLE9BQU8sQ0FBQyxDQUFDO1FBQ2xDSyxZQUFZd0QsU0FBUyxHQUFHLENBQUMsQ0FBQyxFQUFFN0QsT0FBTyxDQUFDLENBQUM7SUFDdkM7SUFFQSxNQUFNUSxjQUFjSixXQUFXSyxNQUFNLEdBQUcsSUFBSSxDQUFDLE1BQU0sRUFBRUwsV0FBV00sSUFBSSxDQUFDLFVBQVUsR0FBRztJQUVsRixPQUFPO0lBQ1AsTUFBTUMsV0FBVyxDQUFDOzs7SUFHaEIsRUFBRUgsWUFBWTtFQUNoQixDQUFDO0lBRUQsTUFBTUksY0FBYyxNQUFNdEIsMkRBQVlBLENBQUNxQixVQUFVTjtJQUNqRCxNQUFNUSxRQUFRRCxZQUFZRSxJQUFJLENBQUMsRUFBRSxFQUFFQyxTQUFTO0lBRTVDLE9BQU87SUFDUCxNQUFNQyxVQUFVLENBQUM7Ozs7Ozs7O0lBUWYsRUFBRVIsWUFBWTtpQkFDRCxFQUFFUCxPQUFPLENBQUMsRUFBRUMsVUFBVTs7RUFFckMsQ0FBQztJQUVELE1BQU1lLGFBQWEsTUFBTTNCLDJEQUFZQSxDQUFDMEIsU0FBU1g7SUFFL0MsTUFBTWEsUUFBUUQsV0FBV0gsSUFBSSxDQUFDSyxHQUFHLENBQUNDLENBQUFBLE1BQVE7WUFDeENDLElBQUlELElBQUlFLEVBQUU7WUFDVndDLFdBQVcxQyxJQUFJMkMsVUFBVTtZQUN6QkMsWUFBWTVDLElBQUk2QyxXQUFXO1lBQzNCQyxjQUFjOUMsSUFBSStDLGFBQWE7WUFDL0JDLFlBQVloRCxJQUFJaUQsV0FBVztZQUMzQkMsWUFBWWxELElBQUltRCxXQUFXO1lBQzNCQyxlQUFlcEQsSUFBSXFELGNBQWM7WUFDakM1RSxRQUFRdUIsSUFBSUcsT0FBTztZQUNuQkMsVUFBVUosSUFBSUssU0FBUztZQUN2Qk8sV0FBV1osSUFBSWEsVUFBVTtZQUN6QkMsV0FBV2QsSUFBSWUsVUFBVTtZQUN6Qk8sV0FBV3RCLElBQUl1QixVQUFVO1FBQzNCO0lBRUEsT0FBTztRQUNMekI7UUFDQUw7UUFDQXBCO1FBQ0FDO1FBQ0FrRCxZQUFZQyxLQUFLQyxJQUFJLENBQUNqQyxRQUFRbkI7SUFDaEM7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZWdGLG1CQUNwQjdFLE1BQWMsRUFDZEMsYUFBcUIsRUFDckJDLGVBQXVCLEVBQ3ZCNkIsb0JBQTRCLEVBQzVCRSxlQUF1QixFQUN2QkUsU0FBaUIsRUFDakJFLFNBQWlCLEVBQ2pCRSxXQUFvQixFQUNwQkUsWUFBcUIsRUFDckJFLGFBQXNCO0lBRXRCLE1BQU1tQyxNQUFNLENBQUM7Ozs7Ozs7Ozs7O0VBV2IsQ0FBQztJQUVELE1BQU1yRiwyREFBWUEsQ0FBQ3FGLEtBQUs7UUFDdEI5RTtRQUNBQztRQUNBQztRQUNBNkI7UUFDQTRDLGVBQWU7UUFDZkYsWUFBWTtRQUNabEMsYUFBYUEsZUFBZTtRQUM1QndDLGdCQUFnQjtRQUNoQnRDLGNBQWNBLGdCQUFnQjtRQUM5Qk47UUFDQUU7UUFDQU0sZUFBZUEsaUJBQWlCO1FBQ2hDcUMsV0FBVztJQUNiO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVDLGVBQ3BCakYsTUFBYyxFQUNkb0QsU0FBaUIsRUFDakJFLFdBQW1CLEVBQ25CbkIsU0FBaUIsRUFDakJFLFNBQWlCO0lBRWpCLE1BQU15QyxNQUFNLENBQUM7Ozs7Ozs7OztFQVNiLENBQUM7SUFFRCxNQUFNckYsMkRBQVlBLENBQUNxRixLQUFLO1FBQ3RCOUU7UUFDQW9EO1FBQ0FFO1FBQ0FuQjtRQUNBRTtJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWU2QyxlQUNwQmpCLFNBQWlCLEVBQ2pCRSxVQUFrQixFQUNsQkUsWUFBb0IsRUFDcEJFLFVBQW1CLEVBQ25CRSxVQUFtQixFQUNuQkUsYUFBc0IsRUFDdEIzRSxNQUFlLEVBQ2ZtQyxTQUFrQixFQUNsQkUsU0FBa0I7SUFFbEIsTUFBTXlDLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7RUFXYixDQUFDO0lBRUQsTUFBTXJGLDJEQUFZQSxDQUFDcUYsS0FBSztRQUN0QmI7UUFDQUU7UUFDQUU7UUFDQUUsWUFBWUEsY0FBYztRQUMxQkUsWUFBWUEsY0FBYztRQUMxQkUsZUFBZUEsaUJBQWlCO1FBQ2hDM0UsUUFBUUEsVUFBVTtRQUNsQm1DLFdBQVdBLGFBQWE7UUFDeEJFLFdBQVdBLGFBQWE7SUFDMUI7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3dhbmdmZW5nL0RvY3VtZW50cy9tZWRpaW5zcGVjdC12Mi9zcmMvbGliL3N5c3RlbS1sb2ctc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIOezu+e7n+aXpeW/l+acjeWKoVxuICog5a6e546wUDAtMDA45pWw5o2u5bqT6K6+6K6h5LiOQVBJ5a6e546w5LiA6Ie05oCn5L+u5aSN55uu5qCH77yaXG4gKiAtIOabv+aNouaooeaLn+aVsOaNru+8jOWunueOsOecn+WunueahOaVsOaNruW6k+afpeivolxuICogLSDnoa7kv51TWVNURU1fT1BFUkFUSU9OX0xPR+etieihqOS4jkFQSeaOpeWPo+WujOWFqOWMuemFjVxuICovXG5cbmltcG9ydCB7IGV4ZWN1dGVRdWVyeSB9IGZyb20gJ0AvbGliL2RhdGFiYXNlJ1xuXG5leHBvcnQgaW50ZXJmYWNlIFN5c3RlbU9wZXJhdGlvbkxvZyB7XG4gIGlkOiBudW1iZXJcbiAgdXNlcklkOiBudW1iZXJcbiAgdXNlck5hbWU/OiBzdHJpbmdcbiAgb3BlcmF0aW9uVHlwZTogc3RyaW5nXG4gIG9wZXJhdGlvbk1vZHVsZTogc3RyaW5nXG4gIG9wZXJhdGlvbkRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgb3BlcmF0aW9uUmVzdWx0OiBzdHJpbmdcbiAgaXBBZGRyZXNzOiBzdHJpbmdcbiAgdXNlckFnZW50OiBzdHJpbmdcbiAgcmVxdWVzdERhdGE/OiBzdHJpbmdcbiAgcmVzcG9uc2VEYXRhPzogc3RyaW5nXG4gIGV4ZWN1dGlvblRpbWU6IG51bWJlclxuICBjcmVhdGVkQXQ6IERhdGVcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTeXN0ZW1Mb2dpbkxvZyB7XG4gIGlkOiBudW1iZXJcbiAgdXNlcklkOiBudW1iZXJcbiAgdXNlck5hbWU/OiBzdHJpbmdcbiAgbG9naW5UeXBlOiBzdHJpbmdcbiAgbG9naW5SZXN1bHQ6IHN0cmluZ1xuICBpcEFkZHJlc3M6IHN0cmluZ1xuICB1c2VyQWdlbnQ6IHN0cmluZ1xuICBsb2dpblRpbWU6IERhdGVcbiAgbG9nb3V0VGltZT86IERhdGVcbiAgc2Vzc2lvbkR1cmF0aW9uPzogbnVtYmVyXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3lzdGVtRXJyb3JMb2cge1xuICBpZDogbnVtYmVyXG4gIGVycm9yVHlwZTogc3RyaW5nXG4gIGVycm9yTGV2ZWw6IHN0cmluZ1xuICBlcnJvck1lc3NhZ2U6IHN0cmluZ1xuICBlcnJvclN0YWNrPzogc3RyaW5nXG4gIHJlcXVlc3RVcmw/OiBzdHJpbmdcbiAgcmVxdWVzdE1ldGhvZD86IHN0cmluZ1xuICB1c2VySWQ/OiBudW1iZXJcbiAgdXNlck5hbWU/OiBzdHJpbmdcbiAgaXBBZGRyZXNzPzogc3RyaW5nXG4gIHVzZXJBZ2VudD86IHN0cmluZ1xuICBjcmVhdGVkQXQ6IERhdGVcbn1cblxuZXhwb3J0IGludGVyZmFjZSBMb2dRdWVyeVBhcmFtcyB7XG4gIHBhZ2U/OiBudW1iZXJcbiAgcGFnZVNpemU/OiBudW1iZXJcbiAgc3RhcnREYXRlPzogRGF0ZVxuICBlbmREYXRlPzogRGF0ZVxuICB1c2VySWQ/OiBudW1iZXJcbiAgb3BlcmF0aW9uVHlwZT86IHN0cmluZ1xuICBvcGVyYXRpb25Nb2R1bGU/OiBzdHJpbmdcbiAgbG9nTGV2ZWw/OiBzdHJpbmdcbiAgc2VhcmNoPzogc3RyaW5nXG4gIHNvcnRCeT86IHN0cmluZ1xuICBzb3J0T3JkZXI/OiAnQVNDJyB8ICdERVNDJ1xufVxuXG4vKipcbiAqIOiOt+WPluezu+e7n+aTjeS9nOaXpeW/l1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0U3lzdGVtT3BlcmF0aW9uTG9ncyhwYXJhbXM6IExvZ1F1ZXJ5UGFyYW1zID0ge30pIHtcbiAgY29uc3Qge1xuICAgIHBhZ2UgPSAxLFxuICAgIHBhZ2VTaXplID0gMjAsXG4gICAgc3RhcnREYXRlLFxuICAgIGVuZERhdGUsXG4gICAgdXNlcklkLFxuICAgIG9wZXJhdGlvblR5cGUsXG4gICAgb3BlcmF0aW9uTW9kdWxlLFxuICAgIHNlYXJjaCxcbiAgICBzb3J0QnkgPSAnQ1JFQVRFRF9BVCcsXG4gICAgc29ydE9yZGVyID0gJ0RFU0MnXG4gIH0gPSBwYXJhbXNcblxuICBjb25zdCBvZmZzZXQgPSAocGFnZSAtIDEpICogcGFnZVNpemVcbiAgY29uc3QgY29uZGl0aW9uczogc3RyaW5nW10gPSBbXVxuICBjb25zdCBxdWVyeVBhcmFtczogYW55ID0geyBvZmZzZXQsIHBhZ2VTaXplIH1cblxuICAvLyDmnoTlu7rmn6Xor6LmnaHku7ZcbiAgaWYgKHN0YXJ0RGF0ZSkge1xuICAgIGNvbmRpdGlvbnMucHVzaCgnc29sLkNSRUFURURfQVQgPj0gOnN0YXJ0RGF0ZScpXG4gICAgcXVlcnlQYXJhbXMuc3RhcnREYXRlID0gc3RhcnREYXRlXG4gIH1cblxuICBpZiAoZW5kRGF0ZSkge1xuICAgIGNvbmRpdGlvbnMucHVzaCgnc29sLkNSRUFURURfQVQgPD0gOmVuZERhdGUnKVxuICAgIHF1ZXJ5UGFyYW1zLmVuZERhdGUgPSBlbmREYXRlXG4gIH1cblxuICBpZiAodXNlcklkKSB7XG4gICAgY29uZGl0aW9ucy5wdXNoKCdzb2wuVVNFUl9JRCA9IDp1c2VySWQnKVxuICAgIHF1ZXJ5UGFyYW1zLnVzZXJJZCA9IHVzZXJJZFxuICB9XG5cbiAgaWYgKG9wZXJhdGlvblR5cGUpIHtcbiAgICBjb25kaXRpb25zLnB1c2goJ3NvbC5PUEVSQVRJT05fVFlQRSA9IDpvcGVyYXRpb25UeXBlJylcbiAgICBxdWVyeVBhcmFtcy5vcGVyYXRpb25UeXBlID0gb3BlcmF0aW9uVHlwZVxuICB9XG5cbiAgaWYgKG9wZXJhdGlvbk1vZHVsZSkge1xuICAgIGNvbmRpdGlvbnMucHVzaCgnc29sLk9QRVJBVElPTl9NT0RVTEUgPSA6b3BlcmF0aW9uTW9kdWxlJylcbiAgICBxdWVyeVBhcmFtcy5vcGVyYXRpb25Nb2R1bGUgPSBvcGVyYXRpb25Nb2R1bGVcbiAgfVxuXG4gIGlmIChzZWFyY2gpIHtcbiAgICBjb25kaXRpb25zLnB1c2goJyhVUFBFUihzb2wuT1BFUkFUSU9OX0RFU0NSSVBUSU9OKSBMSUtFIFVQUEVSKDpzZWFyY2gpIE9SIFVQUEVSKHUuUkVBTF9OQU1FKSBMSUtFIFVQUEVSKDpzZWFyY2hVc2VyKSknKVxuICAgIHF1ZXJ5UGFyYW1zLnNlYXJjaCA9IGAlJHtzZWFyY2h9JWBcbiAgICBxdWVyeVBhcmFtcy5zZWFyY2hVc2VyID0gYCUke3NlYXJjaH0lYFxuICB9XG5cbiAgY29uc3Qgd2hlcmVDbGF1c2UgPSBjb25kaXRpb25zLmxlbmd0aCA+IDAgPyBgV0hFUkUgJHtjb25kaXRpb25zLmpvaW4oJyBBTkQgJyl9YCA6ICcnXG5cbiAgLy8g5p+l6K+i5oC75pWwXG4gIGNvbnN0IGNvdW50U3FsID0gYFxuICAgIFNFTEVDVCBDT1VOVCgqKSBhcyB0b3RhbFxuICAgIEZST00gU1lTVEVNX09QRVJBVElPTl9MT0cgc29sXG4gICAgTEVGVCBKT0lOIFVTRVJfSU5GTyB1IE9OIHNvbC5VU0VSX0lEID0gdS5JRFxuICAgICR7d2hlcmVDbGF1c2V9XG4gIGBcblxuICBjb25zdCBjb3VudFJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeShjb3VudFNxbCwgcXVlcnlQYXJhbXMpXG4gIGNvbnN0IHRvdGFsID0gY291bnRSZXN1bHQucm93c1swXT8uVE9UQUwgfHwgMFxuXG4gIC8vIOafpeivouaVsOaNrlxuICBjb25zdCBkYXRhU3FsID0gYFxuICAgIFNFTEVDVCBcbiAgICAgIHNvbC5JRCwgc29sLlVTRVJfSUQsIHNvbC5PUEVSQVRJT05fVFlQRSwgc29sLk9QRVJBVElPTl9NT0RVTEUsXG4gICAgICBzb2wuT1BFUkFUSU9OX0RFU0NSSVBUSU9OLCBzb2wuT1BFUkFUSU9OX1JFU1VMVCwgc29sLklQX0FERFJFU1MsXG4gICAgICBzb2wuVVNFUl9BR0VOVCwgc29sLlJFUVVFU1RfREFUQSwgc29sLlJFU1BPTlNFX0RBVEEsXG4gICAgICBzb2wuRVhFQ1VUSU9OX1RJTUUsIHNvbC5DUkVBVEVEX0FULFxuICAgICAgdS5SRUFMX05BTUUgYXMgVVNFUl9OQU1FXG4gICAgRlJPTSBTWVNURU1fT1BFUkFUSU9OX0xPRyBzb2xcbiAgICBMRUZUIEpPSU4gVVNFUl9JTkZPIHUgT04gc29sLlVTRVJfSUQgPSB1LklEXG4gICAgJHt3aGVyZUNsYXVzZX1cbiAgICBPUkRFUiBCWSBzb2wuJHtzb3J0Qnl9ICR7c29ydE9yZGVyfVxuICAgIE9GRlNFVCA6b2Zmc2V0IFJPV1MgRkVUQ0ggTkVYVCA6cGFnZVNpemUgUk9XUyBPTkxZXG4gIGBcblxuICBjb25zdCBkYXRhUmVzdWx0ID0gYXdhaXQgZXhlY3V0ZVF1ZXJ5KGRhdGFTcWwsIHF1ZXJ5UGFyYW1zKVxuXG4gIGNvbnN0IGl0ZW1zID0gZGF0YVJlc3VsdC5yb3dzLm1hcChyb3cgPT4gKHtcbiAgICBpZDogcm93LklELFxuICAgIHVzZXJJZDogcm93LlVTRVJfSUQsXG4gICAgdXNlck5hbWU6IHJvdy5VU0VSX05BTUUsXG4gICAgb3BlcmF0aW9uVHlwZTogcm93Lk9QRVJBVElPTl9UWVBFLFxuICAgIG9wZXJhdGlvbk1vZHVsZTogcm93Lk9QRVJBVElPTl9NT0RVTEUsXG4gICAgb3BlcmF0aW9uRGVzY3JpcHRpb246IHJvdy5PUEVSQVRJT05fREVTQ1JJUFRJT04sXG4gICAgb3BlcmF0aW9uUmVzdWx0OiByb3cuT1BFUkFUSU9OX1JFU1VMVCxcbiAgICBpcEFkZHJlc3M6IHJvdy5JUF9BRERSRVNTLFxuICAgIHVzZXJBZ2VudDogcm93LlVTRVJfQUdFTlQsXG4gICAgcmVxdWVzdERhdGE6IHJvdy5SRVFVRVNUX0RBVEEsXG4gICAgcmVzcG9uc2VEYXRhOiByb3cuUkVTUE9OU0VfREFUQSxcbiAgICBleGVjdXRpb25UaW1lOiByb3cuRVhFQ1VUSU9OX1RJTUUgfHwgMCxcbiAgICBjcmVhdGVkQXQ6IHJvdy5DUkVBVEVEX0FUXG4gIH0pKVxuXG4gIHJldHVybiB7XG4gICAgaXRlbXMsXG4gICAgdG90YWwsXG4gICAgcGFnZSxcbiAgICBwYWdlU2l6ZSxcbiAgICB0b3RhbFBhZ2VzOiBNYXRoLmNlaWwodG90YWwgLyBwYWdlU2l6ZSlcbiAgfVxufVxuXG4vKipcbiAqIOiOt+WPluezu+e7n+eZu+W9leaXpeW/l1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0U3lzdGVtTG9naW5Mb2dzKHBhcmFtczogTG9nUXVlcnlQYXJhbXMgPSB7fSkge1xuICBjb25zdCB7XG4gICAgcGFnZSA9IDEsXG4gICAgcGFnZVNpemUgPSAyMCxcbiAgICBzdGFydERhdGUsXG4gICAgZW5kRGF0ZSxcbiAgICB1c2VySWQsXG4gICAgc2VhcmNoLFxuICAgIHNvcnRCeSA9ICdMT0dJTl9USU1FJyxcbiAgICBzb3J0T3JkZXIgPSAnREVTQydcbiAgfSA9IHBhcmFtc1xuXG4gIGNvbnN0IG9mZnNldCA9IChwYWdlIC0gMSkgKiBwYWdlU2l6ZVxuICBjb25zdCBjb25kaXRpb25zOiBzdHJpbmdbXSA9IFtdXG4gIGNvbnN0IHF1ZXJ5UGFyYW1zOiBhbnkgPSB7IG9mZnNldCwgcGFnZVNpemUgfVxuXG4gIC8vIOaehOW7uuafpeivouadoeS7tlxuICBpZiAoc3RhcnREYXRlKSB7XG4gICAgY29uZGl0aW9ucy5wdXNoKCdzbGwuTE9HSU5fVElNRSA+PSA6c3RhcnREYXRlJylcbiAgICBxdWVyeVBhcmFtcy5zdGFydERhdGUgPSBzdGFydERhdGVcbiAgfVxuXG4gIGlmIChlbmREYXRlKSB7XG4gICAgY29uZGl0aW9ucy5wdXNoKCdzbGwuTE9HSU5fVElNRSA8PSA6ZW5kRGF0ZScpXG4gICAgcXVlcnlQYXJhbXMuZW5kRGF0ZSA9IGVuZERhdGVcbiAgfVxuXG4gIGlmICh1c2VySWQpIHtcbiAgICBjb25kaXRpb25zLnB1c2goJ3NsbC5VU0VSX0lEID0gOnVzZXJJZCcpXG4gICAgcXVlcnlQYXJhbXMudXNlcklkID0gdXNlcklkXG4gIH1cblxuICBpZiAoc2VhcmNoKSB7XG4gICAgY29uZGl0aW9ucy5wdXNoKCcoVVBQRVIodS5SRUFMX05BTUUpIExJS0UgVVBQRVIoOnNlYXJjaCkgT1IgVVBQRVIoc2xsLklQX0FERFJFU1MpIExJS0UgVVBQRVIoOnNlYXJjaElQKSknKVxuICAgIHF1ZXJ5UGFyYW1zLnNlYXJjaCA9IGAlJHtzZWFyY2h9JWBcbiAgICBxdWVyeVBhcmFtcy5zZWFyY2hJUCA9IGAlJHtzZWFyY2h9JWBcbiAgfVxuXG4gIGNvbnN0IHdoZXJlQ2xhdXNlID0gY29uZGl0aW9ucy5sZW5ndGggPiAwID8gYFdIRVJFICR7Y29uZGl0aW9ucy5qb2luKCcgQU5EICcpfWAgOiAnJ1xuXG4gIC8vIOafpeivouaAu+aVsFxuICBjb25zdCBjb3VudFNxbCA9IGBcbiAgICBTRUxFQ1QgQ09VTlQoKikgYXMgdG90YWxcbiAgICBGUk9NIFNZU1RFTV9MT0dJTl9MT0cgc2xsXG4gICAgTEVGVCBKT0lOIFVTRVJfSU5GTyB1IE9OIHNsbC5VU0VSX0lEID0gdS5JRFxuICAgICR7d2hlcmVDbGF1c2V9XG4gIGBcblxuICBjb25zdCBjb3VudFJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeShjb3VudFNxbCwgcXVlcnlQYXJhbXMpXG4gIGNvbnN0IHRvdGFsID0gY291bnRSZXN1bHQucm93c1swXT8uVE9UQUwgfHwgMFxuXG4gIC8vIOafpeivouaVsOaNrlxuICBjb25zdCBkYXRhU3FsID0gYFxuICAgIFNFTEVDVCBcbiAgICAgIHNsbC5JRCwgc2xsLlVTRVJfSUQsIHNsbC5MT0dJTl9UWVBFLCBzbGwuTE9HSU5fUkVTVUxULFxuICAgICAgc2xsLklQX0FERFJFU1MsIHNsbC5VU0VSX0FHRU5ULCBzbGwuTE9HSU5fVElNRSwgc2xsLkxPR09VVF9USU1FLFxuICAgICAgc2xsLlNFU1NJT05fRFVSQVRJT04sXG4gICAgICB1LlJFQUxfTkFNRSBhcyBVU0VSX05BTUVcbiAgICBGUk9NIFNZU1RFTV9MT0dJTl9MT0cgc2xsXG4gICAgTEVGVCBKT0lOIFVTRVJfSU5GTyB1IE9OIHNsbC5VU0VSX0lEID0gdS5JRFxuICAgICR7d2hlcmVDbGF1c2V9XG4gICAgT1JERVIgQlkgc2xsLiR7c29ydEJ5fSAke3NvcnRPcmRlcn1cbiAgICBPRkZTRVQgOm9mZnNldCBST1dTIEZFVENIIE5FWFQgOnBhZ2VTaXplIFJPV1MgT05MWVxuICBgXG5cbiAgY29uc3QgZGF0YVJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeShkYXRhU3FsLCBxdWVyeVBhcmFtcylcblxuICBjb25zdCBpdGVtcyA9IGRhdGFSZXN1bHQucm93cy5tYXAocm93ID0+ICh7XG4gICAgaWQ6IHJvdy5JRCxcbiAgICB1c2VySWQ6IHJvdy5VU0VSX0lELFxuICAgIHVzZXJOYW1lOiByb3cuVVNFUl9OQU1FLFxuICAgIGxvZ2luVHlwZTogcm93LkxPR0lOX1RZUEUsXG4gICAgbG9naW5SZXN1bHQ6IHJvdy5MT0dJTl9SRVNVTFQsXG4gICAgaXBBZGRyZXNzOiByb3cuSVBfQUREUkVTUyxcbiAgICB1c2VyQWdlbnQ6IHJvdy5VU0VSX0FHRU5ULFxuICAgIGxvZ2luVGltZTogcm93LkxPR0lOX1RJTUUsXG4gICAgbG9nb3V0VGltZTogcm93LkxPR09VVF9USU1FLFxuICAgIHNlc3Npb25EdXJhdGlvbjogcm93LlNFU1NJT05fRFVSQVRJT05cbiAgfSkpXG5cbiAgcmV0dXJuIHtcbiAgICBpdGVtcyxcbiAgICB0b3RhbCxcbiAgICBwYWdlLFxuICAgIHBhZ2VTaXplLFxuICAgIHRvdGFsUGFnZXM6IE1hdGguY2VpbCh0b3RhbCAvIHBhZ2VTaXplKVxuICB9XG59XG5cbi8qKlxuICog6I635Y+W57O757uf6ZSZ6K+v5pel5b+XXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRTeXN0ZW1FcnJvckxvZ3MocGFyYW1zOiBMb2dRdWVyeVBhcmFtcyA9IHt9KSB7XG4gIGNvbnN0IHtcbiAgICBwYWdlID0gMSxcbiAgICBwYWdlU2l6ZSA9IDIwLFxuICAgIHN0YXJ0RGF0ZSxcbiAgICBlbmREYXRlLFxuICAgIGxvZ0xldmVsLFxuICAgIHNlYXJjaCxcbiAgICBzb3J0QnkgPSAnQ1JFQVRFRF9BVCcsXG4gICAgc29ydE9yZGVyID0gJ0RFU0MnXG4gIH0gPSBwYXJhbXNcblxuICBjb25zdCBvZmZzZXQgPSAocGFnZSAtIDEpICogcGFnZVNpemVcbiAgY29uc3QgY29uZGl0aW9uczogc3RyaW5nW10gPSBbXVxuICBjb25zdCBxdWVyeVBhcmFtczogYW55ID0geyBvZmZzZXQsIHBhZ2VTaXplIH1cblxuICAvLyDmnoTlu7rmn6Xor6LmnaHku7ZcbiAgaWYgKHN0YXJ0RGF0ZSkge1xuICAgIGNvbmRpdGlvbnMucHVzaCgnc2VsLkNSRUFURURfQVQgPj0gOnN0YXJ0RGF0ZScpXG4gICAgcXVlcnlQYXJhbXMuc3RhcnREYXRlID0gc3RhcnREYXRlXG4gIH1cblxuICBpZiAoZW5kRGF0ZSkge1xuICAgIGNvbmRpdGlvbnMucHVzaCgnc2VsLkNSRUFURURfQVQgPD0gOmVuZERhdGUnKVxuICAgIHF1ZXJ5UGFyYW1zLmVuZERhdGUgPSBlbmREYXRlXG4gIH1cblxuICBpZiAobG9nTGV2ZWwpIHtcbiAgICBjb25kaXRpb25zLnB1c2goJ3NlbC5FUlJPUl9MRVZFTCA9IDpsb2dMZXZlbCcpXG4gICAgcXVlcnlQYXJhbXMubG9nTGV2ZWwgPSBsb2dMZXZlbFxuICB9XG5cbiAgaWYgKHNlYXJjaCkge1xuICAgIGNvbmRpdGlvbnMucHVzaCgnKFVQUEVSKHNlbC5FUlJPUl9NRVNTQUdFKSBMSUtFIFVQUEVSKDpzZWFyY2gpIE9SIFVQUEVSKHNlbC5SRVFVRVNUX1VSTCkgTElLRSBVUFBFUig6c2VhcmNoVXJsKSknKVxuICAgIHF1ZXJ5UGFyYW1zLnNlYXJjaCA9IGAlJHtzZWFyY2h9JWBcbiAgICBxdWVyeVBhcmFtcy5zZWFyY2hVcmwgPSBgJSR7c2VhcmNofSVgXG4gIH1cblxuICBjb25zdCB3aGVyZUNsYXVzZSA9IGNvbmRpdGlvbnMubGVuZ3RoID4gMCA/IGBXSEVSRSAke2NvbmRpdGlvbnMuam9pbignIEFORCAnKX1gIDogJydcblxuICAvLyDmn6Xor6LmgLvmlbBcbiAgY29uc3QgY291bnRTcWwgPSBgXG4gICAgU0VMRUNUIENPVU5UKCopIGFzIHRvdGFsXG4gICAgRlJPTSBTWVNURU1fRVJST1JfTE9HIHNlbFxuICAgICR7d2hlcmVDbGF1c2V9XG4gIGBcblxuICBjb25zdCBjb3VudFJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeShjb3VudFNxbCwgcXVlcnlQYXJhbXMpXG4gIGNvbnN0IHRvdGFsID0gY291bnRSZXN1bHQucm93c1swXT8uVE9UQUwgfHwgMFxuXG4gIC8vIOafpeivouaVsOaNrlxuICBjb25zdCBkYXRhU3FsID0gYFxuICAgIFNFTEVDVCBcbiAgICAgIHNlbC5JRCwgc2VsLkVSUk9SX1RZUEUsIHNlbC5FUlJPUl9MRVZFTCwgc2VsLkVSUk9SX01FU1NBR0UsXG4gICAgICBzZWwuRVJST1JfU1RBQ0ssIHNlbC5SRVFVRVNUX1VSTCwgc2VsLlJFUVVFU1RfTUVUSE9ELFxuICAgICAgc2VsLlVTRVJfSUQsIHNlbC5JUF9BRERSRVNTLCBzZWwuVVNFUl9BR0VOVCwgc2VsLkNSRUFURURfQVQsXG4gICAgICB1LlJFQUxfTkFNRSBhcyBVU0VSX05BTUVcbiAgICBGUk9NIFNZU1RFTV9FUlJPUl9MT0cgc2VsXG4gICAgTEVGVCBKT0lOIFVTRVJfSU5GTyB1IE9OIHNlbC5VU0VSX0lEID0gdS5JRFxuICAgICR7d2hlcmVDbGF1c2V9XG4gICAgT1JERVIgQlkgc2VsLiR7c29ydEJ5fSAke3NvcnRPcmRlcn1cbiAgICBPRkZTRVQgOm9mZnNldCBST1dTIEZFVENIIE5FWFQgOnBhZ2VTaXplIFJPV1MgT05MWVxuICBgXG5cbiAgY29uc3QgZGF0YVJlc3VsdCA9IGF3YWl0IGV4ZWN1dGVRdWVyeShkYXRhU3FsLCBxdWVyeVBhcmFtcylcblxuICBjb25zdCBpdGVtcyA9IGRhdGFSZXN1bHQucm93cy5tYXAocm93ID0+ICh7XG4gICAgaWQ6IHJvdy5JRCxcbiAgICBlcnJvclR5cGU6IHJvdy5FUlJPUl9UWVBFLFxuICAgIGVycm9yTGV2ZWw6IHJvdy5FUlJPUl9MRVZFTCxcbiAgICBlcnJvck1lc3NhZ2U6IHJvdy5FUlJPUl9NRVNTQUdFLFxuICAgIGVycm9yU3RhY2s6IHJvdy5FUlJPUl9TVEFDSyxcbiAgICByZXF1ZXN0VXJsOiByb3cuUkVRVUVTVF9VUkwsXG4gICAgcmVxdWVzdE1ldGhvZDogcm93LlJFUVVFU1RfTUVUSE9ELFxuICAgIHVzZXJJZDogcm93LlVTRVJfSUQsXG4gICAgdXNlck5hbWU6IHJvdy5VU0VSX05BTUUsXG4gICAgaXBBZGRyZXNzOiByb3cuSVBfQUREUkVTUyxcbiAgICB1c2VyQWdlbnQ6IHJvdy5VU0VSX0FHRU5ULFxuICAgIGNyZWF0ZWRBdDogcm93LkNSRUFURURfQVRcbiAgfSkpXG5cbiAgcmV0dXJuIHtcbiAgICBpdGVtcyxcbiAgICB0b3RhbCxcbiAgICBwYWdlLFxuICAgIHBhZ2VTaXplLFxuICAgIHRvdGFsUGFnZXM6IE1hdGguY2VpbCh0b3RhbCAvIHBhZ2VTaXplKVxuICB9XG59XG5cbi8qKlxuICog6K6w5b2V57O757uf5pON5L2c5pel5b+XXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2dTeXN0ZW1PcGVyYXRpb24oXG4gIHVzZXJJZDogbnVtYmVyLFxuICBvcGVyYXRpb25UeXBlOiBzdHJpbmcsXG4gIG9wZXJhdGlvbk1vZHVsZTogc3RyaW5nLFxuICBvcGVyYXRpb25EZXNjcmlwdGlvbjogc3RyaW5nLFxuICBvcGVyYXRpb25SZXN1bHQ6IHN0cmluZyxcbiAgaXBBZGRyZXNzOiBzdHJpbmcsXG4gIHVzZXJBZ2VudDogc3RyaW5nLFxuICByZXF1ZXN0RGF0YT86IHN0cmluZyxcbiAgcmVzcG9uc2VEYXRhPzogc3RyaW5nLFxuICBleGVjdXRpb25UaW1lPzogbnVtYmVyXG4pOiBQcm9taXNlPHZvaWQ+IHtcbiAgY29uc3Qgc3FsID0gYFxuICAgIElOU0VSVCBJTlRPIFNZU1RFTV9PUEVSQVRJT05fTE9HIChcbiAgICAgIElELCBVU0VSX0lELCBPUEVSQVRJT05fVFlQRSwgT1BFUkFUSU9OX01PRFVMRSwgT1BFUkFUSU9OX0RFU0MsXG4gICAgICBSRVFVRVNUX01FVEhPRCwgUkVRVUVTVF9VUkwsIFJFUVVFU1RfUEFSQU1TLCBSRVNQT05TRV9TVEFUVVMsIFJFU1BPTlNFX0RBVEEsXG4gICAgICBJUF9BRERSRVNTLCBVU0VSX0FHRU5ULCBFWEVDVVRJT05fVElNRSwgSVNfU1VDQ0VTUywgQ1JFQVRFRF9BVFxuICAgICkgVkFMVUVTIChcbiAgICAgIFNFUV9TWVNURU1fT1BFUkFUSU9OX0xPRy5ORVhUVkFMLFxuICAgICAgOnVzZXJJZCwgOm9wZXJhdGlvblR5cGUsIDpvcGVyYXRpb25Nb2R1bGUsIDpvcGVyYXRpb25EZXNjcmlwdGlvbixcbiAgICAgIDpyZXF1ZXN0TWV0aG9kLCA6cmVxdWVzdFVybCwgOnJlcXVlc3REYXRhLCA6cmVzcG9uc2VTdGF0dXMsIDpyZXNwb25zZURhdGEsXG4gICAgICA6aXBBZGRyZXNzLCA6dXNlckFnZW50LCA6ZXhlY3V0aW9uVGltZSwgOmlzU3VjY2VzcywgQ1VSUkVOVF9USU1FU1RBTVBcbiAgICApXG4gIGBcblxuICBhd2FpdCBleGVjdXRlUXVlcnkoc3FsLCB7XG4gICAgdXNlcklkLFxuICAgIG9wZXJhdGlvblR5cGUsXG4gICAgb3BlcmF0aW9uTW9kdWxlLFxuICAgIG9wZXJhdGlvbkRlc2NyaXB0aW9uLFxuICAgIHJlcXVlc3RNZXRob2Q6ICdQT1NUJyxcbiAgICByZXF1ZXN0VXJsOiAnJyxcbiAgICByZXF1ZXN0RGF0YTogcmVxdWVzdERhdGEgfHwgJycsXG4gICAgcmVzcG9uc2VTdGF0dXM6IDIwMCxcbiAgICByZXNwb25zZURhdGE6IHJlc3BvbnNlRGF0YSB8fCAnJyxcbiAgICBpcEFkZHJlc3MsXG4gICAgdXNlckFnZW50LFxuICAgIGV4ZWN1dGlvblRpbWU6IGV4ZWN1dGlvblRpbWUgfHwgMCxcbiAgICBpc1N1Y2Nlc3M6IDFcbiAgfSlcbn1cblxuLyoqXG4gKiDorrDlvZXns7vnu5/nmbvlvZXml6Xlv5dcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvZ1N5c3RlbUxvZ2luKFxuICB1c2VySWQ6IG51bWJlcixcbiAgbG9naW5UeXBlOiBzdHJpbmcsXG4gIGxvZ2luUmVzdWx0OiBzdHJpbmcsXG4gIGlwQWRkcmVzczogc3RyaW5nLFxuICB1c2VyQWdlbnQ6IHN0cmluZ1xuKTogUHJvbWlzZTx2b2lkPiB7XG4gIGNvbnN0IHNxbCA9IGBcbiAgICBJTlNFUlQgSU5UTyBTWVNURU1fTE9HSU5fTE9HIChcbiAgICAgIElELCBVU0VSX0lELCBMT0dJTl9UWVBFLCBMT0dJTl9SRVNVTFQsIElQX0FERFJFU1MsXG4gICAgICBVU0VSX0FHRU5ULCBMT0dJTl9USU1FLCBDUkVBVEVEX0FUXG4gICAgKSBWQUxVRVMgKFxuICAgICAgU0VRX1NZU1RFTV9MT0dJTl9MT0cuTkVYVFZBTCxcbiAgICAgIDp1c2VySWQsIDpsb2dpblR5cGUsIDpsb2dpblJlc3VsdCwgOmlwQWRkcmVzcyxcbiAgICAgIDp1c2VyQWdlbnQsIENVUlJFTlRfVElNRVNUQU1QLCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgIClcbiAgYFxuXG4gIGF3YWl0IGV4ZWN1dGVRdWVyeShzcWwsIHtcbiAgICB1c2VySWQsXG4gICAgbG9naW5UeXBlLFxuICAgIGxvZ2luUmVzdWx0LFxuICAgIGlwQWRkcmVzcyxcbiAgICB1c2VyQWdlbnRcbiAgfSlcbn1cblxuLyoqXG4gKiDorrDlvZXns7vnu5/plJnor6/ml6Xlv5dcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvZ1N5c3RlbUVycm9yKFxuICBlcnJvclR5cGU6IHN0cmluZyxcbiAgZXJyb3JMZXZlbDogc3RyaW5nLFxuICBlcnJvck1lc3NhZ2U6IHN0cmluZyxcbiAgZXJyb3JTdGFjaz86IHN0cmluZyxcbiAgcmVxdWVzdFVybD86IHN0cmluZyxcbiAgcmVxdWVzdE1ldGhvZD86IHN0cmluZyxcbiAgdXNlcklkPzogbnVtYmVyLFxuICBpcEFkZHJlc3M/OiBzdHJpbmcsXG4gIHVzZXJBZ2VudD86IHN0cmluZ1xuKTogUHJvbWlzZTx2b2lkPiB7XG4gIGNvbnN0IHNxbCA9IGBcbiAgICBJTlNFUlQgSU5UTyBTWVNURU1fRVJST1JfTE9HIChcbiAgICAgIElELCBFUlJPUl9UWVBFLCBFUlJPUl9MRVZFTCwgRVJST1JfTUVTU0FHRSwgRVJST1JfU1RBQ0ssXG4gICAgICBSRVFVRVNUX1VSTCwgUkVRVUVTVF9NRVRIT0QsIFVTRVJfSUQsIElQX0FERFJFU1MsXG4gICAgICBVU0VSX0FHRU5ULCBDUkVBVEVEX0FUXG4gICAgKSBWQUxVRVMgKFxuICAgICAgU0VRX1NZU1RFTV9FUlJPUl9MT0cuTkVYVFZBTCxcbiAgICAgIDplcnJvclR5cGUsIDplcnJvckxldmVsLCA6ZXJyb3JNZXNzYWdlLCA6ZXJyb3JTdGFjayxcbiAgICAgIDpyZXF1ZXN0VXJsLCA6cmVxdWVzdE1ldGhvZCwgOnVzZXJJZCwgOmlwQWRkcmVzcyxcbiAgICAgIDp1c2VyQWdlbnQsIENVUlJFTlRfVElNRVNUQU1QXG4gICAgKVxuICBgXG5cbiAgYXdhaXQgZXhlY3V0ZVF1ZXJ5KHNxbCwge1xuICAgIGVycm9yVHlwZSxcbiAgICBlcnJvckxldmVsLFxuICAgIGVycm9yTWVzc2FnZSxcbiAgICBlcnJvclN0YWNrOiBlcnJvclN0YWNrIHx8ICcnLFxuICAgIHJlcXVlc3RVcmw6IHJlcXVlc3RVcmwgfHwgJycsXG4gICAgcmVxdWVzdE1ldGhvZDogcmVxdWVzdE1ldGhvZCB8fCAnJyxcbiAgICB1c2VySWQ6IHVzZXJJZCB8fCBudWxsLFxuICAgIGlwQWRkcmVzczogaXBBZGRyZXNzIHx8ICcnLFxuICAgIHVzZXJBZ2VudDogdXNlckFnZW50IHx8ICcnXG4gIH0pXG59XG4iXSwibmFtZXMiOlsiZXhlY3V0ZVF1ZXJ5IiwiZ2V0U3lzdGVtT3BlcmF0aW9uTG9ncyIsInBhcmFtcyIsInBhZ2UiLCJwYWdlU2l6ZSIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJ1c2VySWQiLCJvcGVyYXRpb25UeXBlIiwib3BlcmF0aW9uTW9kdWxlIiwic2VhcmNoIiwic29ydEJ5Iiwic29ydE9yZGVyIiwib2Zmc2V0IiwiY29uZGl0aW9ucyIsInF1ZXJ5UGFyYW1zIiwicHVzaCIsInNlYXJjaFVzZXIiLCJ3aGVyZUNsYXVzZSIsImxlbmd0aCIsImpvaW4iLCJjb3VudFNxbCIsImNvdW50UmVzdWx0IiwidG90YWwiLCJyb3dzIiwiVE9UQUwiLCJkYXRhU3FsIiwiZGF0YVJlc3VsdCIsIml0ZW1zIiwibWFwIiwicm93IiwiaWQiLCJJRCIsIlVTRVJfSUQiLCJ1c2VyTmFtZSIsIlVTRVJfTkFNRSIsIk9QRVJBVElPTl9UWVBFIiwiT1BFUkFUSU9OX01PRFVMRSIsIm9wZXJhdGlvbkRlc2NyaXB0aW9uIiwiT1BFUkFUSU9OX0RFU0NSSVBUSU9OIiwib3BlcmF0aW9uUmVzdWx0IiwiT1BFUkFUSU9OX1JFU1VMVCIsImlwQWRkcmVzcyIsIklQX0FERFJFU1MiLCJ1c2VyQWdlbnQiLCJVU0VSX0FHRU5UIiwicmVxdWVzdERhdGEiLCJSRVFVRVNUX0RBVEEiLCJyZXNwb25zZURhdGEiLCJSRVNQT05TRV9EQVRBIiwiZXhlY3V0aW9uVGltZSIsIkVYRUNVVElPTl9USU1FIiwiY3JlYXRlZEF0IiwiQ1JFQVRFRF9BVCIsInRvdGFsUGFnZXMiLCJNYXRoIiwiY2VpbCIsImdldFN5c3RlbUxvZ2luTG9ncyIsInNlYXJjaElQIiwibG9naW5UeXBlIiwiTE9HSU5fVFlQRSIsImxvZ2luUmVzdWx0IiwiTE9HSU5fUkVTVUxUIiwibG9naW5UaW1lIiwiTE9HSU5fVElNRSIsImxvZ291dFRpbWUiLCJMT0dPVVRfVElNRSIsInNlc3Npb25EdXJhdGlvbiIsIlNFU1NJT05fRFVSQVRJT04iLCJnZXRTeXN0ZW1FcnJvckxvZ3MiLCJsb2dMZXZlbCIsInNlYXJjaFVybCIsImVycm9yVHlwZSIsIkVSUk9SX1RZUEUiLCJlcnJvckxldmVsIiwiRVJST1JfTEVWRUwiLCJlcnJvck1lc3NhZ2UiLCJFUlJPUl9NRVNTQUdFIiwiZXJyb3JTdGFjayIsIkVSUk9SX1NUQUNLIiwicmVxdWVzdFVybCIsIlJFUVVFU1RfVVJMIiwicmVxdWVzdE1ldGhvZCIsIlJFUVVFU1RfTUVUSE9EIiwibG9nU3lzdGVtT3BlcmF0aW9uIiwic3FsIiwicmVzcG9uc2VTdGF0dXMiLCJpc1N1Y2Nlc3MiLCJsb2dTeXN0ZW1Mb2dpbiIsImxvZ1N5c3RlbUVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/system-log-service.ts\n");

/***/ })

};
;