const oracledb = require('oracledb');
require('dotenv').config({ path: '.env.local' });

// Oracle客户端配置
oracledb.outFormat = oracledb.OUT_FORMAT_OBJECT;
oracledb.autoCommit = true;

// 数据库连接配置
const dbConfig = {
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  connectString: `${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_SERVICE_NAME}`,
  poolMin: parseInt(process.env.DB_POOL_MIN) || 2,
  poolMax: parseInt(process.env.DB_POOL_MAX) || 10,
  poolIncrement: parseInt(process.env.DB_POOL_INCREMENT) || 1,
};

console.log('🏥 医保基金监管平台 - 数据分析测试数据初始化');
console.log('==================================================');

async function initAnalyticsData() {
  let connection;
  
  try {
    console.log('🔄 正在连接数据库...');
    connection = await oracledb.getConnection(dbConfig);
    console.log('✅ 数据库连接成功！');

    // 清理现有数据
    console.log('🧹 清理现有测试数据...');
    await connection.execute('DELETE FROM RULE_EXECUTION_RESULT WHERE 1=1');
    await connection.execute('DELETE FROM RULE_EXECUTION_LOG WHERE 1=1');
    await connection.execute('DELETE FROM MEDICAL_COST_DETAIL WHERE 1=1');
    await connection.execute('DELETE FROM MEDICAL_DIAGNOSIS WHERE 1=1');
    await connection.execute('DELETE FROM MEDICAL_SURGERY WHERE 1=1');
    await connection.execute('DELETE FROM MEDICAL_SETTLEMENT WHERE 1=1');
    await connection.execute('DELETE FROM MEDICAL_CASE WHERE 1=1');
    await connection.execute('DELETE FROM RULE_SUPERVISION WHERE 1=1');

    // 插入监管规则数据
    console.log('📋 插入监管规则数据...');
    const rules = [
      {
        id: 1,
        name: '费用异常检测规则',
        code: 'COST_ANOMALY_001',
        type: 'BASIC',
        category: 'COST_CONTROL',
        description: '检测异常高额医疗费用',
        content: '检测单次医疗费用超过平均值3倍的案例',
        priority: 1,
        severity: 'HIGH',
        version: '1.0',
        isActive: 1
      },
      {
        id: 2,
        name: '重复用药检测规则',
        code: 'DRUG_DUPLICATE_002',
        type: 'ADVANCED',
        category: 'FRAUD_DETECTION',
        description: '检测重复开药行为',
        content: '检测同一患者短期内重复开具相同药品',
        priority: 2,
        severity: 'MEDIUM',
        version: '1.0',
        isActive: 1
      },
      {
        id: 3,
        name: '诊疗项目合规检测',
        code: 'TREATMENT_COMPLIANCE_003',
        type: 'BASIC',
        category: 'COMPLIANCE_CHECK',
        description: '检测诊疗项目合规性',
        content: '检测诊疗项目是否符合医保规定',
        priority: 1,
        severity: 'HIGH',
        version: '1.0',
        isActive: 1
      },
      {
        id: 4,
        name: '住院天数异常检测',
        code: 'STAY_DURATION_004',
        type: 'ADVANCED',
        category: 'QUALITY_ASSURANCE',
        description: '检测异常住院天数',
        content: '检测住院天数超过同类疾病平均值的案例',
        priority: 2,
        severity: 'MEDIUM',
        version: '1.0',
        isActive: 1
      },
      {
        id: 5,
        name: '医保目录外用药检测',
        code: 'NON_CATALOG_DRUG_005',
        type: 'BASIC',
        category: 'COMPLIANCE_CHECK',
        description: '检测医保目录外用药',
        content: '检测使用医保目录外药品的案例',
        priority: 1,
        severity: 'HIGH',
        version: '1.0',
        isActive: 1
      }
    ];

    for (const rule of rules) {
      await connection.execute(`
        INSERT INTO RULE_SUPERVISION (
          ID, RULE_NAME, RULE_CODE, RULE_TYPE, RULE_CATEGORY, DESCRIPTION,
          RULE_CONTENT, PRIORITY_LEVEL, SEVERITY_LEVEL, IS_ACTIVE,
          EFFECTIVE_DATE, VERSION_NUMBER, IS_DELETED, CREATED_AT, UPDATED_AT
        ) VALUES (
          :id, :name, :code, :type, :category, :description,
          :content, :priority, :severity, :isActive,
          CURRENT_DATE, :version, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
      `, rule);
    }

    // 插入医疗案例数据
    console.log('🏥 插入医疗案例数据...');
    const hospitals = ['市人民医院', '中心医院', '第三人民医院', '中医院', '妇幼保健院'];
    const departments = ['内科', '外科', '儿科', '妇产科', '骨科', '心内科', '神经科', '眼科'];
    const caseTypes = ['INPATIENT', 'OUTPATIENT']; // 住院、门诊
    const genders = ['MALE', 'FEMALE', 'OTHER']; // 性别
    const medicalCategories = ['普通住院', '普通门诊', '门诊慢特病', '急诊住院', '异地住院', '急诊', '日间手术', '定点药店购药'];
    const doctorNames = [
      '张医生', '李医生', '王医生', '刘医生', '陈医生', '杨医生', '赵医生', '黄医生',
      '周医生', '吴医生', '徐医生', '孙医生', '胡医生', '朱医生', '高医生', '林医生',
      '何医生', '郭医生', '马医生', '罗医生', '梁医生', '宋医生', '郑医生', '谢医生'
    ];

    // 生成过去6个月的案例数据
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6);

    for (let i = 1; i <= 200; i++) {
      // 随机生成案例日期
      const caseDate = new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime()));
      
      const caseType = caseTypes[Math.floor(Math.random() * caseTypes.length)];
      const isInpatient = caseType === 'INPATIENT';

      // 生成入院和出院日期（仅住院案例）
      let admissionDate = null;
      let dischargeDate = null;
      if (isInpatient) {
        admissionDate = new Date(caseDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000); // 入院日期在案例日期前30天内
        dischargeDate = Math.random() > 0.3 ? new Date(admissionDate.getTime() + Math.random() * 15 * 24 * 60 * 60 * 1000) : null; // 70%概率已出院
      }

      const medicalCase = {
        id: i,
        caseNumber: `MC${String(i).padStart(6, '0')}`,
        patientName: `患者${i}`,
        patientIdCard: `${String(Math.floor(Math.random() * 900000000000000000) + 100000000000000000)}`, // 18位身份证
        patientAge: Math.floor(Math.random() * 80) + 18, // 18-98岁
        patientGender: genders[Math.floor(Math.random() * genders.length)],
        hospitalName: hospitals[Math.floor(Math.random() * hospitals.length)],
        hospitalCode: `H${String(Math.floor(Math.random() * hospitals.length) + 1).padStart(3, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        doctorName: doctorNames[Math.floor(Math.random() * doctorNames.length)],
        caseType: caseType,
        medicalCategory: medicalCategories[Math.floor(Math.random() * medicalCategories.length)],
        admissionDate: admissionDate ? admissionDate.toISOString().slice(0, 10) : null,
        dischargeDate: dischargeDate ? dischargeDate.toISOString().slice(0, 10) : null,
        totalCost: Math.floor(Math.random() * 50000) + 1000, // 1000-51000元
        createdAt: caseDate.toISOString().slice(0, 19).replace('T', ' ')
      };

      await connection.execute(`
        INSERT INTO MEDICAL_CASE (
          ID, CASE_NUMBER, PATIENT_NAME, PATIENT_ID_CARD, PATIENT_AGE, PATIENT_GENDER,
          HOSPITAL_NAME, HOSPITAL_CODE, DEPARTMENT, DOCTOR_NAME, CASE_TYPE, MEDICAL_CATEGORY,
          ADMISSION_DATE, DISCHARGE_DATE, TOTAL_COST, IS_DELETED, CREATED_AT, UPDATED_AT
        ) VALUES (
          :id, :caseNumber, :patientName, :patientIdCard, :patientAge, :patientGender,
          :hospitalName, :hospitalCode, :department, :doctorName, :caseType, :medicalCategory,
          CASE WHEN :admissionDate IS NOT NULL THEN TO_DATE(:admissionDate, 'YYYY-MM-DD') ELSE NULL END,
          CASE WHEN :dischargeDate IS NOT NULL THEN TO_DATE(:dischargeDate, 'YYYY-MM-DD') ELSE NULL END,
          :totalCost, 0, TO_TIMESTAMP(:createdAt, 'YYYY-MM-DD HH24:MI:SS'), CURRENT_TIMESTAMP
        )
      `, medicalCase);
    }

    // 插入规则执行日志
    console.log('⚙️ 插入规则执行日志...');
    for (let i = 1; i <= 1000; i++) {
      const execDate = new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime()));
      const ruleId = Math.floor(Math.random() * 5) + 1;
      const status = Math.random() > 0.1 ? 'SUCCESS' : 'FAILED'; // 90%成功率
      const duration = Math.floor(Math.random() * 1000) + 100; // 100-1100ms
      const endDate = new Date(execDate.getTime() + duration);

      await connection.execute(`
        INSERT INTO RULE_EXECUTION_LOG (
          ID, RULE_ID, EXECUTION_ID, EXECUTION_STATUS, STARTED_AT, ENDED_AT,
          EXECUTION_DURATION, PROCESSED_RECORD_COUNT, MATCHED_RECORD_COUNT, CREATED_AT
        ) VALUES (
          :id, :ruleId, :executionId, :status, TO_TIMESTAMP(:startedAt, 'YYYY-MM-DD HH24:MI:SS'),
          TO_TIMESTAMP(:endedAt, 'YYYY-MM-DD HH24:MI:SS'), :duration, :processedCount, :matchedCount,
          TO_TIMESTAMP(:createdAt, 'YYYY-MM-DD HH24:MI:SS')
        )
      `, {
        id: i,
        ruleId,
        executionId: `EXEC_${String(i).padStart(8, '0')}`,
        status,
        startedAt: execDate.toISOString().slice(0, 19).replace('T', ' '),
        endedAt: endDate.toISOString().slice(0, 19).replace('T', ' '),
        duration,
        processedCount: Math.floor(Math.random() * 100) + 10,
        matchedCount: Math.floor(Math.random() * 10),
        createdAt: execDate.toISOString().slice(0, 19).replace('T', ' ')
      });
    }

    // 插入规则执行结果（违规检测）
    console.log('🚨 插入违规检测结果...');
    for (let i = 1; i <= 50; i++) {
      const execDate = new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime()));
      const caseId = Math.floor(Math.random() * 200) + 1;
      const ruleId = Math.floor(Math.random() * 5) + 1;
      const executionLogId = Math.floor(Math.random() * 1000) + 1;

      await connection.execute(`
        INSERT INTO RULE_EXECUTION_RESULT (
          ID, EXECUTION_LOG_ID, RULE_ID, CASE_ID, RESULT_TYPE, RISK_LEVEL,
          VIOLATION_DESCRIPTION, VIOLATION_AMOUNT, CONFIDENCE_SCORE, RESULT_STATUS,
          IS_AUTO_PROCESSED, IS_FOLLOW_UP_REQUIRED, CREATED_AT, UPDATED_AT
        ) VALUES (
          :id, :executionLogId, :ruleId, :caseId, 'VIOLATION', :riskLevel,
          :description, :violationAmount, :confidence, 'PENDING',
          0, 1, TO_TIMESTAMP(:createdAt, 'YYYY-MM-DD HH24:MI:SS'), CURRENT_TIMESTAMP
        )
      `, {
        id: i,
        executionLogId,
        ruleId,
        caseId,
        riskLevel: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)],
        description: `规则${ruleId}检测到违规行为`,
        violationAmount: Math.floor(Math.random() * 10000) + 500, // 500-10500元
        confidence: Math.floor(Math.random() * 40) + 60, // 60-100%
        createdAt: execDate.toISOString().slice(0, 19).replace('T', ' ')
      });
    }

    console.log('✅ 数据分析测试数据初始化完成！');
    console.log('📊 数据统计:');
    console.log('   - 监管规则: 5条');
    console.log('   - 医疗案例: 200条');
    console.log('   - 规则执行日志: 1000条');
    console.log('   - 违规检测结果: 50条');

  } catch (error) {
    console.error('❌ 初始化数据失败:', error);
    throw error;
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('🔌 数据库连接已关闭');
      } catch (error) {
        console.error('❌ 关闭数据库连接失败:', error);
      }
    }
  }
}

// 运行初始化
initAnalyticsData()
  .then(() => {
    console.log('🎉 数据分析测试数据初始化成功！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 初始化失败:', error);
    process.exit(1);
  });
