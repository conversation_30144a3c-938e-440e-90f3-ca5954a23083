#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 按顺序执行所有SQL脚本来初始化数据库
 */

const fs = require('fs');
const path = require('path');
const oracledb = require('oracledb');
require('dotenv').config({ path: '.env.local' });

// Oracle客户端配置
oracledb.outFormat = oracledb.OUT_FORMAT_OBJECT;
oracledb.autoCommit = false; // 手动控制事务

// 数据库连接配置
const dbConfig = {
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  connectString: `${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_SERVICE_NAME}`,
};

// SQL脚本执行顺序
const SQL_SCRIPTS = [
  '01_user_management.sql',
  '02_medical_case.sql', 
  '03_supervision_rules.sql',
  '04_knowledge_base.sql',
  '05_system_logs.sql'
];

/**
 * 读取SQL文件内容
 */
function readSQLFile(filename) {
  const filePath = path.join(__dirname, '..', 'schema', filename);
  if (!fs.existsSync(filePath)) {
    throw new Error(`SQL文件不存在: ${filePath}`);
  }
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * 解析SQL文件，分割成单独的语句
 */
function parseSQLStatements(sqlContent) {
  // 移除注释和空行
  const lines = sqlContent.split('\n');
  const cleanLines = [];
  let inBlockComment = false;

  for (let line of lines) {
    line = line.trim();

    // 跳过空行
    if (!line) continue;

    // 处理块注释
    if (line.includes('/*')) {
      inBlockComment = true;
    }
    if (line.includes('*/')) {
      inBlockComment = false;
      continue;
    }
    if (inBlockComment) continue;

    // 跳过单行注释
    if (line.startsWith('--')) continue;

    cleanLines.push(line);
  }

  // 合并成完整SQL
  const fullSQL = cleanLines.join('\n');

  // 处理PL/SQL块（以/结尾）和普通SQL语句（以;结尾）
  const statements = [];
  let currentStatement = '';
  let inPlsqlBlock = false;

  const lines2 = fullSQL.split('\n');

  for (let line of lines2) {
    line = line.trim();
    if (!line) continue;

    // 检测PL/SQL块开始
    if (line.toUpperCase().includes('CREATE OR REPLACE TRIGGER') ||
        line.toUpperCase().includes('CREATE OR REPLACE PROCEDURE') ||
        line.toUpperCase().includes('CREATE OR REPLACE FUNCTION')) {
      inPlsqlBlock = true;
    }

    currentStatement += line + '\n';

    // PL/SQL块以/结尾
    if (inPlsqlBlock && line === '/') {
      statements.push(currentStatement.replace(/\/\s*$/, '').trim());
      currentStatement = '';
      inPlsqlBlock = false;
    }
    // 普通SQL语句以;结尾
    else if (!inPlsqlBlock && line.endsWith(';')) {
      statements.push(currentStatement.replace(/;\s*$/, '').trim());
      currentStatement = '';
    }
  }

  // 处理最后一个语句
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }

  return statements.filter(stmt => stmt.length > 0);
}

/**
 * 执行单个SQL语句
 */
async function executeSQLStatement(connection, statement, statementIndex) {
  try {
    console.log(`   📝 执行语句 ${statementIndex + 1}...`);
    
    // 特殊处理CREATE OR REPLACE语句
    if (statement.toUpperCase().includes('CREATE OR REPLACE')) {
      await connection.execute(statement);
      console.log(`   ✅ 语句 ${statementIndex + 1} 执行成功`);
      return;
    }
    
    // 执行语句
    const result = await connection.execute(statement);
    
    if (result.rowsAffected !== undefined) {
      console.log(`   ✅ 语句 ${statementIndex + 1} 执行成功 (影响 ${result.rowsAffected} 行)`);
    } else {
      console.log(`   ✅ 语句 ${statementIndex + 1} 执行成功`);
    }
    
  } catch (error) {
    // 忽略一些常见的非致命错误
    const ignorableErrors = [
      'ORA-00955', // 名称已被现有对象使用
      'ORA-01418', // 指定的索引不存在
      'ORA-02289', // 序列不存在
      'ORA-00942', // 表或视图不存在
    ];
    
    const isIgnorable = ignorableErrors.some(code => 
      error.message.includes(code)
    );
    
    if (isIgnorable) {
      console.log(`   ⚠️  语句 ${statementIndex + 1} 跳过 (${error.message.split('\n')[0]})`);
    } else {
      console.error(`   ❌ 语句 ${statementIndex + 1} 执行失败:`);
      console.error(`      ${error.message.split('\n')[0]}`);
      throw error;
    }
  }
}

/**
 * 执行SQL脚本文件
 */
async function executeSQLFile(connection, filename) {
  console.log(`\n📄 执行脚本: ${filename}`);
  console.log('-'.repeat(50));
  
  try {
    // 读取SQL文件
    const sqlContent = readSQLFile(filename);
    console.log(`   📖 文件大小: ${(sqlContent.length / 1024).toFixed(2)} KB`);
    
    // 解析SQL语句
    const statements = parseSQLStatements(sqlContent);
    console.log(`   📊 解析到 ${statements.length} 个SQL语句`);
    
    // 执行每个语句
    for (let i = 0; i < statements.length; i++) {
      await executeSQLStatement(connection, statements[i], i);
    }
    
    // 提交事务
    await connection.commit();
    console.log(`   💾 事务已提交`);
    console.log(`✅ 脚本 ${filename} 执行完成`);
    
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.log(`   🔄 事务已回滚`);
    console.error(`❌ 脚本 ${filename} 执行失败: ${error.message}`);
    throw error;
  }
}

/**
 * 检查数据库初始化状态
 */
async function checkInitializationStatus(connection) {
  console.log('\n🔍 检查数据库初始化状态...');
  
  try {
    // 检查关键表是否存在
    const keyTables = [
      'USER_ROLE_INFO',
      'USER_INFO', 
      'MEDICAL_CASE',
      'RULE_SUPERVISION',
      'KNOWLEDGE_CATEGORY',
      'SYSTEM_OPERATION_LOG'
    ];
    
    const existingTables = [];
    
    for (const tableName of keyTables) {
      try {
        const result = await connection.execute(`
          SELECT COUNT(*) as COUNT FROM USER_TABLES 
          WHERE TABLE_NAME = :tableName
        `, [tableName]);
        
        if (result.rows[0].COUNT > 0) {
          existingTables.push(tableName);
        }
      } catch (error) {
        // 忽略查询错误
      }
    }
    
    if (existingTables.length === 0) {
      console.log('📋 数据库为空，可以进行初始化');
      return 'empty';
    } else if (existingTables.length === keyTables.length) {
      console.log('✅ 数据库已完全初始化');
      console.log('📊 现有表:');
      existingTables.forEach(table => {
        console.log(`   - ${table}`);
      });
      return 'initialized';
    } else {
      console.log('⚠️  数据库部分初始化');
      console.log('📊 现有表:');
      existingTables.forEach(table => {
        console.log(`   - ${table}`);
      });
      return 'partial';
    }
    
  } catch (error) {
    console.error('❌ 检查初始化状态失败:', error.message);
    return 'unknown';
  }
}

/**
 * 主初始化函数
 */
async function initializeDatabase(force = false) {
  let connection;
  
  try {
    console.log('🏥 医保基金监管平台 - 数据库初始化');
    console.log('=' .repeat(60));
    
    // 建立数据库连接
    console.log('🔄 连接数据库...');
    connection = await oracledb.getConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查初始化状态
    const status = await checkInitializationStatus(connection);
    
    if (status === 'initialized' && !force) {
      console.log('\n⚠️  数据库已经初始化，如需重新初始化请使用 --force 参数');
      return;
    }
    
    if (status === 'partial' && !force) {
      console.log('\n⚠️  数据库部分初始化，如需重新初始化请使用 --force 参数');
      return;
    }
    
    // 执行SQL脚本
    console.log('\n🚀 开始执行SQL脚本...');
    
    for (const scriptFile of SQL_SCRIPTS) {
      await executeSQLFile(connection, scriptFile);
    }
    
    console.log('\n🎉 数据库初始化完成！');
    
    // 最终状态检查
    await checkInitializationStatus(connection);
    
  } catch (error) {
    console.error('\n💥 数据库初始化失败:');
    console.error(`   ${error.message}`);
    process.exit(1);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('\n🔌 数据库连接已关闭');
      } catch (error) {
        console.error('⚠️ 关闭连接时出错:', error.message);
      }
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force') || args.includes('-f');
  
  if (force) {
    console.log('⚠️  强制模式：将重新初始化数据库');
  }
  
  await initializeDatabase(force);
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { initializeDatabase };
