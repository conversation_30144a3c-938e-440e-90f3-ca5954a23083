#!/usr/bin/env node

/**
 * 执行规则类型迁移脚本
 * 将规则类型从 BASIC/ADVANCED/CUSTOM/TEMPLATE 更新为 SQL/DSL/JAVASCRIPT
 */

const fs = require('fs')
const path = require('path')
const oracledb = require('oracledb')
require('dotenv').config({ path: '.env.local' })

// 数据库连接配置
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
}

async function executeMigration() {
  let connection

  try {
    console.log('🔄 开始执行规则类型迁移...')
    
    // 连接数据库
    connection = await oracledb.getConnection(dbConfig)
    console.log('✅ 数据库连接成功')

    // 读取迁移脚本
    const migrationScript = fs.readFileSync(
      path.join(__dirname, '../schema/migrations/update_rule_types.sql'),
      'utf8'
    )

    // 分割SQL语句（简单的分割，基于分号和换行）
    const statements = migrationScript
      .split(/;\s*\n/)
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && !stmt.startsWith('PROMPT'))

    console.log(`📝 准备执行 ${statements.length} 个SQL语句`)

    // 执行每个SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement) {
        try {
          console.log(`⏳ 执行语句 ${i + 1}/${statements.length}`)
          await connection.execute(statement)
        } catch (error) {
          console.error(`❌ 执行语句失败: ${statement.substring(0, 100)}...`)
          console.error(error.message)
          throw error
        }
      }
    }

    // 提交事务
    await connection.commit()
    console.log('✅ 所有更改已提交')

    // 验证迁移结果
    console.log('\n📊 验证迁移结果:')
    const result = await connection.execute(`
      SELECT 
        RULE_TYPE,
        COUNT(*) as COUNT,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as PERCENTAGE
      FROM RULE_SUPERVISION 
      WHERE IS_DELETED = 0
      GROUP BY RULE_TYPE
      ORDER BY COUNT(*) DESC
    `)

    if (result.rows && result.rows.length > 0) {
      console.log('\n规则类型分布:')
      result.rows.forEach(row => {
        console.log(`  ${row[0]}: ${row[1]} 条 (${row[2]}%)`)
      })
    }

    console.log('\n🎉 规则类型迁移完成!')
    console.log('\n📋 后续步骤:')
    console.log('1. 重启应用程序以使用新的规则类型')
    console.log('2. 验证前端显示是否正确')
    console.log('3. 如需回滚，请执行: npm run rollback-rule-types')

  } catch (error) {
    console.error('❌ 迁移失败:', error.message)
    
    if (connection) {
      try {
        await connection.rollback()
        console.log('🔄 已回滚所有更改')
      } catch (rollbackError) {
        console.error('❌ 回滚失败:', rollbackError.message)
      }
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      try {
        await connection.close()
        console.log('🔌 数据库连接已关闭')
      } catch (error) {
        console.error('❌ 关闭连接失败:', error.message)
      }
    }
  }
}

// 执行迁移
if (require.main === module) {
  executeMigration()
}

module.exports = { executeMigration }
