#!/usr/bin/env node

/**
 * 回滚规则类型迁移脚本
 * 将规则类型从 SQL/DSL/JAVASCRIPT 恢复为 BASIC/ADVANCED/CUSTOM/TEMPLATE
 */

const fs = require('fs')
const path = require('path')
const oracledb = require('oracledb')
require('dotenv').config({ path: '.env.local' })

// 数据库连接配置
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
}

async function executeRollback() {
  let connection

  try {
    console.log('🔄 开始回滚规则类型迁移...')
    
    // 连接数据库
    connection = await oracledb.getConnection(dbConfig)
    console.log('✅ 数据库连接成功')

    // 检查备份表是否存在
    const backupCheck = await connection.execute(`
      SELECT COUNT(*) as COUNT FROM USER_TABLES WHERE TABLE_NAME = 'RULE_SUPERVISION_BACKUP'
    `)

    if (!backupCheck.rows || backupCheck.rows[0][0] === 0) {
      throw new Error('备份表 RULE_SUPERVISION_BACKUP 不存在，无法执行回滚')
    }

    console.log('✅ 找到备份表，准备回滚')

    // 读取回滚脚本
    const rollbackScript = fs.readFileSync(
      path.join(__dirname, '../schema/migrations/rollback_rule_types.sql'),
      'utf8'
    )

    // 分割SQL语句
    const statements = rollbackScript
      .split(/;\s*\n/)
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && !stmt.startsWith('PROMPT'))

    console.log(`📝 准备执行 ${statements.length} 个SQL语句`)

    // 执行每个SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement) {
        try {
          console.log(`⏳ 执行语句 ${i + 1}/${statements.length}`)
          await connection.execute(statement)
        } catch (error) {
          console.error(`❌ 执行语句失败: ${statement.substring(0, 100)}...`)
          console.error(error.message)
          throw error
        }
      }
    }

    // 提交事务
    await connection.commit()
    console.log('✅ 所有更改已提交')

    // 验证回滚结果
    console.log('\n📊 验证回滚结果:')
    const result = await connection.execute(`
      SELECT 
        RULE_TYPE,
        COUNT(*) as COUNT,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as PERCENTAGE
      FROM RULE_SUPERVISION 
      WHERE IS_DELETED = 0
      GROUP BY RULE_TYPE
      ORDER BY COUNT(*) DESC
    `)

    if (result.rows && result.rows.length > 0) {
      console.log('\n规则类型分布:')
      result.rows.forEach(row => {
        console.log(`  ${row[0]}: ${row[1]} 条 (${row[2]}%)`)
      })
    }

    console.log('\n🎉 规则类型回滚完成!')
    console.log('\n📋 后续步骤:')
    console.log('1. 重启应用程序以使用原始的规则类型')
    console.log('2. 验证前端显示是否正确')
    console.log('3. 备份表 RULE_SUPERVISION_BACKUP 仍然保留')

  } catch (error) {
    console.error('❌ 回滚失败:', error.message)
    
    if (connection) {
      try {
        await connection.rollback()
        console.log('🔄 已回滚所有更改')
      } catch (rollbackError) {
        console.error('❌ 回滚失败:', rollbackError.message)
      }
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      try {
        await connection.close()
        console.log('🔌 数据库连接已关闭')
      } catch (error) {
        console.error('❌ 关闭连接失败:', error.message)
      }
    }
  }
}

// 执行回滚
if (require.main === module) {
  executeRollback()
}

module.exports = { executeRollback }
