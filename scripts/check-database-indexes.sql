-- =====================================================
-- 数据库索引检查脚本
-- 验证P0-003性能优化所需的索引是否已创建
-- =====================================================

-- 1. 检查所有表的索引状态
SELECT 
    'INDEX_STATUS' as CHECK_TYPE,
    ui.INDEX_NAME,
    ui.TABLE_NAME,
    ui.INDEX_TYPE,
    ui.UNIQUENESS,
    ui.STATUS,
    LISTAGG(uic.COLUMN_NAME, ', ') WITHIN GROUP (ORDER BY uic.COLUMN_POSITION) as COLUMNS
FROM USER_INDEXES ui
LEFT JOIN USER_IND_COLUMNS uic ON ui.INDEX_NAME = uic.INDEX_NAME
WHERE ui.TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO')
GROUP BY ui.INDEX_NAME, ui.TABLE_NAME, ui.INDEX_TYPE, ui.UNIQUENESS, ui.STATUS
ORDER BY ui.TABLE_NAME, ui.INDEX_NAME;

-- 2. 检查必需的性能优化索引
WITH REQUIRED_INDEXES AS (
    SELECT 'IDX_MEDICAL_CASE_SEARCH' as INDEX_NAME, 'MEDICAL_CASE' as TABLE_NAME FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_PATIENT_SEARCH', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_HOSPITAL_STATS', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_COST_ANALYSIS', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_DATE_RANGE', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_MONITORING', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_PERFORMANCE', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_BATCH', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_USER_HISTORY', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_SUPERVISION_SEARCH', 'RULE_SUPERVISION' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_SUPERVISION_CATEGORY', 'RULE_SUPERVISION' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_SUPERVISION_STATUS', 'RULE_SUPERVISION' FROM DUAL
    UNION ALL SELECT 'IDX_USER_INFO_SEARCH', 'USER_INFO' FROM DUAL
    UNION ALL SELECT 'IDX_USER_INFO_STATUS', 'USER_INFO' FROM DUAL
    UNION ALL SELECT 'IDX_USER_INFO_DEPARTMENT', 'USER_INFO' FROM DUAL
)
SELECT 
    'MISSING_INDEXES' as CHECK_TYPE,
    ri.INDEX_NAME,
    ri.TABLE_NAME,
    CASE 
        WHEN ui.INDEX_NAME IS NOT NULL THEN '✅ 已创建'
        ELSE '❌ 缺失'
    END as STATUS
FROM REQUIRED_INDEXES ri
LEFT JOIN USER_INDEXES ui ON ri.INDEX_NAME = ui.INDEX_NAME AND ri.TABLE_NAME = ui.TABLE_NAME
ORDER BY ri.TABLE_NAME, ri.INDEX_NAME;

-- 3. 检查表统计信息更新状态
SELECT 
    'TABLE_STATS' as CHECK_TYPE,
    TABLE_NAME,
    NUM_ROWS,
    BLOCKS,
    AVG_ROW_LEN,
    LAST_ANALYZED,
    CASE 
        WHEN LAST_ANALYZED IS NULL THEN '❌ 未分析'
        WHEN LAST_ANALYZED < SYSDATE - 7 THEN '⚠️ 需更新'
        ELSE '✅ 最新'
    END as STATS_STATUS
FROM USER_TABLES 
WHERE TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO')
ORDER BY TABLE_NAME;

-- 4. 检查索引使用情况（需要DBA权限）
/*
SELECT 
    'INDEX_USAGE' as CHECK_TYPE,
    i.INDEX_NAME,
    i.TABLE_NAME,
    NVL(iu.TOTAL_ACCESS_COUNT, 0) as ACCESS_COUNT,
    NVL(iu.TOTAL_EXEC_COUNT, 0) as EXEC_COUNT,
    CASE 
        WHEN iu.TOTAL_ACCESS_COUNT IS NULL THEN '❌ 未使用'
        WHEN iu.TOTAL_ACCESS_COUNT < 10 THEN '⚠️ 使用较少'
        ELSE '✅ 正常使用'
    END as USAGE_STATUS
FROM USER_INDEXES i
LEFT JOIN V$INDEX_USAGE_INFO iu ON i.INDEX_NAME = iu.NAME
WHERE i.TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO')
ORDER BY i.TABLE_NAME, i.INDEX_NAME;
*/

-- 5. 生成缺失索引的创建脚本
SELECT 
    'CREATE_MISSING_INDEXES' as CHECK_TYPE,
    'CREATE INDEX ' || ri.INDEX_NAME || ' ON ' || ri.TABLE_NAME || ' (' ||
    CASE ri.INDEX_NAME
        WHEN 'IDX_MEDICAL_CASE_SEARCH' THEN 'CASE_TYPE, MEDICAL_CATEGORY, CREATED_AT DESC'
        WHEN 'IDX_MEDICAL_CASE_PATIENT_SEARCH' THEN 'PATIENT_ID_CARD, PATIENT_NAME, IS_DELETED'
        WHEN 'IDX_MEDICAL_CASE_HOSPITAL_STATS' THEN 'HOSPITAL_CODE, ADMISSION_DATE, TOTAL_COST'
        WHEN 'IDX_MEDICAL_CASE_COST_ANALYSIS' THEN 'TOTAL_COST, CASE_TYPE, MEDICAL_CATEGORY'
        WHEN 'IDX_MEDICAL_CASE_DATE_RANGE' THEN 'ADMISSION_DATE, DISCHARGE_DATE, IS_DELETED'
        WHEN 'IDX_RULE_EXEC_MONITORING' THEN 'RULE_ID, EXECUTION_STATUS, STARTED_AT DESC'
        WHEN 'IDX_RULE_EXEC_PERFORMANCE' THEN 'EXECUTION_STATUS, EXECUTION_DURATION, STARTED_AT'
        WHEN 'IDX_RULE_EXEC_BATCH' THEN 'EXECUTION_ID, STARTED_AT DESC'
        WHEN 'IDX_RULE_EXEC_USER_HISTORY' THEN 'EXECUTED_BY, STARTED_AT DESC, EXECUTION_STATUS'
        WHEN 'IDX_RULE_SUPERVISION_SEARCH' THEN 'RULE_NAME, RULE_CODE, IS_DELETED'
        WHEN 'IDX_RULE_SUPERVISION_CATEGORY' THEN 'RULE_CATEGORY, RULE_TYPE, IS_ACTIVE'
        WHEN 'IDX_RULE_SUPERVISION_STATUS' THEN 'IS_ACTIVE, IS_DELETED, CREATED_AT DESC'
        WHEN 'IDX_USER_INFO_SEARCH' THEN 'USERNAME, REAL_NAME, STATUS, IS_DELETED'
        WHEN 'IDX_USER_INFO_STATUS' THEN 'STATUS, IS_DELETED, CREATED_AT DESC'
        WHEN 'IDX_USER_INFO_DEPARTMENT' THEN 'DEPARTMENT, STATUS, IS_DELETED'
        ELSE 'UNKNOWN_COLUMNS'
    END || ');' as CREATE_SCRIPT
FROM (
    SELECT 'IDX_MEDICAL_CASE_SEARCH' as INDEX_NAME, 'MEDICAL_CASE' as TABLE_NAME FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_PATIENT_SEARCH', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_HOSPITAL_STATS', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_COST_ANALYSIS', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_MEDICAL_CASE_DATE_RANGE', 'MEDICAL_CASE' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_MONITORING', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_PERFORMANCE', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_BATCH', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_EXEC_USER_HISTORY', 'RULE_EXECUTION_LOG' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_SUPERVISION_SEARCH', 'RULE_SUPERVISION' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_SUPERVISION_CATEGORY', 'RULE_SUPERVISION' FROM DUAL
    UNION ALL SELECT 'IDX_RULE_SUPERVISION_STATUS', 'RULE_SUPERVISION' FROM DUAL
    UNION ALL SELECT 'IDX_USER_INFO_SEARCH', 'USER_INFO' FROM DUAL
    UNION ALL SELECT 'IDX_USER_INFO_STATUS', 'USER_INFO' FROM DUAL
    UNION ALL SELECT 'IDX_USER_INFO_DEPARTMENT', 'USER_INFO' FROM DUAL
) ri
WHERE NOT EXISTS (
    SELECT 1 FROM USER_INDEXES ui 
    WHERE ui.INDEX_NAME = ri.INDEX_NAME AND ui.TABLE_NAME = ri.TABLE_NAME
);

-- 6. 检查表空间使用情况
SELECT 
    'TABLESPACE_USAGE' as CHECK_TYPE,
    TABLESPACE_NAME,
    ROUND(BYTES/1024/1024, 2) as USED_MB,
    ROUND(MAXBYTES/1024/1024, 2) as MAX_MB,
    ROUND((BYTES/MAXBYTES)*100, 2) as USAGE_PERCENT,
    CASE 
        WHEN (BYTES/MAXBYTES)*100 > 90 THEN '❌ 空间不足'
        WHEN (BYTES/MAXBYTES)*100 > 80 THEN '⚠️ 空间紧张'
        ELSE '✅ 空间充足'
    END as SPACE_STATUS
FROM USER_TS_QUOTAS
WHERE MAXBYTES > 0;

-- 7. 性能优化建议
SELECT 
    'OPTIMIZATION_TIPS' as CHECK_TYPE,
    '1. 定期更新表统计信息: EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, ''TABLE_NAME'');' as RECOMMENDATION
FROM DUAL
UNION ALL
SELECT 
    'OPTIMIZATION_TIPS',
    '2. 监控慢查询: 查看执行时间超过1秒的SQL语句'
FROM DUAL
UNION ALL
SELECT 
    'OPTIMIZATION_TIPS',
    '3. 定期重建索引: ALTER INDEX index_name REBUILD;'
FROM DUAL
UNION ALL
SELECT 
    'OPTIMIZATION_TIPS',
    '4. 使用查询缓存: 在应用层实现查询结果缓存'
FROM DUAL
UNION ALL
SELECT 
    'OPTIMIZATION_TIPS',
    '5. 分区大表: 对于超过100万行的表考虑分区'
FROM DUAL;

-- 8. 生成性能报告摘要
SELECT 
    'PERFORMANCE_SUMMARY' as CHECK_TYPE,
    (SELECT COUNT(*) FROM USER_INDEXES WHERE TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO')) as TOTAL_INDEXES,
    (SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO') AND LAST_ANALYZED IS NOT NULL) as ANALYZED_TABLES,
    (SELECT SUM(NUM_ROWS) FROM USER_TABLES WHERE TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO')) as TOTAL_ROWS,
    SYSDATE as REPORT_TIME
FROM DUAL;
