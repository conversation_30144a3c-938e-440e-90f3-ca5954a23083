#!/usr/bin/env node

/**
 * 数据库完整设置脚本
 * 整合连接测试、数据库初始化、默认数据创建和验证
 */

const { testConnection } = require('./test-db-connection');
const { initializeDatabase } = require('./init-database');
const { initializeDefaultData } = require('./init-default-data');
const { verifyDatabase } = require('./verify-database');

/**
 * 主设置函数
 */
async function setupDatabase(force = false) {
  console.log('🏥 医保基金监管平台 - 数据库完整设置');
  console.log('=' .repeat(70));
  console.log('📋 设置步骤:');
  console.log('   1️⃣ 测试数据库连接');
  console.log('   2️⃣ 初始化数据库结构');
  console.log('   3️⃣ 创建默认数据');
  console.log('   4️⃣ 验证设置结果');
  console.log('=' .repeat(70));
  
  try {
    // 步骤1: 测试数据库连接
    console.log('\n🔄 步骤 1/4: 测试数据库连接');
    console.log('-' .repeat(50));
    await testConnection();
    
    // 步骤2: 初始化数据库结构
    console.log('\n🔄 步骤 2/4: 初始化数据库结构');
    console.log('-' .repeat(50));
    await initializeDatabase(force);
    
    // 步骤3: 创建默认数据
    console.log('\n🔄 步骤 3/4: 创建默认数据');
    console.log('-' .repeat(50));
    await initializeDefaultData();
    
    // 步骤4: 验证设置结果
    console.log('\n🔄 步骤 4/4: 验证设置结果');
    console.log('-' .repeat(50));
    await verifyDatabase();
    
    // 设置完成
    console.log('\n🎉 数据库设置完成！');
    console.log('=' .repeat(70));
    console.log('📋 设置摘要:');
    console.log('   ✅ 数据库连接正常');
    console.log('   ✅ 数据库结构已创建 (22个表)');
    console.log('   ✅ 默认数据已初始化');
    console.log('   ✅ 验证检查通过');
    
    console.log('\n🔐 管理员登录信息:');
    console.log('   用户名: admin');
    console.log('   密码: Admin123!');
    console.log('   ⚠️  请在首次登录后修改密码！');
    
    console.log('\n🚀 下一步:');
    console.log('   1. 启动应用程序');
    console.log('   2. 使用管理员账户登录');
    console.log('   3. 修改默认密码');
    console.log('   4. 创建其他用户账户');
    console.log('   5. 配置业务规则');
    
  } catch (error) {
    console.error('\n💥 数据库设置失败:');
    console.error(`   ${error.message}`);
    console.error('\n🔧 故障排除建议:');
    console.error('   1. 检查数据库连接配置 (.env.local)');
    console.error('   2. 确认数据库服务正在运行');
    console.error('   3. 验证用户权限是否充足');
    console.error('   4. 检查网络连接');
    console.error('   5. 查看详细错误日志');
    
    process.exit(1);
  }
}

/**
 * 解析命令行参数
 */
function parseArguments() {
  const args = process.argv.slice(2);
  
  const options = {
    help: args.includes('--help') || args.includes('-h'),
    force: args.includes('--force') || args.includes('-f'),
    skipTest: args.includes('--skip-test'),
    skipInit: args.includes('--skip-init'),
    skipData: args.includes('--skip-data'),
    skipVerify: args.includes('--skip-verify')
  };
  
  return options;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log('🏥 医保基金监管平台 - 数据库设置脚本');
  console.log('');
  console.log('用法:');
  console.log('  node scripts/setup-database.js [选项]');
  console.log('');
  console.log('选项:');
  console.log('  -h, --help        显示帮助信息');
  console.log('  -f, --force       强制重新初始化（清除现有数据）');
  console.log('  --skip-test       跳过连接测试');
  console.log('  --skip-init       跳过数据库初始化');
  console.log('  --skip-data       跳过默认数据创建');
  console.log('  --skip-verify     跳过验证检查');
  console.log('');
  console.log('示例:');
  console.log('  node scripts/setup-database.js                # 完整设置');
  console.log('  node scripts/setup-database.js --force        # 强制重新设置');
  console.log('  node scripts/setup-database.js --skip-test    # 跳过连接测试');
  console.log('');
  console.log('环境变量 (.env.local):');
  console.log('  DB_HOST           数据库主机地址');
  console.log('  DB_PORT           数据库端口');
  console.log('  DB_SERVICE_NAME   数据库服务名');
  console.log('  DB_USERNAME       数据库用户名');
  console.log('  DB_PASSWORD       数据库密码');
}

/**
 * 自定义设置流程
 */
async function customSetup(options) {
  console.log('🏥 医保基金监管平台 - 自定义数据库设置');
  console.log('=' .repeat(70));
  
  try {
    if (!options.skipTest) {
      console.log('\n🔄 测试数据库连接...');
      await testConnection();
    }
    
    if (!options.skipInit) {
      console.log('\n🔄 初始化数据库结构...');
      await initializeDatabase(options.force);
    }
    
    if (!options.skipData) {
      console.log('\n🔄 创建默认数据...');
      await initializeDefaultData();
    }
    
    if (!options.skipVerify) {
      console.log('\n🔄 验证设置结果...');
      await verifyDatabase();
    }
    
    console.log('\n🎉 自定义设置完成！');
    
  } catch (error) {
    console.error('\n💥 自定义设置失败:', error.message);
    process.exit(1);
  }
}

// 主函数
async function main() {
  const options = parseArguments();
  
  if (options.help) {
    showHelp();
    return;
  }
  
  // 检查环境变量
  const requiredEnvVars = ['DB_HOST', 'DB_PORT', 'DB_SERVICE_NAME', 'DB_USERNAME', 'DB_PASSWORD'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ 缺少必要的环境变量:');
    missingVars.forEach(varName => {
      console.error(`   ${varName}`);
    });
    console.error('\n💡 请检查 .env.local 文件');
    console.error('💡 运行 --help 查看详细说明');
    process.exit(1);
  }
  
  // 执行设置
  if (options.skipTest || options.skipInit || options.skipData || options.skipVerify) {
    await customSetup(options);
  } else {
    await setupDatabase(options.force);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { setupDatabase };
