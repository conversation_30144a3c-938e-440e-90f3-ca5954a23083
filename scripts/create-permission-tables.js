const oracledb = require('oracledb');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  user: process.env.DB_USERNAME || 'Mediinspect',
  password: process.env.DB_PASSWORD || 'Alt654301',
  connectString: `${process.env.DB_HOST || '2.tcp.cpolar.cn'}:${process.env.DB_PORT || '10123'}/${process.env.DB_SERVICE_NAME || 'ORCL'}`
};

async function createPermissionTables() {
  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    console.log('连接字符串:', dbConfig.connectString);
    
    connection = await oracledb.getConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 读取SQL脚本
    const sqlFilePath = path.join(__dirname, '..', 'database', 'create_permission_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 分割SQL语句，特殊处理PL/SQL块
    const statements = [];
    const lines = sqlContent.split('\n');
    let currentStatement = '';
    let inPlSqlBlock = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // 跳过注释和空行
      if (line.startsWith('--') || line.length === 0) {
        continue;
      }

      currentStatement += line + '\n';

      // 检测PL/SQL块的开始
      if (line.toUpperCase().includes('BEGIN') ||
          line.toUpperCase().includes('CREATE OR REPLACE TRIGGER')) {
        inPlSqlBlock = true;
      }

      // 检测PL/SQL块的结束
      if (inPlSqlBlock && (line === '/' || line.endsWith('/'))) {
        statements.push(currentStatement.replace(/\/$/, '').trim());
        currentStatement = '';
        inPlSqlBlock = false;
      }
      // 普通SQL语句以分号结束
      else if (!inPlSqlBlock && line.endsWith(';')) {
        statements.push(currentStatement.replace(/;$/, '').trim());
        currentStatement = '';
      }
    }

    // 添加最后一个语句（如果有）
    if (currentStatement.trim()) {
      statements.push(currentStatement.trim());
    }

    // 过滤空语句
    const validStatements = statements.filter(stmt => stmt.length > 5);

    console.log(`📝 找到 ${validStatements.length} 个SQL语句`);

    // 执行每个SQL语句
    for (let i = 0; i < validStatements.length; i++) {
      const statement = validStatements[i];
      
      // 跳过注释和空语句
      if (statement.startsWith('--') || statement.length < 5) {
        continue;
      }

      try {
        console.log(`⚡ 执行语句 ${i + 1}/${validStatements.length}...`);
        
        // 特殊处理：如果是CREATE语句，先检查对象是否存在
        if (statement.toUpperCase().includes('CREATE TABLE')) {
          const tableName = extractTableName(statement);
          if (tableName) {
            const exists = await checkTableExists(connection, tableName);
            if (exists) {
              console.log(`⚠️  表 ${tableName} 已存在，跳过创建`);
              continue;
            }
          }
        }

        if (statement.toUpperCase().includes('CREATE SEQUENCE')) {
          const sequenceName = extractSequenceName(statement);
          if (sequenceName) {
            const exists = await checkSequenceExists(connection, sequenceName);
            if (exists) {
              console.log(`⚠️  序列 ${sequenceName} 已存在，跳过创建`);
              continue;
            }
          }
        }

        await connection.execute(statement);
        console.log(`✅ 语句执行成功`);
        
      } catch (error) {
        console.error(`❌ 语句执行失败:`, error.message);
        console.log('失败的语句:', statement.substring(0, 100) + '...');
        
        // 对于某些可以忽略的错误，继续执行
        if (error.message.includes('ORA-00955') || // 名称已被现有对象使用
            error.message.includes('ORA-00001') || // 违反唯一约束条件
            error.message.includes('ORA-02260')) { // 表已经有主键
          console.log('⚠️  忽略此错误，继续执行...');
          continue;
        }
        
        // 对于严重错误，停止执行
        throw error;
      }
    }

    // 提交事务
    await connection.commit();
    console.log('✅ 所有SQL语句执行完成，事务已提交');

    // 验证创建结果
    await verifyTables(connection);

  } catch (error) {
    console.error('❌ 创建权限表失败:', error);
    if (connection) {
      try {
        await connection.rollback();
        console.log('🔄 事务已回滚');
      } catch (rollbackError) {
        console.error('❌ 回滚失败:', rollbackError);
      }
    }
    throw error;
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('🔌 数据库连接已关闭');
      } catch (closeError) {
        console.error('❌ 关闭连接失败:', closeError);
      }
    }
  }
}

// 提取表名
function extractTableName(createStatement) {
  const match = createStatement.match(/CREATE\s+TABLE\s+(\w+)/i);
  return match ? match[1] : null;
}

// 提取序列名
function extractSequenceName(createStatement) {
  const match = createStatement.match(/CREATE\s+SEQUENCE\s+(\w+)/i);
  return match ? match[1] : null;
}

// 检查表是否存在
async function checkTableExists(connection, tableName) {
  try {
    const result = await connection.execute(
      `SELECT COUNT(*) as COUNT FROM USER_TABLES WHERE TABLE_NAME = :tableName`,
      { tableName: tableName.toUpperCase() }
    );
    return result.rows[0][0] > 0;
  } catch (error) {
    return false;
  }
}

// 检查序列是否存在
async function checkSequenceExists(connection, sequenceName) {
  try {
    const result = await connection.execute(
      `SELECT COUNT(*) as COUNT FROM USER_SEQUENCES WHERE SEQUENCE_NAME = :sequenceName`,
      { sequenceName: sequenceName.toUpperCase() }
    );
    return result.rows[0][0] > 0;
  } catch (error) {
    return false;
  }
}

// 验证表创建结果
async function verifyTables(connection) {
  console.log('\n📊 验证表创建结果...');
  
  const tables = ['USER_PERMISSION_INFO', 'USER_ROLE_PERMISSION_MAPPING'];
  
  for (const tableName of tables) {
    try {
      const result = await connection.execute(
        `SELECT COUNT(*) as COUNT FROM ${tableName}`
      );
      const count = result.rows[0][0];
      console.log(`✅ 表 ${tableName}: ${count} 条记录`);
    } catch (error) {
      console.log(`❌ 表 ${tableName}: 不存在或无法访问`);
    }
  }
}

// 主函数
async function main() {
  try {
    console.log('🚀 开始创建权限表...\n');
    await createPermissionTables();
    console.log('\n🎉 权限表创建完成！');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 创建权限表失败:', error.message);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { createPermissionTables };
