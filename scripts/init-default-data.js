#!/usr/bin/env node

/**
 * 初始化默认数据脚本
 * 创建默认角色、管理员用户等基础数据
 */

const oracledb = require('oracledb');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

// Oracle客户端配置
oracledb.outFormat = oracledb.OUT_FORMAT_OBJECT;
oracledb.autoCommit = false;

// 数据库连接配置
const dbConfig = {
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  connectString: `${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_SERVICE_NAME}`,
};

/**
 * 创建默认角色
 */
async function createDefaultRoles(connection) {
  console.log('\n👥 创建默认角色...');
  
  const defaultRoles = [
    {
      roleCode: 'ADMIN',
      roleName: '系统管理员',
      description: '系统管理员角色，拥有所有权限，负责系统配置和用户管理'
    },
    {
      roleCode: 'SUPERVISOR',
      roleName: '监管主管',
      description: '监管主管角色，负责监管规则制定和审核结果确认'
    },
    {
      roleCode: 'OPERATOR',
      roleName: '业务操作员',
      description: '业务操作员角色，负责日常业务操作和数据录入'
    },
    {
      roleCode: 'AUDITOR',
      roleName: '审核员',
      description: '审核员角色，负责规则执行结果的审核和确认'
    },
    {
      roleCode: 'VIEWER',
      roleName: '查看员',
      description: '查看员角色，只能查看数据，不能进行修改操作'
    }
  ];
  
  for (const role of defaultRoles) {
    try {
      // 检查角色是否已存在
      const existingRole = await connection.execute(`
        SELECT COUNT(*) as COUNT FROM USER_ROLE_INFO 
        WHERE ROLE_CODE = :roleCode
      `, [role.roleCode]);
      
      if (existingRole.rows[0].COUNT > 0) {
        console.log(`   ⚠️  角色 ${role.roleCode} 已存在，跳过`);
        continue;
      }
      
      // 创建角色
      await connection.execute(`
        INSERT INTO USER_ROLE_INFO (
          ROLE_CODE, ROLE_NAME, DESCRIPTION, 
          IS_ACTIVE, IS_DELETED, CREATED_BY
        ) VALUES (
          :roleCode, :roleName, :description,
          1, 0, 1
        )
      `, {
        roleCode: role.roleCode,
        roleName: role.roleName,
        description: role.description
      });
      
      console.log(`   ✅ 创建角色: ${role.roleCode} - ${role.roleName}`);
      
    } catch (error) {
      console.error(`   ❌ 创建角色 ${role.roleCode} 失败: ${error.message}`);
      throw error;
    }
  }
  
  await connection.commit();
  console.log('✅ 默认角色创建完成');
}

/**
 * 创建默认管理员用户
 */
async function createDefaultAdmin(connection) {
  console.log('\n👤 创建默认管理员用户...');
  
  const adminUser = {
    username: 'admin',
    password: 'Admin123!',
    realName: '系统管理员',
    email: '<EMAIL>',
    department: 'IT部门',
    position: '系统管理员'
  };
  
  try {
    // 检查管理员是否已存在
    const existingAdmin = await connection.execute(`
      SELECT COUNT(*) as COUNT FROM USER_INFO 
      WHERE USERNAME = :username
    `, [adminUser.username]);
    
    if (existingAdmin.rows[0].COUNT > 0) {
      console.log('   ⚠️  管理员用户已存在，跳过');
      return;
    }
    
    // 加密密码
    const passwordHash = await bcrypt.hash(adminUser.password, 12);
    
    // 创建管理员用户
    const result = await connection.execute(`
      INSERT INTO USER_INFO (
        USERNAME, PASSWORD_HASH, REAL_NAME, EMAIL,
        DEPARTMENT, POSITION, STATUS, IS_DELETED, CREATED_BY
      ) VALUES (
        :username, :passwordHash, :realName, :email,
        :department, :position, 'ACTIVE', 0, 1
      ) RETURNING ID INTO :userId
    `, {
      username: adminUser.username,
      passwordHash: passwordHash,
      realName: adminUser.realName,
      email: adminUser.email,
      department: adminUser.department,
      position: adminUser.position,
      userId: { type: oracledb.NUMBER, dir: oracledb.BIND_OUT }
    });
    
    const userId = result.outBinds.userId[0];
    
    // 获取管理员角色ID
    const adminRole = await connection.execute(`
      SELECT ID FROM USER_ROLE_INFO WHERE ROLE_CODE = 'ADMIN'
    `);
    
    if (adminRole.rows.length === 0) {
      throw new Error('管理员角色不存在');
    }
    
    const roleId = adminRole.rows[0].ID;
    
    // 分配管理员角色
    await connection.execute(`
      INSERT INTO USER_ROLE_MAPPING (
        USER_ID, ROLE_ID, IS_DELETED, CREATED_BY
      ) VALUES (
        :userId, :roleId, 0, 1
      )
    `, {
      userId: userId,
      roleId: roleId
    });
    
    await connection.commit();
    
    console.log(`   ✅ 创建管理员用户: ${adminUser.username}`);
    console.log(`   📧 邮箱: ${adminUser.email}`);
    console.log(`   🔑 默认密码: ${adminUser.password}`);
    console.log('   ⚠️  请在首次登录后修改密码！');
    
  } catch (error) {
    await connection.rollback();
    console.error(`   ❌ 创建管理员用户失败: ${error.message}`);
    throw error;
  }
}

/**
 * 创建默认知识库分类
 */
async function createDefaultKnowledgeCategories(connection) {
  console.log('\n📚 创建默认知识库分类...');
  
  const defaultCategories = [
    {
      categoryCode: 'POLICY',
      categoryName: '政策法规',
      description: '医保相关的政策法规文档',
      level: 1,
      sortOrder: 1
    },
    {
      categoryCode: 'REGULATION',
      categoryName: '管理制度',
      description: '内部管理制度和操作规范',
      level: 1,
      sortOrder: 2
    },
    {
      categoryCode: 'MANUAL',
      categoryName: '操作手册',
      description: '系统操作手册和用户指南',
      level: 1,
      sortOrder: 3
    },
    {
      categoryCode: 'FAQ',
      categoryName: '常见问题',
      description: '常见问题解答和故障处理',
      level: 1,
      sortOrder: 4
    },
    {
      categoryCode: 'TRAINING',
      categoryName: '培训资料',
      description: '培训课件和学习资料',
      level: 1,
      sortOrder: 5
    }
  ];
  
  for (const category of defaultCategories) {
    try {
      // 检查分类是否已存在
      const existingCategory = await connection.execute(`
        SELECT COUNT(*) as COUNT FROM KNOWLEDGE_CATEGORY 
        WHERE CATEGORY_CODE = :categoryCode
      `, [category.categoryCode]);
      
      if (existingCategory.rows[0].COUNT > 0) {
        console.log(`   ⚠️  分类 ${category.categoryCode} 已存在，跳过`);
        continue;
      }
      
      // 创建分类
      await connection.execute(`
        INSERT INTO KNOWLEDGE_CATEGORY (
          CATEGORY_CODE, CATEGORY_NAME, DESCRIPTION,
          CATEGORY_LEVEL, SORT_ORDER, IS_ACTIVE, IS_DELETED, CREATED_BY
        ) VALUES (
          :categoryCode, :categoryName, :description,
          :categoryLevel, :sortOrder, 1, 0, 1
        )
      `, {
        categoryCode: category.categoryCode,
        categoryName: category.categoryName,
        description: category.description,
        categoryLevel: category.level,
        sortOrder: category.sortOrder
      });
      
      console.log(`   ✅ 创建分类: ${category.categoryCode} - ${category.categoryName}`);
      
    } catch (error) {
      console.error(`   ❌ 创建分类 ${category.categoryCode} 失败: ${error.message}`);
      throw error;
    }
  }
  
  await connection.commit();
  console.log('✅ 默认知识库分类创建完成');
}

/**
 * 主初始化函数
 */
async function initializeDefaultData() {
  let connection;
  
  try {
    console.log('🏥 医保基金监管平台 - 初始化默认数据');
    console.log('=' .repeat(60));
    
    // 建立数据库连接
    console.log('🔄 连接数据库...');
    connection = await oracledb.getConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 检查表是否存在
    const tables = await connection.execute(`
      SELECT TABLE_NAME FROM USER_TABLES 
      WHERE TABLE_NAME IN ('USER_ROLE_INFO', 'USER_INFO', 'KNOWLEDGE_CATEGORY')
    `);
    
    if (tables.rows.length < 3) {
      console.error('❌ 数据库表不完整，请先运行数据库初始化脚本');
      process.exit(1);
    }
    
    // 创建默认数据
    await createDefaultRoles(connection);
    await createDefaultAdmin(connection);
    await createDefaultKnowledgeCategories(connection);
    
    console.log('\n🎉 默认数据初始化完成！');
    console.log('\n📋 初始化摘要:');
    console.log('   👥 创建了 5 个默认角色');
    console.log('   👤 创建了管理员用户 (admin)');
    console.log('   📚 创建了 5 个知识库分类');
    console.log('\n🔐 管理员登录信息:');
    console.log('   用户名: admin');
    console.log('   密码: Admin123!');
    console.log('   ⚠️  请在首次登录后修改密码！');
    
  } catch (error) {
    console.error('\n💥 默认数据初始化失败:');
    console.error(`   ${error.message}`);
    process.exit(1);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('\n🔌 数据库连接已关闭');
      } catch (error) {
        console.error('⚠️ 关闭连接时出错:', error.message);
      }
    }
  }
}

// 运行脚本
if (require.main === module) {
  initializeDefaultData().catch(error => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { initializeDefaultData };
