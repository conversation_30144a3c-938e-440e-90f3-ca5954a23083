-- =====================================================
-- 数据库性能验证和监控脚本
-- 验证索引是否已创建，检查查询性能
-- =====================================================

-- 1. 检查索引是否已创建
SELECT 
    INDEX_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_POSITION,
    DESCEND
FROM USER_IND_COLUMNS 
WHERE TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO')
ORDER BY TABLE_NAME, INDEX_NAME, COLUMN_POSITION;

-- 2. 检查表统计信息
SELECT 
    TABLE_NAME,
    NUM_ROWS,
    BLOCKS,
    AVG_ROW_LEN,
    LAST_ANALYZED
FROM USER_TABLES 
WHERE TABLE_NAME IN ('MEDICAL_CASE', 'RULE_EXECUTION_LOG', 'RULE_SUPERVISION', 'USER_INFO');

-- 3. 医疗案例查询性能测试
-- 测试常见的分页查询
EXPLAIN PLAN FOR
SELECT 
    ID, CASE_NUMBER, PATIENT_NAME, CASE_TYPE, MEDICAL_CATEGORY,
    HOSPITAL_NAME, TOTAL_COST, ADMISSION_DATE, CREATED_AT
FROM MEDICAL_CASE 
WHERE IS_DELETED = 0 
    AND CASE_TYPE = 'INPATIENT'
    AND MEDICAL_CATEGORY = '异地住院'
    AND CREATED_AT >= SYSDATE - 30
ORDER BY CREATED_AT DESC;

SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

-- 4. 规则执行日志查询性能测试
EXPLAIN PLAN FOR
SELECT 
    rel.ID, rel.RULE_ID, rel.EXECUTION_STATUS, rel.STARTED_AT,
    rel.EXECUTION_DURATION, rs.RULE_NAME
FROM RULE_EXECUTION_LOG rel
LEFT JOIN RULE_SUPERVISION rs ON rel.RULE_ID = rs.ID
WHERE rel.RULE_ID = 1
    AND rel.EXECUTION_STATUS = 'SUCCESS'
    AND rel.STARTED_AT >= SYSDATE - 7
ORDER BY rel.STARTED_AT DESC;

SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

-- 5. 用户查询性能测试
EXPLAIN PLAN FOR
SELECT 
    ID, USERNAME, REAL_NAME, EMAIL, DEPARTMENT, STATUS
FROM USER_INFO 
WHERE IS_DELETED = 0
    AND STATUS = 'ACTIVE'
    AND (UPPER(USERNAME) LIKE '%ADMIN%' OR UPPER(REAL_NAME) LIKE '%ADMIN%')
ORDER BY CREATED_AT DESC;

SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

-- 6. 检查慢查询（如果有AWR报告）
-- 这需要DBA权限，仅供参考
/*
SELECT 
    sql_id,
    plan_hash_value,
    executions,
    elapsed_time/1000000 as elapsed_seconds,
    cpu_time/1000000 as cpu_seconds,
    buffer_gets,
    disk_reads,
    sql_text
FROM v$sql 
WHERE elapsed_time/executions > 1000000  -- 超过1秒的查询
    AND executions > 10
ORDER BY elapsed_time DESC;
*/

-- 7. 表空间使用情况检查
SELECT 
    tablespace_name,
    bytes/1024/1024 as size_mb,
    maxbytes/1024/1024 as max_size_mb,
    (bytes/maxbytes)*100 as usage_percent
FROM user_ts_quotas;

-- 8. 创建性能监控视图
CREATE OR REPLACE VIEW V_QUERY_PERFORMANCE AS
SELECT 
    'MEDICAL_CASE_LIST' as query_type,
    COUNT(*) as record_count,
    'Medical case list query performance' as description
FROM MEDICAL_CASE 
WHERE IS_DELETED = 0
UNION ALL
SELECT 
    'RULE_EXECUTION_LOG' as query_type,
    COUNT(*) as record_count,
    'Rule execution log query performance' as description
FROM RULE_EXECUTION_LOG
UNION ALL
SELECT 
    'USER_LIST' as query_type,
    COUNT(*) as record_count,
    'User list query performance' as description
FROM USER_INFO 
WHERE IS_DELETED = 0;

-- 9. 性能基准测试
-- 创建测试存储过程
CREATE OR REPLACE PROCEDURE SP_PERFORMANCE_BENCHMARK AS
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
    v_duration NUMBER;
    v_count NUMBER;
BEGIN
    -- 测试医疗案例查询
    v_start_time := SYSTIMESTAMP;
    
    SELECT COUNT(*) INTO v_count
    FROM MEDICAL_CASE 
    WHERE IS_DELETED = 0 
        AND CASE_TYPE = 'INPATIENT'
        AND CREATED_AT >= SYSDATE - 30;
    
    v_end_time := SYSTIMESTAMP;
    v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time)) * 1000;
    
    DBMS_OUTPUT.PUT_LINE('Medical Case Query: ' || v_duration || 'ms, Records: ' || v_count);
    
    -- 测试规则执行日志查询
    v_start_time := SYSTIMESTAMP;
    
    SELECT COUNT(*) INTO v_count
    FROM RULE_EXECUTION_LOG 
    WHERE EXECUTION_STATUS = 'SUCCESS'
        AND STARTED_AT >= SYSDATE - 7;
    
    v_end_time := SYSTIMESTAMP;
    v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time)) * 1000;
    
    DBMS_OUTPUT.PUT_LINE('Rule Execution Log Query: ' || v_duration || 'ms, Records: ' || v_count);
    
    -- 测试用户查询
    v_start_time := SYSTIMESTAMP;
    
    SELECT COUNT(*) INTO v_count
    FROM USER_INFO 
    WHERE IS_DELETED = 0 
        AND STATUS = 'ACTIVE';
    
    v_end_time := SYSTIMESTAMP;
    v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time)) * 1000;
    
    DBMS_OUTPUT.PUT_LINE('User Query: ' || v_duration || 'ms, Records: ' || v_count);
    
END;
/

-- 执行性能基准测试
SET SERVEROUTPUT ON;
EXEC SP_PERFORMANCE_BENCHMARK;

-- 10. 建议的统计信息更新
-- 定期更新表统计信息以保持查询优化器的准确性
BEGIN
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'MEDICAL_CASE',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'RULE_EXECUTION_LOG',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'RULE_SUPERVISION',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
    
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'USER_INFO',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        cascade => TRUE
    );
END;
/

COMMIT;
