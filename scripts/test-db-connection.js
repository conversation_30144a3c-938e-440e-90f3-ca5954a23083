#!/usr/bin/env node

/**
 * 数据库连接测试脚本
 * 用于验证Oracle数据库连接是否正常
 */

const oracledb = require('oracledb');
require('dotenv').config({ path: '.env.local' });

// Oracle客户端配置
oracledb.outFormat = oracledb.OUT_FORMAT_OBJECT;
oracledb.autoCommit = true;

// 数据库连接配置
const dbConfig = {
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  connectString: `${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_SERVICE_NAME}`,
  poolMin: parseInt(process.env.DB_POOL_MIN) || 2,
  poolMax: parseInt(process.env.DB_POOL_MAX) || 10,
  poolIncrement: parseInt(process.env.DB_POOL_INCREMENT) || 1,
};

async function testConnection() {
  let connection;
  
  try {
    console.log('🔄 正在测试数据库连接...');
    console.log(`📍 连接地址: ${dbConfig.connectString}`);
    console.log(`👤 用户名: ${dbConfig.user}`);
    
    // 建立连接
    connection = await oracledb.getConnection(dbConfig);
    
    console.log('✅ 数据库连接成功！');
    
    // 测试基本查询
    const result = await connection.execute('SELECT SYSDATE FROM DUAL');
    console.log(`📅 数据库时间: ${result.rows[0].SYSDATE}`);
    
    // 检查用户权限
    const userInfo = await connection.execute(`
      SELECT USERNAME, ACCOUNT_STATUS, CREATED, DEFAULT_TABLESPACE 
      FROM USER_USERS
    `);
    
    if (userInfo.rows.length > 0) {
      const user = userInfo.rows[0];
      console.log('👤 用户信息:');
      console.log(`   用户名: ${user.USERNAME}`);
      console.log(`   状态: ${user.ACCOUNT_STATUS}`);
      console.log(`   创建时间: ${user.CREATED}`);
      console.log(`   默认表空间: ${user.DEFAULT_TABLESPACE}`);
    }
    
    // 检查表空间信息
    const tablespaceInfo = await connection.execute(`
      SELECT TABLESPACE_NAME, STATUS, CONTENTS 
      FROM USER_TABLESPACES
    `);
    
    if (tablespaceInfo.rows.length > 0) {
      console.log('💾 可用表空间:');
      tablespaceInfo.rows.forEach(ts => {
        console.log(`   ${ts.TABLESPACE_NAME} (${ts.STATUS}, ${ts.CONTENTS})`);
      });
    }
    
    // 检查现有表
    const existingTables = await connection.execute(`
      SELECT TABLE_NAME, NUM_ROWS, LAST_ANALYZED 
      FROM USER_TABLES 
      ORDER BY TABLE_NAME
    `);
    
    if (existingTables.rows.length > 0) {
      console.log('📋 现有表:');
      existingTables.rows.forEach(table => {
        console.log(`   ${table.TABLE_NAME} (${table.NUM_ROWS || 0} 行)`);
      });
    } else {
      console.log('📋 当前没有表，可以开始初始化');
    }
    
    console.log('🎉 数据库连接测试完成！');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:');
    console.error(`   错误代码: ${error.errorNum || 'N/A'}`);
    console.error(`   错误信息: ${error.message}`);
    
    if (error.errorNum) {
      switch (error.errorNum) {
        case 12541:
          console.error('💡 建议: 检查数据库服务器地址和端口是否正确');
          break;
        case 1017:
          console.error('💡 建议: 检查用户名和密码是否正确');
          break;
        case 12514:
          console.error('💡 建议: 检查服务名是否正确');
          break;
        default:
          console.error('💡 建议: 检查网络连接和数据库配置');
      }
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('🔌 数据库连接已关闭');
      } catch (error) {
        console.error('⚠️ 关闭连接时出错:', error.message);
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🏥 医保基金监管平台 - 数据库连接测试');
  console.log('=' .repeat(50));
  
  // 检查环境变量
  const requiredEnvVars = ['DB_HOST', 'DB_PORT', 'DB_SERVICE_NAME', 'DB_USERNAME', 'DB_PASSWORD'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ 缺少必要的环境变量:');
    missingVars.forEach(varName => {
      console.error(`   ${varName}`);
    });
    console.error('💡 请检查 .env.local 文件');
    process.exit(1);
  }
  
  await testConnection();
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testConnection, dbConfig };
