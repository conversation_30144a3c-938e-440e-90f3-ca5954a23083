# 医保基金监管平台 - 后端架构设计文档

## 📋 目录

- [1. 架构概述](#1-架构概述)
- [2. 技术栈选型](#2-技术栈选型)
- [3. API设计规范](#3-api设计规范)
- [4. 服务层设计](#4-服务层设计)
- [5. 数据访问层](#5-数据访问层)
- [6. 中间件设计](#6-中间件设计)
- [7. 安全设计](#7-安全设计)
- [8. 性能优化](#8-性能优化)

## 1. 架构概述

### 1.1 设计目标
构建高性能、高可用、易扩展的后端服务，为医保基金监管提供稳定可靠的数据支撑。

### 1.2 架构特点
- **分层架构**: 清晰的分层设计，职责分离
- **微服务友好**: 支持未来向微服务架构演进
- **高性能**: 多层缓存和查询优化
- **高安全**: 完善的认证授权和数据保护
- **可扩展**: 支持水平和垂直扩展

### 1.3 核心原则
- **单一职责**: 每个模块只负责一个业务领域
- **依赖倒置**: 面向接口编程，降低耦合
- **开闭原则**: 对扩展开放，对修改关闭
- **最小权限**: 严格的权限控制和数据访问

## 2. 技术栈选型

### 2.1 核心框架
- **Node.js 18+**: 高性能JavaScript运行时
- **Next.js API Routes**: 全栈框架的API层
- **Express.js**: 复杂业务逻辑的独立服务
- **TypeScript**: 静态类型检查，提升代码质量

### 2.2 数据存储
- **Oracle Database 12c+**: 主业务数据库（读写分离）
- **ClickHouse**: 大数据分析专用数据库
- **Elasticsearch**: 全文搜索引擎
- **Redis**: 高性能缓存和消息队列
- **MinIO**: S3兼容的对象存储

### 2.3 认证授权
- **NextAuth.js**: 认证框架
- **JWT**: 无状态token认证
- **bcryptjs**: 密码加密
- **jsonwebtoken**: JWT token处理

### 2.4 数据处理
- **Prisma**: 现代化ORM框架
- **Zod**: 数据验证和类型推断
- **Bull Queue**: 任务队列处理
- **node-cron**: 定时任务调度

### 2.5 监控运维
- **Winston**: 结构化日志
- **Prometheus**: 指标监控
- **Jaeger**: 分布式链路追踪
- **Sentry**: 错误监控和性能分析

## 3. API设计规范

### 3.1 RESTful API设计

#### 3.1.1 URL设计规范
```
GET    /api/users              # 获取用户列表
POST   /api/users              # 创建用户
GET    /api/users/{id}         # 获取用户详情
PUT    /api/users/{id}         # 更新用户
DELETE /api/users/{id}         # 删除用户

GET    /api/medical-cases      # 获取病例列表
POST   /api/medical-cases      # 创建病例
GET    /api/medical-cases/{id} # 获取病例详情
PUT    /api/medical-cases/{id} # 更新病例
DELETE /api/medical-cases/{id} # 删除病例

POST   /api/supervision-rules/{id}/execute # 执行规则
GET    /api/analytics/dashboard             # 仪表板数据
```

#### 3.1.2 HTTP状态码规范
```typescript
// 成功响应
200 OK          // 请求成功
201 Created     // 资源创建成功
204 No Content  // 删除成功

// 客户端错误
400 Bad Request      // 请求参数错误
401 Unauthorized     // 未认证
403 Forbidden        // 权限不足
404 Not Found        // 资源不存在
409 Conflict         // 资源冲突
422 Unprocessable    // 数据验证失败

// 服务器错误
500 Internal Server Error // 服务器内部错误
502 Bad Gateway          // 网关错误
503 Service Unavailable  // 服务不可用
```

#### 3.1.3 响应格式规范
```typescript
// 成功响应格式
interface ApiResponse<T> {
  success: true;
  data: T;
  message?: string;
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
  };
}

// 错误响应格式
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  path: string;
}

// 分页响应格式
interface PaginatedResponse<T> {
  success: true;
  data: T[];
  meta: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}
```

### 3.2 API路由结构

#### 3.2.1 认证模块
```typescript
// /api/auth/*
POST   /api/auth/login          # 用户登录
POST   /api/auth/logout         # 用户登出
POST   /api/auth/refresh        # 刷新token
GET    /api/auth/profile        # 获取用户信息
PUT    /api/auth/profile        # 更新用户信息
POST   /api/auth/change-password # 修改密码
```

#### 3.2.2 用户管理模块
```typescript
// /api/users/*
GET    /api/users               # 获取用户列表
POST   /api/users               # 创建用户
GET    /api/users/{id}          # 获取用户详情
PUT    /api/users/{id}          # 更新用户
DELETE /api/users/{id}          # 删除用户
GET    /api/users/{id}/roles    # 获取用户角色
PUT    /api/users/{id}/roles    # 设置用户角色
```

#### 3.2.3 医疗病例模块
```typescript
// /api/medical-cases/*
GET    /api/medical-cases       # 获取病例列表
POST   /api/medical-cases       # 创建病例
GET    /api/medical-cases/{id}  # 获取病例详情
PUT    /api/medical-cases/{id}  # 更新病例
DELETE /api/medical-cases/{id}  # 删除病例
GET    /api/medical-cases/{id}/costs    # 获取费用明细
POST   /api/medical-cases/{id}/costs    # 添加费用明细
GET    /api/medical-cases/{id}/diagnosis # 获取诊断信息
POST   /api/medical-cases/{id}/diagnosis # 添加诊断信息
```

#### 3.2.4 监管规则模块
```typescript
// /api/supervision-rules/*
GET    /api/supervision-rules           # 获取规则列表
POST   /api/supervision-rules           # 创建规则
GET    /api/supervision-rules/{id}      # 获取规则详情
PUT    /api/supervision-rules/{id}      # 更新规则
DELETE /api/supervision-rules/{id}      # 删除规则
POST   /api/supervision-rules/{id}/execute    # 执行规则
GET    /api/supervision-rules/{id}/logs       # 获取执行日志
GET    /api/supervision-rules/{id}/results    # 获取执行结果
```

### 3.3 数据验证

#### 3.3.1 请求验证中间件
```typescript
import { z } from 'zod';

// 用户创建验证模式
const CreateUserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(8),
  realName: z.string().min(1).max(100),
  phone: z.string().optional(),
  departmentId: z.number().positive().optional(),
});

// 病例创建验证模式
const CreateMedicalCaseSchema = z.object({
  patientName: z.string().min(1).max(100),
  patientIdCard: z.string().length(18),
  hospitalCode: z.string().min(1),
  hospitalName: z.string().min(1),
  caseType: z.enum(['OUTPATIENT', 'INPATIENT']),
  admissionDate: z.string().datetime(),
  dischargeDate: z.string().datetime().optional(),
  totalCost: z.number().positive(),
});

// 验证中间件
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return (req: NextRequest, res: NextResponse, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.validatedData = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(422).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Data validation failed',
            details: error.errors,
          },
        });
      }
      next(error);
    }
  };
}
```

## 4. 服务层设计

### 4.1 服务分层架构

```
Controller Layer (API Routes)
├── 请求验证和参数解析
├── 权限检查和认证
├── 调用服务层方法
└── 响应格式化和错误处理

Service Layer (Business Logic)
├── 业务逻辑处理
├── 数据转换和计算
├── 事务管理
└── 外部服务调用

Repository Layer (Data Access)
├── 数据库操作
├── 查询构建和优化
├── 缓存管理
└── 数据映射
```

### 4.2 核心服务模块

#### 4.2.1 用户服务
```typescript
export class UserService {
  constructor(
    private userRepository: UserRepository,
    private roleRepository: RoleRepository,
    private cacheService: CacheService
  ) {}

  async createUser(userData: CreateUserData): Promise<User> {
    // 检查用户名和邮箱唯一性
    await this.validateUserUniqueness(userData.username, userData.email);
    
    // 密码加密
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    
    // 创建用户
    const user = await this.userRepository.create({
      ...userData,
      passwordHash: hashedPassword,
    });
    
    // 清除相关缓存
    await this.cacheService.invalidate('users:*');
    
    return user;
  }

  async getUserById(id: string): Promise<User | null> {
    // 尝试从缓存获取
    const cacheKey = `user:${id}`;
    const cached = await this.cacheService.get(cacheKey);
    if (cached) return cached;
    
    // 从数据库获取
    const user = await this.userRepository.findById(id);
    if (user) {
      await this.cacheService.set(cacheKey, user, 300); // 5分钟缓存
    }
    
    return user;
  }

  async updateUser(id: string, updateData: UpdateUserData): Promise<User> {
    const user = await this.userRepository.update(id, updateData);
    
    // 更新缓存
    const cacheKey = `user:${id}`;
    await this.cacheService.set(cacheKey, user, 300);
    
    return user;
  }

  private async validateUserUniqueness(username: string, email: string): Promise<void> {
    const existingUser = await this.userRepository.findByUsernameOrEmail(username, email);
    if (existingUser) {
      throw new ConflictError('Username or email already exists');
    }
  }
}
```

#### 4.2.2 医疗病例服务
```typescript
export class MedicalCaseService {
  constructor(
    private caseRepository: MedicalCaseRepository,
    private costRepository: CostDetailRepository,
    private diagnosisRepository: DiagnosisRepository,
    private auditService: AuditService
  ) {}

  async createCase(caseData: CreateCaseData, userId: string): Promise<MedicalCase> {
    return await this.caseRepository.transaction(async (tx) => {
      // 生成病例编号
      const caseNumber = await this.generateCaseNumber();
      
      // 创建主病例记录
      const medicalCase = await this.caseRepository.create({
        ...caseData,
        caseNumber,
        createdBy: userId,
      }, tx);
      
      // 记录审计日志
      await this.auditService.logOperation({
        operation: 'CREATE_MEDICAL_CASE',
        resourceId: medicalCase.id,
        userId,
        details: { caseNumber },
      });
      
      return medicalCase;
    });
  }

  async getCaseWithDetails(id: string): Promise<MedicalCaseDetail> {
    const medicalCase = await this.caseRepository.findById(id);
    if (!medicalCase) {
      throw new NotFoundError('Medical case not found');
    }
    
    // 并行获取相关数据
    const [costs, diagnoses, surgeries, groupInfo] = await Promise.all([
      this.costRepository.findByCaseId(id),
      this.diagnosisRepository.findByCaseId(id),
      this.surgeryRepository.findByCaseId(id),
      this.groupRepository.findByCaseId(id),
    ]);
    
    return {
      ...medicalCase,
      costs,
      diagnoses,
      surgeries,
      groupInfo,
    };
  }

  private async generateCaseNumber(): Promise<string> {
    const date = new Date();
    const prefix = `MC${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    const sequence = await this.caseRepository.getNextSequence(prefix);
    return `${prefix}${sequence.toString().padStart(6, '0')}`;
  }
}
```

#### 4.2.3 监管规则服务
```typescript
export class SupervisionRuleService {
  constructor(
    private ruleRepository: RuleRepository,
    private executionRepository: ExecutionRepository,
    private queueService: QueueService,
    private ruleEngine: RuleEngine
  ) {}

  async executeRule(ruleId: string, userId: string): Promise<string> {
    const rule = await this.ruleRepository.findById(ruleId);
    if (!rule || !rule.isActive) {
      throw new BadRequestError('Rule not found or inactive');
    }

    // 创建执行记录
    const executionId = generateUUID();
    await this.executionRepository.create({
      id: executionId,
      ruleId,
      status: 'PENDING',
      startTime: new Date(),
      createdBy: userId,
    });

    // 添加到任务队列
    await this.queueService.add('rule-execution', {
      executionId,
      ruleId,
      userId,
    });

    return executionId;
  }

  async processRuleExecution(data: RuleExecutionData): Promise<void> {
    const { executionId, ruleId } = data;

    try {
      // 更新执行状态
      await this.executionRepository.updateStatus(executionId, 'RUNNING');

      // 执行规则
      const results = await this.ruleEngine.execute(ruleId);

      // 保存执行结果
      await this.saveExecutionResults(executionId, results);

      // 更新执行状态
      await this.executionRepository.updateStatus(executionId, 'COMPLETED', {
        endTime: new Date(),
        processedCount: results.length,
        successCount: results.filter(r => r.status === 'SUCCESS').length,
        errorCount: results.filter(r => r.status === 'ERROR').length,
      });

    } catch (error) {
      await this.executionRepository.updateStatus(executionId, 'FAILED', {
        endTime: new Date(),
        errorMessage: error.message,
      });
      throw error;
    }
  }

  private async saveExecutionResults(
    executionId: string,
    results: RuleExecutionResult[]
  ): Promise<void> {
    const batchSize = 1000;
    for (let i = 0; i < results.length; i += batchSize) {
      const batch = results.slice(i, i + batchSize);
      await this.executionRepository.saveBatchResults(executionId, batch);
    }
  }
}
```

## 5. 数据访问层

### 5.1 Repository模式

#### 5.1.1 基础Repository
```typescript
export abstract class BaseRepository<T> {
  constructor(protected db: Database) {}

  abstract getTableName(): string;

  async findById(id: string): Promise<T | null> {
    const query = `SELECT * FROM ${this.getTableName()} WHERE ID = :id`;
    const result = await this.db.execute(query, { id });
    return result.rows[0] || null;
  }

  async findMany(conditions: Record<string, any> = {}): Promise<T[]> {
    const whereClause = Object.keys(conditions).length > 0
      ? `WHERE ${Object.keys(conditions).map(key => `${key} = :${key}`).join(' AND ')}`
      : '';

    const query = `SELECT * FROM ${this.getTableName()} ${whereClause}`;
    const result = await this.db.execute(query, conditions);
    return result.rows;
  }

  async create(data: Partial<T>, transaction?: Transaction): Promise<T> {
    const fields = Object.keys(data).join(', ');
    const values = Object.keys(data).map(key => `:${key}`).join(', ');

    const query = `
      INSERT INTO ${this.getTableName()} (${fields})
      VALUES (${values})
      RETURNING *
    `;

    const db = transaction || this.db;
    const result = await db.execute(query, data);
    return result.rows[0];
  }

  async update(id: string, data: Partial<T>): Promise<T> {
    const setClause = Object.keys(data)
      .map(key => `${key} = :${key}`)
      .join(', ');

    const query = `
      UPDATE ${this.getTableName()}
      SET ${setClause}, UPDATED_AT = SYSDATE
      WHERE ID = :id
      RETURNING *
    `;

    const result = await this.db.execute(query, { ...data, id });
    return result.rows[0];
  }

  async delete(id: string): Promise<boolean> {
    const query = `DELETE FROM ${this.getTableName()} WHERE ID = :id`;
    const result = await this.db.execute(query, { id });
    return result.rowsAffected > 0;
  }

  async transaction<R>(callback: (tx: Transaction) => Promise<R>): Promise<R> {
    return await this.db.transaction(callback);
  }
}
```

#### 5.1.2 医疗病例Repository
```typescript
export class MedicalCaseRepository extends BaseRepository<MedicalCase> {
  getTableName(): string {
    return 'MEDICAL_CASE';
  }

  async findByPatientIdCard(idCard: string): Promise<MedicalCase[]> {
    const query = `
      SELECT * FROM ${this.getTableName()}
      WHERE PATIENT_ID_CARD = :idCard
      ORDER BY ADMISSION_DATE DESC
    `;
    const result = await this.db.execute(query, { idCard });
    return result.rows;
  }

  async getStatsByHospital(hospitalCode: string): Promise<CaseStats> {
    const query = `
      SELECT
        COUNT(*) as TOTAL_CASES,
        SUM(TOTAL_COST) as TOTAL_AMOUNT,
        AVG(TOTAL_COST) as AVG_AMOUNT,
        COUNT(CASE WHEN CASE_TYPE = 'INPATIENT' THEN 1 END) as INPATIENT_COUNT,
        COUNT(CASE WHEN CASE_TYPE = 'OUTPATIENT' THEN 1 END) as OUTPATIENT_COUNT
      FROM ${this.getTableName()}
      WHERE HOSPITAL_CODE = :hospitalCode
        AND STATUS = 'ACTIVE'
    `;
    const result = await this.db.execute(query, { hospitalCode });
    return result.rows[0];
  }
}
```
