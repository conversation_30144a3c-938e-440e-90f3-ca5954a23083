-- =====================================================
-- 医保基金监管平台 - 系统日志和审计模块表结构
-- =====================================================

-- 1. 系统操作日志表
CREATE TABLE SYSTEM_OPERATION_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    USER_ID             NUMBER(19,0),
    USERNAME            VARCHAR2(50),
    OPERATION_TYPE      VARCHAR2(50) NOT NULL,
    OPERATION_MODULE    VARCHAR2(50) NOT NULL,
    OPERATION_DESC      VARCHAR2(500) NOT NULL,
    REQUEST_METHOD      VARCHAR2(10),
    REQUEST_URL         VARCHAR2(500),
    REQUEST_PARAMS      CLOB,
    RESPONSE_STATUS     NUMBER(3,0),
    RESPONSE_DATA       CLOB,
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(500),
    EXECUTION_TIME      NUMBER(10,0),
    IS_SUCCESS          NUMBER(1,0) DEFAULT 1 NOT NULL,
    ERROR_MESSAGE       CLOB,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CONSTRAINT PK_SYSTEM_OPERATION_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_OPERATION_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_OPERATION_TYPE CHECK (OPERATION_TYPE IN ('CREATE','UPDATE','DELETE','QUERY','LOGIN','LOGOUT','EXPORT','IMPORT','EXECUTE')),
    CONSTRAINT CK_SYSTEM_OPERATION_MODULE CHECK (OPERATION_MODULE IN ('USER','ROLE','MEDICAL_CASE','SUPERVISION_RULE','KNOWLEDGE','SYSTEM','AUTH')),
    CONSTRAINT CK_SYSTEM_OPERATION_METHOD CHECK (REQUEST_METHOD IN ('GET','POST','PUT','DELETE','PATCH')),
    CONSTRAINT CK_SYSTEM_OPERATION_SUCCESS CHECK (IS_SUCCESS IN (0,1))
);

COMMENT ON TABLE SYSTEM_OPERATION_LOG IS '系统操作日志表';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ID IS '日志ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USERNAME IS '用户名';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_TYPE IS '操作类型(CREATE:创建,UPDATE:更新,DELETE:删除,QUERY:查询,LOGIN:登录,LOGOUT:登出,EXPORT:导出,IMPORT:导入,EXECUTE:执行)';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_MODULE IS '操作模块(USER:用户管理,ROLE:角色管理,MEDICAL_CASE:病例管理,SUPERVISION_RULE:监管规则,KNOWLEDGE:知识库,SYSTEM:系统管理,AUTH:认证授权)';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_DESC IS '操作描述';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_METHOD IS '请求方法';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_URL IS '请求URL';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_PARAMS IS '请求参数';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_STATUS IS '响应状态码';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_DATA IS '响应数据';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.EXECUTION_TIME IS '执行时间(毫秒)';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.IS_SUCCESS IS '是否成功(0:失败,1:成功)';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ERROR_MESSAGE IS '错误信息';

-- 2. 用户登录日志表
CREATE TABLE SYSTEM_LOGIN_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    USER_ID             NUMBER(19,0),
    USERNAME            VARCHAR2(50) NOT NULL,
    LOGIN_TYPE          VARCHAR2(20) NOT NULL,
    LOGIN_STATUS        VARCHAR2(20) NOT NULL,
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(500),
    LOGGED_IN_AT        DATE DEFAULT SYSDATE NOT NULL,
    LOGGED_OUT_AT       DATE,
    SESSION_DURATION    NUMBER(10,0),
    FAILURE_REASON      VARCHAR2(200),
    LOCATION            VARCHAR2(100),
    DEVICE_TYPE         VARCHAR2(50),
    BROWSER_TYPE        VARCHAR2(50),
    CONSTRAINT PK_SYSTEM_LOGIN_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_LOGIN_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_LOGIN_TYPE CHECK (LOGIN_TYPE IN ('PASSWORD','SSO','TOKEN','REFRESH')),
    CONSTRAINT CK_SYSTEM_LOGIN_STATUS CHECK (LOGIN_STATUS IN ('SUCCESS','FAILED','LOCKED','EXPIRED'))
);

COMMENT ON TABLE SYSTEM_LOGIN_LOG IS '用户登录日志表';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.ID IS '登录日志ID';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.USERNAME IS '用户名';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGIN_TYPE IS '登录类型(PASSWORD:密码登录,SSO:单点登录,TOKEN:令牌登录,REFRESH:刷新登录)';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGIN_STATUS IS '登录状态(SUCCESS:成功,FAILED:失败,LOCKED:账户锁定,EXPIRED:密码过期)';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGGED_IN_AT IS '登录时间';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOGGED_OUT_AT IS '登出时间';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.SESSION_DURATION IS '会话时长(秒)';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.FAILURE_REASON IS '失败原因';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.LOCATION IS '登录地点';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.DEVICE_TYPE IS '设备类型';
COMMENT ON COLUMN SYSTEM_LOGIN_LOG.BROWSER_TYPE IS '浏览器类型';

-- 3. 数据变更审计表
CREATE TABLE SYSTEM_CHANGE_AUDIT (
    ID                  NUMBER(19,0) NOT NULL,
    TABLE_NAME          VARCHAR2(50) NOT NULL,
    RECORD_ID           NUMBER(19,0) NOT NULL,
    OPERATION_TYPE      VARCHAR2(10) NOT NULL,
    OLD_VALUES          CLOB,
    NEW_VALUES          CLOB,
    CHANGED_FIELDS      VARCHAR2(1000),
    USER_ID             NUMBER(19,0),
    USERNAME            VARCHAR2(50),
    IP_ADDRESS          VARCHAR2(50),
    CHANGED_AT          DATE DEFAULT SYSDATE NOT NULL,
    BUSINESS_KEY        VARCHAR2(100),
    CHANGE_REASON       VARCHAR2(500),
    CONSTRAINT PK_SYSTEM_CHANGE_AUDIT PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_CHANGE_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_CHANGE_OPERATION CHECK (OPERATION_TYPE IN ('INSERT','UPDATE','DELETE'))
);

COMMENT ON TABLE SYSTEM_CHANGE_AUDIT IS '数据变更审计表';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.ID IS '审计ID';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.TABLE_NAME IS '表名';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.RECORD_ID IS '记录ID';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.OPERATION_TYPE IS '操作类型(INSERT:新增,UPDATE:更新,DELETE:删除)';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.OLD_VALUES IS '变更前数据';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.NEW_VALUES IS '变更后数据';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.CHANGED_FIELDS IS '变更字段';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.USER_ID IS '操作用户ID';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.USERNAME IS '操作用户名';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.CHANGED_AT IS '变更时间';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.BUSINESS_KEY IS '业务主键';
COMMENT ON COLUMN SYSTEM_CHANGE_AUDIT.CHANGE_REASON IS '变更原因';

-- 4. 系统错误日志表
CREATE TABLE SYSTEM_ERROR_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    ERROR_CODE          VARCHAR2(50),
    ERROR_TYPE          VARCHAR2(50) NOT NULL,
    ERROR_LEVEL         VARCHAR2(20) NOT NULL,
    ERROR_MESSAGE       CLOB NOT NULL,
    STACK_TRACE         CLOB,
    REQUEST_URL         VARCHAR2(500),
    REQUEST_METHOD      VARCHAR2(10),
    REQUEST_PARAMS      CLOB,
    USER_ID             NUMBER(19,0),
    USERNAME            VARCHAR2(50),
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(500),
    ERROR_OCCURRED_AT   DATE DEFAULT SYSDATE NOT NULL,
    IS_RESOLVED         NUMBER(1,0) DEFAULT 0 NOT NULL,
    RESOLVED_BY         NUMBER(19,0),
    RESOLVED_AT         DATE,
    RESOLUTION_NOTES    VARCHAR2(1000),
    CONSTRAINT PK_SYSTEM_ERROR_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_ERROR_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_SYSTEM_ERROR_RESOLVER FOREIGN KEY (RESOLVED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_ERROR_TYPE CHECK (ERROR_TYPE IN ('DATABASE','NETWORK','BUSINESS','SECURITY','SYSTEM','VALIDATION')),
    CONSTRAINT CK_SYSTEM_ERROR_LEVEL CHECK (ERROR_LEVEL IN ('DEBUG','INFO','WARN','ERROR','FATAL')),
    CONSTRAINT CK_SYSTEM_ERROR_RESOLVED CHECK (IS_RESOLVED IN (0,1))
);

COMMENT ON TABLE SYSTEM_ERROR_LOG IS '系统错误日志表';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ID IS '错误日志ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_CODE IS '错误代码';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_TYPE IS '错误类型(DATABASE:数据库,NETWORK:网络,BUSINESS:业务,SECURITY:安全,SYSTEM:系统,VALIDATION:验证)';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_LEVEL IS '错误级别(DEBUG:调试,INFO:信息,WARN:警告,ERROR:错误,FATAL:致命)';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_MESSAGE IS '错误信息';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.STACK_TRACE IS '堆栈跟踪';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_URL IS '请求URL';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_METHOD IS '请求方法';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_PARAMS IS '请求参数';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.USERNAME IS '用户名';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_OCCURRED_AT IS '错误时间';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.IS_RESOLVED IS '是否已解决(0:未解决,1:已解决)';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLVED_BY IS '解决人ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLVED_AT IS '解决时间';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLUTION_NOTES IS '解决备注';

-- 创建序列和触发器
CREATE SEQUENCE SEQ_SYSTEM_OPERATION_LOG START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_LOGIN_LOG START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_CHANGE_AUDIT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_ERROR_LOG START WITH 1 INCREMENT BY 1;

-- 系统操作日志表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_OPERATION_LOG_ID
    BEFORE INSERT ON SYSTEM_OPERATION_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_OPERATION_LOG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;
/

-- 用户登录日志表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_LOGIN_LOG_ID
    BEFORE INSERT ON SYSTEM_LOGIN_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_LOGIN_LOG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;
/

-- 数据变更审计表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_CHANGE_AUDIT_ID
    BEFORE INSERT ON SYSTEM_CHANGE_AUDIT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_CHANGE_AUDIT.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;
/

-- 系统错误日志表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_ERROR_LOG_ID
    BEFORE INSERT ON SYSTEM_ERROR_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_ERROR_LOG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;
/

-- 创建索引
CREATE INDEX IDX_SYSTEM_OPERATION_USER ON SYSTEM_OPERATION_LOG(USER_ID);
CREATE INDEX IDX_SYSTEM_OPERATION_TYPE ON SYSTEM_OPERATION_LOG(OPERATION_TYPE);
CREATE INDEX IDX_SYSTEM_OPERATION_MODULE ON SYSTEM_OPERATION_LOG(OPERATION_MODULE);
CREATE INDEX IDX_SYSTEM_OPERATION_TIME ON SYSTEM_OPERATION_LOG(CREATED_AT);
CREATE INDEX IDX_SYSTEM_OPERATION_SUCCESS ON SYSTEM_OPERATION_LOG(IS_SUCCESS);
CREATE INDEX IDX_SYSTEM_OPERATION_USERNAME ON SYSTEM_OPERATION_LOG(USERNAME);
CREATE INDEX IDX_SYSTEM_OPERATION_IP ON SYSTEM_OPERATION_LOG(IP_ADDRESS);

CREATE INDEX IDX_SYSTEM_LOGIN_USER ON SYSTEM_LOGIN_LOG(USER_ID);
CREATE INDEX IDX_SYSTEM_LOGIN_USERNAME ON SYSTEM_LOGIN_LOG(USERNAME);
CREATE INDEX IDX_SYSTEM_LOGIN_STATUS ON SYSTEM_LOGIN_LOG(LOGIN_STATUS);
CREATE INDEX IDX_SYSTEM_LOGIN_TIME ON SYSTEM_LOGIN_LOG(LOGGED_IN_AT);
CREATE INDEX IDX_SYSTEM_LOGIN_IP ON SYSTEM_LOGIN_LOG(IP_ADDRESS);
CREATE INDEX IDX_SYSTEM_LOGIN_TYPE ON SYSTEM_LOGIN_LOG(LOGIN_TYPE);

CREATE INDEX IDX_SYSTEM_CHANGE_TABLE ON SYSTEM_CHANGE_AUDIT(TABLE_NAME);
CREATE INDEX IDX_SYSTEM_CHANGE_RECORD ON SYSTEM_CHANGE_AUDIT(RECORD_ID);
CREATE INDEX IDX_SYSTEM_CHANGE_OPERATION ON SYSTEM_CHANGE_AUDIT(OPERATION_TYPE);
CREATE INDEX IDX_SYSTEM_CHANGE_USER ON SYSTEM_CHANGE_AUDIT(USER_ID);
CREATE INDEX IDX_SYSTEM_CHANGE_TIME ON SYSTEM_CHANGE_AUDIT(CHANGED_AT);
CREATE INDEX IDX_SYSTEM_CHANGE_BUSINESS_KEY ON SYSTEM_CHANGE_AUDIT(BUSINESS_KEY);

CREATE INDEX IDX_SYSTEM_ERROR_TYPE ON SYSTEM_ERROR_LOG(ERROR_TYPE);
CREATE INDEX IDX_SYSTEM_ERROR_LEVEL ON SYSTEM_ERROR_LOG(ERROR_LEVEL);
CREATE INDEX IDX_SYSTEM_ERROR_TIME ON SYSTEM_ERROR_LOG(ERROR_OCCURRED_AT);
CREATE INDEX IDX_SYSTEM_ERROR_RESOLVED ON SYSTEM_ERROR_LOG(IS_RESOLVED);
CREATE INDEX IDX_SYSTEM_ERROR_USER ON SYSTEM_ERROR_LOG(USER_ID);
CREATE INDEX IDX_SYSTEM_ERROR_CODE ON SYSTEM_ERROR_LOG(ERROR_CODE);
CREATE INDEX IDX_SYSTEM_ERROR_RESOLVER ON SYSTEM_ERROR_LOG(RESOLVED_BY);
