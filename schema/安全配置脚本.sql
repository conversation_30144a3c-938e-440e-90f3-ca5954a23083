-- =====================================================
-- 医保基金监管平台数据库重构 - 安全配置脚本
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 说明: 数据库安全配置和加密设置
-- =====================================================

-- 设置环境
SET SERVEROUTPUT ON;
SET TIMING ON;
SET ECHO ON;

-- =====================================================
-- 1. 数据加密配置
-- =====================================================

-- 创建加密密钥管理包
CREATE OR REPLACE PACKAGE PKG_ENCRYPTION_MANAGER AS
    -- 加密敏感数据
    FUNCTION ENCRYPT_SENSITIVE_DATA(
        p_data IN VARCHAR2,
        p_data_type IN VARCHAR2 DEFAULT 'GENERAL'
    ) RETURN VARCHAR2;
    
    -- 解密敏感数据
    FUNCTION DECRYPT_SENSITIVE_DATA(
        p_encrypted_data IN VARCHAR2,
        p_data_type IN VARCHAR2 DEFAULT 'GENERAL'
    ) RETURN VARCHAR2;
    
    -- 生成数据掩码
    FUNCTION MASK_SENSITIVE_DATA(
        p_data IN VARCHAR2,
        p_mask_type IN VARCHAR2
    ) RETURN VARCHAR2;
    
    -- 验证数据完整性
    FUNCTION VERIFY_DATA_INTEGRITY(
        p_data IN VARCHAR2,
        p_hash IN VARCHAR2
    ) RETURN BOOLEAN;
    
    -- 生成数据哈希
    FUNCTION GENERATE_DATA_HASH(p_data IN VARCHAR2) RETURN VARCHAR2;
    
    -- 密钥轮换
    PROCEDURE ROTATE_ENCRYPTION_KEYS;
END PKG_ENCRYPTION_MANAGER;
/

CREATE OR REPLACE PACKAGE BODY PKG_ENCRYPTION_MANAGER AS
    
    -- 内部常量
    C_ENCRYPTION_KEY CONSTANT RAW(32) := UTL_RAW.CAST_TO_RAW('MediInspectSecretKey2024!@#$%^&*');
    C_IV CONSTANT RAW(16) := UTL_RAW.CAST_TO_RAW('InitVector123456');
    
    FUNCTION ENCRYPT_SENSITIVE_DATA(
        p_data IN VARCHAR2,
        p_data_type IN VARCHAR2 DEFAULT 'GENERAL'
    ) RETURN VARCHAR2 IS
        v_encrypted_raw RAW(2000);
        v_data_raw RAW(2000);
        v_key RAW(32);
        v_iv RAW(16);
    BEGIN
        IF p_data IS NULL THEN
            RETURN NULL;
        END IF;
        
        -- 根据数据类型选择不同的密钥
        CASE p_data_type
            WHEN 'ID_CARD' THEN
                v_key := UTL_RAW.CAST_TO_RAW('IDCardEncryptionKey2024!@#$%^&*');
            WHEN 'PHONE' THEN
                v_key := UTL_RAW.CAST_TO_RAW('PhoneEncryptionKey2024!@#$%^&*');
            WHEN 'NAME' THEN
                v_key := UTL_RAW.CAST_TO_RAW('NameEncryptionKey2024!@#$%^&*');
            WHEN 'EMAIL' THEN
                v_key := UTL_RAW.CAST_TO_RAW('EmailEncryptionKey2024!@#$%^&*');
            ELSE
                v_key := C_ENCRYPTION_KEY;
        END CASE;
        
        v_iv := C_IV;
        v_data_raw := UTL_RAW.CAST_TO_RAW(p_data);
        
        -- 使用AES-256-CBC加密
        v_encrypted_raw := DBMS_CRYPTO.ENCRYPT(
            src => v_data_raw,
            typ => DBMS_CRYPTO.AES256_CBC,
            key => v_key,
            iv => v_iv
        );
        
        -- 返回Base64编码的加密数据
        RETURN UTL_RAW.CAST_TO_VARCHAR2(UTL_ENCODE.BASE64_ENCODE(v_encrypted_raw));
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 记录加密错误但不暴露敏感信息
            INSERT INTO SYSTEM_ERROR_LOG (
                TENANT_ID, ERROR_TYPE, ERROR_CODE, ERROR_MESSAGE,
                CREATED_BY
            ) VALUES (
                1, 'ENCRYPTION_ERROR', 'ENC_001', 
                '数据加密失败: ' || SUBSTR(SQLERRM, 1, 500),
                1
            );
            COMMIT;
            
            -- 返回标记值表示加密失败
            RETURN '[ENCRYPTION_FAILED]';
    END ENCRYPT_SENSITIVE_DATA;
    
    FUNCTION DECRYPT_SENSITIVE_DATA(
        p_encrypted_data IN VARCHAR2,
        p_data_type IN VARCHAR2 DEFAULT 'GENERAL'
    ) RETURN VARCHAR2 IS
        v_decrypted_raw RAW(2000);
        v_encrypted_raw RAW(2000);
        v_key RAW(32);
        v_iv RAW(16);
    BEGIN
        IF p_encrypted_data IS NULL OR p_encrypted_data = '[ENCRYPTION_FAILED]' THEN
            RETURN NULL;
        END IF;
        
        -- 根据数据类型选择对应的密钥
        CASE p_data_type
            WHEN 'ID_CARD' THEN
                v_key := UTL_RAW.CAST_TO_RAW('IDCardEncryptionKey2024!@#$%^&*');
            WHEN 'PHONE' THEN
                v_key := UTL_RAW.CAST_TO_RAW('PhoneEncryptionKey2024!@#$%^&*');
            WHEN 'NAME' THEN
                v_key := UTL_RAW.CAST_TO_RAW('NameEncryptionKey2024!@#$%^&*');
            WHEN 'EMAIL' THEN
                v_key := UTL_RAW.CAST_TO_RAW('EmailEncryptionKey2024!@#$%^&*');
            ELSE
                v_key := C_ENCRYPTION_KEY;
        END CASE;
        
        v_iv := C_IV;
        
        -- Base64解码
        v_encrypted_raw := UTL_ENCODE.BASE64_DECODE(UTL_RAW.CAST_TO_RAW(p_encrypted_data));
        
        -- AES解密
        v_decrypted_raw := DBMS_CRYPTO.DECRYPT(
            src => v_encrypted_raw,
            typ => DBMS_CRYPTO.AES256_CBC,
            key => v_key,
            iv => v_iv
        );
        
        RETURN UTL_RAW.CAST_TO_VARCHAR2(v_decrypted_raw);
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 记录解密错误
            INSERT INTO SYSTEM_ERROR_LOG (
                TENANT_ID, ERROR_TYPE, ERROR_CODE, ERROR_MESSAGE,
                CREATED_BY
            ) VALUES (
                1, 'DECRYPTION_ERROR', 'DEC_001', 
                '数据解密失败: ' || SUBSTR(SQLERRM, 1, 500),
                1
            );
            COMMIT;
            
            RETURN '[DECRYPTION_FAILED]';
    END DECRYPT_SENSITIVE_DATA;
    
    FUNCTION MASK_SENSITIVE_DATA(
        p_data IN VARCHAR2,
        p_mask_type IN VARCHAR2
    ) RETURN VARCHAR2 IS
        v_masked_data VARCHAR2(4000);
        v_data_length NUMBER;
    BEGIN
        IF p_data IS NULL THEN
            RETURN NULL;
        END IF;
        
        v_data_length := LENGTH(p_data);
        
        CASE p_mask_type
            WHEN 'ID_CARD' THEN
                -- 身份证号掩码：显示前6位和后4位
                IF v_data_length >= 10 THEN
                    v_masked_data := SUBSTR(p_data, 1, 6) || 
                                   LPAD('*', v_data_length - 10, '*') || 
                                   SUBSTR(p_data, -4);
                ELSE
                    v_masked_data := LPAD('*', v_data_length, '*');
                END IF;
                
            WHEN 'PHONE' THEN
                -- 手机号掩码：显示前3位和后4位
                IF v_data_length >= 7 THEN
                    v_masked_data := SUBSTR(p_data, 1, 3) || 
                                   LPAD('*', v_data_length - 7, '*') || 
                                   SUBSTR(p_data, -4);
                ELSE
                    v_masked_data := LPAD('*', v_data_length, '*');
                END IF;
                
            WHEN 'NAME' THEN
                -- 姓名掩码：显示第一个字符
                IF v_data_length > 1 THEN
                    v_masked_data := SUBSTR(p_data, 1, 1) || 
                                   LPAD('*', v_data_length - 1, '*');
                ELSE
                    v_masked_data := '*';
                END IF;
                
            WHEN 'EMAIL' THEN
                -- 邮箱掩码：显示@前的第一个字符和@后的域名
                DECLARE
                    v_at_pos NUMBER;
                BEGIN
                    v_at_pos := INSTR(p_data, '@');
                    IF v_at_pos > 1 THEN
                        v_masked_data := SUBSTR(p_data, 1, 1) || 
                                       LPAD('*', v_at_pos - 2, '*') || 
                                       SUBSTR(p_data, v_at_pos);
                    ELSE
                        v_masked_data := LPAD('*', v_data_length, '*');
                    END IF;
                END;
                
            WHEN 'BANK_CARD' THEN
                -- 银行卡号掩码：显示后4位
                IF v_data_length > 4 THEN
                    v_masked_data := LPAD('*', v_data_length - 4, '*') || 
                                   SUBSTR(p_data, -4);
                ELSE
                    v_masked_data := LPAD('*', v_data_length, '*');
                END IF;
                
            ELSE
                -- 默认掩码：全部替换为*
                v_masked_data := LPAD('*', v_data_length, '*');
        END CASE;
        
        RETURN v_masked_data;
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN LPAD('*', NVL(LENGTH(p_data), 1), '*');
    END MASK_SENSITIVE_DATA;
    
    FUNCTION GENERATE_DATA_HASH(p_data IN VARCHAR2) RETURN VARCHAR2 IS
        v_hash_raw RAW(32);
    BEGIN
        IF p_data IS NULL THEN
            RETURN NULL;
        END IF;
        
        -- 使用SHA-256生成哈希
        v_hash_raw := DBMS_CRYPTO.HASH(
            src => UTL_RAW.CAST_TO_RAW(p_data),
            typ => DBMS_CRYPTO.HASH_SH256
        );
        
        RETURN RAWTOHEX(v_hash_raw);
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN NULL;
    END GENERATE_DATA_HASH;
    
    FUNCTION VERIFY_DATA_INTEGRITY(
        p_data IN VARCHAR2,
        p_hash IN VARCHAR2
    ) RETURN BOOLEAN IS
        v_calculated_hash VARCHAR2(64);
    BEGIN
        v_calculated_hash := GENERATE_DATA_HASH(p_data);
        RETURN v_calculated_hash = p_hash;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN FALSE;
    END VERIFY_DATA_INTEGRITY;
    
    PROCEDURE ROTATE_ENCRYPTION_KEYS IS
    BEGIN
        -- 密钥轮换逻辑（实际实现需要更复杂的密钥管理）
        INSERT INTO SYSTEM_OPERATION_LOG (
            TENANT_ID, OPERATION_TYPE, OPERATION_DESCRIPTION,
            OPERATOR_ID, OPERATION_RESULT
        ) VALUES (
            1, 'KEY_ROTATION', '加密密钥轮换操作',
            1, 'SUCCESS'
        );
        COMMIT;
        
        DBMS_OUTPUT.PUT_LINE('密钥轮换操作已记录');
    END ROTATE_ENCRYPTION_KEYS;
    
END PKG_ENCRYPTION_MANAGER;
/

-- =====================================================
-- 2. 访问控制配置
-- =====================================================

-- 创建访问控制包
CREATE OR REPLACE PACKAGE PKG_ACCESS_CONTROL AS
    -- 检查用户权限
    FUNCTION CHECK_USER_PERMISSION(
        p_user_id IN NUMBER,
        p_resource IN VARCHAR2,
        p_action IN VARCHAR2,
        p_tenant_id IN NUMBER DEFAULT NULL
    ) RETURN BOOLEAN;
    
    -- 记录访问日志
    PROCEDURE LOG_ACCESS_ATTEMPT(
        p_user_id IN NUMBER,
        p_resource IN VARCHAR2,
        p_action IN VARCHAR2,
        p_result IN VARCHAR2,
        p_ip_address IN VARCHAR2 DEFAULT NULL,
        p_user_agent IN VARCHAR2 DEFAULT NULL
    );
    
    -- 检查数据访问权限
    FUNCTION CHECK_DATA_ACCESS(
        p_user_id IN NUMBER,
        p_table_name IN VARCHAR2,
        p_row_id IN NUMBER,
        p_action IN VARCHAR2
    ) RETURN BOOLEAN;
    
    -- 获取用户可访问的租户列表
    FUNCTION GET_USER_TENANTS(p_user_id IN NUMBER) RETURN SYS.ODCINUMBERLIST;
    
    -- 数据脱敏检查
    FUNCTION SHOULD_MASK_DATA(
        p_user_id IN NUMBER,
        p_data_type IN VARCHAR2
    ) RETURN BOOLEAN;
END PKG_ACCESS_CONTROL;
/

CREATE OR REPLACE PACKAGE BODY PKG_ACCESS_CONTROL AS
    
    FUNCTION CHECK_USER_PERMISSION(
        p_user_id IN NUMBER,
        p_resource IN VARCHAR2,
        p_action IN VARCHAR2,
        p_tenant_id IN NUMBER DEFAULT NULL
    ) RETURN BOOLEAN IS
        v_permission_count NUMBER := 0;
        v_user_tenant_id NUMBER;
    BEGIN
        -- 获取用户的租户ID
        SELECT TENANT_ID INTO v_user_tenant_id
        FROM USER_INFO
        WHERE ID = p_user_id AND IS_DELETED = 0;
        
        -- 检查租户权限
        IF p_tenant_id IS NOT NULL AND v_user_tenant_id != p_tenant_id THEN
            RETURN FALSE;
        END IF;
        
        -- 检查用户角色权限
        SELECT COUNT(*)
        INTO v_permission_count
        FROM USER_ROLE_ASSIGNMENT ura
        JOIN USER_ROLE_INFO uri ON ura.ROLE_ID = uri.ID
        WHERE ura.USER_ID = p_user_id
        AND ura.IS_ACTIVE = 1
        AND uri.IS_ACTIVE = 1
        AND uri.IS_DELETED = 0
        AND (
            JSON_EXISTS(uri.PERMISSIONS, '$[*]' || 
                       ' ? (@.resource == "' || p_resource || '" && @.actions[*] == "' || p_action || '")')
            OR JSON_EXISTS(uri.PERMISSIONS, '$[*]' || 
                          ' ? (@.resource == "*" && @.actions[*] == "*")')
        );
        
        RETURN v_permission_count > 0;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN FALSE;
        WHEN OTHERS THEN
            -- 记录权限检查错误
            INSERT INTO SYSTEM_ERROR_LOG (
                TENANT_ID, ERROR_TYPE, ERROR_CODE, ERROR_MESSAGE,
                CREATED_BY
            ) VALUES (
                NVL(p_tenant_id, 1), 'PERMISSION_ERROR', 'PERM_001',
                '权限检查失败: ' || SUBSTR(SQLERRM, 1, 500),
                p_user_id
            );
            COMMIT;
            RETURN FALSE;
    END CHECK_USER_PERMISSION;
    
    PROCEDURE LOG_ACCESS_ATTEMPT(
        p_user_id IN NUMBER,
        p_resource IN VARCHAR2,
        p_action IN VARCHAR2,
        p_result IN VARCHAR2,
        p_ip_address IN VARCHAR2 DEFAULT NULL,
        p_user_agent IN VARCHAR2 DEFAULT NULL
    ) IS
        PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
        INSERT INTO SYSTEM_OPERATION_LOG (
            TENANT_ID, OPERATION_TYPE, OPERATION_DESCRIPTION,
            OPERATOR_ID, OPERATION_RESULT, CLIENT_IP, USER_AGENT
        ) VALUES (
            1, 'ACCESS_ATTEMPT',
            '资源访问: ' || p_resource || ', 操作: ' || p_action,
            p_user_id, p_result, p_ip_address, p_user_agent
        );
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
    END LOG_ACCESS_ATTEMPT;
    
    FUNCTION CHECK_DATA_ACCESS(
        p_user_id IN NUMBER,
        p_table_name IN VARCHAR2,
        p_row_id IN NUMBER,
        p_action IN VARCHAR2
    ) RETURN BOOLEAN IS
        v_tenant_id NUMBER;
        v_user_tenant_id NUMBER;
        v_sql VARCHAR2(4000);
        v_count NUMBER;
    BEGIN
        -- 获取用户租户ID
        SELECT TENANT_ID INTO v_user_tenant_id
        FROM USER_INFO
        WHERE ID = p_user_id AND IS_DELETED = 0;
        
        -- 构建动态SQL检查数据租户权限
        v_sql := 'SELECT TENANT_ID FROM ' || p_table_name || 
                ' WHERE ID = :1';
        
        EXECUTE IMMEDIATE v_sql INTO v_tenant_id USING p_row_id;
        
        -- 检查租户匹配
        IF v_tenant_id != v_user_tenant_id THEN
            RETURN FALSE;
        END IF;
        
        -- 检查具体的数据访问权限
        RETURN CHECK_USER_PERMISSION(p_user_id, p_table_name, p_action, v_tenant_id);
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN FALSE;
        WHEN OTHERS THEN
            RETURN FALSE;
    END CHECK_DATA_ACCESS;
    
    FUNCTION GET_USER_TENANTS(p_user_id IN NUMBER) RETURN SYS.ODCINUMBERLIST IS
        v_tenants SYS.ODCINUMBERLIST := SYS.ODCINUMBERLIST();
        v_user_tenant_id NUMBER;
    BEGIN
        -- 获取用户主租户
        SELECT TENANT_ID INTO v_user_tenant_id
        FROM USER_INFO
        WHERE ID = p_user_id AND IS_DELETED = 0;
        
        v_tenants.EXTEND;
        v_tenants(1) := v_user_tenant_id;
        
        -- 如果是超级管理员，可以访问所有租户
        DECLARE
            v_is_super_admin NUMBER;
        BEGIN
            SELECT COUNT(*)
            INTO v_is_super_admin
            FROM USER_ROLE_ASSIGNMENT ura
            JOIN USER_ROLE_INFO uri ON ura.ROLE_ID = uri.ID
            WHERE ura.USER_ID = p_user_id
            AND uri.ROLE_CODE = 'SUPER_ADMIN'
            AND ura.IS_ACTIVE = 1
            AND uri.IS_ACTIVE = 1;
            
            IF v_is_super_admin > 0 THEN
                -- 添加所有租户（这里简化处理）
                FOR i IN 2..10 LOOP
                    v_tenants.EXTEND;
                    v_tenants(v_tenants.COUNT) := i;
                END LOOP;
            END IF;
        END;
        
        RETURN v_tenants;
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN SYS.ODCINUMBERLIST();
    END GET_USER_TENANTS;
    
    FUNCTION SHOULD_MASK_DATA(
        p_user_id IN NUMBER,
        p_data_type IN VARCHAR2
    ) RETURN BOOLEAN IS
        v_mask_permission NUMBER := 0;
    BEGIN
        -- 检查用户是否有查看敏感数据的权限
        SELECT COUNT(*)
        INTO v_mask_permission
        FROM USER_ROLE_ASSIGNMENT ura
        JOIN USER_ROLE_INFO uri ON ura.ROLE_ID = uri.ID
        WHERE ura.USER_ID = p_user_id
        AND ura.IS_ACTIVE = 1
        AND uri.IS_ACTIVE = 1
        AND (
            JSON_EXISTS(uri.PERMISSIONS, '$[*]' || 
                       ' ? (@.resource == "SENSITIVE_DATA" && @.actions[*] == "VIEW")')
            OR JSON_EXISTS(uri.PERMISSIONS, '$[*]' || 
                          ' ? (@.resource == "*" && @.actions[*] == "*")')
        );
        
        -- 如果没有查看敏感数据权限，则需要脱敏
        RETURN v_mask_permission = 0;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- 出错时默认脱敏
            RETURN TRUE;
    END SHOULD_MASK_DATA;
    
END PKG_ACCESS_CONTROL;
/

-- =====================================================
-- 3. 审计配置
-- =====================================================

-- 创建审计触发器
CREATE OR REPLACE TRIGGER TRG_PATIENT_INFO_AUDIT
    AFTER INSERT OR UPDATE OR DELETE ON PATIENT_INFO
    FOR EACH ROW
DECLARE
    v_operation VARCHAR2(10);
    v_old_values CLOB;
    v_new_values CLOB;
BEGIN
    -- 确定操作类型
    IF INSERTING THEN
        v_operation := 'INSERT';
        v_new_values := JSON_OBJECT(
            'PATIENT_CODE' VALUE :NEW.PATIENT_CODE,
            'PATIENT_STATUS' VALUE :NEW.PATIENT_STATUS,
            'RISK_LEVEL' VALUE :NEW.RISK_LEVEL
        );
    ELSIF UPDATING THEN
        v_operation := 'UPDATE';
        v_old_values := JSON_OBJECT(
            'PATIENT_CODE' VALUE :OLD.PATIENT_CODE,
            'PATIENT_STATUS' VALUE :OLD.PATIENT_STATUS,
            'RISK_LEVEL' VALUE :OLD.RISK_LEVEL
        );
        v_new_values := JSON_OBJECT(
            'PATIENT_CODE' VALUE :NEW.PATIENT_CODE,
            'PATIENT_STATUS' VALUE :NEW.PATIENT_STATUS,
            'RISK_LEVEL' VALUE :NEW.RISK_LEVEL
        );
    ELSIF DELETING THEN
        v_operation := 'DELETE';
        v_old_values := JSON_OBJECT(
            'PATIENT_CODE' VALUE :OLD.PATIENT_CODE,
            'PATIENT_STATUS' VALUE :OLD.PATIENT_STATUS,
            'RISK_LEVEL' VALUE :OLD.RISK_LEVEL
        );
    END IF;
    
    -- 插入审计记录
    INSERT INTO SYSTEM_OPERATION_LOG (
        TENANT_ID, OPERATION_TYPE, OPERATION_DESCRIPTION,
        TABLE_NAME, RECORD_ID, OLD_VALUES, NEW_VALUES,
        OPERATOR_ID, OPERATION_RESULT
    ) VALUES (
        COALESCE(:NEW.TENANT_ID, :OLD.TENANT_ID),
        'DATA_' || v_operation,
        '患者信息' || v_operation || '操作',
        'PATIENT_INFO',
        COALESCE(:NEW.ID, :OLD.ID),
        v_old_values,
        v_new_values,
        COALESCE(:NEW.UPDATED_BY, :OLD.UPDATED_BY, :NEW.CREATED_BY),
        'SUCCESS'
    );
EXCEPTION
    WHEN OTHERS THEN
        -- 审计失败不应影响业务操作
        NULL;
END;
/

-- 创建医疗记录审计触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_RECORD_AUDIT
    AFTER INSERT OR UPDATE OR DELETE ON MEDICAL_RECORD
    FOR EACH ROW
DECLARE
    v_operation VARCHAR2(10);
    v_old_values CLOB;
    v_new_values CLOB;
BEGIN
    IF INSERTING THEN
        v_operation := 'INSERT';
        v_new_values := JSON_OBJECT(
            'RECORD_NUMBER' VALUE :NEW.RECORD_NUMBER,
            'TOTAL_COST' VALUE :NEW.TOTAL_COST,
            'RECORD_STATUS' VALUE :NEW.RECORD_STATUS
        );
    ELSIF UPDATING THEN
        v_operation := 'UPDATE';
        v_old_values := JSON_OBJECT(
            'RECORD_NUMBER' VALUE :OLD.RECORD_NUMBER,
            'TOTAL_COST' VALUE :OLD.TOTAL_COST,
            'RECORD_STATUS' VALUE :OLD.RECORD_STATUS
        );
        v_new_values := JSON_OBJECT(
            'RECORD_NUMBER' VALUE :NEW.RECORD_NUMBER,
            'TOTAL_COST' VALUE :NEW.TOTAL_COST,
            'RECORD_STATUS' VALUE :NEW.RECORD_STATUS
        );
    ELSIF DELETING THEN
        v_operation := 'DELETE';
        v_old_values := JSON_OBJECT(
            'RECORD_NUMBER' VALUE :OLD.RECORD_NUMBER,
            'TOTAL_COST' VALUE :OLD.TOTAL_COST,
            'RECORD_STATUS' VALUE :OLD.RECORD_STATUS
        );
    END IF;
    
    INSERT INTO SYSTEM_OPERATION_LOG (
        TENANT_ID, OPERATION_TYPE, OPERATION_DESCRIPTION,
        TABLE_NAME, RECORD_ID, OLD_VALUES, NEW_VALUES,
        OPERATOR_ID, OPERATION_RESULT
    ) VALUES (
        COALESCE(:NEW.TENANT_ID, :OLD.TENANT_ID),
        'DATA_' || v_operation,
        '医疗记录' || v_operation || '操作',
        'MEDICAL_RECORD',
        COALESCE(:NEW.ID, :OLD.ID),
        v_old_values,
        v_new_values,
        COALESCE(:NEW.UPDATED_BY, :OLD.UPDATED_BY, :NEW.CREATED_BY),
        'SUCCESS'
    );
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
/

-- =====================================================
-- 4. 数据脱敏视图
-- =====================================================

-- 创建患者信息脱敏视图
CREATE OR REPLACE VIEW V_PATIENT_INFO_MASKED AS
SELECT 
    ID,
    TENANT_ID,
    PATIENT_CODE,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(PATIENT_NAME_ENCRYPTED, 'NAME'),
        'NAME'
    ) AS PATIENT_NAME_MASKED,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(ID_CARD_ENCRYPTED, 'ID_CARD'),
        'ID_CARD'
    ) AS ID_CARD_MASKED,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(PHONE_ENCRYPTED, 'PHONE'),
        'PHONE'
    ) AS PHONE_MASKED,
    GENDER,
    BIRTH_DATE,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(ADDRESS_ENCRYPTED, 'GENERAL'),
        'NAME'
    ) AS ADDRESS_MASKED,
    INSURANCE_TYPE,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(INSURANCE_NUMBER_ENCRYPTED, 'GENERAL'),
        'BANK_CARD'
    ) AS INSURANCE_NUMBER_MASKED,
    PATIENT_STATUS,
    RISK_LEVEL,
    VERSION_NUMBER,
    IS_DELETED,
    CREATED_TIME,
    UPDATED_TIME,
    CREATED_BY,
    UPDATED_BY
FROM PATIENT_INFO
WHERE IS_DELETED = 0;

-- 创建用户信息脱敏视图
CREATE OR REPLACE VIEW V_USER_INFO_MASKED AS
SELECT 
    ID,
    TENANT_ID,
    USERNAME,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(REAL_NAME_ENCRYPTED, 'NAME'),
        'NAME'
    ) AS REAL_NAME_MASKED,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(EMAIL_ENCRYPTED, 'EMAIL'),
        'EMAIL'
    ) AS EMAIL_MASKED,
    PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA(
        PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(PHONE_ENCRYPTED, 'PHONE'),
        'PHONE'
    ) AS PHONE_MASKED,
    USER_TYPE,
    USER_STATUS,
    DEPARTMENT_CODE,
    POSITION_CODE,
    LAST_LOGIN_TIME,
    LOGIN_COUNT,
    PASSWORD_EXPIRE_TIME,
    ACCOUNT_LOCKED_TIME,
    VERSION_NUMBER,
    IS_DELETED,
    CREATED_TIME,
    UPDATED_TIME,
    CREATED_BY,
    UPDATED_BY
FROM USER_INFO
WHERE IS_DELETED = 0;

-- =====================================================
-- 5. 安全策略配置
-- =====================================================

-- 创建行级安全策略函数
CREATE OR REPLACE FUNCTION FN_TENANT_SECURITY_POLICY(
    p_schema IN VARCHAR2,
    p_object IN VARCHAR2
) RETURN VARCHAR2 IS
    v_user_id NUMBER;
    v_tenant_id NUMBER;
BEGIN
    -- 获取当前用户ID（这里简化处理，实际应从应用上下文获取）
    BEGIN
        SELECT TO_NUMBER(SYS_CONTEXT('USERENV', 'CLIENT_IDENTIFIER')) INTO v_user_id FROM DUAL;
    EXCEPTION
        WHEN OTHERS THEN
            v_user_id := 1; -- 默认用户
    END;
    
    -- 获取用户租户ID
    BEGIN
        SELECT TENANT_ID INTO v_tenant_id
        FROM USER_INFO
        WHERE ID = v_user_id AND IS_DELETED = 0;
    EXCEPTION
        WHEN OTHERS THEN
            v_tenant_id := 1; -- 默认租户
    END;
    
    -- 返回租户过滤条件
    RETURN 'TENANT_ID = ' || v_tenant_id;
EXCEPTION
    WHEN OTHERS THEN
        RETURN '1=0'; -- 拒绝访问
END;
/

-- 应用行级安全策略（需要DBA权限）
/*
BEGIN
    -- 为患者信息表应用安全策略
    DBMS_RLS.ADD_POLICY(
        object_schema => USER,
        object_name => 'PATIENT_INFO',
        policy_name => 'TENANT_SECURITY_POLICY',
        function_schema => USER,
        policy_function => 'FN_TENANT_SECURITY_POLICY',
        statement_types => 'SELECT,INSERT,UPDATE,DELETE'
    );
    
    -- 为医疗记录表应用安全策略
    DBMS_RLS.ADD_POLICY(
        object_schema => USER,
        object_name => 'MEDICAL_RECORD',
        policy_name => 'TENANT_SECURITY_POLICY',
        function_schema => USER,
        policy_function => 'FN_TENANT_SECURITY_POLICY',
        statement_types => 'SELECT,INSERT,UPDATE,DELETE'
    );
END;
/
*/

-- =====================================================
-- 6. 安全监控
-- =====================================================

-- 创建安全监控包
CREATE OR REPLACE PACKAGE PKG_SECURITY_MONITOR AS
    -- 检测异常访问
    PROCEDURE DETECT_ANOMALOUS_ACCESS;
    
    -- 生成安全报告
    PROCEDURE GENERATE_SECURITY_REPORT(
        p_start_date IN DATE DEFAULT SYSDATE - 7,
        p_end_date IN DATE DEFAULT SYSDATE
    );
    
    -- 检查密码强度
    FUNCTION CHECK_PASSWORD_STRENGTH(p_password IN VARCHAR2) RETURN NUMBER;
    
    -- 检测暴力破解
    PROCEDURE DETECT_BRUTE_FORCE_ATTACKS;
END PKG_SECURITY_MONITOR;
/

CREATE OR REPLACE PACKAGE BODY PKG_SECURITY_MONITOR AS
    
    PROCEDURE DETECT_ANOMALOUS_ACCESS IS
        v_anomaly_count NUMBER;
    BEGIN
        -- 检测异常登录时间
        SELECT COUNT(*)
        INTO v_anomaly_count
        FROM SYSTEM_OPERATION_LOG
        WHERE OPERATION_TYPE = 'LOGIN'
        AND CREATED_TIME >= SYSDATE - 1
        AND (
            EXTRACT(HOUR FROM CREATED_TIME) < 6 OR
            EXTRACT(HOUR FROM CREATED_TIME) > 22
        );
        
        IF v_anomaly_count > 0 THEN
            INSERT INTO SYSTEM_ERROR_LOG (
                TENANT_ID, ERROR_TYPE, ERROR_CODE, ERROR_MESSAGE,
                CREATED_BY
            ) VALUES (
                1, 'SECURITY_ALERT', 'SEC_001',
                '检测到异常时间登录: ' || v_anomaly_count || ' 次',
                1
            );
        END IF;
        
        -- 检测频繁访问
        FOR rec IN (
            SELECT OPERATOR_ID, COUNT(*) AS ACCESS_COUNT
            FROM SYSTEM_OPERATION_LOG
            WHERE CREATED_TIME >= SYSDATE - 1/24 -- 最近1小时
            GROUP BY OPERATOR_ID
            HAVING COUNT(*) > 1000
        ) LOOP
            INSERT INTO SYSTEM_ERROR_LOG (
                TENANT_ID, ERROR_TYPE, ERROR_CODE, ERROR_MESSAGE,
                CREATED_BY
            ) VALUES (
                1, 'SECURITY_ALERT', 'SEC_002',
                '用户 ' || rec.OPERATOR_ID || ' 频繁访问: ' || rec.ACCESS_COUNT || ' 次',
                1
            );
        END LOOP;
        
        COMMIT;
    END DETECT_ANOMALOUS_ACCESS;
    
    PROCEDURE GENERATE_SECURITY_REPORT(
        p_start_date IN DATE DEFAULT SYSDATE - 7,
        p_end_date IN DATE DEFAULT SYSDATE
    ) IS
    BEGIN
        DBMS_OUTPUT.PUT_LINE('=== 安全监控报告 ===');
        DBMS_OUTPUT.PUT_LINE('报告期间: ' || TO_CHAR(p_start_date, 'YYYY-MM-DD') || 
                           ' 至 ' || TO_CHAR(p_end_date, 'YYYY-MM-DD'));
        
        -- 登录统计
        DECLARE
            v_login_count NUMBER;
            v_failed_login_count NUMBER;
        BEGIN
            SELECT COUNT(*) INTO v_login_count
            FROM SYSTEM_OPERATION_LOG
            WHERE OPERATION_TYPE = 'LOGIN'
            AND OPERATION_RESULT = 'SUCCESS'
            AND CREATED_TIME BETWEEN p_start_date AND p_end_date;
            
            SELECT COUNT(*) INTO v_failed_login_count
            FROM SYSTEM_OPERATION_LOG
            WHERE OPERATION_TYPE = 'LOGIN'
            AND OPERATION_RESULT = 'FAILED'
            AND CREATED_TIME BETWEEN p_start_date AND p_end_date;
            
            DBMS_OUTPUT.PUT_LINE('成功登录: ' || v_login_count || ' 次');
            DBMS_OUTPUT.PUT_LINE('失败登录: ' || v_failed_login_count || ' 次');
        END;
        
        -- 数据访问统计
        DECLARE
            v_data_access_count NUMBER;
        BEGIN
            SELECT COUNT(*) INTO v_data_access_count
            FROM SYSTEM_OPERATION_LOG
            WHERE OPERATION_TYPE LIKE 'DATA_%'
            AND CREATED_TIME BETWEEN p_start_date AND p_end_date;
            
            DBMS_OUTPUT.PUT_LINE('数据操作: ' || v_data_access_count || ' 次');
        END;
        
        -- 安全告警统计
        DECLARE
            v_alert_count NUMBER;
        BEGIN
            SELECT COUNT(*) INTO v_alert_count
            FROM SYSTEM_ERROR_LOG
            WHERE ERROR_TYPE = 'SECURITY_ALERT'
            AND CREATED_TIME BETWEEN p_start_date AND p_end_date;
            
            DBMS_OUTPUT.PUT_LINE('安全告警: ' || v_alert_count || ' 次');
        END;
        
    END GENERATE_SECURITY_REPORT;
    
    FUNCTION CHECK_PASSWORD_STRENGTH(p_password IN VARCHAR2) RETURN NUMBER IS
        v_score NUMBER := 0;
        v_length NUMBER;
    BEGIN
        IF p_password IS NULL THEN
            RETURN 0;
        END IF;
        
        v_length := LENGTH(p_password);
        
        -- 长度评分
        IF v_length >= 8 THEN v_score := v_score + 20; END IF;
        IF v_length >= 12 THEN v_score := v_score + 10; END IF;
        
        -- 复杂度评分
        IF REGEXP_LIKE(p_password, '[a-z]') THEN v_score := v_score + 15; END IF;
        IF REGEXP_LIKE(p_password, '[A-Z]') THEN v_score := v_score + 15; END IF;
        IF REGEXP_LIKE(p_password, '[0-9]') THEN v_score := v_score + 15; END IF;
        IF REGEXP_LIKE(p_password, '[^a-zA-Z0-9]') THEN v_score := v_score + 25; END IF;
        
        RETURN LEAST(v_score, 100);
    END CHECK_PASSWORD_STRENGTH;
    
    PROCEDURE DETECT_BRUTE_FORCE_ATTACKS IS
    BEGIN
        -- 检测暴力破解攻击
        FOR rec IN (
            SELECT OPERATOR_ID, CLIENT_IP, COUNT(*) AS FAILED_COUNT
            FROM SYSTEM_OPERATION_LOG
            WHERE OPERATION_TYPE = 'LOGIN'
            AND OPERATION_RESULT = 'FAILED'
            AND CREATED_TIME >= SYSDATE - 1/24 -- 最近1小时
            GROUP BY OPERATOR_ID, CLIENT_IP
            HAVING COUNT(*) >= 5
        ) LOOP
            INSERT INTO SYSTEM_ERROR_LOG (
                TENANT_ID, ERROR_TYPE, ERROR_CODE, ERROR_MESSAGE,
                CREATED_BY
            ) VALUES (
                1, 'SECURITY_ALERT', 'SEC_003',
                '检测到暴力破解攻击 - 用户: ' || rec.OPERATOR_ID || 
                ', IP: ' || rec.CLIENT_IP || ', 失败次数: ' || rec.FAILED_COUNT,
                1
            );
        END LOOP;
        
        COMMIT;
    END DETECT_BRUTE_FORCE_ATTACKS;
    
END PKG_SECURITY_MONITOR;
/

-- =====================================================
-- 7. 创建安全监控作业
-- =====================================================

BEGIN
    -- 删除可能存在的旧作业
    BEGIN
        DBMS_SCHEDULER.DROP_JOB('SECURITY_MONITOR_JOB');
    EXCEPTION
        WHEN OTHERS THEN
            NULL;
    END;
    
    -- 创建安全监控作业
    DBMS_SCHEDULER.CREATE_JOB(
        job_name => 'SECURITY_MONITOR_JOB',
        job_type => 'PLSQL_BLOCK',
        job_action => '
            BEGIN
                PKG_SECURITY_MONITOR.DETECT_ANOMALOUS_ACCESS();
                PKG_SECURITY_MONITOR.DETECT_BRUTE_FORCE_ATTACKS();
            END;',
        start_date => SYSTIMESTAMP,
        repeat_interval => 'FREQ=HOURLY; INTERVAL=1',
        enabled => TRUE,
        comments => '安全监控作业'
    );
    
    DBMS_OUTPUT.PUT_LINE('安全监控作业创建完成');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('安全监控作业创建失败: ' || SQLERRM);
END;
/

-- =====================================================
-- 8. 输出完成信息
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('医保基金监管平台安全配置完成！');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('安全特性：');
    DBMS_OUTPUT.PUT_LINE('✓ AES-256数据加密');
    DBMS_OUTPUT.PUT_LINE('✓ 敏感数据脱敏');
    DBMS_OUTPUT.PUT_LINE('✓ 基于角色的访问控制(RBAC)');
    DBMS_OUTPUT.PUT_LINE('✓ 多租户数据隔离');
    DBMS_OUTPUT.PUT_LINE('✓ 完整审计追踪');
    DBMS_OUTPUT.PUT_LINE('✓ 行级安全策略');
    DBMS_OUTPUT.PUT_LINE('✓ 异常访问检测');
    DBMS_OUTPUT.PUT_LINE('✓ 暴力破解防护');
    DBMS_OUTPUT.PUT_LINE('✓ 密码强度检查');
    DBMS_OUTPUT.PUT_LINE('✓ 自动安全监控');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('使用说明：');
    DBMS_OUTPUT.PUT_LINE('1. 使用 PKG_ENCRYPTION_MANAGER 进行数据加密/解密');
    DBMS_OUTPUT.PUT_LINE('2. 使用 PKG_ACCESS_CONTROL 进行权限检查');
    DBMS_OUTPUT.PUT_LINE('3. 使用脱敏视图 V_*_MASKED 查看脱敏数据');
    DBMS_OUTPUT.PUT_LINE('4. 运行 PKG_SECURITY_MONITOR.GENERATE_SECURITY_REPORT() 生成安全报告');
    DBMS_OUTPUT.PUT_LINE('==============================================');
END;
/