-- =====================================================
-- 医保基金监管平台 - 医疗病例管理模块表结构
-- =====================================================

-- 1. 医疗病例主表
CREATE TABLE MEDICAL_CASE (
    ID                  NUMBER(19,0) NOT NULL,
    CASE_NUMBER         VARCHAR2(50) NOT NULL,
    PATIENT_NAME        VARCHAR2(100) NOT NULL,
    PATIENT_ID_CARD     VARCHAR2(18) NOT NULL,
    PATIENT_PHONE       VARCHAR2(20),
    PATIENT_AGE         NUMBER(3,0),
    PATIENT_GENDER      VARCHAR2(10),
    CASE_TYPE           VARCHAR2(20) NOT NULL,
    MEDICAL_CATEGORY    VARCHAR2(50) NOT NULL,
    HOSPITAL_NAME       VARCHAR2(200) NOT NULL,
    HOSPITAL_CODE       VARCHAR2(50),
    DEPARTMENT          VARCHAR2(100),
    DOCTOR_NAME         VARCHAR2(100),
    ADMISSION_DATE      DATE,
    DISCHARGE_DATE      DATE,
    TOTAL_COST          NUMBER(12,2) DEFAULT 0,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_MEDICAL_CASE PRIMARY KEY (ID),
    CONSTRAINT UK_MEDICAL_CASE_NUMBER UNIQUE (CASE_NUMBER),
    CONSTRAINT CK_MEDICAL_CASE_TYPE CHECK (CASE_TYPE IN ('INPATIENT','OUTPATIENT')),
    CONSTRAINT CK_MEDICAL_CATEGORY CHECK (MEDICAL_CATEGORY IN ('异地住院','急诊住院','普通住院','门诊慢特病','转外诊治住院','生育住院','统筹区内转院','住院前急诊','特药购药','急诊转住院','普通门诊','药店购慢特病药','定点药店购药','村卫门诊','特药门诊','外伤住院','日间手术','急诊','急诊2级危重','急诊3级急症','急诊1级濒危','新冠门诊','4级非急症','辅助生殖门诊','急诊(死亡)','家庭医生签约')),
    CONSTRAINT CK_MEDICAL_CASE_GENDER CHECK (PATIENT_GENDER IN ('MALE','FEMALE','OTHER')),
    CONSTRAINT CK_MEDICAL_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE MEDICAL_CASE IS '医疗病例主表';
COMMENT ON COLUMN MEDICAL_CASE.ID IS '病例ID';
COMMENT ON COLUMN MEDICAL_CASE.CASE_NUMBER IS '病例编号';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_NAME IS '患者姓名';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_ID_CARD IS '患者身份证号';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_PHONE IS '患者电话';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_AGE IS '患者年龄';
COMMENT ON COLUMN MEDICAL_CASE.PATIENT_GENDER IS '患者性别(MALE:男,FEMALE:女,OTHER:其他)';
COMMENT ON COLUMN MEDICAL_CASE.CASE_TYPE IS '病例类型(INPATIENT:住院,OUTPATIENT:门诊)';
COMMENT ON COLUMN MEDICAL_CASE.MEDICAL_CATEGORY IS '医疗类别(异地住院,急诊住院,普通住院,门诊慢特病,转外诊治住院,生育住院,统筹区内转院,住院前急诊,特药购药,急诊转住院,普通门诊,药店购慢特病药,定点药店购药,村卫门诊,特药门诊,外伤住院,日间手术,急诊,急诊2级危重,急诊3级急症,急诊1级濒危,新冠门诊,4级非急症,辅助生殖门诊,急诊(死亡),家庭医生签约)';
COMMENT ON COLUMN MEDICAL_CASE.HOSPITAL_NAME IS '医院名称';
COMMENT ON COLUMN MEDICAL_CASE.HOSPITAL_CODE IS '医院编码';
COMMENT ON COLUMN MEDICAL_CASE.DEPARTMENT IS '科室';
COMMENT ON COLUMN MEDICAL_CASE.DOCTOR_NAME IS '主治医生';
COMMENT ON COLUMN MEDICAL_CASE.ADMISSION_DATE IS '入院日期';
COMMENT ON COLUMN MEDICAL_CASE.DISCHARGE_DATE IS '出院日期';
COMMENT ON COLUMN MEDICAL_CASE.TOTAL_COST IS '总费用';

-- 2. 诊断信息表
CREATE TABLE MEDICAL_DIAGNOSIS (
    ID                  NUMBER(19,0) NOT NULL,
    CASE_ID             NUMBER(19,0) NOT NULL,
    DIAGNOSIS_TYPE      VARCHAR2(20) NOT NULL,
    DIAGNOSIS_CODE      VARCHAR2(20),
    DIAGNOSIS_NAME      VARCHAR2(200) NOT NULL,
    DIAGNOSIS_DESC      CLOB,
    IS_PRIMARY          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_MEDICAL_DIAGNOSIS PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_DIAGNOSIS_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID),
    CONSTRAINT CK_MEDICAL_DIAGNOSIS_TYPE CHECK (DIAGNOSIS_TYPE IN ('ADMISSION','DISCHARGE','DIFFERENTIAL')),
    CONSTRAINT CK_MEDICAL_IS_PRIMARY CHECK (IS_PRIMARY IN (0,1))
);

COMMENT ON TABLE MEDICAL_DIAGNOSIS IS '医疗诊断信息表';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.ID IS '诊断ID';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.CASE_ID IS '病例ID';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_TYPE IS '诊断类型(ADMISSION:入院诊断,DISCHARGE:出院诊断,DIFFERENTIAL:鉴别诊断)';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_CODE IS '诊断编码';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_NAME IS '诊断名称';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.DIAGNOSIS_DESC IS '诊断描述';
COMMENT ON COLUMN MEDICAL_DIAGNOSIS.IS_PRIMARY IS '是否主要诊断(0:否,1:是)';

-- 3. 手术信息表
CREATE TABLE MEDICAL_SURGERY (
    ID                  NUMBER(19,0) NOT NULL,
    CASE_ID             NUMBER(19,0) NOT NULL,
    SURGERY_CODE        VARCHAR2(20),
    SURGERY_NAME        VARCHAR2(200) NOT NULL,
    SURGERY_DATE        DATE NOT NULL,
    SURGEON_NAME        VARCHAR2(100),
    ANESTHESIA_TYPE     VARCHAR2(50),
    SURGERY_LEVEL       VARCHAR2(20),
    SURGERY_DURATION    NUMBER(5,0),
    SURGERY_NOTES       CLOB,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_MEDICAL_SURGERY PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_SURGERY_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID),
    CONSTRAINT CK_MEDICAL_SURGERY_LEVEL CHECK (SURGERY_LEVEL IN ('LEVEL1','LEVEL2','LEVEL3','LEVEL4'))
);

COMMENT ON TABLE MEDICAL_SURGERY IS '医疗手术信息表';
COMMENT ON COLUMN MEDICAL_SURGERY.ID IS '手术ID';
COMMENT ON COLUMN MEDICAL_SURGERY.CASE_ID IS '病例ID';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_CODE IS '手术编码';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_NAME IS '手术名称';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_DATE IS '手术日期';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGEON_NAME IS '主刀医生';
COMMENT ON COLUMN MEDICAL_SURGERY.ANESTHESIA_TYPE IS '麻醉方式';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_LEVEL IS '手术级别(LEVEL1:一级,LEVEL2:二级,LEVEL3:三级,LEVEL4:四级)';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_DURATION IS '手术时长(分钟)';
COMMENT ON COLUMN MEDICAL_SURGERY.SURGERY_NOTES IS '手术备注';

-- 4. 费用明细表
CREATE TABLE MEDICAL_COST_DETAIL (
    ID                  NUMBER(19,0) NOT NULL,
    CASE_ID             NUMBER(19,0) NOT NULL,
    ITEM_CODE           VARCHAR2(50),
    ITEM_NAME           VARCHAR2(200) NOT NULL,
    INSURANCE_ITEM_CODE VARCHAR2(50),
    INSURANCE_ITEM_NAME VARCHAR2(200),
    ITEM_TYPE           VARCHAR2(50) NOT NULL,
    UNIT_PRICE          NUMBER(10,2) NOT NULL,
    QUANTITY            NUMBER(8,2) NOT NULL,
    TOTAL_AMOUNT        NUMBER(12,2) NOT NULL,
    COMPLIANT_AMOUNT    NUMBER(12,2) DEFAULT 0,
    CHARGED_AT          TIMESTAMP NOT NULL,
    DEPARTMENT          VARCHAR2(100),
    DOCTOR_NAME         VARCHAR2(100),
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_MEDICAL_COST_DETAIL PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_COST_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID),
    CONSTRAINT CK_MEDICAL_COST_TYPE CHECK (ITEM_TYPE IN ('检查费','化验费','护理费','治疗费','卫生材料费','中成药费','手术费','其他费','中药饮片费','诊察费','西药费','床位费'))
);

COMMENT ON TABLE MEDICAL_COST_DETAIL IS '医疗费用明细表';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ID IS '费用明细ID';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.CASE_ID IS '病例ID';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ITEM_CODE IS '项目编码';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ITEM_NAME IS '项目名称';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.INSURANCE_ITEM_CODE IS '医保项目编码';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.INSURANCE_ITEM_NAME IS '医保项目名称';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.ITEM_TYPE IS '项目类型(检查费,化验费,护理费,治疗费,卫生材料费,中成药费,手术费,其他费,中药饮片费,诊察费,西药费,床位费)';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.UNIT_PRICE IS '单价';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.QUANTITY IS '数量';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.TOTAL_AMOUNT IS '总金额';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.COMPLIANT_AMOUNT IS '符合范围内金额';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.CHARGED_AT IS '收费日期时间';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.DEPARTMENT IS '收费科室';
COMMENT ON COLUMN MEDICAL_COST_DETAIL.DOCTOR_NAME IS '开单医生';

-- 5. 医疗结算表
CREATE TABLE MEDICAL_SETTLEMENT (
    ID                              NUMBER(19,0) NOT NULL,
    CASE_ID                         NUMBER(19,0) NOT NULL,
    SETTLEMENT_NUMBER               VARCHAR2(50) NOT NULL,
    TOTAL_MEDICAL_COST              NUMBER(12,2) DEFAULT 0 NOT NULL,
    FULL_SELF_PAY_AMOUNT            NUMBER(12,2) DEFAULT 0,
    OVER_LIMIT_SELF_PAY_AMOUNT      NUMBER(12,2) DEFAULT 0,
    ADVANCE_SELF_PAY_AMOUNT         NUMBER(12,2) DEFAULT 0,
    ELIGIBLE_AMOUNT                 NUMBER(12,2) DEFAULT 0,
    ACTUAL_DEDUCTIBLE               NUMBER(12,2) DEFAULT 0,
    BASIC_MEDICAL_PAY_RATIO         NUMBER(5,2) DEFAULT 0,
    TOTAL_FUND_PAYMENT              NUMBER(12,2) DEFAULT 0,
    POOLING_FUND_PAYMENT            NUMBER(12,2) DEFAULT 0,
    CIVIL_SERVANT_SUBSIDY           NUMBER(12,2) DEFAULT 0,
    SUPPLEMENTARY_INSURANCE_PAYMENT NUMBER(12,2) DEFAULT 0,
    CRITICAL_ILLNESS_INSURANCE      NUMBER(12,2) DEFAULT 0,
    LARGE_AMOUNT_SUBSIDY            NUMBER(12,2) DEFAULT 0,
    DISABILITY_MEDICAL_FUND         NUMBER(12,2) DEFAULT 0,
    MEDICAL_ASSISTANCE_FUND         NUMBER(12,2) DEFAULT 0,
    OTHER_FUND_PAYMENT              NUMBER(12,2) DEFAULT 0,
    PERSONAL_PAYMENT_AMOUNT         NUMBER(12,2) DEFAULT 0,
    PERSONAL_ACCOUNT_PAYMENT        NUMBER(12,2) DEFAULT 0,
    CASH_PAYMENT_AMOUNT             NUMBER(12,2) DEFAULT 0,
    UNION_MUTUAL_FUND               NUMBER(12,2) DEFAULT 0,
    DISABLED_VETERAN_FUND_1_6       NUMBER(12,2) DEFAULT 0,
    DISABLED_VETERAN_FUND_7_10      NUMBER(12,2) DEFAULT 0,
    POLICY_RANGE_SELF_PAY           NUMBER(12,2) DEFAULT 0,
    OUT_OF_POLICY_RANGE_AMOUNT      NUMBER(12,2) DEFAULT 0,
    IS_REFUND                       NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_VALID                        NUMBER(1,0) DEFAULT 1 NOT NULL,
    MEDIUM_TYPE                     VARCHAR2(20),
    SETTLEMENT_STAFF_CODE           VARCHAR2(50),
    SETTLEMENT_STAFF_NAME           VARCHAR2(100),
    SETTLED_AT                      TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    IS_DELETED                      NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT                      DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT                      DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY                      NUMBER(19,0),
    UPDATED_BY                      NUMBER(19,0),
    CONSTRAINT PK_MEDICAL_SETTLEMENT PRIMARY KEY (ID),
    CONSTRAINT UK_MEDICAL_SETTLEMENT_NUMBER UNIQUE (SETTLEMENT_NUMBER),
    CONSTRAINT FK_MEDICAL_SETTLEMENT_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID),
    CONSTRAINT CK_MEDICAL_SETTLEMENT_REFUND CHECK (IS_REFUND IN (0,1)),
    CONSTRAINT CK_MEDICAL_SETTLEMENT_VALID CHECK (IS_VALID IN (0,1)),
    CONSTRAINT CK_MEDICAL_SETTLEMENT_IS_DELETED CHECK (IS_DELETED IN (0,1)),
    CONSTRAINT CK_MEDICAL_SETTLEMENT_MEDIUM CHECK (MEDIUM_TYPE IN ('社保卡','医保卡','身份证','临时卡','其他'))
);

COMMENT ON TABLE MEDICAL_SETTLEMENT IS '医疗结算表';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.ID IS '结算ID';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.CASE_ID IS '病例ID';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.SETTLEMENT_NUMBER IS '结算单号';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.TOTAL_MEDICAL_COST IS '医疗费总额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.FULL_SELF_PAY_AMOUNT IS '全自费金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.OVER_LIMIT_SELF_PAY_AMOUNT IS '超限价自费费用';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.ADVANCE_SELF_PAY_AMOUNT IS '先行自付金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.ELIGIBLE_AMOUNT IS '符合范围金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.ACTUAL_DEDUCTIBLE IS '实际支付起付线';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.BASIC_MEDICAL_PAY_RATIO IS '基本医疗统筹支付比例(%)';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.TOTAL_FUND_PAYMENT IS '基金支付总额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.POOLING_FUND_PAYMENT IS '统筹基金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.CIVIL_SERVANT_SUBSIDY IS '公务员医疗补助资金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.SUPPLEMENTARY_INSURANCE_PAYMENT IS '补充医疗保险基金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.CRITICAL_ILLNESS_INSURANCE IS '大病补充医疗保险基金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.LARGE_AMOUNT_SUBSIDY IS '大额医疗补助基金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.DISABILITY_MEDICAL_FUND IS '伤残人员医疗保障基金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.MEDICAL_ASSISTANCE_FUND IS '医疗救助基金支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.OTHER_FUND_PAYMENT IS '其它基金支付';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.PERSONAL_PAYMENT_AMOUNT IS '个人支付金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.PERSONAL_ACCOUNT_PAYMENT IS '个人账户支出';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.CASH_PAYMENT_AMOUNT IS '现金支付金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.UNION_MUTUAL_FUND IS '工会互助基金';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.DISABLED_VETERAN_FUND_1_6 IS '一至六级残疾军人医疗补助基金';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.DISABLED_VETERAN_FUND_7_10 IS '七至十级残疾军人医疗补助基金';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.POLICY_RANGE_SELF_PAY IS '政策范围内自付金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.OUT_OF_POLICY_RANGE_AMOUNT IS '政策范围外金额';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.IS_REFUND IS '退费标志(0:正常,1:退费)';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.IS_VALID IS '有效标志(0:无效,1:有效)';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.MEDIUM_TYPE IS '介质类型(社保卡,医保卡,身份证,临时卡,其他)';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.SETTLEMENT_STAFF_CODE IS '结算工作人员编号';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.SETTLEMENT_STAFF_NAME IS '结算工作人员姓名';
COMMENT ON COLUMN MEDICAL_SETTLEMENT.SETTLED_AT IS '结算日期时间';

-- 6. 医疗病例分组表
CREATE TABLE MEDICAL_CASE_GROUP (
    ID                          NUMBER(19,0) NOT NULL,
    CASE_ID                     NUMBER(19,0) NOT NULL,
    GROUP_CODE                  VARCHAR2(50) NOT NULL,
    GROUP_NAME                  VARCHAR2(200) NOT NULL,
    GROUP_WEIGHT                NUMBER(8,4) DEFAULT 0,
    GROUP_RATE                  NUMBER(10,2) DEFAULT 0,
    MONTHLY_PAYMENT_STANDARD    NUMBER(12,2) DEFAULT 0,
    SETTLEMENT_PAYMENT_STANDARD NUMBER(12,2) DEFAULT 0,
    GROUP_TYPE                  VARCHAR2(20) DEFAULT 'DRG',
    IS_VALID                    NUMBER(1,0) DEFAULT 1 NOT NULL,
    GROUPED_AT                  TIMESTAMP DEFAULT SYSTIMESTAMP NOT NULL,
    IS_DELETED                  NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT                  DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT                  DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY                  NUMBER(19,0),
    UPDATED_BY                  NUMBER(19,0),
    CONSTRAINT PK_MEDICAL_CASE_GROUP PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_CASE_GROUP_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID),
    CONSTRAINT CK_MEDICAL_CASE_GROUP_TYPE CHECK (GROUP_TYPE IN ('DRG','DIP','OTHER')),
    CONSTRAINT CK_MEDICAL_CASE_GROUP_VALID CHECK (IS_VALID IN (0,1)),
    CONSTRAINT CK_MEDICAL_CASE_GROUP_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE MEDICAL_CASE_GROUP IS '医疗病例分组表';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.ID IS '分组记录ID';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.CASE_ID IS '病例ID';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_CODE IS '分组编码';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_NAME IS '分组名称';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_WEIGHT IS '权重';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_RATE IS '费率';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.MONTHLY_PAYMENT_STANDARD IS '月结支付标准';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.SETTLEMENT_PAYMENT_STANDARD IS '清算支付标准';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUP_TYPE IS '分组类型(DRG:按疾病诊断相关分组,DIP:按病种分值付费,OTHER:其他)';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.IS_VALID IS '是否有效(0:无效,1:有效)';
COMMENT ON COLUMN MEDICAL_CASE_GROUP.GROUPED_AT IS '分组日期时间';

-- 创建序列和触发器
CREATE SEQUENCE SEQ_MEDICAL_CASE START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_MEDICAL_DIAGNOSIS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_MEDICAL_SURGERY START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_MEDICAL_COST_DETAIL START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_MEDICAL_SETTLEMENT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_MEDICAL_CASE_GROUP START WITH 1 INCREMENT BY 1;

-- 医疗病例主表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_CASE_ID
    BEFORE INSERT ON MEDICAL_CASE
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_MEDICAL_CASE.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 诊断信息表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_DIAGNOSIS_ID
    BEFORE INSERT ON MEDICAL_DIAGNOSIS
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_MEDICAL_DIAGNOSIS.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 手术信息表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_SURGERY_ID
    BEFORE INSERT ON MEDICAL_SURGERY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_MEDICAL_SURGERY.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 费用明细表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_COST_DETAIL_ID
    BEFORE INSERT ON MEDICAL_COST_DETAIL
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_MEDICAL_COST_DETAIL.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 医疗结算表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_SETTLEMENT_ID
    BEFORE INSERT ON MEDICAL_SETTLEMENT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_MEDICAL_SETTLEMENT.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 医疗病例分组表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_CASE_GROUP_ID
    BEFORE INSERT ON MEDICAL_CASE_GROUP
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_MEDICAL_CASE_GROUP.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 创建索引
-- 注意: CASE_NUMBER 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_MEDICAL_CASE_PATIENT ON MEDICAL_CASE(PATIENT_ID_CARD);
CREATE INDEX IDX_MEDICAL_CASE_TYPE ON MEDICAL_CASE(CASE_TYPE);
CREATE INDEX IDX_MEDICAL_CATEGORY ON MEDICAL_CASE(MEDICAL_CATEGORY);
CREATE INDEX IDX_MEDICAL_CASE_HOSPITAL ON MEDICAL_CASE(HOSPITAL_CODE);
CREATE INDEX IDX_MEDICAL_CASE_DATE ON MEDICAL_CASE(ADMISSION_DATE, DISCHARGE_DATE);
CREATE INDEX IDX_MEDICAL_CASE_CREATED_AT ON MEDICAL_CASE(CREATED_AT);

CREATE INDEX IDX_MEDICAL_DIAGNOSIS_CASE ON MEDICAL_DIAGNOSIS(CASE_ID);
CREATE INDEX IDX_MEDICAL_DIAGNOSIS_TYPE ON MEDICAL_DIAGNOSIS(DIAGNOSIS_TYPE);
CREATE INDEX IDX_MEDICAL_DIAGNOSIS_CODE ON MEDICAL_DIAGNOSIS(DIAGNOSIS_CODE);
CREATE INDEX IDX_MEDICAL_DIAGNOSIS_PRIMARY ON MEDICAL_DIAGNOSIS(IS_PRIMARY);

CREATE INDEX IDX_MEDICAL_SURGERY_CASE ON MEDICAL_SURGERY(CASE_ID);
CREATE INDEX IDX_MEDICAL_SURGERY_DATE ON MEDICAL_SURGERY(SURGERY_DATE);
CREATE INDEX IDX_MEDICAL_SURGERY_CODE ON MEDICAL_SURGERY(SURGERY_CODE);
CREATE INDEX IDX_MEDICAL_SURGERY_LEVEL ON MEDICAL_SURGERY(SURGERY_LEVEL);

CREATE INDEX IDX_MEDICAL_COST_CASE ON MEDICAL_COST_DETAIL(CASE_ID);
CREATE INDEX IDX_MEDICAL_COST_TYPE ON MEDICAL_COST_DETAIL(ITEM_TYPE);
CREATE INDEX IDX_MEDICAL_COST_DATE ON MEDICAL_COST_DETAIL(CHARGED_AT);
CREATE INDEX IDX_MEDICAL_COST_CODE ON MEDICAL_COST_DETAIL(ITEM_CODE);
CREATE INDEX IDX_MEDICAL_COST_INSURANCE_CODE ON MEDICAL_COST_DETAIL(INSURANCE_ITEM_CODE);

CREATE INDEX IDX_MEDICAL_SETTLEMENT_CASE ON MEDICAL_SETTLEMENT(CASE_ID);
-- 注意: SETTLEMENT_NUMBER 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_MEDICAL_SETTLEMENT_DATE ON MEDICAL_SETTLEMENT(SETTLED_AT);
CREATE INDEX IDX_MEDICAL_SETTLEMENT_VALID ON MEDICAL_SETTLEMENT(IS_VALID);
CREATE INDEX IDX_MEDICAL_SETTLEMENT_REFUND ON MEDICAL_SETTLEMENT(IS_REFUND);
CREATE INDEX IDX_MEDICAL_SETTLEMENT_STAFF ON MEDICAL_SETTLEMENT(SETTLEMENT_STAFF_CODE);

CREATE INDEX IDX_MEDICAL_CASE_GROUP_CASE ON MEDICAL_CASE_GROUP(CASE_ID);
CREATE INDEX IDX_MEDICAL_CASE_GROUP_CODE ON MEDICAL_CASE_GROUP(GROUP_CODE);
CREATE INDEX IDX_MEDICAL_CASE_GROUP_TYPE ON MEDICAL_CASE_GROUP(GROUP_TYPE);
CREATE INDEX IDX_MEDICAL_CASE_GROUP_VALID ON MEDICAL_CASE_GROUP(IS_VALID);
CREATE INDEX IDX_MEDICAL_CASE_GROUP_DATE ON MEDICAL_CASE_GROUP(GROUPED_AT);