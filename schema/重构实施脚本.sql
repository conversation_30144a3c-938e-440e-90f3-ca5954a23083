-- =====================================================
-- 医保基金监管平台数据库重构实施脚本
-- 版本：2.0
-- 创建时间：2024年
-- 说明：基于企业级架构重构所有核心表
-- =====================================================

-- 设置环境
SET SERVEROUTPUT ON;
ALTER SESSION SET NLS_DATE_FORMAT = 'YYYY-MM-DD HH24:MI:SS';
ALTER SESSION SET NLS_TIMESTAMP_FORMAT = 'YYYY-MM-DD HH24:MI:SS.FF6';

-- =====================================================
-- 1. 创建序列
-- =====================================================

-- 用户管理模块序列
CREATE SEQUENCE SEQ_USER_ROLE_INFO START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_USER_INFO START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_USER_ROLE_MAPPING START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_USER_LOGIN_AUDIT START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_USER_PASSWORD_HISTORY START WITH 1 INCREMENT BY 1 NOCACHE;

-- 医疗记录模块序列
CREATE SEQUENCE SEQ_PATIENT_INFO START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_MEDICAL_RECORD START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_MEDICAL_EXPENSE START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_MEDICAL_AUDIT_RECORD START WITH 1 INCREMENT BY 1 NOCACHE;

-- 监管规则模块序列
CREATE SEQUENCE SEQ_SUPERVISION_RULE START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SUPERVISION_EXECUTION_LOG START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SUPERVISION_EXECUTION_RESULT START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SUPERVISION_AUDIT_RECORD START WITH 1 INCREMENT BY 1 NOCACHE;

-- 知识库模块序列
CREATE SEQUENCE SEQ_KNOWLEDGE_CATEGORY START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_KNOWLEDGE_DOCUMENT START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_KNOWLEDGE_ACCESS_LOG START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_KNOWLEDGE_COMMENT START WITH 1 INCREMENT BY 1 NOCACHE;

-- 系统管理模块序列
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_CATEGORY START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_ITEM START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_CHANGE_HISTORY START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SYSTEM_OPERATION_LOG START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SYSTEM_PERFORMANCE_LOG START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE SEQ_SYSTEM_ERROR_LOG START WITH 1 INCREMENT BY 1 NOCACHE;

-- =====================================================
-- 2. 用户管理模块表
-- =====================================================

-- 角色信息表
CREATE TABLE USER_ROLE_INFO (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    ROLE_CODE           VARCHAR2(50) NOT NULL,
    ROLE_NAME           VARCHAR2(200) NOT NULL,
    ROLE_DESCRIPTION    VARCHAR2(1000),
    ROLE_TYPE           VARCHAR2(50) DEFAULT 'BUSINESS' NOT NULL,
    ROLE_LEVEL          NUMBER(3,0) DEFAULT 1,
    PARENT_ROLE_ID      NUMBER(19,0),
    PERMISSIONS         CLOB CHECK (JSON_VALID(PERMISSIONS) = 1),
    IS_SYSTEM_ROLE      NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    SORT_ORDER          NUMBER(10,0) DEFAULT 0,
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_USER_ROLE_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_USER_ROLE_INFO_CODE UNIQUE (TENANT_ID, ROLE_CODE, IS_DELETED),
    CONSTRAINT CK_USER_ROLE_INFO_TYPE CHECK (ROLE_TYPE IN ('SYSTEM', 'BUSINESS', 'FUNCTIONAL')),
    CONSTRAINT CK_USER_ROLE_INFO_DATA_CLASS CHECK (DATA_CLASSIFICATION IN ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED')),
    CONSTRAINT CK_USER_ROLE_INFO_IS_SYSTEM CHECK (IS_SYSTEM_ROLE IN (0, 1)),
    CONSTRAINT CK_USER_ROLE_INFO_IS_ACTIVE CHECK (IS_ACTIVE IN (0, 1)),
    CONSTRAINT CK_USER_ROLE_INFO_IS_DELETED CHECK (IS_DELETED IN (0, 1))
);

-- 用户信息表
CREATE TABLE USER_INFO (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    USERNAME            VARCHAR2(100) NOT NULL,
    PASSWORD_HASH       VARCHAR2(256) NOT NULL,
    SALT                VARCHAR2(64) NOT NULL,
    REAL_NAME_ENCRYPTED RAW(256),
    EMAIL_ENCRYPTED     RAW(256),
    PHONE_ENCRYPTED     RAW(256),
    ID_CARD_ENCRYPTED   RAW(256),
    DEPARTMENT          VARCHAR2(200),
    POSITION            VARCHAR2(200),
    USER_TYPE           VARCHAR2(50) DEFAULT 'REGULAR' NOT NULL,
    USER_STATUS         VARCHAR2(50) DEFAULT 'ACTIVE' NOT NULL,
    LAST_LOGIN_TIME     TIMESTAMP(6),
    LAST_LOGIN_IP       VARCHAR2(50),
    LOGIN_FAILURE_COUNT NUMBER(3,0) DEFAULT 0,
    ACCOUNT_LOCKED_UNTIL TIMESTAMP(6),
    PASSWORD_EXPIRE_DATE TIMESTAMP(6),
    MUST_CHANGE_PASSWORD NUMBER(1,0) DEFAULT 0 NOT NULL,
    MFA_ENABLED         NUMBER(1,0) DEFAULT 0 NOT NULL,
    MFA_SECRET          RAW(256),
    PROFILE_SETTINGS    CLOB CHECK (JSON_VALID(PROFILE_SETTINGS) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_USER_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_USER_INFO_USERNAME UNIQUE (TENANT_ID, USERNAME, IS_DELETED),
    CONSTRAINT CK_USER_INFO_USER_TYPE CHECK (USER_TYPE IN ('ADMIN', 'REGULAR', 'SYSTEM', 'GUEST')),
    CONSTRAINT CK_USER_INFO_USER_STATUS CHECK (USER_STATUS IN ('ACTIVE', 'INACTIVE', 'LOCKED', 'EXPIRED')),
    CONSTRAINT CK_USER_INFO_DATA_CLASS CHECK (DATA_CLASSIFICATION IN ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED')),
    CONSTRAINT CK_USER_INFO_MFA_ENABLED CHECK (MFA_ENABLED IN (0, 1)),
    CONSTRAINT CK_USER_INFO_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 用户角色关联表
CREATE TABLE USER_ROLE_MAPPING (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    USER_ID             NUMBER(19,0) NOT NULL,
    ROLE_ID             NUMBER(19,0) NOT NULL,
    ASSIGNED_BY         NUMBER(19,0),
    ASSIGNED_AT         TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    EFFECTIVE_DATE      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    EXPIRY_DATE         TIMESTAMP(6),
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    ASSIGNMENT_REASON   VARCHAR2(1000),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_USER_ROLE_MAPPING PRIMARY KEY (ID),
    CONSTRAINT UK_USER_ROLE_MAPPING UNIQUE (TENANT_ID, USER_ID, ROLE_ID, IS_DELETED),
    CONSTRAINT FK_USER_ROLE_MAPPING_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_USER_ROLE_MAPPING_ROLE FOREIGN KEY (ROLE_ID) REFERENCES USER_ROLE_INFO(ID),
    CONSTRAINT CK_USER_ROLE_MAPPING_IS_ACTIVE CHECK (IS_ACTIVE IN (0, 1)),
    CONSTRAINT CK_USER_ROLE_MAPPING_IS_DELETED CHECK (IS_DELETED IN (0, 1))
);

-- 用户登录审计表
CREATE TABLE USER_LOGIN_AUDIT (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    USER_ID             NUMBER(19,0),
    USERNAME            VARCHAR2(100),
    LOGIN_TIME          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    LOGOUT_TIME         TIMESTAMP(6),
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(1000),
    LOGIN_METHOD        VARCHAR2(50) DEFAULT 'PASSWORD' NOT NULL,
    LOGIN_STATUS        VARCHAR2(50) NOT NULL,
    FAILURE_REASON      VARCHAR2(500),
    SESSION_ID          VARCHAR2(100),
    DEVICE_INFO         CLOB CHECK (JSON_VALID(DEVICE_INFO) = 1),
    LOCATION_INFO       CLOB CHECK (JSON_VALID(LOCATION_INFO) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CONSTRAINT PK_USER_LOGIN_AUDIT PRIMARY KEY (ID),
    CONSTRAINT FK_USER_LOGIN_AUDIT_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_USER_LOGIN_AUDIT_METHOD CHECK (LOGIN_METHOD IN ('PASSWORD', 'MFA', 'SSO', 'API_KEY')),
    CONSTRAINT CK_USER_LOGIN_AUDIT_STATUS CHECK (LOGIN_STATUS IN ('SUCCESS', 'FAILURE', 'LOCKED', 'EXPIRED'))
)
PARTITION BY RANGE (LOGIN_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 用户密码历史表
CREATE TABLE USER_PASSWORD_HISTORY (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    USER_ID             NUMBER(19,0) NOT NULL,
    PASSWORD_HASH       VARCHAR2(256) NOT NULL,
    SALT                VARCHAR2(64) NOT NULL,
    CHANGE_TIME         TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CHANGE_REASON       VARCHAR2(200),
    CHANGED_BY          NUMBER(19,0),
    IS_CURRENT          NUMBER(1,0) DEFAULT 0 NOT NULL,
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'RESTRICTED' NOT NULL,
    CONSTRAINT PK_USER_PASSWORD_HISTORY PRIMARY KEY (ID),
    CONSTRAINT FK_USER_PASSWORD_HISTORY_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_USER_PASSWORD_HISTORY_IS_CURRENT CHECK (IS_CURRENT IN (0, 1))
)
PARTITION BY RANGE (CHANGE_TIME) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- =====================================================
-- 3. 医疗记录管理模块表
-- =====================================================

-- 患者信息表
CREATE TABLE PATIENT_INFO (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    PATIENT_NO          VARCHAR2(50) NOT NULL,
    NAME_ENCRYPTED      RAW(256) NOT NULL,
    ID_CARD_ENCRYPTED   RAW(256) NOT NULL,
    PHONE_ENCRYPTED     RAW(256),
    GENDER              NUMBER(1,0),
    BIRTH_DATE          DATE,
    ADDRESS_ENCRYPTED   RAW(512),
    INSURANCE_TYPE      VARCHAR2(50),
    INSURANCE_NO_ENCRYPTED RAW(256),
    EMERGENCY_CONTACT_ENCRYPTED RAW(512),
    MEDICAL_ALLERGIES   CLOB,
    CHRONIC_DISEASES    CLOB CHECK (JSON_VALID(CHRONIC_DISEASES) = 1),
    PATIENT_STATUS      VARCHAR2(50) DEFAULT 'ACTIVE' NOT NULL,
    RISK_LEVEL          VARCHAR2(20) DEFAULT 'LOW',
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'RESTRICTED' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_PATIENT_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_PATIENT_INFO_NO UNIQUE (TENANT_ID, PATIENT_NO, IS_DELETED),
    CONSTRAINT CK_PATIENT_INFO_GENDER CHECK (GENDER IN (0, 1, 2)), -- 0:未知, 1:男, 2:女
    CONSTRAINT CK_PATIENT_INFO_STATUS CHECK (PATIENT_STATUS IN ('ACTIVE', 'INACTIVE', 'DECEASED')),
    CONSTRAINT CK_PATIENT_INFO_RISK_LEVEL CHECK (RISK_LEVEL IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    CONSTRAINT CK_PATIENT_INFO_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 医疗记录表
CREATE TABLE MEDICAL_RECORD (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    PATIENT_ID          NUMBER(19,0) NOT NULL,
    VISIT_NO            VARCHAR2(50) NOT NULL,
    HOSPITAL_CODE       VARCHAR2(50) NOT NULL,
    HOSPITAL_NAME       VARCHAR2(200) NOT NULL,
    DEPARTMENT_CODE     VARCHAR2(50),
    DEPARTMENT_NAME     VARCHAR2(200),
    DOCTOR_CODE         VARCHAR2(50),
    DOCTOR_NAME_ENCRYPTED RAW(256),
    VISIT_TYPE          VARCHAR2(50) NOT NULL,
    VISIT_DATE          TIMESTAMP(6) NOT NULL,
    DIAGNOSIS_CODE      VARCHAR2(20),
    DIAGNOSIS_NAME      VARCHAR2(500),
    DIAGNOSIS_TYPE      VARCHAR2(50),
    TREATMENT_PLAN      CLOB,
    PRESCRIPTION_DETAILS CLOB CHECK (JSON_VALID(PRESCRIPTION_DETAILS) = 1),
    EXAMINATION_RESULTS CLOB CHECK (JSON_VALID(EXAMINATION_RESULTS) = 1),
    RECORD_STATUS       VARCHAR2(50) DEFAULT 'DRAFT' NOT NULL,
    TOTAL_COST          NUMBER(15,2) DEFAULT 0,
    INSURANCE_COVERAGE  NUMBER(15,2) DEFAULT 0,
    PATIENT_PAYMENT     NUMBER(15,2) DEFAULT 0,
    SETTLEMENT_STATUS   VARCHAR2(50) DEFAULT 'PENDING',
    RISK_INDICATORS     CLOB CHECK (JSON_VALID(RISK_INDICATORS) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'RESTRICTED' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_MEDICAL_RECORD PRIMARY KEY (ID),
    CONSTRAINT UK_MEDICAL_RECORD_VISIT UNIQUE (TENANT_ID, VISIT_NO, IS_DELETED),
    CONSTRAINT FK_MEDICAL_RECORD_PATIENT FOREIGN KEY (PATIENT_ID) REFERENCES PATIENT_INFO(ID),
    CONSTRAINT CK_MEDICAL_RECORD_VISIT_TYPE CHECK (VISIT_TYPE IN ('OUTPATIENT', 'INPATIENT', 'EMERGENCY', 'PHYSICAL_EXAM')),
    CONSTRAINT CK_MEDICAL_RECORD_STATUS CHECK (RECORD_STATUS IN ('DRAFT', 'SUBMITTED', 'REVIEWED', 'APPROVED', 'REJECTED')),
    CONSTRAINT CK_MEDICAL_RECORD_SETTLEMENT CHECK (SETTLEMENT_STATUS IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED')),
    CONSTRAINT CK_MEDICAL_RECORD_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (VISIT_DATE) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 医疗费用明细表
CREATE TABLE MEDICAL_EXPENSE (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    MEDICAL_RECORD_ID   NUMBER(19,0) NOT NULL,
    EXPENSE_TYPE        VARCHAR2(50) NOT NULL,
    EXPENSE_CODE        VARCHAR2(50),
    EXPENSE_NAME        VARCHAR2(500) NOT NULL,
    SPECIFICATION       VARCHAR2(200),
    UNIT                VARCHAR2(50),
    QUANTITY            NUMBER(15,4) DEFAULT 1,
    UNIT_PRICE          NUMBER(15,4) NOT NULL,
    TOTAL_AMOUNT        NUMBER(15,2) NOT NULL,
    DISCOUNT_AMOUNT     NUMBER(15,2) DEFAULT 0,
    ACTUAL_AMOUNT       NUMBER(15,2) NOT NULL,
    INSURANCE_RATIO     NUMBER(5,4) DEFAULT 0,
    INSURANCE_AMOUNT    NUMBER(15,2) DEFAULT 0,
    PATIENT_AMOUNT      NUMBER(15,2) NOT NULL,
    EXPENSE_DATE        TIMESTAMP(6) NOT NULL,
    PROVIDER_CODE       VARCHAR2(50),
    PROVIDER_NAME       VARCHAR2(200),
    APPROVAL_STATUS     VARCHAR2(50) DEFAULT 'PENDING',
    RISK_SCORE          NUMBER(5,2) DEFAULT 0,
    RISK_REASONS        CLOB CHECK (JSON_VALID(RISK_REASONS) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_MEDICAL_EXPENSE PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_EXPENSE_RECORD FOREIGN KEY (MEDICAL_RECORD_ID) REFERENCES MEDICAL_RECORD(ID),
    CONSTRAINT CK_MEDICAL_EXPENSE_TYPE CHECK (EXPENSE_TYPE IN ('DRUG', 'EXAMINATION', 'TREATMENT', 'SURGERY', 'MATERIAL', 'BED', 'OTHER')),
    CONSTRAINT CK_MEDICAL_EXPENSE_APPROVAL CHECK (APPROVAL_STATUS IN ('PENDING', 'APPROVED', 'REJECTED', 'REVIEWING')),
    CONSTRAINT CK_MEDICAL_EXPENSE_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (EXPENSE_DATE) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 医疗审核记录表
CREATE TABLE MEDICAL_AUDIT_RECORD (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    MEDICAL_RECORD_ID   NUMBER(19,0) NOT NULL,
    AUDIT_TYPE          VARCHAR2(50) NOT NULL,
    AUDIT_LEVEL         VARCHAR2(20) DEFAULT 'NORMAL' NOT NULL,
    AUDITOR_ID          NUMBER(19,0),
    AUDIT_TIME          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    AUDIT_STATUS        VARCHAR2(50) NOT NULL,
    AUDIT_RESULT        VARCHAR2(50),
    AUDIT_SCORE         NUMBER(5,2),
    RISK_LEVEL          VARCHAR2(20),
    VIOLATION_ITEMS     CLOB CHECK (JSON_VALID(VIOLATION_ITEMS) = 1),
    AUDIT_COMMENTS      CLOB,
    RECOMMENDED_ACTIONS CLOB CHECK (JSON_VALID(RECOMMENDED_ACTIONS) = 1),
    FOLLOW_UP_REQUIRED  NUMBER(1,0) DEFAULT 0 NOT NULL,
    FOLLOW_UP_DEADLINE  TIMESTAMP(6),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_MEDICAL_AUDIT_RECORD PRIMARY KEY (ID),
    CONSTRAINT FK_MEDICAL_AUDIT_RECORD FOREIGN KEY (MEDICAL_RECORD_ID) REFERENCES MEDICAL_RECORD(ID),
    CONSTRAINT FK_MEDICAL_AUDIT_AUDITOR FOREIGN KEY (AUDITOR_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_MEDICAL_AUDIT_TYPE CHECK (AUDIT_TYPE IN ('MANUAL', 'AUTOMATIC', 'RANDOM', 'TARGETED')),
    CONSTRAINT CK_MEDICAL_AUDIT_LEVEL CHECK (AUDIT_LEVEL IN ('NORMAL', 'PRIORITY', 'URGENT', 'CRITICAL')),
    CONSTRAINT CK_MEDICAL_AUDIT_STATUS CHECK (AUDIT_STATUS IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'SUSPENDED')),
    CONSTRAINT CK_MEDICAL_AUDIT_RESULT CHECK (AUDIT_RESULT IN ('PASS', 'WARNING', 'VIOLATION', 'FRAUD')),
    CONSTRAINT CK_MEDICAL_AUDIT_RISK CHECK (RISK_LEVEL IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    CONSTRAINT CK_MEDICAL_AUDIT_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (AUDIT_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- =====================================================
-- 4. 监管规则管理模块表
-- =====================================================

-- 监管规则表
CREATE TABLE SUPERVISION_RULE (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    RULE_CODE           VARCHAR2(50) NOT NULL,
    RULE_NAME           VARCHAR2(200) NOT NULL,
    RULE_DESCRIPTION    VARCHAR2(1000),
    RULE_TYPE           VARCHAR2(50) NOT NULL,
    RULE_CATEGORY       VARCHAR2(50) NOT NULL,
    RULE_CONTENT        CLOB CHECK (JSON_VALID(RULE_CONTENT) = 1) NOT NULL,
    RULE_EXPRESSION     CLOB NOT NULL,
    RULE_PARAMETERS     CLOB CHECK (JSON_VALID(RULE_PARAMETERS) = 1),
    SEVERITY_LEVEL      VARCHAR2(20) DEFAULT 'MEDIUM' NOT NULL,
    RISK_SCORE          NUMBER(5,2) DEFAULT 0,
    EXECUTION_ORDER     NUMBER(10,0) DEFAULT 0,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    EFFECTIVE_DATE      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    EXPIRY_DATE         TIMESTAMP(6),
    EXECUTION_FREQUENCY VARCHAR2(50) DEFAULT 'REAL_TIME',
    TARGET_SCOPE        CLOB CHECK (JSON_VALID(TARGET_SCOPE) = 1),
    APPROVAL_STATUS     VARCHAR2(50) DEFAULT 'DRAFT' NOT NULL,
    APPROVED_BY         NUMBER(19,0),
    APPROVED_AT         TIMESTAMP(6),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SUPERVISION_RULE PRIMARY KEY (ID),
    CONSTRAINT UK_SUPERVISION_RULE_CODE UNIQUE (TENANT_ID, RULE_CODE, IS_DELETED),
    CONSTRAINT FK_SUPERVISION_RULE_APPROVER FOREIGN KEY (APPROVED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SUPERVISION_RULE_TYPE CHECK (RULE_TYPE IN ('THRESHOLD', 'PATTERN', 'STATISTICAL', 'LOGICAL', 'ML_BASED')),
    CONSTRAINT CK_SUPERVISION_RULE_CATEGORY CHECK (RULE_CATEGORY IN ('COST_CONTROL', 'FRAUD_DETECTION', 'COMPLIANCE', 'QUALITY', 'SAFETY')),
    CONSTRAINT CK_SUPERVISION_RULE_SEVERITY CHECK (SEVERITY_LEVEL IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    CONSTRAINT CK_SUPERVISION_RULE_FREQUENCY CHECK (EXECUTION_FREQUENCY IN ('REAL_TIME', 'HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY')),
    CONSTRAINT CK_SUPERVISION_RULE_APPROVAL CHECK (APPROVAL_STATUS IN ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED')),
    CONSTRAINT CK_SUPERVISION_RULE_IS_ACTIVE CHECK (IS_ACTIVE IN (0, 1)),
    CONSTRAINT CK_SUPERVISION_RULE_IS_DELETED CHECK (IS_DELETED IN (0, 1))
);

-- 监管执行记录表
CREATE TABLE SUPERVISION_EXECUTION_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    RULE_ID             NUMBER(19,0) NOT NULL,
    EXECUTION_ID        VARCHAR2(100) NOT NULL,
    EXECUTION_TYPE      VARCHAR2(50) NOT NULL,
    TRIGGER_SOURCE      VARCHAR2(100),
    EXECUTION_START_TIME TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    EXECUTION_END_TIME  TIMESTAMP(6),
    EXECUTION_STATUS    VARCHAR2(50) NOT NULL,
    EXECUTION_RESULT    VARCHAR2(50),
    PROCESSED_RECORDS   NUMBER(19,0) DEFAULT 0,
    MATCHED_RECORDS     NUMBER(19,0) DEFAULT 0,
    VIOLATION_RECORDS   NUMBER(19,0) DEFAULT 0,
    EXECUTION_DURATION  NUMBER(19,0), -- 毫秒
    ERROR_MESSAGE       CLOB,
    EXECUTION_DETAILS   CLOB CHECK (JSON_VALID(EXECUTION_DETAILS) = 1),
    PERFORMANCE_METRICS CLOB CHECK (JSON_VALID(PERFORMANCE_METRICS) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SUPERVISION_EXECUTION_LOG PRIMARY KEY (ID),
    CONSTRAINT UK_SUPERVISION_EXECUTION_ID UNIQUE (EXECUTION_ID),
    CONSTRAINT FK_SUPERVISION_EXECUTION_RULE FOREIGN KEY (RULE_ID) REFERENCES SUPERVISION_RULE(ID),
    CONSTRAINT CK_SUPERVISION_EXECUTION_TYPE CHECK (EXECUTION_TYPE IN ('SCHEDULED', 'MANUAL', 'TRIGGERED', 'BATCH')),
    CONSTRAINT CK_SUPERVISION_EXECUTION_STATUS CHECK (EXECUTION_STATUS IN ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED')),
    CONSTRAINT CK_SUPERVISION_EXECUTION_RESULT CHECK (EXECUTION_RESULT IN ('SUCCESS', 'PARTIAL_SUCCESS', 'FAILURE', 'NO_DATA')),
    CONSTRAINT CK_SUPERVISION_EXECUTION_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (EXECUTION_START_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 监管执行结果表
CREATE TABLE SUPERVISION_EXECUTION_RESULT (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    EXECUTION_LOG_ID    NUMBER(19,0) NOT NULL,
    RULE_ID             NUMBER(19,0) NOT NULL,
    TARGET_RECORD_ID    NUMBER(19,0) NOT NULL,
    TARGET_RECORD_TYPE  VARCHAR2(50) NOT NULL,
    VIOLATION_TYPE      VARCHAR2(50) NOT NULL,
    VIOLATION_LEVEL     VARCHAR2(20) NOT NULL,
    RISK_SCORE          NUMBER(5,2) DEFAULT 0,
    VIOLATION_DETAILS   CLOB CHECK (JSON_VALID(VIOLATION_DETAILS) = 1) NOT NULL,
    EVIDENCE_DATA       CLOB CHECK (JSON_VALID(EVIDENCE_DATA) = 1),
    RECOMMENDED_ACTIONS CLOB CHECK (JSON_VALID(RECOMMENDED_ACTIONS) = 1),
    FINANCIAL_IMPACT    NUMBER(15,2),
    RESULT_STATUS       VARCHAR2(50) DEFAULT 'PENDING' NOT NULL,
    HANDLING_STATUS     VARCHAR2(50) DEFAULT 'UNHANDLED' NOT NULL,
    HANDLER_ID          NUMBER(19,0),
    HANDLED_AT          TIMESTAMP(6),
    HANDLING_COMMENTS   CLOB,
    FOLLOW_UP_REQUIRED  NUMBER(1,0) DEFAULT 0 NOT NULL,
    FOLLOW_UP_DEADLINE  TIMESTAMP(6),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SUPERVISION_EXECUTION_RESULT PRIMARY KEY (ID),
    CONSTRAINT FK_SUPERVISION_RESULT_LOG FOREIGN KEY (EXECUTION_LOG_ID) REFERENCES SUPERVISION_EXECUTION_LOG(ID),
    CONSTRAINT FK_SUPERVISION_RESULT_RULE FOREIGN KEY (RULE_ID) REFERENCES SUPERVISION_RULE(ID),
    CONSTRAINT FK_SUPERVISION_RESULT_HANDLER FOREIGN KEY (HANDLER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SUPERVISION_RESULT_TARGET_TYPE CHECK (TARGET_RECORD_TYPE IN ('MEDICAL_RECORD', 'MEDICAL_EXPENSE', 'PATIENT_INFO', 'HOSPITAL', 'DOCTOR')),
    CONSTRAINT CK_SUPERVISION_RESULT_VIOLATION_TYPE CHECK (VIOLATION_TYPE IN ('OVERCHARGE', 'DUPLICATE', 'INAPPROPRIATE', 'FRAUD', 'COMPLIANCE')),
    CONSTRAINT CK_SUPERVISION_RESULT_VIOLATION_LEVEL CHECK (VIOLATION_LEVEL IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    CONSTRAINT CK_SUPERVISION_RESULT_STATUS CHECK (RESULT_STATUS IN ('PENDING', 'CONFIRMED', 'FALSE_POSITIVE', 'DISMISSED')),
    CONSTRAINT CK_SUPERVISION_RESULT_HANDLING CHECK (HANDLING_STATUS IN ('UNHANDLED', 'IN_PROGRESS', 'RESOLVED', 'ESCALATED')),
    CONSTRAINT CK_SUPERVISION_RESULT_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 监管审核记录表
CREATE TABLE SUPERVISION_AUDIT_RECORD (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    EXECUTION_RESULT_ID NUMBER(19,0) NOT NULL,
    AUDIT_TYPE          VARCHAR2(50) NOT NULL,
    AUDITOR_ID          NUMBER(19,0) NOT NULL,
    AUDIT_TIME          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    AUDIT_STATUS        VARCHAR2(50) NOT NULL,
    AUDIT_RESULT        VARCHAR2(50) NOT NULL,
    AUDIT_SCORE         NUMBER(5,2),
    AUDIT_COMMENTS      CLOB,
    CORRECTIVE_ACTIONS  CLOB CHECK (JSON_VALID(CORRECTIVE_ACTIONS) = 1),
    APPROVAL_LEVEL      VARCHAR2(20),
    NEXT_APPROVER_ID    NUMBER(19,0),
    APPROVAL_DEADLINE   TIMESTAMP(6),
    ESCALATION_REQUIRED NUMBER(1,0) DEFAULT 0 NOT NULL,
    ESCALATION_REASON   VARCHAR2(1000),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'CONFIDENTIAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SUPERVISION_AUDIT_RECORD PRIMARY KEY (ID),
    CONSTRAINT FK_SUPERVISION_AUDIT_RESULT FOREIGN KEY (EXECUTION_RESULT_ID) REFERENCES SUPERVISION_EXECUTION_RESULT(ID),
    CONSTRAINT FK_SUPERVISION_AUDIT_AUDITOR FOREIGN KEY (AUDITOR_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_SUPERVISION_AUDIT_NEXT_APPROVER FOREIGN KEY (NEXT_APPROVER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SUPERVISION_AUDIT_TYPE CHECK (AUDIT_TYPE IN ('FIRST_LEVEL', 'SECOND_LEVEL', 'FINAL', 'APPEAL')),
    CONSTRAINT CK_SUPERVISION_AUDIT_STATUS CHECK (AUDIT_STATUS IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'SUSPENDED')),
    CONSTRAINT CK_SUPERVISION_AUDIT_RESULT CHECK (AUDIT_RESULT IN ('APPROVED', 'REJECTED', 'REQUIRES_MODIFICATION', 'ESCALATED')),
    CONSTRAINT CK_SUPERVISION_AUDIT_LEVEL CHECK (APPROVAL_LEVEL IN ('L1', 'L2', 'L3', 'EXECUTIVE')),
    CONSTRAINT CK_SUPERVISION_AUDIT_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (AUDIT_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- =====================================================
-- 5. 知识库管理模块表
-- =====================================================

-- 知识库分类表
CREATE TABLE KNOWLEDGE_CATEGORY (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    CATEGORY_CODE       VARCHAR2(50) NOT NULL,
    CATEGORY_NAME       VARCHAR2(200) NOT NULL,
    CATEGORY_DESCRIPTION VARCHAR2(1000),
    PARENT_CATEGORY_ID  NUMBER(19,0),
    CATEGORY_LEVEL      NUMBER(3,0) DEFAULT 1,
    CATEGORY_PATH       VARCHAR2(1000),
    SORT_ORDER          NUMBER(10,0) DEFAULT 0,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    ACCESS_LEVEL        VARCHAR2(50) DEFAULT 'PUBLIC' NOT NULL,
    ALLOWED_ROLES       CLOB CHECK (JSON_VALID(ALLOWED_ROLES) = 1),
    CATEGORY_ICON       VARCHAR2(200),
    CATEGORY_COLOR      VARCHAR2(20),
    METADATA            CLOB CHECK (JSON_VALID(METADATA) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_KNOWLEDGE_CATEGORY PRIMARY KEY (ID),
    CONSTRAINT UK_KNOWLEDGE_CATEGORY_CODE UNIQUE (TENANT_ID, CATEGORY_CODE, IS_DELETED),
    CONSTRAINT FK_KNOWLEDGE_CATEGORY_PARENT FOREIGN KEY (PARENT_CATEGORY_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_ACCESS CHECK (ACCESS_LEVEL IN ('PUBLIC', 'INTERNAL', 'RESTRICTED', 'PRIVATE')),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_IS_ACTIVE CHECK (IS_ACTIVE IN (0, 1)),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_IS_DELETED CHECK (IS_DELETED IN (0, 1))
);

-- 知识库文档表
CREATE TABLE KNOWLEDGE_DOCUMENT (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    CATEGORY_ID         NUMBER(19,0) NOT NULL,
    DOCUMENT_CODE       VARCHAR2(50),
    TITLE               VARCHAR2(500) NOT NULL,
    SUBTITLE            VARCHAR2(500),
    CONTENT             CLOB NOT NULL,
    CONTENT_TYPE        VARCHAR2(50) DEFAULT 'TEXT' NOT NULL,
    CONTENT_FORMAT      VARCHAR2(50) DEFAULT 'HTML',
    SUMMARY             VARCHAR2(2000),
    KEYWORDS            VARCHAR2(1000),
    TAGS                CLOB CHECK (JSON_VALID(TAGS) = 1),
    AUTHOR_ID           NUMBER(19,0),
    AUTHOR_NAME         VARCHAR2(200),
    DOCUMENT_STATUS     VARCHAR2(50) DEFAULT 'DRAFT' NOT NULL,
    PUBLISH_DATE        TIMESTAMP(6),
    EXPIRY_DATE         TIMESTAMP(6),
    VERSION_NUMBER      VARCHAR2(20) DEFAULT '1.0',
    IS_FEATURED         NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_PUBLIC           NUMBER(1,0) DEFAULT 0 NOT NULL,
    ACCESS_LEVEL        VARCHAR2(50) DEFAULT 'INTERNAL' NOT NULL,
    ALLOWED_ROLES       CLOB CHECK (JSON_VALID(ALLOWED_ROLES) = 1),
    VIEW_COUNT          NUMBER(19,0) DEFAULT 0,
    DOWNLOAD_COUNT      NUMBER(19,0) DEFAULT 0,
    LIKE_COUNT          NUMBER(19,0) DEFAULT 0,
    RATING_SCORE        NUMBER(3,2) DEFAULT 0,
    RATING_COUNT        NUMBER(19,0) DEFAULT 0,
    FILE_PATH           VARCHAR2(1000),
    FILE_SIZE           NUMBER(19,0),
    FILE_TYPE           VARCHAR2(50),
    THUMBNAIL_PATH      VARCHAR2(1000),
    ATTACHMENTS         CLOB CHECK (JSON_VALID(ATTACHMENTS) = 1),
    RELATED_DOCUMENTS   CLOB CHECK (JSON_VALID(RELATED_DOCUMENTS) = 1),
    SEO_KEYWORDS        VARCHAR2(1000),
    SEO_DESCRIPTION     VARCHAR2(500),
    METADATA            CLOB CHECK (JSON_VALID(METADATA) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_KNOWLEDGE_DOCUMENT PRIMARY KEY (ID),
    CONSTRAINT UK_KNOWLEDGE_DOCUMENT_CODE UNIQUE (TENANT_ID, DOCUMENT_CODE, IS_DELETED),
    CONSTRAINT FK_KNOWLEDGE_DOCUMENT_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
    CONSTRAINT FK_KNOWLEDGE_DOCUMENT_AUTHOR FOREIGN KEY (AUTHOR_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_CONTENT_TYPE CHECK (CONTENT_TYPE IN ('TEXT', 'HTML', 'MARKDOWN', 'PDF', 'WORD', 'EXCEL', 'IMAGE', 'VIDEO', 'AUDIO')),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_STATUS CHECK (DOCUMENT_STATUS IN ('DRAFT', 'REVIEW', 'PUBLISHED', 'ARCHIVED', 'DELETED')),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_ACCESS CHECK (ACCESS_LEVEL IN ('PUBLIC', 'INTERNAL', 'RESTRICTED', 'PRIVATE')),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_IS_FEATURED CHECK (IS_FEATURED IN (0, 1)),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_IS_PUBLIC CHECK (IS_PUBLIC IN (0, 1)),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 文档访问记录表
CREATE TABLE KNOWLEDGE_ACCESS_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,
    USER_ID             NUMBER(19,0),
    ACCESS_TYPE         VARCHAR2(50) NOT NULL,
    ACCESS_TIME         TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(1000),
    SESSION_ID          VARCHAR2(100),
    REFERRER_URL        VARCHAR2(1000),
    ACCESS_DURATION     NUMBER(19,0), -- 秒
    DEVICE_TYPE         VARCHAR2(50),
    BROWSER_TYPE        VARCHAR2(100),
    OPERATING_SYSTEM    VARCHAR2(100),
    LOCATION_INFO       CLOB CHECK (JSON_VALID(LOCATION_INFO) = 1),
    ACCESS_RESULT       VARCHAR2(50) DEFAULT 'SUCCESS',
    ERROR_MESSAGE       VARCHAR2(1000),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CONSTRAINT PK_KNOWLEDGE_ACCESS_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_DOCUMENT FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_KNOWLEDGE_ACCESS_TYPE CHECK (ACCESS_TYPE IN ('VIEW', 'DOWNLOAD', 'PRINT', 'SHARE', 'SEARCH')),
    CONSTRAINT CK_KNOWLEDGE_ACCESS_RESULT CHECK (ACCESS_RESULT IN ('SUCCESS', 'FAILED', 'DENIED', 'TIMEOUT'))
)
PARTITION BY RANGE (ACCESS_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 文档评论表
CREATE TABLE KNOWLEDGE_COMMENT (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,
    USER_ID             NUMBER(19,0) NOT NULL,
    PARENT_COMMENT_ID   NUMBER(19,0),
    COMMENT_CONTENT     CLOB NOT NULL,
    COMMENT_TYPE        VARCHAR2(50) DEFAULT 'COMMENT' NOT NULL,
    RATING_SCORE        NUMBER(1,0),
    IS_ANONYMOUS        NUMBER(1,0) DEFAULT 0 NOT NULL,
    COMMENT_STATUS      VARCHAR2(50) DEFAULT 'PUBLISHED' NOT NULL,
    LIKE_COUNT          NUMBER(19,0) DEFAULT 0,
    DISLIKE_COUNT       NUMBER(19,0) DEFAULT 0,
    REPLY_COUNT         NUMBER(19,0) DEFAULT 0,
    MODERATED_BY        NUMBER(19,0),
    MODERATED_AT        TIMESTAMP(6),
    MODERATION_REASON   VARCHAR2(500),
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(1000),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_KNOWLEDGE_COMMENT PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_COMMENT_DOCUMENT FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_COMMENT_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_KNOWLEDGE_COMMENT_PARENT FOREIGN KEY (PARENT_COMMENT_ID) REFERENCES KNOWLEDGE_COMMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_COMMENT_MODERATOR FOREIGN KEY (MODERATED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_TYPE CHECK (COMMENT_TYPE IN ('COMMENT', 'REVIEW', 'QUESTION', 'SUGGESTION')),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_RATING CHECK (RATING_SCORE BETWEEN 1 AND 5),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_STATUS CHECK (COMMENT_STATUS IN ('DRAFT', 'PUBLISHED', 'HIDDEN', 'DELETED', 'PENDING_MODERATION')),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_IS_ANONYMOUS CHECK (IS_ANONYMOUS IN (0, 1)),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_IS_DELETED CHECK (IS_DELETED IN (0, 1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- =====================================================
-- 6. 系统管理模块表
-- =====================================================

-- 系统配置分类表
CREATE TABLE SYSTEM_CONFIG_CATEGORY (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    CATEGORY_CODE       VARCHAR2(50) NOT NULL,
    CATEGORY_NAME       VARCHAR2(200) NOT NULL,
    CATEGORY_DESCRIPTION VARCHAR2(1000),
    PARENT_CATEGORY_ID  NUMBER(19,0),
    CATEGORY_LEVEL      NUMBER(3,0) DEFAULT 1,
    SORT_ORDER          NUMBER(10,0) DEFAULT 0,
    IS_SYSTEM_CATEGORY  NUMBER(1,0) DEFAULT 0 NOT NULL,
    ACCESS_LEVEL        VARCHAR2(50) DEFAULT 'ADMIN' NOT NULL,
    ALLOWED_ROLES       CLOB CHECK (JSON_VALID(ALLOWED_ROLES) = 1),
    CATEGORY_ICON       VARCHAR2(200),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SYSTEM_CONFIG_CATEGORY PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_CATEGORY_CODE UNIQUE (TENANT_ID, CATEGORY_CODE, IS_DELETED),
    CONSTRAINT FK_SYSTEM_CONFIG_CATEGORY_PARENT FOREIGN KEY (PARENT_CATEGORY_ID) REFERENCES SYSTEM_CONFIG_CATEGORY(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_CATEGORY_ACCESS CHECK (ACCESS_LEVEL IN ('PUBLIC', 'USER', 'ADMIN', 'SYSTEM')),
    CONSTRAINT CK_SYSTEM_CONFIG_CATEGORY_IS_SYSTEM CHECK (IS_SYSTEM_CATEGORY IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_CATEGORY_IS_DELETED CHECK (IS_DELETED IN (0, 1))
);

-- 系统配置项表
CREATE TABLE SYSTEM_CONFIG_ITEM (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    CATEGORY_ID         NUMBER(19,0) NOT NULL,
    CONFIG_KEY          VARCHAR2(200) NOT NULL,
    CONFIG_NAME         VARCHAR2(200) NOT NULL,
    CONFIG_DESCRIPTION  VARCHAR2(1000),
    CONFIG_VALUE        CLOB,
    DEFAULT_VALUE       CLOB,
    CONFIG_TYPE         VARCHAR2(50) DEFAULT 'STRING' NOT NULL,
    VALUE_FORMAT        VARCHAR2(100),
    VALIDATION_RULE     VARCHAR2(1000),
    IS_ENCRYPTED        NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_SYSTEM_CONFIG    NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_READONLY         NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_REQUIRED         NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_SENSITIVE        NUMBER(1,0) DEFAULT 0 NOT NULL,
    ACCESS_LEVEL        VARCHAR2(50) DEFAULT 'ADMIN' NOT NULL,
    ALLOWED_ROLES       CLOB CHECK (JSON_VALID(ALLOWED_ROLES) = 1),
    SORT_ORDER          NUMBER(10,0) DEFAULT 0,
    EFFECTIVE_DATE      TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    EXPIRY_DATE         TIMESTAMP(6),
    LAST_MODIFIED_BY    NUMBER(19,0),
    LAST_MODIFIED_AT    TIMESTAMP(6),
    CHANGE_REASON       VARCHAR2(1000),
    APPROVAL_STATUS     VARCHAR2(50) DEFAULT 'APPROVED',
    APPROVED_BY         NUMBER(19,0),
    APPROVED_AT         TIMESTAMP(6),
    BACKUP_VALUE        CLOB,
    SYNC_STATUS         VARCHAR2(50) DEFAULT 'SYNCED',
    SYNC_TARGET         VARCHAR2(200),
    LAST_SYNC_TIME      TIMESTAMP(6),
    METADATA            CLOB CHECK (JSON_VALID(METADATA) = 1),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_SYSTEM_CONFIG_ITEM PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_ITEM_KEY UNIQUE (TENANT_ID, CONFIG_KEY, IS_DELETED),
    CONSTRAINT FK_SYSTEM_CONFIG_ITEM_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES SYSTEM_CONFIG_CATEGORY(ID),
    CONSTRAINT FK_SYSTEM_CONFIG_ITEM_MODIFIER FOREIGN KEY (LAST_MODIFIED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_SYSTEM_CONFIG_ITEM_APPROVER FOREIGN KEY (APPROVED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_TYPE CHECK (CONFIG_TYPE IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'XML', 'DATE', 'TIME', 'DATETIME', 'EMAIL', 'URL', 'PASSWORD')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_ACCESS CHECK (ACCESS_LEVEL IN ('PUBLIC', 'USER', 'ADMIN', 'SYSTEM')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_APPROVAL CHECK (APPROVAL_STATUS IN ('PENDING', 'APPROVED', 'REJECTED')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_SYNC CHECK (SYNC_STATUS IN ('SYNCED', 'PENDING', 'FAILED', 'DISABLED')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_ENCRYPTED CHECK (IS_ENCRYPTED IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_SYSTEM CHECK (IS_SYSTEM_CONFIG IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_READONLY CHECK (IS_READONLY IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_REQUIRED CHECK (IS_REQUIRED IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_SENSITIVE CHECK (IS_SENSITIVE IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_DELETED CHECK (IS_DELETED IN (0, 1))
);

-- 系统配置变更历史表
CREATE TABLE SYSTEM_CONFIG_CHANGE_HISTORY (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    CONFIG_ITEM_ID      NUMBER(19,0) NOT NULL,
    CHANGE_TYPE         VARCHAR2(50) NOT NULL,
    OLD_VALUE           CLOB,
    NEW_VALUE           CLOB,
    CHANGE_REASON       VARCHAR2(1000),
    CHANGED_BY          NUMBER(19,0) NOT NULL,
    CHANGE_TIME         TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    APPROVAL_STATUS     VARCHAR2(50) DEFAULT 'PENDING',
    APPROVED_BY         NUMBER(19,0),
    APPROVED_AT         TIMESTAMP(6),
    ROLLBACK_AVAILABLE  NUMBER(1,0) DEFAULT 1 NOT NULL,
    ROLLBACK_EXECUTED   NUMBER(1,0) DEFAULT 0 NOT NULL,
    ROLLBACK_BY         NUMBER(19,0),
    ROLLBACK_AT         TIMESTAMP(6),
    IMPACT_ASSESSMENT   CLOB CHECK (JSON_VALID(IMPACT_ASSESSMENT) = 1),
    VALIDATION_RESULT   VARCHAR2(50),
    ERROR_MESSAGE       VARCHAR2(1000),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CONSTRAINT PK_SYSTEM_CONFIG_CHANGE_HISTORY PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_CONFIG_CHANGE_ITEM FOREIGN KEY (CONFIG_ITEM_ID) REFERENCES SYSTEM_CONFIG_ITEM(ID),
    CONSTRAINT FK_SYSTEM_CONFIG_CHANGE_USER FOREIGN KEY (CHANGED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_SYSTEM_CONFIG_CHANGE_APPROVER FOREIGN KEY (APPROVED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_SYSTEM_CONFIG_CHANGE_ROLLBACK_USER FOREIGN KEY (ROLLBACK_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_TYPE CHECK (CHANGE_TYPE IN ('CREATE', 'UPDATE', 'DELETE', 'ROLLBACK')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_APPROVAL CHECK (APPROVAL_STATUS IN ('PENDING', 'APPROVED', 'REJECTED')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_VALIDATION CHECK (VALIDATION_RESULT IN ('PASS', 'FAIL', 'WARNING', 'PENDING')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_ROLLBACK_AVAILABLE CHECK (ROLLBACK_AVAILABLE IN (0, 1)),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_ROLLBACK_EXECUTED CHECK (ROLLBACK_EXECUTED IN (0, 1))
)
PARTITION BY RANGE (CHANGE_TIME) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 系统操作日志表
CREATE TABLE SYSTEM_OPERATION_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    USER_ID             NUMBER(19,0),
    SESSION_ID          VARCHAR2(100),
    OPERATION_TYPE      VARCHAR2(50) NOT NULL,
    OPERATION_MODULE    VARCHAR2(100) NOT NULL,
    OPERATION_ACTION    VARCHAR2(100) NOT NULL,
    OPERATION_TARGET    VARCHAR2(200),
    TARGET_ID           NUMBER(19,0),
    OPERATION_TIME      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    OPERATION_STATUS    VARCHAR2(50) NOT NULL,
    OPERATION_RESULT    VARCHAR2(50),
    REQUEST_METHOD      VARCHAR2(20),
    REQUEST_URL         VARCHAR2(1000),
    REQUEST_PARAMS      CLOB CHECK (JSON_VALID(REQUEST_PARAMS) = 1),
    REQUEST_BODY        CLOB,
    RESPONSE_STATUS     NUMBER(3,0),
    RESPONSE_TIME       NUMBER(19,0), -- 毫秒
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(1000),
    DEVICE_INFO         CLOB CHECK (JSON_VALID(DEVICE_INFO) = 1),
    LOCATION_INFO       CLOB CHECK (JSON_VALID(LOCATION_INFO) = 1),
    ERROR_CODE          VARCHAR2(50),
    ERROR_MESSAGE       CLOB,
    STACK_TRACE         CLOB,
    BUSINESS_DATA       CLOB CHECK (JSON_VALID(BUSINESS_DATA) = 1),
    RISK_LEVEL          VARCHAR2(20) DEFAULT 'LOW',
    ALERT_TRIGGERED     NUMBER(1,0) DEFAULT 0 NOT NULL,
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CONSTRAINT PK_SYSTEM_OPERATION_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_OPERATION_LOG_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_OPERATION_LOG_TYPE CHECK (OPERATION_TYPE IN ('CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'APPROVE', 'REJECT')),
    CONSTRAINT CK_SYSTEM_OPERATION_LOG_STATUS CHECK (OPERATION_STATUS IN ('SUCCESS', 'FAILURE', 'PARTIAL', 'TIMEOUT', 'CANCELLED')),
    CONSTRAINT CK_SYSTEM_OPERATION_LOG_RESULT CHECK (OPERATION_RESULT IN ('COMPLETED', 'FAILED', 'PENDING', 'SKIPPED')),
    CONSTRAINT CK_SYSTEM_OPERATION_LOG_RISK CHECK (RISK_LEVEL IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    CONSTRAINT CK_SYSTEM_OPERATION_LOG_ALERT CHECK (ALERT_TRIGGERED IN (0, 1))
)
PARTITION BY RANGE (OPERATION_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 系统性能日志表
CREATE TABLE SYSTEM_PERFORMANCE_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    LOG_TIME            TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    METRIC_TYPE         VARCHAR2(50) NOT NULL,
    METRIC_NAME         VARCHAR2(100) NOT NULL,
    METRIC_VALUE        NUMBER(15,4) NOT NULL,
    METRIC_UNIT         VARCHAR2(20),
    THRESHOLD_VALUE     NUMBER(15,4),
    IS_THRESHOLD_EXCEEDED NUMBER(1,0) DEFAULT 0 NOT NULL,
    SERVER_NAME         VARCHAR2(100),
    SERVICE_NAME        VARCHAR2(100),
    INSTANCE_ID         VARCHAR2(100),
    ENVIRONMENT         VARCHAR2(50) DEFAULT 'PRODUCTION',
    ADDITIONAL_METRICS  CLOB CHECK (JSON_VALID(ADDITIONAL_METRICS) = 1),
    ALERT_LEVEL         VARCHAR2(20) DEFAULT 'INFO',
    ALERT_SENT          NUMBER(1,0) DEFAULT 0 NOT NULL,
    COLLECTION_METHOD   VARCHAR2(50) DEFAULT 'AUTOMATIC',
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CONSTRAINT PK_SYSTEM_PERFORMANCE_LOG PRIMARY KEY (ID),
    CONSTRAINT CK_SYSTEM_PERFORMANCE_LOG_TYPE CHECK (METRIC_TYPE IN ('CPU', 'MEMORY', 'DISK', 'NETWORK', 'DATABASE', 'APPLICATION', 'CUSTOM')),
    CONSTRAINT CK_SYSTEM_PERFORMANCE_LOG_THRESHOLD CHECK (IS_THRESHOLD_EXCEEDED IN (0, 1)),
    CONSTRAINT CK_SYSTEM_PERFORMANCE_LOG_ALERT_LEVEL CHECK (ALERT_LEVEL IN ('INFO', 'WARNING', 'ERROR', 'CRITICAL')),
    CONSTRAINT CK_SYSTEM_PERFORMANCE_LOG_ALERT_SENT CHECK (ALERT_SENT IN (0, 1)),
    CONSTRAINT CK_SYSTEM_PERFORMANCE_LOG_METHOD CHECK (COLLECTION_METHOD IN ('AUTOMATIC', 'MANUAL', 'SCHEDULED'))
)
PARTITION BY RANGE (LOG_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'HOUR'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 系统错误日志表
CREATE TABLE SYSTEM_ERROR_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    TENANT_ID           NUMBER(19,0),
    ERROR_TIME          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    ERROR_LEVEL         VARCHAR2(20) NOT NULL,
    ERROR_CODE          VARCHAR2(50),
    ERROR_MESSAGE       CLOB NOT NULL,
    ERROR_TYPE          VARCHAR2(50) NOT NULL,
    ERROR_SOURCE        VARCHAR2(200),
    MODULE_NAME         VARCHAR2(100),
    FUNCTION_NAME       VARCHAR2(200),
    LINE_NUMBER         NUMBER(10,0),
    STACK_TRACE         CLOB,
    USER_ID             NUMBER(19,0),
    SESSION_ID          VARCHAR2(100),
    REQUEST_ID          VARCHAR2(100),
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(1000),
    REQUEST_URL         VARCHAR2(1000),
    REQUEST_METHOD      VARCHAR2(20),
    REQUEST_PARAMS      CLOB CHECK (JSON_VALID(REQUEST_PARAMS) = 1),
    RESPONSE_STATUS     NUMBER(3,0),
    SERVER_NAME         VARCHAR2(100),
    ENVIRONMENT         VARCHAR2(50) DEFAULT 'PRODUCTION',
    ERROR_CONTEXT       CLOB CHECK (JSON_VALID(ERROR_CONTEXT) = 1),
    RESOLUTION_STATUS   VARCHAR2(50) DEFAULT 'OPEN',
    RESOLVED_BY         NUMBER(19,0),
    RESOLVED_AT         TIMESTAMP(6),
    RESOLUTION_NOTES    CLOB,
    OCCURRENCE_COUNT    NUMBER(10,0) DEFAULT 1,
    FIRST_OCCURRENCE    TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    LAST_OCCURRENCE     TIMESTAMP(6) DEFAULT SYSTIMESTAMP,
    ALERT_SENT          NUMBER(1,0) DEFAULT 0 NOT NULL,
    TICKET_ID           VARCHAR2(100),
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CONSTRAINT PK_SYSTEM_ERROR_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_ERROR_LOG_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_SYSTEM_ERROR_LOG_RESOLVER FOREIGN KEY (RESOLVED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_SYSTEM_ERROR_LOG_LEVEL CHECK (ERROR_LEVEL IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'FATAL')),
    CONSTRAINT CK_SYSTEM_ERROR_LOG_TYPE CHECK (ERROR_TYPE IN ('SYSTEM', 'APPLICATION', 'DATABASE', 'NETWORK', 'SECURITY', 'BUSINESS', 'INTEGRATION')),
    CONSTRAINT CK_SYSTEM_ERROR_LOG_RESOLUTION CHECK (RESOLUTION_STATUS IN ('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'WONT_FIX')),
    CONSTRAINT CK_SYSTEM_ERROR_LOG_ALERT CHECK (ALERT_SENT IN (0, 1))
)
PARTITION BY RANGE (ERROR_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- =====================================================
-- 7. 创建索引
-- =====================================================

-- 用户管理模块索引
CREATE INDEX IDX_USER_ROLE_INFO_TENANT ON USER_ROLE_INFO(TENANT_ID, IS_DELETED, IS_ACTIVE);
CREATE INDEX IDX_USER_ROLE_INFO_TYPE ON USER_ROLE_INFO(ROLE_TYPE, IS_ACTIVE);
CREATE INDEX IDX_USER_ROLE_INFO_PARENT ON USER_ROLE_INFO(PARENT_ROLE_ID);

CREATE INDEX IDX_USER_INFO_TENANT ON USER_INFO(TENANT_ID, IS_DELETED, USER_STATUS);
CREATE INDEX IDX_USER_INFO_STATUS ON USER_INFO(USER_STATUS, IS_DELETED);
CREATE INDEX IDX_USER_INFO_TYPE ON USER_INFO(USER_TYPE, IS_DELETED);
CREATE INDEX IDX_USER_INFO_LOGIN_TIME ON USER_INFO(LAST_LOGIN_TIME);
CREATE INDEX IDX_USER_INFO_CREATED_AT ON USER_INFO(CREATED_AT);

CREATE INDEX IDX_USER_ROLE_MAPPING_USER ON USER_ROLE_MAPPING(USER_ID, IS_ACTIVE, IS_DELETED);
CREATE INDEX IDX_USER_ROLE_MAPPING_ROLE ON USER_ROLE_MAPPING(ROLE_ID, IS_ACTIVE, IS_DELETED);
CREATE INDEX IDX_USER_ROLE_MAPPING_EFFECTIVE ON USER_ROLE_MAPPING(EFFECTIVE_DATE, EXPIRY_DATE);

CREATE INDEX IDX_USER_LOGIN_AUDIT_USER ON USER_LOGIN_AUDIT(USER_ID, LOGIN_TIME);
CREATE INDEX IDX_USER_LOGIN_AUDIT_TIME ON USER_LOGIN_AUDIT(LOGIN_TIME);
CREATE INDEX IDX_USER_LOGIN_AUDIT_STATUS ON USER_LOGIN_AUDIT(LOGIN_STATUS, LOGIN_TIME);
CREATE INDEX IDX_USER_LOGIN_AUDIT_IP ON USER_LOGIN_AUDIT(IP_ADDRESS, LOGIN_TIME);

CREATE INDEX IDX_USER_PASSWORD_HISTORY_USER ON USER_PASSWORD_HISTORY(USER_ID, CHANGE_TIME);
CREATE INDEX IDX_USER_PASSWORD_HISTORY_CURRENT ON USER_PASSWORD_HISTORY(IS_CURRENT, USER_ID);

-- 医疗记录模块索引
CREATE INDEX IDX_PATIENT_INFO_TENANT ON PATIENT_INFO(TENANT_ID, IS_DELETED, PATIENT_STATUS);
CREATE INDEX IDX_PATIENT_INFO_STATUS ON PATIENT_INFO(PATIENT_STATUS, IS_DELETED);
CREATE INDEX IDX_PATIENT_INFO_RISK ON PATIENT_INFO(RISK_LEVEL, IS_DELETED);
CREATE INDEX IDX_PATIENT_INFO_CREATED_AT ON PATIENT_INFO(CREATED_AT);

CREATE INDEX IDX_MEDICAL_RECORD_PATIENT ON MEDICAL_RECORD(PATIENT_ID, VISIT_DATE);
CREATE INDEX IDX_MEDICAL_RECORD_HOSPITAL ON MEDICAL_RECORD(HOSPITAL_CODE, VISIT_DATE);
CREATE INDEX IDX_MEDICAL_RECORD_DOCTOR ON MEDICAL_RECORD(DOCTOR_CODE, VISIT_DATE);
CREATE INDEX IDX_MEDICAL_RECORD_STATUS ON MEDICAL_RECORD(RECORD_STATUS, IS_DELETED);
CREATE INDEX IDX_MEDICAL_RECORD_SETTLEMENT ON MEDICAL_RECORD(SETTLEMENT_STATUS, VISIT_DATE);
CREATE INDEX IDX_MEDICAL_RECORD_VISIT_DATE ON MEDICAL_RECORD(VISIT_DATE);
CREATE INDEX IDX_MEDICAL_RECORD_DIAGNOSIS ON MEDICAL_RECORD(DIAGNOSIS_CODE, VISIT_DATE);

CREATE INDEX IDX_MEDICAL_EXPENSE_RECORD ON MEDICAL_EXPENSE(MEDICAL_RECORD_ID, EXPENSE_DATE);
CREATE INDEX IDX_MEDICAL_EXPENSE_TYPE ON MEDICAL_EXPENSE(EXPENSE_TYPE, EXPENSE_DATE);
CREATE INDEX IDX_MEDICAL_EXPENSE_PROVIDER ON MEDICAL_EXPENSE(PROVIDER_CODE, EXPENSE_DATE);
CREATE INDEX IDX_MEDICAL_EXPENSE_APPROVAL ON MEDICAL_EXPENSE(APPROVAL_STATUS, EXPENSE_DATE);
CREATE INDEX IDX_MEDICAL_EXPENSE_RISK ON MEDICAL_EXPENSE(RISK_SCORE, EXPENSE_DATE);
CREATE INDEX IDX_MEDICAL_EXPENSE_AMOUNT ON MEDICAL_EXPENSE(TOTAL_AMOUNT, EXPENSE_DATE);

CREATE INDEX IDX_MEDICAL_AUDIT_RECORD ON MEDICAL_AUDIT_RECORD(MEDICAL_RECORD_ID, AUDIT_TIME);
CREATE INDEX IDX_MEDICAL_AUDIT_AUDITOR ON MEDICAL_AUDIT_RECORD(AUDITOR_ID, AUDIT_TIME);
CREATE INDEX IDX_MEDICAL_AUDIT_STATUS ON MEDICAL_AUDIT_RECORD(AUDIT_STATUS, AUDIT_TIME);
CREATE INDEX IDX_MEDICAL_AUDIT_RESULT ON MEDICAL_AUDIT_RECORD(AUDIT_RESULT, AUDIT_TIME);
CREATE INDEX IDX_MEDICAL_AUDIT_RISK ON MEDICAL_AUDIT_RECORD(RISK_LEVEL, AUDIT_TIME);

-- 监管规则模块索引
CREATE INDEX IDX_SUPERVISION_RULE_TENANT ON SUPERVISION_RULE(TENANT_ID, IS_DELETED, IS_ACTIVE);
CREATE INDEX IDX_SUPERVISION_RULE_TYPE ON SUPERVISION_RULE(RULE_TYPE, IS_ACTIVE);
CREATE INDEX IDX_SUPERVISION_RULE_CATEGORY ON SUPERVISION_RULE(RULE_CATEGORY, IS_ACTIVE);
CREATE INDEX IDX_SUPERVISION_RULE_SEVERITY ON SUPERVISION_RULE(SEVERITY_LEVEL, IS_ACTIVE);
CREATE INDEX IDX_SUPERVISION_RULE_APPROVAL ON SUPERVISION_RULE(APPROVAL_STATUS, APPROVED_AT);
CREATE INDEX IDX_SUPERVISION_RULE_EFFECTIVE ON SUPERVISION_RULE(EFFECTIVE_DATE, EXPIRY_DATE);

CREATE INDEX IDX_SUPERVISION_EXECUTION_RULE ON SUPERVISION_EXECUTION_LOG(RULE_ID, EXECUTION_START_TIME);
CREATE INDEX IDX_SUPERVISION_EXECUTION_STATUS ON SUPERVISION_EXECUTION_LOG(EXECUTION_STATUS, EXECUTION_START_TIME);
CREATE INDEX IDX_SUPERVISION_EXECUTION_TYPE ON SUPERVISION_EXECUTION_LOG(EXECUTION_TYPE, EXECUTION_START_TIME);
CREATE INDEX IDX_SUPERVISION_EXECUTION_TIME ON SUPERVISION_EXECUTION_LOG(EXECUTION_START_TIME);

CREATE INDEX IDX_SUPERVISION_RESULT_LOG ON SUPERVISION_EXECUTION_RESULT(EXECUTION_LOG_ID, CREATED_AT);
CREATE INDEX IDX_SUPERVISION_RESULT_RULE ON SUPERVISION_EXECUTION_RESULT(RULE_ID, CREATED_AT);
CREATE INDEX IDX_SUPERVISION_RESULT_TARGET ON SUPERVISION_EXECUTION_RESULT(TARGET_RECORD_ID, TARGET_RECORD_TYPE);
CREATE INDEX IDX_SUPERVISION_RESULT_VIOLATION ON SUPERVISION_EXECUTION_RESULT(VIOLATION_TYPE, VIOLATION_LEVEL);
CREATE INDEX IDX_SUPERVISION_RESULT_STATUS ON SUPERVISION_EXECUTION_RESULT(RESULT_STATUS, HANDLING_STATUS);
CREATE INDEX IDX_SUPERVISION_RESULT_HANDLER ON SUPERVISION_EXECUTION_RESULT(HANDLER_ID, HANDLED_AT);
CREATE INDEX IDX_SUPERVISION_RESULT_RISK ON SUPERVISION_EXECUTION_RESULT(RISK_SCORE, CREATED_AT);

CREATE INDEX IDX_SUPERVISION_AUDIT_RESULT ON SUPERVISION_AUDIT_RECORD(EXECUTION_RESULT_ID, AUDIT_TIME);
CREATE INDEX IDX_SUPERVISION_AUDIT_AUDITOR ON SUPERVISION_AUDIT_RECORD(AUDITOR_ID, AUDIT_TIME);
CREATE INDEX IDX_SUPERVISION_AUDIT_STATUS ON SUPERVISION_AUDIT_RECORD(AUDIT_STATUS, AUDIT_TIME);
CREATE INDEX IDX_SUPERVISION_AUDIT_RESULT_STATUS ON SUPERVISION_AUDIT_RECORD(AUDIT_RESULT, AUDIT_TIME);

-- 知识库模块索引
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_TENANT ON KNOWLEDGE_CATEGORY(TENANT_ID, IS_DELETED, IS_ACTIVE);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_PARENT ON KNOWLEDGE_CATEGORY(PARENT_CATEGORY_ID, SORT_ORDER);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_LEVEL ON KNOWLEDGE_CATEGORY(CATEGORY_LEVEL, SORT_ORDER);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_ACCESS ON KNOWLEDGE_CATEGORY(ACCESS_LEVEL, IS_ACTIVE);

CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_CATEGORY ON KNOWLEDGE_DOCUMENT(CATEGORY_ID, DOCUMENT_STATUS);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_AUTHOR ON KNOWLEDGE_DOCUMENT(AUTHOR_ID, CREATED_AT);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_STATUS ON KNOWLEDGE_DOCUMENT(DOCUMENT_STATUS, PUBLISH_DATE);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_ACCESS ON KNOWLEDGE_DOCUMENT(ACCESS_LEVEL, IS_PUBLIC);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_FEATURED ON KNOWLEDGE_DOCUMENT(IS_FEATURED, PUBLISH_DATE);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_RATING ON KNOWLEDGE_DOCUMENT(RATING_SCORE, RATING_COUNT);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_VIEW_COUNT ON KNOWLEDGE_DOCUMENT(VIEW_COUNT, CREATED_AT);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_KEYWORDS ON KNOWLEDGE_DOCUMENT(KEYWORDS);

CREATE INDEX IDX_KNOWLEDGE_ACCESS_DOCUMENT ON KNOWLEDGE_ACCESS_LOG(DOCUMENT_ID, ACCESS_TIME);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_USER ON KNOWLEDGE_ACCESS_LOG(USER_ID, ACCESS_TIME);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_TYPE ON KNOWLEDGE_ACCESS_LOG(ACCESS_TYPE, ACCESS_TIME);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_IP ON KNOWLEDGE_ACCESS_LOG(IP_ADDRESS, ACCESS_TIME);

CREATE INDEX IDX_KNOWLEDGE_COMMENT_DOCUMENT ON KNOWLEDGE_COMMENT(DOCUMENT_ID, CREATED_AT);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_USER ON KNOWLEDGE_COMMENT(USER_ID, CREATED_AT);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_PARENT ON KNOWLEDGE_COMMENT(PARENT_COMMENT_ID, CREATED_AT);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_STATUS ON KNOWLEDGE_COMMENT(COMMENT_STATUS, CREATED_AT);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_RATING ON KNOWLEDGE_COMMENT(RATING_SCORE, CREATED_AT);

-- 系统管理模块索引
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_TENANT ON SYSTEM_CONFIG_CATEGORY(TENANT_ID, IS_DELETED);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_PARENT ON SYSTEM_CONFIG_CATEGORY(PARENT_CATEGORY_ID, SORT_ORDER);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_ACCESS ON SYSTEM_CONFIG_CATEGORY(ACCESS_LEVEL);

CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_CATEGORY ON SYSTEM_CONFIG_ITEM(CATEGORY_ID, SORT_ORDER);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_TYPE ON SYSTEM_CONFIG_ITEM(CONFIG_TYPE, IS_SYSTEM_CONFIG);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_ACCESS ON SYSTEM_CONFIG_ITEM(ACCESS_LEVEL, IS_READONLY);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_SENSITIVE ON SYSTEM_CONFIG_ITEM(IS_SENSITIVE, IS_ENCRYPTED);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_APPROVAL ON SYSTEM_CONFIG_ITEM(APPROVAL_STATUS, APPROVED_AT);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_SYNC ON SYSTEM_CONFIG_ITEM(SYNC_STATUS, LAST_SYNC_TIME);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_EFFECTIVE ON SYSTEM_CONFIG_ITEM(EFFECTIVE_DATE, EXPIRY_DATE);

CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_ITEM ON SYSTEM_CONFIG_CHANGE_HISTORY(CONFIG_ITEM_ID, CHANGE_TIME);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_USER ON SYSTEM_CONFIG_CHANGE_HISTORY(CHANGED_BY, CHANGE_TIME);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_TYPE ON SYSTEM_CONFIG_CHANGE_HISTORY(CHANGE_TYPE, CHANGE_TIME);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_APPROVAL ON SYSTEM_CONFIG_CHANGE_HISTORY(APPROVAL_STATUS, APPROVED_AT);

CREATE INDEX IDX_SYSTEM_OPERATION_LOG_USER ON SYSTEM_OPERATION_LOG(USER_ID, OPERATION_TIME);
CREATE INDEX IDX_SYSTEM_OPERATION_LOG_MODULE ON SYSTEM_OPERATION_LOG(OPERATION_MODULE, OPERATION_TIME);
CREATE INDEX IDX_SYSTEM_OPERATION_LOG_TYPE ON SYSTEM_OPERATION_LOG(OPERATION_TYPE, OPERATION_TIME);
CREATE INDEX IDX_SYSTEM_OPERATION_LOG_STATUS ON SYSTEM_OPERATION_LOG(OPERATION_STATUS, OPERATION_TIME);
CREATE INDEX IDX_SYSTEM_OPERATION_LOG_IP ON SYSTEM_OPERATION_LOG(IP_ADDRESS, OPERATION_TIME);
CREATE INDEX IDX_SYSTEM_OPERATION_LOG_RISK ON SYSTEM_OPERATION_LOG(RISK_LEVEL, OPERATION_TIME);

CREATE INDEX IDX_SYSTEM_PERFORMANCE_LOG_TYPE ON SYSTEM_PERFORMANCE_LOG(METRIC_TYPE, LOG_TIME);
CREATE INDEX IDX_SYSTEM_PERFORMANCE_LOG_NAME ON SYSTEM_PERFORMANCE_LOG(METRIC_NAME, LOG_TIME);
CREATE INDEX IDX_SYSTEM_PERFORMANCE_LOG_SERVER ON SYSTEM_PERFORMANCE_LOG(SERVER_NAME, LOG_TIME);
CREATE INDEX IDX_SYSTEM_PERFORMANCE_LOG_THRESHOLD ON SYSTEM_PERFORMANCE_LOG(IS_THRESHOLD_EXCEEDED, LOG_TIME);
CREATE INDEX IDX_SYSTEM_PERFORMANCE_LOG_ALERT ON SYSTEM_PERFORMANCE_LOG(ALERT_LEVEL, ALERT_SENT);

CREATE INDEX IDX_SYSTEM_ERROR_LOG_LEVEL ON SYSTEM_ERROR_LOG(ERROR_LEVEL, ERROR_TIME);
CREATE INDEX IDX_SYSTEM_ERROR_LOG_TYPE ON SYSTEM_ERROR_LOG(ERROR_TYPE, ERROR_TIME);
CREATE INDEX IDX_SYSTEM_ERROR_LOG_MODULE ON SYSTEM_ERROR_LOG(MODULE_NAME, ERROR_TIME);
CREATE INDEX IDX_SYSTEM_ERROR_LOG_USER ON SYSTEM_ERROR_LOG(USER_ID, ERROR_TIME);
CREATE INDEX IDX_SYSTEM_ERROR_LOG_RESOLUTION ON SYSTEM_ERROR_LOG(RESOLUTION_STATUS, ERROR_TIME);
CREATE INDEX IDX_SYSTEM_ERROR_LOG_SERVER ON SYSTEM_ERROR_LOG(SERVER_NAME, ERROR_TIME);
CREATE INDEX IDX_SYSTEM_ERROR_LOG_OCCURRENCE ON SYSTEM_ERROR_LOG(OCCURRENCE_COUNT, LAST_OCCURRENCE);

-- =====================================================
-- 8. 创建触发器
-- =====================================================

-- 用户角色信息表触发器
CREATE OR REPLACE TRIGGER TRG_USER_ROLE_INFO_BIU
    BEFORE INSERT OR UPDATE ON USER_ROLE_INFO
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_USER_ROLE_INFO.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 用户信息表触发器
CREATE OR REPLACE TRIGGER TRG_USER_INFO_BIU
    BEFORE INSERT OR UPDATE ON USER_INFO
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_USER_INFO.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 用户角色关联表触发器
CREATE OR REPLACE TRIGGER TRG_USER_ROLE_MAPPING_BIU
    BEFORE INSERT OR UPDATE ON USER_ROLE_MAPPING
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_USER_ROLE_MAPPING.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 用户登录审计表触发器
CREATE OR REPLACE TRIGGER TRG_USER_LOGIN_AUDIT_BI
    BEFORE INSERT ON USER_LOGIN_AUDIT
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_USER_LOGIN_AUDIT.NEXTVAL;
END;
/

-- 用户密码历史表触发器
CREATE OR REPLACE TRIGGER TRG_USER_PASSWORD_HISTORY_BI
    BEFORE INSERT ON USER_PASSWORD_HISTORY
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_USER_PASSWORD_HISTORY.NEXTVAL;
END;
/

-- 患者信息表触发器
CREATE OR REPLACE TRIGGER TRG_PATIENT_INFO_BIU
    BEFORE INSERT OR UPDATE ON PATIENT_INFO
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_PATIENT_INFO.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 医疗记录表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_RECORD_BIU
    BEFORE INSERT OR UPDATE ON MEDICAL_RECORD
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_MEDICAL_RECORD.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 医疗费用明细表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_EXPENSE_BIU
    BEFORE INSERT OR UPDATE ON MEDICAL_EXPENSE
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_MEDICAL_EXPENSE.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 医疗审核记录表触发器
CREATE OR REPLACE TRIGGER TRG_MEDICAL_AUDIT_RECORD_BIU
    BEFORE INSERT OR UPDATE ON MEDICAL_AUDIT_RECORD
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_MEDICAL_AUDIT_RECORD.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 监管规则表触发器
CREATE OR REPLACE TRIGGER TRG_SUPERVISION_RULE_BIU
    BEFORE INSERT OR UPDATE ON SUPERVISION_RULE
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_SUPERVISION_RULE.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 监管执行记录表触发器
CREATE OR REPLACE TRIGGER TRG_SUPERVISION_EXECUTION_LOG_BIU
    BEFORE INSERT OR UPDATE ON SUPERVISION_EXECUTION_LOG
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_SUPERVISION_EXECUTION_LOG.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 监管执行结果表触发器
CREATE OR REPLACE TRIGGER TRG_SUPERVISION_EXECUTION_RESULT_BIU
    BEFORE INSERT OR UPDATE ON SUPERVISION_EXECUTION_RESULT
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_SUPERVISION_EXECUTION_RESULT.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 监管审核记录表触发器
CREATE OR REPLACE TRIGGER TRG_SUPERVISION_AUDIT_RECORD_BIU
    BEFORE INSERT OR UPDATE ON SUPERVISION_AUDIT_RECORD
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_SUPERVISION_AUDIT_RECORD.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 知识库分类表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_CATEGORY_BIU
    BEFORE INSERT OR UPDATE ON KNOWLEDGE_CATEGORY
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_KNOWLEDGE_CATEGORY.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 知识库文档表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_DOCUMENT_BIU
    BEFORE INSERT OR UPDATE ON KNOWLEDGE_DOCUMENT
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_KNOWLEDGE_DOCUMENT.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 文档访问记录表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_ACCESS_LOG_BI
    BEFORE INSERT ON KNOWLEDGE_ACCESS_LOG
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_KNOWLEDGE_ACCESS_LOG.NEXTVAL;
END;
/

-- 文档评论表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_COMMENT_BIU
    BEFORE INSERT OR UPDATE ON KNOWLEDGE_COMMENT
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_KNOWLEDGE_COMMENT.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 系统配置分类表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_CATEGORY_BIU
    BEFORE INSERT OR UPDATE ON SYSTEM_CONFIG_CATEGORY
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_SYSTEM_CONFIG_CATEGORY.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
    END IF;
END;
/

-- 系统配置项表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_ITEM_BIU
    BEFORE INSERT OR UPDATE ON SYSTEM_CONFIG_ITEM
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.ID := SEQ_SYSTEM_CONFIG_ITEM.NEXTVAL;
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.VERSION := :OLD.VERSION + 1;
        :NEW.LAST_MODIFIED_BY := :NEW.UPDATED_BY;
        :NEW.LAST_MODIFIED_AT := SYSTIMESTAMP;
    END IF;
END;
/

-- 系统配置变更历史表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_CHANGE_HISTORY_BI
    BEFORE INSERT ON SYSTEM_CONFIG_CHANGE_HISTORY
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_SYSTEM_CONFIG_CHANGE_HISTORY.NEXTVAL;
END;
/

-- 系统操作日志表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_OPERATION_LOG_BI
    BEFORE INSERT ON SYSTEM_OPERATION_LOG
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_SYSTEM_OPERATION_LOG.NEXTVAL;
END;
/

-- 系统性能日志表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_PERFORMANCE_LOG_BI
    BEFORE INSERT ON SYSTEM_PERFORMANCE_LOG
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_SYSTEM_PERFORMANCE_LOG.NEXTVAL;
END;
/

-- 系统错误日志表触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_ERROR_LOG_BI
    BEFORE INSERT ON SYSTEM_ERROR_LOG
    FOR EACH ROW
BEGIN
    :NEW.ID := SEQ_SYSTEM_ERROR_LOG.NEXTVAL;
END;
/

-- =====================================================
-- 9. 创建视图
-- =====================================================

-- 用户角色权限视图
CREATE OR REPLACE VIEW V_USER_ROLE_PERMISSIONS AS
SELECT 
    u.ID AS USER_ID,
    u.USERNAME,
    u.REAL_NAME_ENCRYPTED,
    u.USER_TYPE,
    u.USER_STATUS,
    r.ID AS ROLE_ID,
    r.ROLE_CODE,
    r.ROLE_NAME,
    r.ROLE_TYPE,
    r.PERMISSIONS,
    urm.EFFECTIVE_DATE,
    urm.EXPIRY_DATE,
    urm.IS_ACTIVE AS MAPPING_ACTIVE
FROM USER_INFO u
INNER JOIN USER_ROLE_MAPPING urm ON u.ID = urm.USER_ID AND urm.IS_DELETED = 0
INNER JOIN USER_ROLE_INFO r ON urm.ROLE_ID = r.ID AND r.IS_DELETED = 0
WHERE u.IS_DELETED = 0
  AND u.USER_STATUS = 'ACTIVE'
  AND r.IS_ACTIVE = 1
  AND urm.IS_ACTIVE = 1
  AND (urm.EXPIRY_DATE IS NULL OR urm.EXPIRY_DATE > SYSTIMESTAMP);

-- 医疗记录统计视图
CREATE OR REPLACE VIEW V_MEDICAL_RECORD_STATISTICS AS
SELECT 
    mr.HOSPITAL_CODE,
    mr.HOSPITAL_NAME,
    mr.DEPARTMENT_CODE,
    mr.DEPARTMENT_NAME,
    mr.VISIT_TYPE,
    TRUNC(mr.VISIT_DATE) AS VISIT_DATE,
    COUNT(*) AS RECORD_COUNT,
    SUM(mr.TOTAL_COST) AS TOTAL_COST,
    SUM(mr.INSURANCE_COVERAGE) AS TOTAL_INSURANCE_COVERAGE,
    SUM(mr.PATIENT_PAYMENT) AS TOTAL_PATIENT_PAYMENT,
    AVG(mr.TOTAL_COST) AS AVG_COST,
    COUNT(CASE WHEN mr.SETTLEMENT_STATUS = 'COMPLETED' THEN 1 END) AS SETTLED_COUNT,
    COUNT(CASE WHEN mr.SETTLEMENT_STATUS = 'PENDING' THEN 1 END) AS PENDING_COUNT
FROM MEDICAL_RECORD mr
WHERE mr.IS_DELETED = 0
  AND mr.RECORD_STATUS IN ('SUBMITTED', 'REVIEWED', 'APPROVED')
GROUP BY mr.HOSPITAL_CODE, mr.HOSPITAL_NAME, mr.DEPARTMENT_CODE, 
         mr.DEPARTMENT_NAME, mr.VISIT_TYPE, TRUNC(mr.VISIT_DATE);

-- 监管规则执行统计视图
CREATE OR REPLACE VIEW V_SUPERVISION_RULE_STATISTICS AS
SELECT 
    sr.ID AS RULE_ID,
    sr.RULE_CODE,
    sr.RULE_NAME,
    sr.RULE_TYPE,
    sr.RULE_CATEGORY,
    sr.SEVERITY_LEVEL,
    COUNT(sel.ID) AS EXECUTION_COUNT,
    COUNT(CASE WHEN sel.EXECUTION_STATUS = 'COMPLETED' THEN 1 END) AS COMPLETED_COUNT,
    COUNT(CASE WHEN sel.EXECUTION_STATUS = 'FAILED' THEN 1 END) AS FAILED_COUNT,
    SUM(sel.PROCESSED_RECORDS) AS TOTAL_PROCESSED_RECORDS,
    SUM(sel.VIOLATION_RECORDS) AS TOTAL_VIOLATION_RECORDS,
    AVG(sel.EXECUTION_DURATION) AS AVG_EXECUTION_DURATION,
    MAX(sel.EXECUTION_START_TIME) AS LAST_EXECUTION_TIME
FROM SUPERVISION_RULE sr
LEFT JOIN SUPERVISION_EXECUTION_LOG sel ON sr.ID = sel.RULE_ID AND sel.IS_DELETED = 0
WHERE sr.IS_DELETED = 0
  AND sr.IS_ACTIVE = 1
GROUP BY sr.ID, sr.RULE_CODE, sr.RULE_NAME, sr.RULE_TYPE, 
         sr.RULE_CATEGORY, sr.SEVERITY_LEVEL;

-- 知识库文档访问统计视图
CREATE OR REPLACE VIEW V_KNOWLEDGE_DOCUMENT_STATISTICS AS
SELECT 
    kd.ID AS DOCUMENT_ID,
    kd.TITLE,
    kd.CATEGORY_ID,
    kc.CATEGORY_NAME,
    kd.AUTHOR_ID,
    kd.DOCUMENT_STATUS,
    kd.VIEW_COUNT,
    kd.DOWNLOAD_COUNT,
    kd.LIKE_COUNT,
    kd.RATING_SCORE,
    kd.RATING_COUNT,
    COUNT(kal.ID) AS ACCESS_LOG_COUNT,
    COUNT(CASE WHEN kal.ACCESS_TYPE = 'VIEW' THEN 1 END) AS VIEW_LOG_COUNT,
    COUNT(CASE WHEN kal.ACCESS_TYPE = 'DOWNLOAD' THEN 1 END) AS DOWNLOAD_LOG_COUNT,
    MAX(kal.ACCESS_TIME) AS LAST_ACCESS_TIME
FROM KNOWLEDGE_DOCUMENT kd
INNER JOIN KNOWLEDGE_CATEGORY kc ON kd.CATEGORY_ID = kc.ID AND kc.IS_DELETED = 0
LEFT JOIN KNOWLEDGE_ACCESS_LOG kal ON kd.ID = kal.DOCUMENT_ID
WHERE kd.IS_DELETED = 0
  AND kd.DOCUMENT_STATUS = 'PUBLISHED'
GROUP BY kd.ID, kd.TITLE, kd.CATEGORY_ID, kc.CATEGORY_NAME, 
         kd.AUTHOR_ID, kd.DOCUMENT_STATUS, kd.VIEW_COUNT, 
         kd.DOWNLOAD_COUNT, kd.LIKE_COUNT, kd.RATING_SCORE, kd.RATING_COUNT;

-- 系统操作审计视图
CREATE OR REPLACE VIEW V_SYSTEM_OPERATION_AUDIT AS
SELECT 
    sol.ID,
    sol.USER_ID,
    u.USERNAME,
    u.REAL_NAME_ENCRYPTED,
    sol.OPERATION_TYPE,
    sol.OPERATION_MODULE,
    sol.OPERATION_ACTION,
    sol.OPERATION_TARGET,
    sol.OPERATION_TIME,
    sol.OPERATION_STATUS,
    sol.IP_ADDRESS,
    sol.RISK_LEVEL,
    sol.ALERT_TRIGGERED,
    CASE 
        WHEN sol.RISK_LEVEL IN ('HIGH', 'CRITICAL') THEN 'HIGH_RISK'
        WHEN sol.OPERATION_TYPE IN ('DELETE', 'EXPORT') THEN 'SENSITIVE_OPERATION'
        WHEN sol.OPERATION_STATUS = 'FAILURE' THEN 'FAILED_OPERATION'
        ELSE 'NORMAL'
    END AS AUDIT_CATEGORY
FROM SYSTEM_OPERATION_LOG sol
LEFT JOIN USER_INFO u ON sol.USER_ID = u.ID AND u.IS_DELETED = 0
WHERE sol.OPERATION_TIME >= SYSTIMESTAMP - INTERVAL '30' DAY;

-- =====================================================
-- 10. 初始化数据
-- =====================================================

-- 插入系统默认角色
INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 'SYSTEM', 1, 
        '{"permissions": ["*"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'SYSTEM_ADMIN', '系统管理员', '系统管理员，负责系统配置和用户管理', 'SYSTEM', 1, 
        '{"permissions": ["system:*", "user:*", "role:*"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'MEDICAL_ADMIN', '医疗管理员', '医疗数据管理员，负责医疗记录管理', 'BUSINESS', 0, 
        '{"permissions": ["medical:*", "patient:*"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'SUPERVISION_ADMIN', '监管管理员', '监管规则管理员，负责监管规则配置和执行', 'BUSINESS', 0, 
        '{"permissions": ["supervision:*", "rule:*"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'KNOWLEDGE_ADMIN', '知识库管理员', '知识库管理员，负责知识库内容管理', 'BUSINESS', 0, 
        '{"permissions": ["knowledge:*", "document:*"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'AUDITOR', '审核员', '审核员，负责医疗记录和监管结果审核', 'BUSINESS', 0, 
        '{"permissions": ["audit:*", "medical:read", "supervision:read"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'OPERATOR', '操作员', '普通操作员，负责日常数据录入和查询', 'BUSINESS', 0, 
        '{"permissions": ["medical:read", "medical:create", "patient:read", "knowledge:read"]}', 1, 1);

INSERT INTO USER_ROLE_INFO (TENANT_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, ROLE_TYPE, IS_SYSTEM_ROLE, PERMISSIONS, CREATED_BY, UPDATED_BY)
VALUES (1, 'VIEWER', '查看者', '只读用户，只能查看相关数据', 'BUSINESS', 0, 
        '{"permissions": ["medical:read", "patient:read", "knowledge:read", "supervision:read"]}', 1, 1);

-- 插入系统配置分类
INSERT INTO SYSTEM_CONFIG_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, IS_SYSTEM_CATEGORY, CREATED_BY, UPDATED_BY)
VALUES (1, 'SYSTEM', '系统配置', '系统基础配置参数', 1, 1, 1);

INSERT INTO SYSTEM_CONFIG_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, IS_SYSTEM_CATEGORY, CREATED_BY, UPDATED_BY)
VALUES (1, 'SECURITY', '安全配置', '系统安全相关配置', 1, 1, 1);

INSERT INTO SYSTEM_CONFIG_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, IS_SYSTEM_CATEGORY, CREATED_BY, UPDATED_BY)
VALUES (1, 'MEDICAL', '医疗配置', '医疗业务相关配置', 0, 1, 1);

INSERT INTO SYSTEM_CONFIG_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, IS_SYSTEM_CATEGORY, CREATED_BY, UPDATED_BY)
VALUES (1, 'SUPERVISION', '监管配置', '监管规则相关配置', 0, 1, 1);

INSERT INTO SYSTEM_CONFIG_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, IS_SYSTEM_CATEGORY, CREATED_BY, UPDATED_BY)
VALUES (1, 'KNOWLEDGE', '知识库配置', '知识库相关配置', 0, 1, 1);

-- 插入知识库分类
INSERT INTO KNOWLEDGE_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, CREATED_BY, UPDATED_BY)
VALUES (1, 'POLICY', '政策法规', '医保相关政策法规文档', 1, 1);

INSERT INTO KNOWLEDGE_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, CREATED_BY, UPDATED_BY)
VALUES (1, 'STANDARD', '标准规范', '医疗行业标准规范文档', 1, 1);

INSERT INTO KNOWLEDGE_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, CREATED_BY, UPDATED_BY)
VALUES (1, 'MANUAL', '操作手册', '系统操作手册和用户指南', 1, 1);

INSERT INTO KNOWLEDGE_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, CREATED_BY, UPDATED_BY)
VALUES (1, 'FAQ', '常见问题', '常见问题解答和故障排除', 1, 1);

INSERT INTO KNOWLEDGE_CATEGORY (TENANT_ID, CATEGORY_CODE, CATEGORY_NAME, CATEGORY_DESCRIPTION, CREATED_BY, UPDATED_BY)
VALUES (1, 'TRAINING', '培训资料', '用户培训和学习资料', 1, 1);

-- 提交事务
COMMIT;

-- 输出完成信息
SET SERVEROUTPUT ON;
BEGIN
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('医保基金监管平台数据库重构完成！');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('创建的对象统计：');
    DBMS_OUTPUT.PUT_LINE('- 序列：' || '16个');
    DBMS_OUTPUT.PUT_LINE('- 表：' || '20个');
    DBMS_OUTPUT.PUT_LINE('- 索引：' || '100+个');
    DBMS_OUTPUT.PUT_LINE('- 触发器：' || '20个');
    DBMS_OUTPUT.PUT_LINE('- 视图：' || '5个');
    DBMS_OUTPUT.PUT_LINE('- 初始数据：' || '已插入');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('重构特性：');
    DBMS_OUTPUT.PUT_LINE('✓ 企业级安全设计（数据加密、分类、权限控制）');
    DBMS_OUTPUT.PUT_LINE('✓ 多租户架构支持');
    DBMS_OUTPUT.PUT_LINE('✓ 完整审计追踪');
    DBMS_OUTPUT.PUT_LINE('✓ 性能优化（分区、索引）');
    DBMS_OUTPUT.PUT_LINE('✓ 数据完整性约束');
    DBMS_OUTPUT.PUT_LINE('✓ 版本控制和软删除');
    DBMS_OUTPUT.PUT_LINE('✓ 统一标准化设计');
    DBMS_OUTPUT.PUT_LINE('==============================================');
END;
/