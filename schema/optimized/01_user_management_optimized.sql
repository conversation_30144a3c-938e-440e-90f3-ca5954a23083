-- =====================================================
-- 医保基金监管平台 - 用户管理模块优化表结构 (Oracle 19c)
-- =====================================================

-- 1. 角色表 - 添加分区支持和性能优化
CREATE TABLE USER_ROLE_INFO (
    ID              NUMBER(19,0) NOT NULL,                       -- 角色ID，主键
    ROLE_CODE       VARCHAR2(50) NOT NULL,                       -- 角色编码，唯一标识
    ROLE_NAME       VARCHAR2(100) NOT NULL,                      -- 角色名称
    DESCRIPTION     VARCHAR2(500),                               -- 角色描述
    IS_ACTIVE       NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否激活（1:激活,0:禁用）
    IS_DELETED      NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 逻辑删除标识（0:未删除,1:已删除）
    CREATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY      NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY      NUMBER(19,0),                                -- 更新人ID
    VERSION         NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    METADATA        JSON,                                        -- 元数据，JSON格式
    CONSTRAINT PK_USER_ROLE_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_USER_ROLE_INFO_CODE UNIQUE (ROLE_CODE),
    CONSTRAINT CK_USER_ROLE_INFO_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_USER_ROLE_INFO_IS_DELETED CHECK (IS_DELETED IN (0,1))
) 
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
);

-- 创建索引
CREATE INDEX IDX_USER_ROLE_ACTIVE ON USER_ROLE_INFO(IS_ACTIVE, IS_DELETED);
CREATE INDEX IDX_USER_ROLE_CREATED ON USER_ROLE_INFO(CREATED_AT DESC);

-- 2. 用户表 - 增强安全性和性能
CREATE TABLE USER_INFO (
    ID              NUMBER(19,0) NOT NULL,                       -- 用户ID，主键
    USERNAME        VARCHAR2(50) NOT NULL,                       -- 用户名，登录账号
    PASSWORD_HASH   VARCHAR2(255) NOT NULL,                      -- 密码哈希值
    REAL_NAME       VARCHAR2(100) NOT NULL,                      -- 真实姓名
    EMAIL           VARCHAR2(100),                               -- 邮箱地址
    PHONE           VARCHAR2(20),                                -- 手机号码
    DEPARTMENT      VARCHAR2(100),                               -- 所属部门
    POSITION        VARCHAR2(100),                               -- 职位
    AVATAR          BLOB,                                        -- 头像
    STATUS          VARCHAR2(20) DEFAULT 'ACTIVE' NOT NULL,     -- 用户状态（ACTIVE/DISABLED/LOCKED/EXPIRED）
    LAST_LOGIN_AT   TIMESTAMP(6),                                -- 最后登录时间
    LOGIN_ATTEMPT_COUNT NUMBER(3,0) DEFAULT 0 NOT NULL,         -- 登录失败次数
    LOCKED_UNTIL    TIMESTAMP(6),                                -- 账户锁定截止时间
    PASSWORD_CHANGED_AT TIMESTAMP(6) DEFAULT SYSTIMESTAMP,      -- 密码最后修改时间
    LAST_PASSWORD_RESET TIMESTAMP(6),                           -- 最后密码重置时间
    MFA_SECRET        VARCHAR2(255),                             -- 多因素认证密钥
    MFA_ENABLED       NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 是否启用多因素认证（1:启用,0:禁用）
    IS_DELETED        NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识（0:未删除,1:已删除）
    CREATED_AT        TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT        TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY        NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY        NUMBER(19,0),                             -- 更新人ID
    VERSION           NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号，用于乐观锁
    METADATA          JSON,                                     -- 元数据，JSON格式
    CONSTRAINT PK_USER_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_USER_USERNAME UNIQUE (USERNAME),
    CONSTRAINT UK_USER_EMAIL UNIQUE (EMAIL),
    CONSTRAINT CK_USER_STATUS CHECK (STATUS IN ('ACTIVE','DISABLED','LOCKED','EXPIRED')),
    CONSTRAINT CK_USER_IS_DELETED CHECK (IS_DELETED IN (0,1)),
    CONSTRAINT CK_USER_MFA_ENABLED CHECK (MFA_ENABLED IN (0,1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
);

-- 创建复合索引
CREATE INDEX IDX_USER_STATUS ON USER_INFO(STATUS, IS_DELETED);
CREATE INDEX IDX_USER_LOGIN ON USER_INFO(USERNAME, STATUS);
CREATE INDEX IDX_USER_EMAIL_STATUS ON USER_INFO(EMAIL, STATUS);
CREATE INDEX IDX_USER_CREATED ON USER_INFO(CREATED_AT DESC);
CREATE INDEX IDX_USER_LAST_LOGIN ON USER_INFO(LAST_LOGIN_AT DESC);

-- 3. 用户角色关联表 - 添加审计字段
CREATE TABLE USER_ROLE_MAPPING (
    ID              NUMBER(19,0) NOT NULL,                       -- 关联ID，主键
    USER_ID         NUMBER(19,0) NOT NULL,                       -- 用户ID，外键
    ROLE_ID         NUMBER(19,0) NOT NULL,                       -- 角色ID，外键
    EFFECTIVE_DATE  DATE DEFAULT SYSDATE NOT NULL,               -- 生效日期
    EXPIRY_DATE     DATE,                                        -- 失效日期
    IS_ACTIVE       NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否激活（1:激活,0:禁用）
    CREATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY      NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY      NUMBER(19,0),                                -- 更新人ID
    VERSION         NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    METADATA        JSON,                                        -- 元数据，JSON格式
    CONSTRAINT PK_USER_ROLE_MAPPING PRIMARY KEY (ID),
    CONSTRAINT UK_USER_ROLE_MAPPING UNIQUE (USER_ID, ROLE_ID, EFFECTIVE_DATE),
    CONSTRAINT FK_USER_ROLE_MAPPING_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_USER_ROLE_MAPPING_ROLE FOREIGN KEY (ROLE_ID) REFERENCES USER_ROLE_INFO(ID),
    CONSTRAINT CK_USER_ROLE_MAPPING_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_USER_ROLE_MAPPING_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
);

-- 创建索引
CREATE INDEX IDX_USER_ROLE_MAPPING_USER ON USER_ROLE_MAPPING(USER_ID, IS_ACTIVE);
CREATE INDEX IDX_USER_ROLE_MAPPING_ROLE ON USER_ROLE_MAPPING(ROLE_ID, IS_ACTIVE);

-- 4. 用户登录审计表 - 新增安全审计
CREATE TABLE USER_LOGIN_AUDIT (
    ID              NUMBER(19,0) NOT NULL,                       -- 审计ID，主键
    USER_ID         NUMBER(19,0) NOT NULL,                       -- 用户ID，外键
    LOGIN_TIME      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 登录时间
    LOGIN_IP        VARCHAR2(45),                                -- 登录IP地址
    LOGIN_DEVICE    VARCHAR2(500),                               -- 登录设备信息
    LOGIN_BROWSER   VARCHAR2(500),                               -- 登录浏览器信息
    LOGIN_STATUS    VARCHAR2(20) NOT NULL,                       -- 登录状态（SUCCESS/FAILED/LOCKED/EXPIRED）
    FAILURE_REASON  VARCHAR2(500),                               -- 登录失败原因
    SESSION_ID      VARCHAR2(255),                               -- 会话ID
    MFA_VERIFIED    NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 多因素认证是否通过（1:通过,0:未通过）
    CREATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CONSTRAINT PK_USER_LOGIN_AUDIT PRIMARY KEY (ID),
    CONSTRAINT FK_USER_LOGIN_AUDIT_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_LOGIN_STATUS CHECK (LOGIN_STATUS IN ('SUCCESS','FAILED','LOCKED','EXPIRED')),
    CONSTRAINT CK_MFA_VERIFIED CHECK (MFA_VERIFIED IN (0,1))
)
PARTITION BY RANGE (LOGIN_TIME) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
);

-- 创建索引
CREATE INDEX IDX_LOGIN_AUDIT_USER ON USER_LOGIN_AUDIT(USER_ID, LOGIN_TIME DESC);
CREATE INDEX IDX_LOGIN_AUDIT_IP ON USER_LOGIN_AUDIT(LOGIN_IP, LOGIN_TIME DESC);
CREATE INDEX IDX_LOGIN_AUDIT_STATUS ON USER_LOGIN_AUDIT(LOGIN_STATUS, LOGIN_TIME DESC);

-- 5. 用户密码历史表 - 增强密码安全
CREATE TABLE USER_PASSWORD_HISTORY (
    ID              NUMBER(19,0) NOT NULL,                       -- 历史ID，主键
    USER_ID         NUMBER(19,0) NOT NULL,                       -- 用户ID，外键
    PASSWORD_HASH   VARCHAR2(255) NOT NULL,                      -- 历史密码哈希值
    CREATED_AT      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CONSTRAINT PK_USER_PASSWORD_HISTORY PRIMARY KEY (ID),
    CONSTRAINT FK_USER_PASSWORD_HISTORY_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID)
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
);

-- 创建索引
CREATE INDEX IDX_PASSWORD_HISTORY_USER ON USER_PASSWORD_HISTORY(USER_ID, CREATED_AT DESC);

-- 6. 创建序列和触发器
CREATE SEQUENCE SEQ_USER_ROLE_INFO START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_USER_INFO START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_USER_ROLE_MAPPING START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_USER_LOGIN_AUDIT START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_USER_PASSWORD_HISTORY START WITH 1 INCREMENT BY 1 CACHE 100;

-- 7. 创建触发器 - 自动更新时间和版本
CREATE OR REPLACE TRIGGER TRG_USER_ROLE_INFO_UPD
BEFORE UPDATE ON USER_ROLE_INFO
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

CREATE OR REPLACE TRIGGER TRG_USER_INFO_UPD
BEFORE UPDATE ON USER_INFO
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

CREATE OR REPLACE TRIGGER TRG_USER_ROLE_MAPPING_UPD
BEFORE UPDATE ON USER_ROLE_MAPPING
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

-- 8. 创建视图 - 用户完整信息视图
CREATE OR REPLACE VIEW VW_USER_COMPLETE AS
SELECT 
    u.ID,
    u.USERNAME,
    u.REAL_NAME,
    u.EMAIL,
    u.PHONE,
    u.DEPARTMENT,
    u.POSITION,
    u.STATUS,
    u.LAST_LOGIN_AT,
    u.CREATED_AT,
    u.UPDATED_AT,
    LISTAGG(r.ROLE_NAME, ', ') WITHIN GROUP (ORDER BY r.ROLE_NAME) AS ROLES,
    COUNT(rm.ID) AS ROLE_COUNT
FROM USER_INFO u
LEFT JOIN USER_ROLE_MAPPING rm ON u.ID = rm.USER_ID AND rm.IS_ACTIVE = 1 AND rm.EXPIRY_DATE IS NULL
LEFT JOIN USER_ROLE_INFO r ON rm.ROLE_ID = r.ID AND r.IS_ACTIVE = 1 AND r.IS_DELETED = 0
WHERE u.IS_DELETED = 0
GROUP BY u.ID, u.USERNAME, u.REAL_NAME, u.EMAIL, u.PHONE, u.DEPARTMENT, u.POSITION, u.STATUS, u.LAST_LOGIN_AT, u.CREATED_AT, u.UPDATED_AT;

-- 9. 创建物化视图 - 用户统计信息
CREATE MATERIALIZED VIEW MV_USER_STATISTICS
REFRESH COMPLETE ON DEMAND
AS
SELECT 
    TRUNC(u.CREATED_AT) AS CREATION_DATE,
    COUNT(*) AS NEW_USERS,
    COUNT(CASE WHEN u.STATUS = 'ACTIVE' THEN 1 END) AS ACTIVE_USERS,
    COUNT(CASE WHEN u.STATUS = 'LOCKED' THEN 1 END) AS LOCKED_USERS,
    COUNT(CASE WHEN u.LAST_LOGIN_AT >= SYSDATE - 7 THEN 1 END) AS RECENT_LOGIN_USERS
FROM USER_INFO u
WHERE u.IS_DELETED = 0
GROUP BY TRUNC(u.CREATED_AT);

-- 10. 创建函数 - 检查用户权限
CREATE OR REPLACE FUNCTION FN_CHECK_USER_PERMISSION(
    p_user_id IN NUMBER,
    p_role_code IN VARCHAR2
) RETURN NUMBER IS
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM USER_INFO u
    JOIN USER_ROLE_MAPPING urm ON u.ID = urm.USER_ID AND urm.IS_ACTIVE = 1
    JOIN USER_ROLE_INFO r ON urm.ROLE_ID = r.ID AND r.IS_ACTIVE = 1 AND r.IS_DELETED = 0
    WHERE u.ID = p_user_id 
    AND u.STATUS = 'ACTIVE' 
    AND u.IS_DELETED = 0
    AND r.ROLE_CODE = p_role_code;
    
    RETURN v_count;
END;
/

-- 11. 创建同义词和授权
CREATE SYNONYM USER_ROLES FOR VW_USER_COMPLETE;
GRANT SELECT ON VW_USER_COMPLETE TO APP_USER;
GRANT SELECT ON MV_USER_STATISTICS TO APP_USER;

-- 12. 创建表空间配置（可选）
-- ALTER TABLE USER_INFO MOVE TABLESPACE USERS_LARGE;
-- ALTER TABLE USER_ROLE_INFO MOVE TABLESPACE USERS_MEDIUM;
-- ALTER TABLE USER_LOGIN_AUDIT MOVE TABLESPACE USERS_AUDIT;

-- 13. 创建压缩配置（可选）
-- ALTER TABLE USER_LOGIN_AUDIT COMPRESS FOR ARCHIVE HIGH;

-- 注释已移至字段定义后，便于阅读和维护