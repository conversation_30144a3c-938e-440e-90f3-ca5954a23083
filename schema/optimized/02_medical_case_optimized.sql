-- =====================================================
-- 医保基金监管平台 - 医疗案例管理模块优化表结构 (Oracle 19c)
-- 基于提取数据参考文档重新设计
-- =====================================================

-- 1. 住院结算主单表 (SETTLE_ZY)
CREATE TABLE SETTLE_ZY (
    ID                      NUMBER(19,0) NOT NULL,                    -- 主键ID
    HOSPITAL_ID             VARCHAR2(100) NOT NULL,                   -- 医疗机构编码
    HOSPITAL_NAME           VARCHAR2(100) NOT NULL,                   -- 医疗机构名称
    BRIDGE_ID               VARCHAR2(100) NOT NULL,                   -- 病案关联字段
    HISID                   VARCHAR2(100) NOT NULL,                   -- 结算单据号
    P_LEVEL                 VARCHAR2(30) NOT NULL,                    -- 医保结算等级
    BILL_DATE               DATE NOT NULL,                            -- 结算日期
    ZYH                     VARCHAR2(100) NOT NULL,                   -- 住院号
    PATIENT_ID              VARCHAR2(100) NOT NULL,                   -- 个人编码
    SOCIAL_ID               VARCHAR2(100),                            -- 患者社会保障号码
    ID_CARD                 VARCHAR2(100) NOT NULL,                   -- 身份证号
    BENEFIT_TYPE            VARCHAR2(50) NOT NULL,                    -- 险种类型
    BENEFIT_GROUP_ID        VARCHAR2(30) NOT NULL,                    -- 人员类型
    ADMISSION_DEPT_ID       VARCHAR2(30),                             -- 入院科室编码
    ADMISSION_DEPT_NAME     VARCHAR2(30) NOT NULL,                    -- 入院科室名称
    TRANSFER_DEPT_ID        VARCHAR2(50),                             -- 转诊科室编码
    TRANSFER_DEPT_NAME      VARCHAR2(50),                             -- 转科科室名称
    DISCHARGE_DEPT_ID       VARCHAR2(30),                             -- 出院科室编码
    DISCHARGE_DEPT_NAME     VARCHAR2(30) NOT NULL,                    -- 出院科室名称
    DOCTOR_ID               VARCHAR2(30),                             -- 主诊医师编码
    DOCTOR_NAME             VARCHAR2(30) NOT NULL,                    -- 主诊医师姓名
    PATIENT_NAME            VARCHAR2(50) NOT NULL,                    -- 患者姓名
    PATIENT_GENDER          VARCHAR2(30) NOT NULL,                    -- 患者性别
    PATIENT_BIRTHDAY        DATE NOT NULL,                            -- 患者出生日期
    PATIENT_AGE             NUMBER(16,2) NOT NULL,                    -- 患者年龄
    BEDID                   VARCHAR2(30),                             -- 患者床位号
    NB_TYPE                 VARCHAR2(30),                             -- 新生儿入院类型
    NB_BIRTH_WEIGHT         NUMBER(16,2),                             -- 新生儿出生体重
    NB_INPATIENT_WEIGHT     NUMBER(16,2),                             -- 新生儿入院体重
    CLAIM_TYPE              VARCHAR2(30) NOT NULL,                    -- 住院医疗类型
    IF_LOCAL_FLAG           VARCHAR2(30) NOT NULL,                    -- 异地标志
    ADMISSION_DATE          DATE NOT NULL,                            -- 入院日期
    DISCHARGE_DATE          DATE NOT NULL,                            -- 出院日期
    ZYTS                    NUMBER(16,2) NOT NULL,                    -- 住院天数
    DISCHARGE_STATUS        VARCHAR2(30) NOT NULL,                    -- 离院方式
    TOTAL_AMOUNT            NUMBER(16,2) NOT NULL,                    -- 医疗总费用
    BMI_PAY_AMOUNT          NUMBER(16,2) NOT NULL,                    -- 基本统筹支付
    CASH                    NUMBER(16,2),                             -- 个人现金支付
    SELF_PAY_AMOUNT         NUMBER(16,2),                             -- 个人账户支付
    SELF_PAY_IN             NUMBER(16,2),                             -- 个人自付
    SELF_PAY_OUT            NUMBER(16,2),                             -- 个人自费
    BMI_CONVERED_AMOUNT     NUMBER(16,2),                             -- 符合基本医疗保险的费用
    HOSPITAL_AREA           VARCHAR2(30) NOT NULL,                    -- 院区
    MEDICAL_INSURANCE_FLAG  VARCHAR2(30) NOT NULL,                    -- 医保结算与非医保结算标志
    REFUND_FLAG_TYPE        VARCHAR2(6) NOT NULL,                     -- 退费标识
    REFUND_HISID            VARCHAR2(100),                            -- 退费冲销原结算单据号
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识
    CREATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY              NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY              NUMBER(19,0),                             -- 更新人ID
    VERSION                 NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号
    CONSTRAINT PK_SETTLE_ZY PRIMARY KEY (ID),
    CONSTRAINT UK_SETTLE_ZY_HISID UNIQUE (HISID),
    CONSTRAINT CK_SETTLE_ZY_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY RANGE (BILL_DATE) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (DATE '2024-02-01')
)
COMPRESS FOR ARCHIVE HIGH;

-- 2. 住院诊断信息表
CREATE TABLE SETTLE_ZY_DIAGNOSIS (
    ID                      NUMBER(19,0) NOT NULL,                    -- 主键ID
    HOSPITAL_ID             VARCHAR2(100) NOT NULL,                   -- 医疗机构编码
    HOSPITAL_NAME           VARCHAR2(100) NOT NULL,                   -- 医疗机构名称
    BRIDGE_ID               VARCHAR2(100) NOT NULL,                   -- 病案关联字段
    HISID                   VARCHAR2(100) NOT NULL,                   -- 结算单据号
    ZYH                     VARCHAR2(100) NOT NULL,                   -- 住院号
    PATIENT_ID              VARCHAR2(100) NOT NULL,                   -- 个人编码
    SOCIAL_ID               VARCHAR2(100),                            -- 患者社会保障号码
    ID_CARD                 VARCHAR2(100) NOT NULL,                   -- 身份证号
    ADMISSION_DISEASE_ID    VARCHAR2(200) NOT NULL,                   -- 入院诊断编码
    ADMISSION_DISEASE_NAME  VARCHAR2(200) NOT NULL,                   -- 入院诊断名称
    DISCHARGE_DISEASE_ID    VARCHAR2(20) NOT NULL,                    -- 诊断编码
    DISCHARGE_DISEASE_NAME  VARCHAR2(50) NOT NULL,                    -- 诊断名称
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识
    CREATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY              NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY              NUMBER(19,0),                             -- 更新人ID
    VERSION                 NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号
    CONSTRAINT PK_SETTLE_ZY_DIAGNOSIS PRIMARY KEY (ID),
    CONSTRAINT FK_SETTLE_ZY_DIAGNOSIS_HISID FOREIGN KEY (HISID) REFERENCES SETTLE_ZY(HISID),
    CONSTRAINT CK_SETTLE_ZY_DIAGNOSIS_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY REFERENCE (FK_SETTLE_ZY_DIAGNOSIS_HISID)
COMPRESS FOR ARCHIVE HIGH;

-- 3. 手术操作信息表
CREATE TABLE SETTLE_ZY_SURGERY (
    ID                      NUMBER(19,0) NOT NULL,                    -- 主键ID
    HOSPITAL_ID             VARCHAR2(100) NOT NULL,                   -- 医疗机构编码
    HOSPITAL_NAME           VARCHAR2(100) NOT NULL,                   -- 医疗机构名称
    HISID                   VARCHAR2(100) NOT NULL,                   -- 结算单据号
    ZYH                     VARCHAR2(100) NOT NULL,                   -- 住院号
    PATIENT_ID              VARCHAR2(100) NOT NULL,                   -- 个人编码
    SOCIAL_ID               VARCHAR2(100),                            -- 患者社会保障号码
    ID_CARD                 VARCHAR2(100) NOT NULL,                   -- 身份证号
    ICD9_CODE               VARCHAR2(100) NOT NULL,                   -- 主手术及操作编码
    ICD9_NAME               VARCHAR2(100) NOT NULL,                   -- 主手术及操作名称
    OPRN_OPRT_DATE          DATE,                                     -- 手术操作日期
    ANST_WAY                VARCHAR2(100) NOT NULL,                   -- 麻醉方式
    ANST_BEGIN_TIME         TIMESTAMP(6),                             -- 麻醉开始时间
    ANST_END_TIME           TIMESTAMP(6),                             -- 麻醉结束时间
    OPER_BEGIN_TIME         TIMESTAMP(6),                             -- 手术开始时间
    OPER_END_TIME           TIMESTAMP(6),                             -- 手术结束时间
    OPER_DR_NAME            VARCHAR2(50) NOT NULL,                    -- 主刀术者医师姓名
    OPER_DR_CODE            VARCHAR2(50),                             -- 主刀术者医师代码
    OPER_OTHER_DR_NAME      VARCHAR2(50),                             -- 其他术者医师姓名
    OPER_OTHER_DR_CODE      VARCHAR2(50),                             -- 其他术者医师代码
    ANST_DR_NAME            VARCHAR2(50),                             -- 麻醉医师姓名
    ANST_DR_CODE            VARCHAR2(50),                             -- 麻醉医师代码
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识
    CREATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY              NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY              NUMBER(19,0),                             -- 更新人ID
    VERSION                 NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号
    CONSTRAINT PK_SETTLE_ZY_SURGERY PRIMARY KEY (ID),
    CONSTRAINT FK_SETTLE_ZY_SURGERY_HISID FOREIGN KEY (HISID) REFERENCES SETTLE_ZY(HISID),
    CONSTRAINT CK_SETTLE_ZY_SURGERY_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY REFERENCE (FK_SETTLE_ZY_SURGERY_HISID)
COMPRESS FOR ARCHIVE HIGH;

-- 4. 住院结算明细表 (SETTLE_ZY_DETAIL)
CREATE TABLE SETTLE_ZY_DETAIL (
    ID                      NUMBER(19,0) NOT NULL,                    -- 主键ID
    HOSPITAL_ID             VARCHAR2(100) NOT NULL,                   -- 医疗机构编码
    HOSPITAL_NAME           VARCHAR2(100) NOT NULL,                   -- 医疗机构名称
    HISID                   VARCHAR2(100) NOT NULL,                   -- 结算单据号
    PATIENT_ID              VARCHAR2(100) NOT NULL,                   -- 个人编码
    ZYH                     VARCHAR2(100) NOT NULL,                   -- 住院号
    ID_CARD                 VARCHAR2(100) NOT NULL,                   -- 身份证号
    BILLING_DEPT_ID         VARCHAR2(30) NOT NULL,                    -- 开单科室编码
    BILLING_DEPT_NAME       VARCHAR2(30) NOT NULL,                    -- 开单科室名称
    BILLING_TIME            TIMESTAMP(6),                             -- 项目开单时间
    USAGE_DATE              TIMESTAMP(6),                             -- 项目执行时间
    EXCUTE_DEPT_ID          VARCHAR2(30) NOT NULL,                    -- 执行科室编码
    EXCUTE_DEPT_NAME        VARCHAR2(30) NOT NULL,                    -- 执行科室名称
    DOCTOR_ID               VARCHAR2(30),                             -- 开单医师编码
    DOCTOR_NAME             VARCHAR2(30) NOT NULL,                    -- 开单医师姓名
    P_CATEGORY              VARCHAR2(30) NOT NULL,                    -- 费用类别
    BILL_DATE               DATE NOT NULL,                            -- 结算日期
    DISCHARGE_MEDICATION    VARCHAR2(30),                             -- 出院带药标识
    ITEM_ID_HOSP            VARCHAR2(50) NOT NULL,                    -- 医院项目编码
    ITEM_NAME_HOSP          VARCHAR2(100) NOT NULL,                   -- 医院项目名称
    ITEM_ID                 VARCHAR2(50) NOT NULL,                    -- 医保项目编码
    ITEM_NAME               VARCHAR2(100) NOT NULL,                   -- 医保项目名称
    DRUG_SPEC               VARCHAR2(100),                            -- 规格
    DOSAGE_FORM             VARCHAR2(100),                            -- 剂型
    PACKAGE_UNIT            VARCHAR2(30),                             -- 计价单位
    UNIT_PRICE              NUMBER(16,2) NOT NULL,                    -- 单价
    NUM                     NUMBER(16,2) NOT NULL,                    -- 数量
    COST                    NUMBER(16,2) NOT NULL,                    -- 金额
    SELF_PAY_LIMIT          NUMBER(16,2),                             -- 拒付金额
    BMI_CONVERED_AMOUNT     NUMBER(16,2) NOT NULL,                    -- 医保范围内金额
    P_TYPE                  NUMBER(16,2) NOT NULL,                    -- 支付类别
    P_TYPE_PCT              NUMBER(16,2) NOT NULL,                    -- 报销比例
    REFUND_FLAG_TYPE        VARCHAR2(2) NOT NULL,                     -- 退费标识
    REFUND_HISID            VARCHAR2(100),                            -- 退费冲销原结算单据号
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识
    CREATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY              NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY              NUMBER(19,0),                             -- 更新人ID
    VERSION                 NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号
    CONSTRAINT PK_SETTLE_ZY_DETAIL PRIMARY KEY (ID),
    CONSTRAINT FK_SETTLE_ZY_DETAIL_HISID FOREIGN KEY (HISID) REFERENCES SETTLE_ZY(HISID),
    CONSTRAINT CK_SETTLE_ZY_DETAIL_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY REFERENCE (FK_SETTLE_ZY_DETAIL_HISID)
COMPRESS FOR ARCHIVE HIGH;

-- 5. 门诊结算主单表 (SETTLE_MZ)
CREATE TABLE SETTLE_MZ (
    ID                      NUMBER(19,0) NOT NULL,                    -- 主键ID
    HOSPITAL_ID             VARCHAR2(100) NOT NULL,                   -- 医疗机构编码
    HOSPITAL_NAME           VARCHAR2(100) NOT NULL,                   -- 医疗机构名称
    HISID                   VARCHAR2(100) NOT NULL,                   -- 结算单据号
    P_LEVEL                 VARCHAR2(30),                             -- 医保结算等级
    BILL_DATE               DATE NOT NULL,                            -- 结算日期
    YEAR                    VARCHAR2(30),                             -- 结算年份
    MONTH                   VARCHAR2(30),                             -- 结算月份
    PATIENT_ID              VARCHAR2(100) NOT NULL,                   -- 个人编码
    SOCIAL_ID               VARCHAR2(50) NOT NULL,                    -- 患者社会保障号码
    BENEFIT_TYPE            VARCHAR2(50) NOT NULL,                    -- 险种类型
    BENEFIT_GROUP_ID        VARCHAR2(50) NOT NULL,                    -- 人员类别
    MZH                     VARCHAR2(50) NOT NULL,                    -- 门诊号
    ADMISSION_DEPT_ID       VARCHAR2(50) NOT NULL,                    -- 就诊科室编码
    ADMISSION_DEPT_NAME     VARCHAR2(50) NOT NULL,                    -- 就诊科室名称
    DOCTOR_ID               VARCHAR2(50),                             -- 医师编码
    DOCTOR_NAME             VARCHAR2(50) NOT NULL,                    -- 医师名称
    PATIENT_NAME            VARCHAR2(50) NOT NULL,                    -- 患者姓名
    PATIENT_GENDER          VARCHAR2(6) NOT NULL,                     -- 患者性别
    PATIENT_BIRTHDAY        DATE NOT NULL,                            -- 患者出生日期
    PATIENT_AGE             NUMBER(16,2) NOT NULL,                    -- 患者年龄
    CLAIM_TYPE              VARCHAR2(50) NOT NULL,                    -- 就医类型
    IF_LOCAL_FLAG           VARCHAR2(50) NOT NULL,                    -- 异地标志
    ADMISSION_DATE          DATE NOT NULL,                            -- 就诊日期
    OUTPATIENT_DISEASE_ID   VARCHAR2(300) NOT NULL,                   -- 诊断编码
    ADMISSION_DISEASE_NAME  VARCHAR2(300) NOT NULL,                   -- 诊断名称
    TOTAL_AMOUNT            NUMBER(16,2) NOT NULL,                    -- 医疗总费用
    BMI_PAY_AMOUNT          NUMBER(16,2) NOT NULL,                    -- 基本统筹支付
    CASH                    NUMBER(16,2) NOT NULL,                    -- 现金支付
    SELF_PAY_AMOUNT         NUMBER(16,2) NOT NULL,                    -- 个人账户支付
    BMI_CONVERED_AMOUNT     NUMBER(16,2),                             -- 符合基本医疗保险的费用
    HOSPITAL_AREA           VARCHAR2(30) NOT NULL,                    -- 院区
    MEDICAL_INSURANCE_FLAG  VARCHAR2(6) NOT NULL,                     -- 医保结算与非医保结算志
    REFUND_FLAG_TYPE        VARCHAR2(6) NOT NULL,                     -- 退费标识
    REFUND_HISID            VARCHAR2(100),                            -- 退费冲销原结算单据号
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识
    CREATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY              NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY              NUMBER(19,0),                             -- 更新人ID
    VERSION                 NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号
    CONSTRAINT PK_SETTLE_MZ PRIMARY KEY (ID),
    CONSTRAINT UK_SETTLE_MZ_HISID UNIQUE (HISID),
    CONSTRAINT CK_SETTLE_MZ_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY RANGE (BILL_DATE) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (DATE '2024-02-01')
)
COMPRESS FOR ARCHIVE HIGH;

-- 6. 门诊结算明细表 (SETTLE_MZ_DETAIL)
CREATE TABLE SETTLE_MZ_DETAIL (
    ID                      NUMBER(19,0) NOT NULL,                    -- 主键ID
    HOSPITAL_ID             VARCHAR2(100) NOT NULL,                   -- 医疗机构编码
    HOSPITAL_NAME           VARCHAR2(100) NOT NULL,                   -- 医疗机构名称
    HISID                   VARCHAR2(100) NOT NULL,                   -- 结算单据号
    PATIENT_ID              VARCHAR2(100) NOT NULL,                   -- 个人编码
    DISCHARGE_DEPT_ID       VARCHAR2(50) NOT NULL,                    -- 开单科室编码
    DISCHARGE_DEPT_NAME     VARCHAR2(50) NOT NULL,                    -- 开单科室名称
    BILLING_TIME            TIMESTAMP(6) NOT NULL,                    -- 项目开单时间
    EXCUTE_DEPT_ID          VARCHAR2(50) NOT NULL,                    -- 执行科室编码
    EXCUTE_DEPT_NAME        VARCHAR2(50) NOT NULL,                    -- 执行科室名称
    USAGE_DATE              TIMESTAMP(6) NOT NULL,                    -- 项目执行时间
    DOCTOR_ID               VARCHAR2(50),                             -- 开单医师编码
    DOCTOR_NAME             VARCHAR2(50) NOT NULL,                    -- 开单医师姓名
    BILL_DATE               DATE NOT NULL,                            -- 结算日期
    PRESCRIPTION            VARCHAR2(100) NOT NULL,                   -- 处方号
    P_CATEGORY              VARCHAR2(50) NOT NULL,                    -- 费用类别
    ITEM_ID_HOSP            VARCHAR2(100) NOT NULL,                   -- 医院项目编码
    ITEM_NAME_HOSP          VARCHAR2(100) NOT NULL,                   -- 医院项目名称
    ITEM_ID                 VARCHAR2(100) NOT NULL,                   -- 医保项目编码
    ITEM_NAME               VARCHAR2(100) NOT NULL,                   -- 医保项目名称
    DRUG_SPEC               VARCHAR2(100),                            -- 规格
    DOSAGE_FORM             VARCHAR2(100),                            -- 剂型
    PACKAGE_UNIT            VARCHAR2(30),                             -- 计价单位
    UNIT_PRICE              NUMBER(16,2) NOT NULL,                    -- 单价
    NUM                     NUMBER(16,2) NOT NULL,                    -- 数量
    COST                    NUMBER(16,2) NOT NULL,                    -- 金额
    SELF_PAY_LIMIT          NUMBER(16,2),                             -- 拒付金额
    BMI_CONVERED_AMOUNT     NUMBER(16,2) NOT NULL,                    -- 医保范围内金额
    P_TYPE                  NUMBER(16,2) NOT NULL,                    -- 支付类别
    P_TYPE_PCT              NUMBER(16,2) NOT NULL,                    -- 报销比例
    REFUND_FLAG_TYPE        VARCHAR2(2) NOT NULL,                     -- 退费标识
    REFUND_HISID            VARCHAR2(100),                            -- 退费冲销原结算单据号
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,           -- 逻辑删除标识
    CREATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_AT              TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CREATED_BY              NUMBER(19,0),                             -- 创建人ID
    UPDATED_BY              NUMBER(19,0),                             -- 更新人ID
    VERSION                 NUMBER(10,0) DEFAULT 1 NOT NULL,          -- 版本号
    CONSTRAINT PK_SETTLE_MZ_DETAIL PRIMARY KEY (ID),
    CONSTRAINT FK_SETTLE_MZ_DETAIL_HISID FOREIGN KEY (HISID) REFERENCES SETTLE_MZ(HISID),
    CONSTRAINT CK_SETTLE_MZ_DETAIL_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY REFERENCE (FK_SETTLE_MZ_DETAIL_HISID)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
-- 住院结算主单索引
CREATE INDEX IDX_SETTLE_ZY_PATIENT ON SETTLE_ZY(ID_CARD, PATIENT_NAME);
CREATE INDEX IDX_SETTLE_ZY_HOSPITAL ON SETTLE_ZY(HOSPITAL_ID, BILL_DATE DESC);
CREATE INDEX IDX_SETTLE_ZY_DOCTOR ON SETTLE_ZY(DOCTOR_ID, ADMISSION_DATE DESC);
CREATE INDEX IDX_SETTLE_ZY_DEPT ON SETTLE_ZY(ADMISSION_DEPT_ID, DISCHARGE_DEPT_ID);
CREATE INDEX IDX_SETTLE_ZY_COST ON SETTLE_ZY(TOTAL_AMOUNT DESC, BMI_PAY_AMOUNT DESC);
CREATE INDEX IDX_SETTLE_ZY_BRIDGE ON SETTLE_ZY(BRIDGE_ID);
CREATE INDEX IDX_SETTLE_ZY_ZYH ON SETTLE_ZY(ZYH, HOSPITAL_ID);

-- 住院诊断信息表索引
CREATE INDEX IDX_SETTLE_ZY_DIAGNOSIS_HISID ON SETTLE_ZY_DIAGNOSIS(HISID);
CREATE INDEX IDX_SETTLE_ZY_DIAGNOSIS_CODE ON SETTLE_ZY_DIAGNOSIS(DISCHARGE_DISEASE_ID, ADMISSION_DISEASE_ID);
CREATE INDEX IDX_SETTLE_ZY_DIAGNOSIS_PATIENT ON SETTLE_ZY_DIAGNOSIS(PATIENT_ID, ID_CARD);

-- 手术操作信息表索引
CREATE INDEX IDX_SETTLE_ZY_SURGERY_HISID ON SETTLE_ZY_SURGERY(HISID);
CREATE INDEX IDX_SETTLE_ZY_SURGERY_CODE ON SETTLE_ZY_SURGERY(ICD9_CODE, ICD9_NAME);
CREATE INDEX IDX_SETTLE_ZY_SURGERY_DOCTOR ON SETTLE_ZY_SURGERY(OPER_DR_CODE, OPRN_OPRT_DATE DESC);
CREATE INDEX IDX_SETTLE_ZY_SURGERY_DATE ON SETTLE_ZY_SURGERY(OPRN_OPRT_DATE DESC);

-- 住院结算明细表索引
CREATE INDEX IDX_SETTLE_ZY_DETAIL_HISID ON SETTLE_ZY_DETAIL(HISID, BILL_DATE);
CREATE INDEX IDX_SETTLE_ZY_DETAIL_ITEM ON SETTLE_ZY_DETAIL(ITEM_ID, ITEM_ID_HOSP);
CREATE INDEX IDX_SETTLE_ZY_DETAIL_COST ON SETTLE_ZY_DETAIL(COST DESC, P_CATEGORY);
CREATE INDEX IDX_SETTLE_ZY_DETAIL_DOCTOR ON SETTLE_ZY_DETAIL(DOCTOR_ID, BILLING_TIME DESC);
CREATE INDEX IDX_SETTLE_ZY_DETAIL_DEPT ON SETTLE_ZY_DETAIL(BILLING_DEPT_ID, EXCUTE_DEPT_ID);

-- 门诊结算主单索引
CREATE INDEX IDX_SETTLE_MZ_PATIENT ON SETTLE_MZ(SOCIAL_ID, PATIENT_NAME);
CREATE INDEX IDX_SETTLE_MZ_HOSPITAL ON SETTLE_MZ(HOSPITAL_ID, BILL_DATE DESC);
CREATE INDEX IDX_SETTLE_MZ_DOCTOR ON SETTLE_MZ(DOCTOR_ID, ADMISSION_DATE DESC);
CREATE INDEX IDX_SETTLE_MZ_DEPT ON SETTLE_MZ(ADMISSION_DEPT_ID);
CREATE INDEX IDX_SETTLE_MZ_COST ON SETTLE_MZ(TOTAL_AMOUNT DESC, BMI_PAY_AMOUNT DESC);
CREATE INDEX IDX_SETTLE_MZ_MZH ON SETTLE_MZ(MZH, HOSPITAL_ID);

-- 门诊结算明细表索引
CREATE INDEX IDX_SETTLE_MZ_DETAIL_HISID ON SETTLE_MZ_DETAIL(HISID, BILL_DATE);
CREATE INDEX IDX_SETTLE_MZ_DETAIL_ITEM ON SETTLE_MZ_DETAIL(ITEM_ID, ITEM_ID_HOSP);
CREATE INDEX IDX_SETTLE_MZ_DETAIL_COST ON SETTLE_MZ_DETAIL(COST DESC, P_CATEGORY);
CREATE INDEX IDX_SETTLE_MZ_DETAIL_DOCTOR ON SETTLE_MZ_DETAIL(DOCTOR_ID, BILLING_TIME DESC);
CREATE INDEX IDX_SETTLE_MZ_DETAIL_PRESCRIPTION ON SETTLE_MZ_DETAIL(PRESCRIPTION, BILL_DATE);

-- 创建序列
CREATE SEQUENCE SEQ_SETTLE_ZY START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SETTLE_ZY_DIAGNOSIS START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SETTLE_ZY_SURGERY START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SETTLE_ZY_DETAIL START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SETTLE_MZ START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SETTLE_MZ_DETAIL START WITH 1 INCREMENT BY 1 CACHE 100;

-- 创建触发器
CREATE OR REPLACE TRIGGER TRG_SETTLE_ZY_UPD
BEFORE UPDATE ON SETTLE_ZY
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
    
    -- 验证日期逻辑
    IF :NEW.ADMISSION_DATE IS NOT NULL AND :NEW.DISCHARGE_DATE IS NOT NULL THEN
        IF :NEW.DISCHARGE_DATE < :NEW.ADMISSION_DATE THEN
            RAISE_APPLICATION_ERROR(-20001, '出院时间不能早于入院时间');
        END IF;
    END IF;
    
    IF :NEW.BILL_DATE IS NOT NULL AND :NEW.DISCHARGE_DATE IS NOT NULL THEN
        IF :NEW.BILL_DATE < :NEW.DISCHARGE_DATE THEN
            RAISE_APPLICATION_ERROR(-20002, '结算日期不能早于出院时间');
        END IF;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER TRG_SETTLE_MZ_UPD
BEFORE UPDATE ON SETTLE_MZ
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
    
    -- 自动计算年月
    IF :NEW.BILL_DATE IS NOT NULL THEN
        :NEW.YEAR := TO_CHAR(:NEW.BILL_DATE, 'YYYY');
        :NEW.MONTH := TO_CHAR(:NEW.BILL_DATE, 'MM');
    END IF;
END;
/

-- 创建视图 - 住院案例完整信息
CREATE OR REPLACE VIEW VW_SETTLE_ZY_COMPLETE AS
SELECT 
    sz.ID,
    sz.HOSPITAL_ID,
    sz.HOSPITAL_NAME,
    sz.BRIDGE_ID,
    sz.HISID,
    sz.PATIENT_NAME,
    sz.ID_CARD,
    sz.PATIENT_GENDER,
    sz.PATIENT_AGE,
    sz.BENEFIT_TYPE,
    sz.BENEFIT_GROUP_ID,
    sz.ADMISSION_DEPT_NAME,
    sz.DISCHARGE_DEPT_NAME,
    sz.DOCTOR_NAME,
    sz.ADMISSION_DATE,
    sz.DISCHARGE_DATE,
    sz.ZYTS,
    sz.BILL_DATE,
    sz.TOTAL_AMOUNT,
    sz.BMI_PAY_AMOUNT,
    sz.SELF_PAY_AMOUNT + sz.CASH AS PERSONAL_PAYMENT,
    sz.CLAIM_TYPE,
    sz.IF_LOCAL_FLAG,
    COUNT(szd.ID) AS DIAGNOSIS_COUNT,
    COUNT(szs.ID) AS SURGERY_COUNT,
    COUNT(szdt.ID) AS COST_ITEM_COUNT,
    SUM(szdt.COST) AS TOTAL_DETAIL_COST,
    sz.CREATED_AT,
    sz.UPDATED_AT
FROM SETTLE_ZY sz
LEFT JOIN SETTLE_ZY_DIAGNOSIS szd ON sz.HISID = szd.HISID AND szd.IS_DELETED = 0
LEFT JOIN SETTLE_ZY_SURGERY szs ON sz.HISID = szs.HISID AND szs.IS_DELETED = 0
LEFT JOIN SETTLE_ZY_DETAIL szdt ON sz.HISID = szdt.HISID AND szdt.IS_DELETED = 0
WHERE sz.IS_DELETED = 0
GROUP BY sz.ID, sz.HOSPITAL_ID, sz.HOSPITAL_NAME, sz.BRIDGE_ID, sz.HISID,
         sz.PATIENT_NAME, sz.ID_CARD, sz.PATIENT_GENDER, sz.PATIENT_AGE,
         sz.BENEFIT_TYPE, sz.BENEFIT_GROUP_ID, sz.ADMISSION_DEPT_NAME,
         sz.DISCHARGE_DEPT_NAME, sz.DOCTOR_NAME, sz.ADMISSION_DATE,
         sz.DISCHARGE_DATE, sz.ZYTS, sz.BILL_DATE, sz.TOTAL_AMOUNT,
         sz.BMI_PAY_AMOUNT, sz.SELF_PAY_AMOUNT, sz.CASH, sz.CLAIM_TYPE,
         sz.IF_LOCAL_FLAG, sz.CREATED_AT, sz.UPDATED_AT;

-- 创建视图 - 门诊案例完整信息
CREATE OR REPLACE VIEW VW_SETTLE_MZ_COMPLETE AS
SELECT 
    sm.ID,
    sm.HOSPITAL_ID,
    sm.HOSPITAL_NAME,
    sm.HISID,
    sm.PATIENT_NAME,
    sm.SOCIAL_ID,
    sm.PATIENT_GENDER,
    sm.PATIENT_AGE,
    sm.BENEFIT_TYPE,
    sm.BENEFIT_GROUP_ID,
    sm.ADMISSION_DEPT_NAME,
    sm.DOCTOR_NAME,
    sm.ADMISSION_DATE,
    sm.BILL_DATE,
    sm.TOTAL_AMOUNT,
    sm.BMI_PAY_AMOUNT,
    sm.SELF_PAY_AMOUNT + sm.CASH AS PERSONAL_PAYMENT,
    sm.CLAIM_TYPE,
    sm.IF_LOCAL_FLAG,
    sm.OUTPATIENT_DISEASE_ID,
    sm.ADMISSION_DISEASE_NAME,
    COUNT(smd.ID) AS COST_ITEM_COUNT,
    SUM(smd.COST) AS TOTAL_DETAIL_COST,
    sm.CREATED_AT,
    sm.UPDATED_AT
FROM SETTLE_MZ sm
LEFT JOIN SETTLE_MZ_DETAIL smd ON sm.HISID = smd.HISID AND smd.IS_DELETED = 0
WHERE sm.IS_DELETED = 0
GROUP BY sm.ID, sm.HOSPITAL_ID, sm.HOSPITAL_NAME, sm.HISID, sm.PATIENT_NAME,
         sm.SOCIAL_ID, sm.PATIENT_GENDER, sm.PATIENT_AGE, sm.BENEFIT_TYPE,
         sm.BENEFIT_GROUP_ID, sm.ADMISSION_DEPT_NAME, sm.DOCTOR_NAME,
         sm.ADMISSION_DATE, sm.BILL_DATE, sm.TOTAL_AMOUNT, sm.BMI_PAY_AMOUNT,
         sm.SELF_PAY_AMOUNT, sm.CASH, sm.CLAIM_TYPE, sm.IF_LOCAL_FLAG,
         sm.OUTPATIENT_DISEASE_ID, sm.ADMISSION_DISEASE_NAME, sm.CREATED_AT, sm.UPDATED_AT;

-- 创建物化视图 - 医疗案例统计
CREATE MATERIALIZED VIEW MV_MEDICAL_STATISTICS
BUILD IMMEDIATE
REFRESH FAST ON DEMAND
AS
SELECT 
    'INPATIENT' AS MEDICAL_TYPE,
    TRUNC(sz.BILL_DATE, 'MM') as MONTH_YEAR,
    sz.HOSPITAL_ID,
    sz.HOSPITAL_NAME,
    sz.BENEFIT_TYPE,
    sz.ADMISSION_DEPT_NAME AS DEPT_NAME,
    sz.BENEFIT_GROUP_ID,
    COUNT(*) as CASE_COUNT,
    AVG(sz.ZYTS) as AVG_STAY_DAYS,
    SUM(sz.TOTAL_AMOUNT) as TOTAL_COST_SUM,
    AVG(sz.TOTAL_AMOUNT) as AVG_TOTAL_COST,
    SUM(sz.BMI_PAY_AMOUNT) as TOTAL_FUND_PAYMENT_SUM,
    AVG(sz.BMI_PAY_AMOUNT) as AVG_FUND_PAYMENT,
    SUM(sz.SELF_PAY_AMOUNT + sz.CASH) as PERSONAL_PAYMENT_SUM,
    AVG(sz.SELF_PAY_AMOUNT + sz.CASH) as AVG_PERSONAL_PAYMENT,
    COUNT(DISTINCT sz.ID_CARD) as UNIQUE_PATIENT_COUNT
FROM SETTLE_ZY sz
WHERE sz.IS_DELETED = 0
  AND sz.BILL_DATE IS NOT NULL
GROUP BY TRUNC(sz.BILL_DATE, 'MM'), sz.HOSPITAL_ID, sz.HOSPITAL_NAME,
         sz.BENEFIT_TYPE, sz.ADMISSION_DEPT_NAME, sz.BENEFIT_GROUP_ID
UNION ALL
SELECT 
    'OUTPATIENT' AS MEDICAL_TYPE,
    TRUNC(sm.BILL_DATE, 'MM') as MONTH_YEAR,
    sm.HOSPITAL_ID,
    sm.HOSPITAL_NAME,
    sm.BENEFIT_TYPE,
    sm.ADMISSION_DEPT_NAME AS DEPT_NAME,
    sm.BENEFIT_GROUP_ID,
    COUNT(*) as CASE_COUNT,
    0 as AVG_STAY_DAYS,
    SUM(sm.TOTAL_AMOUNT) as TOTAL_COST_SUM,
    AVG(sm.TOTAL_AMOUNT) as AVG_TOTAL_COST,
    SUM(sm.BMI_PAY_AMOUNT) as TOTAL_FUND_PAYMENT_SUM,
    AVG(sm.BMI_PAY_AMOUNT) as AVG_FUND_PAYMENT,
    SUM(sm.SELF_PAY_AMOUNT + sm.CASH) as PERSONAL_PAYMENT_SUM,
    AVG(sm.SELF_PAY_AMOUNT + sm.CASH) as AVG_PERSONAL_PAYMENT,
    COUNT(DISTINCT sm.SOCIAL_ID) as UNIQUE_PATIENT_COUNT
FROM SETTLE_MZ sm
WHERE sm.IS_DELETED = 0
  AND sm.BILL_DATE IS NOT NULL
GROUP BY TRUNC(sm.BILL_DATE, 'MM'), sm.HOSPITAL_ID, sm.HOSPITAL_NAME,
         sm.BENEFIT_TYPE, sm.ADMISSION_DEPT_NAME, sm.BENEFIT_GROUP_ID;

-- 创建函数 - 计算案例基础统计
CREATE OR REPLACE FUNCTION FN_GET_SETTLE_STATISTICS(
    p_hisid IN VARCHAR2,
    p_medical_type IN VARCHAR2 DEFAULT 'INPATIENT'
) RETURN VARCHAR2 IS
    v_diagnosis_count NUMBER := 0;
    v_surgery_count NUMBER := 0;
    v_cost_item_count NUMBER := 0;
    v_result VARCHAR2(1000);
BEGIN
    IF p_medical_type = 'INPATIENT' THEN
        -- 计算住院诊断数量
        SELECT COUNT(*) INTO v_diagnosis_count
        FROM SETTLE_ZY_DIAGNOSIS WHERE HISID = p_hisid AND IS_DELETED = 0;
        
        -- 计算手术数量
        SELECT COUNT(*) INTO v_surgery_count
        FROM SETTLE_ZY_SURGERY WHERE HISID = p_hisid AND IS_DELETED = 0;
        
        -- 计算费用项目数量
        SELECT COUNT(*) INTO v_cost_item_count
        FROM SETTLE_ZY_DETAIL WHERE HISID = p_hisid AND IS_DELETED = 0;
        
        v_result := 'DIAGNOSIS:' || v_diagnosis_count || ',SURGERY:' || v_surgery_count || ',COST_ITEMS:' || v_cost_item_count;
    ELSE
        -- 计算门诊费用项目数量
        SELECT COUNT(*) INTO v_cost_item_count
        FROM SETTLE_MZ_DETAIL WHERE HISID = p_hisid AND IS_DELETED = 0;
        
        v_result := 'COST_ITEMS:' || v_cost_item_count;
    END IF;
    
    RETURN v_result;
END;
/

-- 创建同义词和授权
CREATE SYNONYM INPATIENT_CASES FOR VW_SETTLE_ZY_COMPLETE;
CREATE SYNONYM OUTPATIENT_CASES FOR VW_SETTLE_MZ_COMPLETE;
GRANT SELECT ON VW_SETTLE_ZY_COMPLETE TO APP_USER;
GRANT SELECT ON VW_SETTLE_MZ_COMPLETE TO APP_USER;
GRANT SELECT ON MV_MEDICAL_STATISTICS TO APP_USER;
GRANT EXECUTE ON FN_GET_SETTLE_STATISTICS TO APP_USER;

-- 表和字段注释
-- 1. 住院结算主单表注释
COMMENT ON TABLE SETTLE_ZY IS '住院结算主单表';
COMMENT ON COLUMN SETTLE_ZY.ID IS '主键ID';
COMMENT ON COLUMN SETTLE_ZY.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN SETTLE_ZY.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN SETTLE_ZY.BRIDGE_ID IS '病案关联字段';
COMMENT ON COLUMN SETTLE_ZY.HISID IS '结算单据号';
COMMENT ON COLUMN SETTLE_ZY.P_LEVEL IS '医保结算等级';
COMMENT ON COLUMN SETTLE_ZY.BILL_DATE IS '结算日期';
COMMENT ON COLUMN SETTLE_ZY.ZYH IS '住院号';
COMMENT ON COLUMN SETTLE_ZY.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN SETTLE_ZY.SOCIAL_ID IS '患者社会保障号码';
COMMENT ON COLUMN SETTLE_ZY.ID_CARD IS '身份证号';
COMMENT ON COLUMN SETTLE_ZY.BENEFIT_TYPE IS '险种类型';
COMMENT ON COLUMN SETTLE_ZY.BENEFIT_GROUP_ID IS '人员类型';
COMMENT ON COLUMN SETTLE_ZY.ADMISSION_DEPT_ID IS '入院科室编码';
COMMENT ON COLUMN SETTLE_ZY.ADMISSION_DEPT_NAME IS '入院科室名称';
COMMENT ON COLUMN SETTLE_ZY.TRANSFER_DEPT_ID IS '转诊科室编码';
COMMENT ON COLUMN SETTLE_ZY.TRANSFER_DEPT_NAME IS '转科科室名称';
COMMENT ON COLUMN SETTLE_ZY.DISCHARGE_DEPT_ID IS '出院科室编码';
COMMENT ON COLUMN SETTLE_ZY.DISCHARGE_DEPT_NAME IS '出院科室名称';
COMMENT ON COLUMN SETTLE_ZY.DOCTOR_ID IS '主诊医师编码';
COMMENT ON COLUMN SETTLE_ZY.DOCTOR_NAME IS '主诊医师姓名';
COMMENT ON COLUMN SETTLE_ZY.PATIENT_NAME IS '患者姓名';
COMMENT ON COLUMN SETTLE_ZY.PATIENT_GENDER IS '患者性别';
COMMENT ON COLUMN SETTLE_ZY.PATIENT_BIRTHDAY IS '患者出生日期';
COMMENT ON COLUMN SETTLE_ZY.PATIENT_AGE IS '患者年龄';
COMMENT ON COLUMN SETTLE_ZY.BEDID IS '患者床位号';
COMMENT ON COLUMN SETTLE_ZY.NB_TYPE IS '新生儿入院类型';
COMMENT ON COLUMN SETTLE_ZY.NB_BIRTH_WEIGHT IS '新生儿出生体重';
COMMENT ON COLUMN SETTLE_ZY.NB_INPATIENT_WEIGHT IS '新生儿入院体重';
COMMENT ON COLUMN SETTLE_ZY.CLAIM_TYPE IS '住院医疗类型';
COMMENT ON COLUMN SETTLE_ZY.IF_LOCAL_FLAG IS '异地标志';
COMMENT ON COLUMN SETTLE_ZY.ADMISSION_DATE IS '入院日期';
COMMENT ON COLUMN SETTLE_ZY.DISCHARGE_DATE IS '出院日期';
COMMENT ON COLUMN SETTLE_ZY.ZYTS IS '住院天数';
COMMENT ON COLUMN SETTLE_ZY.DISCHARGE_STATUS IS '离院方式';
COMMENT ON COLUMN SETTLE_ZY.TOTAL_AMOUNT IS '医疗总费用';
COMMENT ON COLUMN SETTLE_ZY.BMI_PAY_AMOUNT IS '基本统筹支付';
COMMENT ON COLUMN SETTLE_ZY.CASH IS '个人现金支付';
COMMENT ON COLUMN SETTLE_ZY.SELF_PAY_AMOUNT IS '个人账户支付';
COMMENT ON COLUMN SETTLE_ZY.SELF_PAY_IN IS '个人自付';
COMMENT ON COLUMN SETTLE_ZY.SELF_PAY_OUT IS '个人自费';
COMMENT ON COLUMN SETTLE_ZY.BMI_CONVERED_AMOUNT IS '符合基本医疗保险的费用';
COMMENT ON COLUMN SETTLE_ZY.HOSPITAL_AREA IS '院区';
COMMENT ON COLUMN SETTLE_ZY.MEDICAL_INSURANCE_FLAG IS '医保结算与非医保结算标志';
COMMENT ON COLUMN SETTLE_ZY.REFUND_FLAG_TYPE IS '退费标识';
COMMENT ON COLUMN SETTLE_ZY.REFUND_HISID IS '退费冲销原结算单据号';

-- 2. 住院诊断信息表注释
COMMENT ON TABLE SETTLE_ZY_DIAGNOSIS IS '住院诊断信息表';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.ID IS '主键ID';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.BRIDGE_ID IS '病案关联字段';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.HISID IS '结算单据号';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.ZYH IS '住院号';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.SOCIAL_ID IS '患者社会保障号码';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.ID_CARD IS '身份证号';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.ADMISSION_DISEASE_ID IS '入院诊断编码';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.ADMISSION_DISEASE_NAME IS '入院诊断名称';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.DISCHARGE_DISEASE_ID IS '诊断编码';
COMMENT ON COLUMN SETTLE_ZY_DIAGNOSIS.DISCHARGE_DISEASE_NAME IS '诊断名称';

-- 3. 手术操作信息表注释
COMMENT ON TABLE SETTLE_ZY_SURGERY IS '手术操作信息表';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ID IS '主键ID';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.HISID IS '结算单据号';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ZYH IS '住院号';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.SOCIAL_ID IS '患者社会保障号码';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ID_CARD IS '身份证号';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ICD9_CODE IS '主手术及操作编码';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ICD9_NAME IS '主手术及操作名称';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPRN_OPRT_DATE IS '手术操作日期';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ANST_WAY IS '麻醉方式';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ANST_BEGIN_TIME IS '麻醉开始时间';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ANST_END_TIME IS '麻醉结束时间';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPER_BEGIN_TIME IS '手术开始时间';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPER_END_TIME IS '手术结束时间';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPER_DR_NAME IS '主刀术者医师姓名';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPER_DR_CODE IS '主刀术者医师代码';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPER_OTHER_DR_NAME IS '其他术者医师姓名';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.OPER_OTHER_DR_CODE IS '其他术者医师代码';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ANST_DR_NAME IS '麻醉医师姓名';
COMMENT ON COLUMN SETTLE_ZY_SURGERY.ANST_DR_CODE IS '麻醉医师代码';

-- 4. 住院结算明细表注释
COMMENT ON TABLE SETTLE_ZY_DETAIL IS '住院结算明细表';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ID IS '主键ID';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.HISID IS '结算单据号';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ZYH IS '住院号';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ID_CARD IS '身份证号';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.BILLING_DEPT_ID IS '开单科室编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.BILLING_DEPT_NAME IS '开单科室名称';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.BILLING_TIME IS '项目开单时间';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.USAGE_DATE IS '项目执行时间';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.EXCUTE_DEPT_ID IS '执行科室编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.EXCUTE_DEPT_NAME IS '执行科室名称';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.DOCTOR_ID IS '开单医师编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.DOCTOR_NAME IS '开单医师姓名';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.P_CATEGORY IS '费用类别';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.BILL_DATE IS '结算日期';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.DISCHARGE_MEDICATION IS '出院带药标识';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ITEM_ID_HOSP IS '医院项目编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ITEM_NAME_HOSP IS '医院项目名称';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ITEM_ID IS '医保项目编码';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.ITEM_NAME IS '医保项目名称';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.DRUG_SPEC IS '规格';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.DOSAGE_FORM IS '剂型';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.PACKAGE_UNIT IS '计价单位';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.UNIT_PRICE IS '单价';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.NUM IS '数量';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.COST IS '金额';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.SELF_PAY_LIMIT IS '拒付金额';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.BMI_CONVERED_AMOUNT IS '医保范围内金额';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.P_TYPE IS '支付类别';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.P_TYPE_PCT IS '报销比例';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.REFUND_FLAG_TYPE IS '退费标识';
COMMENT ON COLUMN SETTLE_ZY_DETAIL.REFUND_HISID IS '退费冲销原结算单据号';

-- 5. 门诊结算主单表注释
COMMENT ON TABLE SETTLE_MZ IS '门诊结算主单表';
COMMENT ON COLUMN SETTLE_MZ.ID IS '主键ID';
COMMENT ON COLUMN SETTLE_MZ.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN SETTLE_MZ.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN SETTLE_MZ.HISID IS '结算单据号';
COMMENT ON COLUMN SETTLE_MZ.P_LEVEL IS '医保结算等级';
COMMENT ON COLUMN SETTLE_MZ.BILL_DATE IS '结算日期';
COMMENT ON COLUMN SETTLE_MZ.YEAR IS '结算年份';
COMMENT ON COLUMN SETTLE_MZ.MONTH IS '结算月份';
COMMENT ON COLUMN SETTLE_MZ.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN SETTLE_MZ.SOCIAL_ID IS '患者社会保障号码';
COMMENT ON COLUMN SETTLE_MZ.BENEFIT_TYPE IS '险种类型';
COMMENT ON COLUMN SETTLE_MZ.BENEFIT_GROUP_ID IS '人员类别';
COMMENT ON COLUMN SETTLE_MZ.MZH IS '门诊号';
COMMENT ON COLUMN SETTLE_MZ.ADMISSION_DEPT_ID IS '就诊科室编码';
COMMENT ON COLUMN SETTLE_MZ.ADMISSION_DEPT_NAME IS '就诊科室名称';
COMMENT ON COLUMN SETTLE_MZ.DOCTOR_ID IS '医师编码';
COMMENT ON COLUMN SETTLE_MZ.DOCTOR_NAME IS '医师名称';
COMMENT ON COLUMN SETTLE_MZ.PATIENT_NAME IS '患者姓名';
COMMENT ON COLUMN SETTLE_MZ.PATIENT_GENDER IS '患者性别';
COMMENT ON COLUMN SETTLE_MZ.PATIENT_BIRTHDAY IS '患者出生日期';
COMMENT ON COLUMN SETTLE_MZ.PATIENT_AGE IS '患者年龄';
COMMENT ON COLUMN SETTLE_MZ.CLAIM_TYPE IS '就医类型';
COMMENT ON COLUMN SETTLE_MZ.IF_LOCAL_FLAG IS '异地标志';
COMMENT ON COLUMN SETTLE_MZ.ADMISSION_DATE IS '就诊日期';
COMMENT ON COLUMN SETTLE_MZ.OUTPATIENT_DISEASE_ID IS '诊断编码';
COMMENT ON COLUMN SETTLE_MZ.ADMISSION_DISEASE_NAME IS '诊断名称';
COMMENT ON COLUMN SETTLE_MZ.TOTAL_AMOUNT IS '医疗总费用';
COMMENT ON COLUMN SETTLE_MZ.BMI_PAY_AMOUNT IS '基本统筹支付';
COMMENT ON COLUMN SETTLE_MZ.CASH IS '现金支付';
COMMENT ON COLUMN SETTLE_MZ.SELF_PAY_AMOUNT IS '个人账户支付';
COMMENT ON COLUMN SETTLE_MZ.BMI_CONVERED_AMOUNT IS '符合基本医疗保险的费用';
COMMENT ON COLUMN SETTLE_MZ.HOSPITAL_AREA IS '院区';
COMMENT ON COLUMN SETTLE_MZ.MEDICAL_INSURANCE_FLAG IS '医保结算与非医保结算志';
COMMENT ON COLUMN SETTLE_MZ.REFUND_FLAG_TYPE IS '退费标识';
COMMENT ON COLUMN SETTLE_MZ.REFUND_HISID IS '退费冲销原结算单据号';

-- 6. 门诊结算明细表注释
COMMENT ON TABLE SETTLE_MZ_DETAIL IS '门诊结算明细表';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.ID IS '主键ID';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.HISID IS '结算单据号';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.DISCHARGE_DEPT_ID IS '开单科室编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.DISCHARGE_DEPT_NAME IS '开单科室名称';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.BILLING_TIME IS '项目开单时间';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.EXCUTE_DEPT_ID IS '执行科室编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.EXCUTE_DEPT_NAME IS '执行科室名称';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.USAGE_DATE IS '项目执行时间';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.DOCTOR_ID IS '开单医师编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.DOCTOR_NAME IS '开单医师姓名';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.BILL_DATE IS '结算日期';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.PRESCRIPTION IS '处方号';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.P_CATEGORY IS '费用类别';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.ITEM_ID_HOSP IS '医院项目编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.ITEM_NAME_HOSP IS '医院项目名称';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.ITEM_ID IS '医保项目编码';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.ITEM_NAME IS '医保项目名称';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.DRUG_SPEC IS '规格';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.DOSAGE_FORM IS '剂型';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.PACKAGE_UNIT IS '计价单位';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.UNIT_PRICE IS '单价';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.NUM IS '数量';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.COST IS '金额';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.SELF_PAY_LIMIT IS '拒付金额';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.BMI_CONVERED_AMOUNT IS '医保范围内金额';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.P_TYPE IS '支付类别';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.P_TYPE_PCT IS '报销比例';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.REFUND_FLAG_TYPE IS '退费标识';
COMMENT ON COLUMN SETTLE_MZ_DETAIL.REFUND_HISID IS '退费冲销原结算单据号';

-- 创建表统计信息
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'SETTLE_ZY');
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'SETTLE_ZY_DIAGNOSIS');
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'SETTLE_ZY_SURGERY');
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'SETTLE_ZY_DETAIL');
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'SETTLE_MZ');
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'SETTLE_MZ_DETAIL');

-- 创建数据字典视图
CREATE OR REPLACE VIEW VW_MEDICAL_DATA_DICTIONARY AS
SELECT 
    'SETTLE_ZY' AS TABLE_NAME,
    '住院结算主单表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY'
UNION ALL
SELECT 
    'SETTLE_ZY_DIAGNOSIS' AS TABLE_NAME,
    '住院诊断信息表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY_DIAGNOSIS'
UNION ALL
SELECT 
    'SETTLE_ZY_SURGERY' AS TABLE_NAME,
    '手术操作信息表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY_SURGERY'
UNION ALL
SELECT 
    'SETTLE_ZY_DETAIL' AS TABLE_NAME,
    '住院结算明细表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY_DETAIL'
UNION ALL
SELECT 
    'SETTLE_MZ' AS TABLE_NAME,
    '门诊结算主单表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_MZ'
UNION ALL
SELECT 
    'SETTLE_MZ_DETAIL' AS TABLE_NAME,
    '门诊结算明细表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_MZ_DETAIL'
ORDER BY TABLE_NAME, COLUMN_ID;

-- 创建表关系视图
CREATE OR REPLACE VIEW VW_MEDICAL_TABLE_RELATIONSHIPS AS
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'SETTLE_ZY_DIAGNOSIS' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '住院结算主单与诊断信息关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'SETTLE_ZY_SURGERY' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '住院结算主单与手术信息关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'SETTLE_ZY_DETAIL' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '住院结算主单与费用明细关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_MZ' AS PARENT_TABLE,
    'SETTLE_MZ_DETAIL' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '门诊结算主单与费用明细关联' AS DESCRIPTION
FROM DUAL;

-- 创建数据完整性检查视图
CREATE OR REPLACE VIEW VW_MEDICAL_DATA_INTEGRITY AS
SELECT 
    'SETTLE_ZY' AS TABLE_NAME,
    COUNT(*) AS TOTAL_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 0 THEN 1 END) AS ACTIVE_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 1 THEN 1 END) AS DELETED_RECORDS,
    MIN(BILL_DATE) AS MIN_DATE,
    MAX(BILL_DATE) AS MAX_DATE
FROM SETTLE_ZY
UNION ALL
SELECT 
    'SETTLE_MZ' AS TABLE_NAME,
    COUNT(*) AS TOTAL_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 0 THEN 1 END) AS ACTIVE_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 1 THEN 1 END) AS DELETED_RECORDS,
    MIN(BILL_DATE) AS MIN_DATE,
    MAX(BILL_DATE) AS MAX_DATE
FROM SETTLE_MZ;

-- 授权数据字典视图
GRANT SELECT ON VW_MEDICAL_DATA_DICTIONARY TO APP_USER;
GRANT SELECT ON VW_MEDICAL_TABLE_RELATIONSHIPS TO APP_USER;
GRANT SELECT ON VW_MEDICAL_DATA_INTEGRITY TO APP_USER;

-- =============================================
-- 7. DRG/DIP分组结果表
-- =============================================

-- 创建DRG/DIP分组结果表
CREATE TABLE DRG_DIP_GROUPING (
    ID NUMBER(19) NOT NULL,
    HOSPITAL_ID VARCHAR2(50) NOT NULL,
    HOSPITAL_NAME VARCHAR2(200),
    HISID VARCHAR2(50) NOT NULL,
    PATIENT_ID VARCHAR2(50),
    GROUPING_TYPE VARCHAR2(10) NOT NULL, -- DRG或DIP
    GROUPING_VERSION VARCHAR2(20), -- 分组器版本
    GROUPING_CODE VARCHAR2(50) NOT NULL, -- 分组编码
    GROUPING_NAME VARCHAR2(200), -- 分组名称
    GROUPING_CATEGORY VARCHAR2(100), -- 分组类别
    MDC_CODE VARCHAR2(20), -- 主要诊断类别编码
    MDC_NAME VARCHAR2(100), -- 主要诊断类别名称
    ADRG_CODE VARCHAR2(50), -- ADRG编码
    ADRG_NAME VARCHAR2(200), -- ADRG名称
    DRG_CODE VARCHAR2(50), -- DRG编码
    DRG_NAME VARCHAR2(200), -- DRG名称
    DIP_CODE VARCHAR2(50), -- DIP编码
    DIP_NAME VARCHAR2(200), -- DIP名称
    WEIGHT_VALUE NUMBER(10,4), -- 权重值
    BASE_RATE NUMBER(10,2), -- 基础费率
    PAYMENT_STANDARD NUMBER(10,2), -- 支付标准
    ACTUAL_PAYMENT NUMBER(10,2), -- 实际支付金额
    TOTAL_COST NUMBER(10,2), -- 总费用
    COST_EFFICIENCY_RATIO NUMBER(8,4), -- 费用效率比
    OUTLIER_FLAG VARCHAR2(1) DEFAULT '0', -- 异常值标志 0-正常 1-高倍率 2-低倍率
    OUTLIER_THRESHOLD_HIGH NUMBER(8,4), -- 高倍率阈值
    OUTLIER_THRESHOLD_LOW NUMBER(8,4), -- 低倍率阈值
    COMPLEXITY_LEVEL VARCHAR2(10), -- 复杂程度等级
    COMPLICATION_FLAG VARCHAR2(1) DEFAULT '0', -- 并发症标志
    COMORBIDITY_FLAG VARCHAR2(1) DEFAULT '0', -- 合并症标志
    SEVERITY_LEVEL VARCHAR2(10), -- 严重程度等级
    MORTALITY_RISK VARCHAR2(10), -- 死亡风险等级
    RESOURCE_CONSUMPTION VARCHAR2(10), -- 资源消耗等级
    GROUPING_DATE DATE, -- 分组日期
    GROUPING_RESULT_STATUS VARCHAR2(10) DEFAULT 'VALID', -- 分组结果状态
    QUALITY_SCORE NUMBER(5,2), -- 质量评分
    BENCHMARK_COST NUMBER(10,2), -- 基准费用
    VARIANCE_AMOUNT NUMBER(10,2), -- 费用差异
    VARIANCE_RATE NUMBER(8,4), -- 费用差异率
    PAYMENT_METHOD VARCHAR2(20), -- 支付方式
    ADJUSTMENT_FACTOR NUMBER(8,4) DEFAULT 1, -- 调整系数
    REGIONAL_FACTOR NUMBER(8,4) DEFAULT 1, -- 地区系数
    HOSPITAL_LEVEL_FACTOR NUMBER(8,4) DEFAULT 1, -- 医院等级系数
    TEACHING_FACTOR NUMBER(8,4) DEFAULT 1, -- 教学医院系数
    REMARKS VARCHAR2(500), -- 备注
    CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
    CREATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP,
    UPDATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
    UPDATED_AT TIMESTAMP DEFAULT SYSTIMESTAMP,
    VERSION NUMBER(10) DEFAULT 1,
    IS_DELETED NUMBER(1) DEFAULT 0
);

-- 创建主键
ALTER TABLE DRG_DIP_GROUPING ADD CONSTRAINT PK_DRG_DIP_GROUPING PRIMARY KEY (ID);

-- 创建索引
CREATE INDEX IDX_DRG_DIP_HOSPITAL_HISID ON DRG_DIP_GROUPING(HOSPITAL_ID, HISID);
CREATE INDEX IDX_DRG_DIP_GROUPING_TYPE ON DRG_DIP_GROUPING(GROUPING_TYPE, GROUPING_CODE);
CREATE INDEX IDX_DRG_DIP_GROUPING_DATE ON DRG_DIP_GROUPING(GROUPING_DATE);
CREATE INDEX IDX_DRG_DIP_PAYMENT_STANDARD ON DRG_DIP_GROUPING(PAYMENT_STANDARD);
CREATE INDEX IDX_DRG_DIP_WEIGHT_VALUE ON DRG_DIP_GROUPING(WEIGHT_VALUE);
CREATE INDEX IDX_DRG_DIP_OUTLIER_FLAG ON DRG_DIP_GROUPING(OUTLIER_FLAG);
CREATE INDEX IDX_DRG_DIP_MDC_CODE ON DRG_DIP_GROUPING(MDC_CODE);
CREATE INDEX IDX_DRG_DIP_ADRG_CODE ON DRG_DIP_GROUPING(ADRG_CODE);
CREATE INDEX IDX_DRG_DIP_PATIENT_ID ON DRG_DIP_GROUPING(PATIENT_ID);
CREATE INDEX IDX_DRG_DIP_STATUS ON DRG_DIP_GROUPING(GROUPING_RESULT_STATUS);
CREATE INDEX IDX_DRG_DIP_IS_DELETED ON DRG_DIP_GROUPING(IS_DELETED);

-- 创建序列
CREATE SEQUENCE SEQ_DRG_DIP_GROUPING
START WITH 1
INCREMENT BY 1
NOMAXVALUE
NOCYCLE
CACHE 20;

-- 创建触发器
CREATE OR REPLACE TRIGGER TRG_DRG_DIP_GROUPING_INS
BEFORE INSERT ON DRG_DIP_GROUPING
FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        :NEW.ID := SEQ_DRG_DIP_GROUPING.NEXTVAL;
    END IF;
    
    :NEW.CREATED_AT := SYSTIMESTAMP;
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := 1;
    
    -- 自动计算费用效率比
    IF :NEW.TOTAL_COST IS NOT NULL AND :NEW.PAYMENT_STANDARD IS NOT NULL AND :NEW.PAYMENT_STANDARD > 0 THEN
        :NEW.COST_EFFICIENCY_RATIO := :NEW.TOTAL_COST / :NEW.PAYMENT_STANDARD;
    END IF;
    
    -- 自动计算费用差异
    IF :NEW.TOTAL_COST IS NOT NULL AND :NEW.BENCHMARK_COST IS NOT NULL THEN
        :NEW.VARIANCE_AMOUNT := :NEW.TOTAL_COST - :NEW.BENCHMARK_COST;
        IF :NEW.BENCHMARK_COST > 0 THEN
            :NEW.VARIANCE_RATE := :NEW.VARIANCE_AMOUNT / :NEW.BENCHMARK_COST;
        END IF;
    END IF;
    
    -- 自动判断异常值
    IF :NEW.COST_EFFICIENCY_RATIO IS NOT NULL THEN
        IF :NEW.OUTLIER_THRESHOLD_HIGH IS NOT NULL AND :NEW.COST_EFFICIENCY_RATIO > :NEW.OUTLIER_THRESHOLD_HIGH THEN
            :NEW.OUTLIER_FLAG := '1'; -- 高倍率异常
        ELSIF :NEW.OUTLIER_THRESHOLD_LOW IS NOT NULL AND :NEW.COST_EFFICIENCY_RATIO < :NEW.OUTLIER_THRESHOLD_LOW THEN
            :NEW.OUTLIER_FLAG := '2'; -- 低倍率异常
        ELSE
            :NEW.OUTLIER_FLAG := '0'; -- 正常
        END IF;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER TRG_DRG_DIP_GROUPING_UPD
BEFORE UPDATE ON DRG_DIP_GROUPING
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
    
    -- 自动计算费用效率比
    IF :NEW.TOTAL_COST IS NOT NULL AND :NEW.PAYMENT_STANDARD IS NOT NULL AND :NEW.PAYMENT_STANDARD > 0 THEN
        :NEW.COST_EFFICIENCY_RATIO := :NEW.TOTAL_COST / :NEW.PAYMENT_STANDARD;
    END IF;
    
    -- 自动计算费用差异
    IF :NEW.TOTAL_COST IS NOT NULL AND :NEW.BENCHMARK_COST IS NOT NULL THEN
        :NEW.VARIANCE_AMOUNT := :NEW.TOTAL_COST - :NEW.BENCHMARK_COST;
        IF :NEW.BENCHMARK_COST > 0 THEN
            :NEW.VARIANCE_RATE := :NEW.VARIANCE_AMOUNT / :NEW.BENCHMARK_COST;
        END IF;
    END IF;
    
    -- 自动判断异常值
    IF :NEW.COST_EFFICIENCY_RATIO IS NOT NULL THEN
        IF :NEW.OUTLIER_THRESHOLD_HIGH IS NOT NULL AND :NEW.COST_EFFICIENCY_RATIO > :NEW.OUTLIER_THRESHOLD_HIGH THEN
            :NEW.OUTLIER_FLAG := '1'; -- 高倍率异常
        ELSIF :NEW.OUTLIER_THRESHOLD_LOW IS NOT NULL AND :NEW.COST_EFFICIENCY_RATIO < :NEW.OUTLIER_THRESHOLD_LOW THEN
            :NEW.OUTLIER_FLAG := '2'; -- 低倍率异常
        ELSE
            :NEW.OUTLIER_FLAG := '0'; -- 正常
        END IF;
    END IF;
END;
/

-- 创建DRG/DIP分组结果表注释
COMMENT ON TABLE DRG_DIP_GROUPING IS 'DRG/DIP分组结果表';
COMMENT ON COLUMN DRG_DIP_GROUPING.ID IS '主键ID';
COMMENT ON COLUMN DRG_DIP_GROUPING.HOSPITAL_ID IS '医疗机构编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.HOSPITAL_NAME IS '医疗机构名称';
COMMENT ON COLUMN DRG_DIP_GROUPING.HISID IS '结算单据号';
COMMENT ON COLUMN DRG_DIP_GROUPING.PATIENT_ID IS '个人编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_TYPE IS '分组类型(DRG/DIP)';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_VERSION IS '分组器版本';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_CODE IS '分组编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_NAME IS '分组名称';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_CATEGORY IS '分组类别';
COMMENT ON COLUMN DRG_DIP_GROUPING.MDC_CODE IS '主要诊断类别编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.MDC_NAME IS '主要诊断类别名称';
COMMENT ON COLUMN DRG_DIP_GROUPING.ADRG_CODE IS 'ADRG编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.ADRG_NAME IS 'ADRG名称';
COMMENT ON COLUMN DRG_DIP_GROUPING.DRG_CODE IS 'DRG编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.DRG_NAME IS 'DRG名称';
COMMENT ON COLUMN DRG_DIP_GROUPING.DIP_CODE IS 'DIP编码';
COMMENT ON COLUMN DRG_DIP_GROUPING.DIP_NAME IS 'DIP名称';
COMMENT ON COLUMN DRG_DIP_GROUPING.WEIGHT_VALUE IS '权重值';
COMMENT ON COLUMN DRG_DIP_GROUPING.BASE_RATE IS '基础费率';
COMMENT ON COLUMN DRG_DIP_GROUPING.PAYMENT_STANDARD IS '支付标准';
COMMENT ON COLUMN DRG_DIP_GROUPING.ACTUAL_PAYMENT IS '实际支付金额';
COMMENT ON COLUMN DRG_DIP_GROUPING.TOTAL_COST IS '总费用';
COMMENT ON COLUMN DRG_DIP_GROUPING.COST_EFFICIENCY_RATIO IS '费用效率比';
COMMENT ON COLUMN DRG_DIP_GROUPING.OUTLIER_FLAG IS '异常值标志(0-正常,1-高倍率,2-低倍率)';
COMMENT ON COLUMN DRG_DIP_GROUPING.OUTLIER_THRESHOLD_HIGH IS '高倍率阈值';
COMMENT ON COLUMN DRG_DIP_GROUPING.OUTLIER_THRESHOLD_LOW IS '低倍率阈值';
COMMENT ON COLUMN DRG_DIP_GROUPING.COMPLEXITY_LEVEL IS '复杂程度等级';
COMMENT ON COLUMN DRG_DIP_GROUPING.COMPLICATION_FLAG IS '并发症标志';
COMMENT ON COLUMN DRG_DIP_GROUPING.COMORBIDITY_FLAG IS '合并症标志';
COMMENT ON COLUMN DRG_DIP_GROUPING.SEVERITY_LEVEL IS '严重程度等级';
COMMENT ON COLUMN DRG_DIP_GROUPING.MORTALITY_RISK IS '死亡风险等级';
COMMENT ON COLUMN DRG_DIP_GROUPING.RESOURCE_CONSUMPTION IS '资源消耗等级';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_DATE IS '分组日期';
COMMENT ON COLUMN DRG_DIP_GROUPING.GROUPING_RESULT_STATUS IS '分组结果状态';
COMMENT ON COLUMN DRG_DIP_GROUPING.QUALITY_SCORE IS '质量评分';
COMMENT ON COLUMN DRG_DIP_GROUPING.BENCHMARK_COST IS '基准费用';
COMMENT ON COLUMN DRG_DIP_GROUPING.VARIANCE_AMOUNT IS '费用差异';
COMMENT ON COLUMN DRG_DIP_GROUPING.VARIANCE_RATE IS '费用差异率';
COMMENT ON COLUMN DRG_DIP_GROUPING.PAYMENT_METHOD IS '支付方式';
COMMENT ON COLUMN DRG_DIP_GROUPING.ADJUSTMENT_FACTOR IS '调整系数';
COMMENT ON COLUMN DRG_DIP_GROUPING.REGIONAL_FACTOR IS '地区系数';
COMMENT ON COLUMN DRG_DIP_GROUPING.HOSPITAL_LEVEL_FACTOR IS '医院等级系数';
COMMENT ON COLUMN DRG_DIP_GROUPING.TEACHING_FACTOR IS '教学医院系数';
COMMENT ON COLUMN DRG_DIP_GROUPING.REMARKS IS '备注';
COMMENT ON COLUMN DRG_DIP_GROUPING.CREATED_BY IS '创建人';
COMMENT ON COLUMN DRG_DIP_GROUPING.CREATED_AT IS '创建时间';
COMMENT ON COLUMN DRG_DIP_GROUPING.UPDATED_BY IS '更新人';
COMMENT ON COLUMN DRG_DIP_GROUPING.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN DRG_DIP_GROUPING.VERSION IS '版本号';
COMMENT ON COLUMN DRG_DIP_GROUPING.IS_DELETED IS '删除标志';

-- 创建DRG/DIP分组统计视图
CREATE OR REPLACE VIEW VW_DRG_DIP_STATISTICS AS
SELECT 
    GROUPING_TYPE,
    GROUPING_CODE,
    GROUPING_NAME,
    MDC_CODE,
    MDC_NAME,
    COUNT(*) AS CASE_COUNT,
    AVG(WEIGHT_VALUE) AS AVG_WEIGHT,
    AVG(PAYMENT_STANDARD) AS AVG_PAYMENT_STANDARD,
    AVG(TOTAL_COST) AS AVG_TOTAL_COST,
    AVG(COST_EFFICIENCY_RATIO) AS AVG_EFFICIENCY_RATIO,
    COUNT(CASE WHEN OUTLIER_FLAG = '1' THEN 1 END) AS HIGH_OUTLIER_COUNT,
    COUNT(CASE WHEN OUTLIER_FLAG = '2' THEN 1 END) AS LOW_OUTLIER_COUNT,
    COUNT(CASE WHEN OUTLIER_FLAG = '0' THEN 1 END) AS NORMAL_COUNT,
    ROUND(COUNT(CASE WHEN OUTLIER_FLAG = '1' THEN 1 END) * 100.0 / COUNT(*), 2) AS HIGH_OUTLIER_RATE,
    ROUND(COUNT(CASE WHEN OUTLIER_FLAG = '2' THEN 1 END) * 100.0 / COUNT(*), 2) AS LOW_OUTLIER_RATE,
    AVG(QUALITY_SCORE) AS AVG_QUALITY_SCORE,
    SUM(VARIANCE_AMOUNT) AS TOTAL_VARIANCE_AMOUNT,
    AVG(VARIANCE_RATE) AS AVG_VARIANCE_RATE
FROM DRG_DIP_GROUPING
WHERE IS_DELETED = 0
GROUP BY GROUPING_TYPE, GROUPING_CODE, GROUPING_NAME, MDC_CODE, MDC_NAME
ORDER BY GROUPING_TYPE, CASE_COUNT DESC;

-- 更新数据字典视图以包含DRG/DIP表
CREATE OR REPLACE VIEW VW_MEDICAL_DATA_DICTIONARY AS
SELECT 
    'SETTLE_ZY' AS TABLE_NAME,
    '住院结算主单表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY'
UNION ALL
SELECT 
    'SETTLE_ZY_DIAGNOSIS' AS TABLE_NAME,
    '住院诊断信息表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY_DIAGNOSIS'
UNION ALL
SELECT 
    'SETTLE_ZY_SURGERY' AS TABLE_NAME,
    '手术操作信息表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY_SURGERY'
UNION ALL
SELECT 
    'SETTLE_ZY_DETAIL' AS TABLE_NAME,
    '住院结算明细表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_ZY_DETAIL'
UNION ALL
SELECT 
    'SETTLE_MZ' AS TABLE_NAME,
    '门诊结算主单表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_MZ'
UNION ALL
SELECT 
    'SETTLE_MZ_DETAIL' AS TABLE_NAME,
    '门诊结算明细表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'SETTLE_MZ_DETAIL'
UNION ALL
SELECT 
    'DRG_DIP_GROUPING' AS TABLE_NAME,
    'DRG/DIP分组结果表' AS TABLE_COMMENT,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    COMMENTS AS COLUMN_COMMENT
FROM USER_TAB_COLUMNS utc
LEFT JOIN USER_COL_COMMENTS ucc ON utc.TABLE_NAME = ucc.TABLE_NAME AND utc.COLUMN_NAME = ucc.COLUMN_NAME
WHERE utc.TABLE_NAME = 'DRG_DIP_GROUPING'
ORDER BY TABLE_NAME, COLUMN_ID;

-- 更新表关系视图以包含DRG/DIP关系
CREATE OR REPLACE VIEW VW_MEDICAL_TABLE_RELATIONSHIPS AS
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'SETTLE_ZY_DIAGNOSIS' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '住院结算主单与诊断信息关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'SETTLE_ZY_SURGERY' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '住院结算主单与手术信息关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'SETTLE_ZY_DETAIL' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '住院结算主单与费用明细关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_ZY' AS PARENT_TABLE,
    'DRG_DIP_GROUPING' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对一' AS RELATIONSHIP_TYPE,
    '住院结算主单与DRG/DIP分组结果关联' AS DESCRIPTION
FROM DUAL
UNION ALL
SELECT 
    'SETTLE_MZ' AS PARENT_TABLE,
    'SETTLE_MZ_DETAIL' AS CHILD_TABLE,
    'HISID' AS RELATIONSHIP_COLUMN,
    '一对多' AS RELATIONSHIP_TYPE,
    '门诊结算主单与费用明细关联' AS DESCRIPTION
FROM DUAL;

-- 更新数据完整性检查视图
CREATE OR REPLACE VIEW VW_MEDICAL_DATA_INTEGRITY AS
SELECT 
    'SETTLE_ZY' AS TABLE_NAME,
    COUNT(*) AS TOTAL_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 0 THEN 1 END) AS ACTIVE_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 1 THEN 1 END) AS DELETED_RECORDS,
    MIN(BILL_DATE) AS MIN_DATE,
    MAX(BILL_DATE) AS MAX_DATE
FROM SETTLE_ZY
UNION ALL
SELECT 
    'SETTLE_MZ' AS TABLE_NAME,
    COUNT(*) AS TOTAL_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 0 THEN 1 END) AS ACTIVE_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 1 THEN 1 END) AS DELETED_RECORDS,
    MIN(BILL_DATE) AS MIN_DATE,
    MAX(BILL_DATE) AS MAX_DATE
FROM SETTLE_MZ
UNION ALL
SELECT 
    'DRG_DIP_GROUPING' AS TABLE_NAME,
    COUNT(*) AS TOTAL_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 0 THEN 1 END) AS ACTIVE_RECORDS,
    COUNT(CASE WHEN IS_DELETED = 1 THEN 1 END) AS DELETED_RECORDS,
    MIN(GROUPING_DATE) AS MIN_DATE,
    MAX(GROUPING_DATE) AS MAX_DATE
FROM DRG_DIP_GROUPING;

-- 创建表统计信息
EXEC DBMS_STATS.GATHER_TABLE_STATS(USER, 'DRG_DIP_GROUPING');

-- 授权DRG/DIP相关视图
GRANT SELECT ON VW_DRG_DIP_STATISTICS TO APP_USER;

-- 脚本执行完成提示
SELECT '医疗案例管理模块表结构重新设计完成！' AS MESSAGE FROM DUAL;
SELECT '已创建7个主要数据表：住院结算主单、住院诊断信息、手术操作信息、住院结算明细、门诊结算主单、门诊结算明细、DRG/DIP分组结果' AS DETAILS FROM DUAL;
SELECT '已创建相关索引、序列、触发器、视图、函数和数据字典' AS COMPONENTS FROM DUAL;
SELECT '表结构完全符合提取数据参考文档的要求，并支持DRG/DIP分组管理' AS COMPLIANCE FROM DUAL;