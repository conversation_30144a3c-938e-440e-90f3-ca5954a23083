# 医疗数据提取参考文档

## 1. 住院结算主单（SETTLE_ZY）

**注：提取自医院HIS收费结算系统**

| 序号 | 程序字段名 | 所需字段 | 字段格式 | 主键 | 非空 | 备注 |
|------|------------|----------|----------|------|------|------|
| 1 | HOSPITAL_ID | 医疗机构编码 | VARCHAR(100) | | √ | 医保系统中医疗机构编码（一般取医保结算时上传的本院医疗机构编码） |
| 2 | HOSPITAL_NAME | 医疗机构名称 | VARCHAR(100) | | √ | 医保系统中医疗机构名称（一般取医保结算时上传的本院医疗机构名称） |
| 3 | BRIDGE_ID | 病案关联字段 | VARCHAR(100) | √ | √ | 与病案的关联编码，可关联该病人病案首页相关诊断等信息。 |
| 4 | HISID | 结算单据号 | VARCHAR(100) | √ | √ | 重要字段！用于：1.识别本次住院的唯一id；2.关联字段，关联"住院费用明细"、"住院诊断信息表"、"手术操作信息表"。 |
| 5 | P_LEVEL | 医保结算等级 | VARCHAR(30) | | √ | 0\1\2\3级 |
| 6 | BILL_DATE | 结算日期 | DATE/DATETIME | | √ | |
| 7 | ZYH | 住院号 | VARCHAR(100) | | √ | 病人在医院的住院号，如患者唯一或循环使用请处理成唯一 |
| 8 | PATIENT_ID | 个人编码 | VARCHAR(100) | | √ | 医保患者请提供医保的个人编码、自费患者请提供本院HIS唯一的身份编码，需确保患者唯一 |
| 9 | SOCIAL_ID | 患者社会保障号码 | VARCHAR(100) | | √ | 参保人社保号码（或提取社保卡号），自费为空 |
| 10 | ID_CARD | 身份证号 | VARCHAR(100) | | √ | 患者身份证号 |
| 11 | BENEFIT_TYPE | 险种类型 | VARCHAR(50) | | √ | 市属职工、市属居民、区直（职工）、疆内异地（按医保区划名称填写职工医保，居民）、异地省外等、自费、离休、伤残军人、生育等 |
| 12 | BENEFIT_GROUP_ID | 人员类型 | VARCHAR(30) | | √ | 在岗职工、退休职工、学生、一般居民、低保户、特困供养、孤儿等 |
| 13 | ADMISSION_DEPT_ID | 入院科室编码 | VARCHAR(30) | | | |
| 14 | ADMISSION_DEPT_NAME | 入院科室名称 | VARCHAR(30) | | √ | |
| 15 | TRANSFER_DEPT_ID | 转诊科室编码 | VARCHAR(50) | | | |
| 16 | TRANSFER_DEPT_NAME | 转科科室名称 | VARCHAR(50) | | | 转科科室，多个以"|"隔开 |
| 17 | DISCHARGE_DEPT_ID | 出院科室编码 | VARCHAR(30) | | | |
| 18 | DISCHARGE_DEPT_NAME | 出院科室名称 | VARCHAR(30) | | √ | |
| 19 | DOCTOR_ID | 主诊医师编码 | VARCHAR(30) | | | 该病例主诊医师编码 |
| 20 | DOCTOR_NAME | 主诊医师姓名 | VARCHAR(30) | | √ | 该病例主诊医师姓名 |
| 21 | PATIENT_NAME | 患者姓名 | VARCHAR(50) | | √ | |
| 22 | PATIENT_GENDER | 患者性别 | VARCHAR(30) | | √ | |
| 23 | PATIENT_BIRTHDAY | 患者出生日期 | DATE/DATETIME | | √ | |
| 24 | PATIENT_AGE | 患者年龄 | DECIMAL(16,2) | | √ | 就诊时的年龄 |
| 25 | BEDID | 患者床位号 | VARCHAR(30) | | | 患者床位号，如涉及转科或转床，多个以"|"隔开 |
| 26 | NB_TYPE | 新生儿入院类型 | VARCHAR(30) | | | |
| 27 | NB_BIRTH_WEIGHT | 新生儿出生体重 | DECIMAL(16,2) | | | 单位"G" |
| 28 | NB_INPATIENT_WEIGHT | 新生儿入院体重 | DECIMAL(16,2) | | | 单位"G" |
| 29 | CLAIM_TYPE | 住院医疗类型 | VARCHAR(30) | | √ | 日间手术、普通住院、单病种住院、生育住院等等。按照医院字典的该字段，提取汉字，不用与上述列举名称保持完全一致 |
| 30 | IF_LOCAL_FLAG | 异地标志 | VARCHAR(30) | | √ | 用于区分本地异地就医，请提供："异地"，"本地" |
| 31 | ADMISSION_DATE | 入院日期 | DATE | | √ | |
| 32 | DISCHARGE_DATE | 出院日期 | DATE | | √ | |
| 33 | ZYTS | 住院天数 | DECIMAL(16,2) | | √ | 实际住院天数 |
| 34 | DISCHARGE_STATUS | 离院方式 | VARCHAR(30) | | √ | 1.医嘱离院 2.医嘱转院、转社区、转卫生机构 3.非医嘱离院 4.死亡 9.其他 |
| 35 | TOTAL_AMOUNT | 医疗总费用 | DECIMAL(16,2) | | √ | |
| 36 | BMI_PAY_AMOUNT | 基本统筹支付 | DECIMAL(16,2) | | √ | 基本统筹账户支付的金额 |
| 37 | CASH | 个人现金支付 | DECIMAL(16,2) | | | 现金 |
| 38 | SELF_PAY_AMOUNT | 个人账户支付 | DECIMAL(16,2) | | | 个人账户支付费用 |
| 39 | SELF_PAY_IN | 个人自付 | DECIMAL(16,2) | | | 医保报销范围内需个人承担的费用 |
| 40 | SELF_PAY_OUT | 个人自费 | DECIMAL(16,2) | | | 报销范围外全部由个人承担的费用 |
| 41 | BMI_CONVERED_AMOUNT | 符合基本医疗保险的费用 | DECIMAL(16,2) | | | 纳入医保范畴的费用总额，并非全部医保报销，其中包含按比例由基金支付及个人支付金额 |
| 42 | HOSPITAL_AREA | 院区 | VARCHAR(30) | | √ | 飞检涉及多个院区时，需提供每条结算信息所属院区，院区归属与纸质病例档案归属院区一致。只有一个院区，或只检查一个院区，可为空 |
| 43 | MEDICAL_INSURANCE_FLAG | 医保结算与非医保结算标志 | VARCHAR(30) | | √ | 医保结算：0，非医保结算：1 |
| 44 | REFUND_FLAG_TYPE | 退费标识 | VARCHAR(6) | | √ | 非退费为:0，退费单据为:1 |
| 45 | REFUND_HISID | 退费冲销原结算单据号 | VARCHAR(100) | | √ | 如进行退费冲销，提供冲销的结算单据号HISID |

## 2. 住院诊断信息表

| 序号 | 程序字段名 | 所需字段 | 字段格式 | 主键 | 非空 | 备注 |
|------|------------|----------|----------|------|------|------|
| 1 | HOSPITAL_ID | 医疗机构编码 | VARCHAR(100) | | √ | 医保系统中医疗机构编码（一般取医保结算时上传的本院医疗机构编码） |
| 2 | HOSPITAL_NAME | 医疗机构名称 | VARCHAR(100) | | √ | 医保系统中医疗机构名称（一般取医保结算时上传的本院医疗机构名称） |
| 3 | BRIDGE_ID | 病案关联字段 | VARCHAR(100) | √ | √ | 与病案的关联编码，可关联该病人病案首页相关诊断等信息。 |
| 4 | HISID | 结算单据号 | VARCHAR(100) | √ | √ | 重要字段！用于：1.识别本次住院的唯一id；2.关联字段，关联"住院费用明细"。 |
| 5 | ZYH | 住院号 | VARCHAR(100) | | √ | 病人在医院的住院号，如患者唯一或循环使用请处理成唯一 |
| 6 | PATIENT_ID | 个人编码 | VARCHAR(100) | | √ | 医保患者请提供医保的个人编码、自费患者请提供本院HIS唯一的身份编码，需确保患者唯一 |
| 7 | SOCIAL_ID | 患者社会保障号码 | VARCHAR(100) | | | 参保人社保号码（或提取社保卡号），自费为空 |
| 8 | ID_CARD | 身份证号 | VARCHAR(100) | | √ | 患者身份证号 |
| 9 | ADMISSION_DISEASE_ID | 入院诊断编码 | VARCHAR(200) | | √ | 入院诊断编码，多个按照诊断顺序以"|"隔开,需要提取所有主诊断和副诊断,主诊断在最前 |
| 10 | ADMISSION_DISEASE_NAME | 入院诊断名称 | VARCHAR(200) | | √ | 入院诊断名称，多个按照诊断顺序以"|"隔开,需要提取所有主诊断和副诊断,主诊断在最前 |
| 11 | DISCHARGE_DISEASE_ID | 诊断编码 | VARCHAR(20) | | √ | |
| 12 | DISCHARGE_DISEASE_NAME| 诊断名称 | VARCHAR(50) | | √ | |

## 3. 手术操作信息表

| 序号 | 程序字段名 | 所需字段 | 字段格式 | 主键 | 非空 | 备注 |
|------|------------|----------|----------|------|------|------|
| 1 | HOSPITAL_ID | 医疗机构编码 | VARCHAR(100) | | √ | 医保系统中医疗机构编码（一般取医保结算时上传的本院医疗机构编码） |
| 2 | HOSPITAL_NAME | 医疗机构名称 | VARCHAR(100) | | √ | 医保系统中医疗机构名称（一般取医保结算时上传的本院医疗机构名称） |
| 3 | HISID | 结算单据号 | VARCHAR(100) | √ | √ | 重要字段！用于：1.识别本次住院的唯一id；2.关联字段，关联"住院费用明细"，"住院诊断信息表"、"手术操作信息表" |
| 4 | ZYH | 住院号 | VARCHAR(100) | | √ | 病人在医院的住院号，如患者唯一或循环使用请处理成唯一 |
| 5 | PATIENT_ID | 个人编码 | VARCHAR(100) | | √ | 医保患者请提供医保的个人编码、自费患者请提供本院HIS唯一的身份编码，需确保患者唯一 |
| 6 | SOCIAL_ID | 患者社会保障号码 | VARCHAR(100) | | | 参保人社保号码（或提取社保卡号），自费为空 |
| 7 | ID_CARD | 身份证号 | VARCHAR(100) | | √ | 患者身份证号 |
| 8 | ICD9_CODE | 主手术及操作编码 | VARCHAR(100) | | √ | 主要手术及操作编码 |
| 9 | ICD9_NAME | 主手术及操作名称 | VARCHAR(100) | | √ | 主要手术及操作名称 |
| 12 | OPRN_OPRT_DATE | 手术操作日期 | DATE/DATETIME | | | |
| 13 | ANST_WAY | 麻醉方式 | VARCHAR(100) | | √ | |
| 14 | ANST_BEGIN_TIME | 麻醉开始时间 | DATE/DATETIME | | | |
| 15 | ANST_END_TIME | 麻醉结束时间 | DATE/DATETIME | | | |
| 16 | OPER_BEGIN_TIME | 手术开始时间 | DATE/DATETIME | | | |
| 17 | OPER_END_TIME | 手术结束时间 | DATE/DATETIME | | | |
| 18 | OPER_DR_NAME | 主刀术者医师姓名 | VARCHAR(50) | | √ | |
| 19 | OPER_DR_CODE | 主刀术者医师代码 | VARCHAR(50) | | | |
| 20 | OPER_OTHER_DR_NAME | 其他术者医师姓名 | VARCHAR(50) | | | 多个按照顺序以"|"隔开 |
| 21 | OPER_OTHER_DR_CODE | 其他术者医师代码 | VARCHAR(50) | | | 多个按照顺序以"|"隔开 |
| 22 | ANST_DR_NAME | 麻醉医师姓名 | VARCHAR(50) | | | |
| 23 | ANST_DR_CODE | 麻醉医师代码 | VARCHAR(50) | | | |

## 4. 住院结算明细（SETTLE_ZY_DETAIL）

**注：提取自医院HIS收费结算系统**

| 序号 | 字段 | 名称 | 数据类型 | 主键 | 非空 | 备注 |
|------|------|------|----------|------|------|------|
| 1 | HOSPITAL_ID | 医疗机构编码 | VARCHAR(100) | | √ | 医保系统中医疗机构编码（一般取医保结算时上传的本院医疗机构编码） |
| 2 | HOSPITAL_NAME | 医疗机构名称 | VARCHAR(100) | | √ | 医保系统中医疗机构名称（一般取医保结算时上传的本院医疗机构名称） |
| 3 | HISID | 结算单据号 | VARCHAR(100) | √ | √ | 重要字段！用于：1.识别本次住院的唯一id；2.关联字段，关联"住院费用明细"，"住院诊断信息表"、"手术操作信息表" |
| 4 | PATIENT_ID | 个人编码 | VARCHAR(100) | | √ | 医保患者请提供医保的个人编码、自费患者请提供本院唯一的身份编码 |
| 5 | ZYH | 住院号 | VARCHAR(100) | √ | √ | 参保人在医院的住院号 |
| 6 | ID_CARD | 身份证号 | VARCHAR(100) | | √ | 患者身份证号 |
| 7 | BILLING_DEPT_ID | 开单科室编码 | VARCHAR(30) | | √ | |
| 8 | BILLING_DEPT_NAME | 开单科室名称 | VARCHAR(30) | | √ | |
| 9 | BILLING_TIME | 项目开单时间 | DATE/DATETIME | | | 开具此项目的时点 |
| 10 | USAGE_DATE | 项目执行时间 | DATE/DATETIME | | | 执行此项目时点 |
| 11 | EXCUTE_DEPT_ID | 执行科室编码 | VARCHAR(30) | | √ | |
| 12 | EXCUTE_DEPT_NAME | 执行科室名称 | VARCHAR(30) | | √ | |
| 13 | DOCTOR_ID | 开单医师编码 | VARCHAR(30) | | | 医师编码 |
| 14 | DOCTOR_NAME | 开单医师姓名 | VARCHAR(30) | | √ | 医师名称 |
| 15 | P_CATEGORY | 费用类别 | VARCHAR(30) | | √ | 床位费、诊查费、检查费、化验费、治疗费、手术费、护理费、卫生材料费、西药费、中药饮片费、中成药费、一般诊疗费、挂号费、其他费；如果目前费用类别与此统计口径有差异，按照目前费用类别提取，按照医院字典的该字段，提取汉字，不用与上述列举名称保持完全一致 |
| 16 | BILL_DATE | 结算日期 | DATE | | √ | |
| 17 | DISCHARGE_MEDICATION | 出院带药标识 | VARCHAR(30) | | | 0否，1是，无为空 |
| 18 | ITEM_ID_HOSP | 医院项目编码 | VARCHAR(50) | | √ | 医院HIS结算端项目编码，即院内收费编码。 |
| 19 | ITEM_NAME_HOSP | 医院项目名称 | VARCHAR(100) | | √ | 医院HIS结算端项目名称，即院内收费编码。 |
| 20 | ITEM_ID | 医保项目编码 | VARCHAR(50) | | √ | 医保项目编码，请提取历史数据，自费患者为"" |
| 21 | ITEM_NAME | 医保项目名称 | VARCHAR(100) | | √ | 医保项目名称，请提取历史数据，自费患者为"" |
| 22 | DRUG_SPEC | 规格 | VARCHAR(100) | | | 药品必须提供 |
| 23 | DOSAGE_FORM | 剂型 | VARCHAR(100) | | | 药品必须提供 |
| 24 | PACKAGE_UNIT | 计价单位 | VARCHAR(30) | | | |
| 25 | UNIT_PRICE | 单价 | DECIMAL(16,2) | | √ | 该项目单价，保留两位小数 |
| 26 | NUM | 数量 | DECIMAL(16,2) | | √ | 该项目数量 |
| 27 | COST | 金额 | DECIMAL(16,2) | | √ | 该项目单价*数量 |
| 28 | SELF_PAY_LIMIT | 拒付金额 | DECIMAL(16,2) | | | 部分地区为超限价自付金额 |
| 29 | BMI_CONVERED_AMOUNT | 医保范围内金额 | DECIMAL(16,2) | | √ | 该项目医保范围内金额，自费患者为"" |
| 30 | P_TYPE | 支付类别 | DECIMAL(16,2) | | √ | 指甲、乙、丙类，自费患者为"" |
| 31 | P_TYPE_PCT | 报销比例 | DECIMAL(16,2) | | √ | 甲类为100%，乙类根据当地实际情况（80%，70%），丙类（0%或其他），自费患者为"" |
| 32 | REFUND_FLAG_TYPE | 退费标识 | VARCHAR(2) | | √ | 非退费为:0，退费单据为:1 |
| 33 | REFUND_HISID | 退费冲销原结算单据号 | VARCHAR(100) | | √ | 如进行退费冲销，提供冲销的结算单据号HISID |

## 5. 门诊结算主单（SETTLE_MZ）

**注：提取自医院HIS收费结算系统**

| 序号 | 程序字段名 | 所需字段 | 字段格式 | 主键 | 非空 | 备注 |
|------|------------|----------|----------|------|------|------|
| 1 | HOSPITAL_ID | 医疗机构编码 | VARCHAR(100) | √ | √ | 医保系统中医疗机构编码（一般取医保结算时上传的本院医疗机构编码） |
| 2 | HOSPITAL_NAME | 医疗机构名称 | VARCHAR(100) | | √ | 医保系统中医疗机构名称（一般取医保结算时上传的本院医疗机构编码） |
| 3 | HISID | 结算单据号 | VARCHAR(100) | √ | √ | 重要字段！用于：1.识别本次住院的唯一id；2.关联字段，关联"住院费用明细"，"住院诊断信息表"、"手术操作信息表" |
| 4 | P_LEVEL | 医保结算等级 | VARCHAR(30) | | | 0\1\2\3 |
| 5 | BILL_DATE | 结算日期 | DATE/DATETIME | | √ | |
| 6 | YEAR | 结算年份 | VARCHAR(30) | | | 根据结算日期计算的结算年份 |
| 7 | MONTH | 结算月份 | VARCHAR(30) | | | 根据结算日期计算的结算月份 |
| 8 | PATIENT_ID | 个人编码 | VARCHAR(100) | | √ | 参保人在金保系统中的编号（医院结算能查到的个人编号）、自费患者请提供本院HIS唯一的身份编码，需确保患者唯一 |
| 9 | SOCIAL_ID | 患者社会保障号码 | VARCHAR(50) | | √ | 参保人社保号码（或提取社保卡号），自费为空 |
| 10 | BENEFIT_TYPE | 险种类型 | VARCHAR(50) | | √ | 市属职工、市属居民、省直（职工）、省内异地（省内新农合+省内职工医保+省内居民）、异地省外等、自费、离休、伤残军人、生育。按照医院字典的该字段，提取汉字，不用与上述列举名称保持完全一致 |
| 11 | BENEFIT_GROUP_ID | 人员类别 | VARCHAR(50) | | √ | 在岗职工、退休职工、学生等 |
| 12 | MZH | 门诊号 | VARCHAR(50) | | √ | 患者挂号时产生的门诊流水号，如患者唯一或循环使用请处理成唯一 |
| 13 | ADMISSION_DEPT_ID | 就诊科室编码 | VARCHAR(50) | | √ | |
| 14 | ADMISSION_DEPT_NAME | 就诊科室名称 | VARCHAR(50) | | √ | |
| 15 | DOCTOR_ID | 医师编码 | VARCHAR(50) | | | 该病例主诊医师编码 |
| 16 | DOCTOR_NAME | 医师名称 | VARCHAR(50) | | √ | 该病例主诊医师姓名 |
| 17 | PATIENT_NAME | 患者姓名 | VARCHAR(50) | | √ | |
| 18 | PATIENT_GENDER | 患者性别 | VARCHAR(6) | | √ | |
| 19 | PATIENT_BIRTHDAY | 患者出生日期 | DATE | | √ | YYYYMMDD日期型字段,不含分秒时 |
| 20 | PATIENT_AGE | 患者年龄 | DECIMAL(16,2) | | √ | 就诊时的实际年龄 |
| 21 | CLAIM_TYPE | 就医类型 | VARCHAR(50) | | √ | 普通门诊、急诊、门慢、门特。按照医院字典的该字段，提取汉字 |
| 22 | IF_LOCAL_FLAG | 异地标志 | VARCHAR(50) | | √ | 用于区分本地异地就医，请提供："异地"，"本地" |
| 23 | ADMISSION_DATE | 就诊日期 | DATE | | √ | YYYYMMDD |
| 24 | OUTPATIENT_DISEASE_ID | 诊断编码 | VARCHAR(300) | | √ | 普通门诊、门慢、门特诊断编码，多个按照诊断顺序以"|"隔开，需要提取所有诊断 |
| 25 | ADMISSION_DISEASE_NAME | 诊断名称 | VARCHAR(300) | | √ | 普通门诊、门慢、门特诊断名称，多个按照诊断顺序以"|"隔开，需要提取所有诊断 |
| 26 | TOTAL_AMOUNT | 医疗总费用 | DECIMAL(16,2) | | √ | |
| 27 | BMI_PAY_AMOUNT | 基本统筹支付 | DECIMAL(16,2) | | √ | 结算系统按项目付费基本统筹支持金额 |
| 28 | CASH | 现金支付 | DECIMAL(16,2) | | √ | 现金 |
| 29 | SELF_PAY_AMOUNT | 个人账户支付 | DECIMAL(16,2) | | √ | 个人账户支付费用 |
| 30 | BMI_CONVERED_AMOUNT | 符合基本医疗保险的费用 | DECIMAL(16,2) | | | 纳入医保范畴的费用总额，并非全部医保报销，其中包含按比例由基金支付及个人支付金额 |
| 31 | HOSPITAL_AREA | 院区 | VARCHAR(30) | | √ | 飞检涉及多个院区时，需提供每条结算信息所属院区，院区归属与纸质病例档案归属院区一致。只有一个院区，或只检查一个院区，可为空 |
| 32 | MEDICAL_INSURANCE_FLAG | 医保结算与非医保结算志 | VARCHAR(6) | | √ | 医保结算：0，非医保结算：1 |
| 33 | REFUND_FLAG_TYPE | 退费标识 | VARCHAR(6) | | √ | 退费单据为:1，非退费为:0 |
| 34 | REFUND_HISID | 退费冲销原结算单据号 | VARCHAR(100) | | √ | 如进行退费冲销，提供冲销的结算单据号HISID |

## 6. 门诊结算明细（SETTLE_MZ_DETAIL）

**注：提取自医院HIS收费结算系统**

| 序号 | 程序字段名 | 所需字段 | 字段格式 | 主键 | 非空 | 备注 |
|------|------------|----------|----------|------|------|------|
| 1 | HOSPITAL_ID | 医疗机构编码 | VARCHAR(100) | √ | √ | 医保系统中医疗机构编码（一般取医保结算时上传的本院医疗机构编码） |
| 2 | HOSPITAL_NAME | 医疗机构名称 | VARCHAR(100) | | √ | 医保系统中医疗机构名称（一般取医保结算时上传的本院医疗机构编码） |
| 3 | HISID | 结算单据号 | VARCHAR(100) | √ | √ | 重要字段！用于：1.识别本次住院的唯一id；2.关联字段，关联"住院费用明细"，"住院诊断信息表"、"手术操作信息表" |
| 4 | PATIENT_ID | 个人编码 | VARCHAR(100) | | √ | 医保患者请提供医保的个人编码、自费患者请提供本院HIS唯一的身份编码，需确保患者唯一 |
| 5 | DISCHARGE_DEPT_ID | 开单科室编码 | VARCHAR(50) | | √ | |
| 6 | DISCHARGE_DEPT_NAME | 开单科室名称 | VARCHAR(50) | | √ | |
| 7 | BILLING_TIME | 项目开单时间 | DATE/DATETIME | | √ | 开具此项目时点 |
| 8 | EXCUTE_DEPT_ID | 执行科室编码 | VARCHAR(50) | | √ | |
| 9 | EXCUTE_DEPT_NAME | 执行科室名称 | VARCHAR(50) | | √ | |
| 10 | USAGE_DATE | 项目执行时间 | DATE/DATETIME | | √ | 执行此项目时点 |
| 11 | DOCTOR_ID | 开单医师编码 | VARCHAR(50) | | | |
| 12 | DOCTOR_NAME | 开单医师姓名 | VARCHAR(50) | | √ | |
| 13 | BILL_DATE | 结算日期 | DATE | | √ | |
| 14 | PRESCRIPTION | 处方号 | VARCHAR(100) | | √ | 一个医院的处方号不能出现重复，如出现重复，请加上结算日期 |
| 15 | P_CATEGORY | 费用类别 | VARCHAR(50) | | √ | 床位费、诊查费、检查费、化验费、治疗费、手术费、护理费、卫生材料费、西药费、中药饮片费、中成药费、一般诊疗费、挂号费、其他费；如果目前费用类别与此统计口径有差异，按照目前费用类别提取，按照医院字典的该字段，提取汉字，不用与上述列举名称保持完全一致 |
| 16 | ITEM_ID_HOSP | 医院项目编码 | VARCHAR(100) | | √ | 医院HIS结算端项目编码，请提取历史数据。 |
| 17 | ITEM_NAME_HOSP | 医院项目名称 | VARCHAR(100) | | √ | 医院HIS结算端项目名称，请提取历史数据。 |
| 18 | ITEM_ID | 医保项目编码 | VARCHAR(100) | | √ | 医保项目编码，请提取历史数据，自费患者为"" |
| 19 | ITEM_NAME | 医保项目名称 | VARCHAR(100) | | √ | 医保项目名称，请提取历史数据，自费患者为"" |
| 20 | DRUG_SPEC | 规格 | VARCHAR(100) | | √ | 请提取历史数据，药品必须提供 |
| 21 | DOSAGE_FORM | 剂型 | VARCHAR(100) | | √ | 请提取历史数据，药品必须提供 |
| 22 | PACKAGE_UNIT | 计价单位 | VARCHAR(30) | | √ | 请提取历史数据 |
| 23 | UNIT_PRICE | 单价 | DECIMAL(16,2) | | √ | 该项目单价 |
| 24 | NUM | 数量 | DECIMAL(16,2) | | √ | 该项目数量 |
| 25 | COST | 金额 | DECIMAL(16,2) | | √ | 该项目单价*数量 |
| 26 | SELF_PAY_LIMIT | 拒付金额 | DECIMAL(16,2) | | √ | 部分地区为超限价自付金额，超限价部分=单价*数量，自费患者为"" |
| 27 | BMI_CONVERED_AMOUNT | 医保范围内金额 | DECIMAL(16,2) | | √ | 该项目医保范围内金额，自费患者为空 |
| 28 | P_TYPE | 支付类别 | DECIMAL(16,2) | | √ | 指甲、乙、丙类，自费患者为空 |
| 29 | P_TYPE_PCT | 报销比例 | DECIMAL(16,2) | | √ | 甲类为100%，乙类根据当地实际情况（80%，70%），丙类（0%），自费患者为空 |
| 30 | REFUND_FLAG_TYPE | 处方退费标识 | VARCHAR(30) | | √ | 退费处方为是，非退费为否 |
| 31 | REFUND_HISID | 退费冲销原结算单据号 | VARCHAR(100) | | √ | 如进行退费冲销，提供冲销的结算单据号HISID |