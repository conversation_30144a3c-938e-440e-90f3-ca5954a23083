-- =====================================================
-- 医保基金监管平台 - 系统配置模块优化表结构 (Oracle 19c)
-- =====================================================

-- 1. 系统配置分类表 - 增强分类管理
CREATE TABLE SYSTEM_CONFIG_CATEGORY (
    ID                  NUMBER(19,0) NOT NULL,                      -- 分类ID，主键
    CATEGORY_CODE       VARCHAR2(50) NOT NULL,                      -- 分类编码，唯一标识
    CATEGORY_NAME       VARCHAR2(200) NOT NULL,                     -- 分类名称
    CATEGORY_DESC       VARCHAR2(1000),                             -- 分类描述
    PARENT_ID           NUMBER(19,0),                               -- 父分类ID，用于构建分类树
    CATEGORY_LEVEL      NUMBER(2,0) DEFAULT 1,                      -- 分类层级，从1开始
    CATEGORY_PATH       VARCHAR2(500),                              -- 分类路径，如：/system/database/connection
    DISPLAY_ORDER       NUMBER(5,0) DEFAULT 0,                      -- 显示顺序
    ICON_CLASS          VARCHAR2(100),                              -- 图标样式类
    COLOR_CODE          VARCHAR2(20),                               -- 颜色代码
    ACCESS_LEVEL        VARCHAR2(20) DEFAULT 'NORMAL',              -- 访问级别：LOW-低，NORMAL-普通，HIGH-高，CRITICAL-关键，RESTRICTED-受限
    REQUIRE_APPROVAL    NUMBER(1,0) DEFAULT 0,                      -- 是否需要审批，1-需要，0-不需要
    APPROVAL_WORKFLOW   VARCHAR2(100),                              -- 审批工作流
    ENCRYPTION_REQUIRED NUMBER(1,0) DEFAULT 0,                      -- 是否需要加密，1-需要，0-不需要
    AUDIT_REQUIRED      NUMBER(1,0) DEFAULT 1,                      -- 是否需要审计，1-需要，0-不需要
    BACKUP_REQUIRED     NUMBER(1,0) DEFAULT 1,                      -- 是否需要备份，1-需要，0-不需要
    SYNC_REQUIRED       NUMBER(1,0) DEFAULT 0,                      -- 是否需要同步，1-需要，0-不需要
    SYNC_TARGET         VARCHAR2(200),                              -- 同步目标
    VALIDATION_RULES    JSON,                                       -- 验证规则，JSON格式
    DEFAULT_VALUES      JSON,                                       -- 默认值，JSON格式
    ALLOWED_ROLES       VARCHAR2(1000),                             -- 允许的角色列表
    RESTRICTED_IPS      VARCHAR2(1000),                             -- 受限IP列表
    ENVIRONMENT_SCOPE   VARCHAR2(50) DEFAULT 'ALL',                 -- 环境范围：ALL-全部，DEV-开发，TEST-测试，STAGING-预发布，PROD-生产
    MODULE_SCOPE        VARCHAR2(100),                              -- 模块范围
    TAGS                VARCHAR2(500),                              -- 标签
    METADATA            JSON,                                       -- 元数据，JSON格式
    STATUS              VARCHAR2(20) DEFAULT 'ACTIVE',             -- 状态：ACTIVE-活跃，INACTIVE-非活跃，DEPRECATED-已弃用，ARCHIVED-已归档
    CREATED_BY          NUMBER(19,0) NOT NULL,                      -- 创建人ID
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_BY          NUMBER(19,0),                               -- 更新人ID
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CONSTRAINT PK_SYSTEM_CONFIG_CATEGORY PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_CATEGORY_CODE UNIQUE (CATEGORY_CODE),
    CONSTRAINT FK_SYSTEM_CONFIG_CATEGORY_PARENT FOREIGN KEY (PARENT_ID) REFERENCES SYSTEM_CONFIG_CATEGORY(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_ACCESS_LEVEL CHECK (ACCESS_LEVEL IN ('LOW','NORMAL','HIGH','CRITICAL','RESTRICTED')),
    CONSTRAINT CK_SYSTEM_CONFIG_REQUIRE_APPROVAL CHECK (REQUIRE_APPROVAL IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ENCRYPTION_REQUIRED CHECK (ENCRYPTION_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_AUDIT_REQUIRED CHECK (AUDIT_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_BACKUP_REQUIRED CHECK (BACKUP_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_SYNC_REQUIRED CHECK (SYNC_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ENVIRONMENT_SCOPE CHECK (ENVIRONMENT_SCOPE IN ('ALL','DEV','TEST','STAGING','PROD')),
    CONSTRAINT CK_SYSTEM_CONFIG_CATEGORY_STATUS CHECK (STATUS IN ('ACTIVE','INACTIVE','DEPRECATED','ARCHIVED'))
);

-- 创建索引
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_PARENT ON SYSTEM_CONFIG_CATEGORY(PARENT_ID);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_LEVEL ON SYSTEM_CONFIG_CATEGORY(CATEGORY_LEVEL, DISPLAY_ORDER);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_STATUS ON SYSTEM_CONFIG_CATEGORY(STATUS, ENVIRONMENT_SCOPE);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_MODULE ON SYSTEM_CONFIG_CATEGORY(MODULE_SCOPE, STATUS);

-- 2. 系统配置项表 - 增强配置管理
CREATE TABLE SYSTEM_CONFIG_ITEM (
    ID                  NUMBER(19,0) NOT NULL,                      -- 配置项ID，主键
    CONFIG_KEY          VARCHAR2(200) NOT NULL,                     -- 配置键，唯一标识
    CONFIG_NAME         VARCHAR2(200) NOT NULL,                     -- 配置名称
    CONFIG_DESC         VARCHAR2(1000),                             -- 配置描述
    CATEGORY_ID         NUMBER(19,0) NOT NULL,                      -- 分类ID
    CONFIG_TYPE         VARCHAR2(20) NOT NULL,                      -- 配置类型：SYSTEM-系统，APPLICATION-应用，BUSINESS-业务，SECURITY-安全，PERFORMANCE-性能，FEATURE-功能，INTEGRATION-集成，UI-界面，WORKFLOW-工作流
    DATA_TYPE           VARCHAR2(20) NOT NULL,                      -- 数据类型：STRING-字符串，INTEGER-整数，DECIMAL-小数，BOOLEAN-布尔，JSON-JSON对象，XML-XML，URL-链接，EMAIL-邮箱，PASSWORD-密码，FILE-文件，DATE-日期，DATETIME-日期时间，LIST-列表，MAP-映射
    CONFIG_VALUE        CLOB,                                       -- 配置值
    ENCRYPTED_VALUE     CLOB,                                       -- 加密后的配置值
    DEFAULT_VALUE       CLOB,                                       -- 默认值
    PREVIOUS_VALUE      CLOB,                                       -- 上一个值
    VALUE_FORMAT        VARCHAR2(50),                               -- 值格式
    VALUE_UNIT          VARCHAR2(20),                               -- 值单位
    MIN_VALUE           VARCHAR2(100),                              -- 最小值
    MAX_VALUE           VARCHAR2(100),                              -- 最大值
    ALLOWED_VALUES      JSON,                                       -- 允许的值，JSON格式
    VALIDATION_PATTERN  VARCHAR2(500),                              -- 验证正则表达式
    VALIDATION_FUNCTION VARCHAR2(200),                              -- 验证函数
    VALIDATION_MESSAGE  VARCHAR2(500),                              -- 验证错误信息
    IS_REQUIRED         NUMBER(1,0) DEFAULT 0,                      -- 是否必填，1-必填，0-可选
    IS_ENCRYPTED        NUMBER(1,0) DEFAULT 0,                      -- 是否加密，1-加密，0-明文
    IS_SENSITIVE        NUMBER(1,0) DEFAULT 0,                      -- 是否敏感，1-敏感，0-普通
    IS_READONLY         NUMBER(1,0) DEFAULT 0,                      -- 是否只读，1-只读，0-可写
    IS_SYSTEM           NUMBER(1,0) DEFAULT 0,                      -- 是否系统配置，1-系统，0-业务
    IS_DYNAMIC          NUMBER(1,0) DEFAULT 0,                      -- 是否动态配置，1-动态，0-静态
    REQUIRE_RESTART     NUMBER(1,0) DEFAULT 0,                      -- 是否需要重启，1-需要，0-不需要
    CACHE_ENABLED       NUMBER(1,0) DEFAULT 1,                      -- 是否启用缓存，1-启用，0-禁用
    CACHE_TTL           NUMBER(10,0) DEFAULT 3600,                  -- 缓存生存时间，秒
    REFRESH_INTERVAL    NUMBER(10,0),                               -- 刷新间隔，秒
    PRIORITY_LEVEL      NUMBER(2,0) DEFAULT 5,                      -- 优先级，1-10
    DEPENDENCY_KEYS     VARCHAR2(1000),                             -- 依赖的配置键列表
    IMPACT_ASSESSMENT   VARCHAR2(20) DEFAULT 'LOW',                 -- 影响评估：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键
    CHANGE_FREQUENCY    VARCHAR2(20) DEFAULT 'RARELY',              -- 变更频率：NEVER-从不，RARELY-很少，OCCASIONALLY-偶尔，FREQUENTLY-频繁，CONSTANTLY-持续
    ACCESS_LEVEL        VARCHAR2(20) DEFAULT 'NORMAL',              -- 访问级别：LOW-低，NORMAL-普通，HIGH-高，CRITICAL-关键，RESTRICTED-受限
    ALLOWED_ROLES       VARCHAR2(1000),                             -- 允许的角色列表
    RESTRICTED_IPS      VARCHAR2(1000),                             -- 受限IP列表
    ENVIRONMENT         VARCHAR2(20) DEFAULT 'ALL',                 -- 环境：ALL-全部，DEV-开发，TEST-测试，STAGING-预发布，PROD-生产
    APPLICATION_SCOPE   VARCHAR2(100),                              -- 应用范围
    MODULE_SCOPE        VARCHAR2(100),                              -- 模块范围
    FEATURE_FLAG        VARCHAR2(100),                              -- 功能开关
    AB_TEST_GROUP       VARCHAR2(50),                               -- A/B测试分组
    ROLLOUT_PERCENTAGE  NUMBER(5,2) DEFAULT 100,                    -- 灰度发布百分比
    EFFECTIVE_DATE      TIMESTAMP(6),                               -- 生效日期
    EXPIRY_DATE         TIMESTAMP(6),                               -- 失效日期
    APPROVAL_STATUS     VARCHAR2(20) DEFAULT 'APPROVED',           -- 审批状态：APPROVED-已审批，PENDING-待审批，REJECTED-已拒绝，EXPIRED-已过期
    APPROVAL_WORKFLOW_ID VARCHAR2(50),                              -- 审批工作流ID
    APPROVED_BY         NUMBER(19,0),                               -- 审批人ID
    APPROVED_AT         TIMESTAMP(6),                               -- 审批时间
    APPROVAL_COMMENTS   VARCHAR2(1000),                             -- 审批意见
    LAST_VALIDATED      TIMESTAMP(6),                               -- 最后验证时间
    VALIDATION_STATUS   VARCHAR2(20) DEFAULT 'VALID',               -- 验证状态：VALID-有效，INVALID-无效，PENDING-待验证，ERROR-错误
    VALIDATION_ERROR    VARCHAR2(1000),                             -- 验证错误信息
    USAGE_COUNT         NUMBER(10,0) DEFAULT 0,                     -- 使用次数
    LAST_ACCESSED       TIMESTAMP(6),                               -- 最后访问时间
    ACCESS_FREQUENCY    NUMBER(10,0) DEFAULT 0,                     -- 访问频率
    PERFORMANCE_IMPACT  VARCHAR2(20) DEFAULT 'NONE',                -- 性能影响：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键
    MONITORING_ENABLED  NUMBER(1,0) DEFAULT 0,                      -- 是否启用监控，1-启用，0-禁用
    ALERT_THRESHOLD     VARCHAR2(100),                              -- 告警阈值
    BACKUP_ENABLED      NUMBER(1,0) DEFAULT 1,                      -- 是否启用备份，1-启用，0-禁用
    BACKUP_FREQUENCY    VARCHAR2(20) DEFAULT 'DAILY',               -- 备份频率：REALTIME-实时，HOURLY-每小时，DAILY-每日，WEEKLY-每周，MONTHLY-每月
    SYNC_ENABLED        NUMBER(1,0) DEFAULT 0,                      -- 是否启用同步，1-启用，0-禁用
    SYNC_TARGET         VARCHAR2(200),                              -- 同步目标
    SYNC_STATUS         VARCHAR2(20) DEFAULT 'SYNCED',              -- 同步状态：SYNCED-已同步，PENDING-待同步，ERROR-错误，DISABLED-禁用
    LAST_SYNC_TIME      TIMESTAMP(6),                               -- 最后同步时间
    SYNC_ERROR          VARCHAR2(1000),                             -- 同步错误信息
    VERSION_NUMBER      NUMBER(10,0) DEFAULT 1,                     -- 版本号
    CHANGE_LOG          JSON,                                       -- 变更日志，JSON格式
    TAGS                VARCHAR2(500),                              -- 标签
    CUSTOM_ATTRIBUTES   JSON,                                       -- 自定义属性，JSON格式
    METADATA            JSON,                                       -- 元数据，JSON格式
    STATUS              VARCHAR2(20) DEFAULT 'ACTIVE',             -- 状态：ACTIVE-活跃，INACTIVE-非活跃，DEPRECATED-已弃用，ARCHIVED-已归档，DRAFT-草稿
    CREATED_BY          NUMBER(19,0) NOT NULL,                      -- 创建人ID
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_BY          NUMBER(19,0),                               -- 更新人ID
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CONSTRAINT PK_SYSTEM_CONFIG_ITEM PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_ITEM_KEY UNIQUE (CONFIG_KEY, ENVIRONMENT),
    CONSTRAINT FK_SYSTEM_CONFIG_ITEM_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES SYSTEM_CONFIG_CATEGORY(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_TYPE CHECK (CONFIG_TYPE IN ('SYSTEM','APPLICATION','BUSINESS','SECURITY','PERFORMANCE','FEATURE','INTEGRATION','UI','WORKFLOW')),
    CONSTRAINT CK_SYSTEM_CONFIG_DATA_TYPE CHECK (DATA_TYPE IN ('STRING','INTEGER','DECIMAL','BOOLEAN','JSON','XML','URL','EMAIL','PASSWORD','FILE','DATE','DATETIME','LIST','MAP')),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_REQUIRED CHECK (IS_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_ENCRYPTED CHECK (IS_ENCRYPTED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_SENSITIVE CHECK (IS_SENSITIVE IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_READONLY CHECK (IS_READONLY IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_SYSTEM CHECK (IS_SYSTEM IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_DYNAMIC CHECK (IS_DYNAMIC IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_REQUIRE_RESTART CHECK (REQUIRE_RESTART IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_CACHE_ENABLED CHECK (CACHE_ENABLED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IMPACT_ASSESSMENT CHECK (IMPACT_ASSESSMENT IN ('NONE','LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_FREQUENCY CHECK (CHANGE_FREQUENCY IN ('NEVER','RARELY','OCCASIONALLY','FREQUENTLY','CONSTANTLY')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_ACCESS_LEVEL CHECK (ACCESS_LEVEL IN ('LOW','NORMAL','HIGH','CRITICAL','RESTRICTED')),
    CONSTRAINT CK_SYSTEM_CONFIG_ENVIRONMENT CHECK (ENVIRONMENT IN ('ALL','DEV','TEST','STAGING','PROD')),
    CONSTRAINT CK_SYSTEM_CONFIG_APPROVAL_STATUS CHECK (APPROVAL_STATUS IN ('PENDING','APPROVED','REJECTED','EXPIRED')),
    CONSTRAINT CK_SYSTEM_CONFIG_VALIDATION_STATUS CHECK (VALIDATION_STATUS IN ('VALID','INVALID','PENDING','ERROR')),
    CONSTRAINT CK_SYSTEM_CONFIG_PERFORMANCE_IMPACT CHECK (PERFORMANCE_IMPACT IN ('NONE','LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_CONFIG_MONITORING_ENABLED CHECK (MONITORING_ENABLED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_BACKUP_ENABLED CHECK (BACKUP_ENABLED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_BACKUP_FREQUENCY CHECK (BACKUP_FREQUENCY IN ('REALTIME','HOURLY','DAILY','WEEKLY','MONTHLY')),
    CONSTRAINT CK_SYSTEM_CONFIG_SYNC_ENABLED CHECK (SYNC_ENABLED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_SYNC_STATUS CHECK (SYNC_STATUS IN ('SYNCED','PENDING','ERROR','DISABLED')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_STATUS CHECK (STATUS IN ('ACTIVE','INACTIVE','DEPRECATED','ARCHIVED','DRAFT'))
);

-- 创建索引
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_CATEGORY ON SYSTEM_CONFIG_ITEM(CATEGORY_ID, STATUS);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_TYPE ON SYSTEM_CONFIG_ITEM(CONFIG_TYPE, DATA_TYPE);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_ENV ON SYSTEM_CONFIG_ITEM(ENVIRONMENT, APPLICATION_SCOPE);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_DYNAMIC ON SYSTEM_CONFIG_ITEM(IS_DYNAMIC, CACHE_ENABLED);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_APPROVAL ON SYSTEM_CONFIG_ITEM(APPROVAL_STATUS, APPROVED_AT DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_SYNC ON SYSTEM_CONFIG_ITEM(SYNC_ENABLED, SYNC_STATUS, LAST_SYNC_TIME);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_ACCESS ON SYSTEM_CONFIG_ITEM(LAST_ACCESSED DESC, ACCESS_FREQUENCY DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_VERSION ON SYSTEM_CONFIG_ITEM(CONFIG_KEY, VERSION_NUMBER DESC);

-- 3. 系统配置变更历史表 - 新增变更追踪
CREATE TABLE SYSTEM_CONFIG_CHANGE_HISTORY (
    ID                  NUMBER(19,0) NOT NULL,                      -- 历史记录ID，主键
    CHANGE_ID           VARCHAR2(50) NOT NULL,                      -- 变更ID，唯一标识
    CONFIG_ITEM_ID      NUMBER(19,0) NOT NULL,                      -- 配置项ID
    CONFIG_KEY          VARCHAR2(200) NOT NULL,                     -- 配置键
    CHANGE_TYPE         VARCHAR2(20) NOT NULL,                      -- 变更类型：CREATE-创建，UPDATE-更新，DELETE-删除，ACTIVATE-激活，DEACTIVATE-停用，ARCHIVE-归档，RESTORE-恢复，MIGRATE-迁移
    CHANGE_REASON       VARCHAR2(50) NOT NULL,                      -- 变更原因：BUSINESS_REQUIREMENT-业务需求，BUG_FIX-错误修复，PERFORMANCE_OPTIMIZATION-性能优化，SECURITY_UPDATE-安全更新，COMPLIANCE-合规，MAINTENANCE-维护，UPGRADE-升级，ROLLBACK-回滚，EMERGENCY-紧急
    CHANGE_DESCRIPTION  VARCHAR2(1000),                             -- 变更描述
    OLD_VALUE           CLOB,                                       -- 旧值
    NEW_VALUE           CLOB,                                       -- 新值
    VALUE_DIFF          JSON,                                       -- 值变更差异，JSON格式
    CHANGE_SCOPE        VARCHAR2(50),                               -- 变更范围：SINGLE-单个，MODULE-模块，APPLICATION-应用，SYSTEM-系统，GLOBAL-全局
    IMPACT_ANALYSIS     VARCHAR2(1000),                             -- 影响分析
    ROLLBACK_PLAN       VARCHAR2(1000),                             -- 回滚计划
    VALIDATION_RESULT   VARCHAR2(20),                               -- 验证结果：PASSED-通过，FAILED-失败，WARNING-警告，SKIPPED-跳过
    VALIDATION_DETAILS  JSON,                                       -- 验证详情，JSON格式
    APPROVAL_REQUIRED   NUMBER(1,0) DEFAULT 0,                      -- 是否需要审批，1-需要，0-不需要
    APPROVAL_WORKFLOW_ID VARCHAR2(50),                              -- 审批工作流ID
    APPROVAL_STATUS     VARCHAR2(20) DEFAULT 'PENDING',            -- 审批状态：PENDING-待审批，APPROVED-已审批，REJECTED-已拒绝，EXPIRED-已过期，CANCELLED-已取消
    APPROVED_BY         NUMBER(19,0),                               -- 审批人ID
    APPROVED_AT         TIMESTAMP(6),                               -- 审批时间
    APPROVAL_COMMENTS   VARCHAR2(1000),                             -- 审批意见
    DEPLOYMENT_STATUS   VARCHAR2(20) DEFAULT 'PENDING',            -- 部署状态：PENDING-待部署，IN_PROGRESS-部署中，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消，ROLLED_BACK-已回滚
    DEPLOYED_BY         NUMBER(19,0),                               -- 部署人ID
    DEPLOYED_AT         TIMESTAMP(6),                               -- 部署时间
    DEPLOYMENT_METHOD   VARCHAR2(50),                               -- 部署方式
    DEPLOYMENT_RESULT   VARCHAR2(20),                               -- 部署结果：SUCCESS-成功，PARTIAL-部分成功，FAILED-失败，CANCELLED-已取消
    DEPLOYMENT_ERROR    VARCHAR2(1000),                             -- 部署错误信息
    ROLLBACK_STATUS     VARCHAR2(20),                               -- 回滚状态：NOT_REQUIRED-不需要，AVAILABLE-可回滚，IN_PROGRESS-回滚中，COMPLETED-已完成，FAILED-失败
    ROLLBACK_BY         NUMBER(19,0),                               -- 回滚人ID
    ROLLBACK_AT         TIMESTAMP(6),                               -- 回滚时间
    ROLLBACK_REASON     VARCHAR2(1000),                             -- 回滚原因
    AFFECTED_SYSTEMS    JSON,                                       -- 受影响的系统，JSON格式
    AFFECTED_USERS      JSON,                                       -- 受影响的用户，JSON格式
    DOWNTIME_REQUIRED   NUMBER(1,0) DEFAULT 0,                      -- 是否需要停机，1-需要，0-不需要
    DOWNTIME_DURATION   NUMBER(10,0),                               -- 停机时长，分钟
    MAINTENANCE_WINDOW  VARCHAR2(100),                              -- 维护窗口
    NOTIFICATION_SENT   NUMBER(1,0) DEFAULT 0,                      -- 是否已发送通知，1-已发送，0-未发送
    NOTIFICATION_LIST   VARCHAR2(1000),                             -- 通知列表
    RISK_ASSESSMENT     VARCHAR2(20) DEFAULT 'LOW',                 -- 风险评估：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键
    RISK_MITIGATION     VARCHAR2(1000),                             -- 风险缓解措施
    TESTING_REQUIRED    NUMBER(1,0) DEFAULT 0,                      -- 是否需要测试，1-需要，0-不需要
    TESTING_STATUS      VARCHAR2(20),                               -- 测试状态：NOT_REQUIRED-不需要，PENDING-待测试，IN_PROGRESS-测试中，PASSED-通过，FAILED-失败，SKIPPED-跳过
    TESTING_RESULTS     JSON,                                       -- 测试结果，JSON格式
    PERFORMANCE_IMPACT  VARCHAR2(20) DEFAULT 'NONE',                -- 性能影响：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键
    MONITORING_ENABLED  NUMBER(1,0) DEFAULT 1,                      -- 是否启用监控，1-启用，0-禁用
    MONITORING_DURATION NUMBER(10,0) DEFAULT 24,                    -- 监控持续时间，小时
    TAGS                VARCHAR2(500),                              -- 标签
    CUSTOM_FIELDS       JSON,                                       -- 自定义字段，JSON格式
    METADATA            JSON,                                       -- 元数据，JSON格式
    CREATED_BY          NUMBER(19,0) NOT NULL,                      -- 创建人ID
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 创建时间
    UPDATED_BY          NUMBER(19,0),                               -- 更新人ID
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL, -- 更新时间
    CONSTRAINT PK_SYSTEM_CONFIG_CHANGE_HISTORY PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_CHANGE_ID UNIQUE (CHANGE_ID),
    CONSTRAINT FK_SYSTEM_CONFIG_CHANGE_ITEM FOREIGN KEY (CONFIG_ITEM_ID) REFERENCES SYSTEM_CONFIG_ITEM(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_TYPE CHECK (CHANGE_TYPE IN ('CREATE','UPDATE','DELETE','ACTIVATE','DEACTIVATE','ARCHIVE','RESTORE','MIGRATE')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_REASON CHECK (CHANGE_REASON IN ('BUSINESS_REQUIREMENT','BUG_FIX','PERFORMANCE_OPTIMIZATION','SECURITY_UPDATE','COMPLIANCE','MAINTENANCE','UPGRADE','ROLLBACK','EMERGENCY')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_SCOPE CHECK (CHANGE_SCOPE IN ('SINGLE','MODULE','APPLICATION','SYSTEM','GLOBAL')),
    CONSTRAINT CK_SYSTEM_CONFIG_VALIDATION_RESULT CHECK (VALIDATION_RESULT IN ('PASSED','FAILED','WARNING','SKIPPED')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_APPROVAL_REQUIRED CHECK (APPROVAL_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_APPROVAL_STATUS CHECK (APPROVAL_STATUS IN ('PENDING','APPROVED','REJECTED','EXPIRED','CANCELLED')),
    CONSTRAINT CK_SYSTEM_CONFIG_DEPLOYMENT_STATUS CHECK (DEPLOYMENT_STATUS IN ('PENDING','IN_PROGRESS','COMPLETED','FAILED','CANCELLED','ROLLED_BACK')),
    CONSTRAINT CK_SYSTEM_CONFIG_DEPLOYMENT_RESULT CHECK (DEPLOYMENT_RESULT IN ('SUCCESS','PARTIAL','FAILED','CANCELLED')),
    CONSTRAINT CK_SYSTEM_CONFIG_ROLLBACK_STATUS CHECK (ROLLBACK_STATUS IN ('NOT_REQUIRED','AVAILABLE','IN_PROGRESS','COMPLETED','FAILED')),
    CONSTRAINT CK_SYSTEM_CONFIG_DOWNTIME_REQUIRED CHECK (DOWNTIME_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_NOTIFICATION_SENT CHECK (NOTIFICATION_SENT IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_RISK_ASSESSMENT CHECK (RISK_ASSESSMENT IN ('LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_CONFIG_TESTING_REQUIRED CHECK (TESTING_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_TESTING_STATUS CHECK (TESTING_STATUS IN ('NOT_REQUIRED','PENDING','IN_PROGRESS','PASSED','FAILED','SKIPPED')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_PERFORMANCE_IMPACT CHECK (PERFORMANCE_IMPACT IN ('NONE','LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_CONFIG_CHANGE_MONITORING_ENABLED CHECK (MONITORING_ENABLED IN (0,1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_ITEM ON SYSTEM_CONFIG_CHANGE_HISTORY(CONFIG_ITEM_ID, CREATED_AT DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_TYPE ON SYSTEM_CONFIG_CHANGE_HISTORY(CHANGE_TYPE, CHANGE_REASON, CREATED_AT DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_STATUS ON SYSTEM_CONFIG_CHANGE_HISTORY(DEPLOYMENT_STATUS, APPROVAL_STATUS);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_USER ON SYSTEM_CONFIG_CHANGE_HISTORY(CREATED_BY, CREATED_AT DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_APPROVAL ON SYSTEM_CONFIG_CHANGE_HISTORY(APPROVAL_STATUS, APPROVED_AT DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_CHANGE_DEPLOYMENT ON SYSTEM_CONFIG_CHANGE_HISTORY(DEPLOYMENT_STATUS, DEPLOYED_AT DESC);

-- 4. 系统配置模板表 - 新增配置模板
CREATE TABLE SYSTEM_CONFIG_TEMPLATE (
    ID                  NUMBER(19,0) NOT NULL,                      -- 主键ID
    TEMPLATE_CODE       VARCHAR2(50) NOT NULL,                       -- 模板编码
    TEMPLATE_NAME       VARCHAR2(200) NOT NULL,                      -- 模板名称
    TEMPLATE_DESC       VARCHAR2(1000),                              -- 模板描述
    TEMPLATE_TYPE       VARCHAR2(20) NOT NULL,                       -- 模板类型：BASIC-基础，ADVANCED-高级，CUSTOM-自定义，STANDARD-标准，ENTERPRISE-企业版，STARTER-入门版，PRODUCTION-生产版
    TEMPLATE_CATEGORY   VARCHAR2(50) NOT NULL,                       -- 模板分类：DATABASE-数据库，SECURITY-安全，PERFORMANCE-性能，INTEGRATION-集成，UI-界面，WORKFLOW-工作流，MONITORING-监控，BACKUP-备份，DEPLOYMENT-部署
    TARGET_ENVIRONMENT  VARCHAR2(20),                                -- 目标环境：ALL-全部，DEV-开发，TEST-测试，STAGING-预发布，PROD-生产
    TARGET_APPLICATION  VARCHAR2(100),                               -- 目标应用
    TARGET_MODULE       VARCHAR2(100),                               -- 目标模块
    TEMPLATE_VERSION    VARCHAR2(20) DEFAULT '1.0',                 -- 模板版本
    TEMPLATE_CONTENT    JSON NOT NULL,                               -- 模板内容，JSON格式
    DEFAULT_VALUES      JSON,                                        -- 默认值，JSON格式
    VARIABLE_MAPPINGS   JSON,                                        -- 变量映射，JSON格式
    VALIDATION_RULES    JSON,                                        -- 验证规则，JSON格式
    DEPLOYMENT_SCRIPT   CLOB,                                        -- 部署脚本
    ROLLBACK_SCRIPT     CLOB,                                        -- 回滚脚本
    PREREQUISITES       JSON,                                        -- 前置条件，JSON格式
    DEPENDENCIES        JSON,                                        -- 依赖关系，JSON格式
    COMPATIBILITY_INFO  JSON,                                        -- 兼容性信息，JSON格式
    USAGE_INSTRUCTIONS  CLOB,                                        -- 使用说明
    BEST_PRACTICES      CLOB,                                        -- 最佳实践
    KNOWN_ISSUES        CLOB,                                        -- 已知问题
    CHANGE_LOG          JSON,                                        -- 变更日志，JSON格式
    TAGS                VARCHAR2(500),                               -- 标签
    POPULARITY_SCORE    NUMBER(5,2) DEFAULT 0,                      -- 流行度评分
    USAGE_COUNT         NUMBER(10,0) DEFAULT 0,                      -- 使用次数
    LAST_USED           TIMESTAMP(6),                                -- 最后使用时间
    RATING_AVERAGE      NUMBER(3,2) DEFAULT 0,                      -- 平均评分
    RATING_COUNT        NUMBER(10,0) DEFAULT 0,                      -- 评分次数
    IS_OFFICIAL         NUMBER(1,0) DEFAULT 0,                       -- 是否官方模板，1-是，0-否
    IS_VERIFIED         NUMBER(1,0) DEFAULT 0,                       -- 是否已验证，1-已验证，0-未验证
    IS_DEPRECATED       NUMBER(1,0) DEFAULT 0,                       -- 是否已弃用，1-已弃用，0-未弃用
    DEPRECATION_REASON  VARCHAR2(500),                               -- 弃用原因
    REPLACEMENT_TEMPLATE VARCHAR2(50),                               -- 替代模板编码
    MAINTENANCE_STATUS  VARCHAR2(20) DEFAULT 'ACTIVE',              -- 维护状态：ACTIVE-活跃，MAINTENANCE-维护中，DEPRECATED-已弃用，ARCHIVED-已归档
    MAINTAINED_BY       NUMBER(19,0),                                -- 维护人ID
    LAST_REVIEWED       TIMESTAMP(6),                                -- 最后审查时间
    NEXT_REVIEW_DATE    TIMESTAMP(6),                                -- 下次审查日期
    ACCESS_LEVEL        VARCHAR2(20) DEFAULT 'PUBLIC',               -- 访问级别：PUBLIC-公开，PRIVATE-私有，RESTRICTED-受限，INTERNAL-内部
    ALLOWED_ROLES       VARCHAR2(1000),                              -- 允许的角色列表
    RESTRICTED_USERS    VARCHAR2(1000),                              -- 受限用户列表
    DOWNLOAD_COUNT      NUMBER(10,0) DEFAULT 0,                      -- 下载次数
    FEEDBACK_COUNT      NUMBER(10,0) DEFAULT 0,                      -- 反馈次数
    METADATA            JSON,                                        -- 元数据，JSON格式
    STATUS              VARCHAR2(20) DEFAULT 'ACTIVE',              -- 状态：ACTIVE-活跃，INACTIVE-非活跃，DRAFT-草稿，REVIEW-审查中，APPROVED-已批准，REJECTED-已拒绝，ARCHIVED-已归档
    CREATED_BY          NUMBER(19,0) NOT NULL,                       -- 创建人ID
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CONSTRAINT PK_SYSTEM_CONFIG_TEMPLATE PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_TEMPLATE_CODE UNIQUE (TEMPLATE_CODE),
    CONSTRAINT CK_SYSTEM_CONFIG_TEMPLATE_TYPE CHECK (TEMPLATE_TYPE IN ('BASIC','ADVANCED','CUSTOM','STANDARD','ENTERPRISE','STARTER','PRODUCTION')),
    CONSTRAINT CK_SYSTEM_CONFIG_TEMPLATE_CATEGORY CHECK (TEMPLATE_CATEGORY IN ('DATABASE','SECURITY','PERFORMANCE','INTEGRATION','UI','WORKFLOW','MONITORING','BACKUP','DEPLOYMENT')),
    CONSTRAINT CK_SYSTEM_CONFIG_TEMPLATE_ENVIRONMENT CHECK (TARGET_ENVIRONMENT IN ('ALL','DEV','TEST','STAGING','PROD')),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_OFFICIAL CHECK (IS_OFFICIAL IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_VERIFIED CHECK (IS_VERIFIED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_IS_DEPRECATED CHECK (IS_DEPRECATED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_MAINTENANCE_STATUS CHECK (MAINTENANCE_STATUS IN ('ACTIVE','MAINTENANCE','DEPRECATED','ARCHIVED')),
    CONSTRAINT CK_SYSTEM_CONFIG_TEMPLATE_ACCESS_LEVEL CHECK (ACCESS_LEVEL IN ('PUBLIC','PRIVATE','RESTRICTED','INTERNAL')),
    CONSTRAINT CK_SYSTEM_CONFIG_TEMPLATE_STATUS CHECK (STATUS IN ('ACTIVE','INACTIVE','DRAFT','REVIEW','APPROVED','REJECTED','ARCHIVED'))
);

-- 创建索引
CREATE INDEX IDX_SYSTEM_CONFIG_TEMPLATE_TYPE ON SYSTEM_CONFIG_TEMPLATE(TEMPLATE_TYPE, TEMPLATE_CATEGORY);
CREATE INDEX IDX_SYSTEM_CONFIG_TEMPLATE_ENV ON SYSTEM_CONFIG_TEMPLATE(TARGET_ENVIRONMENT, TARGET_APPLICATION);
CREATE INDEX IDX_SYSTEM_CONFIG_TEMPLATE_POPULARITY ON SYSTEM_CONFIG_TEMPLATE(POPULARITY_SCORE DESC, USAGE_COUNT DESC);
CREATE INDEX IDX_SYSTEM_CONFIG_TEMPLATE_STATUS ON SYSTEM_CONFIG_TEMPLATE(STATUS, MAINTENANCE_STATUS);
CREATE INDEX IDX_SYSTEM_CONFIG_TEMPLATE_ACCESS ON SYSTEM_CONFIG_TEMPLATE(ACCESS_LEVEL, IS_OFFICIAL);
CREATE INDEX IDX_SYSTEM_CONFIG_TEMPLATE_RATING ON SYSTEM_CONFIG_TEMPLATE(RATING_AVERAGE DESC, RATING_COUNT DESC);

-- 5. 创建序列
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_CATEGORY START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_ITEM START WITH 1 INCREMENT BY 1 CACHE 1000;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_CHANGE_HISTORY START WITH 1 INCREMENT BY 1 CACHE 1000;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_TEMPLATE START WITH 1 INCREMENT BY 1 CACHE 100;

-- 6. 创建触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_CATEGORY_UPD
BEFORE UPDATE ON SYSTEM_CONFIG_CATEGORY
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_ITEM_UPD
BEFORE UPDATE ON SYSTEM_CONFIG_ITEM
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION_NUMBER := :OLD.VERSION_NUMBER + 1;
    :NEW.PREVIOUS_VALUE := :OLD.CONFIG_VALUE;
    
    -- 记录变更历史
    IF :NEW.CONFIG_VALUE != :OLD.CONFIG_VALUE OR :NEW.CONFIG_VALUE IS NULL AND :OLD.CONFIG_VALUE IS NOT NULL OR :NEW.CONFIG_VALUE IS NOT NULL AND :OLD.CONFIG_VALUE IS NULL THEN
        INSERT INTO SYSTEM_CONFIG_CHANGE_HISTORY (
            ID, CHANGE_ID, CONFIG_ITEM_ID, CONFIG_KEY, CHANGE_TYPE, CHANGE_REASON,
            OLD_VALUE, NEW_VALUE, CREATED_BY, CREATED_AT
        ) VALUES (
            SEQ_SYSTEM_CONFIG_CHANGE_HISTORY.NEXTVAL,
            'CHG_' || TO_CHAR(SYSTIMESTAMP, 'YYYYMMDDHH24MISS') || '_' || :NEW.ID,
            :NEW.ID, :NEW.CONFIG_KEY, 'UPDATE', 'MANUAL_UPDATE',
            :OLD.CONFIG_VALUE, :NEW.CONFIG_VALUE, :NEW.UPDATED_BY, SYSTIMESTAMP
        );
    END IF;
END;
/

CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_CHANGE_UPD
BEFORE UPDATE ON SYSTEM_CONFIG_CHANGE_HISTORY
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_TEMPLATE_UPD
BEFORE UPDATE ON SYSTEM_CONFIG_TEMPLATE
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

-- 7. 创建视图 - 配置项统计
CREATE OR REPLACE VIEW VW_SYSTEM_CONFIG_STATISTICS AS
SELECT 
    scc.CATEGORY_CODE,
    scc.CATEGORY_NAME,
    COUNT(sci.ID) AS TOTAL_CONFIGS,
    COUNT(CASE WHEN sci.STATUS = 'ACTIVE' THEN 1 END) AS ACTIVE_CONFIGS,
    COUNT(CASE WHEN sci.IS_ENCRYPTED = 1 THEN 1 END) AS ENCRYPTED_CONFIGS,
    COUNT(CASE WHEN sci.IS_DYNAMIC = 1 THEN 1 END) AS DYNAMIC_CONFIGS,
    COUNT(CASE WHEN sci.REQUIRE_RESTART = 1 THEN 1 END) AS RESTART_REQUIRED_CONFIGS,
    COUNT(CASE WHEN sci.APPROVAL_STATUS = 'PENDING' THEN 1 END) AS PENDING_APPROVAL_CONFIGS,
    AVG(sci.ACCESS_FREQUENCY) AS AVG_ACCESS_FREQUENCY,
    MAX(sci.LAST_ACCESSED) AS LAST_ACCESS_TIME
FROM SYSTEM_CONFIG_CATEGORY scc
LEFT JOIN SYSTEM_CONFIG_ITEM sci ON scc.ID = sci.CATEGORY_ID
WHERE scc.STATUS = 'ACTIVE'
GROUP BY scc.CATEGORY_CODE, scc.CATEGORY_NAME;

-- 8. 创建物化视图 - 配置变更趋势
CREATE MATERIALIZED VIEW MV_CONFIG_CHANGE_TREND
REFRESH COMPLETE ON DEMAND
AS
SELECT 
    TRUNC(scch.CREATED_AT) AS CHANGE_DATE,
    scch.CHANGE_TYPE,
    scch.CHANGE_REASON,
    scch.DEPLOYMENT_STATUS,
    COUNT(*) AS CHANGE_COUNT,
    COUNT(CASE WHEN scch.APPROVAL_STATUS = 'APPROVED' THEN 1 END) AS APPROVED_COUNT,
    COUNT(CASE WHEN scch.DEPLOYMENT_RESULT = 'SUCCESS' THEN 1 END) AS SUCCESS_COUNT,
    COUNT(CASE WHEN scch.DEPLOYMENT_RESULT = 'FAILED' THEN 1 END) AS FAILED_COUNT,
    COUNT(CASE WHEN scch.ROLLBACK_STATUS = 'COMPLETED' THEN 1 END) AS ROLLBACK_COUNT,
    COUNT(DISTINCT scch.CREATED_BY) AS UNIQUE_CHANGERS,
    AVG(CASE WHEN scch.DEPLOYED_AT IS NOT NULL AND scch.CREATED_AT IS NOT NULL 
             THEN EXTRACT(HOUR FROM (scch.DEPLOYED_AT - scch.CREATED_AT)) * 60 + 
                  EXTRACT(MINUTE FROM (scch.DEPLOYED_AT - scch.CREATED_AT))
        END) AS AVG_DEPLOYMENT_TIME_MINUTES
FROM SYSTEM_CONFIG_CHANGE_HISTORY scch
WHERE scch.CREATED_AT >= SYSDATE - 90
GROUP BY TRUNC(scch.CREATED_AT), scch.CHANGE_TYPE, scch.CHANGE_REASON, scch.DEPLOYMENT_STATUS;

-- 9. 创建函数 - 获取配置值
CREATE OR REPLACE FUNCTION FN_GET_CONFIG_VALUE(
    p_config_key IN VARCHAR2,
    p_environment IN VARCHAR2 DEFAULT 'PROD',
    p_application IN VARCHAR2 DEFAULT NULL,
    p_decrypt IN NUMBER DEFAULT 0
) RETURN CLOB IS
    v_config_value CLOB;
    v_is_encrypted NUMBER;
    v_cache_enabled NUMBER;
    v_last_accessed TIMESTAMP;
BEGIN
    -- 获取配置值
    SELECT 
        CASE 
            WHEN p_decrypt = 1 AND IS_ENCRYPTED = 1 THEN 
                -- 这里应该调用解密函数
                ENCRYPTED_VALUE
            ELSE 
                CONFIG_VALUE
        END,
        IS_ENCRYPTED,
        CACHE_ENABLED,
        LAST_ACCESSED
    INTO v_config_value, v_is_encrypted, v_cache_enabled, v_last_accessed
    FROM SYSTEM_CONFIG_ITEM
    WHERE CONFIG_KEY = p_config_key
    AND (ENVIRONMENT = p_environment OR ENVIRONMENT = 'ALL')
    AND (APPLICATION_SCOPE = p_application OR APPLICATION_SCOPE IS NULL OR p_application IS NULL)
    AND STATUS = 'ACTIVE'
    AND APPROVAL_STATUS = 'APPROVED'
    AND (EFFECTIVE_DATE IS NULL OR EFFECTIVE_DATE <= SYSTIMESTAMP)
    AND (EXPIRY_DATE IS NULL OR EXPIRY_DATE > SYSTIMESTAMP)
    ORDER BY 
        CASE WHEN ENVIRONMENT = p_environment THEN 1 ELSE 2 END,
        CASE WHEN APPLICATION_SCOPE = p_application THEN 1 ELSE 2 END
    FETCH FIRST 1 ROWS ONLY;
    
    -- 更新访问统计
    UPDATE SYSTEM_CONFIG_ITEM 
    SET 
        USAGE_COUNT = USAGE_COUNT + 1,
        LAST_ACCESSED = SYSTIMESTAMP,
        ACCESS_FREQUENCY = ACCESS_FREQUENCY + 1
    WHERE CONFIG_KEY = p_config_key
    AND (ENVIRONMENT = p_environment OR ENVIRONMENT = 'ALL')
    AND (APPLICATION_SCOPE = p_application OR APPLICATION_SCOPE IS NULL OR p_application IS NULL)
    AND STATUS = 'ACTIVE';
    
    RETURN v_config_value;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN NULL;
    WHEN OTHERS THEN
        RAISE;
END;
/

-- 10. 创建函数 - 验证配置值
CREATE OR REPLACE FUNCTION FN_VALIDATE_CONFIG_VALUE(
    p_config_key IN VARCHAR2,
    p_config_value IN CLOB,
    p_environment IN VARCHAR2 DEFAULT 'PROD'
) RETURN VARCHAR2 IS
    v_data_type VARCHAR2(20);
    v_validation_pattern VARCHAR2(500);
    v_min_value VARCHAR2(100);
    v_max_value VARCHAR2(100);
    v_allowed_values JSON;
    v_validation_function VARCHAR2(200);
    v_is_required NUMBER;
    v_result VARCHAR2(20) := 'VALID';
    v_number_value NUMBER;
    v_date_value DATE;
BEGIN
    -- 获取配置项验证规则
    SELECT 
        DATA_TYPE, VALIDATION_PATTERN, MIN_VALUE, MAX_VALUE, 
        ALLOWED_VALUES, VALIDATION_FUNCTION, IS_REQUIRED
    INTO 
        v_data_type, v_validation_pattern, v_min_value, v_max_value,
        v_allowed_values, v_validation_function, v_is_required
    FROM SYSTEM_CONFIG_ITEM
    WHERE CONFIG_KEY = p_config_key
    AND (ENVIRONMENT = p_environment OR ENVIRONMENT = 'ALL')
    AND STATUS = 'ACTIVE';
    
    -- 检查必填项
    IF v_is_required = 1 AND (p_config_value IS NULL OR LENGTH(TRIM(p_config_value)) = 0) THEN
        RETURN 'INVALID: Required field cannot be empty';
    END IF;
    
    -- 如果值为空且非必填，则跳过验证
    IF p_config_value IS NULL OR LENGTH(TRIM(p_config_value)) = 0 THEN
        RETURN 'VALID';
    END IF;
    
    -- 数据类型验证
    CASE v_data_type
        WHEN 'INTEGER' THEN
            BEGIN
                v_number_value := TO_NUMBER(p_config_value);
                IF v_min_value IS NOT NULL AND v_number_value < TO_NUMBER(v_min_value) THEN
                    RETURN 'INVALID: Value below minimum (' || v_min_value || ')';
                END IF;
                IF v_max_value IS NOT NULL AND v_number_value > TO_NUMBER(v_max_value) THEN
                    RETURN 'INVALID: Value above maximum (' || v_max_value || ')';
                END IF;
            EXCEPTION
                WHEN VALUE_ERROR THEN
                    RETURN 'INVALID: Not a valid integer';
            END;
        WHEN 'DECIMAL' THEN
            BEGIN
                v_number_value := TO_NUMBER(p_config_value);
                IF v_min_value IS NOT NULL AND v_number_value < TO_NUMBER(v_min_value) THEN
                    RETURN 'INVALID: Value below minimum (' || v_min_value || ')';
                END IF;
                IF v_max_value IS NOT NULL AND v_number_value > TO_NUMBER(v_max_value) THEN
                    RETURN 'INVALID: Value above maximum (' || v_max_value || ')';
                END IF;
            EXCEPTION
                WHEN VALUE_ERROR THEN
                    RETURN 'INVALID: Not a valid decimal number';
            END;
        WHEN 'BOOLEAN' THEN
            IF UPPER(p_config_value) NOT IN ('TRUE', 'FALSE', '1', '0', 'YES', 'NO', 'Y', 'N') THEN
                RETURN 'INVALID: Not a valid boolean value';
            END IF;
        WHEN 'DATE' THEN
            BEGIN
                v_date_value := TO_DATE(p_config_value, 'YYYY-MM-DD');
            EXCEPTION
                WHEN OTHERS THEN
                    RETURN 'INVALID: Not a valid date (YYYY-MM-DD format expected)';
            END;
        WHEN 'EMAIL' THEN
            IF NOT REGEXP_LIKE(p_config_value, '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$') THEN
                RETURN 'INVALID: Not a valid email address';
            END IF;
        WHEN 'URL' THEN
            IF NOT REGEXP_LIKE(p_config_value, '^https?://[A-Za-z0-9.-]+') THEN
                RETURN 'INVALID: Not a valid URL';
            END IF;
    END CASE;
    
    -- 正则表达式验证
    IF v_validation_pattern IS NOT NULL THEN
        IF NOT REGEXP_LIKE(p_config_value, v_validation_pattern) THEN
            RETURN 'INVALID: Value does not match required pattern';
        END IF;
    END IF;
    
    -- 允许值列表验证
    IF v_allowed_values IS NOT NULL THEN
        -- 这里需要实现JSON数组包含检查的逻辑
        -- 简化实现，实际应该解析JSON数组
        NULL;
    END IF;
    
    -- 自定义验证函数
    IF v_validation_function IS NOT NULL THEN
        -- 这里可以调用自定义的验证函数
        NULL;
    END IF;
    
    RETURN 'VALID';
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 'INVALID: Configuration key not found';
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
/

-- 11. 创建存储过程 - 批量配置部署
CREATE OR REPLACE PROCEDURE SP_DEPLOY_CONFIG_BATCH(
    p_change_ids IN VARCHAR2, -- 逗号分隔的变更ID列表
    p_deployed_by IN NUMBER,
    p_deployment_method IN VARCHAR2 DEFAULT 'MANUAL'
) IS
    v_change_id VARCHAR2(50);
    v_config_item_id NUMBER;
    v_new_value CLOB;
    v_deployment_result VARCHAR2(20);
    v_error_message VARCHAR2(1000);
    v_affected_count NUMBER := 0;
    v_success_count NUMBER := 0;
    v_failed_count NUMBER := 0;
BEGIN
    -- 解析变更ID列表并逐个部署
    FOR rec IN (
        SELECT TRIM(REGEXP_SUBSTR(p_change_ids, '[^,]+', 1, LEVEL)) AS change_id
        FROM DUAL
        CONNECT BY REGEXP_SUBSTR(p_change_ids, '[^,]+', 1, LEVEL) IS NOT NULL
    ) LOOP
        v_change_id := rec.change_id;
        v_affected_count := v_affected_count + 1;
        
        BEGIN
            -- 获取变更信息
            SELECT CONFIG_ITEM_ID, NEW_VALUE
            INTO v_config_item_id, v_new_value
            FROM SYSTEM_CONFIG_CHANGE_HISTORY
            WHERE CHANGE_ID = v_change_id
            AND APPROVAL_STATUS = 'APPROVED'
            AND DEPLOYMENT_STATUS = 'PENDING';
            
            -- 更新配置项值
            UPDATE SYSTEM_CONFIG_ITEM
            SET CONFIG_VALUE = v_new_value,
                UPDATED_BY = p_deployed_by,
                UPDATED_AT = SYSTIMESTAMP
            WHERE ID = v_config_item_id;
            
            -- 更新部署状态
            UPDATE SYSTEM_CONFIG_CHANGE_HISTORY
            SET DEPLOYMENT_STATUS = 'COMPLETED',
                DEPLOYMENT_RESULT = 'SUCCESS',
                DEPLOYED_BY = p_deployed_by,
                DEPLOYED_AT = SYSTIMESTAMP,
                DEPLOYMENT_METHOD = p_deployment_method
            WHERE CHANGE_ID = v_change_id;
            
            v_success_count := v_success_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                v_error_message := SQLERRM;
                v_failed_count := v_failed_count + 1;
                
                -- 更新失败状态
                UPDATE SYSTEM_CONFIG_CHANGE_HISTORY
                SET DEPLOYMENT_STATUS = 'FAILED',
                    DEPLOYMENT_RESULT = 'FAILED',
                    DEPLOYMENT_ERROR = v_error_message,
                    DEPLOYED_BY = p_deployed_by,
                    DEPLOYED_AT = SYSTIMESTAMP,
                    DEPLOYMENT_METHOD = p_deployment_method
                WHERE CHANGE_ID = v_change_id;
        END;
    END LOOP;
    
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('Deployment completed:');
    DBMS_OUTPUT.PUT_LINE('Total: ' || v_affected_count);
    DBMS_OUTPUT.PUT_LINE('Success: ' || v_success_count);
    DBMS_OUTPUT.PUT_LINE('Failed: ' || v_failed_count);
END;
/

-- 12. 创建存储过程 - 配置同步
CREATE OR REPLACE PROCEDURE SP_SYNC_CONFIG_TO_TARGET(
    p_config_keys IN VARCHAR2, -- 逗号分隔的配置键列表
    p_source_env IN VARCHAR2,
    p_target_env IN VARCHAR2,
    p_sync_by IN NUMBER
) IS
    v_config_key VARCHAR2(200);
    v_config_value CLOB;
    v_target_exists NUMBER;
    v_sync_count NUMBER := 0;
BEGIN
    -- 解析配置键列表并逐个同步
    FOR rec IN (
        SELECT TRIM(REGEXP_SUBSTR(p_config_keys, '[^,]+', 1, LEVEL)) AS config_key
        FROM DUAL
        CONNECT BY REGEXP_SUBSTR(p_config_keys, '[^,]+', 1, LEVEL) IS NOT NULL
    ) LOOP
        v_config_key := rec.config_key;
        
        -- 获取源环境配置值
        SELECT CONFIG_VALUE
        INTO v_config_value
        FROM SYSTEM_CONFIG_ITEM
        WHERE CONFIG_KEY = v_config_key
        AND ENVIRONMENT = p_source_env
        AND STATUS = 'ACTIVE';
        
        -- 检查目标环境是否存在该配置
        SELECT COUNT(*)
        INTO v_target_exists
        FROM SYSTEM_CONFIG_ITEM
        WHERE CONFIG_KEY = v_config_key
        AND ENVIRONMENT = p_target_env;
        
        IF v_target_exists > 0 THEN
            -- 更新现有配置
            UPDATE SYSTEM_CONFIG_ITEM
            SET CONFIG_VALUE = v_config_value,
                SYNC_STATUS = 'SYNCED',
                LAST_SYNC_TIME = SYSTIMESTAMP,
                UPDATED_BY = p_sync_by,
                UPDATED_AT = SYSTIMESTAMP
            WHERE CONFIG_KEY = v_config_key
            AND ENVIRONMENT = p_target_env;
        ELSE
            -- 创建新配置（复制源配置的所有属性）
            INSERT INTO SYSTEM_CONFIG_ITEM (
                ID, CONFIG_KEY, CONFIG_NAME, CONFIG_DESC, CATEGORY_ID,
                CONFIG_TYPE, DATA_TYPE, CONFIG_VALUE, ENVIRONMENT,
                SYNC_STATUS, LAST_SYNC_TIME, CREATED_BY, CREATED_AT
            )
            SELECT 
                SEQ_SYSTEM_CONFIG_ITEM.NEXTVAL, CONFIG_KEY, CONFIG_NAME, CONFIG_DESC, CATEGORY_ID,
                CONFIG_TYPE, DATA_TYPE, v_config_value, p_target_env,
                'SYNCED', SYSTIMESTAMP, p_sync_by, SYSTIMESTAMP
            FROM SYSTEM_CONFIG_ITEM
            WHERE CONFIG_KEY = v_config_key
            AND ENVIRONMENT = p_source_env;
        END IF;
        
        v_sync_count := v_sync_count + 1;
    END LOOP;
    
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('Synchronized ' || v_sync_count || ' configurations from ' || 
                        p_source_env || ' to ' || p_target_env);
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

-- 13. 创建同义词和授权
CREATE SYNONYM CONFIG_STATS FOR VW_SYSTEM_CONFIG_STATISTICS;
GRANT SELECT ON VW_SYSTEM_CONFIG_STATISTICS TO APP_USER;
GRANT SELECT ON MV_CONFIG_CHANGE_TREND TO APP_USER;
GRANT EXECUTE ON FN_GET_CONFIG_VALUE TO APP_USER;
GRANT EXECUTE ON FN_VALIDATE_CONFIG_VALUE TO APP_USER;
GRANT EXECUTE ON SP_DEPLOY_CONFIG_BATCH TO CONFIG_ADMIN;
GRANT EXECUTE ON SP_SYNC_CONFIG_TO_TARGET TO CONFIG_ADMIN;

-- 表和字段注释
-- 1. 系统配置分类表注释
COMMENT ON TABLE SYSTEM_CONFIG_CATEGORY IS '系统配置分类表';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ID IS '分类ID，主键';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_CODE IS '分类编码，唯一标识';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_NAME IS '分类名称';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_DESC IS '分类描述';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.PARENT_ID IS '父分类ID，用于构建分类树';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_LEVEL IS '分类层级，从1开始';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_PATH IS '分类路径，如：/system/database/connection';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.DISPLAY_ORDER IS '显示顺序';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ICON_CLASS IS '图标样式类';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.COLOR_CODE IS '颜色代码';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ACCESS_LEVEL IS '访问级别：LOW-低，NORMAL-普通，HIGH-高，CRITICAL-关键，RESTRICTED-受限';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.REQUIRE_APPROVAL IS '是否需要审批，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.APPROVAL_WORKFLOW IS '审批工作流';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ENCRYPTION_REQUIRED IS '是否需要加密，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.AUDIT_REQUIRED IS '是否需要审计，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.BACKUP_REQUIRED IS '是否需要备份，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.SYNC_REQUIRED IS '是否需要同步，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.SYNC_TARGET IS '同步目标';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.VALIDATION_RULES IS '验证规则，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.DEFAULT_VALUES IS '默认值，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ALLOWED_ROLES IS '允许的角色列表';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.RESTRICTED_IPS IS '受限IP列表';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ENVIRONMENT_SCOPE IS '环境范围：ALL-全部，DEV-开发，TEST-测试，STAGING-预发布，PROD-生产';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.MODULE_SCOPE IS '模块范围';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.STATUS IS '状态：ACTIVE-活跃，INACTIVE-非活跃，DEPRECATED-已弃用，ARCHIVED-已归档';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.UPDATED_AT IS '更新时间';

-- 2. 系统配置项表注释
COMMENT ON TABLE SYSTEM_CONFIG_ITEM IS '系统配置项表';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ID IS '配置项ID，主键';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_KEY IS '配置键，唯一标识';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_NAME IS '配置名称';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_DESC IS '配置描述';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CATEGORY_ID IS '分类ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_TYPE IS '配置类型：SYSTEM-系统，APPLICATION-应用，BUSINESS-业务，SECURITY-安全，PERFORMANCE-性能，FEATURE-功能，INTEGRATION-集成，UI-界面，WORKFLOW-工作流';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.DATA_TYPE IS '数据类型：STRING-字符串，INTEGER-整数，DECIMAL-小数，BOOLEAN-布尔，JSON-JSON对象，XML-XML，URL-链接，EMAIL-邮箱，PASSWORD-密码，FILE-文件，DATE-日期，DATETIME-日期时间，LIST-列表，MAP-映射';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_VALUE IS '配置值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ENCRYPTED_VALUE IS '加密后的配置值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.DEFAULT_VALUE IS '默认值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.PREVIOUS_VALUE IS '上一个值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALUE_FORMAT IS '值格式';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALUE_UNIT IS '值单位';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.MIN_VALUE IS '最小值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.MAX_VALUE IS '最大值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ALLOWED_VALUES IS '允许的值，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALIDATION_PATTERN IS '验证正则表达式';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALIDATION_FUNCTION IS '验证函数';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALIDATION_MESSAGE IS '验证错误信息';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_REQUIRED IS '是否必填，1-必填，0-可选';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_ENCRYPTED IS '是否加密，1-加密，0-明文';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_SENSITIVE IS '是否敏感，1-敏感，0-普通';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_READONLY IS '是否只读，1-只读，0-可写';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_SYSTEM IS '是否系统配置，1-系统，0-业务';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_DYNAMIC IS '是否动态配置，1-动态，0-静态';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.REQUIRE_RESTART IS '是否需要重启，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CACHE_ENABLED IS '是否启用缓存，1-启用，0-禁用';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CACHE_TTL IS '缓存生存时间，秒';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.REFRESH_INTERVAL IS '刷新间隔，秒';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.PRIORITY_LEVEL IS '优先级，1-10';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.DEPENDENCY_KEYS IS '依赖的配置键列表';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IMPACT_ASSESSMENT IS '影响评估：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CHANGE_FREQUENCY IS '变更频率：NEVER-从不，RARELY-很少，OCCASIONALLY-偶尔，FREQUENTLY-频繁，CONSTANTLY-持续';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ACCESS_LEVEL IS '访问级别：LOW-低，NORMAL-普通，HIGH-高，CRITICAL-关键，RESTRICTED-受限';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ALLOWED_ROLES IS '允许的角色列表';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.RESTRICTED_IPS IS '受限IP列表';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ENVIRONMENT IS '环境：ALL-全部，DEV-开发，TEST-测试，STAGING-预发布，PROD-生产';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.APPLICATION_SCOPE IS '应用范围';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.MODULE_SCOPE IS '模块范围';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.FEATURE_FLAG IS '功能开关';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.AB_TEST_GROUP IS 'A/B测试分组';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ROLLOUT_PERCENTAGE IS '灰度发布百分比';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.EFFECTIVE_DATE IS '生效日期';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.EXPIRY_DATE IS '失效日期';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.APPROVAL_STATUS IS '审批状态：APPROVED-已审批，PENDING-待审批，REJECTED-已拒绝，EXPIRED-已过期';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.APPROVAL_WORKFLOW_ID IS '审批工作流ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.APPROVED_BY IS '审批人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.APPROVED_AT IS '审批时间';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.APPROVAL_COMMENTS IS '审批意见';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.LAST_VALIDATED IS '最后验证时间';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALIDATION_STATUS IS '验证状态：VALID-有效，INVALID-无效，PENDING-待验证，ERROR-错误';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VALIDATION_ERROR IS '验证错误信息';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.USAGE_COUNT IS '使用次数';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.LAST_ACCESSED IS '最后访问时间';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ACCESS_FREQUENCY IS '访问频率';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.PERFORMANCE_IMPACT IS '性能影响：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.MONITORING_ENABLED IS '是否启用监控，1-启用，0-禁用';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ALERT_THRESHOLD IS '告警阈值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.BACKUP_ENABLED IS '是否启用备份，1-启用，0-禁用';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.BACKUP_FREQUENCY IS '备份频率：REALTIME-实时，HOURLY-每小时，DAILY-每日，WEEKLY-每周，MONTHLY-每月';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.SYNC_ENABLED IS '是否启用同步，1-启用，0-禁用';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.SYNC_TARGET IS '同步目标';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.SYNC_STATUS IS '同步状态：SYNCED-已同步，PENDING-待同步，ERROR-错误，DISABLED-禁用';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.LAST_SYNC_TIME IS '最后同步时间';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.SYNC_ERROR IS '同步错误信息';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.VERSION_NUMBER IS '版本号';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CHANGE_LOG IS '变更日志，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CUSTOM_ATTRIBUTES IS '自定义属性，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.STATUS IS '状态：ACTIVE-活跃，INACTIVE-非活跃，DEPRECATED-已弃用，ARCHIVED-已归档，DRAFT-草稿';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.UPDATED_AT IS '更新时间';

-- 3. 系统配置变更历史表注释
COMMENT ON TABLE SYSTEM_CONFIG_CHANGE_HISTORY IS '系统配置变更历史表';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.ID IS '历史记录ID，主键';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CHANGE_ID IS '变更ID，唯一标识';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CONFIG_ITEM_ID IS '配置项ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CONFIG_KEY IS '配置键';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CHANGE_TYPE IS '变更类型：CREATE-创建，UPDATE-更新，DELETE-删除，ACTIVATE-激活，DEACTIVATE-停用，ARCHIVE-归档，RESTORE-恢复，MIGRATE-迁移';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CHANGE_REASON IS '变更原因：BUSINESS_REQUIREMENT-业务需求，BUG_FIX-错误修复，PERFORMANCE_OPTIMIZATION-性能优化，SECURITY_UPDATE-安全更新，COMPLIANCE-合规，MAINTENANCE-维护，UPGRADE-升级，ROLLBACK-回滚，EMERGENCY-紧急';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CHANGE_DESCRIPTION IS '变更描述';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.OLD_VALUE IS '旧值';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.NEW_VALUE IS '新值';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.VALUE_DIFF IS '值变更差异，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CHANGE_SCOPE IS '变更范围：SINGLE-单个，MODULE-模块，APPLICATION-应用，SYSTEM-系统，GLOBAL-全局';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.IMPACT_ANALYSIS IS '影响分析';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.ROLLBACK_PLAN IS '回滚计划';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.VALIDATION_RESULT IS '验证结果：PASSED-通过，FAILED-失败，WARNING-警告，SKIPPED-跳过';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.VALIDATION_DETAILS IS '验证详情，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.APPROVAL_REQUIRED IS '是否需要审批，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.APPROVAL_WORKFLOW_ID IS '审批工作流ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.APPROVAL_STATUS IS '审批状态：PENDING-待审批，APPROVED-已审批，REJECTED-已拒绝，EXPIRED-已过期，CANCELLED-已取消';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.APPROVED_BY IS '审批人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.APPROVED_AT IS '审批时间';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.APPROVAL_COMMENTS IS '审批意见';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DEPLOYMENT_STATUS IS '部署状态：PENDING-待部署，IN_PROGRESS-部署中，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消，ROLLED_BACK-已回滚';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DEPLOYED_BY IS '部署人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DEPLOYED_AT IS '部署时间';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DEPLOYMENT_METHOD IS '部署方式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DEPLOYMENT_RESULT IS '部署结果：SUCCESS-成功，PARTIAL-部分成功，FAILED-失败，CANCELLED-已取消';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DEPLOYMENT_ERROR IS '部署错误信息';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.ROLLBACK_STATUS IS '回滚状态：NOT_REQUIRED-不需要，AVAILABLE-可回滚，IN_PROGRESS-回滚中，COMPLETED-已完成，FAILED-失败';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.ROLLBACK_BY IS '回滚人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.ROLLBACK_AT IS '回滚时间';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.ROLLBACK_REASON IS '回滚原因';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.AFFECTED_SYSTEMS IS '受影响的系统，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.AFFECTED_USERS IS '受影响的用户，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DOWNTIME_REQUIRED IS '是否需要停机，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.DOWNTIME_DURATION IS '停机时长，分钟';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.MAINTENANCE_WINDOW IS '维护窗口';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.NOTIFICATION_SENT IS '是否已发送通知，1-已发送，0-未发送';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.NOTIFICATION_LIST IS '通知列表';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.RISK_ASSESSMENT IS '风险评估：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.RISK_MITIGATION IS '风险缓解措施';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.TESTING_REQUIRED IS '是否需要测试，1-需要，0-不需要';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.TESTING_STATUS IS '测试状态：NOT_REQUIRED-不需要，PENDING-待测试，IN_PROGRESS-测试中，PASSED-通过，FAILED-失败，SKIPPED-跳过';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.TESTING_RESULTS IS '测试结果，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.PERFORMANCE_IMPACT IS '性能影响：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-关键';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.MONITORING_ENABLED IS '是否启用监控，1-启用，0-禁用';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.MONITORING_DURATION IS '监控时长，小时';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CUSTOM_FIELDS IS '自定义字段，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CHANGE_HISTORY.UPDATED_AT IS '更新时间';

-- 4. 系统配置模板表注释
COMMENT ON TABLE SYSTEM_CONFIG_TEMPLATE IS '系统配置模板表';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.ID IS '模板ID，主键';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_CODE IS '模板编码，唯一标识';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_NAME IS '模板名称';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_DESC IS '模板描述';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_TYPE IS '模板类型：BASIC-基础，ADVANCED-高级，CUSTOM-自定义，STANDARD-标准，ENTERPRISE-企业，STARTER-入门，PRODUCTION-生产';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_CATEGORY IS '模板分类：DATABASE-数据库，SECURITY-安全，PERFORMANCE-性能，INTEGRATION-集成，UI-界面，WORKFLOW-工作流，MONITORING-监控，BACKUP-备份，DEPLOYMENT-部署';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TARGET_ENVIRONMENT IS '目标环境：ALL-全部，DEV-开发，TEST-测试，STAGING-预发布，PROD-生产';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TARGET_APPLICATION IS '目标应用';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TARGET_MODULE IS '目标模块';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_VERSION IS '模板版本';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TEMPLATE_CONTENT IS '模板内容，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.DEFAULT_VALUES IS '默认值，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.VARIABLE_MAPPINGS IS '变量映射，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.VALIDATION_RULES IS '验证规则，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.DEPLOYMENT_SCRIPT IS '部署脚本';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.ROLLBACK_SCRIPT IS '回滚脚本';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.PREREQUISITES IS '前置条件，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.DEPENDENCIES IS '依赖关系，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.COMPATIBILITY_INFO IS '兼容性信息，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.USAGE_INSTRUCTIONS IS '使用说明';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.BEST_PRACTICES IS '最佳实践';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.KNOWN_ISSUES IS '已知问题';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.CHANGE_LOG IS '变更日志，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.POPULARITY_SCORE IS '模板受欢迎程度评分';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.USAGE_COUNT IS '使用次数';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.LAST_USED IS '最后使用时间';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.RATING_AVERAGE IS '平均评分';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.RATING_COUNT IS '评分次数';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.IS_OFFICIAL IS '是否官方模板，1-官方，0-非官方';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.IS_VERIFIED IS '是否已验证，1-已验证，0-未验证';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.IS_DEPRECATED IS '是否已弃用，1-已弃用，0-正常';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.DEPRECATION_REASON IS '弃用原因';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.REPLACEMENT_TEMPLATE IS '替代模板';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.MAINTENANCE_STATUS IS '维护状态：ACTIVE-活跃，MAINTENANCE-维护中，DEPRECATED-已弃用，ARCHIVED-已归档';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.MAINTAINED_BY IS '维护人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.LAST_REVIEWED IS '最后审查时间';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.NEXT_REVIEW_DATE IS '下次审查日期';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.ACCESS_LEVEL IS '访问级别：PUBLIC-公开，PRIVATE-私有，RESTRICTED-受限，INTERNAL-内部';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.ALLOWED_ROLES IS '允许的角色列表';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.RESTRICTED_USERS IS '受限用户列表';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.DOWNLOAD_COUNT IS '下载次数';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.FEEDBACK_COUNT IS '反馈次数';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.STATUS IS '状态：ACTIVE-活跃，INACTIVE-非活跃，DRAFT-草稿，REVIEW-审查中，APPROVED-已审批，REJECTED-已拒绝，ARCHIVED-已归档';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN SYSTEM_CONFIG_TEMPLATE.UPDATED_AT IS '更新时间';}}}