-- =====================================================
-- 医保基金监管平台 - 知识库模块优化表结构 (Oracle 19c)
-- =====================================================

-- 1. 知识库分类表 - 增强分类管理
CREATE TABLE KNOWLEDGE_CATEGORY (
    ID                  NUMBER(19,0) NOT NULL,                       -- 分类ID，主键
    CATEGORY_CODE       VARCHAR2(50) NOT NULL,                       -- 分类编码，唯一标识
    CATEGORY_NAME       VARCHAR2(100) NOT NULL,                      -- 分类名称
    CATEGORY_DESC       VARCHAR2(500),                               -- 分类描述
    PARENT_ID           NUMBER(19,0),                                -- 父分类ID
    CATEGORY_LEVEL      NUMBER(2,0) DEFAULT 1 NOT NULL,              -- 分类层级（1-5）
    CATEGORY_PATH       VARCHAR2(500),                               -- 分类路径
    DISPLAY_ORDER       NUMBER(5,0) DEFAULT 0,                      -- 显示顺序
    ICON_URL            VARCHAR2(200),                               -- 图标URL
    COLOR_CODE          VARCHAR2(20),                                -- 颜色代码
    CATEGORY_TYPE       VARCHAR2(20) DEFAULT 'GENERAL' NOT NULL,    -- 分类类型（GENERAL/POLICY/REGULATION等）
    ACCESS_LEVEL        VARCHAR2(20) DEFAULT 'PUBLIC' NOT NULL,     -- 访问级别（PUBLIC/INTERNAL/RESTRICTED等）
    ALLOWED_ROLES       VARCHAR2(500),                               -- 允许访问的角色
    DOCUMENT_COUNT      NUMBER(10,0) DEFAULT 0,                     -- 文档数量
    TOTAL_VIEWS         NUMBER(15,0) DEFAULT 0,                     -- 总浏览量
    LAST_UPDATED        TIMESTAMP(6),                                -- 最后更新时间
    SEO_KEYWORDS        VARCHAR2(1000),                              -- SEO关键词
    SEO_DESCRIPTION     VARCHAR2(500),                               -- SEO描述
    IS_FEATURED         NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 是否推荐（1:是,0:否）
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否激活（1:激活,0:禁用）
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 逻辑删除标识（0:未删除,1:已删除）
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    METADATA            JSON,                                        -- 扩展元数据
    CONSTRAINT PK_KNOWLEDGE_CATEGORY PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_CATEGORY_PARENT FOREIGN KEY (PARENT_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
    CONSTRAINT UK_KNOWLEDGE_CATEGORY_CODE UNIQUE (CATEGORY_CODE),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_TYPE CHECK (CATEGORY_TYPE IN ('GENERAL','POLICY','REGULATION','GUIDELINE','FAQ','TUTORIAL','REFERENCE')),
    CONSTRAINT CK_KNOWLEDGE_ACCESS_LEVEL CHECK (ACCESS_LEVEL IN ('PUBLIC','INTERNAL','RESTRICTED','CONFIDENTIAL')),
    CONSTRAINT CK_KNOWLEDGE_IS_FEATURED CHECK (IS_FEATURED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_IS_DELETED CHECK (IS_DELETED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_LEVEL CHECK (CATEGORY_LEVEL BETWEEN 1 AND 5)
);

-- 创建索引
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_PARENT ON KNOWLEDGE_CATEGORY(PARENT_ID, DISPLAY_ORDER);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_TYPE ON KNOWLEDGE_CATEGORY(CATEGORY_TYPE, IS_ACTIVE);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_LEVEL ON KNOWLEDGE_CATEGORY(CATEGORY_LEVEL, CATEGORY_PATH);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_FEATURED ON KNOWLEDGE_CATEGORY(IS_FEATURED, DISPLAY_ORDER);

-- 2. 知识库文档表 - 增强文档管理
CREATE TABLE KNOWLEDGE_DOCUMENT (
    ID                  NUMBER(19,0) NOT NULL,                       -- 文档ID，主键
    DOCUMENT_CODE       VARCHAR2(50) NOT NULL,                       -- 文档编码，唯一标识
    TITLE               VARCHAR2(200) NOT NULL,                      -- 文档标题
    SUBTITLE            VARCHAR2(200),                               -- 文档副标题
    SUMMARY             VARCHAR2(1000),                              -- 文档摘要
    CONTENT             CLOB NOT NULL,                               -- 文档内容
    CONTENT_TYPE        VARCHAR2(20) DEFAULT 'HTML' NOT NULL,       -- 内容类型（HTML/MARKDOWN/PLAIN_TEXT等）
    CONTENT_FORMAT      VARCHAR2(20) DEFAULT 'RICH_TEXT' NOT NULL,  -- 内容格式（RICH_TEXT/MARKDOWN/WIKI等）
    DOCUMENT_TYPE       VARCHAR2(50) NOT NULL,                       -- 文档类型（ARTICLE/POLICY/REGULATION等）
    CATEGORY_ID         NUMBER(19,0) NOT NULL,                       -- 分类ID，外键
    SECONDARY_CATEGORIES VARCHAR2(500),                              -- 次要分类
    TAGS                VARCHAR2(1000),                              -- 标签
    KEYWORDS            VARCHAR2(1000),                              -- 关键词
    LANGUAGE            VARCHAR2(10) DEFAULT 'zh-CN' NOT NULL,      -- 语言
    READING_LEVEL       VARCHAR2(20) DEFAULT 'INTERMEDIATE',        -- 阅读难度（BEGINNER/INTERMEDIATE/ADVANCED等）
    ESTIMATED_READ_TIME NUMBER(5,0),                                 -- 预计阅读时间（分钟）
    WORD_COUNT          NUMBER(10,0),                                -- 字数统计
    AUTHOR_ID           NUMBER(19,0),                                -- 作者ID
    AUTHOR_NAME         VARCHAR2(100),                               -- 作者姓名
    REVIEWER_ID         NUMBER(19,0),                                -- 审核人ID
    REVIEWER_NAME       VARCHAR2(100),                               -- 审核人姓名
    REVIEW_DATE         TIMESTAMP(6),                                -- 审核日期
    REVIEW_COMMENTS     VARCHAR2(1000),                              -- 审核意见
    STATUS              VARCHAR2(20) DEFAULT 'DRAFT' NOT NULL,      -- 状态（DRAFT/REVIEW/APPROVED等）
    PUBLISH_STATUS      VARCHAR2(20) DEFAULT 'UNPUBLISHED' NOT NULL, -- 发布状态（UNPUBLISHED/PUBLISHED/SCHEDULED等）
    PUBLISH_DATE        TIMESTAMP(6),                                -- 发布日期
    EFFECTIVE_DATE      DATE,                                        -- 生效日期
    EXPIRY_DATE         DATE,                                        -- 失效日期
    VERSION_NUMBER      VARCHAR2(20) DEFAULT '1.0' NOT NULL,        -- 版本号
    PARENT_DOCUMENT_ID  NUMBER(19,0),                                -- 父文档ID
    IS_LATEST_VERSION   NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否最新版本（1:是,0:否）
    ACCESS_LEVEL        VARCHAR2(20) DEFAULT 'PUBLIC' NOT NULL,     -- 访问级别（PUBLIC/INTERNAL/RESTRICTED等）
    ALLOWED_ROLES       VARCHAR2(500),                               -- 允许访问的角色
    DOWNLOAD_ALLOWED    NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否允许下载（1:允许,0:不允许）
    PRINT_ALLOWED       NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否允许打印（1:允许,0:不允许）
    SHARE_ALLOWED       NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否允许分享（1:允许,0:不允许）
    COMMENT_ALLOWED     NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否允许评论（1:允许,0:不允许）
    VIEW_COUNT          NUMBER(15,0) DEFAULT 0,                     -- 浏览次数
    DOWNLOAD_COUNT      NUMBER(10,0) DEFAULT 0,                     -- 下载次数
    LIKE_COUNT          NUMBER(10,0) DEFAULT 0,                     -- 点赞次数
    SHARE_COUNT         NUMBER(10,0) DEFAULT 0,                     -- 分享次数
    COMMENT_COUNT       NUMBER(10,0) DEFAULT 0,                     -- 评论次数
    RATING_SCORE        NUMBER(3,2) DEFAULT 0,                      -- 评分
    RATING_COUNT        NUMBER(10,0) DEFAULT 0,                     -- 评分次数
    LAST_VIEWED_AT      TIMESTAMP(6),                                -- 最后浏览时间
    LAST_MODIFIED_AT    TIMESTAMP(6),                                -- 最后修改时间
    SEO_TITLE           VARCHAR2(200),                               -- SEO标题
    SEO_DESCRIPTION     VARCHAR2(500),                               -- SEO描述
    SEO_KEYWORDS        VARCHAR2(1000),                              -- SEO关键词
    FEATURED_IMAGE_URL  VARCHAR2(500),                               -- 特色图片URL
    THUMBNAIL_URL       VARCHAR2(500),                               -- 缩略图URL
    ATTACHMENTS         JSON,                                        -- 附件信息，JSON格式
    RELATED_DOCUMENTS   JSON,                                        -- 相关文档，JSON格式
    EXTERNAL_LINKS      JSON,                                        -- 外部链接，JSON格式
    CHANGE_LOG          JSON,                                        -- 变更日志，JSON格式
    METADATA            JSON,                                        -- 扩展元数据
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 逻辑删除标识（0:未删除,1:已删除）
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    CONSTRAINT PK_KNOWLEDGE_DOCUMENT PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_DOC_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
    CONSTRAINT FK_KNOWLEDGE_DOC_PARENT FOREIGN KEY (PARENT_DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT UK_KNOWLEDGE_DOCUMENT_CODE UNIQUE (DOCUMENT_CODE),
    CONSTRAINT CK_KNOWLEDGE_CONTENT_TYPE CHECK (CONTENT_TYPE IN ('HTML','MARKDOWN','PLAIN_TEXT','JSON','XML')),
    CONSTRAINT CK_KNOWLEDGE_CONTENT_FORMAT CHECK (CONTENT_FORMAT IN ('RICH_TEXT','MARKDOWN','WIKI','STRUCTURED')),
    CONSTRAINT CK_KNOWLEDGE_DOC_TYPE CHECK (DOCUMENT_TYPE IN ('ARTICLE','POLICY','REGULATION','GUIDELINE','FAQ','TUTORIAL','REFERENCE','NEWS','ANNOUNCEMENT')),
    CONSTRAINT CK_KNOWLEDGE_READING_LEVEL CHECK (READING_LEVEL IN ('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT')),
    CONSTRAINT CK_KNOWLEDGE_DOC_STATUS CHECK (STATUS IN ('DRAFT','REVIEW','APPROVED','REJECTED','ARCHIVED')),
    CONSTRAINT CK_KNOWLEDGE_PUBLISH_STATUS CHECK (PUBLISH_STATUS IN ('UNPUBLISHED','PUBLISHED','SCHEDULED','EXPIRED')),
    CONSTRAINT CK_KNOWLEDGE_DOC_ACCESS CHECK (ACCESS_LEVEL IN ('PUBLIC','INTERNAL','RESTRICTED','CONFIDENTIAL')),
    CONSTRAINT CK_KNOWLEDGE_IS_LATEST CHECK (IS_LATEST_VERSION IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_DOWNLOAD_ALLOWED CHECK (DOWNLOAD_ALLOWED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_PRINT_ALLOWED CHECK (PRINT_ALLOWED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_SHARE_ALLOWED CHECK (SHARE_ALLOWED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_ALLOWED CHECK (COMMENT_ALLOWED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_DOC_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
);

-- 创建索引
CREATE INDEX IDX_KNOWLEDGE_DOC_CATEGORY ON KNOWLEDGE_DOCUMENT(CATEGORY_ID, PUBLISH_STATUS);
CREATE INDEX IDX_KNOWLEDGE_DOC_STATUS ON KNOWLEDGE_DOCUMENT(STATUS, PUBLISH_STATUS);
CREATE INDEX IDX_KNOWLEDGE_DOC_TYPE ON KNOWLEDGE_DOCUMENT(DOCUMENT_TYPE, PUBLISH_DATE DESC);
CREATE INDEX IDX_KNOWLEDGE_DOC_AUTHOR ON KNOWLEDGE_DOCUMENT(AUTHOR_ID, CREATED_AT DESC);
CREATE INDEX IDX_KNOWLEDGE_DOC_VIEWS ON KNOWLEDGE_DOCUMENT(VIEW_COUNT DESC, PUBLISH_DATE DESC);
CREATE INDEX IDX_KNOWLEDGE_DOC_RATING ON KNOWLEDGE_DOCUMENT(RATING_SCORE DESC, RATING_COUNT DESC);
CREATE INDEX IDX_KNOWLEDGE_DOC_TAGS ON KNOWLEDGE_DOCUMENT(TAGS);
CREATE INDEX IDX_KNOWLEDGE_DOC_KEYWORDS ON KNOWLEDGE_DOCUMENT(KEYWORDS);

-- 创建全文索引
CREATE INDEX IDX_KNOWLEDGE_DOC_FULLTEXT ON KNOWLEDGE_DOCUMENT(TITLE, CONTENT) INDEXTYPE IS CTXSYS.CONTEXT
PARAMETERS ('SYNC (ON COMMIT)');

-- 3. 文档访问记录表 - 新增访问统计
CREATE TABLE KNOWLEDGE_ACCESS_LOG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 访问记录ID，主键
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,                       -- 文档ID，外键
    USER_ID             NUMBER(19,0),                                -- 用户ID
    SESSION_ID          VARCHAR2(100),                               -- 会话ID
    ACCESS_TYPE         VARCHAR2(20) NOT NULL,                       -- 访问类型（VIEW/DOWNLOAD/PRINT等）
    ACCESS_SOURCE       VARCHAR2(50),                                -- 访问来源
    ACCESS_DEVICE       VARCHAR2(100),                               -- 访问设备
    ACCESS_BROWSER      VARCHAR2(100),                               -- 浏览器信息
    ACCESS_IP           VARCHAR2(45),                                -- 访问IP地址
    ACCESS_LOCATION     VARCHAR2(200),                               -- 访问地理位置
    REFERRER_URL        VARCHAR2(1000),                              -- 来源URL
    SEARCH_KEYWORDS     VARCHAR2(500),                               -- 搜索关键词
    ACCESS_DURATION     NUMBER(10,0),                                -- 访问时长（秒）
    SCROLL_PERCENTAGE   NUMBER(5,2),                                 -- 滚动百分比
    ACTIONS_PERFORMED   JSON,                                        -- 执行的操作，JSON格式
    ACCESS_TIME         TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 访问时间
    METADATA            JSON,                                        -- 扩展元数据
    CONSTRAINT PK_KNOWLEDGE_ACCESS_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_DOC FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT CK_KNOWLEDGE_ACCESS_TYPE CHECK (ACCESS_TYPE IN ('VIEW','DOWNLOAD','PRINT','SHARE','SEARCH','BOOKMARK'))
)
PARTITION BY RANGE (ACCESS_TIME) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
CREATE INDEX IDX_KNOWLEDGE_ACCESS_DOC ON KNOWLEDGE_ACCESS_LOG(DOCUMENT_ID, ACCESS_TIME DESC);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_USER ON KNOWLEDGE_ACCESS_LOG(USER_ID, ACCESS_TIME DESC);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_TYPE ON KNOWLEDGE_ACCESS_LOG(ACCESS_TYPE, ACCESS_TIME DESC);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_IP ON KNOWLEDGE_ACCESS_LOG(ACCESS_IP, ACCESS_TIME DESC);

-- 4. 文档评论表 - 新增评论功能
CREATE TABLE KNOWLEDGE_COMMENT (
    ID                  NUMBER(19,0) NOT NULL,                       -- 评论ID，主键
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,                       -- 文档ID，外键
    PARENT_COMMENT_ID   NUMBER(19,0),                                -- 父评论ID
    USER_ID             NUMBER(19,0) NOT NULL,                       -- 用户ID
    USER_NAME           VARCHAR2(100),                               -- 用户姓名
    COMMENT_CONTENT     CLOB NOT NULL,                               -- 评论内容
    COMMENT_TYPE        VARCHAR2(20) DEFAULT 'GENERAL' NOT NULL,    -- 评论类型（GENERAL/QUESTION/SUGGESTION等）
    RATING_SCORE        NUMBER(2,0),                                 -- 评分（1-5）
    IS_ANONYMOUS        NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 是否匿名（1:是,0:否）
    STATUS              VARCHAR2(20) DEFAULT 'PENDING' NOT NULL,    -- 状态（PENDING/APPROVED/REJECTED等）
    MODERATION_REASON   VARCHAR2(500),                               -- 审核原因
    MODERATOR_ID        NUMBER(19,0),                                -- 审核人ID
    MODERATION_DATE     TIMESTAMP(6),                                -- 审核日期
    LIKE_COUNT          NUMBER(10,0) DEFAULT 0,                     -- 点赞数
    DISLIKE_COUNT       NUMBER(10,0) DEFAULT 0,                     -- 点踩数
    REPLY_COUNT         NUMBER(10,0) DEFAULT 0,                     -- 回复数
    IS_HELPFUL          NUMBER(1,0) DEFAULT 0,                      -- 是否有用（1:是,0:否）
    HELPFUL_COUNT       NUMBER(10,0) DEFAULT 0,                     -- 有用数
    REPORT_COUNT        NUMBER(5,0) DEFAULT 0,                      -- 举报数
    IP_ADDRESS          VARCHAR2(45),                                -- IP地址
    USER_AGENT          VARCHAR2(500),                               -- 用户代理
    METADATA            JSON,                                        -- 扩展元数据
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 逻辑删除标识（0:未删除,1:已删除）
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    CONSTRAINT PK_KNOWLEDGE_COMMENT PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_COMMENT_DOC FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_COMMENT_PARENT FOREIGN KEY (PARENT_COMMENT_ID) REFERENCES KNOWLEDGE_COMMENT(ID),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_TYPE CHECK (COMMENT_TYPE IN ('GENERAL','QUESTION','SUGGESTION','CORRECTION','PRAISE','COMPLAINT')),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_STATUS CHECK (STATUS IN ('PENDING','APPROVED','REJECTED','HIDDEN','FLAGGED')),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_RATING CHECK (RATING_SCORE BETWEEN 1 AND 5),
    CONSTRAINT CK_KNOWLEDGE_IS_ANONYMOUS CHECK (IS_ANONYMOUS IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_IS_HELPFUL CHECK (IS_HELPFUL IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_COMMENT_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
);

-- 创建索引
CREATE INDEX IDX_KNOWLEDGE_COMMENT_DOC ON KNOWLEDGE_COMMENT(DOCUMENT_ID, CREATED_AT DESC);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_USER ON KNOWLEDGE_COMMENT(USER_ID, CREATED_AT DESC);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_STATUS ON KNOWLEDGE_COMMENT(STATUS, CREATED_AT DESC);
CREATE INDEX IDX_KNOWLEDGE_COMMENT_PARENT ON KNOWLEDGE_COMMENT(PARENT_COMMENT_ID, CREATED_AT);

-- 5. 文档标签表 - 新增标签管理
CREATE TABLE KNOWLEDGE_TAG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 标签ID，主键
    TAG_NAME            VARCHAR2(50) NOT NULL,                       -- 标签名称
    TAG_DISPLAY_NAME    VARCHAR2(100),                               -- 标签显示名称
    TAG_DESCRIPTION     VARCHAR2(500),                               -- 标签描述
    TAG_COLOR           VARCHAR2(20),                                -- 标签颜色
    TAG_ICON            VARCHAR2(100),                               -- 标签图标
    TAG_CATEGORY        VARCHAR2(50),                                -- 标签分类
    USAGE_COUNT         NUMBER(10,0) DEFAULT 0,                     -- 使用次数
    IS_SYSTEM_TAG       NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 是否系统标签（1:是,0:否）
    IS_FEATURED         NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 是否推荐（1:是,0:否）
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否激活（1:激活,0:禁用）
    METADATA            JSON,                                        -- 扩展元数据
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    CONSTRAINT PK_KNOWLEDGE_TAG PRIMARY KEY (ID),
    CONSTRAINT UK_KNOWLEDGE_TAG_NAME UNIQUE (TAG_NAME),
    CONSTRAINT CK_KNOWLEDGE_IS_SYSTEM_TAG CHECK (IS_SYSTEM_TAG IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_TAG_IS_FEATURED CHECK (IS_FEATURED IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_TAG_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1))
);

-- 创建索引
CREATE INDEX IDX_KNOWLEDGE_TAG_CATEGORY ON KNOWLEDGE_TAG(TAG_CATEGORY, USAGE_COUNT DESC);
CREATE INDEX IDX_KNOWLEDGE_TAG_USAGE ON KNOWLEDGE_TAG(USAGE_COUNT DESC);
CREATE INDEX IDX_KNOWLEDGE_TAG_FEATURED ON KNOWLEDGE_TAG(IS_FEATURED, USAGE_COUNT DESC);

-- 6. 文档标签关联表
CREATE TABLE KNOWLEDGE_DOCUMENT_TAG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 文档标签关联ID，主键
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,                       -- 文档ID，外键
    TAG_ID              NUMBER(19,0) NOT NULL,                       -- 标签ID，外键
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    CONSTRAINT PK_KNOWLEDGE_DOCUMENT_TAG PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_DOC_TAG_DOC FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_DOC_TAG_TAG FOREIGN KEY (TAG_ID) REFERENCES KNOWLEDGE_TAG(ID),
    CONSTRAINT UK_KNOWLEDGE_DOC_TAG UNIQUE (DOCUMENT_ID, TAG_ID)
);

-- 创建索引
CREATE INDEX IDX_KNOWLEDGE_DOC_TAG_DOC ON KNOWLEDGE_DOCUMENT_TAG(DOCUMENT_ID);
CREATE INDEX IDX_KNOWLEDGE_DOC_TAG_TAG ON KNOWLEDGE_DOCUMENT_TAG(TAG_ID);

-- 7. 创建序列
CREATE SEQUENCE SEQ_KNOWLEDGE_CATEGORY START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_KNOWLEDGE_DOCUMENT START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_KNOWLEDGE_ACCESS_LOG START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_KNOWLEDGE_COMMENT START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_KNOWLEDGE_TAG START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_KNOWLEDGE_DOCUMENT_TAG START WITH 1 INCREMENT BY 1 CACHE 100;

-- 8. 创建触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_CATEGORY_UPD
BEFORE UPDATE ON KNOWLEDGE_CATEGORY
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_DOCUMENT_UPD
BEFORE UPDATE ON KNOWLEDGE_DOCUMENT
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
    :NEW.LAST_MODIFIED_AT := SYSTIMESTAMP;
    
    -- 计算字数
    IF :NEW.CONTENT IS NOT NULL THEN
        :NEW.WORD_COUNT := LENGTH(REGEXP_REPLACE(:NEW.CONTENT, '[^\p{L}\p{N}]+', ' '));
        :NEW.ESTIMATED_READ_TIME := CEIL(:NEW.WORD_COUNT / 200); -- 假设每分钟200字
    END IF;
END;
/

CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_COMMENT_UPD
BEFORE UPDATE ON KNOWLEDGE_COMMENT
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

-- 9. 创建视图 - 知识库统计
CREATE OR REPLACE VIEW VW_KNOWLEDGE_STATISTICS AS
SELECT 
    kc.ID AS CATEGORY_ID,
    kc.CATEGORY_NAME,
    kc.CATEGORY_TYPE,
    COUNT(kd.ID) AS DOCUMENT_COUNT,
    COUNT(CASE WHEN kd.PUBLISH_STATUS = 'PUBLISHED' THEN 1 END) AS PUBLISHED_COUNT,
    COUNT(CASE WHEN kd.STATUS = 'DRAFT' THEN 1 END) AS DRAFT_COUNT,
    SUM(kd.VIEW_COUNT) AS TOTAL_VIEWS,
    SUM(kd.DOWNLOAD_COUNT) AS TOTAL_DOWNLOADS,
    AVG(kd.RATING_SCORE) AS AVG_RATING,
    MAX(kd.PUBLISH_DATE) AS LATEST_PUBLISH_DATE,
    COUNT(CASE WHEN kd.CREATED_AT >= SYSDATE - 30 THEN 1 END) AS RECENT_DOCUMENTS
FROM KNOWLEDGE_CATEGORY kc
LEFT JOIN KNOWLEDGE_DOCUMENT kd ON kc.ID = kd.CATEGORY_ID AND kd.IS_DELETED = 0
WHERE kc.IS_DELETED = 0
GROUP BY kc.ID, kc.CATEGORY_NAME, kc.CATEGORY_TYPE;

-- 10. 创建物化视图 - 热门文档
CREATE MATERIALIZED VIEW MV_POPULAR_DOCUMENTS
REFRESH COMPLETE ON DEMAND
AS
SELECT 
    kd.ID,
    kd.DOCUMENT_CODE,
    kd.TITLE,
    kd.DOCUMENT_TYPE,
    kc.CATEGORY_NAME,
    kd.VIEW_COUNT,
    kd.DOWNLOAD_COUNT,
    kd.LIKE_COUNT,
    kd.RATING_SCORE,
    kd.RATING_COUNT,
    kd.COMMENT_COUNT,
    kd.PUBLISH_DATE,
    -- 计算热度分数
    (kd.VIEW_COUNT * 0.3 + kd.DOWNLOAD_COUNT * 0.4 + kd.LIKE_COUNT * 0.2 + kd.RATING_SCORE * kd.RATING_COUNT * 0.1) AS POPULARITY_SCORE,
    RANK() OVER (ORDER BY (kd.VIEW_COUNT * 0.3 + kd.DOWNLOAD_COUNT * 0.4 + kd.LIKE_COUNT * 0.2 + kd.RATING_SCORE * kd.RATING_COUNT * 0.1) DESC) AS POPULARITY_RANK
FROM KNOWLEDGE_DOCUMENT kd
JOIN KNOWLEDGE_CATEGORY kc ON kd.CATEGORY_ID = kc.ID
WHERE kd.PUBLISH_STATUS = 'PUBLISHED' AND kd.IS_DELETED = 0 AND kc.IS_DELETED = 0;

-- 11. 创建函数 - 搜索文档
CREATE OR REPLACE FUNCTION FN_SEARCH_DOCUMENTS(
    p_keywords IN VARCHAR2,
    p_category_id IN NUMBER DEFAULT NULL,
    p_document_type IN VARCHAR2 DEFAULT NULL,
    p_limit IN NUMBER DEFAULT 20
) RETURN SYS_REFCURSOR IS
    v_cursor SYS_REFCURSOR;
    v_sql VARCHAR2(4000);
BEGIN
    v_sql := 'SELECT kd.ID, kd.TITLE, kd.SUMMARY, kd.DOCUMENT_TYPE, kc.CATEGORY_NAME, ' ||
             'kd.VIEW_COUNT, kd.RATING_SCORE, kd.PUBLISH_DATE, ' ||
             'SCORE(1) AS RELEVANCE_SCORE ' ||
             'FROM KNOWLEDGE_DOCUMENT kd ' ||
             'JOIN KNOWLEDGE_CATEGORY kc ON kd.CATEGORY_ID = kc.ID ' ||
             'WHERE CONTAINS(kd.TITLE || '' '' || kd.CONTENT, ?, 1) > 0 ' ||
             'AND kd.PUBLISH_STATUS = ''PUBLISHED'' AND kd.IS_DELETED = 0';
    
    IF p_category_id IS NOT NULL THEN
        v_sql := v_sql || ' AND kd.CATEGORY_ID = ' || p_category_id;
    END IF;
    
    IF p_document_type IS NOT NULL THEN
        v_sql := v_sql || ' AND kd.DOCUMENT_TYPE = ''' || p_document_type || '''';
    END IF;
    
    v_sql := v_sql || ' ORDER BY SCORE(1) DESC, kd.VIEW_COUNT DESC ' ||
             'FETCH FIRST ' || p_limit || ' ROWS ONLY';
    
    OPEN v_cursor FOR v_sql USING p_keywords;
    RETURN v_cursor;
END;
/

-- 12. 创建存储过程 - 更新文档统计
CREATE OR REPLACE PROCEDURE SP_UPDATE_DOCUMENT_STATISTICS(
    p_document_id IN NUMBER,
    p_action_type IN VARCHAR2
) IS
BEGIN
    CASE p_action_type
        WHEN 'VIEW' THEN
            UPDATE KNOWLEDGE_DOCUMENT 
            SET VIEW_COUNT = VIEW_COUNT + 1,
                LAST_VIEWED_AT = SYSTIMESTAMP
            WHERE ID = p_document_id;
            
        WHEN 'DOWNLOAD' THEN
            UPDATE KNOWLEDGE_DOCUMENT 
            SET DOWNLOAD_COUNT = DOWNLOAD_COUNT + 1
            WHERE ID = p_document_id;
            
        WHEN 'LIKE' THEN
            UPDATE KNOWLEDGE_DOCUMENT 
            SET LIKE_COUNT = LIKE_COUNT + 1
            WHERE ID = p_document_id;
            
        WHEN 'SHARE' THEN
            UPDATE KNOWLEDGE_DOCUMENT 
            SET SHARE_COUNT = SHARE_COUNT + 1
            WHERE ID = p_document_id;
            
        WHEN 'COMMENT' THEN
            UPDATE KNOWLEDGE_DOCUMENT 
            SET COMMENT_COUNT = (
                SELECT COUNT(*) FROM KNOWLEDGE_COMMENT 
                WHERE DOCUMENT_ID = p_document_id AND STATUS = 'APPROVED' AND IS_DELETED = 0
            )
            WHERE ID = p_document_id;
    END CASE;
    
    COMMIT;
END;
/

-- 13. 创建存储过程 - 更新分类统计
CREATE OR REPLACE PROCEDURE SP_UPDATE_CATEGORY_STATISTICS IS
BEGIN
    UPDATE KNOWLEDGE_CATEGORY kc
    SET DOCUMENT_COUNT = (
        SELECT COUNT(*) 
        FROM KNOWLEDGE_DOCUMENT kd 
        WHERE kd.CATEGORY_ID = kc.ID 
        AND kd.PUBLISH_STATUS = 'PUBLISHED' 
        AND kd.IS_DELETED = 0
    ),
    TOTAL_VIEWS = (
        SELECT NVL(SUM(kd.VIEW_COUNT), 0)
        FROM KNOWLEDGE_DOCUMENT kd 
        WHERE kd.CATEGORY_ID = kc.ID 
        AND kd.IS_DELETED = 0
    ),
    LAST_UPDATED = SYSTIMESTAMP
    WHERE kc.IS_DELETED = 0;
    
    COMMIT;
END;
/

-- 14. 创建同义词和授权
CREATE SYNONYM KNOWLEDGE_STATS FOR VW_KNOWLEDGE_STATISTICS;
GRANT SELECT ON VW_KNOWLEDGE_STATISTICS TO APP_USER;
GRANT SELECT ON MV_POPULAR_DOCUMENTS TO APP_USER;
GRANT EXECUTE ON FN_SEARCH_DOCUMENTS TO APP_USER;
GRANT EXECUTE ON SP_UPDATE_DOCUMENT_STATISTICS TO APP_USER;

-- 15. 创建定时任务（可选）
-- BEGIN
--     DBMS_SCHEDULER.CREATE_JOB (
--         job_name        => 'JOB_UPDATE_KNOWLEDGE_STATS',
--         job_type        => 'PLSQL_BLOCK',
--         job_action      => 'BEGIN SP_UPDATE_CATEGORY_STATISTICS; END;',
--         start_date      => SYSTIMESTAMP,
--         repeat_interval => 'FREQ=HOURLY;BYMINUTE=0',
--         enabled         => TRUE,
--         comments        => '每小时更新知识库统计信息'
--     );
-- END;
-- /

-- 16. 创建全文搜索首选项（可选）
-- BEGIN
--     CTX_DDL.CREATE_PREFERENCE('KNOWLEDGE_LEXER', 'CHINESE_LEXER');
--     CTX_DDL.CREATE_PREFERENCE('KNOWLEDGE_WORDLIST', 'BASIC_WORDLIST');
--     CTX_DDL.SET_ATTRIBUTE('KNOWLEDGE_WORDLIST', 'FUZZY_MATCH', 'CHINESE');
-- END;
-- /

-- 表和字段注释
-- 1. 知识库分类表注释
COMMENT ON TABLE KNOWLEDGE_CATEGORY IS '知识库分类表';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.ID IS '分类ID，主键';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_CODE IS '分类编码，唯一标识';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_NAME IS '分类名称';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_DESC IS '分类描述';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.PARENT_ID IS '父分类ID';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_LEVEL IS '分类层级';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_PATH IS '分类路径';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.DISPLAY_ORDER IS '显示顺序';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.ICON_URL IS '图标URL';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.COLOR_CODE IS '颜色代码';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_TYPE IS '分类类型：GENERAL-通用，POLICY-政策，REGULATION-法规，GUIDELINE-指导原则，FAQ-常见问题，TUTORIAL-教程，REFERENCE-参考';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.ACCESS_LEVEL IS '访问级别：PUBLIC-公开，INTERNAL-内部，RESTRICTED-受限，CONFIDENTIAL-机密';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.ALLOWED_ROLES IS '允许访问的角色';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.DOCUMENT_COUNT IS '文档数量';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.TOTAL_VIEWS IS '总查看次数';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.LAST_UPDATED IS '最后更新时间';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.SEO_KEYWORDS IS 'SEO关键词';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.SEO_DESCRIPTION IS 'SEO描述';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.IS_FEATURED IS '是否推荐，1-推荐，0-不推荐';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.IS_ACTIVE IS '是否激活，1-激活，0-禁用';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.IS_DELETED IS '是否删除，1-已删除，0-未删除';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CREATED_AT IS '创建时间';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.VERSION IS '版本号，用于乐观锁';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.METADATA IS '元数据，JSON格式';

-- 2. 知识库文档表注释
COMMENT ON TABLE KNOWLEDGE_DOCUMENT IS '知识库文档表';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ID IS '文档ID，主键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOCUMENT_CODE IS '文档编码，唯一标识';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.TITLE IS '文档标题';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SUBTITLE IS '文档副标题';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SUMMARY IS '文档摘要';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CONTENT IS '文档内容';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CONTENT_TYPE IS '内容类型：HTML-HTML格式，MARKDOWN-Markdown格式，PLAIN_TEXT-纯文本，JSON-JSON格式，XML-XML格式';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CONTENT_FORMAT IS '内容格式：RICH_TEXT-富文本，MARKDOWN-Markdown，WIKI-Wiki格式，STRUCTURED-结构化';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOCUMENT_TYPE IS '文档类型：ARTICLE-文章，POLICY-政策，REGULATION-法规，GUIDELINE-指导原则，FAQ-常见问题，TUTORIAL-教程，REFERENCE-参考，NEWS-新闻，ANNOUNCEMENT-公告';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CATEGORY_ID IS '分类ID，外键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SECONDARY_CATEGORIES IS '次要分类';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.TAGS IS '标签';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.KEYWORDS IS '关键词';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.LANGUAGE IS '语言：zh-CN-简体中文，en-US-英语等';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.READING_LEVEL IS '阅读难度级别：BEGINNER-初级，INTERMEDIATE-中级，ADVANCED-高级，EXPERT-专家级';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ESTIMATED_READ_TIME IS '预估阅读时间（分钟）';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.WORD_COUNT IS '字数统计';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.AUTHOR_ID IS '作者ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.AUTHOR_NAME IS '作者姓名';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.REVIEWER_ID IS '审核人ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.REVIEWER_NAME IS '审核人姓名';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.REVIEW_DATE IS '审核日期';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.REVIEW_COMMENTS IS '审核意见';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.STATUS IS '文档状态：DRAFT-草稿，REVIEW-审核中，APPROVED-已批准，REJECTED-已拒绝，ARCHIVED-已归档';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PUBLISH_STATUS IS '发布状态：UNPUBLISHED-未发布，PUBLISHED-已发布，SCHEDULED-定时发布，EXPIRED-已过期';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PUBLISH_DATE IS '发布日期';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.EFFECTIVE_DATE IS '生效日期';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.EXPIRY_DATE IS '失效日期';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VERSION_NUMBER IS '版本号';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PARENT_DOCUMENT_ID IS '父文档ID（用于版本管理）';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.IS_LATEST_VERSION IS '是否最新版本，1-是，0-否';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ACCESS_LEVEL IS '访问级别：PUBLIC-公开，INTERNAL-内部，RESTRICTED-受限，CONFIDENTIAL-机密';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ALLOWED_ROLES IS '允许访问的角色';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOWNLOAD_ALLOWED IS '是否允许下载，1-允许，0-不允许';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PRINT_ALLOWED IS '是否允许打印，1-允许，0-不允许';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SHARE_ALLOWED IS '是否允许分享，1-允许，0-不允许';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.COMMENT_ALLOWED IS '是否允许评论，1-允许，0-不允许';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VIEW_COUNT IS '查看次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOWNLOAD_COUNT IS '下载次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.LIKE_COUNT IS '点赞次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SHARE_COUNT IS '分享次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.COMMENT_COUNT IS '评论次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.RATING_SCORE IS '评分';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.RATING_COUNT IS '评分次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.LAST_VIEWED_AT IS '最后查看时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.LAST_MODIFIED_AT IS '最后修改时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SEO_TITLE IS 'SEO标题';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SEO_DESCRIPTION IS 'SEO描述';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SEO_KEYWORDS IS 'SEO关键词';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.FEATURED_IMAGE_URL IS '特色图片URL';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.THUMBNAIL_URL IS '缩略图URL';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ATTACHMENTS IS '附件信息，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.RELATED_DOCUMENTS IS '相关文档，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.EXTERNAL_LINKS IS '外部链接，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CHANGE_LOG IS '变更日志，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.IS_DELETED IS '是否删除，1-已删除，0-未删除';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CREATED_AT IS '创建时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VERSION IS '版本号，用于乐观锁';

-- 3. 文档访问记录表注释
COMMENT ON TABLE KNOWLEDGE_ACCESS_LOG IS '知识库文档访问记录表';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ID IS '访问记录ID，主键';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.DOCUMENT_ID IS '文档ID，外键';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.SESSION_ID IS '会话ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_TYPE IS '访问类型：VIEW-查看，DOWNLOAD-下载，PRINT-打印，SHARE-分享，SEARCH-搜索，BOOKMARK-收藏';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_SOURCE IS '访问来源';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_DEVICE IS '访问设备';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_BROWSER IS '访问浏览器';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_IP IS '访问IP地址';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_LOCATION IS '访问地理位置';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.REFERRER_URL IS '来源页面URL';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.SEARCH_KEYWORDS IS '搜索关键词';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_DURATION IS '访问时长（秒）';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.SCROLL_PERCENTAGE IS '页面滚动百分比';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACTIONS_PERFORMED IS '执行的操作，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_TIME IS '访问时间';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.METADATA IS '元数据，JSON格式';

-- 4. 文档评论表注释
COMMENT ON TABLE KNOWLEDGE_COMMENT IS '知识库文档评论表';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.ID IS '评论ID，主键';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.DOCUMENT_ID IS '文档ID，外键';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.PARENT_COMMENT_ID IS '父评论ID（用于回复）';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.USER_ID IS '评论用户ID';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.USER_NAME IS '评论用户姓名';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.COMMENT_CONTENT IS '评论内容';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.COMMENT_TYPE IS '评论类型：GENERAL-一般评论，QUESTION-问题，SUGGESTION-建议，CORRECTION-纠错，PRAISE-表扬，COMPLAINT-投诉';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.RATING_SCORE IS '评分（1-5分）';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.IS_ANONYMOUS IS '是否匿名，1-匿名，0-实名';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.STATUS IS '状态：PENDING-待审核，APPROVED-已通过，REJECTED-已拒绝，HIDDEN-已隐藏，FLAGGED-已标记';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.MODERATION_REASON IS '审核原因';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.MODERATOR_ID IS '审核人ID';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.MODERATION_DATE IS '审核日期';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.LIKE_COUNT IS '点赞次数';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.DISLIKE_COUNT IS '点踩次数';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.REPLY_COUNT IS '回复次数';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.IS_HELPFUL IS '评论是否有帮助，1-有帮助，0-无帮助';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.HELPFUL_COUNT IS '有帮助次数';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.REPORT_COUNT IS '举报次数';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.USER_AGENT IS '用户代理';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.IS_DELETED IS '是否删除，1-已删除，0-未删除';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.CREATED_AT IS '创建时间';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN KNOWLEDGE_COMMENT.VERSION IS '版本号，用于乐观锁';

-- 5. 文档标签表注释
COMMENT ON TABLE KNOWLEDGE_TAG IS '知识库标签表';
COMMENT ON COLUMN KNOWLEDGE_TAG.ID IS '标签ID，主键';
COMMENT ON COLUMN KNOWLEDGE_TAG.TAG_NAME IS '标签名称';
COMMENT ON COLUMN KNOWLEDGE_TAG.TAG_DISPLAY_NAME IS '标签显示名称';
COMMENT ON COLUMN KNOWLEDGE_TAG.TAG_DESCRIPTION IS '标签描述';
COMMENT ON COLUMN KNOWLEDGE_TAG.TAG_COLOR IS '标签颜色';
COMMENT ON COLUMN KNOWLEDGE_TAG.TAG_ICON IS '标签图标';
COMMENT ON COLUMN KNOWLEDGE_TAG.TAG_CATEGORY IS '标签分类';
COMMENT ON COLUMN KNOWLEDGE_TAG.USAGE_COUNT IS '使用次数';
COMMENT ON COLUMN KNOWLEDGE_TAG.IS_SYSTEM_TAG IS '是否系统标签，1-系统标签，0-自定义标签';
COMMENT ON COLUMN KNOWLEDGE_TAG.IS_FEATURED IS '是否推荐标签，1-推荐，0-不推荐';
COMMENT ON COLUMN KNOWLEDGE_TAG.IS_ACTIVE IS '是否激活，1-激活，0-禁用';
COMMENT ON COLUMN KNOWLEDGE_TAG.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN KNOWLEDGE_TAG.CREATED_AT IS '创建时间';
COMMENT ON COLUMN KNOWLEDGE_TAG.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN KNOWLEDGE_TAG.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN KNOWLEDGE_TAG.UPDATED_BY IS '更新人ID';

-- 6. 文档标签关联表注释
COMMENT ON TABLE KNOWLEDGE_DOCUMENT_TAG IS '知识库文档标签关联表';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT_TAG.ID IS '关联ID，主键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT_TAG.DOCUMENT_ID IS '文档ID，外键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT_TAG.TAG_ID IS '标签ID，外键';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT_TAG.CREATED_AT IS '创建时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT_TAG.CREATED_BY IS '创建人ID';