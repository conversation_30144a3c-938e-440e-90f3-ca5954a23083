-- =====================================================
-- 医保基金监管平台 - 系统日志模块优化表结构 (Oracle 19c)
-- =====================================================

-- 1. 系统操作日志表 - 增强审计功能
CREATE TABLE SYSTEM_OPERATION_LOG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 日志ID，主键
    LOG_ID              VARCHAR2(50) NOT NULL,                       -- 日志唯一标识
    TRACE_ID            VARCHAR2(50),                                -- 链路追踪ID
    SPAN_ID             VARCHAR2(50),                                -- 跨度ID
    LOG_LEVEL           VARCHAR2(10) NOT NULL,                       -- 日志级别（TRACE/DEBUG/INFO/WARN/ERROR/FATAL）
    LOG_TYPE            VARCHAR2(20) NOT NULL,                       -- 日志类型（ACCESS/OPERATION/SECURITY等）
    LOG_CATEGORY        VARCHAR2(50) NOT NULL,                       -- 日志分类（USER_AUTH/DATA_ACCESS等）
    LOG_SOURCE          VARCHAR2(100) NOT NULL,                      -- 日志来源
    MODULE_NAME         VARCHAR2(100),                               -- 模块名称
    FUNCTION_NAME       VARCHAR2(100),                               -- 函数名称
    OPERATION_TYPE      VARCHAR2(50) NOT NULL,                       -- 操作类型（CREATE/READ/UPDATE/DELETE等）
    OPERATION_NAME      VARCHAR2(200),                               -- 操作名称
    OPERATION_DESC      VARCHAR2(1000),                              -- 操作描述
    USER_ID             NUMBER(19,0),                                -- 用户ID
    USERNAME            VARCHAR2(100),                               -- 用户名
    USER_ROLE           VARCHAR2(100),                               -- 用户角色
    SESSION_ID          VARCHAR2(100),                               -- 会话ID
    REQUEST_ID          VARCHAR2(100),                               -- 请求ID
    CLIENT_IP           VARCHAR2(45),                                -- 客户端IP
    CLIENT_PORT         NUMBER(5,0),                                 -- 客户端端口
    SERVER_IP           VARCHAR2(45),                                -- 服务器IP
    SERVER_PORT         NUMBER(5,0),                                 -- 服务器端口
    USER_AGENT          VARCHAR2(1000),                              -- 用户代理
    REQUEST_METHOD      VARCHAR2(10),                                -- 请求方法
    REQUEST_URL         VARCHAR2(2000),                              -- 请求URL
    REQUEST_PARAMS      JSON,                                        -- 请求参数
    REQUEST_HEADERS     JSON,                                        -- 请求头
    REQUEST_BODY        CLOB,                                        -- 请求体
    RESPONSE_STATUS     NUMBER(3,0),                                 -- 响应状态码
    RESPONSE_HEADERS    JSON,                                        -- 响应头
    RESPONSE_BODY       CLOB,                                        -- 响应体
    RESPONSE_SIZE       NUMBER(10,0),                                -- 响应大小（字节）
    EXECUTION_TIME      NUMBER(10,0),                                -- 执行时间（毫秒）
    DATABASE_TIME       NUMBER(10,0),                                -- 数据库耗时（毫秒）
    EXTERNAL_API_TIME   NUMBER(10,0),                                -- 外部API耗时（毫秒）
    MEMORY_USAGE        NUMBER(10,0),                                -- 内存使用量（KB）
    CPU_USAGE           NUMBER(5,2),                                 -- CPU使用率（%）
    THREAD_ID           VARCHAR2(50),                                -- 线程ID
    PROCESS_ID          VARCHAR2(50),                                -- 进程ID
    BUSINESS_KEY        VARCHAR2(200),                               -- 业务键
    BUSINESS_TYPE       VARCHAR2(50),                                -- 业务类型
    AFFECTED_ENTITIES   JSON,                                        -- 影响的实体
    BEFORE_DATA         JSON,                                        -- 变更前数据
    AFTER_DATA          JSON,                                        -- 变更后数据
    CHANGE_SUMMARY      VARCHAR2(1000),                              -- 变更摘要
    ERROR_CODE          VARCHAR2(50),                                -- 错误代码
    ERROR_MESSAGE       CLOB,                                        -- 错误消息
    ERROR_STACK         CLOB,                                        -- 错误堆栈
    EXCEPTION_TYPE      VARCHAR2(200),                               -- 异常类型
    RISK_LEVEL          VARCHAR2(20) DEFAULT 'LOW',                 -- 风险级别（LOW/MEDIUM/HIGH/CRITICAL）
    SECURITY_EVENT      NUMBER(1,0) DEFAULT 0,                      -- 是否安全事件（1:是,0:否）
    COMPLIANCE_FLAG     NUMBER(1,0) DEFAULT 0,                      -- 合规标识（1:是,0:否）
    RETENTION_PERIOD    NUMBER(5,0) DEFAULT 2555,                   -- 保留期限（天，默认7年）
    TAGS                VARCHAR2(1000),                              -- 标签
    CUSTOM_FIELDS       JSON,                                        -- 自定义字段
    CORRELATION_ID      VARCHAR2(100),                               -- 关联ID
    PARENT_LOG_ID       VARCHAR2(50),                                -- 父日志ID
    LOG_TIMESTAMP       TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 日志时间戳
    SERVER_TIMESTAMP    TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 服务器时间戳
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CONSTRAINT PK_SYSTEM_OPERATION_LOG PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_OPERATION_LOG_ID UNIQUE (LOG_ID),
    CONSTRAINT CK_SYSTEM_LOG_LEVEL CHECK (LOG_LEVEL IN ('TRACE','DEBUG','INFO','WARN','ERROR','FATAL')),
    CONSTRAINT CK_SYSTEM_LOG_TYPE CHECK (LOG_TYPE IN ('ACCESS','OPERATION','SECURITY','PERFORMANCE','ERROR','AUDIT','BUSINESS')),
    CONSTRAINT CK_SYSTEM_LOG_CATEGORY CHECK (LOG_CATEGORY IN ('USER_AUTH','DATA_ACCESS','DATA_MODIFY','SYSTEM_CONFIG','RULE_EXECUTION','REPORT_GENERATION','FILE_OPERATION','API_CALL','DATABASE_OPERATION')),
    CONSTRAINT CK_SYSTEM_OPERATION_TYPE CHECK (OPERATION_TYPE IN ('CREATE','READ','UPDATE','DELETE','LOGIN','LOGOUT','SEARCH','EXPORT','IMPORT','APPROVE','REJECT','EXECUTE','SCHEDULE')),
    CONSTRAINT CK_SYSTEM_RISK_LEVEL CHECK (RISK_LEVEL IN ('LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_SECURITY_EVENT CHECK (SECURITY_EVENT IN (0,1)),
    CONSTRAINT CK_SYSTEM_COMPLIANCE_FLAG CHECK (COMPLIANCE_FLAG IN (0,1))
)
PARTITION BY RANGE (LOG_TIMESTAMP) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
CREATE INDEX IDX_SYSTEM_LOG_TIMESTAMP ON SYSTEM_OPERATION_LOG(LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_USER ON SYSTEM_OPERATION_LOG(USER_ID, LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_TYPE ON SYSTEM_OPERATION_LOG(LOG_TYPE, LOG_LEVEL, LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_CATEGORY ON SYSTEM_OPERATION_LOG(LOG_CATEGORY, OPERATION_TYPE);
CREATE INDEX IDX_SYSTEM_LOG_IP ON SYSTEM_OPERATION_LOG(CLIENT_IP, LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_SESSION ON SYSTEM_OPERATION_LOG(SESSION_ID, LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_TRACE ON SYSTEM_OPERATION_LOG(TRACE_ID, SPAN_ID);
CREATE INDEX IDX_SYSTEM_LOG_BUSINESS ON SYSTEM_OPERATION_LOG(BUSINESS_TYPE, BUSINESS_KEY);
CREATE INDEX IDX_SYSTEM_LOG_ERROR ON SYSTEM_OPERATION_LOG(ERROR_CODE, LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_SECURITY ON SYSTEM_OPERATION_LOG(SECURITY_EVENT, RISK_LEVEL, LOG_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_LOG_PERFORMANCE ON SYSTEM_OPERATION_LOG(EXECUTION_TIME DESC, LOG_TIMESTAMP DESC);

-- 2. 系统性能监控日志表 - 新增性能监控
CREATE TABLE SYSTEM_PERFORMANCE_LOG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 性能日志ID，主键
    LOG_ID              VARCHAR2(50) NOT NULL,                       -- 日志唯一标识
    METRIC_TYPE         VARCHAR2(50) NOT NULL,                       -- 指标类型（CPU/MEMORY/DISK等）
    METRIC_NAME         VARCHAR2(100) NOT NULL,                      -- 指标名称
    METRIC_VALUE        NUMBER(15,6) NOT NULL,                       -- 指标值
    METRIC_UNIT         VARCHAR2(20),                                -- 指标单位
    METRIC_TAGS         JSON,                                        -- 指标标签
    INSTANCE_ID         VARCHAR2(100),                               -- 实例ID
    HOST_NAME           VARCHAR2(100),                               -- 主机名
    APPLICATION_NAME    VARCHAR2(100),                               -- 应用名称
    ENVIRONMENT         VARCHAR2(20),                                -- 环境（DEV/TEST/STAGING/PROD）
    COLLECTION_TIME     TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 采集时间
    AGGREGATION_PERIOD  VARCHAR2(20),                                -- 聚合周期
    MIN_VALUE           NUMBER(15,6),                                -- 最小值
    MAX_VALUE           NUMBER(15,6),                                -- 最大值
    AVG_VALUE           NUMBER(15,6),                                -- 平均值
    SUM_VALUE           NUMBER(15,6),                                -- 总和值
    COUNT_VALUE         NUMBER(10,0),                                -- 计数值
    PERCENTILE_50       NUMBER(15,6),                                -- 50分位数
    PERCENTILE_95       NUMBER(15,6),                                -- 95分位数
    PERCENTILE_99       NUMBER(15,6),                                -- 99分位数
    THRESHOLD_WARNING   NUMBER(15,6),                                -- 警告阈值
    THRESHOLD_CRITICAL  NUMBER(15,6),                                -- 严重阈值
    ALERT_STATUS        VARCHAR2(20) DEFAULT 'NORMAL',              -- 告警状态（NORMAL/WARNING/CRITICAL/UNKNOWN）
    ALERT_MESSAGE       VARCHAR2(500),                               -- 告警消息
    METADATA            JSON,                                        -- 扩展元数据
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CONSTRAINT PK_SYSTEM_PERFORMANCE_LOG PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_PERFORMANCE_LOG_ID UNIQUE (LOG_ID),
    CONSTRAINT CK_SYSTEM_METRIC_TYPE CHECK (METRIC_TYPE IN ('CPU','MEMORY','DISK','NETWORK','DATABASE','APPLICATION','JVM','RESPONSE_TIME','THROUGHPUT','ERROR_RATE')),
    CONSTRAINT CK_SYSTEM_ENVIRONMENT CHECK (ENVIRONMENT IN ('DEV','TEST','STAGING','PROD')),
    CONSTRAINT CK_SYSTEM_ALERT_STATUS CHECK (ALERT_STATUS IN ('NORMAL','WARNING','CRITICAL','UNKNOWN'))
)
PARTITION BY RANGE (COLLECTION_TIME) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
CREATE INDEX IDX_SYSTEM_PERF_TIME ON SYSTEM_PERFORMANCE_LOG(COLLECTION_TIME DESC);
CREATE INDEX IDX_SYSTEM_PERF_METRIC ON SYSTEM_PERFORMANCE_LOG(METRIC_TYPE, METRIC_NAME, COLLECTION_TIME DESC);
CREATE INDEX IDX_SYSTEM_PERF_INSTANCE ON SYSTEM_PERFORMANCE_LOG(INSTANCE_ID, COLLECTION_TIME DESC);
CREATE INDEX IDX_SYSTEM_PERF_ALERT ON SYSTEM_PERFORMANCE_LOG(ALERT_STATUS, COLLECTION_TIME DESC);
CREATE INDEX IDX_SYSTEM_PERF_HOST ON SYSTEM_PERFORMANCE_LOG(HOST_NAME, APPLICATION_NAME);

-- 3. 系统错误日志表 - 增强错误追踪
CREATE TABLE SYSTEM_ERROR_LOG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 错误日志ID，主键
    LOG_ID              VARCHAR2(50) NOT NULL,                       -- 日志唯一标识
    ERROR_ID            VARCHAR2(50),                                -- 错误ID
    ERROR_TYPE          VARCHAR2(50) NOT NULL,                       -- 错误类型（APPLICATION/DATABASE/NETWORK等）
    ERROR_CATEGORY      VARCHAR2(50) NOT NULL,                       -- 错误分类（RUNTIME/COMPILATION/CONFIGURATION等）
    ERROR_SEVERITY      VARCHAR2(20) NOT NULL,                       -- 错误严重程度（LOW/MEDIUM/HIGH/CRITICAL/BLOCKER）
    ERROR_CODE          VARCHAR2(50),                                -- 错误代码
    ERROR_MESSAGE       CLOB NOT NULL,                               -- 错误消息
    ERROR_DESCRIPTION   CLOB,                                        -- 错误描述
    STACK_TRACE         CLOB,                                        -- 堆栈跟踪
    INNER_EXCEPTION     CLOB,                                        -- 内部异常
    SOURCE_FILE         VARCHAR2(500),                               -- 源文件
    SOURCE_LINE         NUMBER(10,0),                                -- 源代码行号
    SOURCE_METHOD       VARCHAR2(200),                               -- 源方法
    SOURCE_CLASS        VARCHAR2(200),                               -- 源类
    APPLICATION_NAME    VARCHAR2(100),                               -- 应用名称
    MODULE_NAME         VARCHAR2(100),                               -- 模块名称
    COMPONENT_NAME      VARCHAR2(100),                               -- 组件名称
    USER_ID             NUMBER(19,0),                                -- 用户ID
    USERNAME            VARCHAR2(100),                               -- 用户名
    SESSION_ID          VARCHAR2(100),                               -- 会话ID
    REQUEST_ID          VARCHAR2(100),                               -- 请求ID
    TRACE_ID            VARCHAR2(50),                                -- 链路追踪ID
    CLIENT_IP           VARCHAR2(45),                                -- 客户端IP
    USER_AGENT          VARCHAR2(1000),                              -- 用户代理
    REQUEST_URL         VARCHAR2(2000),                              -- 请求URL
    REQUEST_METHOD      VARCHAR2(10),                                -- 请求方法
    REQUEST_PARAMS      JSON,                                        -- 请求参数
    ENVIRONMENT_INFO    JSON,                                        -- 环境信息
    SYSTEM_INFO         JSON,                                        -- 系统信息
    BROWSER_INFO        JSON,                                        -- 浏览器信息
    OCCURRENCE_COUNT    NUMBER(10,0) DEFAULT 1,                     -- 发生次数
    FIRST_OCCURRENCE    TIMESTAMP(6),                                -- 首次发生时间
    LAST_OCCURRENCE     TIMESTAMP(6),                                -- 最后发生时间
    RESOLUTION_STATUS   VARCHAR2(20) DEFAULT 'OPEN',                -- 解决状态（OPEN/IN_PROGRESS/RESOLVED等）
    RESOLUTION_ACTION   VARCHAR2(1000),                              -- 解决措施
    ASSIGNED_TO         NUMBER(19,0),                                -- 分配给（用户ID）
    ASSIGNED_DATE       TIMESTAMP(6),                                -- 分配日期
    RESOLVED_DATE       TIMESTAMP(6),                                -- 解决日期
    RESOLVED_BY         NUMBER(19,0),                                -- 解决人ID
    RESOLUTION_NOTES    CLOB,                                        -- 解决备注
    IMPACT_ASSESSMENT   VARCHAR2(20),                                -- 影响评估（LOW/MEDIUM/HIGH/CRITICAL）
    BUSINESS_IMPACT     VARCHAR2(1000),                              -- 业务影响
    WORKAROUND          VARCHAR2(1000),                              -- 临时解决方案
    RELATED_ERRORS      JSON,                                        -- 相关错误
    TAGS                VARCHAR2(1000),                              -- 标签
    CUSTOM_FIELDS       JSON,                                        -- 自定义字段
    NOTIFICATION_SENT   NUMBER(1,0) DEFAULT 0,                      -- 是否已发送通知（1:是,0:否）
    ESCALATION_LEVEL    NUMBER(2,0) DEFAULT 0,                      -- 升级级别
    ESCALATION_DATE     TIMESTAMP(6),                                -- 升级日期
    ERROR_TIMESTAMP     TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 错误时间戳
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CONSTRAINT PK_SYSTEM_ERROR_LOG PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_ERROR_LOG_ID UNIQUE (LOG_ID),
    CONSTRAINT CK_SYSTEM_ERROR_TYPE CHECK (ERROR_TYPE IN ('APPLICATION','DATABASE','NETWORK','SECURITY','VALIDATION','BUSINESS','SYSTEM','INTEGRATION')),
    CONSTRAINT CK_SYSTEM_ERROR_CATEGORY CHECK (ERROR_CATEGORY IN ('RUNTIME','COMPILATION','CONFIGURATION','PERMISSION','TIMEOUT','CONNECTION','DATA','LOGIC')),
    CONSTRAINT CK_SYSTEM_ERROR_SEVERITY CHECK (ERROR_SEVERITY IN ('LOW','MEDIUM','HIGH','CRITICAL','BLOCKER')),
    CONSTRAINT CK_SYSTEM_RESOLUTION_STATUS CHECK (RESOLUTION_STATUS IN ('OPEN','IN_PROGRESS','RESOLVED','CLOSED','DEFERRED','DUPLICATE')),
    CONSTRAINT CK_SYSTEM_IMPACT_ASSESSMENT CHECK (IMPACT_ASSESSMENT IN ('LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_NOTIFICATION_SENT CHECK (NOTIFICATION_SENT IN (0,1))
)
PARTITION BY RANGE (ERROR_TIMESTAMP) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
CREATE INDEX IDX_SYSTEM_ERROR_TIME ON SYSTEM_ERROR_LOG(ERROR_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_ERROR_TYPE ON SYSTEM_ERROR_LOG(ERROR_TYPE, ERROR_SEVERITY, ERROR_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_ERROR_CODE ON SYSTEM_ERROR_LOG(ERROR_CODE, ERROR_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_ERROR_USER ON SYSTEM_ERROR_LOG(USER_ID, ERROR_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_ERROR_STATUS ON SYSTEM_ERROR_LOG(RESOLUTION_STATUS, ERROR_SEVERITY);
CREATE INDEX IDX_SYSTEM_ERROR_ASSIGNED ON SYSTEM_ERROR_LOG(ASSIGNED_TO, ASSIGNED_DATE DESC);
CREATE INDEX IDX_SYSTEM_ERROR_TRACE ON SYSTEM_ERROR_LOG(TRACE_ID, REQUEST_ID);

-- 4. 系统安全日志表 - 新增安全监控
CREATE TABLE SYSTEM_SECURITY_LOG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 安全日志ID，主键
    LOG_ID              VARCHAR2(50) NOT NULL,                       -- 日志唯一标识
    SECURITY_EVENT_TYPE VARCHAR2(50) NOT NULL,                       -- 安全事件类型（LOGIN_FAILURE/BRUTE_FORCE等）
    SECURITY_CATEGORY   VARCHAR2(50) NOT NULL,                       -- 安全分类（AUTHENTICATION/AUTHORIZATION等）
    THREAT_LEVEL        VARCHAR2(20) NOT NULL,                       -- 威胁级别（INFO/LOW/MEDIUM/HIGH/CRITICAL）
    EVENT_DESCRIPTION   VARCHAR2(1000) NOT NULL,                     -- 事件描述
    EVENT_DETAILS       JSON,                                        -- 事件详情
    SOURCE_IP           VARCHAR2(45),                                -- 源IP地址
    SOURCE_PORT         NUMBER(5,0),                                 -- 源端口
    TARGET_IP           VARCHAR2(45),                                -- 目标IP地址
    TARGET_PORT         NUMBER(5,0),                                 -- 目标端口
    PROTOCOL            VARCHAR2(20),                                -- 协议
    USER_ID             NUMBER(19,0),                                -- 用户ID
    USERNAME            VARCHAR2(100),                               -- 用户名
    SESSION_ID          VARCHAR2(100),                               -- 会话ID
    USER_AGENT          VARCHAR2(1000),                              -- 用户代理
    REQUEST_URL         VARCHAR2(2000),                              -- 请求URL
    REQUEST_METHOD      VARCHAR2(10),                                -- 请求方法
    REQUEST_HEADERS     JSON,                                        -- 请求头
    RESPONSE_STATUS     NUMBER(3,0),                                 -- 响应状态码
    ATTACK_VECTOR       VARCHAR2(100),                               -- 攻击向量
    ATTACK_SIGNATURE    VARCHAR2(500),                               -- 攻击特征
    PAYLOAD_DATA        CLOB,                                        -- 载荷数据
    GEO_LOCATION        VARCHAR2(200),                               -- 地理位置
    ISP_INFO            VARCHAR2(200),                               -- ISP信息
    DEVICE_FINGERPRINT  VARCHAR2(500),                               -- 设备指纹
    RISK_SCORE          NUMBER(5,2),                                 -- 风险评分
    CONFIDENCE_LEVEL    NUMBER(5,2),                                 -- 置信度
    DETECTION_METHOD    VARCHAR2(50),                                -- 检测方法（RULE_BASED/ANOMALY_DETECTION等）
    DETECTION_RULE      VARCHAR2(200),                               -- 检测规则
    FALSE_POSITIVE      NUMBER(1,0) DEFAULT 0,                      -- 是否误报（1:是,0:否）
    BLOCKED             NUMBER(1,0) DEFAULT 0,                      -- 是否已阻止（1:是,0:否）
    BLOCK_REASON        VARCHAR2(500),                               -- 阻止原因
    MITIGATION_ACTION   VARCHAR2(100),                               -- 缓解措施
    INVESTIGATION_STATUS VARCHAR2(20) DEFAULT 'NEW',                -- 调查状态（NEW/INVESTIGATING/ANALYZED/CLOSED）
    ASSIGNED_TO         NUMBER(19,0),                                -- 分配给（用户ID）
    ASSIGNED_DATE       TIMESTAMP(6),                                -- 分配日期
    RESOLUTION_STATUS   VARCHAR2(20) DEFAULT 'OPEN',                -- 解决状态（OPEN/IN_PROGRESS/RESOLVED等）
    RESOLUTION_ACTION   VARCHAR2(1000),                              -- 解决措施
    RESOLUTION_DATE     TIMESTAMP(6),                                -- 解决日期
    INCIDENT_ID         VARCHAR2(50),                                -- 事件ID
    RELATED_EVENTS      JSON,                                        -- 相关事件
    COMPLIANCE_IMPACT   VARCHAR2(20),                                -- 合规影响（NONE/LOW/MEDIUM/HIGH/CRITICAL）
    NOTIFICATION_SENT   NUMBER(1,0) DEFAULT 0,                      -- 是否已发送通知（1:是,0:否）
    ESCALATION_REQUIRED NUMBER(1,0) DEFAULT 0,                      -- 是否需要升级（1:是,0:否）
    TAGS                VARCHAR2(1000),                              -- 标签
    CUSTOM_FIELDS       JSON,                                        -- 自定义字段
    EVENT_TIMESTAMP     TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 事件时间戳
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CONSTRAINT PK_SYSTEM_SECURITY_LOG PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_SECURITY_LOG_ID UNIQUE (LOG_ID),
    CONSTRAINT CK_SYSTEM_SECURITY_EVENT_TYPE CHECK (SECURITY_EVENT_TYPE IN ('LOGIN_FAILURE','BRUTE_FORCE','SQL_INJECTION','XSS','CSRF','UNAUTHORIZED_ACCESS','PRIVILEGE_ESCALATION','DATA_BREACH','MALWARE','PHISHING','DDoS','INTRUSION')),
    CONSTRAINT CK_SYSTEM_SECURITY_CATEGORY CHECK (SECURITY_CATEGORY IN ('AUTHENTICATION','AUTHORIZATION','INPUT_VALIDATION','SESSION_MANAGEMENT','ENCRYPTION','NETWORK','APPLICATION','DATABASE')),
    CONSTRAINT CK_SYSTEM_THREAT_LEVEL CHECK (THREAT_LEVEL IN ('INFO','LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_DETECTION_METHOD CHECK (DETECTION_METHOD IN ('RULE_BASED','ANOMALY_DETECTION','SIGNATURE','HEURISTIC','MACHINE_LEARNING','MANUAL')),
    CONSTRAINT CK_SYSTEM_INVESTIGATION_STATUS CHECK (INVESTIGATION_STATUS IN ('NEW','INVESTIGATING','ANALYZED','CLOSED')),
    CONSTRAINT CK_SYSTEM_SEC_RESOLUTION_STATUS CHECK (RESOLUTION_STATUS IN ('OPEN','IN_PROGRESS','RESOLVED','CLOSED','FALSE_POSITIVE')),
    CONSTRAINT CK_SYSTEM_COMPLIANCE_IMPACT CHECK (COMPLIANCE_IMPACT IN ('NONE','LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_SYSTEM_FALSE_POSITIVE CHECK (FALSE_POSITIVE IN (0,1)),
    CONSTRAINT CK_SYSTEM_BLOCKED CHECK (BLOCKED IN (0,1)),
    CONSTRAINT CK_SYSTEM_SEC_NOTIFICATION_SENT CHECK (NOTIFICATION_SENT IN (0,1)),
    CONSTRAINT CK_SYSTEM_ESCALATION_REQUIRED CHECK (ESCALATION_REQUIRED IN (0,1))
)
PARTITION BY RANGE (EVENT_TIMESTAMP) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
CREATE INDEX IDX_SYSTEM_SEC_TIME ON SYSTEM_SECURITY_LOG(EVENT_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_SEC_TYPE ON SYSTEM_SECURITY_LOG(SECURITY_EVENT_TYPE, THREAT_LEVEL, EVENT_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_SEC_IP ON SYSTEM_SECURITY_LOG(SOURCE_IP, EVENT_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_SEC_USER ON SYSTEM_SECURITY_LOG(USER_ID, EVENT_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_SEC_STATUS ON SYSTEM_SECURITY_LOG(INVESTIGATION_STATUS, RESOLUTION_STATUS);
CREATE INDEX IDX_SYSTEM_SEC_RISK ON SYSTEM_SECURITY_LOG(RISK_SCORE DESC, EVENT_TIMESTAMP DESC);
CREATE INDEX IDX_SYSTEM_SEC_INCIDENT ON SYSTEM_SECURITY_LOG(INCIDENT_ID, EVENT_TIMESTAMP DESC);

-- 5. 创建序列
CREATE SEQUENCE SEQ_SYSTEM_OPERATION_LOG START WITH 1 INCREMENT BY 1 CACHE 1000;
CREATE SEQUENCE SEQ_SYSTEM_PERFORMANCE_LOG START WITH 1 INCREMENT BY 1 CACHE 1000;
CREATE SEQUENCE SEQ_SYSTEM_ERROR_LOG START WITH 1 INCREMENT BY 1 CACHE 1000;
CREATE SEQUENCE SEQ_SYSTEM_SECURITY_LOG START WITH 1 INCREMENT BY 1 CACHE 1000;

-- 6. 创建触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_ERROR_LOG_UPD
BEFORE UPDATE ON SYSTEM_ERROR_LOG
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.LAST_OCCURRENCE := SYSTIMESTAMP;
    :NEW.OCCURRENCE_COUNT := :OLD.OCCURRENCE_COUNT + 1;
END;
/

CREATE OR REPLACE TRIGGER TRG_SYSTEM_SECURITY_LOG_UPD
BEFORE UPDATE ON SYSTEM_SECURITY_LOG
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
END;
/

-- 7. 创建视图 - 系统日志统计
CREATE OR REPLACE VIEW VW_SYSTEM_LOG_STATISTICS AS
SELECT 
    TRUNC(sol.LOG_TIMESTAMP) AS LOG_DATE,
    sol.LOG_TYPE,
    sol.LOG_LEVEL,
    sol.LOG_CATEGORY,
    COUNT(*) AS LOG_COUNT,
    COUNT(CASE WHEN sol.LOG_LEVEL = 'ERROR' THEN 1 END) AS ERROR_COUNT,
    COUNT(CASE WHEN sol.LOG_LEVEL = 'WARN' THEN 1 END) AS WARNING_COUNT,
    COUNT(CASE WHEN sol.SECURITY_EVENT = 1 THEN 1 END) AS SECURITY_EVENT_COUNT,
    AVG(sol.EXECUTION_TIME) AS AVG_EXECUTION_TIME,
    MAX(sol.EXECUTION_TIME) AS MAX_EXECUTION_TIME,
    COUNT(DISTINCT sol.USER_ID) AS UNIQUE_USERS,
    COUNT(DISTINCT sol.CLIENT_IP) AS UNIQUE_IPS
FROM SYSTEM_OPERATION_LOG sol
WHERE sol.LOG_TIMESTAMP >= SYSDATE - 30
GROUP BY TRUNC(sol.LOG_TIMESTAMP), sol.LOG_TYPE, sol.LOG_LEVEL, sol.LOG_CATEGORY;

-- 8. 创建物化视图 - 错误趋势分析
CREATE MATERIALIZED VIEW MV_ERROR_TREND_ANALYSIS
REFRESH COMPLETE ON DEMAND
AS
SELECT 
    TRUNC(sel.ERROR_TIMESTAMP, 'HH24') AS ERROR_HOUR,
    sel.ERROR_TYPE,
    sel.ERROR_CATEGORY,
    sel.ERROR_SEVERITY,
    COUNT(*) AS ERROR_COUNT,
    COUNT(DISTINCT sel.USER_ID) AS AFFECTED_USERS,
    COUNT(DISTINCT sel.CLIENT_IP) AS AFFECTED_IPS,
    AVG(sel.OCCURRENCE_COUNT) AS AVG_OCCURRENCE,
    COUNT(CASE WHEN sel.RESOLUTION_STATUS = 'RESOLVED' THEN 1 END) AS RESOLVED_COUNT,
    COUNT(CASE WHEN sel.RESOLUTION_STATUS = 'OPEN' THEN 1 END) AS OPEN_COUNT
FROM SYSTEM_ERROR_LOG sel
WHERE sel.ERROR_TIMESTAMP >= SYSDATE - 7
GROUP BY TRUNC(sel.ERROR_TIMESTAMP, 'HH24'), sel.ERROR_TYPE, sel.ERROR_CATEGORY, sel.ERROR_SEVERITY;

-- 9. 创建函数 - 计算系统健康度
CREATE OR REPLACE FUNCTION FN_CALCULATE_SYSTEM_HEALTH(
    p_hours IN NUMBER DEFAULT 24
) RETURN NUMBER IS
    v_total_requests NUMBER;
    v_error_requests NUMBER;
    v_avg_response_time NUMBER;
    v_security_events NUMBER;
    v_health_score NUMBER := 100;
BEGIN
    -- 获取总请求数
    SELECT COUNT(*) INTO v_total_requests
    FROM SYSTEM_OPERATION_LOG
    WHERE LOG_TIMESTAMP >= SYSTIMESTAMP - INTERVAL '1' HOUR * p_hours
    AND LOG_TYPE = 'ACCESS';
    
    -- 获取错误请求数
    SELECT COUNT(*) INTO v_error_requests
    FROM SYSTEM_OPERATION_LOG
    WHERE LOG_TIMESTAMP >= SYSTIMESTAMP - INTERVAL '1' HOUR * p_hours
    AND LOG_LEVEL = 'ERROR';
    
    -- 获取平均响应时间
    SELECT NVL(AVG(EXECUTION_TIME), 0) INTO v_avg_response_time
    FROM SYSTEM_OPERATION_LOG
    WHERE LOG_TIMESTAMP >= SYSTIMESTAMP - INTERVAL '1' HOUR * p_hours
    AND EXECUTION_TIME IS NOT NULL;
    
    -- 获取安全事件数
    SELECT COUNT(*) INTO v_security_events
    FROM SYSTEM_SECURITY_LOG
    WHERE EVENT_TIMESTAMP >= SYSTIMESTAMP - INTERVAL '1' HOUR * p_hours
    AND THREAT_LEVEL IN ('HIGH', 'CRITICAL');
    
    -- 计算健康度评分
    IF v_total_requests > 0 THEN
        -- 错误率影响 (最多扣30分)
        v_health_score := v_health_score - LEAST((v_error_requests / v_total_requests) * 100, 30);
    END IF;
    
    -- 响应时间影响 (最多扣20分)
    IF v_avg_response_time > 5000 THEN -- 5秒
        v_health_score := v_health_score - LEAST((v_avg_response_time - 5000) / 1000 * 5, 20);
    END IF;
    
    -- 安全事件影响 (最多扣30分)
    v_health_score := v_health_score - LEAST(v_security_events * 5, 30);
    
    RETURN GREATEST(v_health_score, 0);
END;
/

-- 10. 创建存储过程 - 日志清理
CREATE OR REPLACE PROCEDURE SP_CLEANUP_OLD_LOGS(
    p_retention_days IN NUMBER DEFAULT 2555 -- 7年
) IS
    v_cutoff_date DATE;
    v_deleted_count NUMBER;
BEGIN
    v_cutoff_date := SYSDATE - p_retention_days;
    
    -- 清理操作日志
    DELETE FROM SYSTEM_OPERATION_LOG 
    WHERE LOG_TIMESTAMP < v_cutoff_date
    AND COMPLIANCE_FLAG = 0;
    
    v_deleted_count := SQL%ROWCOUNT;
    DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted_count || ' operation log records');
    
    -- 清理性能日志
    DELETE FROM SYSTEM_PERFORMANCE_LOG 
    WHERE COLLECTION_TIME < v_cutoff_date;
    
    v_deleted_count := SQL%ROWCOUNT;
    DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted_count || ' performance log records');
    
    -- 清理已解决的错误日志
    DELETE FROM SYSTEM_ERROR_LOG 
    WHERE ERROR_TIMESTAMP < v_cutoff_date
    AND RESOLUTION_STATUS IN ('RESOLVED', 'CLOSED');
    
    v_deleted_count := SQL%ROWCOUNT;
    DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted_count || ' error log records');
    
    -- 清理低级别安全日志
    DELETE FROM SYSTEM_SECURITY_LOG 
    WHERE EVENT_TIMESTAMP < v_cutoff_date
    AND THREAT_LEVEL IN ('INFO', 'LOW')
    AND RESOLUTION_STATUS = 'CLOSED';
    
    v_deleted_count := SQL%ROWCOUNT;
    DBMS_OUTPUT.PUT_LINE('Deleted ' || v_deleted_count || ' security log records');
    
    COMMIT;
END;
/

-- 11. 创建存储过程 - 日志归档
CREATE OR REPLACE PROCEDURE SP_ARCHIVE_LOGS(
    p_archive_days IN NUMBER DEFAULT 365
) IS
    v_archive_date DATE;
BEGIN
    v_archive_date := SYSDATE - p_archive_days;
    
    -- 这里可以实现将旧日志移动到归档表或外部存储的逻辑
    -- 示例：压缩旧分区
    FOR rec IN (
        SELECT PARTITION_NAME 
        FROM USER_TAB_PARTITIONS 
        WHERE TABLE_NAME = 'SYSTEM_OPERATION_LOG'
        AND PARTITION_NAME LIKE 'P%'
        AND TO_DATE(SUBSTR(PARTITION_NAME, 2), 'YYYYMMDD') < v_archive_date
    ) LOOP
        EXECUTE IMMEDIATE 'ALTER TABLE SYSTEM_OPERATION_LOG MODIFY PARTITION ' || 
                         rec.PARTITION_NAME || ' COMPRESS FOR ARCHIVE HIGH';
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('Log archiving completed');
END;
/

-- 12. 创建同义词和授权
CREATE SYNONYM SYSTEM_LOG_STATS FOR VW_SYSTEM_LOG_STATISTICS;
GRANT SELECT ON VW_SYSTEM_LOG_STATISTICS TO APP_USER;
GRANT SELECT ON MV_ERROR_TREND_ANALYSIS TO APP_USER;
GRANT EXECUTE ON FN_CALCULATE_SYSTEM_HEALTH TO APP_USER;

-- 13. 创建定时任务（可选）
-- BEGIN
--     -- 每日日志清理任务
--     DBMS_SCHEDULER.CREATE_JOB (
--         job_name        => 'JOB_DAILY_LOG_CLEANUP',
--         job_type        => 'PLSQL_BLOCK',
--         job_action      => 'BEGIN SP_CLEANUP_OLD_LOGS(2555); END;',
--         start_date      => SYSTIMESTAMP,
--         repeat_interval => 'FREQ=DAILY;BYHOUR=2;BYMINUTE=0;BYSECOND=0',
--         enabled         => TRUE,
--         comments        => '每日清理过期日志'
--     );
--     
--     -- 每月日志归档任务
--     DBMS_SCHEDULER.CREATE_JOB (
--         job_name        => 'JOB_MONTHLY_LOG_ARCHIVE',
--         job_type        => 'PLSQL_BLOCK',
--         job_action      => 'BEGIN SP_ARCHIVE_LOGS(365); END;',
--         start_date      => SYSTIMESTAMP,
--         repeat_interval => 'FREQ=MONTHLY;BYMONTHDAY=1;BYHOUR=1;BYMINUTE=0;BYSECOND=0',
--         enabled         => TRUE,
--         comments        => '每月归档历史日志'
--     );
-- END;
-- /

-- 表和字段注释
-- 1. 系统操作日志表注释
COMMENT ON TABLE SYSTEM_OPERATION_LOG IS '系统操作日志表 - 记录用户操作和系统事件';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ID IS '日志ID，主键';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.LOG_ID IS '日志唯一标识';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.TRACE_ID IS '分布式追踪ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.SPAN_ID IS '追踪跨度ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.LOG_LEVEL IS '日志级别：TRACE-跟踪，DEBUG-调试，INFO-信息，WARN-警告，ERROR-错误，FATAL-致命';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.LOG_TYPE IS '日志类型：ACCESS-访问，OPERATION-操作，SECURITY-安全，PERFORMANCE-性能，ERROR-错误，AUDIT-审计，BUSINESS-业务';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.LOG_CATEGORY IS '日志分类：USER_AUTH-用户认证，DATA_ACCESS-数据访问，DATA_MODIFY-数据修改，SYSTEM_CONFIG-系统配置，RULE_EXECUTION-规则执行，REPORT_GENERATION-报表生成，FILE_OPERATION-文件操作，API_CALL-API调用，DATABASE_OPERATION-数据库操作';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.LOG_SOURCE IS '日志来源';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.MODULE_NAME IS '模块名称';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.FUNCTION_NAME IS '功能名称';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_TYPE IS '操作类型：CREATE-创建，READ-读取，UPDATE-更新，DELETE-删除，LOGIN-登录，LOGOUT-登出，SEARCH-搜索，EXPORT-导出，IMPORT-导入，APPROVE-审批，REJECT-拒绝，EXECUTE-执行，SCHEDULE-调度';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_NAME IS '操作名称';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.OPERATION_DESC IS '操作描述';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_ID IS '操作用户ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USERNAME IS '操作用户名';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_ROLE IS '用户角色';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.SESSION_ID IS '会话ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_ID IS '请求ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CLIENT_IP IS '客户端IP地址';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CLIENT_PORT IS '客户端端口';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.SERVER_IP IS '服务器IP地址';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.SERVER_PORT IS '服务器端口';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_METHOD IS '请求方法';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_URL IS '请求URL';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_PARAMS IS '请求参数，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_HEADERS IS '请求头，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.REQUEST_BODY IS '请求体';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_STATUS IS '响应状态码';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_HEADERS IS '响应头，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_BODY IS '响应体';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RESPONSE_SIZE IS '响应大小（字节）';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.EXECUTION_TIME IS '执行时间（毫秒）';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.DATABASE_TIME IS '数据库执行时间（毫秒）';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.EXTERNAL_API_TIME IS '外部API调用时间（毫秒）';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.MEMORY_USAGE IS '内存使用量（字节）';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CPU_USAGE IS 'CPU使用率（%）';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.THREAD_ID IS '线程ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.PROCESS_ID IS '进程ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.BUSINESS_KEY IS '业务键';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.BUSINESS_TYPE IS '业务类型';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.AFFECTED_ENTITIES IS '影响的实体，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.BEFORE_DATA IS '操作前数据，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.AFTER_DATA IS '操作后数据，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CHANGE_SUMMARY IS '变更摘要';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ERROR_CODE IS '错误代码';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ERROR_MESSAGE IS '错误信息';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.ERROR_STACK IS '错误堆栈';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.EXCEPTION_TYPE IS '异常类型';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RISK_LEVEL IS '风险级别：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.SECURITY_EVENT IS '是否安全事件，1-是，0-否';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.COMPLIANCE_FLAG IS '合规标识，1-需要合规保留，0-普通日志';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.RETENTION_PERIOD IS '保留期限(天)';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CUSTOM_FIELDS IS '自定义字段，JSON格式';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CORRELATION_ID IS '关联ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.PARENT_LOG_ID IS '父日志ID';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.LOG_TIMESTAMP IS '日志时间戳';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.SERVER_TIMESTAMP IS '服务器时间戳';
COMMENT ON COLUMN SYSTEM_OPERATION_LOG.CREATED_AT IS '创建时间';

-- 2. 系统性能监控日志表注释
COMMENT ON TABLE SYSTEM_PERFORMANCE_LOG IS '系统性能监控日志表 - 记录系统性能指标';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.ID IS '性能日志ID，主键';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.LOG_ID IS '日志唯一标识';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.METRIC_TYPE IS '指标类型：CPU-处理器，MEMORY-内存，DISK-磁盘，NETWORK-网络，DATABASE-数据库，APPLICATION-应用，JVM-Java虚拟机，RESPONSE_TIME-响应时间，THROUGHPUT-吞吐量，ERROR_RATE-错误率';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.METRIC_NAME IS '指标名称';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.METRIC_VALUE IS '指标值';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.METRIC_UNIT IS '指标单位';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.METRIC_TAGS IS '指标标签，JSON格式';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.INSTANCE_ID IS '实例ID';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.HOST_NAME IS '主机名';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.APPLICATION_NAME IS '应用名称';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.ENVIRONMENT IS '环境：DEV-开发，TEST-测试，STAGING-预发布，PROD-生产';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.COLLECTION_TIME IS '采集时间';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.AGGREGATION_PERIOD IS '聚合周期';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.MIN_VALUE IS '最小值';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.MAX_VALUE IS '最大值';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.AVG_VALUE IS '平均值';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.SUM_VALUE IS '总和';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.COUNT_VALUE IS '计数';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.PERCENTILE_50 IS '50百分位数';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.PERCENTILE_95 IS '95百分位数';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.PERCENTILE_99 IS '99百分位数';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.THRESHOLD_WARNING IS '警告阈值';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.THRESHOLD_CRITICAL IS '严重阈值';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.ALERT_STATUS IS '告警状态：NORMAL-正常，WARNING-警告，CRITICAL-严重，UNKNOWN-未知';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.ALERT_MESSAGE IS '告警信息';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SYSTEM_PERFORMANCE_LOG.CREATED_AT IS '创建时间';

-- 3. 系统错误日志表注释
COMMENT ON TABLE SYSTEM_ERROR_LOG IS '系统错误日志表 - 记录系统错误和异常';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ID IS '错误日志ID，主键';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.LOG_ID IS '日志唯一标识';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_ID IS '错误ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_TYPE IS '错误类型：APPLICATION-应用错误，DATABASE-数据库错误，NETWORK-网络错误，SECURITY-安全错误，VALIDATION-验证错误，BUSINESS-业务错误，SYSTEM-系统错误，INTEGRATION-集成错误';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_CATEGORY IS '错误分类：RUNTIME-运行时，COMPILATION-编译，CONFIGURATION-配置，PERMISSION-权限，TIMEOUT-超时，CONNECTION-连接，DATA-数据，LOGIC-逻辑';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_SEVERITY IS '错误严重性：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重，BLOCKER-阻塞';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_CODE IS '错误代码';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_MESSAGE IS '错误信息';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_DESCRIPTION IS '错误描述';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.STACK_TRACE IS '堆栈跟踪';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.INNER_EXCEPTION IS '内部异常';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.SOURCE_FILE IS '源文件';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.SOURCE_LINE IS '源代码行号';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.SOURCE_METHOD IS '源方法';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.SOURCE_CLASS IS '源类';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.APPLICATION_NAME IS '应用名称';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.MODULE_NAME IS '模块名称';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.COMPONENT_NAME IS '组件名称';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.USERNAME IS '用户名';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.SESSION_ID IS '会话ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_ID IS '请求ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.TRACE_ID IS '追踪ID';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.CLIENT_IP IS '客户端IP';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_URL IS '请求URL';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_METHOD IS '请求方法';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.REQUEST_PARAMS IS '请求参数，JSON格式';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ENVIRONMENT_INFO IS '环境信息，JSON格式';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.SYSTEM_INFO IS '系统信息，JSON格式';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.BROWSER_INFO IS '浏览器信息，JSON格式';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.OCCURRENCE_COUNT IS '错误发生次数';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.FIRST_OCCURRENCE IS '首次发生时间';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.LAST_OCCURRENCE IS '最后发生时间';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLUTION_STATUS IS '解决状态：OPEN-开放，IN_PROGRESS-处理中，RESOLVED-已解决，CLOSED-已关闭，DEFERRED-延期，DUPLICATE-重复';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLUTION_ACTION IS '解决措施';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ASSIGNED_TO IS '分配给';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ASSIGNED_DATE IS '分配日期';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLVED_DATE IS '解决日期';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLVED_BY IS '解决人';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RESOLUTION_NOTES IS '解决备注';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.IMPACT_ASSESSMENT IS '影响评估：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.BUSINESS_IMPACT IS '业务影响';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.WORKAROUND IS '临时解决方案';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.RELATED_ERRORS IS '相关错误，JSON格式';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.CUSTOM_FIELDS IS '自定义字段，JSON格式';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.NOTIFICATION_SENT IS '是否已发送通知，1-是，0-否';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ESCALATION_LEVEL IS '升级级别';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ESCALATION_DATE IS '升级日期';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.ERROR_TIMESTAMP IS '错误时间戳';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SYSTEM_ERROR_LOG.UPDATED_AT IS '更新时间';

-- 4. 系统安全日志表注释
COMMENT ON TABLE SYSTEM_SECURITY_LOG IS '系统安全日志表 - 记录安全事件和威胁';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ID IS '安全日志ID，主键';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.LOG_ID IS '日志唯一标识';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.SECURITY_EVENT_TYPE IS '安全事件类型：LOGIN_FAILURE-登录失败，BRUTE_FORCE-暴力破解，SQL_INJECTION-SQL注入，XSS-跨站脚本，CSRF-跨站请求伪造，UNAUTHORIZED_ACCESS-未授权访问，PRIVILEGE_ESCALATION-权限提升，DATA_BREACH-数据泄露，MALWARE-恶意软件，PHISHING-钓鱼，DDoS-分布式拒绝服务，INTRUSION-入侵';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.SECURITY_CATEGORY IS '安全分类：AUTHENTICATION-认证，AUTHORIZATION-授权，INPUT_VALIDATION-输入验证，SESSION_MANAGEMENT-会话管理，ENCRYPTION-加密，NETWORK-网络，APPLICATION-应用，DATABASE-数据库';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.THREAT_LEVEL IS '威胁级别：INFO-信息，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.EVENT_DESCRIPTION IS '事件描述';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.EVENT_DETAILS IS '事件详情，JSON格式';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.SOURCE_IP IS '源IP地址';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.SOURCE_PORT IS '源端口';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.TARGET_IP IS '目标IP地址';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.TARGET_PORT IS '目标端口';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.PROTOCOL IS '协议';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.USERNAME IS '用户名';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.SESSION_ID IS '会话ID';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.REQUEST_URL IS '请求URL';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.REQUEST_METHOD IS '请求方法';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.REQUEST_HEADERS IS '请求头，JSON格式';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.RESPONSE_STATUS IS '响应状态码';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ATTACK_VECTOR IS '攻击向量';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ATTACK_SIGNATURE IS '攻击特征';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.PAYLOAD_DATA IS '载荷数据';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.GEO_LOCATION IS '地理位置';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ISP_INFO IS 'ISP信息';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.DEVICE_FINGERPRINT IS '设备指纹';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.RISK_SCORE IS '风险评分';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.CONFIDENCE_LEVEL IS '检测置信度';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.DETECTION_METHOD IS '检测方法：RULE_BASED-基于规则，ANOMALY_DETECTION-异常检测，SIGNATURE-特征，HEURISTIC-启发式，MACHINE_LEARNING-机器学习，MANUAL-手动';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.DETECTION_RULE IS '检测规则';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.FALSE_POSITIVE IS '是否误报，1-是，0-否';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.BLOCKED IS '是否已阻止，1-是，0-否';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.BLOCK_REASON IS '阻止原因';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.MITIGATION_ACTION IS '缓解措施';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.INVESTIGATION_STATUS IS '调查状态：NEW-新建，INVESTIGATING-调查中，ANALYZED-已分析，CLOSED-已关闭';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ASSIGNED_TO IS '分配给';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ASSIGNED_DATE IS '分配日期';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.RESOLUTION_STATUS IS '解决状态：OPEN-开放，IN_PROGRESS-处理中，RESOLVED-已解决，CLOSED-已关闭，FALSE_POSITIVE-误报';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.RESOLUTION_ACTION IS '解决措施';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.RESOLUTION_DATE IS '解决日期';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.INCIDENT_ID IS '事件ID';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.RELATED_EVENTS IS '相关事件，JSON格式';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.COMPLIANCE_IMPACT IS '合规影响：NONE-无，LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.NOTIFICATION_SENT IS '是否已发送通知，1-是，0-否';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.ESCALATION_REQUIRED IS '是否需要升级，1-是，0-否';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.TAGS IS '标签';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.CUSTOM_FIELDS IS '自定义字段，JSON格式';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.EVENT_TIMESTAMP IS '事件时间戳';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SYSTEM_SECURITY_LOG.UPDATED_AT IS '更新时间';

-- 5. 视图和函数注释
COMMENT ON MATERIALIZED VIEW VW_SYSTEM_LOG_STATISTICS IS '系统日志统计视图 - 提供日志统计分析';
COMMENT ON MATERIALIZED VIEW MV_ERROR_TREND_ANALYSIS IS '错误趋势分析物化视图 - 提供错误趋势分析数据';