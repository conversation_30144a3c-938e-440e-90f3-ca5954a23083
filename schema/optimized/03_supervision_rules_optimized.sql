-- =====================================================
-- 医保基金监管平台 - 监管规则模块优化表结构 (Oracle 19c)
-- =====================================================

-- 1. 监管规则表
CREATE TABLE SUPERVISION_RULE (
    ID                  NUMBER(19,0) NOT NULL,                       -- 规则ID，主键
    RULE_CODE           VARCHAR2(50) NOT NULL,                       -- 规则编码，唯一标识
    RULE_NAME           VARCHAR2(200) NOT NULL,                      -- 规则名称
    RULE_DESCRIPTION    CLOB,                                        -- 规则描述
    RULE_TYPE           VARCHAR2(50) NOT NULL,                       -- 规则类型（VALIDATION/CALCULATION/DETECTION等）
    RULE_CATEGORY       VARCHAR2(50) NOT NULL,                       -- 规则分类（COST/FREQUENCY/DIAGNOSIS等）
    BUSINESS_DOMAIN     VARCHAR2(50) NOT NULL,                       -- 业务领域（MEDICAL/PHARMACY/HOSPITAL等）
    RULE_EXPRESSION     CLOB NOT NULL,                               -- 规则表达式
    RULE_CONDITION      JSON,                                        -- 规则条件，JSON格式
    RULE_PARAMETERS     JSON,                                        -- 规则参数，JSON格式
    THRESHOLD_CONFIG    JSON,                                        -- 阈值配置，JSON格式
    PRIORITY_LEVEL      NUMBER(2,0) DEFAULT 5 NOT NULL,              -- 优先级（1-10）
    SEVERITY_LEVEL      VARCHAR2(20) DEFAULT 'MEDIUM' NOT NULL,     -- 严重程度（LOW/MEDIUM/HIGH/CRITICAL）
    RISK_WEIGHT         NUMBER(5,2) DEFAULT 1.0 NOT NULL,           -- 风险权重
    EXECUTION_MODE      VARCHAR2(20) DEFAULT 'SYNC' NOT NULL,       -- 执行模式（SYNC/ASYNC/BATCH）
    EXECUTION_FREQUENCY VARCHAR2(20) DEFAULT 'REALTIME' NOT NULL,   -- 执行频率（REALTIME/HOURLY/DAILY等）
    TIMEOUT_SECONDS     NUMBER(6,0) DEFAULT 30,                     -- 超时时间（秒）
    RETRY_COUNT         NUMBER(2,0) DEFAULT 3,                      -- 重试次数
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,              -- 是否激活（1:激活,0:禁用）
    IS_SYSTEM_RULE      NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 是否系统规则（1:是,0:否）
    EFFECTIVE_DATE      DATE DEFAULT SYSDATE NOT NULL,               -- 生效日期
    EXPIRY_DATE         DATE,                                        -- 失效日期
    RULE_VERSION        VARCHAR2(20) DEFAULT '1.0' NOT NULL,        -- 规则版本
    RULE_GROUP          VARCHAR2(50),                                -- 规则组
    TAGS                VARCHAR2(1000),                              -- 标签
    APPROVAL_STATUS     VARCHAR2(20) DEFAULT 'DRAFT' NOT NULL,      -- 审批状态（DRAFT/PENDING/APPROVED等）
    APPROVER_ID         NUMBER(19,0),                                -- 审批人ID
    APPROVAL_DATE       TIMESTAMP(6),                                -- 审批日期
    APPROVAL_COMMENTS   VARCHAR2(1000),                              -- 审批意见
    METADATA            JSON,                                        -- 扩展元数据
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 逻辑删除标识（0:未删除,1:已删除）
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    CONSTRAINT PK_SUPERVISION_RULE PRIMARY KEY (ID),
    CONSTRAINT UK_SUPERVISION_RULE_CODE UNIQUE (RULE_CODE),
    CONSTRAINT CK_RULE_TYPE CHECK (RULE_TYPE IN ('VALIDATION','CALCULATION','DETECTION','PREVENTION','ANALYSIS','ALERT')),
    CONSTRAINT CK_RULE_CATEGORY CHECK (RULE_CATEGORY IN ('COST','FREQUENCY','DIAGNOSIS','TREATMENT','PRESCRIPTION','FRAUD','ABUSE')),
    CONSTRAINT CK_RULE_BUSINESS_DOMAIN CHECK (BUSINESS_DOMAIN IN ('MEDICAL','PHARMACY','HOSPITAL','DOCTOR','PATIENT','INSURANCE')),
    CONSTRAINT CK_RULE_SEVERITY CHECK (SEVERITY_LEVEL IN ('LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_RULE_EXECUTION_MODE CHECK (EXECUTION_MODE IN ('SYNC','ASYNC','BATCH')),
    CONSTRAINT CK_RULE_EXECUTION_FREQ CHECK (EXECUTION_FREQUENCY IN ('REALTIME','HOURLY','DAILY','WEEKLY','MONTHLY','MANUAL')),
    CONSTRAINT CK_RULE_APPROVAL_STATUS CHECK (APPROVAL_STATUS IN ('DRAFT','PENDING','APPROVED','REJECTED','SUSPENDED')),
    CONSTRAINT CK_RULE_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_RULE_IS_SYSTEM CHECK (IS_SYSTEM_RULE IN (0,1)),
    CONSTRAINT CK_RULE_IS_DELETED CHECK (IS_DELETED IN (0,1)),
    CONSTRAINT CK_RULE_PRIORITY CHECK (PRIORITY_LEVEL BETWEEN 1 AND 10)
);

-- 2. 监管执行记录表
CREATE TABLE SUPERVISION_EXECUTION_LOG (
    ID                  NUMBER(19,0) NOT NULL,                       -- 执行记录ID，主键
    RULE_ID             NUMBER(19,0) NOT NULL,                       -- 规则ID，外键
    EXECUTION_ID        VARCHAR2(50) NOT NULL,                       -- 执行批次ID，唯一标识
    EXECUTION_TYPE      VARCHAR2(20) NOT NULL,                       -- 执行类型（MANUAL/SCHEDULED/TRIGGERED等）
    TRIGGER_SOURCE      VARCHAR2(50),                                -- 触发源
    TRIGGER_EVENT       VARCHAR2(100),                               -- 触发事件
    INPUT_DATA          JSON,                                        -- 输入数据，JSON格式
    EXECUTION_CONTEXT   JSON,                                        -- 执行上下文，JSON格式
    START_TIME          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 开始时间
    END_TIME            TIMESTAMP(6),                                -- 结束时间
    EXECUTION_DURATION  NUMBER(8,2),                                 -- 执行时长（秒）
    EXECUTION_STATUS    VARCHAR2(20) NOT NULL,                       -- 执行状态（RUNNING/COMPLETED/FAILED等）
    MATCHED_COUNT       NUMBER(10,0) DEFAULT 0,                     -- 匹配记录数
    VIOLATION_COUNT     NUMBER(10,0) DEFAULT 0,                     -- 违规记录数
    WARNING_COUNT       NUMBER(10,0) DEFAULT 0,                     -- 警告记录数
    ERROR_MESSAGE       CLOB,                                        -- 错误信息
    ERROR_STACK         CLOB,                                        -- 错误堆栈
    RETRY_ATTEMPT       NUMBER(2,0) DEFAULT 0,                      -- 重试次数
    NEXT_RETRY_TIME     TIMESTAMP(6),                                -- 下次重试时间
    PERFORMANCE_METRICS JSON,                                        -- 性能指标，JSON格式
    RESOURCE_USAGE      JSON,                                        -- 资源使用情况，JSON格式
    AFFECTED_ENTITIES   JSON,                                        -- 影响的实体，JSON格式
    NOTIFICATION_SENT   NUMBER(1,0) DEFAULT 0,                      -- 是否已发送通知（1:是,0:否）
    METADATA            JSON,                                        -- 元数据，JSON格式
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CONSTRAINT PK_SUPERVISION_EXECUTION_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_SUPERVISION_EXECUTION_RULE FOREIGN KEY (RULE_ID) REFERENCES SUPERVISION_RULE(ID),
    CONSTRAINT UK_SUPERVISION_EXECUTION_ID UNIQUE (EXECUTION_ID),
    CONSTRAINT CK_EXECUTION_TYPE CHECK (EXECUTION_TYPE IN ('MANUAL','SCHEDULED','TRIGGERED','BATCH','REALTIME')),
    CONSTRAINT CK_EXECUTION_STATUS CHECK (EXECUTION_STATUS IN ('RUNNING','COMPLETED','FAILED','TIMEOUT','CANCELLED')),
    CONSTRAINT CK_NOTIFICATION_SENT CHECK (NOTIFICATION_SENT IN (0,1))
)
PARTITION BY RANGE (START_TIME) 
INTERVAL (INTERVAL '1' DAY) (
    PARTITION P20240101 VALUES LESS THAN (TIMESTAMP '2024-01-02 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 3. 监管执行结果表
CREATE TABLE SUPERVISION_EXECUTION_RESULT (
    ID                  NUMBER(19,0) NOT NULL,                       -- 结果记录ID，主键
    EXECUTION_LOG_ID    NUMBER(19,0) NOT NULL,                       -- 执行记录ID，外键
    RULE_ID             NUMBER(19,0) NOT NULL,                       -- 规则ID，外键
    RESULT_TYPE         VARCHAR2(20) NOT NULL,                       -- 结果类型（VIOLATION/WARNING/NORMAL）
    RESULT_STATUS       VARCHAR2(20) NOT NULL,                       -- 结果状态（PASS/FAIL/WARNING等）
    ENTITY_TYPE         VARCHAR2(50) NOT NULL,                       -- 实体类型
    ENTITY_ID           NUMBER(19,0) NOT NULL,                       -- 实体ID
    ENTITY_CODE         VARCHAR2(100),                               -- 实体编码
    ENTITY_NAME         VARCHAR2(200),                               -- 实体名称
    VIOLATION_CODE      VARCHAR2(50),                                -- 违规编码
    VIOLATION_TYPE      VARCHAR2(50),                                -- 违规类型
    VIOLATION_LEVEL     VARCHAR2(20),                                -- 违规级别
    VIOLATION_DETAILS   JSON,                                        -- 违规详情，JSON格式
    VIOLATION_AMOUNT    NUMBER(15,2),                                -- 违规金额
    RISK_SCORE          NUMBER(5,2),                                 -- 风险评分
    CONFIDENCE_LEVEL    NUMBER(5,2),                                 -- 置信度
    EVIDENCE_DATA       JSON,                                        -- 证据数据，JSON格式
    RELATED_ENTITIES    JSON,                                        -- 关联实体，JSON格式
    DETECTION_TIME      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 检测时间
    OCCURRENCE_PATTERN  VARCHAR2(100),                               -- 发生模式
    METADATA            JSON,                                        -- 元数据，JSON格式
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    CONSTRAINT PK_SUPERVISION_EXECUTION_RESULT PRIMARY KEY (ID),
    CONSTRAINT FK_SUPERVISION_RESULT_LOG FOREIGN KEY (EXECUTION_LOG_ID) REFERENCES SUPERVISION_EXECUTION_LOG(ID),
    CONSTRAINT FK_SUPERVISION_RESULT_RULE FOREIGN KEY (RULE_ID) REFERENCES SUPERVISION_RULE(ID),
    CONSTRAINT CK_RESULT_TYPE CHECK (RESULT_TYPE IN ('VIOLATION','WARNING','NORMAL','ERROR')),
    CONSTRAINT CK_RESULT_STATUS CHECK (RESULT_STATUS IN ('PASS','FAIL','WARNING','ERROR','PARTIAL')),
    CONSTRAINT CK_RESULT_ENTITY_TYPE CHECK (ENTITY_TYPE IN ('CASE','PATIENT','DOCTOR','HOSPITAL','PRESCRIPTION','DIAGNOSIS','SURGERY','COST_ITEM')),
    CONSTRAINT CK_RESULT_VIOLATION_TYPE CHECK (VIOLATION_TYPE IN ('COST_OVERRUN','FREQUENCY_ABUSE','DIAGNOSIS_MISMATCH','TREATMENT_INAPPROPRIATE','PRESCRIPTION_ERROR','FRAUD_SUSPECTED','DATA_ANOMALY')),
    CONSTRAINT CK_RESULT_VIOLATION_LEVEL CHECK (VIOLATION_LEVEL IN ('INFO','WARNING','MINOR','MAJOR','CRITICAL'))
)
PARTITION BY RANGE (DETECTION_TIME) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 4. 审核记录表
CREATE TABLE SUPERVISION_AUDIT_RECORD (
    ID                  NUMBER(19,0) NOT NULL,                       -- 审核记录ID，主键
    RESULT_ID           NUMBER(19,0) NOT NULL,                       -- 执行结果ID，外键
    RULE_ID             NUMBER(19,0) NOT NULL,                       -- 规则ID，外键
    AUDIT_TYPE          VARCHAR2(20) NOT NULL,                       -- 审核类型（MANUAL/AUTO/REVIEW）
    AUDIT_STATUS        VARCHAR2(20) DEFAULT 'PENDING' NOT NULL,    -- 审核状态（PENDING/APPROVED/REJECTED/CLOSED）
    AUDIT_RESULT        VARCHAR2(20),                                -- 审核结果（CONFIRMED/FALSE_POSITIVE/RESOLVED）
    AUDITOR_ID          NUMBER(19,0),                                -- 审核人ID
    AUDITOR_NAME        VARCHAR2(100),                               -- 审核人姓名
    AUDIT_DATE          TIMESTAMP(6),                                -- 审核日期
    AUDIT_COMMENTS      CLOB,                                        -- 审核意见
    HANDLING_STATUS     VARCHAR2(20) DEFAULT 'PENDING' NOT NULL,    -- 处理状态（PENDING/ASSIGNED/IN_PROGRESS/COMPLETED）
    ASSIGNED_TO         NUMBER(19,0),                                -- 分配给（用户ID）
    ASSIGNED_DATE       TIMESTAMP(6),                                -- 分配日期
    RESOLUTION_ACTION   VARCHAR2(100),                               -- 解决措施
    RESOLUTION_RESULT   VARCHAR2(20),                                -- 解决结果
    RESOLUTION_AMOUNT   NUMBER(15,2),                                -- 解决金额
    RESOLUTION_DATE     TIMESTAMP(6),                                -- 解决日期
    RESOLUTION_COMMENTS CLOB,                                        -- 解决备注
    FOLLOW_UP_REQUIRED  NUMBER(1,0) DEFAULT 0,                      -- 是否需要跟进
    FOLLOW_UP_DATE      DATE,                                        -- 跟进日期
    ESCALATION_LEVEL    NUMBER(2,0) DEFAULT 0,                      -- 升级级别
    ESCALATION_DATE     TIMESTAMP(6),                                -- 升级日期
    NOTIFICATION_SENT   NUMBER(1,0) DEFAULT 0,                      -- 是否已发送通知
    METADATA            JSON,                                        -- 元数据，JSON格式
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,              -- 逻辑删除标识
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 创建时间
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,  -- 更新时间
    CREATED_BY          NUMBER(19,0),                                -- 创建人ID
    UPDATED_BY          NUMBER(19,0),                                -- 更新人ID
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,             -- 版本号，用于乐观锁
    CONSTRAINT PK_SUPERVISION_AUDIT_RECORD PRIMARY KEY (ID),
    CONSTRAINT FK_SUPERVISION_AUDIT_RESULT FOREIGN KEY (RESULT_ID) REFERENCES SUPERVISION_EXECUTION_RESULT(ID),
    CONSTRAINT FK_SUPERVISION_AUDIT_RULE FOREIGN KEY (RULE_ID) REFERENCES SUPERVISION_RULE(ID),
    CONSTRAINT CK_AUDIT_TYPE CHECK (AUDIT_TYPE IN ('MANUAL','AUTO','REVIEW','APPEAL')),
    CONSTRAINT CK_AUDIT_STATUS CHECK (AUDIT_STATUS IN ('PENDING','APPROVED','REJECTED','CLOSED','SUSPENDED')),
    CONSTRAINT CK_AUDIT_RESULT CHECK (AUDIT_RESULT IN ('CONFIRMED','FALSE_POSITIVE','RESOLVED','PENDING_REVIEW','NO_ACTION')),
    CONSTRAINT CK_AUDIT_HANDLING_STATUS CHECK (HANDLING_STATUS IN ('PENDING','ASSIGNED','IN_PROGRESS','ESCALATED','COMPLETED')),
    CONSTRAINT CK_AUDIT_RESOLUTION_RESULT CHECK (RESOLUTION_RESULT IN ('RESOLVED','PENALTY','WARNING','EDUCATION','REFERRAL','NO_ACTION')),
    CONSTRAINT CK_AUDIT_FOLLOW_UP CHECK (FOLLOW_UP_REQUIRED IN (0,1)),
    CONSTRAINT CK_AUDIT_NOTIFICATION CHECK (NOTIFICATION_SENT IN (0,1)),
    CONSTRAINT CK_AUDIT_IS_DELETED CHECK (IS_DELETED IN (0,1))
)
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (INTERVAL '1' MONTH) (
    PARTITION P202401 VALUES LESS THAN (TIMESTAMP '2024-02-01 00:00:00')
)
COMPRESS FOR ARCHIVE HIGH;

-- 创建索引
-- 监管规则表索引
CREATE INDEX IDX_SUPERVISION_RULE_TYPE ON SUPERVISION_RULE(RULE_TYPE, RULE_CATEGORY);
CREATE INDEX IDX_SUPERVISION_RULE_STATUS ON SUPERVISION_RULE(IS_ACTIVE, APPROVAL_STATUS);
CREATE INDEX IDX_SUPERVISION_RULE_PRIORITY ON SUPERVISION_RULE(PRIORITY_LEVEL, SEVERITY_LEVEL);
CREATE INDEX IDX_SUPERVISION_RULE_DOMAIN ON SUPERVISION_RULE(BUSINESS_DOMAIN, IS_ACTIVE);
CREATE INDEX IDX_SUPERVISION_RULE_GROUP ON SUPERVISION_RULE(RULE_GROUP, IS_ACTIVE);
CREATE INDEX IDX_SUPERVISION_RULE_CREATED ON SUPERVISION_RULE(CREATED_AT DESC);

-- 监管执行记录表索引
CREATE INDEX IDX_SUPERVISION_EXECUTION_RULE ON SUPERVISION_EXECUTION_LOG(RULE_ID, START_TIME DESC);
CREATE INDEX IDX_SUPERVISION_EXECUTION_STATUS ON SUPERVISION_EXECUTION_LOG(EXECUTION_STATUS);
CREATE INDEX IDX_SUPERVISION_EXECUTION_TYPE ON SUPERVISION_EXECUTION_LOG(EXECUTION_TYPE, START_TIME DESC);
CREATE INDEX IDX_SUPERVISION_EXECUTION_DURATION ON SUPERVISION_EXECUTION_LOG(EXECUTION_DURATION DESC);
CREATE INDEX IDX_SUPERVISION_EXECUTION_VIOLATION ON SUPERVISION_EXECUTION_LOG(VIOLATION_COUNT DESC, START_TIME DESC);

-- 监管执行结果表索引
CREATE INDEX IDX_SUPERVISION_RESULT_LOG ON SUPERVISION_EXECUTION_RESULT(EXECUTION_LOG_ID, DETECTION_TIME DESC);
CREATE INDEX IDX_SUPERVISION_RESULT_RULE ON SUPERVISION_EXECUTION_RESULT(RULE_ID, DETECTION_TIME DESC);
CREATE INDEX IDX_SUPERVISION_RESULT_ENTITY ON SUPERVISION_EXECUTION_RESULT(ENTITY_TYPE, ENTITY_ID);
CREATE INDEX IDX_SUPERVISION_RESULT_TYPE ON SUPERVISION_EXECUTION_RESULT(RESULT_TYPE, VIOLATION_LEVEL);
CREATE INDEX IDX_SUPERVISION_RESULT_AMOUNT ON SUPERVISION_EXECUTION_RESULT(VIOLATION_AMOUNT DESC, DETECTION_TIME DESC);

-- 审核记录表索引
CREATE INDEX IDX_SUPERVISION_AUDIT_RESULT ON SUPERVISION_AUDIT_RECORD(RESULT_ID, CREATED_AT DESC);
CREATE INDEX IDX_SUPERVISION_AUDIT_RULE ON SUPERVISION_AUDIT_RECORD(RULE_ID, CREATED_AT DESC);
CREATE INDEX IDX_SUPERVISION_AUDIT_STATUS ON SUPERVISION_AUDIT_RECORD(AUDIT_STATUS, HANDLING_STATUS);
CREATE INDEX IDX_SUPERVISION_AUDIT_AUDITOR ON SUPERVISION_AUDIT_RECORD(AUDITOR_ID, AUDIT_DATE DESC);
CREATE INDEX IDX_SUPERVISION_AUDIT_ASSIGNED ON SUPERVISION_AUDIT_RECORD(ASSIGNED_TO, ASSIGNED_DATE DESC);

-- 创建序列
CREATE SEQUENCE SEQ_SUPERVISION_RULE START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SUPERVISION_EXECUTION_LOG START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SUPERVISION_EXECUTION_RESULT START WITH 1 INCREMENT BY 1 CACHE 100;
CREATE SEQUENCE SEQ_SUPERVISION_AUDIT_RECORD START WITH 1 INCREMENT BY 1 CACHE 100;

-- 创建触发器
CREATE OR REPLACE TRIGGER TRG_SUPERVISION_RULE_UPD
BEFORE UPDATE ON SUPERVISION_RULE
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

CREATE OR REPLACE TRIGGER TRG_SUPERVISION_AUDIT_UPD
BEFORE UPDATE ON SUPERVISION_AUDIT_RECORD
FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := SYSTIMESTAMP;
    :NEW.VERSION := :OLD.VERSION + 1;
END;
/

-- 创建视图 - 监管规则执行统计
CREATE OR REPLACE VIEW VW_SUPERVISION_RULE_STATISTICS AS
SELECT 
    sr.ID AS RULE_ID,
    sr.RULE_CODE,
    sr.RULE_NAME,
    sr.RULE_TYPE,
    sr.RULE_CATEGORY,
    sr.PRIORITY_LEVEL,
    sr.SEVERITY_LEVEL,
    sr.IS_ACTIVE,
    COUNT(sel.ID) AS TOTAL_EXECUTIONS,
    COUNT(CASE WHEN sel.EXECUTION_STATUS = 'COMPLETED' THEN 1 END) AS SUCCESS_EXECUTIONS,
    COUNT(CASE WHEN sel.EXECUTION_STATUS = 'FAILED' THEN 1 END) AS FAILED_EXECUTIONS,
    ROUND(COUNT(CASE WHEN sel.EXECUTION_STATUS = 'COMPLETED' THEN 1 END) / NULLIF(COUNT(sel.ID), 0) * 100, 2) AS SUCCESS_RATE,
    AVG(sel.EXECUTION_DURATION) AS AVG_EXECUTION_TIME,
    MAX(sel.START_TIME) AS LAST_EXECUTION,
    SUM(sel.VIOLATION_COUNT) AS TOTAL_VIOLATIONS,
    SUM(sel.WARNING_COUNT) AS TOTAL_WARNINGS
FROM SUPERVISION_RULE sr
LEFT JOIN SUPERVISION_EXECUTION_LOG sel ON sr.ID = sel.RULE_ID
WHERE sr.IS_DELETED = 0
GROUP BY sr.ID, sr.RULE_CODE, sr.RULE_NAME, sr.RULE_TYPE, sr.RULE_CATEGORY,
         sr.PRIORITY_LEVEL, sr.SEVERITY_LEVEL, sr.IS_ACTIVE;

-- 创建视图 - 违规处理统计
CREATE OR REPLACE VIEW VW_SUPERVISION_VIOLATION_STATISTICS AS
SELECT 
    ser.RULE_ID,
    sr.RULE_NAME,
    ser.VIOLATION_TYPE,
    ser.VIOLATION_LEVEL,
    ser.ENTITY_TYPE,
    COUNT(*) AS TOTAL_VIOLATIONS,
    COUNT(sar.ID) AS AUDITED_COUNT,
    COUNT(CASE WHEN sar.AUDIT_RESULT = 'CONFIRMED' THEN 1 END) AS CONFIRMED_COUNT,
    COUNT(CASE WHEN sar.AUDIT_RESULT = 'FALSE_POSITIVE' THEN 1 END) AS FALSE_POSITIVE_COUNT,
    COUNT(CASE WHEN sar.HANDLING_STATUS = 'COMPLETED' THEN 1 END) AS RESOLVED_COUNT,
    SUM(ser.VIOLATION_AMOUNT) AS TOTAL_VIOLATION_AMOUNT,
    SUM(sar.RESOLUTION_AMOUNT) AS TOTAL_RESOLUTION_AMOUNT,
    AVG(ser.RISK_SCORE) AS AVG_RISK_SCORE,
    AVG(ser.CONFIDENCE_LEVEL) AS AVG_CONFIDENCE_LEVEL
FROM SUPERVISION_EXECUTION_RESULT ser
JOIN SUPERVISION_RULE sr ON ser.RULE_ID = sr.ID
LEFT JOIN SUPERVISION_AUDIT_RECORD sar ON ser.ID = sar.RESULT_ID AND sar.IS_DELETED = 0
WHERE ser.RESULT_TYPE = 'VIOLATION'
GROUP BY ser.RULE_ID, sr.RULE_NAME, ser.VIOLATION_TYPE, ser.VIOLATION_LEVEL, ser.ENTITY_TYPE;

-- 创建物化视图 - 违规趋势分析
CREATE MATERIALIZED VIEW MV_SUPERVISION_VIOLATION_TREND
REFRESH COMPLETE ON DEMAND
AS
SELECT 
    TRUNC(ser.DETECTION_TIME) AS DETECTION_DATE,
    ser.VIOLATION_TYPE,
    ser.VIOLATION_LEVEL,
    ser.ENTITY_TYPE,
    COUNT(*) AS VIOLATION_COUNT,
    COUNT(CASE WHEN sar.AUDIT_RESULT = 'CONFIRMED' THEN 1 END) AS CONFIRMED_COUNT,
    COUNT(CASE WHEN sar.AUDIT_RESULT = 'FALSE_POSITIVE' THEN 1 END) AS FALSE_POSITIVE_COUNT,
    AVG(ser.RISK_SCORE) AS AVG_RISK_SCORE,
    SUM(ser.VIOLATION_AMOUNT) AS TOTAL_AMOUNT,
    AVG(ser.VIOLATION_AMOUNT) AS AVG_AMOUNT
FROM SUPERVISION_EXECUTION_RESULT ser
LEFT JOIN SUPERVISION_AUDIT_RECORD sar ON ser.ID = sar.RESULT_ID AND sar.IS_DELETED = 0
WHERE ser.RESULT_TYPE = 'VIOLATION'
GROUP BY TRUNC(ser.DETECTION_TIME), ser.VIOLATION_TYPE, ser.VIOLATION_LEVEL, ser.ENTITY_TYPE;

-- 创建函数 - 获取规则执行摘要
CREATE OR REPLACE FUNCTION FN_GET_RULE_EXECUTION_SUMMARY(
    p_rule_id IN NUMBER,
    p_days IN NUMBER DEFAULT 30
) RETURN CLOB IS
    v_summary CLOB;
    v_rule_name VARCHAR2(200);
    v_total_executions NUMBER;
    v_success_rate NUMBER;
    v_total_violations NUMBER;
BEGIN
    -- 获取规则基本信息
    SELECT RULE_NAME INTO v_rule_name
    FROM SUPERVISION_RULE
    WHERE ID = p_rule_id;
    
    -- 获取执行统计
    SELECT 
        COUNT(*),
        ROUND(COUNT(CASE WHEN EXECUTION_STATUS = 'COMPLETED' THEN 1 END) / NULLIF(COUNT(*), 0) * 100, 2),
        SUM(VIOLATION_COUNT)
    INTO v_total_executions, v_success_rate, v_total_violations
    FROM SUPERVISION_EXECUTION_LOG
    WHERE RULE_ID = p_rule_id
    AND START_TIME >= SYSDATE - p_days;
    
    v_summary := '规则执行摘要 - ' || v_rule_name || CHR(10) ||
                '统计周期: 最近' || p_days || '天' || CHR(10) ||
                '总执行次数: ' || NVL(v_total_executions, 0) || CHR(10) ||
                '成功率: ' || NVL(v_success_rate, 0) || '%' || CHR(10) ||
                '发现违规: ' || NVL(v_total_violations, 0) || '次';
    
    RETURN v_summary;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN '规则不存在';
    WHEN OTHERS THEN
        RETURN '获取摘要失败: ' || SQLERRM;
END;
/

-- 创建存储过程 - 批量执行规则
CREATE OR REPLACE PROCEDURE SP_EXECUTE_SUPERVISION_RULES(
    p_rule_category IN VARCHAR2 DEFAULT NULL,
    p_execution_type IN VARCHAR2 DEFAULT 'BATCH'
) IS
    CURSOR c_rules IS
        SELECT ID, RULE_CODE, RULE_NAME, RULE_EXPRESSION
        FROM SUPERVISION_RULE 
        WHERE IS_ACTIVE = 1 
        AND APPROVAL_STATUS = 'APPROVED'
        AND (p_rule_category IS NULL OR RULE_CATEGORY = p_rule_category)
        AND IS_DELETED = 0
        ORDER BY PRIORITY_LEVEL, ID;
    
    v_execution_id VARCHAR2(50);
    v_start_time TIMESTAMP(6);
    v_end_time TIMESTAMP(6);
    v_status VARCHAR2(20);
BEGIN
    FOR rec IN c_rules LOOP
        v_execution_id := 'BATCH_' || TO_CHAR(SYSTIMESTAMP, 'YYYYMMDDHH24MISS') || '_' || rec.ID;
        v_start_time := SYSTIMESTAMP;
        v_status := 'COMPLETED';
        
        BEGIN
            -- 这里应该是实际的规则执行逻辑
            -- 模拟执行时间
            DBMS_LOCK.SLEEP(0.1);
            
        EXCEPTION
            WHEN OTHERS THEN
                v_status := 'FAILED';
        END;
        
        v_end_time := SYSTIMESTAMP;
        
        -- 记录执行日志
        INSERT INTO SUPERVISION_EXECUTION_LOG (
            ID, RULE_ID, EXECUTION_ID, EXECUTION_TYPE, START_TIME, END_TIME,
            EXECUTION_DURATION, EXECUTION_STATUS, CREATED_AT
        ) VALUES (
            SEQ_SUPERVISION_EXECUTION_LOG.NEXTVAL, rec.ID, v_execution_id, p_execution_type,
            v_start_time, v_end_time,
            EXTRACT(SECOND FROM (v_end_time - v_start_time)), v_status, SYSTIMESTAMP
        );
    END LOOP;
    
    COMMIT;
END;
/

-- 表和字段注释
-- 1. 监管规则表注释
COMMENT ON TABLE SUPERVISION_RULE IS '监管规则表';
COMMENT ON COLUMN SUPERVISION_RULE.ID IS '规则ID，主键';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_CODE IS '规则编码，唯一标识';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_NAME IS '规则名称';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_DESCRIPTION IS '规则描述';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_TYPE IS '规则类型：VALIDATION-验证，CALCULATION-计算，DETECTION-检测，PREVENTION-预防，ANALYSIS-分析，ALERT-告警';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_CATEGORY IS '规则分类：COST-费用，FREQUENCY-频次，DIAGNOSIS-诊断，TREATMENT-治疗，PRESCRIPTION-处方，FRAUD-欺诈，ABUSE-滥用';
COMMENT ON COLUMN SUPERVISION_RULE.BUSINESS_DOMAIN IS '业务领域：MEDICAL-医疗，PHARMACY-药房，HOSPITAL-医院，DOCTOR-医生，PATIENT-患者，INSURANCE-保险';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_EXPRESSION IS '规则表达式(SQL/JSON/脚本)';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_CONDITION IS '规则条件，JSON格式';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_PARAMETERS IS '规则参数，JSON格式';
COMMENT ON COLUMN SUPERVISION_RULE.THRESHOLD_CONFIG IS '阈值配置，JSON格式';
COMMENT ON COLUMN SUPERVISION_RULE.PRIORITY_LEVEL IS '优先级别(1-10)，数字越小优先级越高';
COMMENT ON COLUMN SUPERVISION_RULE.SEVERITY_LEVEL IS '严重程度：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重';
COMMENT ON COLUMN SUPERVISION_RULE.RISK_WEIGHT IS '风险权重(用于综合评分)';
COMMENT ON COLUMN SUPERVISION_RULE.EXECUTION_MODE IS '执行模式:SYNC同步/ASYNC异步/BATCH批处理';
COMMENT ON COLUMN SUPERVISION_RULE.EXECUTION_FREQUENCY IS '执行频率：REALTIME-实时，HOURLY-每小时，DAILY-每日，WEEKLY-每周，MONTHLY-每月，MANUAL-手动';
COMMENT ON COLUMN SUPERVISION_RULE.TIMEOUT_SECONDS IS '超时时间（秒）';
COMMENT ON COLUMN SUPERVISION_RULE.RETRY_COUNT IS '重试次数';
COMMENT ON COLUMN SUPERVISION_RULE.IS_ACTIVE IS '是否激活，1-激活，0-禁用';
COMMENT ON COLUMN SUPERVISION_RULE.IS_SYSTEM_RULE IS '是否系统规则，1-是，0-否';
COMMENT ON COLUMN SUPERVISION_RULE.EFFECTIVE_DATE IS '生效日期';
COMMENT ON COLUMN SUPERVISION_RULE.EXPIRY_DATE IS '失效日期';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_VERSION IS '规则版本';
COMMENT ON COLUMN SUPERVISION_RULE.RULE_GROUP IS '规则分组';
COMMENT ON COLUMN SUPERVISION_RULE.TAGS IS '标签';
COMMENT ON COLUMN SUPERVISION_RULE.APPROVAL_STATUS IS '审批状态：DRAFT-草稿，PENDING-待审批，APPROVED-已批准，REJECTED-已拒绝，SUSPENDED-暂停';
COMMENT ON COLUMN SUPERVISION_RULE.APPROVER_ID IS '审批人ID';
COMMENT ON COLUMN SUPERVISION_RULE.APPROVAL_DATE IS '审批日期';
COMMENT ON COLUMN SUPERVISION_RULE.APPROVAL_COMMENTS IS '审批意见';
COMMENT ON COLUMN SUPERVISION_RULE.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SUPERVISION_RULE.IS_DELETED IS '是否删除，1-已删除，0-未删除';
COMMENT ON COLUMN SUPERVISION_RULE.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SUPERVISION_RULE.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN SUPERVISION_RULE.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN SUPERVISION_RULE.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN SUPERVISION_RULE.VERSION IS '版本号，用于乐观锁';

-- 2. 监管执行记录表注释
COMMENT ON TABLE SUPERVISION_EXECUTION_LOG IS '监管执行记录表';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.ID IS '执行记录ID，主键';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.RULE_ID IS '规则ID，外键';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.EXECUTION_ID IS '执行批次ID';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.EXECUTION_TYPE IS '执行类型：MANUAL-手动，SCHEDULED-定时，TRIGGERED-触发，BATCH-批处理，REALTIME-实时';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.TRIGGER_SOURCE IS '触发源';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.TRIGGER_EVENT IS '触发事件';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.INPUT_DATA IS '输入数据，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.EXECUTION_CONTEXT IS '执行上下文，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.START_TIME IS '开始时间';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.END_TIME IS '结束时间';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.EXECUTION_DURATION IS '执行时长';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.EXECUTION_STATUS IS '执行状态：RUNNING-运行中，COMPLETED-已完成，FAILED-失败，TIMEOUT-超时，CANCELLED-已取消';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.MATCHED_COUNT IS '匹配记录数';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.VIOLATION_COUNT IS '违规记录数';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.WARNING_COUNT IS '警告记录数';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.ERROR_MESSAGE IS '错误信息';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.ERROR_STACK IS '错误堆栈';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.RETRY_ATTEMPT IS '重试次数';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.NEXT_RETRY_TIME IS '下次重试时间';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.PERFORMANCE_METRICS IS '性能指标，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.RESOURCE_USAGE IS '资源使用情况，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.AFFECTED_ENTITIES IS '影响的实体，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.NOTIFICATION_SENT IS '是否已发送通知，1-是，0-否';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_LOG.CREATED_AT IS '创建时间';

-- 3. 监管执行结果表注释
COMMENT ON TABLE SUPERVISION_EXECUTION_RESULT IS '监管执行结果表';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.ID IS '结果记录ID，主键';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.EXECUTION_LOG_ID IS '执行记录ID，外键';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.RULE_ID IS '规则ID，外键';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.RESULT_TYPE IS '结果类型：VIOLATION-违规，WARNING-警告，NORMAL-正常，ERROR-错误';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.RESULT_STATUS IS '结果状态：PASS-通过，FAIL-失败，WARNING-警告，ERROR-错误，PARTIAL-部分';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.ENTITY_TYPE IS '实体类型：CASE-病例，PATIENT-患者，DOCTOR-医生，HOSPITAL-医院，PRESCRIPTION-处方，DIAGNOSIS-诊断，SURGERY-手术，COST_ITEM-费用项';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.ENTITY_ID IS '实体ID';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.ENTITY_CODE IS '实体编码';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.ENTITY_NAME IS '实体名称';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.VIOLATION_CODE IS '违规编码';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.VIOLATION_TYPE IS '违规类型：COST_OVERRUN-费用超标，FREQUENCY_ABUSE-频次滥用，DIAGNOSIS_MISMATCH-诊断不匹配，TREATMENT_INAPPROPRIATE-治疗不当，PRESCRIPTION_ERROR-处方错误，FRAUD_SUSPECTED-疑似欺诈，DATA_ANOMALY-数据异常';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.VIOLATION_LEVEL IS '违规级别：INFO-信息，WARNING-警告，MINOR-轻微，MAJOR-重大，CRITICAL-严重';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.VIOLATION_DETAILS IS '违规详情，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.VIOLATION_AMOUNT IS '违规金额';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.RISK_SCORE IS '风险评分';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.CONFIDENCE_LEVEL IS '置信度(0-100)';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.EVIDENCE_DATA IS '证据数据，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.RELATED_ENTITIES IS '关联实体，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.DETECTION_TIME IS '检测时间';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.OCCURRENCE_PATTERN IS '发生模式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SUPERVISION_EXECUTION_RESULT.CREATED_AT IS '创建时间';

-- 4. 审核记录表注释
COMMENT ON TABLE SUPERVISION_AUDIT_RECORD IS '审核记录表';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.ID IS '审核记录ID，主键';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RESULT_ID IS '执行结果ID，外键';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RULE_ID IS '规则ID，外键';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDIT_TYPE IS '审核类型：MANUAL-手动，AUTO-自动，REVIEW-复审，APPEAL-申诉';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDIT_STATUS IS '审核状态：PENDING-待审核，APPROVED-已批准，REJECTED-已拒绝，CLOSED-已关闭，SUSPENDED-暂停';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDIT_RESULT IS '审核结果：CONFIRMED-确认，FALSE_POSITIVE-误报，RESOLVED-已解决，PENDING_REVIEW-待复审，NO_ACTION-无需处理';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDITOR_ID IS '审核人ID';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDITOR_NAME IS '审核人姓名';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDIT_DATE IS '审核日期';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.AUDIT_COMMENTS IS '审核意见';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.HANDLING_STATUS IS '处理状态：PENDING-待处理，ASSIGNED-已分配，IN_PROGRESS-处理中，ESCALATED-已升级，COMPLETED-已完成';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.ASSIGNED_TO IS '分配给（用户ID）';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.ASSIGNED_DATE IS '分配日期';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RESOLUTION_ACTION IS '解决措施';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RESOLUTION_RESULT IS '解决结果：RESOLVED-已解决，PENALTY-处罚，WARNING-警告，EDUCATION-教育，REFERRAL-转介，NO_ACTION-无需处理';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RESOLUTION_AMOUNT IS '解决金额';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RESOLUTION_DATE IS '解决日期';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.RESOLUTION_COMMENTS IS '解决备注';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.FOLLOW_UP_REQUIRED IS '是否需要跟进，1-是，0-否';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.FOLLOW_UP_DATE IS '跟进日期';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.ESCALATION_LEVEL IS '升级级别';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.ESCALATION_DATE IS '升级日期';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.NOTIFICATION_SENT IS '是否已发送通知，1-是，0-否';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.METADATA IS '元数据，JSON格式';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.IS_DELETED IS '是否删除，1-已删除，0-未删除';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN SUPERVISION_AUDIT_RECORD.VERSION IS '版本号，用于乐观锁';

-- 创建同义词和授权
CREATE SYNONYM SUPERVISION_STATS FOR VW_SUPERVISION_RULE_STATISTICS;
CREATE SYNONYM VIOLATION_STATS FOR VW_SUPERVISION_VIOLATION_STATISTICS;
GRANT SELECT ON VW_SUPERVISION_RULE_STATISTICS TO APP_USER;
GRANT SELECT ON VW_SUPERVISION_VIOLATION_STATISTICS TO APP_USER;
GRANT SELECT ON MV_SUPERVISION_VIOLATION_TREND TO APP_USER;
GRANT EXECUTE ON FN_GET_RULE_EXECUTION_SUMMARY TO APP_USER;
GRANT EXECUTE ON SP_EXECUTE_SUPERVISION_RULES TO APP_USER;

-- 脚本执行完成
PROMPT '监管规则模块表结构创建完成！';
PROMPT '包含4张核心表：';
PROMPT '1. SUPERVISION_RULE - 监管规则表';
PROMPT '2. SUPERVISION_EXECUTION_LOG - 监管执行记录表';
PROMPT '3. SUPERVISION_EXECUTION_RESULT - 监管执行结果表';
PROMPT '4. SUPERVISION_AUDIT_RECORD - 审核记录表';