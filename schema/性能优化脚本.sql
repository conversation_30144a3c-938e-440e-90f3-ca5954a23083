-- =====================================================
-- 医保基金监管平台数据库重构 - 性能优化脚本
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 说明: 数据库性能优化配置和脚本
-- =====================================================

-- 设置环境
SET SERVEROUTPUT ON;
SET TIMING ON;
SET ECHO ON;

-- =====================================================
-- 1. 数据库参数优化
-- =====================================================

-- 显示当前重要参数
SELECT name, value, description
FROM v$parameter
WHERE name IN (
    'db_cache_size',
    'shared_pool_size',
    'pga_aggregate_target',
    'sga_target',
    'processes',
    'sessions',
    'db_file_multiblock_read_count',
    'optimizer_mode'
)
ORDER BY name;

-- 建议的参数优化（需要DBA权限执行）
/*
-- 内存参数优化建议
ALTER SYSTEM SET sga_target = 4G SCOPE=SPFILE;
ALTER SYSTEM SET pga_aggregate_target = 2G SCOPE=SPFILE;
ALTER SYSTEM SET db_cache_size = 2G SCOPE=SPFILE;
ALTER SYSTEM SET shared_pool_size = 1G SCOPE=SPFILE;

-- 并发参数优化
ALTER SYSTEM SET processes = 500 SCOPE=SPFILE;
ALTER SYSTEM SET sessions = 600 SCOPE=SPFILE;

-- I/O优化
ALTER SYSTEM SET db_file_multiblock_read_count = 16 SCOPE=SPFILE;
ALTER SYSTEM SET filesystemio_options = 'SETALL' SCOPE=SPFILE;

-- 优化器设置
ALTER SYSTEM SET optimizer_mode = 'ALL_ROWS' SCOPE=SPFILE;
ALTER SYSTEM SET optimizer_adaptive_features = TRUE SCOPE=SPFILE;
*/

-- =====================================================
-- 2. 表空间优化
-- =====================================================

-- 创建专用表空间（如果不存在）
DECLARE
    v_count NUMBER;
BEGIN
    -- 检查数据表空间
    SELECT COUNT(*) INTO v_count
    FROM dba_tablespaces
    WHERE tablespace_name = 'MEDIINSPECT_DATA';
    
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE '
            CREATE TABLESPACE MEDIINSPECT_DATA
            DATAFILE ''/opt/oracle/oradata/mediinspect_data01.dbf'' SIZE 1G
            AUTOEXTEND ON NEXT 100M MAXSIZE 10G
            EXTENT MANAGEMENT LOCAL
            SEGMENT SPACE MANAGEMENT AUTO';
        DBMS_OUTPUT.PUT_LINE('数据表空间 MEDIINSPECT_DATA 创建完成');
    END IF;
    
    -- 检查索引表空间
    SELECT COUNT(*) INTO v_count
    FROM dba_tablespaces
    WHERE tablespace_name = 'MEDIINSPECT_INDEX';
    
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE '
            CREATE TABLESPACE MEDIINSPECT_INDEX
            DATAFILE ''/opt/oracle/oradata/mediinspect_index01.dbf'' SIZE 500M
            AUTOEXTEND ON NEXT 50M MAXSIZE 5G
            EXTENT MANAGEMENT LOCAL
            SEGMENT SPACE MANAGEMENT AUTO';
        DBMS_OUTPUT.PUT_LINE('索引表空间 MEDIINSPECT_INDEX 创建完成');
    END IF;
    
    -- 检查临时表空间
    SELECT COUNT(*) INTO v_count
    FROM dba_tablespaces
    WHERE tablespace_name = 'MEDIINSPECT_TEMP';
    
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE '
            CREATE TEMPORARY TABLESPACE MEDIINSPECT_TEMP
            TEMPFILE ''/opt/oracle/oradata/mediinspect_temp01.dbf'' SIZE 500M
            AUTOEXTEND ON NEXT 50M MAXSIZE 2G
            EXTENT MANAGEMENT LOCAL';
        DBMS_OUTPUT.PUT_LINE('临时表空间 MEDIINSPECT_TEMP 创建完成');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('表空间创建跳过（可能权限不足或已存在）: ' || SQLERRM);
END;
/

-- =====================================================
-- 3. 高级索引优化
-- =====================================================

-- 创建函数索引
CREATE INDEX IDX_PATIENT_INFO_UPPER_NAME 
ON PATIENT_INFO (UPPER(PATIENT_NAME_ENCRYPTED))
TABLESPACE MEDIINSPECT_INDEX;

CREATE INDEX IDX_MEDICAL_RECORD_VISIT_MONTH 
ON MEDICAL_RECORD (EXTRACT(YEAR FROM VISIT_DATE), EXTRACT(MONTH FROM VISIT_DATE))
TABLESPACE MEDIINSPECT_INDEX;

-- 创建位图索引（适用于低基数列）
CREATE BITMAP INDEX IDX_PATIENT_INFO_GENDER_BMP 
ON PATIENT_INFO (GENDER)
TABLESPACE MEDIINSPECT_INDEX;

CREATE BITMAP INDEX IDX_MEDICAL_RECORD_VISIT_TYPE_BMP 
ON MEDICAL_RECORD (VISIT_TYPE)
TABLESPACE MEDIINSPECT_INDEX;

CREATE BITMAP INDEX IDX_USER_INFO_USER_TYPE_BMP 
ON USER_INFO (USER_TYPE)
TABLESPACE MEDIINSPECT_INDEX;

-- 创建反向键索引（适用于序列生成的主键）
CREATE INDEX IDX_MEDICAL_RECORD_ID_REVERSE 
ON MEDICAL_RECORD (ID REVERSE)
TABLESPACE MEDIINSPECT_INDEX;

-- 创建压缩索引
CREATE INDEX IDX_MEDICAL_RECORD_HOSPITAL_DEPT_COMPRESS 
ON MEDICAL_RECORD (HOSPITAL_CODE, DEPARTMENT_CODE) COMPRESS 2
TABLESPACE MEDIINSPECT_INDEX;

-- =====================================================
-- 4. 分区管理优化
-- =====================================================

-- 创建分区管理包
CREATE OR REPLACE PACKAGE PKG_PARTITION_MANAGER AS
    -- 自动添加月分区
    PROCEDURE ADD_MONTHLY_PARTITIONS(
        p_table_name IN VARCHAR2,
        p_months_ahead IN NUMBER DEFAULT 3
    );
    
    -- 删除过期分区
    PROCEDURE DROP_OLD_PARTITIONS(
        p_table_name IN VARCHAR2,
        p_retention_months IN NUMBER DEFAULT 36
    );
    
    -- 分区统计信息收集
    PROCEDURE GATHER_PARTITION_STATS(
        p_table_name IN VARCHAR2,
        p_partition_name IN VARCHAR2 DEFAULT NULL
    );
    
    -- 分区压缩
    PROCEDURE COMPRESS_OLD_PARTITIONS(
        p_table_name IN VARCHAR2,
        p_months_old IN NUMBER DEFAULT 6
    );
END PKG_PARTITION_MANAGER;
/

CREATE OR REPLACE PACKAGE BODY PKG_PARTITION_MANAGER AS
    
    PROCEDURE ADD_MONTHLY_PARTITIONS(
        p_table_name IN VARCHAR2,
        p_months_ahead IN NUMBER DEFAULT 3
    ) IS
        v_sql VARCHAR2(4000);
        v_partition_name VARCHAR2(100);
        v_high_value VARCHAR2(100);
        v_target_date DATE;
    BEGIN
        FOR i IN 1..p_months_ahead LOOP
            v_target_date := ADD_MONTHS(TRUNC(SYSDATE, 'MM'), i);
            v_partition_name := 'P' || TO_CHAR(v_target_date, 'YYYY_MM');
            v_high_value := 'TO_DATE(''' || TO_CHAR(ADD_MONTHS(v_target_date, 1), 'YYYY-MM-DD') || ''', ''YYYY-MM-DD'')';
            
            -- 检查分区是否已存在
            DECLARE
                v_count NUMBER;
            BEGIN
                SELECT COUNT(*)
                INTO v_count
                FROM user_tab_partitions
                WHERE table_name = UPPER(p_table_name)
                AND partition_name = v_partition_name;
                
                IF v_count = 0 THEN
                    v_sql := 'ALTER TABLE ' || p_table_name || 
                            ' ADD PARTITION ' || v_partition_name || 
                            ' VALUES LESS THAN (' || v_high_value || ')';
                    
                    EXECUTE IMMEDIATE v_sql;
                    DBMS_OUTPUT.PUT_LINE('添加分区: ' || p_table_name || '.' || v_partition_name);
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    DBMS_OUTPUT.PUT_LINE('添加分区失败: ' || p_table_name || '.' || v_partition_name || ' - ' || SQLERRM);
            END;
        END LOOP;
    END ADD_MONTHLY_PARTITIONS;
    
    PROCEDURE DROP_OLD_PARTITIONS(
        p_table_name IN VARCHAR2,
        p_retention_months IN NUMBER DEFAULT 36
    ) IS
        v_sql VARCHAR2(4000);
        v_cutoff_date DATE;
    BEGIN
        v_cutoff_date := ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -p_retention_months);
        
        FOR rec IN (
            SELECT partition_name, high_value
            FROM user_tab_partitions
            WHERE table_name = UPPER(p_table_name)
            AND partition_name LIKE 'P%'
            ORDER BY partition_name
        ) LOOP
            -- 简单的日期判断（实际应解析high_value）
            IF SUBSTR(rec.partition_name, 2, 4) < TO_CHAR(v_cutoff_date, 'YYYY') OR
               (SUBSTR(rec.partition_name, 2, 4) = TO_CHAR(v_cutoff_date, 'YYYY') AND
                SUBSTR(rec.partition_name, 7, 2) < TO_CHAR(v_cutoff_date, 'MM')) THEN
                
                BEGIN
                    v_sql := 'ALTER TABLE ' || p_table_name || ' DROP PARTITION ' || rec.partition_name;
                    EXECUTE IMMEDIATE v_sql;
                    DBMS_OUTPUT.PUT_LINE('删除过期分区: ' || p_table_name || '.' || rec.partition_name);
                EXCEPTION
                    WHEN OTHERS THEN
                        DBMS_OUTPUT.PUT_LINE('删除分区失败: ' || p_table_name || '.' || rec.partition_name || ' - ' || SQLERRM);
                END;
            END IF;
        END LOOP;
    END DROP_OLD_PARTITIONS;
    
    PROCEDURE GATHER_PARTITION_STATS(
        p_table_name IN VARCHAR2,
        p_partition_name IN VARCHAR2 DEFAULT NULL
    ) IS
    BEGIN
        IF p_partition_name IS NOT NULL THEN
            DBMS_STATS.GATHER_TABLE_STATS(
                ownname => USER,
                tabname => p_table_name,
                partname => p_partition_name,
                cascade => TRUE,
                degree => 4
            );
            DBMS_OUTPUT.PUT_LINE('收集分区统计信息: ' || p_table_name || '.' || p_partition_name);
        ELSE
            DBMS_STATS.GATHER_TABLE_STATS(
                ownname => USER,
                tabname => p_table_name,
                cascade => TRUE,
                degree => 4
            );
            DBMS_OUTPUT.PUT_LINE('收集表统计信息: ' || p_table_name);
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('收集统计信息失败: ' || p_table_name || ' - ' || SQLERRM);
    END GATHER_PARTITION_STATS;
    
    PROCEDURE COMPRESS_OLD_PARTITIONS(
        p_table_name IN VARCHAR2,
        p_months_old IN NUMBER DEFAULT 6
    ) IS
        v_sql VARCHAR2(4000);
        v_cutoff_date DATE;
    BEGIN
        v_cutoff_date := ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -p_months_old);
        
        FOR rec IN (
            SELECT partition_name
            FROM user_tab_partitions
            WHERE table_name = UPPER(p_table_name)
            AND partition_name LIKE 'P%'
            ORDER BY partition_name
        ) LOOP
            -- 简单的日期判断
            IF SUBSTR(rec.partition_name, 2, 4) < TO_CHAR(v_cutoff_date, 'YYYY') OR
               (SUBSTR(rec.partition_name, 2, 4) = TO_CHAR(v_cutoff_date, 'YYYY') AND
                SUBSTR(rec.partition_name, 7, 2) < TO_CHAR(v_cutoff_date, 'MM')) THEN
                
                BEGIN
                    v_sql := 'ALTER TABLE ' || p_table_name || ' MODIFY PARTITION ' || 
                            rec.partition_name || ' COMPRESS FOR OLTP';
                    EXECUTE IMMEDIATE v_sql;
                    DBMS_OUTPUT.PUT_LINE('压缩分区: ' || p_table_name || '.' || rec.partition_name);
                EXCEPTION
                    WHEN OTHERS THEN
                        DBMS_OUTPUT.PUT_LINE('压缩分区失败: ' || p_table_name || '.' || rec.partition_name || ' - ' || SQLERRM);
                END;
            END IF;
        END LOOP;
    END COMPRESS_OLD_PARTITIONS;
    
END PKG_PARTITION_MANAGER;
/

-- 为主要分区表添加未来分区
BEGIN
    PKG_PARTITION_MANAGER.ADD_MONTHLY_PARTITIONS('MEDICAL_RECORD', 6);
    PKG_PARTITION_MANAGER.ADD_MONTHLY_PARTITIONS('SUPERVISION_RULE_EXECUTION', 6);
    PKG_PARTITION_MANAGER.ADD_MONTHLY_PARTITIONS('SYSTEM_OPERATION_LOG', 6);
    PKG_PARTITION_MANAGER.ADD_MONTHLY_PARTITIONS('SYSTEM_PERFORMANCE_LOG', 6);
    PKG_PARTITION_MANAGER.ADD_MONTHLY_PARTITIONS('SYSTEM_ERROR_LOG', 6);
END;
/

-- =====================================================
-- 5. 查询优化
-- =====================================================

-- 创建物化视图用于复杂查询优化
CREATE MATERIALIZED VIEW MV_MEDICAL_RECORD_MONTHLY_STATS
BUILD IMMEDIATE
REFRESH FAST ON DEMAND
AS
SELECT 
    TENANT_ID,
    HOSPITAL_CODE,
    DEPARTMENT_CODE,
    EXTRACT(YEAR FROM VISIT_DATE) AS VISIT_YEAR,
    EXTRACT(MONTH FROM VISIT_DATE) AS VISIT_MONTH,
    VISIT_TYPE,
    COUNT(*) AS RECORD_COUNT,
    SUM(TOTAL_COST) AS TOTAL_COST_SUM,
    AVG(TOTAL_COST) AS TOTAL_COST_AVG,
    SUM(INSURANCE_COVERAGE) AS INSURANCE_COVERAGE_SUM,
    SUM(PATIENT_PAYMENT) AS PATIENT_PAYMENT_SUM
FROM MEDICAL_RECORD
WHERE IS_DELETED = 0
GROUP BY 
    TENANT_ID, HOSPITAL_CODE, DEPARTMENT_CODE,
    EXTRACT(YEAR FROM VISIT_DATE), EXTRACT(MONTH FROM VISIT_DATE),
    VISIT_TYPE;

-- 为物化视图创建索引
CREATE INDEX IDX_MV_MEDICAL_STATS_COMPOSITE
ON MV_MEDICAL_RECORD_MONTHLY_STATS (
    TENANT_ID, HOSPITAL_CODE, VISIT_YEAR, VISIT_MONTH
);

-- 创建物化视图日志以支持快速刷新
CREATE MATERIALIZED VIEW LOG ON MEDICAL_RECORD
WITH ROWID, SEQUENCE (
    TENANT_ID, HOSPITAL_CODE, DEPARTMENT_CODE, 
    VISIT_DATE, VISIT_TYPE, TOTAL_COST, 
    INSURANCE_COVERAGE, PATIENT_PAYMENT, IS_DELETED
)
INCLUDING NEW VALUES;

-- =====================================================
-- 6. 内存优化配置
-- =====================================================

-- 配置结果缓存
ALTER SYSTEM SET result_cache_mode = FORCE;
ALTER SYSTEM SET result_cache_max_size = 256M;

-- 为频繁查询的表启用结果缓存
ALTER TABLE USER_ROLE_INFO RESULT_CACHE (MODE FORCE);
ALTER TABLE SYSTEM_CONFIG_ITEM RESULT_CACHE (MODE FORCE);
ALTER TABLE KNOWLEDGE_CATEGORY RESULT_CACHE (MODE FORCE);

-- =====================================================
-- 7. 并行处理优化
-- =====================================================

-- 为大表启用并行查询
ALTER TABLE MEDICAL_RECORD PARALLEL 4;
ALTER TABLE PATIENT_INFO PARALLEL 4;
ALTER TABLE SUPERVISION_RULE_EXECUTION PARALLEL 4;

-- 为大表的索引启用并行
ALTER INDEX IDX_MEDICAL_RECORD_PATIENT_ID PARALLEL 4;
ALTER INDEX IDX_MEDICAL_RECORD_VISIT_DATE PARALLEL 4;
ALTER INDEX IDX_MEDICAL_RECORD_HOSPITAL_CODE PARALLEL 4;

-- =====================================================
-- 8. 统计信息管理
-- =====================================================

-- 创建统计信息收集作业
BEGIN
    -- 删除可能存在的旧作业
    BEGIN
        DBMS_SCHEDULER.DROP_JOB('MEDIINSPECT_STATS_JOB');
    EXCEPTION
        WHEN OTHERS THEN
            NULL;
    END;
    
    -- 创建新的统计信息收集作业
    DBMS_SCHEDULER.CREATE_JOB(
        job_name => 'MEDIINSPECT_STATS_JOB',
        job_type => 'PLSQL_BLOCK',
        job_action => '
            BEGIN
                -- 收集核心表统计信息
                DBMS_STATS.GATHER_TABLE_STATS(USER, ''MEDICAL_RECORD'', cascade => TRUE, degree => 4);
                DBMS_STATS.GATHER_TABLE_STATS(USER, ''PATIENT_INFO'', cascade => TRUE, degree => 4);
                DBMS_STATS.GATHER_TABLE_STATS(USER, ''SUPERVISION_RULE_EXECUTION'', cascade => TRUE, degree => 4);
                
                -- 刷新物化视图
                DBMS_MVIEW.REFRESH(''MV_MEDICAL_RECORD_MONTHLY_STATS'', ''F'');
            END;',
        start_date => SYSTIMESTAMP,
        repeat_interval => 'FREQ=DAILY; BYHOUR=2; BYMINUTE=0; BYSECOND=0',
        enabled => TRUE,
        comments => '医保基金监管平台统计信息收集作业'
    );
    
    DBMS_OUTPUT.PUT_LINE('统计信息收集作业创建完成');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('统计信息收集作业创建失败: ' || SQLERRM);
END;
/

-- =====================================================
-- 9. 性能监控视图
-- =====================================================

-- 创建性能监控视图
CREATE OR REPLACE VIEW V_PERFORMANCE_MONITOR AS
SELECT 
    'TABLE_SIZE' AS METRIC_TYPE,
    t.table_name AS OBJECT_NAME,
    ROUND(s.bytes/1024/1024, 2) AS VALUE_MB,
    s.bytes AS VALUE_BYTES,
    NULL AS ADDITIONAL_INFO
FROM user_tables t
JOIN user_segments s ON t.table_name = s.segment_name
WHERE s.segment_type = 'TABLE'
UNION ALL
SELECT 
    'INDEX_SIZE' AS METRIC_TYPE,
    i.index_name AS OBJECT_NAME,
    ROUND(s.bytes/1024/1024, 2) AS VALUE_MB,
    s.bytes AS VALUE_BYTES,
    i.table_name AS ADDITIONAL_INFO
FROM user_indexes i
JOIN user_segments s ON i.index_name = s.segment_name
WHERE s.segment_type = 'INDEX'
UNION ALL
SELECT 
    'PARTITION_COUNT' AS METRIC_TYPE,
    table_name AS OBJECT_NAME,
    COUNT(*) AS VALUE_MB,
    COUNT(*) AS VALUE_BYTES,
    NULL AS ADDITIONAL_INFO
FROM user_tab_partitions
GROUP BY table_name;

-- 创建慢查询监控视图
CREATE OR REPLACE VIEW V_SLOW_QUERIES AS
SELECT 
    sql_id,
    sql_text,
    executions,
    ROUND(elapsed_time/1000000, 2) AS elapsed_seconds,
    ROUND(cpu_time/1000000, 2) AS cpu_seconds,
    ROUND(elapsed_time/executions/1000000, 4) AS avg_elapsed_seconds,
    disk_reads,
    buffer_gets,
    rows_processed,
    first_load_time,
    last_active_time
FROM v$sql
WHERE elapsed_time/executions > 1000000  -- 平均执行时间超过1秒
AND executions > 0
ORDER BY elapsed_time/executions DESC;

-- =====================================================
-- 10. 性能测试脚本
-- =====================================================

-- 创建性能测试包
CREATE OR REPLACE PACKAGE PKG_PERFORMANCE_TEST AS
    -- 测试查询性能
    PROCEDURE TEST_QUERY_PERFORMANCE;
    
    -- 测试插入性能
    PROCEDURE TEST_INSERT_PERFORMANCE(p_record_count IN NUMBER DEFAULT 1000);
    
    -- 测试分区查询性能
    PROCEDURE TEST_PARTITION_PERFORMANCE;
    
    -- 生成性能报告
    PROCEDURE GENERATE_PERFORMANCE_REPORT;
END PKG_PERFORMANCE_TEST;
/

CREATE OR REPLACE PACKAGE BODY PKG_PERFORMANCE_TEST AS
    
    PROCEDURE TEST_QUERY_PERFORMANCE IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_count NUMBER;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('=== 查询性能测试 ===');
        
        -- 测试1: 患者信息查询
        v_start_time := SYSTIMESTAMP;
        SELECT COUNT(*) INTO v_count FROM PATIENT_INFO WHERE IS_DELETED = 0;
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('患者信息查询: ' || v_count || ' 条记录, 耗时: ' || v_duration || ' 秒');
        
        -- 测试2: 医疗记录查询
        v_start_time := SYSTIMESTAMP;
        SELECT COUNT(*) INTO v_count FROM MEDICAL_RECORD WHERE VISIT_DATE >= SYSDATE - 30;
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('近30天医疗记录查询: ' || v_count || ' 条记录, 耗时: ' || v_duration || ' 秒');
        
        -- 测试3: 复杂关联查询
        v_start_time := SYSTIMESTAMP;
        SELECT COUNT(*) INTO v_count
        FROM MEDICAL_RECORD mr
        JOIN PATIENT_INFO pi ON mr.PATIENT_ID = pi.ID
        WHERE mr.VISIT_DATE >= SYSDATE - 7
        AND pi.IS_DELETED = 0;
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('关联查询: ' || v_count || ' 条记录, 耗时: ' || v_duration || ' 秒');
        
    END TEST_QUERY_PERFORMANCE;
    
    PROCEDURE TEST_INSERT_PERFORMANCE(p_record_count IN NUMBER DEFAULT 1000) IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('=== 插入性能测试 ===');
        
        v_start_time := SYSTIMESTAMP;
        
        FOR i IN 1..p_record_count LOOP
            INSERT INTO PATIENT_INFO (
                TENANT_ID, PATIENT_CODE, PATIENT_NAME_ENCRYPTED,
                GENDER, BIRTH_DATE, PATIENT_STATUS,
                CREATED_BY, UPDATED_BY
            ) VALUES (
                1, 'TEST_' || i, 'ENCRYPTED_NAME_' || i,
                CASE MOD(i, 2) WHEN 0 THEN 'M' ELSE 'F' END,
                SYSDATE - MOD(i, 365),
                'ACTIVE', 1, 1
            );
            
            IF MOD(i, 100) = 0 THEN
                COMMIT;
            END IF;
        END LOOP;
        
        COMMIT;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('插入 ' || p_record_count || ' 条记录, 耗时: ' || v_duration || ' 秒');
        DBMS_OUTPUT.PUT_LINE('平均插入速度: ' || ROUND(p_record_count/v_duration, 2) || ' 条/秒');
        
        -- 清理测试数据
        DELETE FROM PATIENT_INFO WHERE PATIENT_CODE LIKE 'TEST_%';
        COMMIT;
        
    END TEST_INSERT_PERFORMANCE;
    
    PROCEDURE TEST_PARTITION_PERFORMANCE IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_count NUMBER;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('=== 分区查询性能测试 ===');
        
        -- 测试分区裁剪
        v_start_time := SYSTIMESTAMP;
        SELECT COUNT(*) INTO v_count
        FROM MEDICAL_RECORD
        WHERE VISIT_DATE >= TRUNC(SYSDATE, 'MM')
        AND VISIT_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1);
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('当月分区查询: ' || v_count || ' 条记录, 耗时: ' || v_duration || ' 秒');
        
        -- 测试跨分区查询
        v_start_time := SYSTIMESTAMP;
        SELECT COUNT(*) INTO v_count
        FROM MEDICAL_RECORD
        WHERE VISIT_DATE >= ADD_MONTHS(SYSDATE, -3);
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('跨分区查询(3个月): ' || v_count || ' 条记录, 耗时: ' || v_duration || ' 秒');
        
    END TEST_PARTITION_PERFORMANCE;
    
    PROCEDURE GENERATE_PERFORMANCE_REPORT IS
    BEGIN
        DBMS_OUTPUT.PUT_LINE('=== 性能监控报告 ===');
        
        -- 表大小报告
        DBMS_OUTPUT.PUT_LINE('--- 表大小统计 ---');
        FOR rec IN (
            SELECT OBJECT_NAME, VALUE_MB
            FROM V_PERFORMANCE_MONITOR
            WHERE METRIC_TYPE = 'TABLE_SIZE'
            ORDER BY VALUE_MB DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(rec.OBJECT_NAME || ': ' || rec.VALUE_MB || ' MB');
        END LOOP;
        
        -- 索引大小报告
        DBMS_OUTPUT.PUT_LINE('--- 索引大小统计 ---');
        FOR rec IN (
            SELECT OBJECT_NAME, VALUE_MB, ADDITIONAL_INFO
            FROM V_PERFORMANCE_MONITOR
            WHERE METRIC_TYPE = 'INDEX_SIZE'
            AND VALUE_MB > 10
            ORDER BY VALUE_MB DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(rec.OBJECT_NAME || ' (' || rec.ADDITIONAL_INFO || '): ' || rec.VALUE_MB || ' MB');
        END LOOP;
        
        -- 分区统计
        DBMS_OUTPUT.PUT_LINE('--- 分区统计 ---');
        FOR rec IN (
            SELECT OBJECT_NAME, VALUE_MB
            FROM V_PERFORMANCE_MONITOR
            WHERE METRIC_TYPE = 'PARTITION_COUNT'
            ORDER BY VALUE_MB DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(rec.OBJECT_NAME || ': ' || rec.VALUE_MB || ' 个分区');
        END LOOP;
        
    END GENERATE_PERFORMANCE_REPORT;
    
END PKG_PERFORMANCE_TEST;
/

-- =====================================================
-- 11. 执行性能优化
-- =====================================================

-- 收集所有表的统计信息
BEGIN
    DBMS_OUTPUT.PUT_LINE('开始收集统计信息...');
    
    FOR rec IN (
        SELECT table_name
        FROM user_tables
        WHERE table_name IN (
            'USER_ROLE_INFO', 'USER_INFO', 'PATIENT_INFO', 'MEDICAL_RECORD',
            'SUPERVISION_RULE', 'KNOWLEDGE_DOCUMENT', 'SYSTEM_CONFIG_ITEM'
        )
    ) LOOP
        BEGIN
            DBMS_STATS.GATHER_TABLE_STATS(
                ownname => USER,
                tabname => rec.table_name,
                cascade => TRUE,
                degree => 4,
                estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE
            );
            DBMS_OUTPUT.PUT_LINE('完成: ' || rec.table_name);
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('失败: ' || rec.table_name || ' - ' || SQLERRM);
        END;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('统计信息收集完成');
END;
/

-- 运行性能测试
BEGIN
    PKG_PERFORMANCE_TEST.TEST_QUERY_PERFORMANCE();
    PKG_PERFORMANCE_TEST.GENERATE_PERFORMANCE_REPORT();
END;
/

-- =====================================================
-- 12. 性能优化建议
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('医保基金监管平台性能优化完成！');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('优化特性：');
    DBMS_OUTPUT.PUT_LINE('✓ 专用表空间配置');
    DBMS_OUTPUT.PUT_LINE('✓ 高级索引策略（函数索引、位图索引、压缩索引）');
    DBMS_OUTPUT.PUT_LINE('✓ 分区自动管理');
    DBMS_OUTPUT.PUT_LINE('✓ 物化视图优化');
    DBMS_OUTPUT.PUT_LINE('✓ 并行处理配置');
    DBMS_OUTPUT.PUT_LINE('✓ 结果缓存启用');
    DBMS_OUTPUT.PUT_LINE('✓ 统计信息自动收集');
    DBMS_OUTPUT.PUT_LINE('✓ 性能监控视图');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('建议：');
    DBMS_OUTPUT.PUT_LINE('1. 定期运行 PKG_PERFORMANCE_TEST.GENERATE_PERFORMANCE_REPORT() 监控性能');
    DBMS_OUTPUT.PUT_LINE('2. 根据业务增长调整分区策略');
    DBMS_OUTPUT.PUT_LINE('3. 监控 V_SLOW_QUERIES 视图识别慢查询');
    DBMS_OUTPUT.PUT_LINE('4. 定期清理过期分区和日志数据');
    DBMS_OUTPUT.PUT_LINE('==============================================');
END;
/