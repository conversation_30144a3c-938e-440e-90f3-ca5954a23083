-- =====================================================
-- 医保基金监管平台数据库重构 - 测试验证脚本
-- 版本: 1.0
-- 创建时间: 2024-12-19
-- 说明: 数据库重构后的功能测试和验证
-- =====================================================

-- 设置环境
SET SERVEROUTPUT ON SIZE 1000000;
SET TIMING ON;
SET ECHO ON;

-- =====================================================
-- 1. 基础结构验证
-- =====================================================

-- 创建测试验证包
CREATE OR REPLACE PACKAGE PKG_TEST_VALIDATION AS
    -- 验证表结构
    PROCEDURE VALIDATE_TABLE_STRUCTURE;
    
    -- 验证索引
    PROCEDURE VALIDATE_INDEXES;
    
    -- 验证约束
    PROCEDURE VALIDATE_CONSTRAINTS;
    
    -- 验证触发器
    PROCEDURE VALIDATE_TRIGGERS;
    
    -- 验证分区
    PROCEDURE VALIDATE_PARTITIONS;
    
    -- 功能测试
    PROCEDURE TEST_BASIC_FUNCTIONS;
    
    -- 安全测试
    PROCEDURE TEST_SECURITY_FEATURES;
    
    -- 性能测试
    PROCEDURE TEST_PERFORMANCE;
    
    -- 数据完整性测试
    PROCEDURE TEST_DATA_INTEGRITY;
    
    -- 生成测试报告
    PROCEDURE GENERATE_TEST_REPORT;
END PKG_TEST_VALIDATION;
/

CREATE OR REPLACE PACKAGE BODY PKG_TEST_VALIDATION AS
    
    -- 测试结果记录表
    TYPE t_test_result IS RECORD (
        test_name VARCHAR2(100),
        test_status VARCHAR2(20),
        test_message VARCHAR2(4000),
        execution_time NUMBER
    );
    
    TYPE t_test_results IS TABLE OF t_test_result;
    g_test_results t_test_results := t_test_results();
    
    PROCEDURE ADD_TEST_RESULT(
        p_test_name IN VARCHAR2,
        p_status IN VARCHAR2,
        p_message IN VARCHAR2 DEFAULT NULL,
        p_execution_time IN NUMBER DEFAULT 0
    ) IS
        v_result t_test_result;
    BEGIN
        v_result.test_name := p_test_name;
        v_result.test_status := p_status;
        v_result.test_message := p_message;
        v_result.execution_time := p_execution_time;
        
        g_test_results.EXTEND;
        g_test_results(g_test_results.COUNT) := v_result;
    END ADD_TEST_RESULT;
    
    PROCEDURE VALIDATE_TABLE_STRUCTURE IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_table_count NUMBER;
        v_expected_tables SYS.ODCIVARCHAR2LIST := SYS.ODCIVARCHAR2LIST(
            'USER_ROLE_INFO', 'USER_INFO', 'USER_ROLE_ASSIGNMENT',
            'PATIENT_INFO', 'MEDICAL_RECORD', 'MEDICAL_RECORD_DETAIL',
            'SUPERVISION_RULE', 'SUPERVISION_RULE_EXECUTION',
            'KNOWLEDGE_CATEGORY', 'KNOWLEDGE_DOCUMENT',
            'SYSTEM_CONFIG_CATEGORY', 'SYSTEM_CONFIG_ITEM',
            'SYSTEM_OPERATION_LOG', 'SYSTEM_PERFORMANCE_LOG', 'SYSTEM_ERROR_LOG'
        );
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 验证表结构 ===');
        
        -- 检查核心表是否存在
        FOR i IN 1..v_expected_tables.COUNT LOOP
            SELECT COUNT(*)
            INTO v_table_count
            FROM user_tables
            WHERE table_name = v_expected_tables(i);
            
            IF v_table_count = 1 THEN
                DBMS_OUTPUT.PUT_LINE('✓ 表 ' || v_expected_tables(i) || ' 存在');
                ADD_TEST_RESULT('TABLE_' || v_expected_tables(i), 'PASS', '表存在');
            ELSE
                DBMS_OUTPUT.PUT_LINE('✗ 表 ' || v_expected_tables(i) || ' 不存在');
                ADD_TEST_RESULT('TABLE_' || v_expected_tables(i), 'FAIL', '表不存在');
            END IF;
        END LOOP;
        
        -- 检查标准字段
        FOR rec IN (
            SELECT table_name
            FROM user_tables
            WHERE table_name IN (
                SELECT * FROM TABLE(v_expected_tables)
            )
        ) LOOP
            DECLARE
                v_has_tenant_id NUMBER := 0;
                v_has_created_time NUMBER := 0;
                v_has_updated_time NUMBER := 0;
                v_has_version NUMBER := 0;
                v_has_is_deleted NUMBER := 0;
            BEGIN
                -- 检查TENANT_ID字段
                SELECT COUNT(*) INTO v_has_tenant_id
                FROM user_tab_columns
                WHERE table_name = rec.table_name AND column_name = 'TENANT_ID';
                
                -- 检查审计字段
                SELECT COUNT(*) INTO v_has_created_time
                FROM user_tab_columns
                WHERE table_name = rec.table_name AND column_name = 'CREATED_TIME';
                
                SELECT COUNT(*) INTO v_has_updated_time
                FROM user_tab_columns
                WHERE table_name = rec.table_name AND column_name = 'UPDATED_TIME';
                
                -- 检查版本控制字段
                SELECT COUNT(*) INTO v_has_version
                FROM user_tab_columns
                WHERE table_name = rec.table_name AND column_name = 'VERSION_NUMBER';
                
                -- 检查逻辑删除字段
                SELECT COUNT(*) INTO v_has_is_deleted
                FROM user_tab_columns
                WHERE table_name = rec.table_name AND column_name = 'IS_DELETED';
                
                IF v_has_tenant_id = 1 AND v_has_created_time = 1 AND 
                   v_has_updated_time = 1 AND v_has_version = 1 AND v_has_is_deleted = 1 THEN
                    ADD_TEST_RESULT('STANDARD_FIELDS_' || rec.table_name, 'PASS', '标准字段完整');
                ELSE
                    ADD_TEST_RESULT('STANDARD_FIELDS_' || rec.table_name, 'FAIL', 
                        '缺少标准字段: TENANT_ID=' || v_has_tenant_id || 
                        ', CREATED_TIME=' || v_has_created_time ||
                        ', UPDATED_TIME=' || v_has_updated_time ||
                        ', VERSION_NUMBER=' || v_has_version ||
                        ', IS_DELETED=' || v_has_is_deleted);
                END IF;
            END;
        END LOOP;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('表结构验证完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('VALIDATE_TABLE_STRUCTURE', 'ERROR', SQLERRM);
    END VALIDATE_TABLE_STRUCTURE;
    
    PROCEDURE VALIDATE_INDEXES IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_index_count NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 验证索引 ===');
        
        -- 检查主键索引
        SELECT COUNT(*)
        INTO v_index_count
        FROM user_constraints
        WHERE constraint_type = 'P';
        
        DBMS_OUTPUT.PUT_LINE('主键约束数量: ' || v_index_count);
        ADD_TEST_RESULT('PRIMARY_KEY_COUNT', 'INFO', '主键数量: ' || v_index_count);
        
        -- 检查外键索引
        SELECT COUNT(*)
        INTO v_index_count
        FROM user_constraints
        WHERE constraint_type = 'R';
        
        DBMS_OUTPUT.PUT_LINE('外键约束数量: ' || v_index_count);
        ADD_TEST_RESULT('FOREIGN_KEY_COUNT', 'INFO', '外键数量: ' || v_index_count);
        
        -- 检查普通索引
        SELECT COUNT(*)
        INTO v_index_count
        FROM user_indexes
        WHERE index_type = 'NORMAL'
        AND uniqueness = 'NONUNIQUE';
        
        DBMS_OUTPUT.PUT_LINE('普通索引数量: ' || v_index_count);
        ADD_TEST_RESULT('NORMAL_INDEX_COUNT', 'INFO', '普通索引数量: ' || v_index_count);
        
        -- 检查关键索引是否存在
        DECLARE
            v_key_indexes SYS.ODCIVARCHAR2LIST := SYS.ODCIVARCHAR2LIST(
                'IDX_PATIENT_INFO_TENANT_ID',
                'IDX_MEDICAL_RECORD_PATIENT_ID',
                'IDX_MEDICAL_RECORD_VISIT_DATE',
                'IDX_USER_INFO_USERNAME',
                'IDX_USER_ROLE_ASSIGNMENT_USER_ID'
            );
        BEGIN
            FOR i IN 1..v_key_indexes.COUNT LOOP
                SELECT COUNT(*) INTO v_index_count
                FROM user_indexes
                WHERE index_name = v_key_indexes(i);
                
                IF v_index_count = 1 THEN
                    ADD_TEST_RESULT('INDEX_' || v_key_indexes(i), 'PASS', '关键索引存在');
                ELSE
                    ADD_TEST_RESULT('INDEX_' || v_key_indexes(i), 'FAIL', '关键索引缺失');
                END IF;
            END LOOP;
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('索引验证完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('VALIDATE_INDEXES', 'ERROR', SQLERRM);
    END VALIDATE_INDEXES;
    
    PROCEDURE VALIDATE_CONSTRAINTS IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_constraint_count NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 验证约束 ===');
        
        -- 检查非空约束
        SELECT COUNT(*)
        INTO v_constraint_count
        FROM user_constraints
        WHERE constraint_type = 'C'
        AND search_condition IS NOT NULL
        AND search_condition NOT LIKE '%IS NOT NULL%';
        
        DBMS_OUTPUT.PUT_LINE('检查约束数量: ' || v_constraint_count);
        ADD_TEST_RESULT('CHECK_CONSTRAINT_COUNT', 'INFO', '检查约束数量: ' || v_constraint_count);
        
        -- 验证关键约束
        DECLARE
            v_constraint_exists NUMBER;
        BEGIN
            -- 检查用户名唯一约束
            SELECT COUNT(*) INTO v_constraint_exists
            FROM user_constraints
            WHERE table_name = 'USER_INFO'
            AND constraint_type = 'U'
            AND constraint_name LIKE '%USERNAME%';
            
            IF v_constraint_exists > 0 THEN
                ADD_TEST_RESULT('UNIQUE_USERNAME', 'PASS', '用户名唯一约束存在');
            ELSE
                ADD_TEST_RESULT('UNIQUE_USERNAME', 'FAIL', '用户名唯一约束缺失');
            END IF;
            
            -- 检查患者编码唯一约束
            SELECT COUNT(*) INTO v_constraint_exists
            FROM user_constraints
            WHERE table_name = 'PATIENT_INFO'
            AND constraint_type = 'U'
            AND constraint_name LIKE '%PATIENT_CODE%';
            
            IF v_constraint_exists > 0 THEN
                ADD_TEST_RESULT('UNIQUE_PATIENT_CODE', 'PASS', '患者编码唯一约束存在');
            ELSE
                ADD_TEST_RESULT('UNIQUE_PATIENT_CODE', 'FAIL', '患者编码唯一约束缺失');
            END IF;
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('约束验证完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('VALIDATE_CONSTRAINTS', 'ERROR', SQLERRM);
    END VALIDATE_CONSTRAINTS;
    
    PROCEDURE VALIDATE_TRIGGERS IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_trigger_count NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 验证触发器 ===');
        
        -- 检查触发器总数
        SELECT COUNT(*)
        INTO v_trigger_count
        FROM user_triggers
        WHERE status = 'ENABLED';
        
        DBMS_OUTPUT.PUT_LINE('启用的触发器数量: ' || v_trigger_count);
        ADD_TEST_RESULT('ENABLED_TRIGGER_COUNT', 'INFO', '启用触发器数量: ' || v_trigger_count);
        
        -- 检查关键触发器
        DECLARE
            v_key_triggers SYS.ODCIVARCHAR2LIST := SYS.ODCIVARCHAR2LIST(
                'TRG_USER_INFO_BI',
                'TRG_PATIENT_INFO_BI',
                'TRG_MEDICAL_RECORD_BI',
                'TRG_PATIENT_INFO_AUDIT',
                'TRG_MEDICAL_RECORD_AUDIT'
            );
            v_trigger_exists NUMBER;
        BEGIN
            FOR i IN 1..v_key_triggers.COUNT LOOP
                SELECT COUNT(*) INTO v_trigger_exists
                FROM user_triggers
                WHERE trigger_name = v_key_triggers(i)
                AND status = 'ENABLED';
                
                IF v_trigger_exists = 1 THEN
                    ADD_TEST_RESULT('TRIGGER_' || v_key_triggers(i), 'PASS', '关键触发器存在且启用');
                ELSE
                    ADD_TEST_RESULT('TRIGGER_' || v_key_triggers(i), 'FAIL', '关键触发器缺失或禁用');
                END IF;
            END LOOP;
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('触发器验证完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('VALIDATE_TRIGGERS', 'ERROR', SQLERRM);
    END VALIDATE_TRIGGERS;
    
    PROCEDURE VALIDATE_PARTITIONS IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_partition_count NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 验证分区 ===');
        
        -- 检查分区表
        DECLARE
            v_partitioned_tables SYS.ODCIVARCHAR2LIST := SYS.ODCIVARCHAR2LIST(
                'MEDICAL_RECORD',
                'SUPERVISION_RULE_EXECUTION',
                'SYSTEM_OPERATION_LOG',
                'SYSTEM_PERFORMANCE_LOG',
                'SYSTEM_ERROR_LOG'
            );
            v_is_partitioned NUMBER;
        BEGIN
            FOR i IN 1..v_partitioned_tables.COUNT LOOP
                SELECT COUNT(*) INTO v_is_partitioned
                FROM user_part_tables
                WHERE table_name = v_partitioned_tables(i);
                
                IF v_is_partitioned = 1 THEN
                    -- 检查分区数量
                    SELECT COUNT(*) INTO v_partition_count
                    FROM user_tab_partitions
                    WHERE table_name = v_partitioned_tables(i);
                    
                    ADD_TEST_RESULT('PARTITION_' || v_partitioned_tables(i), 'PASS', 
                        '表已分区，分区数: ' || v_partition_count);
                    DBMS_OUTPUT.PUT_LINE('✓ ' || v_partitioned_tables(i) || ' 已分区，分区数: ' || v_partition_count);
                ELSE
                    ADD_TEST_RESULT('PARTITION_' || v_partitioned_tables(i), 'FAIL', '表未分区');
                    DBMS_OUTPUT.PUT_LINE('✗ ' || v_partitioned_tables(i) || ' 未分区');
                END IF;
            END LOOP;
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('分区验证完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('VALIDATE_PARTITIONS', 'ERROR', SQLERRM);
    END VALIDATE_PARTITIONS;
    
    PROCEDURE TEST_BASIC_FUNCTIONS IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_test_id NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 基础功能测试 ===');
        
        -- 测试用户创建
        BEGIN
            INSERT INTO USER_INFO (
                TENANT_ID, USERNAME, PASSWORD_HASH, SALT,
                REAL_NAME_ENCRYPTED, USER_TYPE, USER_STATUS,
                CREATED_BY, UPDATED_BY
            ) VALUES (
                1, 'test_user_' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS'),
                'test_hash', 'test_salt', 'TEST_USER', 'INTERNAL', 'ACTIVE',
                1, 1
            ) RETURNING ID INTO v_test_id;
            
            ADD_TEST_RESULT('USER_INSERT', 'PASS', '用户创建成功，ID: ' || v_test_id);
            
            -- 测试用户更新
            UPDATE USER_INFO 
            SET USER_STATUS = 'INACTIVE'
            WHERE ID = v_test_id;
            
            ADD_TEST_RESULT('USER_UPDATE', 'PASS', '用户更新成功');
            
            -- 清理测试数据
            DELETE FROM USER_INFO WHERE ID = v_test_id;
            
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('USER_CRUD', 'FAIL', '用户CRUD操作失败: ' || SQLERRM);
        END;
        
        -- 测试患者信息创建
        BEGIN
            INSERT INTO PATIENT_INFO (
                TENANT_ID, PATIENT_CODE, PATIENT_NAME_ENCRYPTED,
                GENDER, BIRTH_DATE, PATIENT_STATUS,
                CREATED_BY, UPDATED_BY
            ) VALUES (
                1, 'TEST_P_' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS'),
                PKG_ENCRYPTION_MANAGER.ENCRYPT_SENSITIVE_DATA('测试患者', 'NAME'),
                'M', SYSDATE - 365*30, 'ACTIVE',
                1, 1
            ) RETURNING ID INTO v_test_id;
            
            ADD_TEST_RESULT('PATIENT_INSERT', 'PASS', '患者创建成功，ID: ' || v_test_id);
            
            -- 清理测试数据
            DELETE FROM PATIENT_INFO WHERE ID = v_test_id;
            
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('PATIENT_INSERT', 'FAIL', '患者创建失败: ' || SQLERRM);
        END;
        
        -- 测试序列
        BEGIN
            DECLARE
                v_seq_val NUMBER;
            BEGIN
                SELECT SEQ_USER_INFO.NEXTVAL INTO v_seq_val FROM DUAL;
                ADD_TEST_RESULT('SEQUENCE_USER_INFO', 'PASS', '序列正常，值: ' || v_seq_val);
            EXCEPTION
                WHEN OTHERS THEN
                    ADD_TEST_RESULT('SEQUENCE_USER_INFO', 'FAIL', '序列异常: ' || SQLERRM);
            END;
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('基础功能测试完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('TEST_BASIC_FUNCTIONS', 'ERROR', SQLERRM);
    END TEST_BASIC_FUNCTIONS;
    
    PROCEDURE TEST_SECURITY_FEATURES IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_encrypted_data VARCHAR2(4000);
        v_decrypted_data VARCHAR2(4000);
        v_masked_data VARCHAR2(4000);
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 安全功能测试 ===');
        
        -- 测试数据加密
        BEGIN
            v_encrypted_data := PKG_ENCRYPTION_MANAGER.ENCRYPT_SENSITIVE_DATA('测试数据', 'GENERAL');
            
            IF v_encrypted_data IS NOT NULL AND v_encrypted_data != '[ENCRYPTION_FAILED]' THEN
                ADD_TEST_RESULT('DATA_ENCRYPTION', 'PASS', '数据加密成功');
                
                -- 测试数据解密
                v_decrypted_data := PKG_ENCRYPTION_MANAGER.DECRYPT_SENSITIVE_DATA(v_encrypted_data, 'GENERAL');
                
                IF v_decrypted_data = '测试数据' THEN
                    ADD_TEST_RESULT('DATA_DECRYPTION', 'PASS', '数据解密成功');
                ELSE
                    ADD_TEST_RESULT('DATA_DECRYPTION', 'FAIL', '数据解密失败');
                END IF;
            ELSE
                ADD_TEST_RESULT('DATA_ENCRYPTION', 'FAIL', '数据加密失败');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('DATA_ENCRYPTION', 'FAIL', '加密测试异常: ' || SQLERRM);
        END;
        
        -- 测试数据脱敏
        BEGIN
            v_masked_data := PKG_ENCRYPTION_MANAGER.MASK_SENSITIVE_DATA('13812345678', 'PHONE');
            
            IF v_masked_data LIKE '138****5678' THEN
                ADD_TEST_RESULT('DATA_MASKING', 'PASS', '数据脱敏成功: ' || v_masked_data);
            ELSE
                ADD_TEST_RESULT('DATA_MASKING', 'FAIL', '数据脱敏失败: ' || v_masked_data);
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('DATA_MASKING', 'FAIL', '脱敏测试异常: ' || SQLERRM);
        END;
        
        -- 测试权限检查
        BEGIN
            DECLARE
                v_has_permission BOOLEAN;
            BEGIN
                v_has_permission := PKG_ACCESS_CONTROL.CHECK_USER_PERMISSION(1, 'USER_MANAGEMENT', 'READ', 1);
                
                IF v_has_permission THEN
                    ADD_TEST_RESULT('PERMISSION_CHECK', 'PASS', '权限检查功能正常');
                ELSE
                    ADD_TEST_RESULT('PERMISSION_CHECK', 'INFO', '权限检查返回false（正常）');
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    ADD_TEST_RESULT('PERMISSION_CHECK', 'FAIL', '权限检查异常: ' || SQLERRM);
            END;
        END;
        
        -- 测试脱敏视图
        BEGIN
            DECLARE
                v_count NUMBER;
            BEGIN
                SELECT COUNT(*) INTO v_count FROM V_PATIENT_INFO_MASKED WHERE ROWNUM <= 1;
                ADD_TEST_RESULT('MASKED_VIEW', 'PASS', '脱敏视图可访问');
            EXCEPTION
                WHEN OTHERS THEN
                    ADD_TEST_RESULT('MASKED_VIEW', 'FAIL', '脱敏视图异常: ' || SQLERRM);
            END;
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('安全功能测试完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('TEST_SECURITY_FEATURES', 'ERROR', SQLERRM);
    END TEST_SECURITY_FEATURES;
    
    PROCEDURE TEST_PERFORMANCE IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_count NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 性能测试 ===');
        
        -- 测试索引查询性能
        BEGIN
            v_start_time := SYSTIMESTAMP;
            
            SELECT COUNT(*) INTO v_count
            FROM PATIENT_INFO
            WHERE TENANT_ID = 1 AND IS_DELETED = 0;
            
            v_end_time := SYSTIMESTAMP;
            v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
            
            IF v_duration < 1 THEN
                ADD_TEST_RESULT('QUERY_PERFORMANCE_PATIENT', 'PASS', 
                    '患者查询性能良好: ' || v_count || ' 条记录，耗时: ' || v_duration || ' 秒');
            ELSE
                ADD_TEST_RESULT('QUERY_PERFORMANCE_PATIENT', 'WARN', 
                    '患者查询性能一般: ' || v_count || ' 条记录，耗时: ' || v_duration || ' 秒');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('QUERY_PERFORMANCE_PATIENT', 'FAIL', '患者查询异常: ' || SQLERRM);
        END;
        
        -- 测试医疗记录查询性能
        BEGIN
            v_start_time := SYSTIMESTAMP;
            
            SELECT COUNT(*) INTO v_count
            FROM MEDICAL_RECORD
            WHERE VISIT_DATE >= SYSDATE - 30 AND IS_DELETED = 0;
            
            v_end_time := SYSTIMESTAMP;
            v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
            
            IF v_duration < 2 THEN
                ADD_TEST_RESULT('QUERY_PERFORMANCE_MEDICAL', 'PASS', 
                    '医疗记录查询性能良好: ' || v_count || ' 条记录，耗时: ' || v_duration || ' 秒');
            ELSE
                ADD_TEST_RESULT('QUERY_PERFORMANCE_MEDICAL', 'WARN', 
                    '医疗记录查询性能一般: ' || v_count || ' 条记录，耗时: ' || v_duration || ' 秒');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('QUERY_PERFORMANCE_MEDICAL', 'FAIL', '医疗记录查询异常: ' || SQLERRM);
        END;
        
        -- 测试关联查询性能
        BEGIN
            v_start_time := SYSTIMESTAMP;
            
            SELECT COUNT(*) INTO v_count
            FROM MEDICAL_RECORD mr
            JOIN PATIENT_INFO pi ON mr.PATIENT_ID = pi.ID
            WHERE mr.VISIT_DATE >= SYSDATE - 7
            AND pi.IS_DELETED = 0
            AND mr.IS_DELETED = 0;
            
            v_end_time := SYSTIMESTAMP;
            v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
            
            IF v_duration < 3 THEN
                ADD_TEST_RESULT('QUERY_PERFORMANCE_JOIN', 'PASS', 
                    '关联查询性能良好: ' || v_count || ' 条记录，耗时: ' || v_duration || ' 秒');
            ELSE
                ADD_TEST_RESULT('QUERY_PERFORMANCE_JOIN', 'WARN', 
                    '关联查询性能一般: ' || v_count || ' 条记录，耗时: ' || v_duration || ' 秒');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('QUERY_PERFORMANCE_JOIN', 'FAIL', '关联查询异常: ' || SQLERRM);
        END;
        
        DBMS_OUTPUT.PUT_LINE('性能测试完成');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('TEST_PERFORMANCE', 'ERROR', SQLERRM);
    END TEST_PERFORMANCE;
    
    PROCEDURE TEST_DATA_INTEGRITY IS
        v_start_time TIMESTAMP;
        v_end_time TIMESTAMP;
        v_duration NUMBER;
        v_error_count NUMBER;
    BEGIN
        v_start_time := SYSTIMESTAMP;
        
        DBMS_OUTPUT.PUT_LINE('=== 数据完整性测试 ===');
        
        -- 检查外键完整性
        BEGIN
            -- 检查医疗记录的患者ID完整性
            SELECT COUNT(*) INTO v_error_count
            FROM MEDICAL_RECORD mr
            LEFT JOIN PATIENT_INFO pi ON mr.PATIENT_ID = pi.ID
            WHERE pi.ID IS NULL AND mr.IS_DELETED = 0;
            
            IF v_error_count = 0 THEN
                ADD_TEST_RESULT('FK_INTEGRITY_MEDICAL_PATIENT', 'PASS', '医疗记录-患者外键完整');
            ELSE
                ADD_TEST_RESULT('FK_INTEGRITY_MEDICAL_PATIENT', 'FAIL', 
                    '医疗记录-患者外键不完整: ' || v_error_count || ' 条记录');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('FK_INTEGRITY_MEDICAL_PATIENT', 'FAIL', '外键检查异常: ' || SQLERRM);
        END;
        
        -- 检查用户角色分配完整性
        BEGIN
            SELECT COUNT(*) INTO v_error_count
            FROM USER_ROLE_ASSIGNMENT ura
            LEFT JOIN USER_INFO ui ON ura.USER_ID = ui.ID
            LEFT JOIN USER_ROLE_INFO uri ON ura.ROLE_ID = uri.ID
            WHERE (ui.ID IS NULL OR uri.ID IS NULL) AND ura.IS_ACTIVE = 1;
            
            IF v_error_count = 0 THEN
                ADD_TEST_RESULT('FK_INTEGRITY_USER_ROLE', 'PASS', '用户角色分配外键完整');
            ELSE
                ADD_TEST_RESULT('FK_INTEGRITY_USER_ROLE', 'FAIL', 
                    '用户角色分配外键不完整: ' || v_error_count || ' 条记录');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('FK_INTEGRITY_USER_ROLE', 'FAIL', '用户角色外键检查异常: ' || SQLERRM);
        END;
        
        -- 检查必填字段完整性
        BEGIN
            SELECT COUNT(*) INTO v_error_count
            FROM USER_INFO
            WHERE USERNAME IS NULL OR PASSWORD_HASH IS NULL;
            
            IF v_error_count = 0 THEN
                ADD_TEST_RESULT('REQUIRED_FIELDS_USER', 'PASS', '用户必填字段完整');
            ELSE
                ADD_TEST_RESULT('REQUIRED_FIELDS_USER', 'FAIL', 
                    '用户必填字段不完整: ' || v_error_count || ' 条记录');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('REQUIRED_FIELDS_USER', 'FAIL', '用户必填字段检查异常: ' || SQLERRM);
        END;
        
        -- 检查数据类型完整性
        BEGIN
            SELECT COUNT(*) INTO v_error_count
            FROM PATIENT_INFO
            WHERE GENDER NOT IN ('M', 'F', 'U') AND IS_DELETED = 0;
            
            IF v_error_count = 0 THEN
                ADD_TEST_RESULT('DATA_TYPE_GENDER', 'PASS', '性别数据类型正确');
            ELSE
                ADD_TEST_RESULT('DATA_TYPE_GENDER', 'FAIL', 
                    '性别数据类型错误: ' || v_error_count || ' 条记录');
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                ADD_TEST_RESULT('DATA_TYPE_GENDER', 'FAIL', '性别数据类型检查异常: ' || SQLERRM);
        END;
        
        v_end_time := SYSTIMESTAMP;
        v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
        DBMS_OUTPUT.PUT_LINE('数据完整性测试完成，耗时: ' || v_duration || ' 秒');
        
    EXCEPTION
        WHEN OTHERS THEN
            ADD_TEST_RESULT('TEST_DATA_INTEGRITY', 'ERROR', SQLERRM);
    END TEST_DATA_INTEGRITY;
    
    PROCEDURE GENERATE_TEST_REPORT IS
        v_total_tests NUMBER := 0;
        v_passed_tests NUMBER := 0;
        v_failed_tests NUMBER := 0;
        v_error_tests NUMBER := 0;
        v_warn_tests NUMBER := 0;
        v_info_tests NUMBER := 0;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('==============================================');
        DBMS_OUTPUT.PUT_LINE('医保基金监管平台数据库重构测试报告');
        DBMS_OUTPUT.PUT_LINE('==============================================');
        DBMS_OUTPUT.PUT_LINE('测试时间: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'));
        DBMS_OUTPUT.PUT_LINE('');
        
        -- 统计测试结果
        FOR i IN 1..g_test_results.COUNT LOOP
            v_total_tests := v_total_tests + 1;
            
            CASE g_test_results(i).test_status
                WHEN 'PASS' THEN v_passed_tests := v_passed_tests + 1;
                WHEN 'FAIL' THEN v_failed_tests := v_failed_tests + 1;
                WHEN 'ERROR' THEN v_error_tests := v_error_tests + 1;
                WHEN 'WARN' THEN v_warn_tests := v_warn_tests + 1;
                WHEN 'INFO' THEN v_info_tests := v_info_tests + 1;
            END CASE;
        END LOOP;
        
        -- 输出统计信息
        DBMS_OUTPUT.PUT_LINE('测试统计:');
        DBMS_OUTPUT.PUT_LINE('- 总测试数: ' || v_total_tests);
        DBMS_OUTPUT.PUT_LINE('- 通过: ' || v_passed_tests || ' (' || 
            ROUND(v_passed_tests * 100 / v_total_tests, 1) || '%)');
        DBMS_OUTPUT.PUT_LINE('- 失败: ' || v_failed_tests || ' (' || 
            ROUND(v_failed_tests * 100 / v_total_tests, 1) || '%)');
        DBMS_OUTPUT.PUT_LINE('- 错误: ' || v_error_tests || ' (' || 
            ROUND(v_error_tests * 100 / v_total_tests, 1) || '%)');
        DBMS_OUTPUT.PUT_LINE('- 警告: ' || v_warn_tests || ' (' || 
            ROUND(v_warn_tests * 100 / v_total_tests, 1) || '%)');
        DBMS_OUTPUT.PUT_LINE('- 信息: ' || v_info_tests || ' (' || 
            ROUND(v_info_tests * 100 / v_total_tests, 1) || '%)');
        DBMS_OUTPUT.PUT_LINE('');
        
        -- 输出详细结果
        DBMS_OUTPUT.PUT_LINE('详细测试结果:');
        DBMS_OUTPUT.PUT_LINE('----------------------------------------------');
        
        FOR i IN 1..g_test_results.COUNT LOOP
            DECLARE
                v_status_icon VARCHAR2(5);
            BEGIN
                CASE g_test_results(i).test_status
                    WHEN 'PASS' THEN v_status_icon := '✓';
                    WHEN 'FAIL' THEN v_status_icon := '✗';
                    WHEN 'ERROR' THEN v_status_icon := '⚠';
                    WHEN 'WARN' THEN v_status_icon := '⚡';
                    WHEN 'INFO' THEN v_status_icon := 'ℹ';
                    ELSE v_status_icon := '?';
                END CASE;
                
                DBMS_OUTPUT.PUT_LINE(v_status_icon || ' [' || g_test_results(i).test_status || '] ' || 
                    g_test_results(i).test_name || 
                    CASE WHEN g_test_results(i).test_message IS NOT NULL 
                         THEN ': ' || g_test_results(i).test_message 
                         ELSE '' END);
            END;
        END LOOP;
        
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('==============================================');
        
        -- 总体评估
        IF v_failed_tests = 0 AND v_error_tests = 0 THEN
            DBMS_OUTPUT.PUT_LINE('✓ 数据库重构验证通过！');
        ELSIF v_failed_tests > 0 OR v_error_tests > 0 THEN
            DBMS_OUTPUT.PUT_LINE('✗ 数据库重构验证发现问题，请检查失败项目');
        END IF;
        
        DBMS_OUTPUT.PUT_LINE('==============================================');
        
    END GENERATE_TEST_REPORT;
    
END PKG_TEST_VALIDATION;
/

-- =====================================================
-- 2. 执行完整测试
-- =====================================================

BEGIN
    DBMS_OUTPUT.PUT_LINE('开始执行医保基金监管平台数据库重构验证测试...');
    DBMS_OUTPUT.PUT_LINE('');
    
    -- 执行所有测试
    PKG_TEST_VALIDATION.VALIDATE_TABLE_STRUCTURE();
    PKG_TEST_VALIDATION.VALIDATE_INDEXES();
    PKG_TEST_VALIDATION.VALIDATE_CONSTRAINTS();
    PKG_TEST_VALIDATION.VALIDATE_TRIGGERS();
    PKG_TEST_VALIDATION.VALIDATE_PARTITIONS();
    PKG_TEST_VALIDATION.TEST_BASIC_FUNCTIONS();
    PKG_TEST_VALIDATION.TEST_SECURITY_FEATURES();
    PKG_TEST_VALIDATION.TEST_PERFORMANCE();
    PKG_TEST_VALIDATION.TEST_DATA_INTEGRITY();
    
    -- 生成测试报告
    PKG_TEST_VALIDATION.GENERATE_TEST_REPORT();
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('测试执行异常: ' || SQLERRM);
END;
/

-- =====================================================
-- 3. 额外的验证查询
-- =====================================================

-- 检查表空间使用情况
SELECT 
    tablespace_name,
    ROUND(bytes/1024/1024, 2) AS size_mb,
    ROUND(maxbytes/1024/1024, 2) AS max_size_mb,
    ROUND((bytes/maxbytes)*100, 2) AS usage_percent
FROM dba_data_files
WHERE tablespace_name IN ('MEDIINSPECT_DATA', 'MEDIINSPECT_INDEX')
ORDER BY tablespace_name;

-- 检查无效对象
SELECT object_type, object_name, status
FROM user_objects
WHERE status = 'INVALID'
ORDER BY object_type, object_name;

-- 检查表统计信息
SELECT 
    table_name,
    num_rows,
    blocks,
    avg_row_len,
    last_analyzed
FROM user_tables
WHERE table_name IN (
    'USER_INFO', 'PATIENT_INFO', 'MEDICAL_RECORD',
    'SUPERVISION_RULE', 'KNOWLEDGE_DOCUMENT'
)
ORDER BY table_name;

-- 检查索引统计信息
SELECT 
    index_name,
    table_name,
    uniqueness,
    status,
    num_rows,
    distinct_keys,
    last_analyzed
FROM user_indexes
WHERE table_name IN (
    'USER_INFO', 'PATIENT_INFO', 'MEDICAL_RECORD',
    'SUPERVISION_RULE', 'KNOWLEDGE_DOCUMENT'
)
ORDER BY table_name, index_name;

-- 输出完成信息
BEGIN
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('医保基金监管平台数据库重构验证完成！');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('验证内容：');
    DBMS_OUTPUT.PUT_LINE('✓ 表结构完整性验证');
    DBMS_OUTPUT.PUT_LINE('✓ 索引和约束验证');
    DBMS_OUTPUT.PUT_LINE('✓ 触发器和分区验证');
    DBMS_OUTPUT.PUT_LINE('✓ 基础功能测试');
    DBMS_OUTPUT.PUT_LINE('✓ 安全功能测试');
    DBMS_OUTPUT.PUT_LINE('✓ 性能测试');
    DBMS_OUTPUT.PUT_LINE('✓ 数据完整性测试');
    DBMS_OUTPUT.PUT_LINE('==============================================');
    DBMS_OUTPUT.PUT_LINE('如需重新测试，请执行：');
    DBMS_OUTPUT.PUT_LINE('BEGIN');
    DBMS_OUTPUT.PUT_LINE('  PKG_TEST_VALIDATION.VALIDATE_TABLE_STRUCTURE();');
    DBMS_OUTPUT.PUT_LINE('  PKG_TEST_VALIDATION.TEST_BASIC_FUNCTIONS();');
    DBMS_OUTPUT.PUT_LINE('  PKG_TEST_VALIDATION.GENERATE_TEST_REPORT();');
    DBMS_OUTPUT.PUT_LINE('END;');
    DBMS_OUTPUT.PUT_LINE('==============================================');
END;
/