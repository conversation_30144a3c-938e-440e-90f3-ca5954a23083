-- =====================================================
-- 医保基金监管平台 - 知识库模块表结构
-- =====================================================

-- 1. 知识库分类表
CREATE TABLE KNOWLEDGE_CATEGORY (
    ID                  NUMBER(19,0) NOT NULL,
    CATEGORY_CODE       VARCHAR2(50) NOT NULL,
    CATEGORY_NAME       VARCHAR2(100) NOT NULL,
    PARENT_ID           NUMBER(19,0),
    CATEGORY_LEVEL      NUMBER(2,0) DEFAULT 1 NOT NULL,
    SORT_ORDER          NUMBER(5,0) DEFAULT 0,
    DESCRIPTION         VARCHAR2(500),
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_KNOWLEDGE_CATEGORY PRIMARY KEY (ID),
    CONSTRAINT UK_KNOWLEDGE_CATEGORY_CODE UNIQUE (CATEGORY_CODE),
    CONSTRAINT FK_KNOWLEDGE_CATEGORY_PARENT FOREIGN KEY (PARENT_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_LEVEL CHECK (CATEGORY_LEVEL BETWEEN 1 AND 5),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_CATEGORY_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE KNOWLEDGE_CATEGORY IS '知识库分类表';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.ID IS '分类ID';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_CODE IS '分类编码';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_NAME IS '分类名称';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.PARENT_ID IS '父分类ID';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.CATEGORY_LEVEL IS '分类层级';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.SORT_ORDER IS '排序号';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.DESCRIPTION IS '分类描述';
COMMENT ON COLUMN KNOWLEDGE_CATEGORY.IS_ACTIVE IS '是否启用(0:禁用,1:启用)';

-- 2. 知识库文档表
CREATE TABLE KNOWLEDGE_DOCUMENT (
    ID                  NUMBER(19,0) NOT NULL,
    DOCUMENT_CODE       VARCHAR2(50) NOT NULL,
    TITLE               VARCHAR2(200) NOT NULL,
    CATEGORY_ID         NUMBER(19,0) NOT NULL,
    DOCUMENT_TYPE       VARCHAR2(20) NOT NULL,
    FILE_NAME           VARCHAR2(255),
    FILE_PATH           VARCHAR2(500),
    FILE_SIZE           NUMBER(12,0),
    FILE_EXTENSION      VARCHAR2(10),
    CONTENT             CLOB,
    SUMMARY             VARCHAR2(1000),
    KEYWORDS            VARCHAR2(500),
    STATUS              VARCHAR2(20) DEFAULT 'DRAFT' NOT NULL,
    VERSION_NUMBER      VARCHAR2(20) DEFAULT '1.0' NOT NULL,
    IS_PUBLIC           NUMBER(1,0) DEFAULT 1 NOT NULL,
    VIEW_COUNT          NUMBER(10,0) DEFAULT 0,
    DOWNLOAD_COUNT      NUMBER(10,0) DEFAULT 0,
    PUBLISHED_AT        DATE,
    PUBLISHED_BY        NUMBER(19,0),
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_KNOWLEDGE_DOCUMENT PRIMARY KEY (ID),
    CONSTRAINT UK_KNOWLEDGE_DOCUMENT_CODE UNIQUE (DOCUMENT_CODE),
    CONSTRAINT FK_KNOWLEDGE_DOCUMENT_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES KNOWLEDGE_CATEGORY(ID),
    CONSTRAINT FK_KNOWLEDGE_DOCUMENT_PUBLISHER FOREIGN KEY (PUBLISHED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_TYPE CHECK (DOCUMENT_TYPE IN ('POLICY','REGULATION','GUIDELINE','FAQ','MANUAL','REPORT','OTHER')),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_STATUS CHECK (STATUS IN ('DRAFT','REVIEWING','PUBLISHED','ARCHIVED')),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_IS_PUBLIC CHECK (IS_PUBLIC IN (0,1)),
    CONSTRAINT CK_KNOWLEDGE_DOCUMENT_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE KNOWLEDGE_DOCUMENT IS '知识库文档表';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.ID IS '文档ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOCUMENT_CODE IS '文档编码';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.TITLE IS '文档标题';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CATEGORY_ID IS '分类ID';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOCUMENT_TYPE IS '文档类型(POLICY:政策,REGULATION:法规,GUIDELINE:指南,FAQ:常见问题,MANUAL:手册,REPORT:报告,OTHER:其他)';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.FILE_NAME IS '文件名';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.FILE_PATH IS '文件路径';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.FILE_SIZE IS '文件大小(字节)';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.FILE_EXTENSION IS '文件扩展名';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.CONTENT IS '文档内容';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.SUMMARY IS '文档摘要';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.KEYWORDS IS '关键词';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.STATUS IS '文档状态(DRAFT:草稿,REVIEWING:审核中,PUBLISHED:已发布,ARCHIVED:已归档)';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VERSION_NUMBER IS '版本号';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.IS_PUBLIC IS '是否公开(0:私有,1:公开)';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.VIEW_COUNT IS '查看次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.DOWNLOAD_COUNT IS '下载次数';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PUBLISHED_AT IS '发布时间';
COMMENT ON COLUMN KNOWLEDGE_DOCUMENT.PUBLISHED_BY IS '发布人ID';

-- 3. 文档访问记录表
CREATE TABLE KNOWLEDGE_ACCESS_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,
    USER_ID             NUMBER(19,0) NOT NULL,
    ACCESS_TYPE         VARCHAR2(20) NOT NULL,
    ACCESSED_AT         DATE DEFAULT SYSDATE NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(500),
    DURATION            NUMBER(10,0),
    CONSTRAINT PK_KNOWLEDGE_ACCESS_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_DOCUMENT FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_CREATED_BY FOREIGN KEY (CREATED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_KNOWLEDGE_ACCESS_UPDATED_BY FOREIGN KEY (UPDATED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_KNOWLEDGE_ACCESS_TYPE CHECK (ACCESS_TYPE IN ('VIEW','DOWNLOAD','SEARCH'))
);

COMMENT ON TABLE KNOWLEDGE_ACCESS_LOG IS '文档访问记录表';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ID IS '访问记录ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.DOCUMENT_ID IS '文档ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.USER_ID IS '用户ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESS_TYPE IS '访问类型(VIEW:查看,DOWNLOAD:下载,SEARCH:搜索)';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.ACCESSED_AT IS '访问时间';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.CREATED_AT IS '创建时间';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.IP_ADDRESS IS 'IP地址';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.USER_AGENT IS '用户代理';
COMMENT ON COLUMN KNOWLEDGE_ACCESS_LOG.DURATION IS '访问时长(秒)';

-- 4. 文档版本历史表
CREATE TABLE KNOWLEDGE_VERSION_HISTORY (
    ID                  NUMBER(19,0) NOT NULL,
    DOCUMENT_ID         NUMBER(19,0) NOT NULL,
    VERSION_NUMBER      VARCHAR2(20) NOT NULL,
    TITLE               VARCHAR2(200) NOT NULL,
    CONTENT             CLOB,
    FILE_PATH           VARCHAR2(500),
    CHANGE_DESCRIPTION  VARCHAR2(1000),
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_KNOWLEDGE_VERSION_HISTORY PRIMARY KEY (ID),
    CONSTRAINT FK_KNOWLEDGE_VERSION_DOCUMENT FOREIGN KEY (DOCUMENT_ID) REFERENCES KNOWLEDGE_DOCUMENT(ID),
    CONSTRAINT FK_KNOWLEDGE_VERSION_CREATED_BY FOREIGN KEY (CREATED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_KNOWLEDGE_VERSION_UPDATED_BY FOREIGN KEY (UPDATED_BY) REFERENCES USER_INFO(ID)
);

COMMENT ON TABLE KNOWLEDGE_VERSION_HISTORY IS '文档版本历史表';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.ID IS '版本历史ID';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.DOCUMENT_ID IS '文档ID';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.VERSION_NUMBER IS '版本号';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.TITLE IS '文档标题';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.CONTENT IS '文档内容';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.FILE_PATH IS '文件路径';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.CHANGE_DESCRIPTION IS '变更说明';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN KNOWLEDGE_VERSION_HISTORY.UPDATED_BY IS '更新人ID';

-- 创建序列和触发器
CREATE SEQUENCE SEQ_KNOWLEDGE_CATEGORY START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_KNOWLEDGE_DOCUMENT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_KNOWLEDGE_ACCESS_LOG START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_KNOWLEDGE_VERSION_HISTORY START WITH 1 INCREMENT BY 1;

-- 知识库分类表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_CATEGORY_ID
    BEFORE INSERT ON KNOWLEDGE_CATEGORY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_KNOWLEDGE_CATEGORY.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 知识库文档表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_DOCUMENT_ID
    BEFORE INSERT ON KNOWLEDGE_DOCUMENT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_KNOWLEDGE_DOCUMENT.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 文档访问记录表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_ACCESS_LOG_ID
    BEFORE INSERT ON KNOWLEDGE_ACCESS_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_KNOWLEDGE_ACCESS_LOG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 文档版本历史表触发器
CREATE OR REPLACE TRIGGER TRG_KNOWLEDGE_VERSION_HISTORY_ID
    BEFORE INSERT ON KNOWLEDGE_VERSION_HISTORY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_KNOWLEDGE_VERSION_HISTORY.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 创建索引
-- 注意: CATEGORY_CODE 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_PARENT ON KNOWLEDGE_CATEGORY(PARENT_ID);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_LEVEL ON KNOWLEDGE_CATEGORY(CATEGORY_LEVEL);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_ACTIVE ON KNOWLEDGE_CATEGORY(IS_ACTIVE);
CREATE INDEX IDX_KNOWLEDGE_CATEGORY_SORT ON KNOWLEDGE_CATEGORY(SORT_ORDER);

-- 注意: DOCUMENT_CODE 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_CATEGORY ON KNOWLEDGE_DOCUMENT(CATEGORY_ID);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_TYPE ON KNOWLEDGE_DOCUMENT(DOCUMENT_TYPE);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_STATUS ON KNOWLEDGE_DOCUMENT(STATUS);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_PUBLIC ON KNOWLEDGE_DOCUMENT(IS_PUBLIC);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_TITLE ON KNOWLEDGE_DOCUMENT(TITLE);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_PUBLISHED ON KNOWLEDGE_DOCUMENT(PUBLISHED_AT);
CREATE INDEX IDX_KNOWLEDGE_DOCUMENT_PUBLISHER ON KNOWLEDGE_DOCUMENT(PUBLISHED_BY);

CREATE INDEX IDX_KNOWLEDGE_ACCESS_DOCUMENT ON KNOWLEDGE_ACCESS_LOG(DOCUMENT_ID);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_USER ON KNOWLEDGE_ACCESS_LOG(USER_ID);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_TIME ON KNOWLEDGE_ACCESS_LOG(ACCESSED_AT);
CREATE INDEX IDX_KNOWLEDGE_ACCESS_TYPE ON KNOWLEDGE_ACCESS_LOG(ACCESS_TYPE);

CREATE INDEX IDX_KNOWLEDGE_VERSION_DOCUMENT ON KNOWLEDGE_VERSION_HISTORY(DOCUMENT_ID);
CREATE INDEX IDX_KNOWLEDGE_VERSION_NUMBER ON KNOWLEDGE_VERSION_HISTORY(VERSION_NUMBER);
CREATE INDEX IDX_KNOWLEDGE_VERSION_TIME ON KNOWLEDGE_VERSION_HISTORY(CREATED_AT);