-- =====================================================
-- 医保基金监管平台 - 系统配置管理表结构
-- 实现动态配置管理，解决硬编码约束问题
-- =====================================================

-- 1. 系统配置分类表
CREATE TABLE SYSTEM_CONFIG_CATEGORY (
    ID                  NUMBER(19,0) NOT NULL,
    CATEGORY_CODE       VARCHAR2(50) NOT NULL,
    CATEGORY_NAME       VARCHAR2(100) NOT NULL,
    DESCRIPTION         VARCHAR2(500),
    SORT_ORDER          NUMBER(5,0) DEFAULT 0,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_SYSTEM_CONFIG_CATEGORY PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_CATEGORY_CODE UNIQUE (CATEGORY_CODE),
    CONSTRAINT CK_SYSTEM_CONFIG_CATEGORY_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_CATEGORY_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE SYSTEM_CONFIG_CATEGORY IS '系统配置分类表';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.ID IS '分类ID';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_CODE IS '分类编码';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.CATEGORY_NAME IS '分类名称';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.DESCRIPTION IS '分类描述';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.SORT_ORDER IS '排序号';
COMMENT ON COLUMN SYSTEM_CONFIG_CATEGORY.IS_ACTIVE IS '是否启用(0:禁用,1:启用)';

-- 2. 系统配置项表
CREATE TABLE SYSTEM_CONFIG_ITEM (
    ID                  NUMBER(19,0) NOT NULL,
    CATEGORY_ID         NUMBER(19,0) NOT NULL,
    CONFIG_KEY          VARCHAR2(100) NOT NULL,
    CONFIG_VALUE        VARCHAR2(500) NOT NULL,
    CONFIG_LABEL        VARCHAR2(200) NOT NULL,
    DESCRIPTION         VARCHAR2(1000),
    DATA_TYPE           VARCHAR2(20) DEFAULT 'STRING' NOT NULL,
    IS_REQUIRED         NUMBER(1,0) DEFAULT 1 NOT NULL,
    SORT_ORDER          NUMBER(5,0) DEFAULT 0,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_SYSTEM_CONFIG_ITEM PRIMARY KEY (ID),
    CONSTRAINT UK_SYSTEM_CONFIG_ITEM_KEY UNIQUE (CATEGORY_ID, CONFIG_KEY),
    CONSTRAINT FK_SYSTEM_CONFIG_ITEM_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES SYSTEM_CONFIG_CATEGORY(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_DATA_TYPE CHECK (DATA_TYPE IN ('STRING','NUMBER','BOOLEAN','JSON')),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_REQUIRED CHECK (IS_REQUIRED IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_SYSTEM_CONFIG_ITEM_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE SYSTEM_CONFIG_ITEM IS '系统配置项表';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.ID IS '配置项ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CATEGORY_ID IS '分类ID';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_KEY IS '配置键';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_VALUE IS '配置值';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.CONFIG_LABEL IS '配置标签';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.DESCRIPTION IS '配置描述';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.DATA_TYPE IS '数据类型(STRING:字符串,NUMBER:数字,BOOLEAN:布尔,JSON:JSON对象)';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_REQUIRED IS '是否必需(0:否,1:是)';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.SORT_ORDER IS '排序号';
COMMENT ON COLUMN SYSTEM_CONFIG_ITEM.IS_ACTIVE IS '是否启用(0:禁用,1:启用)';

-- 3. 配置变更历史表
CREATE TABLE SYSTEM_CONFIG_HISTORY (
    ID                  NUMBER(19,0) NOT NULL,
    CONFIG_ITEM_ID      NUMBER(19,0) NOT NULL,
    OLD_VALUE           VARCHAR2(500),
    NEW_VALUE           VARCHAR2(500) NOT NULL,
    CHANGE_REASON       VARCHAR2(1000),
    CHANGE_TYPE         VARCHAR2(20) NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    CONSTRAINT PK_SYSTEM_CONFIG_HISTORY PRIMARY KEY (ID),
    CONSTRAINT FK_SYSTEM_CONFIG_HISTORY_ITEM FOREIGN KEY (CONFIG_ITEM_ID) REFERENCES SYSTEM_CONFIG_ITEM(ID),
    CONSTRAINT CK_SYSTEM_CONFIG_HISTORY_TYPE CHECK (CHANGE_TYPE IN ('CREATE','UPDATE','DELETE','ACTIVATE','DEACTIVATE'))
);

COMMENT ON TABLE SYSTEM_CONFIG_HISTORY IS '配置变更历史表';
COMMENT ON COLUMN SYSTEM_CONFIG_HISTORY.ID IS '历史记录ID';
COMMENT ON COLUMN SYSTEM_CONFIG_HISTORY.CONFIG_ITEM_ID IS '配置项ID';
COMMENT ON COLUMN SYSTEM_CONFIG_HISTORY.OLD_VALUE IS '原值';
COMMENT ON COLUMN SYSTEM_CONFIG_HISTORY.NEW_VALUE IS '新值';
COMMENT ON COLUMN SYSTEM_CONFIG_HISTORY.CHANGE_REASON IS '变更原因';
COMMENT ON COLUMN SYSTEM_CONFIG_HISTORY.CHANGE_TYPE IS '变更类型(CREATE:创建,UPDATE:更新,DELETE:删除,ACTIVATE:启用,DEACTIVATE:禁用)';

-- 创建序列
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_CATEGORY START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_ITEM START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_CONFIG_HISTORY START WITH 1 INCREMENT BY 1;

-- 创建触发器
CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_CATEGORY_ID
    BEFORE INSERT ON SYSTEM_CONFIG_CATEGORY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_CONFIG_CATEGORY.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_ITEM_ID
    BEFORE INSERT ON SYSTEM_CONFIG_ITEM
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_CONFIG_ITEM.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

CREATE OR REPLACE TRIGGER TRG_SYSTEM_CONFIG_HISTORY_ID
    BEFORE INSERT ON SYSTEM_CONFIG_HISTORY
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_SYSTEM_CONFIG_HISTORY.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;
/

-- 创建索引
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_CODE ON SYSTEM_CONFIG_CATEGORY(CATEGORY_CODE);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_ACTIVE ON SYSTEM_CONFIG_CATEGORY(IS_ACTIVE);
CREATE INDEX IDX_SYSTEM_CONFIG_CATEGORY_SORT ON SYSTEM_CONFIG_CATEGORY(SORT_ORDER);

CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_CATEGORY ON SYSTEM_CONFIG_ITEM(CATEGORY_ID);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_KEY ON SYSTEM_CONFIG_ITEM(CONFIG_KEY);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_ACTIVE ON SYSTEM_CONFIG_ITEM(IS_ACTIVE);
CREATE INDEX IDX_SYSTEM_CONFIG_ITEM_SORT ON SYSTEM_CONFIG_ITEM(SORT_ORDER);

CREATE INDEX IDX_SYSTEM_CONFIG_HISTORY_ITEM ON SYSTEM_CONFIG_HISTORY(CONFIG_ITEM_ID);
CREATE INDEX IDX_SYSTEM_CONFIG_HISTORY_TIME ON SYSTEM_CONFIG_HISTORY(CREATED_AT);
CREATE INDEX IDX_SYSTEM_CONFIG_HISTORY_TYPE ON SYSTEM_CONFIG_HISTORY(CHANGE_TYPE);
