-- =====================================================
-- 医保基金监管平台 - 用户管理模块表结构
-- =====================================================

-- 1. 角色表
CREATE TABLE USER_ROLE_INFO (
    ID              NUMBER(19,0) NOT NULL,
    ROLE_CODE       VARCHAR2(50) NOT NULL,
    ROLE_NAME       VARCHAR2(100) NOT NULL,
    DESCRIPTION     VARCHAR2(500),
    IS_ACTIVE       NUMBER(1,0) DEFAULT 1 NOT NULL,
    IS_DELETED      NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT      DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT      DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY      NUMBER(19,0),
    UPDATED_BY      NUMBER(19,0),
    CONSTRAINT PK_USER_ROLE_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_USER_ROLE_INFO_CODE UNIQUE (ROLE_CODE),
    CONSTRAINT CK_USER_ROLE_INFO_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_USER_ROLE_INFO_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE USER_ROLE_INFO IS '角色表';
COMMENT ON COLUMN USER_ROLE_INFO.ID IS '角色ID';
COMMENT ON COLUMN USER_ROLE_INFO.ROLE_CODE IS '角色编码';
COMMENT ON COLUMN USER_ROLE_INFO.ROLE_NAME IS '角色名称';
COMMENT ON COLUMN USER_ROLE_INFO.DESCRIPTION IS '角色描述';
COMMENT ON COLUMN USER_ROLE_INFO.IS_ACTIVE IS '是否启用(0:禁用,1:启用)';
COMMENT ON COLUMN USER_ROLE_INFO.IS_DELETED IS '是否删除(0:未删除,1:已删除)';

-- 2. 用户表
CREATE TABLE USER_INFO (
    ID              NUMBER(19,0) NOT NULL,
    USERNAME        VARCHAR2(50) NOT NULL,
    PASSWORD_HASH   VARCHAR2(255) NOT NULL,
    REAL_NAME       VARCHAR2(100) NOT NULL,
    EMAIL           VARCHAR2(100),
    PHONE           VARCHAR2(20),
    DEPARTMENT      VARCHAR2(100),
    POSITION        VARCHAR2(100),
    AVATAR          CLOB,
    STATUS          VARCHAR2(20) DEFAULT 'ACTIVE' NOT NULL,
    LAST_LOGIN_AT   DATE,
    LOGIN_ATTEMPT_COUNT NUMBER(3,0) DEFAULT 0 NOT NULL,
    LOCKED_UNTIL    DATE,
    IS_DELETED      NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT      DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT      DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY      NUMBER(19,0),
    UPDATED_BY      NUMBER(19,0),
    CONSTRAINT PK_USER_INFO PRIMARY KEY (ID),
    CONSTRAINT UK_USER_USERNAME UNIQUE (USERNAME),
    CONSTRAINT UK_USER_EMAIL UNIQUE (EMAIL),
    CONSTRAINT CK_USER_STATUS CHECK (STATUS IN ('ACTIVE','DISABLED','LOCKED')),
    CONSTRAINT CK_USER_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE USER_INFO IS '用户信息表';
COMMENT ON COLUMN USER_INFO.ID IS '用户ID';
COMMENT ON COLUMN USER_INFO.USERNAME IS '用户名';
COMMENT ON COLUMN USER_INFO.PASSWORD_HASH IS '密码哈希';
COMMENT ON COLUMN USER_INFO.REAL_NAME IS '真实姓名';
COMMENT ON COLUMN USER_INFO.EMAIL IS '邮箱地址';
COMMENT ON COLUMN USER_INFO.PHONE IS '联系电话';
COMMENT ON COLUMN USER_INFO.DEPARTMENT IS '所属部门';
COMMENT ON COLUMN USER_INFO.POSITION IS '职位';
COMMENT ON COLUMN USER_INFO.AVATAR IS '头像数据';
COMMENT ON COLUMN USER_INFO.STATUS IS '用户状态(ACTIVE:正常,DISABLED:禁用,LOCKED:锁定)';
COMMENT ON COLUMN USER_INFO.LAST_LOGIN_AT IS '最后登录时间';
COMMENT ON COLUMN USER_INFO.LOGIN_ATTEMPT_COUNT IS '登录尝试次数';
COMMENT ON COLUMN USER_INFO.LOCKED_UNTIL IS '锁定截止时间';

-- 3. 用户角色关联表
CREATE TABLE USER_ROLE_MAPPING (
    ID              NUMBER(19,0) NOT NULL,
    USER_ID         NUMBER(19,0) NOT NULL,
    ROLE_ID         NUMBER(19,0) NOT NULL,
    CREATED_AT      DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT      DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY      NUMBER(19,0),
    UPDATED_BY      NUMBER(19,0),
    IS_DELETED      NUMBER(1,0) DEFAULT 0 NOT NULL,
    CONSTRAINT PK_USER_ROLE_MAPPING PRIMARY KEY (ID),
    CONSTRAINT UK_USER_ROLE_MAPPING UNIQUE (USER_ID, ROLE_ID),
    CONSTRAINT FK_USER_ROLE_MAPPING_USER FOREIGN KEY (USER_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_USER_ROLE_MAPPING_ROLE FOREIGN KEY (ROLE_ID) REFERENCES USER_ROLE_INFO(ID),
    CONSTRAINT CK_USER_ROLE_MAPPING_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE USER_ROLE_MAPPING IS '用户角色关联表';
COMMENT ON COLUMN USER_ROLE_MAPPING.ID IS '关联ID';
COMMENT ON COLUMN USER_ROLE_MAPPING.USER_ID IS '用户ID';
COMMENT ON COLUMN USER_ROLE_MAPPING.ROLE_ID IS '角色ID';
COMMENT ON COLUMN USER_ROLE_MAPPING.CREATED_AT IS '创建时间';
COMMENT ON COLUMN USER_ROLE_MAPPING.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN USER_ROLE_MAPPING.CREATED_BY IS '创建人ID';
COMMENT ON COLUMN USER_ROLE_MAPPING.UPDATED_BY IS '更新人ID';

-- 创建序列和触发器
CREATE SEQUENCE SEQ_USER_ROLE_INFO START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_USER_INFO START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_USER_ROLE_MAPPING START WITH 1 INCREMENT BY 1;

-- 角色表触发器
CREATE OR REPLACE TRIGGER TRG_USER_ROLE_INFO_ID
    BEFORE INSERT ON USER_ROLE_INFO
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_USER_ROLE_INFO.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 用户表触发器
CREATE OR REPLACE TRIGGER TRG_USER_INFO_ID
    BEFORE INSERT ON USER_INFO
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_USER_INFO.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 用户角色关联表触发器
CREATE OR REPLACE TRIGGER TRG_USER_ROLE_MAPPING_ID
    BEFORE INSERT ON USER_ROLE_MAPPING
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_USER_ROLE_MAPPING.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 创建索引
-- 注意: ROLE_CODE, USERNAME, EMAIL 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_USER_ROLE_INFO_ACTIVE ON USER_ROLE_INFO(IS_ACTIVE);

CREATE INDEX IDX_USER_INFO_STATUS ON USER_INFO(STATUS);
CREATE INDEX IDX_USER_INFO_CREATED_AT ON USER_INFO(CREATED_AT);

CREATE INDEX IDX_USER_ROLE_MAPPING_USER_ID ON USER_ROLE_MAPPING(USER_ID);
CREATE INDEX IDX_USER_ROLE_MAPPING_ROLE_ID ON USER_ROLE_MAPPING(ROLE_ID);
CREATE INDEX IDX_USER_ROLE_MAPPING_CREATED_AT ON USER_ROLE_MAPPING(CREATED_AT);
