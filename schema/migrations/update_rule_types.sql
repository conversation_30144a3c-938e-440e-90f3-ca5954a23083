-- 更新监管规则类型分类
-- 将规则类型从 BASIC/ADVANCED/CUSTOM/TEMPLATE 改为 SQL/DSL/JAVASCRIPT
-- 执行日期: 2024-01-16

PROMPT '=== 开始更新规则类型分类 ==='

-- 1. 首先备份现有数据
CREATE TABLE RULE_SUPERVISION_BACKUP AS 
SELECT * FROM RULE_SUPERVISION WHERE 1=1;

PROMPT '✅ 已备份现有规则数据到 RULE_SUPERVISION_BACKUP 表'

-- 2. 删除现有的类型约束
ALTER TABLE RULE_SUPERVISION DROP CONSTRAINT CK_RULE_SUPERVISION_TYPE;

PROMPT '✅ 已删除旧的规则类型约束'

-- 3. 更新现有数据的规则类型
-- 根据规则内容智能判断类型
UPDATE RULE_SUPERVISION SET RULE_TYPE = 
  CASE 
    -- 如果有SQL内容，设为SQL
    WHEN RULE_SQL IS NOT NULL AND LENGTH(TRIM(RULE_SQL)) > 0 THEN 'SQL'
    -- 如果有DSL内容，设为DSL  
    WHEN RULE_DSL IS NOT NULL AND LENGTH(TRIM(RULE_DSL)) > 0 THEN 'DSL'
    -- 如果规则内容包含JavaScript特征，设为JAVASCRIPT
    WHEN RULE_CONTENT IS NOT NULL AND (
      UPPER(RULE_CONTENT) LIKE '%FUNCTION%' OR
      RULE_CONTENT LIKE '%=>%' OR
      UPPER(RULE_CONTENT) LIKE '%CONST %' OR
      UPPER(RULE_CONTENT) LIKE '%LET %' OR
      UPPER(RULE_CONTENT) LIKE '%VAR %'
    ) THEN 'JAVASCRIPT'
    -- 根据原有类型映射
    WHEN RULE_TYPE IN ('BASIC', 'TEMPLATE') THEN 'SQL'
    WHEN RULE_TYPE = 'ADVANCED' THEN 'DSL'
    WHEN RULE_TYPE = 'CUSTOM' THEN 'JAVASCRIPT'
    -- 默认为SQL
    ELSE 'SQL'
  END
WHERE IS_DELETED = 0;

PROMPT '✅ 已更新现有规则的类型分类'

-- 4. 添加新的类型约束
ALTER TABLE RULE_SUPERVISION ADD CONSTRAINT CK_RULE_SUPERVISION_TYPE 
CHECK (RULE_TYPE IN ('SQL','DSL','JAVASCRIPT'));

PROMPT '✅ 已添加新的规则类型约束'

-- 5. 更新表注释
COMMENT ON COLUMN RULE_SUPERVISION.RULE_TYPE IS '规则类型(SQL:SQL查询规则,DSL:领域特定语言规则,JAVASCRIPT:JavaScript脚本规则)';

PROMPT '✅ 已更新表字段注释'

-- 6. 验证更新结果
PROMPT '=== 验证更新结果 ==='

SELECT 
  RULE_TYPE,
  COUNT(*) as COUNT,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as PERCENTAGE
FROM RULE_SUPERVISION 
WHERE IS_DELETED = 0
GROUP BY RULE_TYPE
ORDER BY COUNT(*) DESC;

PROMPT '=== 规则类型更新完成 ==='

-- 7. 提交更改
COMMIT;

PROMPT '✅ 所有更改已提交'
PROMPT ''
PROMPT '注意事项:'
PROMPT '1. 原始数据已备份到 RULE_SUPERVISION_BACKUP 表'
PROMPT '2. 如需回滚，请执行 rollback_rule_types.sql'
PROMPT '3. 请更新应用程序代码以使用新的规则类型'
