-- 回滚规则类型更新
-- 将规则类型从 SQL/DSL/JAVASCRIPT 恢复为 BASIC/ADVANCED/CUSTOM/TEMPLATE
-- 执行日期: 2024-01-16

PROMPT '=== 开始回滚规则类型分类 ==='

-- 1. 检查备份表是否存在
SELECT COUNT(*) as BACKUP_COUNT FROM RULE_SUPERVISION_BACKUP;

-- 2. 删除新的类型约束
ALTER TABLE RULE_SUPERVISION DROP CONSTRAINT CK_RULE_SUPERVISION_TYPE;

PROMPT '✅ 已删除新的规则类型约束'

-- 3. 从备份表恢复原始数据
UPDATE RULE_SUPERVISION r SET 
  RULE_TYPE = (SELECT RULE_TYPE FROM RULE_SUPERVISION_BACKUP b WHERE b.ID = r.ID)
WHERE EXISTS (SELECT 1 FROM RULE_SUPERVISION_BACKUP b WHERE b.ID = r.ID);

PROMPT '✅ 已从备份恢复原始规则类型'

-- 4. 恢复原有的类型约束
ALTER TABLE RULE_SUPERVISION ADD CONSTRAINT CK_RULE_SUPERVISION_TYPE 
CHECK (RULE_TYPE IN ('BASIC','ADVANCED','CUSTOM','TEMPLATE'));

PROMPT '✅ 已恢复原有的规则类型约束'

-- 5. 恢复表注释
COMMENT ON COLUMN RULE_SUPERVISION.RULE_TYPE IS '规则类型(BASIC:基础规则,ADVANCED:高级规则,CUSTOM:自定义规则,TEMPLATE:模板规则)';

PROMPT '✅ 已恢复表字段注释'

-- 6. 验证回滚结果
PROMPT '=== 验证回滚结果 ==='

SELECT 
  RULE_TYPE,
  COUNT(*) as COUNT,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as PERCENTAGE
FROM RULE_SUPERVISION 
WHERE IS_DELETED = 0
GROUP BY RULE_TYPE
ORDER BY COUNT(*) DESC;

-- 7. 提交更改
COMMIT;

PROMPT '✅ 规则类型已成功回滚'
PROMPT ''
PROMPT '注意事项:'
PROMPT '1. 规则类型已恢复为原始值'
PROMPT '2. 请相应更新应用程序代码'
PROMPT '3. 备份表 RULE_SUPERVISION_BACKUP 仍然保留'
