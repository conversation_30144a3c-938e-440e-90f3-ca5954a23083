# 医保基金监管平台数据库设计规范

## 1. 总体设计原则

### 1.1 设计理念
- **统一性**：所有表结构遵循统一的设计模式和命名规范
- **安全性**：敏感数据加密存储，完善的访问控制机制
- **扩展性**：支持多租户、微服务架构和水平扩展
- **性能**：合理的分区策略、索引设计和缓存机制
- **合规性**：满足医疗行业数据保护和审计要求

### 1.2 架构原则
- **领域驱动设计**：按业务域划分数据模型
- **CQRS模式**：读写分离，优化查询性能
- **事件驱动**：通过事件实现模块间解耦
- **最终一致性**：分布式环境下的数据一致性保证

## 2. 命名规范

### 2.1 表命名规范
```sql
-- 格式：[模块前缀_]业务实体名称
-- 示例：
USER_INFO                    -- 用户信息表
MEDICAL_RECORD              -- 医疗记录表
SUPERVISION_RULE            -- 监管规则表
KNOWLEDGE_DOCUMENT          -- 知识库文档表
SYSTEM_CONFIG_ITEM          -- 系统配置项表
```

### 2.2 字段命名规范
```sql
-- 主键：ID
-- 外键：关联表名_ID
-- 时间字段：动作_AT (如：CREATED_AT, UPDATED_AT)
-- 状态字段：IS_状态 或 状态_STATUS
-- 数量字段：数量类型_COUNT
-- 标识字段：业务含义_CODE 或 业务含义_NO

-- 示例：
ID                          -- 主键
USER_ID                     -- 用户ID（外键）
CREATED_AT                  -- 创建时间
IS_ACTIVE                   -- 是否激活
LOGIN_COUNT                 -- 登录次数
USER_CODE                   -- 用户编码
```

### 2.3 索引命名规范
```sql
-- 主键索引：PK_表名
-- 唯一索引：UK_表名_字段名
-- 普通索引：IDX_表名_字段名
-- 复合索引：IDX_表名_字段1_字段2
-- 函数索引：FIDX_表名_函数描述

-- 示例：
PK_USER_INFO                -- 主键索引
UK_USER_INFO_USERNAME       -- 唯一索引
IDX_USER_INFO_STATUS        -- 普通索引
IDX_USER_INFO_DEPT_ROLE     -- 复合索引
FIDX_USER_INFO_UPPER_NAME   -- 函数索引
```

### 2.4 约束命名规范
```sql
-- 主键约束：PK_表名
-- 外键约束：FK_表名_引用表名
-- 检查约束：CK_表名_字段名
-- 非空约束：NN_表名_字段名

-- 示例：
PK_USER_INFO                -- 主键约束
FK_USER_INFO_DEPT           -- 外键约束
CK_USER_INFO_STATUS         -- 检查约束
NN_USER_INFO_USERNAME       -- 非空约束
```

## 3. 标准表结构模板

### 3.1 基础表模板
```sql
CREATE TABLE [表名] (
    -- 主键（必须）
    ID                  NUMBER(19,0) NOT NULL,
    
    -- 多租户支持（必须）
    TENANT_ID           NUMBER(19,0),
    
    -- 业务字段
    -- ...
    
    -- 数据分类（必须）
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    
    -- 审计字段（必须）
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    
    -- 版本控制（推荐）
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    
    -- 逻辑删除（必须）
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    
    -- 主键约束
    CONSTRAINT PK_[表名] PRIMARY KEY (ID),
    
    -- 检查约束
    CONSTRAINT CK_[表名]_DATA_CLASSIFICATION 
        CHECK (DATA_CLASSIFICATION IN ('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED')),
    CONSTRAINT CK_[表名]_IS_DELETED 
        CHECK (IS_DELETED IN (0, 1))
);

-- 标准索引
CREATE INDEX IDX_[表名]_TENANT_ID ON [表名](TENANT_ID);
CREATE INDEX IDX_[表名]_CREATED_AT ON [表名](CREATED_AT);
CREATE INDEX IDX_[表名]_IS_DELETED ON [表名](IS_DELETED);

-- 标准注释
COMMENT ON TABLE [表名] IS '[表描述]';
COMMENT ON COLUMN [表名].ID IS '主键';
COMMENT ON COLUMN [表名].TENANT_ID IS '租户ID';
COMMENT ON COLUMN [表名].DATA_CLASSIFICATION IS '数据分类：PUBLIC-公开，INTERNAL-内部，CONFIDENTIAL-机密，RESTRICTED-限制';
COMMENT ON COLUMN [表名].CREATED_AT IS '创建时间';
COMMENT ON COLUMN [表名].UPDATED_AT IS '更新时间';
COMMENT ON COLUMN [表名].CREATED_BY IS '创建人ID';
COMMENT ON COLUMN [表名].UPDATED_BY IS '更新人ID';
COMMENT ON COLUMN [表名].VERSION IS '版本号，用于乐观锁';
COMMENT ON COLUMN [表名].IS_DELETED IS '逻辑删除标识：0-未删除，1-已删除';
```

### 3.2 日志表模板
```sql
CREATE TABLE [日志表名] (
    -- 主键
    ID                  NUMBER(19,0) NOT NULL,
    
    -- 多租户支持
    TENANT_ID           NUMBER(19,0),
    
    -- 日志基础字段
    LOG_TYPE            VARCHAR2(50) NOT NULL,
    LOG_LEVEL           VARCHAR2(20) DEFAULT 'INFO' NOT NULL,
    LOG_SOURCE          VARCHAR2(100),
    LOG_MESSAGE         CLOB,
    LOG_DETAILS         CLOB,
    
    -- 关联信息
    USER_ID             NUMBER(19,0),
    SESSION_ID          VARCHAR2(100),
    REQUEST_ID          VARCHAR2(100),
    IP_ADDRESS          VARCHAR2(50),
    USER_AGENT          VARCHAR2(500),
    
    -- 时间字段
    LOG_TIME            TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    
    -- 数据分类
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    
    -- 主键约束
    CONSTRAINT PK_[日志表名] PRIMARY KEY (ID),
    
    -- 检查约束
    CONSTRAINT CK_[日志表名]_LOG_LEVEL 
        CHECK (LOG_LEVEL IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'))
)
-- 按天分区
PARTITION BY RANGE (LOG_TIME) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))
(
    PARTITION P_INITIAL VALUES LESS THAN (TIMESTAMP '2024-01-01 00:00:00')
);

-- 压缩存储
ALTER TABLE [日志表名] COMPRESS FOR OLTP;
```

### 3.3 配置表模板
```sql
CREATE TABLE [配置表名] (
    -- 主键
    ID                  NUMBER(19,0) NOT NULL,
    
    -- 多租户支持
    TENANT_ID           NUMBER(19,0),
    
    -- 配置基础字段
    CONFIG_KEY          VARCHAR2(200) NOT NULL,
    CONFIG_VALUE        CLOB,
    CONFIG_TYPE         VARCHAR2(50) DEFAULT 'STRING' NOT NULL,
    CONFIG_CATEGORY     VARCHAR2(100),
    CONFIG_DESCRIPTION  VARCHAR2(1000),
    
    -- 配置属性
    IS_ENCRYPTED        NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_SYSTEM           NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_READONLY         NUMBER(1,0) DEFAULT 0 NOT NULL,
    SORT_ORDER          NUMBER(10,0) DEFAULT 0,
    
    -- 生效时间
    EFFECTIVE_DATE      TIMESTAMP(6),
    EXPIRY_DATE         TIMESTAMP(6),
    
    -- 标准字段
    DATA_CLASSIFICATION VARCHAR2(20) DEFAULT 'INTERNAL' NOT NULL,
    CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    
    -- 约束
    CONSTRAINT PK_[配置表名] PRIMARY KEY (ID),
    CONSTRAINT UK_[配置表名]_KEY UNIQUE (TENANT_ID, CONFIG_KEY, IS_DELETED),
    CONSTRAINT CK_[配置表名]_CONFIG_TYPE 
        CHECK (CONFIG_TYPE IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'XML'))
);
```

## 4. 数据类型规范

### 4.1 基础数据类型
```sql
-- 主键和外键
NUMBER(19,0)                -- 主键、外键（支持大数据量）

-- 字符串类型
VARCHAR2(50)                -- 短字符串（编码、名称等）
VARCHAR2(200)               -- 中等字符串（标题、描述等）
VARCHAR2(1000)              -- 长字符串（路径、URL等）
CLOB                        -- 大文本（内容、详情等）

-- 数值类型
NUMBER(10,0)                -- 整数（计数、状态等）
NUMBER(15,2)                -- 金额（保留2位小数）
NUMBER(10,4)                -- 比例、汇率（保留4位小数）

-- 时间类型
TIMESTAMP(6)                -- 时间戳（推荐，精确到微秒）
DATE                        -- 日期（仅需要日期时使用）

-- 二进制类型
RAW(256)                    -- 加密字段
BLOB                        -- 大二进制对象（文件等）

-- 标识类型
NUMBER(1,0)                 -- 布尔值（0/1）
```

### 4.2 特殊字段类型
```sql
-- JSON字段（Oracle 12c+）
VARCHAR2(4000) CHECK (JSON_VALID(column_name) = 1)
-- 或使用CLOB存储大JSON
CLOB CHECK (JSON_VALID(column_name) = 1)

-- 加密字段
RAW(256)                    -- AES-256加密后的数据

-- 枚举字段
VARCHAR2(50) CHECK (column_name IN ('VALUE1', 'VALUE2', 'VALUE3'))
```

## 5. 索引设计规范

### 5.1 索引创建原则
- **主键索引**：自动创建，无需手动创建
- **外键索引**：必须创建，避免锁等待
- **查询索引**：基于实际查询场景创建
- **复合索引**：遵循最左前缀原则
- **函数索引**：用于函数查询优化

### 5.2 标准索引模板
```sql
-- 外键索引（必须）
CREATE INDEX IDX_[表名]_[外键字段] ON [表名]([外键字段]);

-- 租户索引（必须）
CREATE INDEX IDX_[表名]_TENANT_ID ON [表名](TENANT_ID);

-- 时间索引（推荐）
CREATE INDEX IDX_[表名]_CREATED_AT ON [表名](CREATED_AT);
CREATE INDEX IDX_[表名]_UPDATED_AT ON [表名](UPDATED_AT);

-- 状态索引（常用）
CREATE INDEX IDX_[表名]_STATUS ON [表名](STATUS);
CREATE INDEX IDX_[表名]_IS_DELETED ON [表名](IS_DELETED);

-- 复合索引（根据查询场景）
CREATE INDEX IDX_[表名]_TENANT_STATUS ON [表名](TENANT_ID, STATUS, IS_DELETED);

-- 函数索引（特殊场景）
CREATE INDEX FIDX_[表名]_UPPER_NAME ON [表名](UPPER(NAME));
```

### 5.3 分区表索引
```sql
-- 本地索引（推荐）
CREATE INDEX IDX_[表名]_[字段] ON [表名]([字段]) LOCAL;

-- 全局索引（特殊需求）
CREATE INDEX IDX_[表名]_[字段] ON [表名]([字段]) GLOBAL;
```

## 6. 分区策略

### 6.1 分区类型选择
```sql
-- 时间范围分区（日志、历史数据）
PARTITION BY RANGE (CREATED_AT) 
INTERVAL (NUMTODSINTERVAL(1, 'DAY'))  -- 按天分区
-- 或
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH')) -- 按月分区

-- 哈希分区（均匀分布）
PARTITION BY HASH (ID) PARTITIONS 8;

-- 列表分区（枚举值）
PARTITION BY LIST (STATUS) (
    PARTITION P_ACTIVE VALUES ('ACTIVE'),
    PARTITION P_INACTIVE VALUES ('INACTIVE'),
    PARTITION P_DELETED VALUES ('DELETED')
);

-- 复合分区（大表）
PARTITION BY RANGE (CREATED_AT)
SUBPARTITION BY HASH (TENANT_ID) SUBPARTITIONS 4
(
    PARTITION P_2024_01 VALUES LESS THAN (DATE '2024-02-01'),
    PARTITION P_2024_02 VALUES LESS THAN (DATE '2024-03-01')
);
```

### 6.2 分区维护
```sql
-- 自动分区管理
ALTER TABLE [表名] SET INTERVAL (NUMTODSINTERVAL(1, 'DAY'));

-- 分区压缩
ALTER TABLE [表名] MODIFY PARTITION [分区名] COMPRESS FOR OLTP;

-- 分区删除（数据归档）
ALTER TABLE [表名] DROP PARTITION [分区名];
```

## 7. 安全设计规范

### 7.1 数据分类标准
```sql
-- 数据分类枚举
DATA_CLASSIFICATION VARCHAR2(20) CHECK (
    DATA_CLASSIFICATION IN (
        'PUBLIC',       -- 公开数据
        'INTERNAL',     -- 内部数据
        'CONFIDENTIAL', -- 机密数据
        'RESTRICTED'    -- 限制数据
    )
) DEFAULT 'INTERNAL' NOT NULL;
```

### 7.2 敏感数据加密
```sql
-- 加密字段设计
CREATE TABLE PATIENT_INFO (
    ID                  NUMBER(19,0) NOT NULL,
    NAME_ENCRYPTED      RAW(256),     -- 加密姓名
    ID_CARD_ENCRYPTED   RAW(256),     -- 加密身份证
    PHONE_ENCRYPTED     RAW(256),     -- 加密电话
    -- 其他字段...
);

-- 加密函数
CREATE OR REPLACE FUNCTION ENCRYPT_SENSITIVE_DATA(
    p_data VARCHAR2,
    p_key  RAW DEFAULT NULL
) RETURN RAW
IS
    l_key RAW(32) := NVL(p_key, UTL_RAW.CAST_TO_RAW('DEFAULT_ENCRYPTION_KEY_32_BYTES'));
BEGIN
    RETURN DBMS_CRYPTO.ENCRYPT(
        src => UTL_RAW.CAST_TO_RAW(p_data),
        typ => DBMS_CRYPTO.AES256 + DBMS_CRYPTO.CHAIN_CBC + DBMS_CRYPTO.PAD_PKCS5,
        key => l_key
    );
END;
/

-- 解密函数
CREATE OR REPLACE FUNCTION DECRYPT_SENSITIVE_DATA(
    p_encrypted_data RAW,
    p_key           RAW DEFAULT NULL
) RETURN VARCHAR2
IS
    l_key RAW(32) := NVL(p_key, UTL_RAW.CAST_TO_RAW('DEFAULT_ENCRYPTION_KEY_32_BYTES'));
BEGIN
    RETURN UTL_RAW.CAST_TO_VARCHAR2(
        DBMS_CRYPTO.DECRYPT(
            src => p_encrypted_data,
            typ => DBMS_CRYPTO.AES256 + DBMS_CRYPTO.CHAIN_CBC + DBMS_CRYPTO.PAD_PKCS5,
            key => l_key
        )
    );
END;
/
```

### 7.3 访问控制
```sql
-- 行级安全策略
CREATE OR REPLACE FUNCTION TENANT_SECURITY_POLICY(
    p_schema VARCHAR2,
    p_object VARCHAR2
) RETURN VARCHAR2
IS
BEGIN
    RETURN 'TENANT_ID = SYS_CONTEXT(''USERENV'', ''CLIENT_IDENTIFIER'')';
END;
/

-- 应用行级安全
BEGIN
    DBMS_RLS.ADD_POLICY(
        object_schema   => 'MEDIINSPECT',
        object_name     => 'PATIENT_INFO',
        policy_name     => 'TENANT_SECURITY_POLICY',
        function_schema => 'MEDIINSPECT',
        policy_function => 'TENANT_SECURITY_POLICY',
        statement_types => 'SELECT, INSERT, UPDATE, DELETE'
    );
END;
/
```

## 8. 性能优化规范

### 8.1 查询优化
```sql
-- 避免全表扫描
-- 错误示例
SELECT * FROM USER_INFO WHERE UPPER(USERNAME) = 'ADMIN';

-- 正确示例（使用函数索引）
CREATE INDEX FIDX_USER_INFO_UPPER_USERNAME ON USER_INFO(UPPER(USERNAME));
SELECT * FROM USER_INFO WHERE UPPER(USERNAME) = 'ADMIN';

-- 使用绑定变量
-- 错误示例
SELECT * FROM USER_INFO WHERE ID = 12345;

-- 正确示例
SELECT * FROM USER_INFO WHERE ID = :user_id;
```

### 8.2 批量操作优化
```sql
-- 批量插入
INSERT /*+ APPEND */ INTO TARGET_TABLE
SELECT * FROM SOURCE_TABLE;

-- 批量更新
UPDATE /*+ USE_NL(t1 t2) */ TARGET_TABLE t1
SET column1 = (
    SELECT column1 FROM SOURCE_TABLE t2 
    WHERE t2.id = t1.id
)
WHERE EXISTS (
    SELECT 1 FROM SOURCE_TABLE t2 
    WHERE t2.id = t1.id
);
```

### 8.3 统计信息维护
```sql
-- 自动统计信息收集
BEGIN
    DBMS_STATS.SET_TABLE_PREFS(
        ownname => 'MEDIINSPECT',
        tabname => '[表名]',
        pname   => 'INCREMENTAL',
        pvalue  => 'TRUE'
    );
END;
/

-- 手动收集统计信息
BEGIN
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => 'MEDIINSPECT',
        tabname => '[表名]',
        estimate_percent => DBMS_STATS.AUTO_SAMPLE_SIZE,
        method_opt => 'FOR ALL COLUMNS SIZE AUTO',
        cascade => TRUE
    );
END;
/
```

## 9. 审计和监控

### 9.1 审计字段标准
```sql
-- 所有业务表必须包含的审计字段
CREATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
UPDATED_AT          TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
CREATED_BY          NUMBER(19,0),
UPDATED_BY          NUMBER(19,0),
VERSION             NUMBER(10,0) DEFAULT 1 NOT NULL,
IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL;
```

### 9.2 审计触发器模板
```sql
CREATE OR REPLACE TRIGGER TRG_[表名]_AUDIT
    BEFORE INSERT OR UPDATE ON [表名]
    FOR EACH ROW
BEGIN
    -- 插入时设置创建信息
    IF INSERTING THEN
        :NEW.CREATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.CREATED_BY := NVL(:NEW.CREATED_BY, SYS_CONTEXT('USERENV', 'CLIENT_IDENTIFIER'));
        :NEW.UPDATED_BY := :NEW.CREATED_BY;
        :NEW.VERSION := 1;
    END IF;
    
    -- 更新时设置更新信息
    IF UPDATING THEN
        :NEW.UPDATED_AT := SYSTIMESTAMP;
        :NEW.UPDATED_BY := NVL(:NEW.UPDATED_BY, SYS_CONTEXT('USERENV', 'CLIENT_IDENTIFIER'));
        :NEW.VERSION := :OLD.VERSION + 1;
        -- 防止修改创建信息
        :NEW.CREATED_AT := :OLD.CREATED_AT;
        :NEW.CREATED_BY := :OLD.CREATED_BY;
    END IF;
END;
/
```

### 9.3 变更历史记录
```sql
-- 变更历史表模板
CREATE TABLE [表名]_HISTORY (
    HISTORY_ID          NUMBER(19,0) NOT NULL,
    OPERATION_TYPE      VARCHAR2(10) NOT NULL, -- INSERT/UPDATE/DELETE
    OPERATION_TIME      TIMESTAMP(6) DEFAULT SYSTIMESTAMP NOT NULL,
    OPERATION_USER      NUMBER(19,0),
    -- 原表所有字段...
    CONSTRAINT PK_[表名]_HISTORY PRIMARY KEY (HISTORY_ID)
)
PARTITION BY RANGE (OPERATION_TIME) 
INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'));

-- 历史记录触发器
CREATE OR REPLACE TRIGGER TRG_[表名]_HISTORY
    AFTER INSERT OR UPDATE OR DELETE ON [表名]
    FOR EACH ROW
DECLARE
    l_operation VARCHAR2(10);
BEGIN
    IF INSERTING THEN
        l_operation := 'INSERT';
    ELSIF UPDATING THEN
        l_operation := 'UPDATE';
    ELSE
        l_operation := 'DELETE';
    END IF;
    
    INSERT INTO [表名]_HISTORY (
        HISTORY_ID,
        OPERATION_TYPE,
        OPERATION_TIME,
        OPERATION_USER,
        -- 其他字段...
    ) VALUES (
        SEQ_[表名]_HISTORY.NEXTVAL,
        l_operation,
        SYSTIMESTAMP,
        SYS_CONTEXT('USERENV', 'CLIENT_IDENTIFIER'),
        -- 对应值...
    );
END;
/
```

## 10. 数据质量规范

### 10.1 数据完整性约束
```sql
-- 非空约束
ALTER TABLE [表名] MODIFY [字段名] NOT NULL;

-- 唯一约束
ALTER TABLE [表名] ADD CONSTRAINT UK_[表名]_[字段名] UNIQUE ([字段名]);

-- 检查约束
ALTER TABLE [表名] ADD CONSTRAINT CK_[表名]_[字段名] 
CHECK ([字段名] IN ('VALUE1', 'VALUE2', 'VALUE3'));

-- 外键约束
ALTER TABLE [表名] ADD CONSTRAINT FK_[表名]_[引用表名] 
FOREIGN KEY ([外键字段]) REFERENCES [引用表名]([主键字段]);
```

### 10.2 数据验证函数
```sql
-- 身份证号验证
CREATE OR REPLACE FUNCTION VALIDATE_ID_CARD(p_id_card VARCHAR2) 
RETURN NUMBER
IS
BEGIN
    -- 验证逻辑...
    IF LENGTH(p_id_card) = 18 AND REGEXP_LIKE(p_id_card, '^[0-9]{17}[0-9X]$') THEN
        RETURN 1;
    ELSE
        RETURN 0;
    END IF;
END;
/

-- 手机号验证
CREATE OR REPLACE FUNCTION VALIDATE_PHONE(p_phone VARCHAR2) 
RETURN NUMBER
IS
BEGIN
    IF REGEXP_LIKE(p_phone, '^1[3-9][0-9]{9}$') THEN
        RETURN 1;
    ELSE
        RETURN 0;
    END IF;
END;
/

-- 邮箱验证
CREATE OR REPLACE FUNCTION VALIDATE_EMAIL(p_email VARCHAR2) 
RETURN NUMBER
IS
BEGIN
    IF REGEXP_LIKE(p_email, '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$') THEN
        RETURN 1;
    ELSE
        RETURN 0;
    END IF;
END;
/
```

## 11. 部署和维护规范

### 11.1 环境配置
```sql
-- 开发环境
ALTER SYSTEM SET DB_BLOCK_SIZE=8192;
ALTER SYSTEM SET SGA_TARGET=2G;
ALTER SYSTEM SET PGA_AGGREGATE_TARGET=1G;

-- 生产环境
ALTER SYSTEM SET DB_BLOCK_SIZE=8192;
ALTER SYSTEM SET SGA_TARGET=8G;
ALTER SYSTEM SET PGA_AGGREGATE_TARGET=4G;
ALTER SYSTEM SET OPTIMIZER_MODE=ALL_ROWS;
```

### 11.2 备份策略
```bash
#!/bin/bash
# 全量备份脚本
RMAN_SCRIPT="
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE COMPRESSION ALGORITHM 'MEDIUM';
BACKUP DATABASE PLUS ARCHIVELOG DELETE INPUT;
DELETE OBSOLETE;
EXIT;
"

echo "$RMAN_SCRIPT" | rman target / catalog rman/rman@rmancat
```

### 11.3 监控脚本
```sql
-- 性能监控视图
CREATE OR REPLACE VIEW V_SYSTEM_PERFORMANCE AS
SELECT 
    'DATABASE_SIZE' as METRIC_NAME,
    ROUND(SUM(BYTES)/1024/1024/1024, 2) as METRIC_VALUE,
    'GB' as UNIT,
    SYSTIMESTAMP as COLLECT_TIME
FROM DBA_DATA_FILES
UNION ALL
SELECT 
    'ACTIVE_SESSIONS' as METRIC_NAME,
    COUNT(*) as METRIC_VALUE,
    'COUNT' as UNIT,
    SYSTIMESTAMP as COLLECT_TIME
FROM V$SESSION 
WHERE STATUS = 'ACTIVE'
UNION ALL
SELECT 
    'BUFFER_CACHE_HIT_RATIO' as METRIC_NAME,
    ROUND((1 - (phy.value / (cur.value + con.value))) * 100, 2) as METRIC_VALUE,
    'PERCENT' as UNIT,
    SYSTIMESTAMP as COLLECT_TIME
FROM V$SYSSTAT cur, V$SYSSTAT con, V$SYSSTAT phy
WHERE cur.name = 'db block gets'
AND con.name = 'consistent gets'
AND phy.name = 'physical reads';
```

## 12. 总结

本设计规范涵盖了医保基金监管平台数据库设计的各个方面，包括：

1. **统一的命名规范**：确保代码的可读性和维护性
2. **标准的表结构模板**：提高开发效率和代码质量
3. **完善的安全机制**：保护敏感医疗数据
4. **优化的性能设计**：支持大规模数据处理
5. **全面的审计机制**：满足合规要求
6. **严格的数据质量控制**：确保数据完整性
7. **规范的部署维护流程**：保障系统稳定运行

遵循本规范可以确保数据库设计的一致性、安全性、性能和可维护性，为医保基金监管平台提供坚实的数据基础。