-- =====================================================
-- 医保基金监管平台 - 监管规则模块表结构
-- =====================================================

-- 1. 监管规则主表
CREATE TABLE RULE_SUPERVISION (
    ID                  NUMBER(19,0) NOT NULL,
    RULE_CODE           VARCHAR2(50) NOT NULL,
    RULE_NAME           VARCHAR2(200) NOT NULL,
    RULE_TYPE           VARCHAR2(50) NOT NULL,
    RULE_CATEGORY       VARCHAR2(50) NOT NULL,
    DESCRIPTION         CLOB,
    RULE_CONTENT        CLOB NOT NULL,
    RULE_SQL            CLOB,
    RULE_DSL            CLOB,
    PRIORITY_LEVEL      NUMBER(2,0) DEFAULT 5 NOT NULL,
    SEVERITY_LEVEL      VARCHAR2(20) DEFAULT 'MEDIUM' NOT NULL,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    EFFECTIVE_DATE      DATE DEFAULT SYSDATE NOT NULL,
    EXPIRY_DATE         DATE,
    VERSION_NUMBER      VARCHAR2(20) DEFAULT '1.0' NOT NULL,
    PARENT_RULE_ID      NUMBER(19,0),
    RULE_SOURCE         VARCHAR2(200),
    CREATED_FROM        VARCHAR2(50),
    EXECUTION_COUNT     NUMBER(10,0) DEFAULT 0,
    SUCCESS_COUNT       NUMBER(10,0) DEFAULT 0,
    LAST_EXECUTED_AT    DATE,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_RULE_SUPERVISION PRIMARY KEY (ID),
    CONSTRAINT UK_RULE_SUPERVISION_CODE UNIQUE (RULE_CODE),
    CONSTRAINT FK_RULE_SUPERVISION_PARENT FOREIGN KEY (PARENT_RULE_ID) REFERENCES RULE_SUPERVISION(ID),
    CONSTRAINT CK_RULE_SUPERVISION_TYPE CHECK (RULE_TYPE IN ('SQL','DSL','JAVASCRIPT')),
    CONSTRAINT CK_RULE_SUPERVISION_CATEGORY CHECK (RULE_CATEGORY IN ('COST_CONTROL','FRAUD_DETECTION','COMPLIANCE_CHECK','QUALITY_ASSURANCE','STATISTICAL_ANALYSIS')),
    CONSTRAINT CK_RULE_SUPERVISION_SEVERITY CHECK (SEVERITY_LEVEL IN ('LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_RULE_SUPERVISION_PRIORITY CHECK (PRIORITY_LEVEL BETWEEN 1 AND 10),
    CONSTRAINT CK_RULE_SUPERVISION_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_RULE_SUPERVISION_IS_DELETED CHECK (IS_DELETED IN (0,1)),
    CONSTRAINT CK_RULE_SUPERVISION_CREATED_FROM CHECK (CREATED_FROM IN ('MANUAL','TEMPLATE','IMPORT','SYSTEM'))
);

COMMENT ON TABLE RULE_SUPERVISION IS '监管规则主表';
COMMENT ON COLUMN RULE_SUPERVISION.ID IS '规则ID';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_CODE IS '规则编码';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_NAME IS '规则名称';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_TYPE IS '规则类型(SQL:SQL查询规则,DSL:领域特定语言规则,JAVASCRIPT:JavaScript脚本规则)';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_CATEGORY IS '规则分类(COST_CONTROL:费用控制,FRAUD_DETECTION:欺诈检测,COMPLIANCE_CHECK:合规检查,QUALITY_ASSURANCE:质量保证,STATISTICAL_ANALYSIS:统计分析)';
COMMENT ON COLUMN RULE_SUPERVISION.DESCRIPTION IS '规则描述';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_CONTENT IS '规则内容';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_SQL IS 'SQL查询语句';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_DSL IS 'DSL规则表达式';
COMMENT ON COLUMN RULE_SUPERVISION.PRIORITY_LEVEL IS '优先级(1-10,数字越大优先级越高)';
COMMENT ON COLUMN RULE_SUPERVISION.SEVERITY_LEVEL IS '严重程度(LOW:低,MEDIUM:中,HIGH:高,CRITICAL:严重)';
COMMENT ON COLUMN RULE_SUPERVISION.IS_ACTIVE IS '是否启用(0:禁用,1:启用)';
COMMENT ON COLUMN RULE_SUPERVISION.EFFECTIVE_DATE IS '生效日期';
COMMENT ON COLUMN RULE_SUPERVISION.EXPIRY_DATE IS '失效日期';
COMMENT ON COLUMN RULE_SUPERVISION.VERSION_NUMBER IS '版本号';
COMMENT ON COLUMN RULE_SUPERVISION.PARENT_RULE_ID IS '父规则ID';
COMMENT ON COLUMN RULE_SUPERVISION.RULE_SOURCE IS '规则出处';
COMMENT ON COLUMN RULE_SUPERVISION.CREATED_FROM IS '创建来源(MANUAL:手动创建,TEMPLATE:模板创建,IMPORT:导入,SYSTEM:系统生成)';
COMMENT ON COLUMN RULE_SUPERVISION.EXECUTION_COUNT IS '执行次数';
COMMENT ON COLUMN RULE_SUPERVISION.SUCCESS_COUNT IS '成功次数';
COMMENT ON COLUMN RULE_SUPERVISION.LAST_EXECUTED_AT IS '最后执行时间';

-- 2. 规则执行记录表
CREATE TABLE RULE_EXECUTION_LOG (
    ID                  NUMBER(19,0) NOT NULL,
    RULE_ID             NUMBER(19,0) NOT NULL,
    EXECUTION_ID        VARCHAR2(50) NOT NULL,
    EXECUTION_STATUS    VARCHAR2(20) NOT NULL,
    STARTED_AT          DATE NOT NULL,
    ENDED_AT            DATE,
    EXECUTION_DURATION  NUMBER(10,0),
    PROCESSED_RECORD_COUNT NUMBER(10,0) DEFAULT 0,
    MATCHED_RECORD_COUNT   NUMBER(10,0) DEFAULT 0,
    ERROR_MESSAGE       CLOB,
    EXECUTION_PARAMS    CLOB,
    EXECUTION_RESULT    CLOB,
    EXECUTED_BY         NUMBER(19,0),
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CONSTRAINT PK_RULE_EXECUTION_LOG PRIMARY KEY (ID),
    CONSTRAINT FK_RULE_EXECUTION_RULE FOREIGN KEY (RULE_ID) REFERENCES RULE_SUPERVISION(ID),
    CONSTRAINT FK_RULE_EXECUTION_USER FOREIGN KEY (EXECUTED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_RULE_EXECUTION_STATUS CHECK (EXECUTION_STATUS IN ('RUNNING','SUCCESS','FAILED','TIMEOUT','CANCELLED'))
);

COMMENT ON TABLE RULE_EXECUTION_LOG IS '规则执行记录表';
COMMENT ON COLUMN RULE_EXECUTION_LOG.ID IS '执行记录ID';
COMMENT ON COLUMN RULE_EXECUTION_LOG.RULE_ID IS '规则ID';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_ID IS '执行批次ID';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_STATUS IS '执行状态(RUNNING:运行中,SUCCESS:成功,FAILED:失败,TIMEOUT:超时,CANCELLED:已取消)';
COMMENT ON COLUMN RULE_EXECUTION_LOG.STARTED_AT IS '开始时间';
COMMENT ON COLUMN RULE_EXECUTION_LOG.ENDED_AT IS '结束时间';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_DURATION IS '执行时长(毫秒)';
COMMENT ON COLUMN RULE_EXECUTION_LOG.PROCESSED_RECORD_COUNT IS '处理记录数';
COMMENT ON COLUMN RULE_EXECUTION_LOG.MATCHED_RECORD_COUNT IS '匹配记录数';
COMMENT ON COLUMN RULE_EXECUTION_LOG.ERROR_MESSAGE IS '错误信息';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_PARAMS IS '执行参数';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTION_RESULT IS '执行结果';
COMMENT ON COLUMN RULE_EXECUTION_LOG.EXECUTED_BY IS '执行人ID';

-- 3. 规则模板表
CREATE TABLE RULE_TEMPLATE (
    ID                  NUMBER(19,0) NOT NULL,
    TEMPLATE_CODE       VARCHAR2(50) NOT NULL,
    TEMPLATE_NAME       VARCHAR2(200) NOT NULL,
    TEMPLATE_CATEGORY   VARCHAR2(50) NOT NULL,
    DESCRIPTION         CLOB,
    TEMPLATE_CONTENT    CLOB NOT NULL,
    PARAMETER_SCHEMA    CLOB,
    USAGE_COUNT         NUMBER(10,0) DEFAULT 0,
    IS_SYSTEM_TEMPLATE  NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_ACTIVE           NUMBER(1,0) DEFAULT 1 NOT NULL,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_RULE_TEMPLATE PRIMARY KEY (ID),
    CONSTRAINT UK_RULE_TEMPLATE_CODE UNIQUE (TEMPLATE_CODE),
    CONSTRAINT CK_RULE_TEMPLATE_CATEGORY CHECK (TEMPLATE_CATEGORY IN ('COST_CONTROL','FRAUD_DETECTION','COMPLIANCE_CHECK','QUALITY_ASSURANCE','STATISTICAL_ANALYSIS')),
    CONSTRAINT CK_RULE_TEMPLATE_IS_SYSTEM CHECK (IS_SYSTEM_TEMPLATE IN (0,1)),
    CONSTRAINT CK_RULE_TEMPLATE_IS_ACTIVE CHECK (IS_ACTIVE IN (0,1)),
    CONSTRAINT CK_RULE_TEMPLATE_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE RULE_TEMPLATE IS '规则模板表';
COMMENT ON COLUMN RULE_TEMPLATE.ID IS '模板ID';
COMMENT ON COLUMN RULE_TEMPLATE.TEMPLATE_CODE IS '模板编码';
COMMENT ON COLUMN RULE_TEMPLATE.TEMPLATE_NAME IS '模板名称';
COMMENT ON COLUMN RULE_TEMPLATE.TEMPLATE_CATEGORY IS '模板分类';
COMMENT ON COLUMN RULE_TEMPLATE.DESCRIPTION IS '模板描述';
COMMENT ON COLUMN RULE_TEMPLATE.TEMPLATE_CONTENT IS '模板内容';
COMMENT ON COLUMN RULE_TEMPLATE.PARAMETER_SCHEMA IS '参数结构定义';
COMMENT ON COLUMN RULE_TEMPLATE.USAGE_COUNT IS '使用次数';
COMMENT ON COLUMN RULE_TEMPLATE.IS_SYSTEM_TEMPLATE IS '是否系统模板(0:否,1:是)';
COMMENT ON COLUMN RULE_TEMPLATE.IS_ACTIVE IS '是否启用(0:禁用,1:启用)';

-- 4. 规则执行结果表
CREATE TABLE RULE_EXECUTION_RESULT (
    ID                      NUMBER(19,0) NOT NULL,
    EXECUTION_LOG_ID        NUMBER(19,0) NOT NULL,
    RULE_ID                 NUMBER(19,0) NOT NULL,
    CASE_ID                 NUMBER(19,0),
    RESULT_TYPE             VARCHAR2(20) NOT NULL,
    RISK_LEVEL              VARCHAR2(20) NOT NULL,
    VIOLATION_DESCRIPTION   CLOB,
    VIOLATION_AMOUNT        NUMBER(12,2) DEFAULT 0,
    EVIDENCE_DATA           CLOB,
    RULE_MATCHED_FIELDS     VARCHAR2(1000),
    CONFIDENCE_SCORE        NUMBER(5,2) DEFAULT 0,
    RESULT_STATUS           VARCHAR2(20) DEFAULT 'PENDING' NOT NULL,
    IS_AUTO_PROCESSED       NUMBER(1,0) DEFAULT 0 NOT NULL,
    IS_FOLLOW_UP_REQUIRED   NUMBER(1,0) DEFAULT 0 NOT NULL,
    RELATED_CASE_COUNT      NUMBER(10,0) DEFAULT 0,
    CREATED_AT              DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT              DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY              NUMBER(19,0),
    UPDATED_BY              NUMBER(19,0),
    CONSTRAINT PK_RULE_EXECUTION_RESULT PRIMARY KEY (ID),
    CONSTRAINT FK_RULE_EXECUTION_RESULT_LOG FOREIGN KEY (EXECUTION_LOG_ID) REFERENCES RULE_EXECUTION_LOG(ID),
    CONSTRAINT FK_RULE_EXECUTION_RESULT_RULE FOREIGN KEY (RULE_ID) REFERENCES RULE_SUPERVISION(ID),
    CONSTRAINT FK_RULE_EXECUTION_RESULT_CASE FOREIGN KEY (CASE_ID) REFERENCES MEDICAL_CASE(ID),
    CONSTRAINT CK_RULE_RESULT_TYPE CHECK (RESULT_TYPE IN ('VIOLATION','SUSPICIOUS','NORMAL','ERROR')),
    CONSTRAINT CK_RULE_RESULT_RISK CHECK (RISK_LEVEL IN ('LOW','MEDIUM','HIGH','CRITICAL')),
    CONSTRAINT CK_RULE_RESULT_STATUS CHECK (RESULT_STATUS IN ('PENDING','PROCESSING','COMPLETED','IGNORED')),
    CONSTRAINT CK_RULE_RESULT_AUTO_PROCESSED CHECK (IS_AUTO_PROCESSED IN (0,1)),
    CONSTRAINT CK_RULE_RESULT_FOLLOW_UP CHECK (IS_FOLLOW_UP_REQUIRED IN (0,1))
);

COMMENT ON TABLE RULE_EXECUTION_RESULT IS '规则执行结果表';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.ID IS '执行结果ID';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.EXECUTION_LOG_ID IS '执行记录ID';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RULE_ID IS '规则ID';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.CASE_ID IS '病例ID';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RESULT_TYPE IS '结果类型(VIOLATION:违规,SUSPICIOUS:可疑,NORMAL:正常,ERROR:错误)';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RISK_LEVEL IS '风险等级(LOW:低,MEDIUM:中,HIGH:高,CRITICAL:严重)';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.VIOLATION_DESCRIPTION IS '违规描述';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.VIOLATION_AMOUNT IS '违规金额';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.EVIDENCE_DATA IS '证据数据';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RULE_MATCHED_FIELDS IS '规则匹配字段';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.CONFIDENCE_SCORE IS '置信度分数(0-100)';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RESULT_STATUS IS '结果状态(PENDING:待处理,PROCESSING:处理中,COMPLETED:已完成,IGNORED:已忽略)';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.IS_AUTO_PROCESSED IS '是否自动处理(0:否,1:是)';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.IS_FOLLOW_UP_REQUIRED IS '是否需要跟进(0:否,1:是)';
COMMENT ON COLUMN RULE_EXECUTION_RESULT.RELATED_CASE_COUNT IS '关联病例数量';

-- 5. 规则审核结果表
CREATE TABLE RULE_AUDIT_RESULT (
    ID                      NUMBER(19,0) NOT NULL,
    EXECUTION_RESULT_ID     NUMBER(19,0) NOT NULL,
    AUDIT_TYPE              VARCHAR2(20) NOT NULL,
    AUDIT_STATUS            VARCHAR2(20) NOT NULL,
    AUDIT_CONCLUSION        VARCHAR2(20) NOT NULL,
    AUDIT_OPINION           CLOB,
    CORRECTIVE_ACTION       CLOB,
    PENALTY_AMOUNT          NUMBER(12,2) DEFAULT 0,
    RECOVERY_AMOUNT         NUMBER(12,2) DEFAULT 0,
    AUDIT_EVIDENCE          CLOB,
    AUDIT_LEVEL             VARCHAR2(20) NOT NULL,
    APPEAL_STATUS           VARCHAR2(20),
    APPEAL_DEADLINE         DATE,
    FINAL_DECISION          VARCHAR2(20),
    DECISION_DATE           DATE,
    AUDITOR_ID              NUMBER(19,0) NOT NULL,
    AUDITOR_NAME            VARCHAR2(100) NOT NULL,
    AUDIT_DEPARTMENT        VARCHAR2(100),
    AUDIT_START_DATE        DATE DEFAULT SYSDATE NOT NULL,
    AUDIT_END_DATE          DATE,
    IS_DELETED              NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT              DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT              DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY              NUMBER(19,0),
    UPDATED_BY              NUMBER(19,0),
    CONSTRAINT PK_RULE_AUDIT_RESULT PRIMARY KEY (ID),
    CONSTRAINT FK_RULE_AUDIT_RESULT_EXECUTION FOREIGN KEY (EXECUTION_RESULT_ID) REFERENCES RULE_EXECUTION_RESULT(ID),
    CONSTRAINT FK_RULE_AUDIT_RESULT_AUDITOR FOREIGN KEY (AUDITOR_ID) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_RULE_AUDIT_TYPE CHECK (AUDIT_TYPE IN ('INITIAL','REVIEW','APPEAL','FINAL')),
    CONSTRAINT CK_RULE_AUDIT_STATUS CHECK (AUDIT_STATUS IN ('PENDING','AUDITING','COMPLETED','SUSPENDED')),
    CONSTRAINT CK_RULE_AUDIT_CONCLUSION CHECK (AUDIT_CONCLUSION IN ('CONFIRMED','REJECTED','PARTIAL','DEFERRED')),
    CONSTRAINT CK_RULE_AUDIT_LEVEL CHECK (AUDIT_LEVEL IN ('FIRST','SECOND','THIRD','FINAL')),
    CONSTRAINT CK_RULE_APPEAL_STATUS CHECK (APPEAL_STATUS IN ('NONE','SUBMITTED','PROCESSING','ACCEPTED','REJECTED')),
    CONSTRAINT CK_RULE_FINAL_DECISION CHECK (FINAL_DECISION IN ('CONFIRMED','OVERTURNED','MODIFIED','DISMISSED')),
    CONSTRAINT CK_RULE_AUDIT_IS_DELETED CHECK (IS_DELETED IN (0,1))
);

COMMENT ON TABLE RULE_AUDIT_RESULT IS '规则审核结果表';
COMMENT ON COLUMN RULE_AUDIT_RESULT.ID IS '审核结果ID';
COMMENT ON COLUMN RULE_AUDIT_RESULT.EXECUTION_RESULT_ID IS '执行结果ID';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_TYPE IS '审核类型(INITIAL:初审,REVIEW:复审,APPEAL:申诉审核,FINAL:终审)';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_STATUS IS '审核状态(PENDING:待审核,AUDITING:审核中,COMPLETED:已完成,SUSPENDED:暂停)';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_CONCLUSION IS '审核结论(CONFIRMED:确认违规,REJECTED:驳回,PARTIAL:部分确认,DEFERRED:延期处理)';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_OPINION IS '审核意见';
COMMENT ON COLUMN RULE_AUDIT_RESULT.CORRECTIVE_ACTION IS '整改措施';
COMMENT ON COLUMN RULE_AUDIT_RESULT.PENALTY_AMOUNT IS '处罚金额';
COMMENT ON COLUMN RULE_AUDIT_RESULT.RECOVERY_AMOUNT IS '追回金额';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_EVIDENCE IS '审核证据';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_LEVEL IS '审核级别(FIRST:一级,SECOND:二级,THIRD:三级,FINAL:终审)';
COMMENT ON COLUMN RULE_AUDIT_RESULT.APPEAL_STATUS IS '申诉状态(NONE:无申诉,SUBMITTED:已提交,PROCESSING:处理中,ACCEPTED:已受理,REJECTED:已驳回)';
COMMENT ON COLUMN RULE_AUDIT_RESULT.APPEAL_DEADLINE IS '申诉截止日期';
COMMENT ON COLUMN RULE_AUDIT_RESULT.FINAL_DECISION IS '最终决定(CONFIRMED:维持原决定,OVERTURNED:撤销,MODIFIED:修改,DISMISSED:驳回申诉)';
COMMENT ON COLUMN RULE_AUDIT_RESULT.DECISION_DATE IS '决定日期';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDITOR_ID IS '审核人ID';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDITOR_NAME IS '审核人姓名';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_DEPARTMENT IS '审核部门';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_START_DATE IS '审核开始日期';
COMMENT ON COLUMN RULE_AUDIT_RESULT.AUDIT_END_DATE IS '审核结束日期';

-- 6. 规则调度配置表
CREATE TABLE RULE_SCHEDULE_CONFIG (
    ID                  NUMBER(19,0) NOT NULL,
    RULE_ID             NUMBER(19,0) NOT NULL,
    SCHEDULE_NAME       VARCHAR2(200) NOT NULL,
    CRON_EXPRESSION     VARCHAR2(100),
    INTERVAL_SECONDS    NUMBER(10,0),
    IS_ENABLED          NUMBER(1,0) DEFAULT 1 NOT NULL,
    MAX_RETRIES         NUMBER(3,0) DEFAULT 3 NOT NULL,
    RETRY_DELAY         NUMBER(10,0) DEFAULT 5000 NOT NULL,
    TIMEOUT_SECONDS     NUMBER(10,0) DEFAULT 300 NOT NULL,
    EXECUTION_PARAMS    CLOB,
    LAST_EXECUTION_TIME DATE,
    NEXT_EXECUTION_TIME DATE,
    EXECUTION_COUNT     NUMBER(10,0) DEFAULT 0,
    SUCCESS_COUNT       NUMBER(10,0) DEFAULT 0,
    FAILURE_COUNT       NUMBER(10,0) DEFAULT 0,
    IS_DELETED          NUMBER(1,0) DEFAULT 0 NOT NULL,
    CREATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    UPDATED_AT          DATE DEFAULT SYSDATE NOT NULL,
    CREATED_BY          NUMBER(19,0),
    UPDATED_BY          NUMBER(19,0),
    CONSTRAINT PK_RULE_SCHEDULE_CONFIG PRIMARY KEY (ID),
    CONSTRAINT FK_RULE_SCHEDULE_RULE FOREIGN KEY (RULE_ID) REFERENCES RULE_SUPERVISION(ID),
    CONSTRAINT FK_RULE_SCHEDULE_CREATOR FOREIGN KEY (CREATED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT FK_RULE_SCHEDULE_UPDATER FOREIGN KEY (UPDATED_BY) REFERENCES USER_INFO(ID),
    CONSTRAINT CK_RULE_SCHEDULE_ENABLED CHECK (IS_ENABLED IN (0,1)),
    CONSTRAINT CK_RULE_SCHEDULE_DELETED CHECK (IS_DELETED IN (0,1)),
    CONSTRAINT CK_RULE_SCHEDULE_RETRIES CHECK (MAX_RETRIES >= 0 AND MAX_RETRIES <= 10),
    CONSTRAINT CK_RULE_SCHEDULE_TIMEOUT CHECK (TIMEOUT_SECONDS > 0)
);

COMMENT ON TABLE RULE_SCHEDULE_CONFIG IS '规则调度配置表';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.ID IS '调度配置ID';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.RULE_ID IS '规则ID';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.SCHEDULE_NAME IS '调度名称';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.CRON_EXPRESSION IS 'Cron表达式';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.INTERVAL_SECONDS IS '执行间隔(秒)';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.IS_ENABLED IS '是否启用(0:禁用,1:启用)';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.MAX_RETRIES IS '最大重试次数';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.RETRY_DELAY IS '重试延迟(毫秒)';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.TIMEOUT_SECONDS IS '超时时间(秒)';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.EXECUTION_PARAMS IS '执行参数';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.LAST_EXECUTION_TIME IS '最后执行时间';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.NEXT_EXECUTION_TIME IS '下次执行时间';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.EXECUTION_COUNT IS '执行次数';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.SUCCESS_COUNT IS '成功次数';
COMMENT ON COLUMN RULE_SCHEDULE_CONFIG.FAILURE_COUNT IS '失败次数';

-- 创建序列和触发器
CREATE SEQUENCE SEQ_RULE_SUPERVISION START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_RULE_EXECUTION_LOG START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_RULE_TEMPLATE START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_RULE_EXECUTION_RESULT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_RULE_AUDIT_RESULT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_RULE_SCHEDULE_CONFIG START WITH 1 INCREMENT BY 1;

-- 监管规则主表触发器
CREATE OR REPLACE TRIGGER TRG_RULE_SUPERVISION_ID
    BEFORE INSERT ON RULE_SUPERVISION
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_RULE_SUPERVISION.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 规则执行记录表触发器
CREATE OR REPLACE TRIGGER TRG_RULE_EXECUTION_LOG_ID
    BEFORE INSERT ON RULE_EXECUTION_LOG
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_RULE_EXECUTION_LOG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
END;
/

-- 规则模板表触发器
CREATE OR REPLACE TRIGGER TRG_RULE_TEMPLATE_ID
    BEFORE INSERT ON RULE_TEMPLATE
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_RULE_TEMPLATE.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 规则执行结果表触发器
CREATE OR REPLACE TRIGGER TRG_RULE_EXECUTION_RESULT_ID
    BEFORE INSERT ON RULE_EXECUTION_RESULT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_RULE_EXECUTION_RESULT.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 规则审核结果表触发器
CREATE OR REPLACE TRIGGER TRG_RULE_AUDIT_RESULT_ID
    BEFORE INSERT ON RULE_AUDIT_RESULT
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_RULE_AUDIT_RESULT.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 规则调度配置表触发器
CREATE OR REPLACE TRIGGER TRG_RULE_SCHEDULE_CONFIG_ID
    BEFORE INSERT ON RULE_SCHEDULE_CONFIG
    FOR EACH ROW
BEGIN
    IF :NEW.ID IS NULL THEN
        SELECT SEQ_RULE_SCHEDULE_CONFIG.NEXTVAL INTO :NEW.ID FROM DUAL;
    END IF;
    :NEW.UPDATED_AT := SYSDATE;
END;
/

-- 创建索引
-- 注意: RULE_CODE 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_RULE_SUPERVISION_TYPE ON RULE_SUPERVISION(RULE_TYPE);
CREATE INDEX IDX_RULE_SUPERVISION_CATEGORY ON RULE_SUPERVISION(RULE_CATEGORY);
CREATE INDEX IDX_RULE_SUPERVISION_ACTIVE ON RULE_SUPERVISION(IS_ACTIVE);
CREATE INDEX IDX_RULE_SUPERVISION_PRIORITY ON RULE_SUPERVISION(PRIORITY_LEVEL);
CREATE INDEX IDX_RULE_SUPERVISION_EFFECTIVE ON RULE_SUPERVISION(EFFECTIVE_DATE, EXPIRY_DATE);
CREATE INDEX IDX_RULE_SUPERVISION_SOURCE ON RULE_SUPERVISION(RULE_SOURCE);
CREATE INDEX IDX_RULE_SUPERVISION_SEVERITY ON RULE_SUPERVISION(SEVERITY_LEVEL);
CREATE INDEX IDX_RULE_SUPERVISION_CREATED_FROM ON RULE_SUPERVISION(CREATED_FROM);

CREATE INDEX IDX_RULE_EXECUTION_RULE ON RULE_EXECUTION_LOG(RULE_ID);
CREATE INDEX IDX_RULE_EXECUTION_STATUS ON RULE_EXECUTION_LOG(EXECUTION_STATUS);
CREATE INDEX IDX_RULE_EXECUTION_TIME ON RULE_EXECUTION_LOG(STARTED_AT);
CREATE INDEX IDX_RULE_EXECUTION_USER ON RULE_EXECUTION_LOG(EXECUTED_BY);
CREATE INDEX IDX_RULE_EXECUTION_ID ON RULE_EXECUTION_LOG(EXECUTION_ID);

-- 注意: TEMPLATE_CODE 字段已通过唯一约束自动创建索引
CREATE INDEX IDX_RULE_TEMPLATE_CATEGORY ON RULE_TEMPLATE(TEMPLATE_CATEGORY);
CREATE INDEX IDX_RULE_TEMPLATE_ACTIVE ON RULE_TEMPLATE(IS_ACTIVE);
CREATE INDEX IDX_RULE_TEMPLATE_SYSTEM ON RULE_TEMPLATE(IS_SYSTEM_TEMPLATE);
CREATE INDEX IDX_RULE_TEMPLATE_USAGE ON RULE_TEMPLATE(USAGE_COUNT);

CREATE INDEX IDX_RULE_EXECUTION_RESULT_LOG ON RULE_EXECUTION_RESULT(EXECUTION_LOG_ID);
CREATE INDEX IDX_RULE_EXECUTION_RESULT_RULE ON RULE_EXECUTION_RESULT(RULE_ID);
CREATE INDEX IDX_RULE_EXECUTION_RESULT_CASE ON RULE_EXECUTION_RESULT(CASE_ID);
CREATE INDEX IDX_RULE_EXECUTION_RESULT_TYPE ON RULE_EXECUTION_RESULT(RESULT_TYPE);
CREATE INDEX IDX_RULE_EXECUTION_RESULT_RISK ON RULE_EXECUTION_RESULT(RISK_LEVEL);
CREATE INDEX IDX_RULE_EXECUTION_RESULT_STATUS ON RULE_EXECUTION_RESULT(RESULT_STATUS);
CREATE INDEX IDX_RULE_EXECUTION_RESULT_AUTO ON RULE_EXECUTION_RESULT(IS_AUTO_PROCESSED);

CREATE INDEX IDX_RULE_AUDIT_RESULT_EXECUTION ON RULE_AUDIT_RESULT(EXECUTION_RESULT_ID);
CREATE INDEX IDX_RULE_AUDIT_RESULT_TYPE ON RULE_AUDIT_RESULT(AUDIT_TYPE);
CREATE INDEX IDX_RULE_AUDIT_RESULT_STATUS ON RULE_AUDIT_RESULT(AUDIT_STATUS);
CREATE INDEX IDX_RULE_AUDIT_RESULT_CONCLUSION ON RULE_AUDIT_RESULT(AUDIT_CONCLUSION);
CREATE INDEX IDX_RULE_AUDIT_RESULT_LEVEL ON RULE_AUDIT_RESULT(AUDIT_LEVEL);
CREATE INDEX IDX_RULE_AUDIT_RESULT_AUDITOR ON RULE_AUDIT_RESULT(AUDITOR_ID);
CREATE INDEX IDX_RULE_AUDIT_RESULT_APPEAL ON RULE_AUDIT_RESULT(APPEAL_STATUS);
CREATE INDEX IDX_RULE_AUDIT_RESULT_DATE ON RULE_AUDIT_RESULT(AUDIT_START_DATE, AUDIT_END_DATE);